name: Pull Request Slack Notification

on:
  pull_request:
    types: [opened, reopened]

jobs:
  notify_slack:
    runs-on: ubuntu-latest
    steps:
      - name: Notify Slack
        uses: slackapi/slack-github-action@v1.24.0
        with:
          payload: |
            {
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": "${{ github.event.pull_request.title }}",
                    "emoji": true
                  }
                },
                {
                  "type": "context",
                  "elements": [
                    {
                      "type": "mrkdwn",
                      "text": "🔄 New Pull Request by *${{ github.event.pull_request.user.login }}*"
                    }
                  ]
                },
                {
                  "type": "divider"
                },
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Repository:*\n${{ github.repository }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Branch:*\n${{ github.event.pull_request.head.ref }}"
                    }
                  ]
                },
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Base Branch:*\n${{ github.event.pull_request.base.ref }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Changed Files:*\n${{ github.event.pull_request.changed_files }} files"
                    }
                  ]
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*Description:*\n${{ github.event.pull_request.body }}"
                  }
                },
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Additions:*\n+${{ github.event.pull_request.additions }} lines"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Deletions:*\n-${{ github.event.pull_request.deletions }} lines"
                    }
                  ]
                },
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Labels:*\n${{ join(github.event.pull_request.labels.*.name, ', ') }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Created:*\n<!date^${{ floor(github.event.pull_request.created_at) }}^{date_short_pretty} at {time}|${{ github.event.pull_request.created_at }}>"
                    }
                  ]
                },
                {
                  "type": "divider"
                },
                {
                  "type": "actions",
                  "elements": [
                    {
                      "type": "button",
                      "text": {
                        "type": "plain_text",
                        "text": "View Pull Request",
                        "emoji": true
                      },
                      "style": "primary",
                      "url": "${{ github.event.pull_request.html_url }}"
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.PR_SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
