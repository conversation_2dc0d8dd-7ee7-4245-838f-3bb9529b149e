name: Wasp Build Test

on:
  pull_request:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          registry-url: 'https://registry.npmjs.org'
          cache: 'npm'
          cache-dependency-path: './app/package-lock.json'

      - name: Install Wasp
        run: |
          curl -sSL https://get.wasp-lang.dev/installer.sh | sh -s -- -v 0.16.2
          echo "PATH=$HOME/.local/bin:$PATH" >> $GITHUB_ENV
          source $HOME/.bashrc

      - name: Configure npm
        run: |
          npm config set fetch-retries 5
          npm config set fetch-retry-mintimeout 20000
          npm config set fetch-retry-maxtimeout 120000
          npm config set registry https://registry.npmjs.org/

      - name: Test Build
        shell: bash
        run: |
          cd ./app
          # Clean install dependencies with platform-specific flags
          rm -rf node_modules
          npm ci --prefer-offline --no-audit --legacy-peer-deps --platform=linux --arch=x64 || npm install --prefer-offline --no-audit --legacy-peer-deps --force --platform=linux --arch=x64
          npm rebuild --platform=linux --arch=x64
          wasp -v
          if ! wasp build; then
            echo "❌ Build failed"
            exit 1
          else
            echo "✅ Build succeeded"
          fi