name: Deploy to Staging

on:
  push:
    branches:
      - 'main'

env:
  FLY_API_TOKEN: ${{ secrets.FLY_TOKEN }}
  SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
  SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}

jobs:
  check-and-deploy-olivia:
    name: Check and Deploy Olivia
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Check for changes in app directory
        id: check_changes
        run: echo "changes=true" >> $GITHUB_OUTPUT

      - name: Setup Node.js
        if: steps.check_changes.outputs.changes == 'true'
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          registry-url: 'https://registry.npmjs.org'
          cache: 'npm'
          cache-dependency-path: './app/package-lock.json'

      - name: Check npm version
        if: steps.check_changes.outputs.changes == 'true'
        run: npm --version

      - name: Configure npm
        if: steps.check_changes.outputs.changes == 'true'
        run: |
          npm config set fetch-retries 5
          npm config set fetch-retry-mintimeout 20000
          npm config set fetch-retry-maxtimeout 120000
          npm config set registry https://registry.npmjs.org/

      - name: Pre-download problematic packages
        if: steps.check_changes.outputs.changes == 'true'
        run: |
          mkdir -p ~/.npm/_cacache/content-v2/sha512/
          curl -s https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz -o /tmp/camelcase-css-2.0.1.tgz || true
          echo "Pre-downloaded package for cache purposes"

      - name: Clean and reinstall dependencies
        if: steps.check_changes.outputs.changes == 'true'
        working-directory: ./app
        run: |
          rm -rf node_modules
          npm ci --prefer-offline --no-audit --legacy-peer-deps --platform=linux --arch=x64 || npm install --prefer-offline --no-audit --legacy-peer-deps --force --platform=linux --arch=x64
          npm rebuild --platform=linux --arch=x64
          npm dedupe

      - name: Set environment
        id: set_env
        run: |
          echo "environment=staging" >> $GITHUB_OUTPUT
          echo "toml_dir=infra/staging" >> $GITHUB_OUTPUT
          echo "react_env=development" >> $GITHUB_OUTPUT

      - name: Install Wasp
        if: steps.check_changes.outputs.changes == 'true'
        shell: bash
        run: curl -sSL https://get.wasp.sh/installer.sh | sh -s -- -v 0.16.2

      - name: Install Flyctl
        if: steps.check_changes.outputs.changes == 'true'
        uses: superfly/flyctl-actions/setup-flyctl@master

      - name: Change to app directory and deploy
        if: steps.check_changes.outputs.changes == 'true'
        env:
          NODE_OPTIONS: '--max-old-space-size=4096'
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
        run: |
          cd ./app
          REACT_APP_ENVIRONMENT=${{ steps.set_env.outputs.react_env }} REACT_APP_SENTRY_DSN=${{ secrets.SENTRY }} wasp deploy fly deploy --fly-toml-dir "$(pwd)/${{ steps.set_env.outputs.toml_dir }}"
        shell: bash

      - name: Send Slack notification (Deployment Status)
        if: always() && steps.check_changes.outputs.changes == 'true'
        uses: slackapi/slack-github-action@v1
        with:
          payload: |
            {
              "text": "${{ job.status == 'success' && '🚀 Olivia deployment successful!' || '❌ Olivia deployment failed!' }}\n*Repository:* ${{ github.repository }}\n*Branch:* ${{ github.ref_name }}\n*Environment:* ${{ steps.set_env.outputs.environment }}\n*Commit:* ${{ github.sha }}\n*Status:* ${{ job.status }}"
            }

      - name: Send Slack notification (No Changes Detected)
        if: steps.check_changes.outputs.changes != 'true'
        uses: slackapi/slack-github-action@v1
        with:
          payload: |
            {
              "text": "ℹ️ No changes detected in the ./app directory. Skipping Olivia deployment.\n*Repository:* ${{ github.repository }}\n*Branch:* ${{ github.ref_name }}\n*Environment:* ${{ steps.set_env.outputs.environment }}\n*Commit:* ${{ github.sha }}"
            }
