# Tech Context

*   **Languages:** Primary programming languages used.
*   **Frameworks/Libraries:** Key frameworks and libraries (e.g., React, Node.js, Wasp, Prisma). Specify versions if critical.
*   **Database:** Type of database(s) used (e.g., PostgreSQL, MongoDB).
*   **Infrastructure:** Where is the application hosted (e.g., Fly.io, AWS)? Key services used.
*   **Build/Deployment:** How is the code built and deployed? CI/CD pipeline details.
*   **Development Environment:** Setup instructions, required tools (e.g., Node version, Docker).
*   **External Services/APIs:** Dependencies on third-party services.

*(Details the specific technologies and tools involved.)*
