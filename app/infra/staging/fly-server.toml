# fly.toml app configuration file generated for olivia-dev-server on 2024-09-21T01:17:31+08:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'olivia-dev-server'
primary_region = 'yyz'

[build]

[http_service]
internal_port = 8080
force_https = true
auto_stop_machines = 'stop'
auto_start_machines = true
min_machines_running = 1
processes = ['app']

[[vm]]
memory = '1gb'
cpu_kind = 'shared'
cpus = 1
