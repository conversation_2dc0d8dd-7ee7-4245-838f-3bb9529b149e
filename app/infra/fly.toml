# fly.toml app configuration file generated for build on 2024-06-01T20:17:23-04:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'build'
primary_region = 'yyz'

[build]

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 1
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
