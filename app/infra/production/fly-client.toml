# fly.toml app configuration file generated for olivia-prod-client on 2024-12-04T01:34:39+08:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'olivia-prod-client'
primary_region = 'yyz'

[build]

[http_service]
  internal_port = 8043
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
