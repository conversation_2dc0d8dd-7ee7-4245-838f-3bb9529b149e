/**
 * Migration to add canvas elements table
 */
module.exports = {
  async up(db) {
    // Create the canvas_element table
    await db.schema.createTable('canvas_element', (table) => {
      table.string('id').primary();
      table.string('element_id').notNullable();
      table.string('whiteboard_id').notNullable();
      table.string('element_type').notNullable();
      table.float('x').notNullable();
      table.float('y').notNullable();
      table.float('width').nullable();
      table.float('height').nullable();
      table.jsonb('content').notNullable();
      table.timestamp('created_at').notNullable().defaultTo(db.fn.now());
      table.timestamp('updated_at').notNullable().defaultTo(db.fn.now());
      table.integer('user_id').nullable().references('id').inTable('user');
      table.string('organization_id').nullable().references('id').inTable('organization');

      // Indexes
      table.unique(['element_id', 'whiteboard_id']);
      table.index('whiteboard_id');
      table.index('element_type');
    });
  },

  async down(db) {
    // Drop the canvas_element table
    await db.schema.dropTable('canvas_element');
  }
};
