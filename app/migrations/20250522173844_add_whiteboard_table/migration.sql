/*
  Warnings:

  - You are about to drop the column `organizationId` on the `CanvasElement` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `CanvasElement` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "CanvasElement" DROP CONSTRAINT "CanvasElement_organizationId_fkey";

-- DropForeignKey
ALTER TABLE "CanvasElement" DROP CONSTRAINT "CanvasElement_userId_fkey";

-- AlterTable
ALTER TABLE "CanvasElement" DROP COLUMN "organizationId",
DROP COLUMN "userId";

-- CreateTable
CREATE TABLE "WhiteboardCanvas" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT,
    "userId" INTEGER,

    CONSTRAINT "WhiteboardCanvas_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CanvasReferences" (
    "id" TEXT NOT NULL,
    "whiteboardId" TEXT NOT NULL,
    "thumbUrl" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CanvasReferences_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "WhiteboardCanvas_organizationId_idx" ON "WhiteboardCanvas"("organizationId");

-- CreateIndex
CREATE INDEX "WhiteboardCanvas_userId_idx" ON "WhiteboardCanvas"("userId");

-- CreateIndex
CREATE INDEX "CanvasReferences_whiteboardId_idx" ON "CanvasReferences"("whiteboardId");

-- AddForeignKey
ALTER TABLE "WhiteboardCanvas" ADD CONSTRAINT "WhiteboardCanvas_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WhiteboardCanvas" ADD CONSTRAINT "WhiteboardCanvas_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CanvasElement" ADD CONSTRAINT "CanvasElement_whiteboardId_fkey" FOREIGN KEY ("whiteboardId") REFERENCES "WhiteboardCanvas"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CanvasReferences" ADD CONSTRAINT "CanvasReferences_whiteboardId_fkey" FOREIGN KEY ("whiteboardId") REFERENCES "WhiteboardCanvas"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
