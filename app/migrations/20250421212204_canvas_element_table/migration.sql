-- CreateTable
CREATE TABLE "CanvasElement" (
    "id" TEXT NOT NULL,
    "elementId" TEXT NOT NULL,
    "whiteboardId" TEXT NOT NULL,
    "elementType" TEXT NOT NULL,
    "x" DOUBLE PRECISION NOT NULL,
    "y" DOUBLE PRECISION NOT NULL,
    "width" DOUBLE PRECISION,
    "height" DOUBLE PRECISION,
    "content" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" INTEGER,
    "organizationId" TEXT,

    CONSTRAINT "CanvasElement_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "CanvasElement_whiteboardId_idx" ON "CanvasElement"("whiteboardId");

-- CreateIndex
CREATE INDEX "CanvasElement_elementType_idx" ON "CanvasElement"("elementType");

-- CreateIndex
CREATE UNIQUE INDEX "CanvasElement_elementId_whiteboardId_key" ON "CanvasElement"("elementId", "whiteboardId");

-- AddForeignKey
ALTER TABLE "CanvasElement" ADD CONSTRAINT "CanvasElement_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CanvasElement" ADD CONSTRAINT "CanvasElement_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;
