/*
  Warnings:

  - You are about to drop the column `attitudes` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `budget` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `buyingHabits` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `challengesAndPainPoints` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `convenience` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `decisionMakingPower` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `education` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `hobbies` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `incomeLevel` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `interests` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `lifestyle` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `mediaConsumption` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `motivations` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `obstaclesToPurchase` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `onlineActivities` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `price` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `problemsToSolve` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `professionalObjective` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `quality` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `seniority` on the `Audience` table. All the data in the column will be lost.
  - You are about to drop the column `toolsUsed` on the `Audience` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "Audience" DROP COLUMN "attitudes",
DROP COLUMN "budget",
DROP COLUMN "buyingHabits",
DROP COLUMN "challengesAndPainPoints",
DROP COLUMN "convenience",
DROP COLUMN "decisionMakingPower",
DROP COLUMN "education",
DROP COLUMN "hobbies",
DROP COLUMN "incomeLevel",
DROP COLUMN "interests",
DROP COLUMN "lifestyle",
DROP COLUMN "mediaConsumption",
DROP COLUMN "motivations",
DROP COLUMN "obstaclesToPurchase",
DROP COLUMN "onlineActivities",
DROP COLUMN "price",
DROP COLUMN "problemsToSolve",
DROP COLUMN "professionalObjective",
DROP COLUMN "quality",
DROP COLUMN "seniority",
DROP COLUMN "toolsUsed",
ADD COLUMN     "annualIncome" TEXT,
ADD COLUMN     "aspirations" TEXT,
ADD COLUMN     "blogsWebsites" TEXT,
ADD COLUMN     "books" TEXT,
ADD COLUMN     "budgetConcerns" TEXT,
ADD COLUMN     "conferences" TEXT,
ADD COLUMN     "decisionMakingFactors" TEXT,
ADD COLUMN     "educationLevel" TEXT,
ADD COLUMN     "fears" TEXT,
ADD COLUMN     "frustrations" TEXT,
ADD COLUMN     "gurus" TEXT,
ADD COLUMN     "idealDay" TEXT,
ADD COLUMN     "magazines" TEXT,
ADD COLUMN     "maritalStatus" TEXT,
ADD COLUMN     "numberOfChildren" TEXT,
ADD COLUMN     "otherSources" TEXT,
ADD COLUMN     "painPoints" TEXT,
ADD COLUMN     "possibleObjections" TEXT,
ADD COLUMN     "roleInPurchaseProcess" TEXT,
ADD COLUMN     "tier" TEXT,
ADD COLUMN     "values" TEXT;
