-- AlterTable
ALTER TABLE "Asset" ADD COLUMN     "altText" TEXT,
ADD COLUMN     "description" TEXT,
ADD COLUMN     "externalUrl" TEXT,
ADD COLUMN     "license" TEXT,
ADD COLUMN     "metadata" JSONB,
ADD COLUMN     "sortOrder" INTEGER;

-- CreateTable
CREATE TABLE "AssetTag" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "color" TEXT,
    "category" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" INTEGER NOT NULL,

    CONSTRAINT "AssetTag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AssetCollection" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" INTEGER NOT NULL,
    "projectId" TEXT,

    CONSTRAINT "AssetCollection_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AssetAnalytics" (
    "id" TEXT NOT NULL,
    "assetId" INTEGER NOT NULL,
    "views" INTEGER NOT NULL DEFAULT 0,
    "downloads" INTEGER NOT NULL DEFAULT 0,
    "shares" INTEGER NOT NULL DEFAULT 0,
    "lastViewed" TIMESTAMP(3),
    "lastDownload" TIMESTAMP(3),
    "lastShared" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AssetAnalytics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_AssetToAssetTag" (
    "A" INTEGER NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "_AssetToAssetCollection" (
    "A" INTEGER NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "AssetTag_name_userId_key" ON "AssetTag"("name", "userId");

-- CreateIndex
CREATE UNIQUE INDEX "AssetAnalytics_assetId_key" ON "AssetAnalytics"("assetId");

-- CreateIndex
CREATE UNIQUE INDEX "_AssetToAssetTag_AB_unique" ON "_AssetToAssetTag"("A", "B");

-- CreateIndex
CREATE INDEX "_AssetToAssetTag_B_index" ON "_AssetToAssetTag"("B");

-- CreateIndex
CREATE UNIQUE INDEX "_AssetToAssetCollection_AB_unique" ON "_AssetToAssetCollection"("A", "B");

-- CreateIndex
CREATE INDEX "_AssetToAssetCollection_B_index" ON "_AssetToAssetCollection"("B");

-- AddForeignKey
ALTER TABLE "AssetTag" ADD CONSTRAINT "AssetTag_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AssetCollection" ADD CONSTRAINT "AssetCollection_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AssetCollection" ADD CONSTRAINT "AssetCollection_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AssetAnalytics" ADD CONSTRAINT "AssetAnalytics_assetId_fkey" FOREIGN KEY ("assetId") REFERENCES "Asset"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AssetToAssetTag" ADD CONSTRAINT "_AssetToAssetTag_A_fkey" FOREIGN KEY ("A") REFERENCES "Asset"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AssetToAssetTag" ADD CONSTRAINT "_AssetToAssetTag_B_fkey" FOREIGN KEY ("B") REFERENCES "AssetTag"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AssetToAssetCollection" ADD CONSTRAINT "_AssetToAssetCollection_A_fkey" FOREIGN KEY ("A") REFERENCES "Asset"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AssetToAssetCollection" ADD CONSTRAINT "_AssetToAssetCollection_B_fkey" FOREIGN KEY ("B") REFERENCES "AssetCollection"("id") ON DELETE CASCADE ON UPDATE CASCADE;
