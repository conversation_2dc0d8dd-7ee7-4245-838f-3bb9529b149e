-- CreateTable
CREATE TABLE "shared_canvas" (
    "id" TEXT NOT NULL,
    "canvasId" TEXT NOT NULL,
    "sharedByUserId" INTEGER NOT NULL,
    "sharedWithEmail" TEXT NOT NULL,
    "sharedWithUserId" INTEGER,
    "permission" TEXT NOT NULL DEFAULT 'view',
    "token" TEXT NOT NULL,
    "sharedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" TIMESTAMP(3),
    "accessCount" INTEGER NOT NULL DEFAULT 0,
    "lastAccessedAt" TIMESTAMP(3),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "settings" JSONB DEFAULT '{}',

    CONSTRAINT "shared_canvas_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "shared_canvas_token_key" ON "shared_canvas"("token");

-- CreateIndex
CREATE INDEX "shared_canvas_sharedWithEmail_idx" ON "shared_canvas"("sharedWithEmail");

-- CreateIndex
CREATE INDEX "shared_canvas_canvasId_idx" ON "shared_canvas"("canvasId");

-- CreateIndex
CREATE INDEX "shared_canvas_token_idx" ON "shared_canvas"("token");

-- CreateIndex
CREATE INDEX "shared_canvas_sharedByUserId_idx" ON "shared_canvas"("sharedByUserId");

-- AddForeignKey
ALTER TABLE "shared_canvas" ADD CONSTRAINT "shared_canvas_canvasId_fkey" FOREIGN KEY ("canvasId") REFERENCES "Canvas"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shared_canvas" ADD CONSTRAINT "shared_canvas_sharedByUserId_fkey" FOREIGN KEY ("sharedByUserId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shared_canvas" ADD CONSTRAINT "shared_canvas_sharedWithUserId_fkey" FOREIGN KEY ("sharedWithUserId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
