-- AlterTable
ALTER TABLE "Asset" ADD COLUMN     "generationTaskId" INTEGER;

-- CreateTable
CREATE TABLE "GenerationTask" (
    "id" SERIAL NOT NULL,
    "taskId" TEXT,
    "userId" INTEGER NOT NULL,
    "modelId" TEXT NOT NULL,
    "prompt" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "progress" INTEGER NOT NULL,
    "result" TEXT,
    "imageProgresses" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "GenerationTask_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "GenerationTask_taskId_key" ON "GenerationTask"("taskId");

-- AddForeignKey
ALTER TABLE "Asset" ADD CONSTRAINT "Asset_generationTaskId_fkey" FOREIGN KEY ("generationTaskId") REFERENCES "GenerationTask"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Add<PERSON><PERSON><PERSON>Key
ALTER TABLE "GenerationTask" ADD CONSTRAINT "GenerationTask_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
