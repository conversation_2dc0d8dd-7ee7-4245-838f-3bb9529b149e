-- AlterTable
ALTER TABLE "BrandKit" ADD COLUMN     "accentColors" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "avoidanceTerms" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "backgroundStyles" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "brandPersonality" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "brandValues" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "commonScenes" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "lightingPreferences" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "logoVariations" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "moodboardImages" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "photoDosDonts" JSONB,
ADD COLUMN     "photoStyle" JSONB,
ADD COLUMN     "preferredAngles" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "preferredSettings" JSONB,
ADD COLUMN     "primaryColors" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "promptKeywords" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "propGuidelines" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "secondaryColors" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "styleReferences" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "targetEmotions" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "tonality" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "typography" JSONB;
