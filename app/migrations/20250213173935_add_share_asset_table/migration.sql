-- CreateEnum
CREATE TYPE "Permission" AS ENUM ('VIEW', 'EDIT', 'DOWNLOAD', 'FULL_ACCESS');

-- CreateTable
CREATE TABLE "shared_assets" (
    "id" TEXT NOT NULL,
    "assetId" INTEGER NOT NULL,
    "sharedWithEmail" TEXT NOT NULL,
    "sharedWithUserId" INTEGER,
    "permissions" "Permission" NOT NULL DEFAULT 'VIEW',
    "sharedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" TIMESTAMP(3),
    "accessCount" INTEGER NOT NULL DEFAULT 0,
    "lastAccessedAt" TIMESTAMP(3),

    CONSTRAINT "shared_assets_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "shared_assets_sharedWithEmail_idx" ON "shared_assets"("sharedWithEmail");

-- CreateIndex
CREATE INDEX "shared_assets_assetId_idx" ON "shared_assets"("assetId");

-- CreateIndex
CREATE UNIQUE INDEX "shared_assets_assetId_sharedWithEmail_key" ON "shared_assets"("assetId", "sharedWithEmail");

-- AddForeignKey
ALTER TABLE "shared_assets" ADD CONSTRAINT "shared_assets_assetId_fkey" FOREIGN KEY ("assetId") REFERENCES "Asset"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shared_assets" ADD CONSTRAINT "shared_assets_sharedWithUserId_fkey" FOREIGN KEY ("sharedWithUserId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
