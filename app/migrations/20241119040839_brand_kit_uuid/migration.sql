/*
  Warnings:

  - The primary key for the `BrandKit` table will be changed. If it partially fails, the table could be left without primary key constraint.

*/
-- DropForeignKey
ALTER TABLE "CategoryFieldValue" DROP CONSTRAINT "CategoryFieldValue_brandKitId_fkey";

-- DropForeignKey
ALTER TABLE "Project" DROP CONSTRAINT "Project_brandKitId_fkey";

-- AlterTable
ALTER TABLE "BrandKit" DROP CONSTRAINT "BrandKit_pkey",
ALTER COLUMN "id" DROP DEFAULT,
ALTER COLUMN "id" SET DATA TYPE TEXT,
ADD CONSTRAINT "BrandKit_pkey" PRIMARY KEY ("id");
DROP SEQUENCE "BrandKit_id_seq";

-- AlterTable
ALTER TABLE "CategoryFieldValue" ALTER COLUMN "brandKitId" SET DATA TYPE TEXT;

-- AlterTable
ALTER TABLE "Project" ALTER COLUMN "brandKitId" SET DATA TYPE TEXT;

-- AddForeignKey
ALTER TABLE "CategoryFieldValue" ADD CONSTRAINT "CategoryFieldValue_brandKitId_fkey" FOREIGN KEY ("brandKitId") REFERENCES "BrandKit"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Project" ADD CONSTRAINT "Project_brandKitId_fkey" FOREIGN KEY ("brandKitId") REFERENCES "BrandKit"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
