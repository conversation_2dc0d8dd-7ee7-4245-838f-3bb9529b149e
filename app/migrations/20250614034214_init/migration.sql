-- CreateTable
CREATE TABLE "AudienceCharacter" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "age" INTEGER,
    "gender" TEXT,
    "occupation" TEXT,
    "location" TEXT,
    "bio" TEXT,
    "avatarUrl" TEXT,
    "audienceId" INTEGER NOT NULL,
    "userId" INTEGER NOT NULL,
    "organizationId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AudienceCharacter_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "AudienceCharacter_audienceId_idx" ON "AudienceCharacter"("audienceId");

-- CreateIndex
CREATE INDEX "AudienceCharacter_userId_idx" ON "AudienceCharacter"("userId");

-- CreateIndex
CREATE INDEX "AudienceCharacter_organizationId_idx" ON "AudienceCharacter"("organizationId");

-- AddF<PERSON>ignKey
ALTER TABLE "AudienceCharacter" ADD CONSTRAINT "AudienceCharacter_audienceId_fkey" FOREIGN KEY ("audienceId") REFERENCES "Audience"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AudienceCharacter" ADD CONSTRAINT "AudienceCharacter_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AudienceCharacter" ADD CONSTRAINT "AudienceCharacter_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
