/*
  Warnings:

  - A unique constraint covering the columns `[inviteLink]` on the table `User` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "User" ADD COLUMN     "availableSlots" INTEGER NOT NULL DEFAULT 5,
ADD COLUMN     "inviteLink" TEXT,
ADD COLUMN     "referredBy" INTEGER;

-- CreateTable
CREATE TABLE "ReferralClaim" (
    "id" SERIAL NOT NULL,
    "inviterId" INTEGER NOT NULL,
    "claimedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',

    CONSTRAINT "ReferralClaim_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ReferralClaim_inviterId_idx" ON "ReferralClaim"("inviterId");

-- CreateIndex
CREATE UNIQUE INDEX "User_inviteLink_key" ON "User"("inviteLink");

-- CreateIndex
CREATE INDEX "User_inviteLink_idx" ON "User"("inviteLink");

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_referredBy_fkey" FOREIGN KEY ("referredBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReferralClaim" ADD CONSTRAINT "ReferralClaim_inviterId_fkey" FOREIGN KEY ("inviterId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
