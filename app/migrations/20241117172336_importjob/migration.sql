-- AlterTable
ALTER TABLE "ImportJob" ADD COLUMN     "analysisInProgress" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "analysisQueue" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "currentlyAnalyzing" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "maxConcurrentAnalysis" INTEGER NOT NULL DEFAULT 2;

-- AlterTable
ALTER TABLE "Product" ADD COLUMN     "analysisCompletedAt" TIMESTAMP(3),
ADD COLUMN     "analysisError" TEXT,
ADD COLUMN     "analysisStartedAt" TIMESTAMP(3),
ADD COLUMN     "analysisStatus" TEXT NOT NULL DEFAULT 'PENDING';
