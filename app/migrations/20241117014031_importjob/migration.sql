-- CreateEnum
CREATE TYPE "ImportJobStatus" AS ENUM ('PENDING', 'DISCOVERING', 'SELECTION_PENDING', 'IMPORTING', 'COMPLETED', 'FAILED', 'PARTIALLY_COMPLETED');

-- CreateTable
CREATE TABLE "ImportJob" (
    "id" TEXT NOT NULL,
    "userId" INTEGER NOT NULL,
    "websiteUrl" TEXT NOT NULL,
    "status" "ImportJobStatus" NOT NULL DEFAULT 'PENDING',
    "discoveryStarted" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "discoveryCompleted" TIMESTAMP(3),
    "totalPagesScanned" INTEGER NOT NULL DEFAULT 0,
    "totalProductsDiscovered" INTEGER NOT NULL DEFAULT 0,
    "selectedProductCount" INTEGER NOT NULL DEFAULT 0,
    "importedProductCount" INTEGER NOT NULL DEFAULT 0,
    "products" JSONB[],
    "selectedProducts" JSONB[],
    "importedProducts" JSONB[],
    "errors" JSONB[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "completedAt" TIMESTAMP(3),

    CONSTRAINT "ImportJob_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ImportJob_userId_idx" ON "ImportJob"("userId");

-- AddForeignKey
ALTER TABLE "ImportJob" ADD CONSTRAINT "ImportJob_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
