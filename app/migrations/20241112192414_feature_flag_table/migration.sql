-- CreateEnum
CREATE TYPE "FeatureStatus" AS ENUM ('active', 'inactive');

-- CreateEnum
CREATE TYPE "UserTargetType" AS ENUM ('public', 'specific');

-- CreateTable
CREATE TABLE "FeatureFlag" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "description" TEXT,
    "status" "FeatureStatus" NOT NULL DEFAULT 'inactive',
    "isEnabled" BOOLEAN NOT NULL DEFAULT false,
    "targetType" "UserTargetType" NOT NULL DEFAULT 'public',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FeatureFlag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FeatureFlagUser" (
    "id" TEXT NOT NULL,
    "featureFlagId" TEXT NOT NULL,
    "userId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "FeatureFlagUser_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "FeatureFlag_name_key" ON "FeatureFlag"("name");

-- CreateIndex
CREATE UNIQUE INDEX "FeatureFlag_key_key" ON "FeatureFlag"("key");

-- CreateIndex
CREATE INDEX "FeatureFlagUser_userId_idx" ON "FeatureFlagUser"("userId");

-- CreateIndex
CREATE INDEX "FeatureFlagUser_featureFlagId_idx" ON "FeatureFlagUser"("featureFlagId");

-- CreateIndex
CREATE UNIQUE INDEX "FeatureFlagUser_featureFlagId_userId_key" ON "FeatureFlagUser"("featureFlagId", "userId");

-- AddForeignKey
ALTER TABLE "FeatureFlagUser" ADD CONSTRAINT "FeatureFlagUser_featureFlagId_fkey" FOREIGN KEY ("featureFlagId") REFERENCES "FeatureFlag"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FeatureFlagUser" ADD CONSTRAINT "FeatureFlagUser_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
