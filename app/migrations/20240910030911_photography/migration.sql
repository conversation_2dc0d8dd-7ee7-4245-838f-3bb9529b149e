-- CreateTable
CREATE TABLE "PhotographyModel" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "usage" INTEGER NOT NULL DEFAULT 0,
    "performance" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "trainingStart" TIMESTAMP(3),
    "trainingEnd" TIMESTAMP(3),
    "imageUrl" TEXT,
    "userId" INTEGER NOT NULL,

    CONSTRAINT "PhotographyModel_pkey" PRIMARY KEY ("id")
);

-- AddForeign<PERSON><PERSON>
ALTER TABLE "PhotographyModel" ADD CONSTRAINT "PhotographyModel_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
