-- CreateTable
CREATE TABLE "ImageChat" (
    "id" TEXT NOT NULL,
    "imageId" TEXT NOT NULL,
    "imageUrl" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" INTEGER NOT NULL,
    "imageAnalysis" JSONB,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "ImageChat_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ImageChatMessage" (
    "id" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "role" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "imageChatId" TEXT NOT NULL,
    "imageVersionId" TEXT,

    CONSTRAINT "ImageChatMessage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ImageVersion" (
    "id" TEXT NOT NULL,
    "imageUrl" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "imageChatId" TEXT NOT NULL,

    CONSTRAINT "ImageVersion_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "ImageChat" ADD CONSTRAINT "ImageChat_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ImageChat" ADD CONSTRAINT "ImageChat_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ImageChatMessage" ADD CONSTRAINT "ImageChatMessage_imageChatId_fkey" FOREIGN KEY ("imageChatId") REFERENCES "ImageChat"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ImageChatMessage" ADD CONSTRAINT "ImageChatMessage_imageVersionId_fkey" FOREIGN KEY ("imageVersionId") REFERENCES "ImageVersion"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ImageVersion" ADD CONSTRAINT "ImageVersion_imageChatId_fkey" FOREIGN KEY ("imageChatId") REFERENCES "ImageChat"("id") ON DELETE CASCADE ON UPDATE CASCADE;
