# Image Generation with AI Tools

This document explains how the image generation functionality works in our application using OpenRouter and the AI SDK.

## How It Works

Our application uses Claude 3.7 Sonnet via OpenRouter to generate images based on user requests. The process works as follows:

1. User sends a message requesting an image generation
2. The system detects the image generation intent
3. A specialized system prompt is sent to the AI model
4. The AI model calls the `generate_image` tool
5. The application processes the tool call and generates the image

## Implementation Details

### 1. Detecting Image Generation Intent

We use a dedicated function to detect if a message is requesting image generation:

```typescript
// Check if this is an image generation request
const isImageGen = await checkImageGenerationIntent(message);
```

This function uses <PERSON> to analyze the message and determine if it's requesting image generation.

### 2. Specialized System Prompt

When an image generation request is detected, we use a specialized system prompt that explicitly instructs the model to use the `generate_image` tool:

```typescript
const imageGenMessages = [
  {
    role: 'system',
    content: `You are an AI assistant with access to a generate_image tool. When users request image generation, you MUST use the generate_image tool.

IMPORTANT: You MUST call the generate_image tool when asked to generate an image. DO NOT just describe what you would generate.

Here's how to use the tool:
1. When a user asks for an image, ALWAYS call the generate_image tool
2. Extract the key details from their request to form a detailed prompt
3. Include any specific requirements they mention
4. Do not just describe what you would generate - you must actually call the tool
5. After calling the tool, let the user know you're generating their image

HANDLING MULTIPLE DIFFERENT IMAGES:
If the user asks for multiple different images (e.g., "generate an image of a duck and an image of a cat"):
1. First call the generate_image tool for the first request (e.g., duck)
2. Then call the generate_image tool again for the second request (e.g., cat)
3. You can make up to 5 separate tool calls in a single response

Example tool usage:
{
  "name": "generate_image",
  "arguments": {
    "prompt": "detailed description here",
    "numImages": 1,
    "modelId": "${selectedModelId || ''}"
  }
}`
  },
  {
    role: 'user',
    content: cleanedMessage
  }
];
```

### 3. Tool Definition

The `generate_image` tool is defined in `openrouter.ts`:

```typescript
const generateImageTool = {
  description: 'Generate an image based on a detailed product photography prompt',
  parameters: z.object({
    prompt: z.string().describe('The detailed prompt for the image generation'),
    modelId: z.string().optional().describe('Optional specific model ID to use'),
    numImages: z.number().int().min(1).max(4).optional().describe('Number of images to generate (1-4)')
  }),
  execute: async ({ prompt, modelId, numImages = 1 }) => {
    console.log('[OpenRouter] Generate image tool called with:', { prompt, modelId, numImages });
    return { 
      success: true,
      imagePrompt: prompt,
      modelId: modelId || null,
      numImages: numImages || 1
    };
  }
};
```

### 4. Processing Tool Calls

When the AI model calls the `generate_image` tool, we process the tool call and generate the image:

```typescript
// Process with tools
const result = await processMessageWithTools(imageGenMessages);

// Log the response from OpenRouter
const responseText = result.text || '';
console.log('[Chat][DEBUG] Received response from OpenRouter:', responseText.substring(0, 50) + '...');
console.log('[Chat][DEBUG] Tool calls:', result.toolCalls);

// Log steps if available
if (result.steps && result.steps.length > 0) {
  console.log('[Chat][DEBUG] Multi-step execution completed with', result.steps.length, 'steps');
  result.steps.forEach((step, index) => {
    console.log(`[Chat][DEBUG] Step ${index + 1}:`);
    console.log(`  - Text: ${step.text.substring(0, 100)}...`);
    console.log(`  - Tool calls: ${step.toolCalls.length}`);
  });
}
```

### 5. Handling Tool Calls in the Client

The client-side code processes the tool calls from the response:

```typescript
// Helper function to safely access response properties
const safelyAccessResponse = (result: any) => {
  // Handle different response formats
  const responseMessage = result?.choices?.[0]?.message || result;
  
  // Check for tool calls in various locations
  const toolCalls = 
    // Check in metadata first (new format)
    (responseMessage?.metadata?.toolCalls) || 
    // Then check in direct properties (old formats)
    responseMessage?.tool_calls || 
    responseMessage?.toolCalls || 
    // Finally check in steps if available
    (responseMessage?.steps && responseMessage.steps.length > 0 
      ? responseMessage.steps.flatMap((step: any) => step.toolCalls || [])
      : []);
  
  return {
    responseText: responseMessage?.content || responseMessage?.response || responseMessage?.text || '',
    toolCalls: toolCalls || []
  };
};
```

## Multi-Step Image Generation

The application now supports generating multiple different images in a single request. For example, a user can ask:

- "Generate an image of a duck and an image of a cat"
- "Create 1 image of a mountain landscape and 2 images of ocean waves"

### How Multi-Step Generation Works

1. When the system detects a request for multiple different images, it enables multi-step tool calling
2. The AI model processes the request and makes sequential tool calls for each image
3. Each image is generated as a separate step in the process
4. The system can handle up to 5 different image generation requests in a single user message

### Implementation Details

Multi-step tool calling is implemented using the `maxSteps` parameter in the AI SDK:

```javascript
const result = await generateText({
  model: modelId,
  messages: formattedMessages,
  temperature: 0.7,
  maxTokens: 4096,
  tools: tools,
  maxSteps: 5 // Allow up to 5 sequential steps
});
```

The system prompt instructs the model to handle multiple different image requests by making separate tool calls for each image:

```javascript
const systemPrompt = `
...
HANDLING MULTIPLE DIFFERENT IMAGES:
If the user asks for multiple different images (e.g., "generate an image of a duck and an image of a cat"):
1. First call the generate_image tool for the first request (e.g., duck)
2. Then call the generate_image tool again for the second request (e.g., cat)
3. You can make up to 5 separate tool calls in a single response
...
`;
```

### Collecting Tool Calls from All Steps

A key part of making multi-step tool calling work is collecting tool calls from all steps in the execution:

```javascript
// Collect all tool calls from all steps
const allToolCalls: Array<{
  toolName: string;
  args: Record<string, any>;
}> = [];

steps.forEach((step, index) => {
  // Add tool calls from this step to the allToolCalls array
  if (step.toolCalls && step.toolCalls.length > 0) {
    step.toolCalls.forEach(tc => {
      allToolCalls.push(tc);
    });
  }
});

// Return both the response text and all tool calls from all steps
return {
  text: text || "",
  toolCalls: allToolCalls.length > 0 ? allToolCalls : (toolCalls || []),
  steps: steps || []
};
```

### Including Tool Calls in Message Metadata

To ensure tool calls are properly passed to the client, we include them in the message metadata:

```javascript
// Add metadata for tool calls if they exist
const metadata = result.toolCalls && result.toolCalls.length > 0 
  ? { toolCalls: result.toolCalls } 
  : undefined;

// Return the result with metadata
return {
  ...result,
  metadata
};
```

### Debugging Multi-Step Execution

The system logs detailed information about each step in the multi-step execution process:

```
[Chat][DEBUG] Multi-step execution completed with 3 steps
[Chat][DEBUG] Step 1:
  - Text: I'll generate an image of a duck for you...
  - Tool calls: 1
[Chat][DEBUG] Step 2:
  - Text: Now I'll generate an image of a cat for you...
  - Tool calls: 1
[Chat][DEBUG] Step 3:
  - Text: Both images have been generated! The first shows a realistic duck in a pond with detailed feathers...
  - Tool calls: 0
```

## Troubleshooting

### Model Not Using the Tool

If the model isn't using the `generate_image` tool when expected:

1. **Check the logs**: Look for `[OpenRouter] Tool calls: []` in the logs, which indicates no tool calls were made
2. **Be explicit in your request**: Try phrases like "use the generate_image tool to create..."
3. **Adjust the system prompt**: Make it even more explicit that the model MUST use the tool

### Tool Call Format Issues

The AI SDK expects tools to be defined in a specific format:

- Tools must be provided as an object with tool names as keys
- Each tool must have `description`, `parameters` (using Zod schema), and `execute` function

### Multi-Step Tool Call Issues

If multi-step tool calls aren't working:

1. **Check the logs**: Look for `[Chat][DEBUG] Multi-step execution completed with X steps` to confirm multi-step execution
2. **Verify tool calls collection**: Ensure tool calls from all steps are being collected and returned
3. **Check client-side handling**: Make sure the client is checking for tool calls in the steps array

## Example User Requests

Here are some example requests that should trigger image generation:

- "Generate an image of a mountain landscape"
- "Create a product photo of our sleep gummies on a nightstand"
- "Use the generate_image tool to make a picture of a beach sunset"
- "Generate an image of a duck and an image of a cat" (multi-step generation)
- "Create 1 image of a mountain landscape and 2 images of ocean waves" (multi-step generation)

## Model Fallbacks

If the primary model (Claude 3.7 Sonnet) fails, the system will automatically try these fallbacks in order:

1. `anthropic/claude-3.5-sonnet:beta`
2. `google/gemini-2.5-pro-preview-06-05`

This ensures high availability of the image generation functionality.

## Planned Future Improvements

- ✅ Support for multiple different images in a single request
- Support for image variations
- Additional customization options (size, style, etc.)
- Integration with more image generation models
- Ability to edit generated images based on user feedback
- Support for different aspect ratios
- Ability to use reference images for style guidance
- Integration with content moderation systems
- Improve prompt enhancement for better image quality
- Add style presets for different types of product photography

## Future Improvements

Planned improvements to the image generation functionality:

1. Add support for multiple images in a single request
2. Implement image variation capabilities
3. Add style presets for different types of product photography
4. Improve prompt enhancement for better image quality 