# Voice Agent Refactoring Plan

## Overview

This document outlines a comprehensive plan for refactoring our voice agent implementation to improve reliability, maintainability, and performance. The focus is on streamlining the process of accepting voice requests, delegating to the AI SDK agent for generating HTML or images, rendering placeholders on the canvas, and then replacing them with final content.

## Current Architecture

The current implementation involves these key components:

1. **Voice Agent Request Flow**:
   - Python voice agent → `voiceAgentProxy.ts` API endpoint → `TaskProcessingService.processVoiceAgentRequestStreaming()`
   - Task placeholders created on canvas → AI SDK processes request with tools (html/image)
   - WebSocket events update canvas with progress/content → Task management updates database records

2. **Core Components**:
   - `voiceAgentProxy.ts`: API endpoint for voice agent requests
   - `taskProcessingService.ts`: Core service that processes requests using AI SDK
   - `toolRegistry.ts`: Registry of available tools (generate_html, generate_image, etc.)
   - WebSocket handlers: Update UI with progress and results
   - `getPendingTasks.ts`: Manages task updates and cleanup

3. **Database & Task Management**:
   - Tasks stored in PostgreSQL using Prisma
   - WebSockets used for real-time updates and canvas manipulation

## Problems with Current Implementation

1. **Fragmented Flow**: Logic spread across multiple components
2. **Manual Placeholder Management**: Error-prone placeholder creation/removal 
3. **WebSocket Complexity**: Many different event types to handle
4. **Inconsistent Tool Processing**: HTML vs image handling differs
5. **Manual Task Management**: Task dependencies handled imperatively

## New Architecture with PostgreSQL NOTIFY/LISTEN

We'll leverage PostgreSQL's built-in pub/sub capabilities to create a more cohesive system:

```mermaid
flowchart TD
    A[Voice Agent Request] --> B[Request Handler]
    B --> C[Task Manager]
    C -->|Writes| D[PostgreSQL]
    C --> E{AI Processing Engine}
    E --> F[HTML Generator]
    E --> G[Image Generator]
    D -->|NOTIFY| H[Postgres Listener]
    H --> I[Client Updates]
    subgraph "Database Triggers"
    J[Task Created] -->|Trigger| K[NOTIFY task_created]
    L[Task Updated] -->|Trigger| M[NOTIFY task_updated]
    N[Task Completed] -->|Trigger| O[NOTIFY task_completed]
    end
```

### Key Improvements:

1. **Unified Event System**: Using PostgreSQL NOTIFY/LISTEN for all updates
2. **Centralized Task Management**: Clear task lifecycle and relationship handling
3. **Streamlined UI Updates**: Direct database-to-client notification flow
4. **Type Safety**: Improved typing throughout the system
5. **Consistency**: Standardized approach for all tool types
6. **Reduced Code Complexity**: Elimination of manual event management

## Implementation Plan

### 1. Database Schema Enhancements

**Action:** Update the Prisma schema to support improved task management and PostgreSQL notifications.

```prisma
model GenerationTask {
  id            Int      @id @default(autoincrement())
  taskId        String   @unique
  userId        Int
  modelId       String?
  prompt        String?  @db.Text
  result        String?  @db.Text
  status        String   // QUEUED, IN_PROGRESS, COMPLETED, ERROR
  progress      Int      @default(0)
  error         String?
  taskType      String   // HTML, IMAGE, VOICE_AGENT_REQUEST
  metadata      String?  @db.Text // JSON string for additional data
  parentTaskId  String?  // For tracking relationships
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  requestSentAt DateTime?

  // New fields
  canvasElementId String? // Track associated canvas element directly
  completedAt     DateTime?
  attemptCount    Int      @default(0)
}

// New model for canvas elements
model CanvasElement {
  id        String   @id
  userId    Int
  type      String   // html, image, placeholder
  content   String?  @db.Text
  taskId    String?
  position  String?  @db.Text // JSON with x, y, width, height
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
```

### 2. PostgreSQL NOTIFY/LISTEN Setup

**Action:** Create triggers and functions in PostgreSQL to automatically notify on task changes.

```sql
-- Function to convert row data to JSON and send notification
CREATE OR REPLACE FUNCTION notify_task_change() RETURNS TRIGGER AS $$
BEGIN
  PERFORM pg_notify(
    'task_updates', 
    json_build_object(
      'operation', TG_OP,
      'taskId', NEW.taskId,
      'status', NEW.status,
      'progress', NEW.progress,
      'type', NEW.taskType,
      'canvasElementId', NEW.canvasElementId
    )::text
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger on task table updates
CREATE TRIGGER task_change_trigger
AFTER INSERT OR UPDATE ON "GenerationTask"
FOR EACH ROW
EXECUTE FUNCTION notify_task_change();

-- Function to handle canvas element notifications
CREATE OR REPLACE FUNCTION notify_canvas_change() RETURNS TRIGGER AS $$
BEGIN
  PERFORM pg_notify(
    'canvas_updates', 
    json_build_object(
      'operation', TG_OP,
      'id', NEW.id,
      'type', NEW.type,
      'taskId', NEW.taskId
    )::text
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger on canvas element updates
CREATE TRIGGER canvas_change_trigger
AFTER INSERT OR UPDATE ON "CanvasElement"
FOR EACH ROW
EXECUTE FUNCTION notify_canvas_change();
```

### 3. Task Management Service

**Action:** Create a unified TaskManager service for task CRUD operations and relationships.

```typescript
// src/server/agents/services/taskManager.ts

import { prisma } from 'wasp/server';
import { TaskStatus, TaskType } from './taskTypes';

export interface CreateTaskOptions {
  taskId: string;
  userId: number;
  prompt: string;
  taskType: TaskType;
  modelId?: string;
  parentTaskId?: string;
  metadata?: any;
}

export class TaskManager {
  static async createTask(options: CreateTaskOptions) {
    const { taskId, userId, prompt, taskType, modelId, parentTaskId, metadata } = options;
    
    // Create the task record
    const task = await prisma.generationTask.create({
      data: {
        taskId,
        userId,
        prompt: prompt.substring(0, 1000), // Truncate long content
        status: TaskStatus.QUEUED,
        progress: 0,
        taskType,
        modelId: modelId || taskType,
        parentTaskId,
        metadata: metadata ? JSON.stringify(metadata) : null,
        requestSentAt: new Date()
      }
    });
    
    return task;
  }
  
  static async updateTask(taskId: string, data: {
    status?: string;
    progress?: number;
    result?: string;
    error?: string;
    metadata?: any;
    canvasElementId?: string;
  }) {
    const currentTask = await prisma.generationTask.findUnique({
      where: { taskId }
    });
    
    if (!currentTask) {
      throw new Error(`Task ${taskId} not found`);
    }
    
    // If we're providing new metadata, merge it with existing metadata
    let updatedMetadata = data.metadata;
    if (data.metadata && currentTask.metadata) {
      const currentMetadata = JSON.parse(currentTask.metadata);
      updatedMetadata = {...currentMetadata, ...data.metadata};
    }
    
    // Build update data
    const updateData: any = {
      ...data,
      metadata: updatedMetadata ? JSON.stringify(updatedMetadata) : currentTask.metadata
    };
    
    // If status is being updated to COMPLETED, set completedAt
    if (data.status === TaskStatus.COMPLETED) {
      updateData.completedAt = new Date();
    }
    
    // Update the task
    return await prisma.generationTask.update({
      where: { taskId },
      data: updateData
    });
  }
  
  static async getTask(taskId: string) {
    return await prisma.generationTask.findUnique({
      where: { taskId }
    });
  }
  
  static async getChildTasks(parentTaskId: string) {
    return await prisma.generationTask.findMany({
      where: { parentTaskId }
    });
  }
  
  // Additional methods as needed...
}
```

### 4. PostgreSQL Notification Listener Service

**Action:** Create a service to listen to PostgreSQL notifications and forward to clients.

```typescript
// src/server/services/postgresNotificationService.ts

import { Client } from 'pg';
import { USER_ROOM_CHANNEL } from '../../websocket/constants';
import { io } from '../../websocket';

export class PostgresNotificationService {
  private static instance: PostgresNotificationService;
  private pgClient: Client;
  private connected: boolean = false;
  
  private constructor() {
    this.pgClient = new Client(process.env.DATABASE_URL);
    this.setupListener();
  }
  
  static getInstance(): PostgresNotificationService {
    if (!PostgresNotificationService.instance) {
      PostgresNotificationService.instance = new PostgresNotificationService();
    }
    return PostgresNotificationService.instance;
  }
  
  private async setupListener() {
    try {
      await this.pgClient.connect();
      this.connected = true;
      
      // Listen for task updates
      await this.pgClient.query('LISTEN task_updates');
      await this.pgClient.query('LISTEN canvas_updates');
      
      this.pgClient.on('notification', async (notification) => {
        try {
          const payload = JSON.parse(notification.payload);
          
          if (notification.channel === 'task_updates') {
            await this.handleTaskUpdate(payload);
          } else if (notification.channel === 'canvas_updates') {
            await this.handleCanvasUpdate(payload);
          }
        } catch (error) {
          console.error('Error processing Postgres notification:', error);
        }
      });
      
      console.log('[PostgresNotificationService] Listening for database notifications');
    } catch (error) {
      console.error('Error setting up Postgres listener:', error);
      // Retry connection after a delay
      setTimeout(() => this.setupListener(), 5000);
    }
  }
  
  private async handleTaskUpdate(payload: any) {
    try {
      const { taskId, userId } = payload;
      
      if (!userId) {
        // Fetch userId if not included in the payload
        const task = await prisma.generationTask.findUnique({
          where: { taskId },
          select: { userId: true }
        });
        
        if (!task) return;
        payload.userId = task.userId;
      }
      
      // Emit to the appropriate room
      io.to(USER_ROOM_CHANNEL(payload.userId)).emit('task_update', payload);
    } catch (error) {
      console.error('Error handling task update notification:', error);
    }
  }
  
  private async handleCanvasUpdate(payload: any) {
    try {
      const { id, taskId } = payload;
      
      // Get the canvas element to get userId
      const canvasElement = await prisma.canvasElement.findUnique({
        where: { id },
        select: { userId: true }
      });
      
      if (!canvasElement) return;
      
      // Emit to the appropriate room
      io.to(USER_ROOM_CHANNEL(canvasElement.userId)).emit('canvas_update', payload);
    } catch (error) {
      console.error('Error handling canvas update notification:', error);
    }
  }
}

// Initialize the service
export const initPostgresNotificationService = () => {
  PostgresNotificationService.getInstance();
};
```

### 5. Canvas Management Service

**Action:** Create a service for managing canvas elements.

```typescript
// src/server/services/canvasManager.ts

import { prisma } from 'wasp/server';

interface CanvasElementPosition {
  x: number;
  y: number;
  width?: number;
  height?: number;
}

export class CanvasManager {
  static async createElement(data: {
    id: string;
    userId: number;
    type: string;
    content?: string;
    taskId?: string;
    position?: CanvasElementPosition;
  }) {
    // Create a canvas element in the database
    return await prisma.canvasElement.create({
      data: {
        ...data,
        position: data.position ? JSON.stringify(data.position) : null
      }
    });
  }
  
  static async updateElement(id: string, data: {
    content?: string;
    position?: CanvasElementPosition;
  }) {
    // Get the current element
    const element = await prisma.canvasElement.findUnique({
      where: { id }
    });
    
    if (!element) {
      throw new Error(`Canvas element ${id} not found`);
    }
    
    // Update position if provided
    let positionData = element.position;
    if (data.position) {
      const currentPosition = element.position ? JSON.parse(element.position) : {};
      positionData = JSON.stringify({...currentPosition, ...data.position});
    }
    
    // Update the element
    return await prisma.canvasElement.update({
      where: { id },
      data: {
        ...data,
        position: positionData
      }
    });
  }
  
  static async deleteElement(id: string) {
    return await prisma.canvasElement.delete({
      where: { id }
    });
  }
  
  static async getElement(id: string) {
    return await prisma.canvasElement.findUnique({
      where: { id }
    });
  }
  
  static async getElementsByTaskId(taskId: string) {
    return await prisma.canvasElement.findMany({
      where: { taskId }
    });
  }
}
```

### 6. Refactored Voice Agent Processing

**Action:** Update TaskProcessingService to use the new architecture.

```typescript
// src/server/agents/services/taskProcessingService.ts

import { streamMessageWithTools } from '../llm/llmService';
import { getAllTools } from '../tools/core/toolRegistry';
import { TaskManager } from './taskManager';
import { CanvasManager } from '../../services/canvasManager';
import { TaskStatus, TaskType } from './taskTypes';
import { v4 as uuidv4 } from 'uuid';
import { VOICE_AGENT_SYSTEM_PROMPT } from '../llm/systemPrompt';

export class TaskProcessingService {
  static async processVoiceAgentRequestStreaming(
    taskId: string,
    userId: number,
    request: string,
    roomName: string,
    customSystemPrompt?: string,
    modelId?: string
  ) {
    console.log(`[TaskProcessingService] Processing stream request for task ${taskId}`);

    try {
      // 1. Create the task using TaskManager
      await TaskManager.createTask({
        taskId,
        userId,
        prompt: request,
        taskType: TaskType.VOICE_AGENT_REQUEST,
        modelId,
        metadata: { roomName }
      });

      // 2. Create a placeholder canvas element
      const elementId = `voice-agent-placeholder-${taskId}`;
      await CanvasManager.createElement({
        id: elementId,
        userId,
        type: 'placeholder',
        content: `<div>Processing your request: "${request.substring(0, 50)}${request.length > 50 ? '...' : ''}"</div>`,
        taskId,
        position: { x: 100, y: 100 }
      });

      // 3. Update the task with the canvas element ID
      await TaskManager.updateTask(taskId, {
        status: TaskStatus.IN_PROGRESS,
        progress: 15,
        canvasElementId: elementId
      });

      // 4. Prepare messages for the AI model
      const messages = [
        { role: 'system', content: customSystemPrompt || VOICE_AGENT_SYSTEM_PROMPT },
        { role: 'user', content: request }
      ];

      // 5. Prepare tools with context
      const toolsWithContext = Object.entries(getAllTools()).reduce((acc, [name, toolDef]) => {
        acc[name] = {
          ...toolDef,
          execute: async (args: any) => {
            console.log(`[TaskProcessingService] Executing tool: ${name} for task ${taskId}`);
            try {
              // Return markers based on tool name
              if (name === 'generate_image') {
                return await this.handleImageGeneration(args, taskId, userId, roomName);
              } else if (name === 'generate_html') {
                return { action: 'stream_html', originalArgs: args };
              } else {
                // Execute other tools directly or return generic marker
                console.warn(`[TaskProcessingService] Tool ${name} execute called without specific server-side logic.`);
                return { action: `${name}_executed`, originalArgs: args };
              }
            } catch (execError) {
              console.error(`[TaskProcessingService] Error executing tool ${name}:`, execError);
              return { action: 'error', error: `Execution failed for ${name}` };
            }
          }
        };
        return acc;
      }, {} as Record<string, any>);

      // 6. Call streamMessageWithTools
      const stream = await streamMessageWithTools(
        messages,
        Object.values(toolsWithContext),
        modelId
      );

      // 7. Process the results
      await this.handleToolResults(stream, taskId, userId, roomName, messages);

    } catch (error) {
      console.error(`[TaskProcessingService] Error processing stream request for task ${taskId}:`, error);
      await TaskManager.updateTask(taskId, {
        status: TaskStatus.ERROR,
        progress: 0,
        error: error instanceof Error ? error.message : 'Unknown processing error'
      });
    }
  }

  // Rest of the implementation with updated methods...
}
```

### 7. Client-Side Integration

**Action:** Update client-side code to handle the new notification system.

```typescript
// src/client/hooks/useTaskNotifications.ts

import { useEffect, useState } from 'react';
import { useSocket } from 'wasp/client/webSocket';

export const useTaskNotifications = (initialTasks = []) => {
  const socket = useSocket();
  const [tasks, setTasks] = useState(initialTasks);
  const [canvasElements, setCanvasElements] = useState({});

  useEffect(() => {
    // Listen for task updates
    socket.on('task_update', (update) => {
      console.log('Received task update:', update);
      
      setTasks(prevTasks => {
        // Check if this task already exists in our state
        const existingTaskIndex = prevTasks.findIndex(t => t.taskId === update.taskId);
        
        if (existingTaskIndex >= 0) {
          // Update existing task
          const updatedTasks = [...prevTasks];
          updatedTasks[existingTaskIndex] = {
            ...updatedTasks[existingTaskIndex],
            ...update
          };
          return updatedTasks;
        } else {
          // Add new task
          return [...prevTasks, update];
        }
      });
    });
    
    // Listen for canvas updates
    socket.on('canvas_update', (update) => {
      console.log('Received canvas update:', update);
      
      setCanvasElements(prev => ({
        ...prev,
        [update.id]: update
      }));
    });
    
    return () => {
      socket.off('task_update');
      socket.off('canvas_update');
    };
  }, [socket]);

  return {
    tasks,
    canvasElements
  };
};
```

## Implementation Timeline

### Phase 1: Setup PostgreSQL NOTIFY/LISTEN (Week 1)
- Create database migration to add triggers and functions
- Implement PostgresNotificationService
- Test basic notification flow

### Phase 2: Core Services Implementation (Week 2)
- Implement TaskManager 
- Implement CanvasManager
- Update schema.prisma with new models/fields

### Phase 3: TaskProcessingService Refactoring (Week 3)
- Refactor voice agent processing
- Implement updated tool handlers
- Add proper error handling and recovery

### Phase 4: Client Integration (Week 4)
- Update client-side hooks
- Update canvas components
- Ensure proper notification handling

### Phase 5: Testing & Rollout (Week 5)
- Comprehensive testing
- Monitoring setup
- Gradual rollout

## Testing Strategy

1. **Unit Tests**: Services and core logic
2. **Integration Tests**: Database triggers and notification flow
3. **End-to-End Tests**: Complete voice agent flow
4. **Load Tests**: Performance under concurrent requests
5. **Chaos Tests**: Recovery from failures

## Success Metrics

1. **Reliability**: Reduction in placeholder/task orphaning issues
2. **Performance**: Lower latency in updates and rendering
3. **Maintainability**: Reduced codebase complexity
4. **Scalability**: Handling of more concurrent requests

## Implementation Progress

### Completed Tasks

1. **Directory Structure Reorganization**:
   - Created new directory structure in `src/server/agents/realtime/` with subdirectories for tasks, postgres, and canvas
   - Moved `taskProcessingService.ts` to `src/server/agents/realtime/tasks/`
   - Moved `taskManager.ts` to `src/server/agents/realtime/tasks/`
   - Completed migration of imports in key files:
     - `chatAgent.ts`
     - `voiceAgent.ts`
     - `voiceAgentProxy.ts`
     - `handlers.ts`
     - `index.ts`

2. **Fixed Type Imports**:
   - Ensuring `TaskStatus` and `TaskType` are imported from `services/taskTypes.ts` not from service implementations

### In Progress

1. **Remaining Import Fixes**:
   - A few additional import errors to resolve

2. **PostgreSQL Notification Integration**:
   - Implementation of Postgres notification listener service
   - Setting up triggers for task updates
   - Canvas element management integration

3. **Testing & Validation**:
   - Verifying all components work correctly with new architecture
   - Ensuring no regressions in functionality

### Next Steps

1. **Complete Import Refactoring**:
   - Ensure all imports are correctly updated to reference new file locations
   - Verify type exports are consistent

2. **Implement Database Triggers**:
   - Create SQL migrations for the notification triggers
   - Set up testing environment

3. **Client Integration**:
   - Update client-side code to work with new event system
   - Add robust error handling for edge cases

## Conclusion

This refactoring plan leverages PostgreSQL's pub/sub capabilities to create a more robust, maintainable voice agent system. By centralizing task management and standardizing the notification flow, we reduce complexity and improve reliability. The progress so far has focused on directory structure reorganization and fixing imports, with the next phase focused on implementing the PostgreSQL notification system.
