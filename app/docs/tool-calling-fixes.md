# Tool Calling Fixes: Summary of Changes

This document summarizes the changes we made to fix the tool calling functionality, particularly for multi-step image generation.

## The Problem

The system was correctly detecting image generation requests and the AI model was making tool calls, but these tool calls weren't being properly passed back to the client. Specifically:

1. Tool calls from multi-step executions weren't being collected and returned
2. The client wasn't checking all possible locations for tool calls in the response
3. Tool calls weren't being included in message metadata when saving to the database

## Changes Made

### Server-Side Changes

#### 1. In `openrouter.ts`:

- Updated the `processMessageWithTools` function to collect tool calls from all steps:

```typescript
// Collect all tool calls from all steps
const allToolCalls: Array<{
  toolName: string;
  args: Record<string, any>;
}> = [];

steps.forEach((step, index) => {
  // Add tool calls from this step to the allToolCalls array
  if (step.toolCalls && step.toolCalls.length > 0) {
    step.toolCalls.forEach(tc => {
      allToolCalls.push(tc);
    });
  }
});

// Return both the response text and all tool calls from all steps
return {
  text: text || "",
  toolCalls: allToolCalls.length > 0 ? allToolCalls : (toolCalls || []),
  steps: steps || []
};
```

- Added the `maxSteps: 5` parameter to the `generateText` function call to enable multi-step tool calling:

```typescript
const { text, toolCalls, steps } = await generateText({
  model: openrouter.chat(currentModel),
  messages: convertMessages(messages),
  tools,
  toolChoice: "auto",
  temperature: 0.7,
  maxTokens: 1024,
  maxSteps: 5 // Allow up to 5 steps for multiple tool calls
});
```

#### 2. In `chat/index.ts`:

- Updated the `processChatMessage` function to include tool calls in the response metadata:

```typescript
// Add metadata for tool calls if they exist
const metadata = result.toolCalls && result.toolCalls.length > 0 
  ? { toolCalls: result.toolCalls } 
  : undefined;

// Return the result with metadata
return {
  ...result,
  metadata
};
```

### Client-Side Changes

#### 1. In `useMessageHandler.ts`:

- Updated the `safelyAccessResponse` function to check for tool calls in various locations:

```typescript
const safelyAccessResponse = (result: any) => {
  // Handle different response formats
  const responseMessage = result?.choices?.[0]?.message || result;
  
  // Check for tool calls in various locations
  const toolCalls = 
    // Check in metadata first (new format)
    (responseMessage?.metadata?.toolCalls) || 
    // Then check in direct properties (old formats)
    responseMessage?.tool_calls || 
    responseMessage?.toolCalls || 
    // Finally check in steps if available
    (responseMessage?.steps && responseMessage.steps.length > 0 
      ? responseMessage.steps.flatMap((step: any) => step.toolCalls || [])
      : []);
  
  return {
    responseText: responseMessage?.content || responseMessage?.response || responseMessage?.text || '',
    toolCalls: toolCalls || []
  };
};
```

- Updated the `handleToolCalls` function to handle different tool call formats:

```typescript
const handleToolCalls = async (toolCalls: any[]) => {
  if (!toolCalls || toolCalls.length === 0) return;
  
  for (const toolCall of toolCalls) {
    // Handle OpenRouter's function calling format
    if (toolCall.type === 'function' && toolCall.function) {
      const { name, arguments: argsStr } = toolCall.function;
      try {
        const args = JSON.parse(argsStr);
        if (name === 'generate_image') {
          await handleGenerateImageTool(args);
        }
      } catch (error) {
        console.error('Error parsing function arguments:', error);
      }
    }
    // Handle our custom tool call format
    else if (toolCall.toolName === 'generate_image' && toolCall.args) {
      await handleGenerateImageTool(toolCall.args);
    } 
    // Handle AI SDK format
    else if (toolCall.name === 'generate_image' && toolCall.args) {
      await handleGenerateImageTool(toolCall.args);
    }
  }
};
```

- Updated the `addChatMessageAction` call to include tool calls in message metadata:

```typescript
// Check if we have tool calls to include in metadata
const metadata = safelyAccessResponse(llmResponse).toolCalls.length > 0 
  ? { toolCalls: safelyAccessResponse(llmResponse).toolCalls }
  : undefined;

// Save assistant message to database
await addChatMessageAction({
  chatId: currentChatId,
  content: safelyAccessResponse(llmResponse).responseText,
  role: 'assistant',
  type: 'text',
  metadata
});
```

## Key Insights

1. **Tool Format Matters**: The AI SDK requires tools to be defined as an object with tool names as keys, not as an array.

2. **System Prompt is Crucial**: The system prompt must explicitly instruct the model to use the tool and provide clear examples.

3. **Multi-Step Tool Calling**: Setting `maxSteps` to a value greater than 1 enables the model to make multiple sequential tool calls.

4. **Collecting Tool Calls**: When using multi-step tool calling, you need to collect tool calls from all steps, not just the final result.

5. **Response Format Variations**: Different models and configurations can return tool calls in different formats, so your code needs to handle all possible formats.

6. **Metadata Handling**: Including tool calls in message metadata ensures they're available when retrieving messages from the database.

7. **Detailed Logging**: Comprehensive logging is essential for debugging tool calling issues.

## Testing the Fix

To test the fix, try sending messages like:

- "Generate an image of a mountain landscape"
- "Create 4 images of a beach sunset"
- "Generate an image of a duck and an image of a cat"

The system should now correctly:
1. Detect the image generation intent
2. Make the appropriate tool calls
3. Pass the tool calls back to the client
4. Generate the requested images

## Future Improvements

1. **Enhanced Error Handling**: Add more robust error handling for tool calls
2. **Streaming Tool Calls**: Implement streaming for tool calls to provide a more interactive experience
3. **Additional Tools**: Add more tools for different functionalities
4. **Tool Call Analytics**: Track and analyze tool call usage to improve the system 