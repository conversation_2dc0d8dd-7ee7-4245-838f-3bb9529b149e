# Unified Task Event Refactor

> Purpose: replace the double‑pipeline (_generic placeholder_ + _pending image_) with a single **`taskUpdate`** event that drives **one element per task**.  This document tracks the migration so we can safely delete legacy code afterwards.

---

## 1. New Event Contract (backend ➜ frontend)

```jsonc
{
  "taskId": "<uuid>-u1",
  "status": "QUEUED" | "IN_PROGRESS" | "COMPLETED" | "FAILED",
  "progress": 0‑100,           // meaningful for images
  "type": "image" | "html" | "generic",
  "imageUrl": "https://…jpg", // only when type === "image" & status === COMPLETED
  "title": "Generating Image…", // optional UI hint
  "x": 100, "y": 100, "width": 400, "height": 300 // optional placement
}
```
* Every task keeps the **same `taskId`** throughout its life cycle.
* No more `taskId‑img` suffixes.  An _image_ task is represented by one record whose `type` changes from `generic` ➜ `image`.
* Socket channel: `taskUpdate` (can reuse existing room logic).
* Optional server event: `taskRemove` (if we want the backend to force cleanup).

## 2. Client‑side Changes

| Status | Area | Action |
|--------|------|--------|
| ✅ | **Task store** | `canvas/store/task-store.ts` implemented (`useTaskStore`). |
| ✅ | **Socket listener** | Added `useTasks` hook with socket listeners for `taskUpdate`. |
| ✅ | **Components** | Added `TaskPlaceholders.tsx` for rendering from unified task store. |
| ⬜ | **Delete legacy hooks** | `usePendingTasks.ts`, plus image/generic placeholder duplication code. |
| ⬜ | **Clean up ids** | Stop generating `-img` IDs when adding fallback images.  Elements use `voice-agent-image-${taskId}` always. |
| ⬜ | **History / undo** | Ensure new state changes still produce snapshots if needed. |

### HTML Streaming
Nothing in the HTML flow changes except:
* `taskUpdate` events for `type:'html'` follow the same pattern.
* The existing `StreamingHtmlPlaceholder` may remain, but should read from `useTaskStore` instead of window events.

## 3. Server‑side Changes

| Status | Task |
|--------|------|
| ✅ | Added unified `TaskUpdatePayload` interface and `emitTaskUpdate` function in `server/agents/services/taskEvents.ts`. |
| ⬜ | Replace existing event emissions (`updatePendingGenericProgress` / `updatePendingImageProgress` / placeholder `canvasElementUpdate`). |
| ⬜ | Remove creation of `taskId‑img` subtasks; store everything in parent task. |
| ⬜ | Update polling in `getPendingTasks` to emit cached `taskUpdate` events rather than mutating DB. |

## 4. Deletion List (once migration stable)

- `client/app/canvas/components/generic-elements.tsx`
- `client/app/canvas/components/placeholders/GenericPlaceholder.tsx`
- `client/app/canvas/components/placeholders/ImagePlaceholder.tsx`
- `client/app/canvas/hooks/usePendingTasks.ts`
- Legacy window events:
  - `attachPendingGenericToCanvas`
  - `attachPendingImageToCanvas`
  - `updatePendingGenericProgress`
  - `updatePendingImageProgress`
  - related polling fallbacks

## 5. Roll‑out Steps

1. Implement backend `taskUpdate` + stop emitting old events.
2. Introduce new socket listener & minimal rendering on client.
3. Swap out components to use unified store.
4. Delete legacy code (list above) and run clean build.
5. Regression test: image, HTML, generic tasks, error cases.

---

### Open Items

- [ ] Decide if server or client should calculate element placement defaults.
- [ ] Ensure undo/redo history integrates with new task workflow.
- [ ] Optional: animation/transition styles when a pending placeholder becomes a final image.
- [ ] Fix TypeScript linter errors in `useTasks.ts` related to socket event types.

---

_Last updated: 2023-04-26 - Implemented client-side components and prepared server-side changes._ 