# Using Tools with OpenRouter and AI SDK

This guide explains how to use tools with OpenRouter and the AI SDK in our application.

## Overview

Tool calling allows AI models to perform actions beyond just generating text. In our application, we use tool calling to enable image generation and other functionalities.

### Supported Models

The following OpenRouter models support tool calling:

- `anthropic/claude-3.7-sonnet:beta` (primary)
- `anthropic/claude-3.5-sonnet:beta` (fallback)
- `google/gemini-2.0-flash-001` (fallback)

## Defining Tools

Tools are defined using Zod schema for type safety:

```typescript
import { z } from 'zod';

// Define a tool using Zod schema
const generateImageTool = {
  description: 'Generate an image based on a detailed product photography prompt',
  parameters: z.object({
    prompt: z.string().describe('The detailed prompt for the image generation'),
    modelId: z.string().optional().describe('Optional specific model ID to use'),
    numImages: z.number().int().min(1).max(4).optional().describe('Number of images to generate (1-4)')
  }),
  execute: async ({ prompt, modelId, numImages = 1 }) => {
    console.log('[OpenRouter] Generate image tool called with:', { prompt, modelId, numImages });
    return { 
      success: true,
      imagePrompt: prompt,
      modelId: modelId || null,
      numImages: numImages || 1
    };
  }
};
```

### Important: Tool Format for AI SDK

The AI SDK expects tools to be defined in a specific format:

```typescript
// CORRECT: Tools as an object with tool names as keys
const tools = {
  generate_image: generateImageTool
};

// INCORRECT: Tools as an array
const tools = [generateImageTool]; // This won't work with AI SDK
```

## Using Tools with generateText

To use tools with the `generateText` function:

```typescript
import { generateText } from 'ai';
import { createOpenRouter } from '@openrouter/ai-sdk-provider';

const openrouter = createOpenRouter({
  apiKey: OPENROUTER_API_KEY,
});

const { text, toolCalls, steps } = await generateText({
  model: openrouter.chat(modelId),
  messages: convertMessages(messages),
  tools,
  toolChoice: "auto",
  temperature: 0.7,
  maxTokens: 1024,
  maxSteps: 5 // For multi-step tool calling
});
```

## Multi-Step Tool Calling

Multi-step tool calling allows the model to make multiple sequential tool calls in a single request. This is useful for handling complex requests like generating multiple different images.

### How It Works

1. The model makes a tool call
2. The tool's `execute` function is called with the provided arguments
3. The result is returned to the model
4. The model can then make another tool call based on the result
5. This process continues until no more tool calls are made or `maxSteps` is reached

### Implementation

To enable multi-step tool calling, set the `maxSteps` parameter in the `generateText` function:

```typescript
const { text, toolCalls, steps } = await generateText({
  model: openrouter.chat(modelId),
  messages: convertMessages(messages),
  tools,
  toolChoice: "auto",
  temperature: 0.7,
  maxTokens: 1024,
  maxSteps: 5 // Allow up to 5 sequential steps
});
```

### Collecting Tool Calls from All Steps

When using multi-step tool calling, you need to collect tool calls from all steps:

```typescript
// Collect all tool calls from all steps
const allToolCalls = [];

if (steps && steps.length > 0) {
  steps.forEach((step, index) => {
    if (step.toolCalls && step.toolCalls.length > 0) {
      step.toolCalls.forEach(tc => {
        allToolCalls.push(tc);
      });
    }
  });
}

// Return both the response text and all tool calls
return {
  text: text || "",
  toolCalls: allToolCalls.length > 0 ? allToolCalls : (toolCalls || []),
  steps: steps || []
};
```

### Use Cases for Multi-Step Tool Calling

- Generating multiple different images in a single request
- Sequential operations that depend on previous results
- Complex workflows that require multiple tool calls

## System Prompts for Tool Calling

The system prompt is crucial for instructing the model to use tools. Here's an example for image generation:

```typescript
const systemPrompt = `You are an AI assistant with access to a generate_image tool. When users request image generation, you MUST use the generate_image tool.

IMPORTANT: You MUST call the generate_image tool when asked to generate an image. DO NOT just describe what you would generate.

Here's how to use the tool:
1. When a user asks for an image, ALWAYS call the generate_image tool
2. Extract the key details from their request to form a detailed prompt
3. Include any specific requirements they mention
4. Do not just describe what you would generate - you must actually call the tool
5. After calling the tool, let the user know you're generating their image

HANDLING MULTIPLE DIFFERENT IMAGES:
If the user asks for multiple different images (e.g., "generate an image of a duck and an image of a cat"):
1. First call the generate_image tool for the first request (e.g., duck)
2. Then call the generate_image tool again for the second request (e.g., cat)
3. You can make up to 5 separate tool calls in a single response`;
```

## Client-Side Handling of Tool Calls

On the client side, you need to handle tool calls returned from the server:

```typescript
// Helper function to safely access response properties
const safelyAccessResponse = (result: any) => {
  // Handle different response formats
  const responseMessage = result?.choices?.[0]?.message || result;
  
  // Check for tool calls in various locations
  const toolCalls = 
    // Check in metadata first (new format)
    (responseMessage?.metadata?.toolCalls) || 
    // Then check in direct properties (old formats)
    responseMessage?.tool_calls || 
    responseMessage?.toolCalls || 
    // Finally check in steps if available
    (responseMessage?.steps && responseMessage.steps.length > 0 
      ? responseMessage.steps.flatMap((step: any) => step.toolCalls || [])
      : []);
  
  return {
    responseText: responseMessage?.content || responseMessage?.response || responseMessage?.text || '',
    toolCalls: toolCalls || []
  };
};

// Process tool calls
const handleToolCalls = async (toolCalls: any[]) => {
  if (!toolCalls || toolCalls.length === 0) return;
  
  for (const toolCall of toolCalls) {
    // Handle OpenRouter's function calling format
    if (toolCall.type === 'function' && toolCall.function) {
      const { name, arguments: argsStr } = toolCall.function;
      try {
        const args = JSON.parse(argsStr);
        if (name === 'generate_image') {
          await handleGenerateImageTool(args);
        }
      } catch (error) {
        console.error('Error parsing function arguments:', error);
      }
    }
    // Handle our custom tool call format
    else if (toolCall.toolName === 'generate_image' && toolCall.args) {
      await handleGenerateImageTool(toolCall.args);
    } 
    // Handle AI SDK format
    else if (toolCall.name === 'generate_image' && toolCall.args) {
      await handleGenerateImageTool(toolCall.args);
    }
  }
};
```

## Saving Tool Calls in Message Metadata

When saving assistant messages, include tool calls in the metadata:

```typescript
// Check if we have tool calls to include in metadata
const metadata = safelyAccessResponse(llmResponse).toolCalls.length > 0 
  ? { toolCalls: safelyAccessResponse(llmResponse).toolCalls }
  : undefined;

// Save assistant message to database
await addChatMessageAction({
  chatId: currentChatId,
  content: safelyAccessResponse(llmResponse).responseText,
  role: 'assistant',
  type: 'text',
  metadata
});
```

## Troubleshooting

### Model Not Using the Tool

If the model isn't using the tool when expected:

1. **Check the system prompt**: Make it explicit that the model MUST use the tool
2. **Check the tool definition**: Ensure it follows the AI SDK format (object with tool names as keys)
3. **Check the logs**: Look for errors in the tool definition or execution

### Tool Calls Not Being Returned

If tool calls are being made but not returned to the client:

1. **Check the response format**: Make sure you're extracting tool calls from the correct location
2. **Check multi-step handling**: Ensure you're collecting tool calls from all steps
3. **Check metadata handling**: Make sure tool calls are being included in the message metadata

### Common Issues and Solutions

| Issue | Solution |
|-------|----------|
| Model not using tool | Make system prompt more explicit |
| Tool calls not in response | Check response format and extraction logic |
| Multi-step tool calls not working | Ensure `maxSteps` is set and tool calls are collected from all steps |
| Client not receiving tool calls | Check metadata handling and client-side extraction logic |

## Debugging Tips

Add detailed logging to track the flow of tool calls:

```typescript
// Log the request
console.log('[OpenRouter] Request:', {
  model: currentModel,
  messages: messages.map(m => ({
    role: m.role,
    content: typeof m.content === 'string' ? 
      (m.content.length > 100 ? m.content.substring(0, 100) + '...' : m.content) : 
      m.content
  })),
  tools: Object.keys(tools)
});

// Log the response
console.log(`[OpenRouter] Tool calls:`, toolCalls);

// Log steps for multi-step execution
if (steps && steps.length > 0) {
  console.log(`[OpenRouter] Multi-step execution completed with ${steps.length} steps`);
  
  steps.forEach((step, index) => {
    console.log(`[OpenRouter] Step ${index + 1}:`);
    console.log(`  - Text: ${step.text.substring(0, 100)}...`);
    console.log(`  - Tool calls: ${step.toolCalls.length}`);
    
    if (step.toolCalls && step.toolCalls.length > 0) {
      step.toolCalls.forEach(tc => {
        console.log(`    - Tool: ${tc.toolName}, Args: ${JSON.stringify(tc.args)}`);
      });
    }
  });
}
```

## Best Practices

1. **Be explicit in system prompts**: Clearly instruct the model to use tools
2. **Use proper tool format**: Follow the AI SDK's expected format
3. **Handle multi-step tool calls**: Collect tool calls from all steps
4. **Include tool calls in metadata**: Ensure tool calls are passed to the client
5. **Add detailed logging**: Log requests, responses, and steps for debugging
6. **Handle different tool call formats**: Account for variations in how tool calls are returned
7. **Provide fallbacks**: Have a plan for when tool calls fail or aren't made 