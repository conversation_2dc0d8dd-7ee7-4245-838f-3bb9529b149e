# Sending Images to OpenRouter

## Overview
OpenRouter provides access to various AI models with multimodal capabilities, allowing our application to send both text and images in a single message. This document explains how images are sent to OpenRouter in our application, including the message format, conversion process, and best practices.

## Message Format for Images
When sending an image to OpenRouter, the message must be formatted in a specific way. For user messages with images, we use the following format:

```typescript
{
  role: 'user',
  content: [
    { type: 'text', text: 'The user's text message' },
    { type: 'image_url', image_url: { url: 'https://example.com/image.jpg' } }
  ]
}
```

This format combines text and image URL in a single message, allowing the AI model to process both simultaneously.

## Converting Messages for OpenRouter
The `convertMessages` function in `openrouter.ts` handles the conversion of our internal message formats to the format expected by OpenRouter:

```typescript
export function convertMessages(messages: CoreMessage[]): AIMessage[] {
  return messages.map((message) => {
    if (message.role === 'system') {
      return {
        role: 'system',
        content: typeof message.content === 'string' 
          ? message.content 
          : JSON.stringify(message.content)
      };
    } else if (message.role === 'assistant') {
      return {
        role: 'assistant',
        content: typeof message.content === 'string' 
          ? message.content 
          : JSON.stringify(message.content)
      };
    } else if (message.role === 'user') {
      // Only user messages can have array content with images
      if (Array.isArray(message.content)) {
        return {
          role: 'user',
          content: message.content
        };
      } else {
        return {
          role: 'user',
          content: typeof message.content === 'string' 
            ? message.content 
            : JSON.stringify(message.content)
        };
      }
    } else {
      return {
        role: message.role as any,
        content: typeof message.content === 'string' 
          ? message.content 
          : JSON.stringify(message.content)
      };
    }
  });
}
```

This function ensures that user messages with array content (which may contain images) are properly formatted for OpenRouter.

## Preparing the Message with an Image
When a user sends a message with an attached image, the `processChatMessage` function formats the message appropriately:

```typescript
// If an image URL is provided, create a multimodal message
if (selectedImageUrl) {
  userMessage = {
    role: 'user',
    content: [
      { type: 'text', text: message },
      { type: 'image_url', image_url: { url: selectedImageUrl } }
    ]
  };
} else {
  userMessage = {
    role: 'user',
    content: message
  };
}
```

## Image Formats Supported
OpenRouter supports images in the following formats:
- URLs to publicly accessible images
- Base64 data URLs (e.g., `data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAUA...`)

## Image Analysis with OpenRouter
Our application also uses OpenRouter for image analysis. The `analyzeImageWithOpenRouter` function in `enhancePrompt.ts` demonstrates how to process image URLs and base64 data for analysis:

```typescript
export async function analyzeImageWithOpenRouter(imageUrlOrBase64: string): Promise<string> {
  try {
    // Determine if the input is a URL or base64 data
    const isBase64 = imageUrlOrBase64.startsWith('data:');
    
    // Format the message with the image
    const messages = [
      {
        role: 'user',
        content: [
          { 
            type: 'text', 
            text: 'Describe this image in detail, focusing on the product shown.' 
          },
          { 
            type: 'image_url', 
            image_url: { 
              url: imageUrlOrBase64 
            } 
          }
        ]
      }
    ];
    
    // Send to OpenRouter for analysis
    const response = await openrouter.chat.completions.create({
      model: 'anthropic/claude-3-opus-20240229',
      messages: messages as any,
      max_tokens: 1000
    });
    
    return response.choices[0]?.message?.content || '';
  } catch (error) {
    console.error('Error analyzing image with OpenRouter:', error);
    return '';
  }
}
```

## Best Practices for Sending Images

### Image Size and Format
- Keep image sizes reasonable (under 5MB if possible)
- Use common formats like JPEG, PNG, or WebP
- Ensure images are publicly accessible if using URLs

### URL vs Base64
- Use URLs for publicly accessible images (more efficient)
- Use Base64 for temporary or private images (increases payload size)

### Error Handling
- Always include error handling when sending images
- Check for valid image URLs before sending
- Validate that the image format is supported

### Logging
- Log image URLs (but not full Base64 data) for debugging
- Track success/failure rates for image processing

## Troubleshooting
If images are not being processed correctly:

1. **Check URL accessibility**: Ensure the image URL is publicly accessible
2. **Verify message format**: Confirm the message structure follows the required format
3. **Check image size**: Large images may cause timeouts or failures
4. **Inspect network requests**: Look for errors in the OpenRouter API response
5. **Try a different model**: Some models handle images better than others

By following these guidelines, you can effectively send images to OpenRouter and leverage multimodal AI capabilities in your application. 