/**
 * Agent Orchestrator
 *
 * This file provides functions for orchestrating agents.
 */

import { createThread, getThread, addEvent } from './threadService';
import { processNextStep } from './eventHandlers';
import { safeEmit } from '../utils/emitUtils';
import { prisma } from 'wasp/server';
import { defaultSystemPrompt, NEWSLETTER_GENERATION_PROMPT } from '../prompts/systemPrompt';
import { EventEmitter } from 'events';
import {
  PRIMARY_MODEL as OPENROUTER_MODEL,
  streamResponse,
  processStream,
  getOpenRouterCompletionWithSchema,
} from './openRouterService';
import { Writable as WritableStream } from 'stream';
import { executeIntent } from '../tools/toolExecutor';
import { newsletterOutlineSchema } from '../schemas/newsletterSchemas';
import { updateTaskStatus } from '../taskmanagement/taskProcessor';
import { HttpError } from 'wasp/server';

const MAX_RETRIES = 1;
const RETRY_DELAY_MS = 1000;

// Helper function to introduce a delay
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Define a basic OpenRouterMessage type locally
interface OpenRouterMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string | null; // Content can be null for assistant messages with tool_calls
  name?: string; // For tool role
  tool_calls?: any[]; // If assistant message has tool calls
  tool_call_id?: string; // For tool role
}

/**
 * Agent orchestrator service
 * This class is responsible for managing the overall lifecycle and operations of AI agents.
 * It handles things like starting agents, letting them talk to language models,
 * and processing their responses.
 */
export class AgentOrchestrator {
  /**
   * Constructor for the AgentOrchestrator.
   * @param eventEmitter Used for internal communication within the agent system.
   * @param uiEventEmitter (Optional) Used for sending events specifically to the user interface.
   */
  constructor(
    private eventEmitter: EventEmitter,
    private uiEventEmitter?: any
  ) {}

  /**
   * Determines the system prompt (initial instructions) for the AI agent based on the task.
   * Why it's useful: Different tasks require different instructions for the AI to perform well.
   * This ensures the AI gets the right context.
   * @param taskType The type of task (e.g., 'generate_newsletter_flow').
   * @param additionalInstructions Any custom instructions to override or add to the default.
   * @returns The system prompt string.
   */
  private getSystemPromptForTask(taskType?: string, additionalInstructions?: string): string {
    if (additionalInstructions) {
      return additionalInstructions;
    }
    if (taskType === 'generate_newsletter_flow') {
      return NEWSLETTER_GENERATION_PROMPT;
    }
    return defaultSystemPrompt;
  }

  /**
   * Launch a new agent session.
   * What it does: Creates a new "thread" (like a conversation history) for the agent,
   * records the user's first message, and tells the agent to start working.
   * Why it's useful: This is the starting point for any new agent interaction.
   * @param userId The ID of the user starting the agent.
   * @param initialMessage The first message or request from the user.
   * @param metadata Additional information about the launch (e.g., where it came from).
   * @returns The ID of the newly created agent thread.
   */
  static async launch(userId: string, initialMessage: string, metadata: Record<string, any> = {}): Promise<string> {
    try {
      // Create a new thread
      const thread = await createThread(userId, metadata);

      // Add the initial message
      await addEvent(thread.id, 'user_message', {
        content: initialMessage,
        source: metadata.source || 'web',
        metadata: metadata,
      });

      // Start processing
      await processNextStep(thread.id);

      return thread.id;
    } catch (error) {
      console.error(`[AgentOrchestrator] Error launching agent:`, error);
      throw error;
    }
  }

  /**
   * Resume an agent session with new input from a human.
   * What it does: Finds an existing agent thread, adds the human's new message to it,
   * and tells the agent to continue working based on this new input.
   * Why it's useful: Allows users to interact with an agent that might be waiting for feedback or more information.
   * @param threadId The ID of the agent thread to resume.
   * @param humanInput The new message or input from the user.
   * @param metadata Additional information about this interaction.
   */
  static async resume(threadId: string, humanInput: string, metadata: Record<string, any> = {}): Promise<void> {
    try {
      // Get the thread
      const thread = await getThread(threadId);
      if (!thread) {
        throw new Error(`Thread ${threadId} not found`);
      }

      // Add the human input
      await addEvent(threadId, 'human_input_response', {
        response: humanInput,
        metadata,
      });

      // Resume processing
      await processNextStep(threadId);
    } catch (error) {
      console.error(`[AgentOrchestrator] Error resuming agent:`, error);
      throw error;
    }
  }

  /**
   * Pause an ongoing agent session.
   * What it does: Finds the agent thread, records that it's paused (and why),
   * and notifies the user that the agent is paused.
   * Why it's useful: Allows temporarily stopping an agent's activity without ending the session.
   * @param threadId The ID of the agent thread to pause.
   * @param reason The reason why the agent is being paused.
   */
  static async pause(threadId: string, reason: string): Promise<void> {
    try {
      // Get the thread
      const thread = await getThread(threadId);
      if (!thread) {
        throw new Error(`Thread ${threadId} not found`);
      }

      // Add the pause event
      await addEvent(threadId, 'agent_paused', {
        reason,
        timestamp: new Date(),
      });

      // Notify the user
      safeEmit(thread.userId, 'agent_paused', {
        threadId,
        reason,
      });
    } catch (error) {
      console.error(`[AgentOrchestrator] Error pausing agent:`, error);
      throw error;
    }
  }

  /**
   * Cancel an ongoing agent session.
   * What it does: Finds the agent thread, records that it's cancelled (and why),
   * and notifies the user that the agent session has ended.
   * Why it's useful: Allows permanently stopping an agent's activity.
   * @param threadId The ID of the agent thread to cancel.
   * @param reason The reason why the agent is being cancelled.
   */
  static async cancel(threadId: string, reason: string): Promise<void> {
    try {
      // Get the thread
      const thread = await getThread(threadId);
      if (!thread) {
        throw new Error(`Thread ${threadId} not found`);
      }

      // Add the cancel event
      await addEvent(threadId, 'agent_cancelled', {
        reason,
        timestamp: new Date(),
      });

      // Notify the user
      safeEmit(thread.userId, 'agent_cancelled', {
        threadId,
        reason,
      });
    } catch (error) {
      console.error(`[AgentOrchestrator] Error cancelling agent:`, error);
      throw error;
    }
  }

  /**
   * Get the current status of an agent session.
   * What it does: Fetches the agent thread and looks at the last thing that happened
   * to determine if the agent is active, paused, completed, waiting for input, etc.
   * Why it's useful: Provides visibility into what an agent is currently doing or its state.
   * @param threadId The ID of the agent thread to check.
   * @returns An object containing the agent's status and other details.
   */
  static async getStatus(threadId: string): Promise<any> {
    try {
      // Get the thread
      const thread = await getThread(threadId);
      if (!thread) {
        throw new Error(`Thread ${threadId} not found`);
      }

      // Determine the status based on the last event
      const lastEvent = thread.events[thread.events.length - 1];

      let status = 'unknown';
      let message = '';

      if (lastEvent) {
        switch (lastEvent.type) {
          case 'agent_paused':
            status = 'paused';
            message = lastEvent.data.reason;
            break;

          case 'agent_cancelled':
            status = 'cancelled';
            message = lastEvent.data.reason;
            break;

          case 'done_for_now':
            status = 'completed';
            message = lastEvent.data.message;
            break;

          case 'request_human_input':
            status = 'waiting_for_human';
            message = lastEvent.data.question;
            break;

          case 'error':
            status = 'error';
            message = lastEvent.data.message;
            break;

          default:
            status = 'active';
            message = 'Agent is active';
        }
      }

      return {
        threadId,
        userId: thread.userId,
        status,
        message,
        lastUpdated: thread.updatedAt,
      };
    } catch (error) {
      console.error(`[AgentOrchestrator] Error getting agent status:`, error);
      throw error;
    }
  }

  // Simplified getOrCreateThread logic, assuming threadId is string (not null) if exists
  /**
   * Gets an existing agent thread or creates a new one if it doesn't exist.
   * What it does: Checks if a thread ID is provided and valid. If so, it uses that thread.
   * Otherwise, it creates a brand new thread for the user.
   * Why it's useful: Ensures there's always a valid conversation history (thread) to work with.
   * @param userId The ID of the user.
   * @param threadId (Optional) The ID of an existing thread.
   * @returns The ID of the existing or newly created thread.
   */
  private async getOrCreateThread(userId: string, threadId?: string | null): Promise<string> {
    if (threadId) {
      const existingThread = await getThread(threadId);
      if (existingThread) {
        return existingThread.id;
      }
    }
    const newThread = await createThread(userId);
    return newThread.id;
  }

  /**
   * Creates a run (an interaction with the AI model) and streams the response.
   * This is a core method for tasks like generating newsletters or other content.
   * What it does:
   *   - Sets up the AI with instructions (system prompt), including specific details for the task (e.g., product info for a newsletter).
   *   - Sends the user's request (prompt) to the AI model (OpenRouter).
   *   - Can handle responses in two ways:
   *     1. Streaming: Sends back the AI's response piece by piece, good for long text.
   *     2. JSON Schema: Asks the AI to structure its response in a specific JSON format.
   *   - Updates the status of the task in the database (e.g., 'outline_ready_for_review', 'failed').
   *   - Emits events to notify other parts of the system (and the UI) about progress or completion.
   * Why it's useful: This is the engine that drives the actual content generation by the AI,
   * managing the communication, data formatting, and status updates.
   * @param params Parameters for the run, including user ID, prompt, task details, etc.
   * @returns The AI's response (e.g., parsed JSON if using schema, or undefined if fully streamed to UI).
   */
  public async createRunAndStream({
    userId,
    prompt,
    threadId,
    assistantId,
    host,
    roomName,
    uiStream,
    eventEmitter,
    additionalAgentInstructions,
    context,
    responseFormat,
  }: {
    userId: number;
    prompt: string;
    threadId: string | null;
    assistantId?: string | null;
    host?: string;
    roomName?: string;
    uiStream: WritableStream;
    eventEmitter: EventEmitter;
    additionalAgentInstructions?: string;
    context: { originalTaskId: string; taskType: string; [key: string]: any };
    responseFormat?: { type: 'json_schema'; json_schema: any };
  }): Promise<any> {
    console.log(`[AgentOrchestrator] createRunAndStream for task ${context.originalTaskId}, type: ${context.taskType}`);

    const baseSystemPrompt = this.getSystemPromptForTask(context.taskType, additionalAgentInstructions);
    const currentAttemptEmitter = eventEmitter || this.eventEmitter;
    let currentThreadId: string = '';
    let accumulatedJsonString = '';

    let attempt = 0;
    let lastError: any = null;

    while (attempt < MAX_RETRIES) {
      try {
        currentThreadId = await this.getOrCreateThread(userId, threadId);
        console.log(`[AgentOrchestrator] Using threadId: ${currentThreadId} for task ${context.originalTaskId}`);

        const userPromptContent = prompt;
        let finalSystemPrompt = baseSystemPrompt;

        if (context.taskType === 'generate_newsletter_flow') {
          let additionalDetailsString =
            '\n\n**Provided Context for Newsletter Generation (use this information to tailor the newsletter):**\n';
          let contextAdded = false;

          if (context.productDetails) {
            const pd = context.productDetails;
            additionalDetailsString += '\n--- Product Details ---\n';
            if (pd.name) additionalDetailsString += `Name: ${pd.name}\n`;
            if (pd.productType) additionalDetailsString += `Type: ${pd.productType}\n`;
            if (pd.description) additionalDetailsString += `Description: ${pd.description}\n`;
            if (pd.features && Array.isArray(pd.features) && pd.features.length > 0)
              additionalDetailsString += `Key Features: ${pd.features.join(', ')}\n`;
            if (pd.targetAudience) additionalDetailsString += `Target Audience: ${pd.targetAudience}\n`;
            if (pd.usp) additionalDetailsString += `Unique Selling Proposition (USP): ${pd.usp}\n`;
            if (pd.keywords && Array.isArray(pd.keywords) && pd.keywords.length > 0)
              additionalDetailsString += `Keywords: ${pd.keywords.join(', ')}\n`;
            contextAdded = true;
          }

          if (context.brandKitDetails) {
            const bk = context.brandKitDetails;
            additionalDetailsString += '\n--- Brand Kit Details ---\n';
            if (bk.name) additionalDetailsString += `Brand Name: ${bk.name}\n`;
            if (context.productDetails?.brandName && context.productDetails.brandName !== bk.name) {
              additionalDetailsString += `Product-specific Brand Name: ${context.productDetails.brandName}\n`;
            }
            const voice: string[] = [];
            if (bk.brandPersonalityText) voice.push(String(bk.brandPersonalityText));
            if (bk.tonalityText) voice.push(String(bk.tonalityText));
            if (voice.length > 0) additionalDetailsString += `Brand Personality/Voice: ${voice.join('; ')}\n`;
            if (bk.brandValuesText) additionalDetailsString += `Brand Values: ${String(bk.brandValuesText)}\n`;
            if (bk.targetEmotionsText) additionalDetailsString += `Target Emotions: ${String(bk.targetEmotionsText)}\n`;
            if (bk.writingStyleText) additionalDetailsString += `Writing Style: ${String(bk.writingStyleText)}\n`;

            const colors: string[] = [];
            if (bk.primaryColors && Array.isArray(bk.primaryColors) && bk.primaryColors.length > 0)
              colors.push(`Primary: ${bk.primaryColors.join(', ')}`);
            if (bk.secondaryColors && Array.isArray(bk.secondaryColors) && bk.secondaryColors.length > 0)
              colors.push(`Secondary: ${bk.secondaryColors.join(', ')}`);
            if (bk.accentColors && Array.isArray(bk.accentColors) && bk.accentColors.length > 0)
              colors.push(`Accent: ${bk.accentColors.join(', ')}`);
            if (colors.length > 0) additionalDetailsString += `Key Colors: ${colors.join('; ')}\n`;

            if (bk.preferredTermsText) additionalDetailsString += `Preferred Terms: ${String(bk.preferredTermsText)}\n`;
            if (bk.avoidedTermsText) additionalDetailsString += `Avoided Terms: ${String(bk.avoidedTermsText)}\n`;
            if (bk.promptKeywordsText)
              additionalDetailsString += `AI Prompt Keywords: ${String(bk.promptKeywordsText)}\n`;
            contextAdded = true;
          }

          if (contextAdded) {
            finalSystemPrompt = additionalDetailsString + '\n\n---\n\n' + baseSystemPrompt;
            console.log('[AgentOrchestrator] Enriched SYSTEM prompt for newsletter with product/brand details.');
          } else {
            console.log('[AgentOrchestrator] No product/brand details to add to system prompt for newsletter.');
          }
        }

        const messages: OpenRouterMessage[] = [
          { role: 'system', content: finalSystemPrompt },
          { role: 'user', content: userPromptContent },
        ];

        // Re-enable non-streaming path for json_schema if not generate_newsletter_flow
        if (responseFormat?.type === 'json_schema' && context.taskType !== 'generate_newsletter_flow') {
          console.log(
            `[AgentOrchestrator] Attempting non-streaming JSON schema completion for task ${context.originalTaskId}, type: ${context.taskType}`
          );
          const parsedJsonResponse = await getOpenRouterCompletionWithSchema(
            messages,
            OPENROUTER_MODEL,
            responseFormat
          );
          console.log('[AgentOrchestrator] Successfully received and parsed non-streaming JSON response.');
          return parsedJsonResponse;
        }

        // Fallback to streaming logic (primarily for generate_newsletter_flow or text streaming)
        let currentResponseFormatForStreaming = responseFormat;
        if (context.taskType === 'generate_newsletter_flow' && !currentResponseFormatForStreaming) {
          console.log(
            '[AgentOrchestrator] Applying specific newsletterOutlineSchema for generate_newsletter_flow (streaming).'
          );
          currentResponseFormatForStreaming = { type: 'json_schema', json_schema: newsletterOutlineSchema };
        } else if (currentResponseFormatForStreaming?.type === 'json_schema') {
          // This log might be redundant if non-streaming path is taken for other json_schema tasks
          console.log(
            `[AgentOrchestrator] Using provided json_schema for streaming path: ${currentResponseFormatForStreaming.json_schema.name || 'Unnamed Schema'}`
          );
        }

        console.log(`[AgentOrchestrator] Calling streamResponse (streaming) for task ${context.originalTaskId}`);
        const readableStream = await streamResponse(
          messages,
          OPENROUTER_MODEL,
          currentResponseFormatForStreaming,
          undefined
        );

        console.log(`[AgentOrchestrator] Stream response received for ${context.originalTaskId}, processing...`);
        accumulatedJsonString = '';

        return new Promise(async (resolve, reject) => {
          try {
            await processStream(
              readableStream,
              (chunk: any) => {
                const contentDelta = chunk?.choices?.[0]?.delta?.content;
                if (contentDelta) {
                  if (currentResponseFormatForStreaming?.type === 'json_schema') {
                    accumulatedJsonString += contentDelta;
                  } else if (uiStream && !uiStream.writableEnded) {
                    uiStream.write(contentDelta);
                  }
                }
              },
              async () => {
                console.log(`[AgentOrchestrator] Stream processing completed for task ${context.originalTaskId}.`);
                if (uiStream && !uiStream.writableEnded && currentResponseFormatForStreaming?.type !== 'json_schema') {
                  uiStream.end();
                }

                if (currentResponseFormatForStreaming?.type === 'json_schema') {
                  console.log(
                    `[AgentOrchestrator] Accumulated JSON (streaming) for task ${context.originalTaskId}: ${accumulatedJsonString.substring(0, 500)}...`
                  );
                  try {
                    const parsedJson = JSON.parse(accumulatedJsonString);
                    console.log('[AgentOrchestrator] Successfully parsed JSON response (streaming).');

                    if (context.taskType === 'generate_newsletter_flow') {
                      await updateTaskStatus(context.originalTaskId, 'outline_ready_for_review', {
                        outline: parsedJson,
                        message: 'Newsletter outline is ready for your review.',
                      });
                      const outlineReadyPayload = {
                        taskId: context.originalTaskId,
                        threadId: currentThreadId,
                        outline: parsedJson,
                        clientLoadingCardId: context.client_loadingCardId,
                      };
                      currentAttemptEmitter.emit('newsletter_outline_ready', outlineReadyPayload);
                      safeEmit(userId, 'newsletter_outline_ready', outlineReadyPayload);
                      console.log(
                        `[AgentOrchestrator] Emitted 'newsletter_outline_ready' for task ${context.originalTaskId}`
                      );
                    }
                    resolve(parsedJson);
                  } catch (parseError: any) {
                    console.error(
                      `[AgentOrchestrator] Failed to parse JSON (streaming) for task ${context.originalTaskId}:`,
                      parseError,
                      'Raw string was:',
                      accumulatedJsonString
                    );
                    if (context.taskType === 'generate_newsletter_flow') {
                      await updateTaskStatus(context.originalTaskId, 'failed', {
                        error: 'Failed to process newsletter outline from LLM.',
                        rawResponse: accumulatedJsonString,
                      });
                    }
                    currentAttemptEmitter.emit('agentError', {
                      userId,
                      taskId: context.originalTaskId,
                      error: `Failed to parse JSON response (streaming): ${parseError.message}`,
                    });
                    reject(parseError);
                  }
                } else {
                  resolve(undefined);
                }
              }
            );
          } catch (streamProcessingError) {
            console.error('[AgentOrchestrator] Error during processStream:', streamProcessingError);
            reject(streamProcessingError);
          }
        });
      } catch (error: any) {
        attempt++;
        console.error(
          `[AgentOrchestrator] createRunAndStream Attempt ${attempt} failed for task ${context.originalTaskId}:`,
          error.message,
          error.stack
        );
        lastError = error;
        if (attempt < MAX_RETRIES) await delay(RETRY_DELAY_MS);
      }
    }
    console.error(
      `[AgentOrchestrator] Failed to create and stream run for task ${context.originalTaskId} after ${MAX_RETRIES} attempts.`
    );
    if (lastError) {
      try {
        await updateTaskStatus(context.originalTaskId, 'failed', {
          error: lastError.message || 'Agent orchestration failed after retries.',
        });
      } catch (statusUpdateError) {
        console.error(
          `[AgentOrchestrator] CRITICAL: Failed to update task ${context.originalTaskId} to failed status:`,
          statusUpdateError
        );
      }
    }
    currentAttemptEmitter.emit('agentError', {
      userId,
      taskId: context.originalTaskId,
      error: lastError?.message || 'Unknown agent error',
    });
    throw lastError || new Error('Failed to create and stream run after all attempts.');
  }

  /**
   * Resumes an agent thread with specific user input, typically for a multi-step task.
   * For example, after a newsletter outline is generated, the user might approve it or give feedback.
   * What it does:
   *   - Adds the user's input (e.g., "approved" or "change this section") to the agent's conversation history (thread).
   *   - Tells the agent to continue its work based on this new input (e.g., generate the full newsletter if approved).
   * Why it's useful: Enables interactive, multi-turn conversations with the agent for complex tasks,
   * allowing users to guide the agent's output.
   * @param threadId The ID of the agent thread to resume.
   * @param userInput The feedback or input from the user.
   * @param metadata Additional information about the user's action and the original task.
   */
  static async resumeWithInput(
    threadId: string,
    userInput: string,
    metadata: {
      userId: number; // User who provided the input
      originalTaskId: string; // The AgentTask this input is for
      userAction: 'approve_outline' | 'submit_outline_feedback'; // Nature of the input
      // Add any other relevant metadata to be logged or used
    }
  ): Promise<void> {
    console.log(
      `[AgentOrchestrator] Resuming thread ${threadId} for task ${metadata.originalTaskId} with user input. Action: ${metadata.userAction}`
    );

    if (!threadId || typeof userInput === 'undefined') {
      console.error('[AgentOrchestrator.resumeWithInput] threadId and userInput are required.');
      throw new Error('threadId and userInput are required to resume agent.');
    }

    try {
      // Fetch the thread to ensure it exists (optional, addEvent might do this)
      const thread = await getThread(threadId);
      if (!thread) {
        throw new HttpError(404, `Thread ${threadId} not found.`);
      }

      // Add the user's input as a new event to the thread
      // The event type and data structure should be what the agent/LLM expects or can process next.
      await addEvent(threadId, 'human_input_response', {
        contentType: metadata.userAction === 'approve_outline' ? 'approval' : 'feedback',
        content: userInput,
        originalTaskId: metadata.originalTaskId,
        timestamp: new Date().toISOString(),
        source: 'user_via_api',
      });
      console.log(`[AgentOrchestrator] Added human_input_response event to thread ${threadId}.`);

      // Trigger the next step in the agent's processing
      // processNextStep will use the updated thread (with the new event) to prompt the LLM.
      await processNextStep(threadId);
      console.log(`[AgentOrchestrator] Triggered processNextStep for thread ${threadId}.`);
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error during resumeWithInput';
      console.error(`[AgentOrchestrator.resumeWithInput] Error resuming thread ${threadId}:`, errorMessage, error);
      // This error should be caught by the calling API handler, which can update the task status.
      throw new HttpError(500, `Failed to resume agent thread ${threadId}: ${errorMessage}`);
    }
  }
}

export const agentOrchestrator = new AgentOrchestrator(new EventEmitter(), undefined);
