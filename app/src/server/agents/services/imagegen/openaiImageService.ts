/**
 * OpenAI Image Generation Service
 *
 * This file provides a service for generating and editing images using OpenAI's GPT Image 1 model.
 */

import OpenAI, { toFile } from 'openai';
import { v4 as uuidv4 } from 'uuid';
import { OPENAI_API_KEY } from '../../../config';
import { uploadToR2 } from '../../../libs/r2';
import axios from 'axios';
import { getEmitter } from '../../../../websocket/emitters';
import { imageToDataUri } from '../../taskmanagement/generationUtils';
import { emitTaskUpdate } from '../../../utils/cloudflareWorkers';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: OPENAI_API_KEY,
});

/**
 * Generate an image using OpenAI's GPT Image 1 model
 * @param prompt The prompt to generate an image from
 * @param numImages Number of images to generate (default: 1, max: 10)
 * @param size Size of the generated image (default: "1024x1024")
 * @param quality Quality of the generated image (default: "low" for gpt-image-1)
 * @param referenceImages Optional array of reference image URLs
 * @returns Array of generated image URLs
 */
export async function generateImageWithOpenAI(
  prompt: string,
  numImages: number = 1,
  size: '1024x1024' | '1536x1024' | '1024x1536' | 'auto' = '1024x1024',
  quality: 'high' | 'medium' | 'low' | 'auto' = 'low', // Default to low for testing
  referenceImages?: string[]
): Promise<string[]> {
  try {
    console.log(`[OpenAIImageService] Generating ${numImages} image(s) with prompt: ${prompt.substring(0, 50)}...`);

    // Check if reference images are provided
    if (referenceImages && referenceImages.length > 0) {
      console.log(
        `[OpenAIImageService] ${referenceImages.length} reference images provided, but GPT-image-1 does not currently support reference images directly`
      );
      console.log(`[OpenAIImageService] Proceeding with text-only prompt generation`);
      // Note: As of this version, OpenAI's image generation doesn't directly support reference images
      // This logging is added for debugging purposes
    }

    // Validate and clamp numImages
    const n = Math.min(Math.max(1, numImages), 10); // Ensure between 1 and 10

    // Generate image with OpenAI
    console.log(
      `[OpenAIImageService] Calling OpenAI API with model: gpt-image-1, n: ${n}, size: ${size}, quality: ${quality}`
    );
    const response = await openai.images.generate({
      model: 'gpt-image-1',
      prompt: prompt,
      n: n,
      size: size,
      quality: quality,
      // No response_format needed - gpt-image-1 always returns base64
    });

    console.log(`[OpenAIImageService] OpenAI API response received:`, {
      responseType: typeof response,
      hasData: !!response.data,
      dataLength: response.data?.length,
      firstItemKeys: response.data?.[0] ? Object.keys(response.data[0]) : 'no data',
    });

    // Check if response data exists
    if (!response.data || response.data.length === 0) {
      throw new Error('No data returned from OpenAI');
    }

    // Process all generated images
    console.log(`[OpenAIImageService] Processing ${response.data.length} generated images`);

    const imageUrls: string[] = [];

    for (let i = 0; i < response.data.length; i++) {
      const imageData = response.data[i]?.b64_json;
      if (!imageData) {
        console.warn(`[OpenAIImageService] No image data in response for image ${i + 1}`);
        continue;
      }

      console.log(`[OpenAIImageService] Image ${i + 1} data received:`, {
        hasImageData: true,
        dataType: typeof imageData,
        dataLength: imageData.length,
        dataPreview: `${imageData.substring(0, 20)}...`,
      });

      // Convert base64 to buffer
      const imageBuffer = Buffer.from(imageData, 'base64');

      // Generate a unique filename
      const fileName = `openai-image-${uuidv4()}.png`;

      // Upload to R2
      console.log(`[OpenAIImageService] Uploading image ${i + 1} to R2: ${fileName}`);

      try {
        const r2Url = await uploadToR2({
          fileBuffer: imageBuffer,
          fileName: fileName,
          key: 'generated-images',
          contentType: 'image/png',
        });

        console.log(`[OpenAIImageService] Image ${i + 1} uploaded to R2: ${r2Url}`);
        imageUrls.push(r2Url);
      } catch (uploadError) {
        console.error(`[OpenAIImageService] Error uploading image ${i + 1}:`, uploadError);
      }
    }

    if (imageUrls.length === 0) {
      throw new Error('Failed to process any images');
    }

    console.log(`[OpenAIImageService] Successfully processed ${imageUrls.length} images`);
    return imageUrls;
  } catch (error) {
    // Log detailed error information
    console.error('[OpenAIImageService] Error generating image with OpenAI:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : 'No stack trace',
      name: error instanceof Error ? error.name : 'Unknown error type',
    });

    // Return the new gift placeholder image URL in case of error
    return ['https://oliviatest.xyz/giftplaceholder.png'];
  }
}

/**
 * Edit an image using OpenAI's GPT Image 1 model
 * @param images Array of image URLs or base64 strings to edit
 * @param prompt The prompt describing the desired edits
 * @param mask Optional mask image URL or base64 string
 * @param numImages Number of images to generate (default: 1, max: 10)
 * @param size Size of the generated image (default: "1024x1024")
 * @param quality Quality of the generated image (default: "low" for gpt-image-1)
 * @returns Array of edited image URLs
 */
/**
 * Generate a low-quality preview image for concept cards
 * @param prompt The prompt to generate an image from
 * @param referenceImages Optional array of reference image URLs
 * @param aspectRatio Optional aspect ratio for the preview image (defaults to "1024x1024")
 * @returns URL of the generated preview image
 */
export async function generateConceptCardPreview(
  prompt: string,
  referenceImages?: string[],
  aspectRatio?: '1024x1024' | '1536x1024' | '1024x1536'
): Promise<string> {
  try {
    const previewSize = aspectRatio || '1024x1024'; // Use detected aspect ratio or default to square
    console.log(`[OpenAIImageService] Generating concept card preview with prompt: ${prompt.substring(0, 50)}...`);
    console.log(`[OpenAIImageService] Using preview size: ${previewSize}`);

    // Log reference images if provided
    if (referenceImages && referenceImages.length > 0) {
      console.log(`[OpenAIImageService] Received ${referenceImages.length} reference images for concept card preview`);
      referenceImages.forEach((img, idx) => {
        console.log(`[OpenAIImageService] Reference image ${idx + 1}: ${img ? img.substring(0, 50) + '...' : 'null'}`);
      });

      // If we have reference images, use image editing instead of generation
      console.log(`[OpenAIImageService] Using image editing for concept card preview with reference images`);
      const editedImages = await editImageWithOpenAI(
        referenceImages,
        prompt,
        undefined, // No mask
        1, // Generate only one image
        previewSize, // Use the detected aspect ratio
        'low' // Explicitly set to low quality
      );

      return editedImages[0];
    } else {
      console.log(
        `[OpenAIImageService] No reference images provided for concept card preview - using standard generation`
      );

      // Use the detected aspect ratio and low quality
      const imageUrls = await generateImageWithOpenAI(
        prompt,
        1, // Generate only one image
        previewSize, // Use the detected aspect ratio
        'low' // Explicitly set to low quality
      );

      // Return the first (and only) image URL
      return imageUrls[0];
    }
  } catch (error) {
    console.error('[OpenAIImageService] Error generating concept card preview:', error);

    // Check if it's a connection error and log more details
    if (error instanceof Error) {
      if (error.message.includes('Connection error') || error.message.includes('ECONNRESET')) {
        console.error('[OpenAIImageService] 🔌 OpenAI API connection issue detected. This may be temporary.');
        console.error('[OpenAIImageService] 💡 Consider implementing retry logic or using a fallback service.');
        console.error('[OpenAIImageService] 🔄 The system will continue with a placeholder image for now.');
      }
    }

    // Return placeholder image in case of error
    return 'https://oliviatest.xyz/giftplaceholder.png';
  }
}

export async function editImageWithOpenAI(
  images: (string | Buffer)[],
  prompt: string,
  mask?: string,
  numImages: number = 1,
  // Make size optional - no default
  size?: '1024x1024' | '1536x1024' | '1024x1536',
  quality: 'high' | 'medium' | 'low' | 'auto' = 'low' // Default to low for testing
): Promise<string[]> {
  try {
    console.log(`[OpenAIImageService] Editing image with prompt: ${prompt.substring(0, 50)}...`);
    console.log(`[OpenAIImageService] Number of images: ${images.length}`);

    // Log the reference images
    console.log(`[OpenAIImageService] Processing ${images.length} reference images`);
    images.forEach((imgInput, index) => {
      if (typeof imgInput === 'string') {
        console.log(`[OpenAIImageService] Reference image ${index + 1} (string): ${imgInput.substring(0, 50)}...`);
      } else if (Buffer.isBuffer(imgInput)) {
        console.log(`[OpenAIImageService] Reference image ${index + 1} (Buffer): size ${imgInput.length} bytes.`);
      } else {
        console.log(`[OpenAIImageService] Reference image ${index + 1}: Unknown type`);
      }
    });

    // Convert image URLs to OpenAI-compatible format
    const imageFiles = await Promise.all(
      images.map(async (imageInput, index) => {
        try {
          if (Buffer.isBuffer(imageInput)) {
            // It's already a Buffer
            console.log(`[OpenAIImageService] Processing reference image ${index + 1} from Buffer.`);
            return await toFile(imageInput, `image-${index}.png`, { type: 'image/png' });
          } else if (typeof imageInput === 'string') {
            // It's a string (URL or base64)
            if (imageInput.startsWith('data:')) {
              // It's a base64 string
              console.log(`[OpenAIImageService] Processing reference image ${index + 1} from base64 string.`);
              const base64Data = imageInput.split(',')[1];
              const buffer = Buffer.from(base64Data, 'base64');
              return await toFile(buffer, `image-${index}.png`, { type: 'image/png' });
            } else {
              // It's a URL, fetch the image
              console.log(`[OpenAIImageService] Fetching reference image ${index + 1} from URL: ${imageInput}`);
              const response = await axios.get(imageInput, {
                responseType: 'arraybuffer',
                timeout: 10000, // 10 second timeout
                headers: {
                  Accept: 'image/*',
                },
              });
              console.log(
                `[OpenAIImageService] Successfully fetched image ${index + 1}, size: ${response.data.length} bytes`
              );
              const buffer = Buffer.from(response.data);
              return await toFile(buffer, `image-${index}.png`, { type: 'image/png' });
            }
          } else {
            throw new Error('Invalid image input type. Must be a URL string, base64 string, or Buffer.');
          }
        } catch (error: any) {
          console.error(`[OpenAIImageService] Error processing reference image ${index + 1}:`, error);
          throw new Error(`Failed to process reference image ${index + 1}: ${error.message || String(error)}`);
        }
      })
    );

    // Process mask if provided
    let maskFile;
    if (mask) {
      if (mask.startsWith('data:')) {
        // It's a base64 string
        const base64Data = mask.split(',')[1];
        const buffer = Buffer.from(base64Data, 'base64');
        maskFile = await toFile(buffer, 'mask.png', { type: 'image/png' });
      } else {
        // It's a URL
        const response = await axios.get(mask, { responseType: 'arraybuffer' });
        const buffer = Buffer.from(response.data);
        maskFile = await toFile(buffer, 'mask.png', { type: 'image/png' });
      }
    }

    // Validate and clamp numImages
    const n = Math.min(Math.max(1, numImages), 10); // Ensure between 1 and 10

    // Call OpenAI API to edit the image
    // For edit requests, we should NOT specify size - let OpenAI use the original image dimensions
    const apiParams: any = {
      model: 'gpt-image-1',
      image: imageFiles,
      prompt: prompt,
      n: n,
      quality: quality,
    };

    // Only add mask if provided
    if (maskFile) {
      apiParams.mask = maskFile;

      // DEBUG: Log information about the mask and image files
      try {
        // Log information about the mask file
        console.log(`[OpenAIImageService] DEBUG: Mask file type:`, typeof maskFile);
        console.log(`[OpenAIImageService] DEBUG: Mask file name:`, maskFile.name);
        console.log(`[OpenAIImageService] DEBUG: Mask file size:`, maskFile.size);
        console.log(`[OpenAIImageService] DEBUG: Mask file type:`, maskFile.type);

        // Log information about the image file
        console.log(`[OpenAIImageService] DEBUG: Image file type:`, typeof imageFiles[0]);
        console.log(`[OpenAIImageService] DEBUG: Image file name:`, imageFiles[0].name);
        console.log(`[OpenAIImageService] DEBUG: Image file size:`, imageFiles[0].size);
        console.log(`[OpenAIImageService] DEBUG: Image file type:`, imageFiles[0].type);

        console.log(
          `[OpenAIImageService] DEBUG: If you're still seeing the 'mask size does not match image size' error, try the following:`
        );
        console.log(`[OpenAIImageService] DEBUG: 1. Make sure the mask is being properly scaled on the client side`);
        console.log(
          `[OpenAIImageService] DEBUG: 2. Check if the mask has the correct format (white transparent/black opaque)`
        );
        console.log(
          `[OpenAIImageService] DEBUG: 3. Verify that the mask dimensions exactly match the original image dimensions`
        );
      } catch (debugError) {
        console.error('[OpenAIImageService] Error logging debug info:', debugError);
      }
    }

    // Only add size if specified (should be omitted for edit requests)
    if (size) {
      apiParams.size = size;
      console.log(
        `[OpenAIImageService] Calling OpenAI API for image edit with model: gpt-image-1, n: ${n}, size: ${size}, quality: ${quality}`
      );
    } else {
      console.log(
        `[OpenAIImageService] Calling OpenAI API for image edit with model: gpt-image-1, n: ${n}, no size specified (using original dimensions), quality: ${quality}`
      );
    }

    const response = await openai.images.edit(apiParams);

    console.log(`[OpenAIImageService] OpenAI API response received:`, {
      responseType: typeof response,
      hasData: !!response.data,
      dataLength: response.data?.length,
      firstItemKeys: response.data?.[0] ? Object.keys(response.data[0]) : 'no data',
    });

    // Check if response data exists
    if (!response.data || response.data.length === 0) {
      throw new Error('No data returned from OpenAI');
    }

    // Process all generated images
    console.log(`[OpenAIImageService] Processing ${response.data.length} edited images`);

    const imageUrls: string[] = [];

    for (let i = 0; i < response.data.length; i++) {
      const imageData = response.data[i]?.b64_json;
      if (!imageData) {
        console.warn(`[OpenAIImageService] No image data in response for edited image ${i + 1}`);
        continue;
      }

      console.log(`[OpenAIImageService] Edited image ${i + 1} data received:`, {
        hasImageData: true,
        dataType: typeof imageData,
        dataLength: imageData.length,
        dataPreview: `${imageData.substring(0, 20)}...`,
      });

      // Convert base64 to buffer
      const imageBuffer = Buffer.from(imageData, 'base64');

      // Generate a unique filename
      const fileName = `openai-edited-image-${uuidv4()}.png`;

      // Upload to R2
      console.log(`[OpenAIImageService] Uploading edited image ${i + 1} to R2: ${fileName}`);

      try {
        const r2Url = await uploadToR2({
          fileBuffer: imageBuffer,
          fileName: fileName,
          key: 'generated-images',
          contentType: 'image/png',
        });

        console.log(`[OpenAIImageService] Edited image ${i + 1} uploaded to R2: ${r2Url}`);
        imageUrls.push(r2Url);
      } catch (uploadError) {
        console.error(`[OpenAIImageService] Error uploading edited image ${i + 1}:`, uploadError);
      }
    }

    if (imageUrls.length === 0) {
      throw new Error('Failed to process any edited images');
    }

    console.log(`[OpenAIImageService] Successfully processed ${imageUrls.length} edited images`);
    return imageUrls;
  } catch (error) {
    // Log detailed error information
    console.error('[OpenAIImageService] Error editing image with OpenAI:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : 'No stack trace',
      name: error instanceof Error ? error.name : 'Unknown error type',
    });

    // Propagate the error instead of returning a placeholder
    // This allows proper error handling in the UI
    throw error;
  }
}

/**
 * Generate images using OpenAI's Responses API with streaming support
 * @param prompt The prompt to generate an image from
 * @param numImages Number of images to generate (default: 1)
 * @param size Size of the generated image (default: "1024x1024")
 * @param quality Quality of the generated image (default: "auto")
 * @param referenceImages Optional array of reference image URLs or base64 strings
 * @param userId User ID for streaming updates (optional)
 * @param taskId Task ID for streaming updates (optional)
 * @returns Array of generated image URLs
 */
export async function generateImageWithResponsesAPI(
  prompt: string,
  numImages: number = 1,
  size: '1024x1024' | '1536x1024' | '1024x1536' | 'auto' = '1024x1024',
  quality: 'high' | 'medium' | 'low' | 'auto' = 'auto',
  referenceImages?: string[],
  userId?: string,
  taskId?: string,
  roomName?: string
): Promise<string[]> {
  try {
    console.log(`[OpenAIImageService] 🚀 Generating ${numImages} image(s) with Responses API`);
    console.log(`[OpenAIImageService] Prompt: ${prompt.substring(0, 100)}...`);
    console.log(`[OpenAIImageService] Size: ${size}, Quality: ${quality}`);

    const referenceImageInputs = referenceImages
      ? await Promise.all(
          referenceImages.map(async (imgUrl, index) => {
            console.log(
              `[OpenAIImageService] Fetching and converting reference image ${index + 1}: ${imgUrl.substring(0, 100)}...`
            );
            const fullDataUri = await imageToDataUri(imgUrl);
            console.log(`[OpenAIImageService] Successfully converted reference image ${index + 1} to full data URI.`);
            return {
              type: 'input_image' as const,
              image_url: fullDataUri,
            };
          })
        )
      : [];

    const instruction =
      '\n\nPlease make sure not to cut any text off in the image. That means dont generate text near the edges of the image.';

    // Combine the prompt with instructions
    const finalPrompt = `${prompt}${instruction}`;

    console.log(
      `[OpenAIImageService] Final prompt being sent (length ${finalPrompt.length}): ${finalPrompt.substring(0, 100)}...`
    );
    console.log(`[OpenAIImageService] Using size parameter: ${size} and quality: ${quality}`);

    // Since Responses API doesn't support 'n' parameter, we'll make multiple calls
    const allImageUrls: string[] = [];

    if (numImages === 1) {
      // Single image - use the original approach
      const imageUrls = await generateSingleImageWithResponsesAPI(
        finalPrompt,
        referenceImageInputs,
        size,
        quality,
        userId,
        taskId,
        undefined, // imageNumber
        roomName
      );
      allImageUrls.push(...imageUrls);
    } else {
      // Multiple images - make multiple calls
      console.log(`[OpenAIImageService] Making ${numImages} separate API calls for multiple images`);

      const imagePromises = Array.from({ length: numImages }, async (_, index) => {
        const imageTaskId = taskId ? `${taskId}-img${index + 1}` : undefined;
        console.log(`[OpenAIImageService] Starting generation for image ${index + 1}/${numImages}`);

        try {
          const imageUrls = await generateSingleImageWithResponsesAPI(
            finalPrompt,
            referenceImageInputs,
            size,
            quality,
            userId,
            imageTaskId,
            index + 1, // Pass image number for better logging
            roomName
          );
          return imageUrls;
        } catch (error) {
          console.error(`[OpenAIImageService] Error generating image ${index + 1}:`, error);
          return []; // Return empty array for failed images
        }
      });

      // Wait for all images to complete
      const imageResults = await Promise.all(imagePromises);

      // Flatten the results
      imageResults.forEach((urls) => allImageUrls.push(...urls));
    }

    if (allImageUrls.length === 0) {
      throw new Error('No images were successfully generated');
    }

    console.log(
      `[OpenAIImageService] 🎉 Successfully generated and processed ${allImageUrls.length} images with Responses API`
    );
    return allImageUrls;
  } catch (error) {
    console.error('[OpenAIImageService] Error with Responses API:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : 'No stack trace',
    });

    // Emit error if userId provided
    if (userId && taskId) {
      const emitter = getEmitter();
      emitter.emitTaskError(userId, {
        taskId,
        error: error instanceof Error ? error.message : String(error),
      });
    }

    throw error;
  }
}

/**
 * Generate a single image using OpenAI's Responses API with streaming support
 * @param finalPrompt The final prompt to use
 * @param referenceImageInputs Processed reference images
 * @param size Size of the generated image
 * @param quality Quality of the generated image
 * @param userId User ID for streaming updates (optional)
 * @param taskId Task ID for streaming updates (optional)
 * @param imageNumber Image number for logging (optional)
 * @returns Array with single generated image URL
 */
async function generateSingleImageWithResponsesAPI(
  finalPrompt: string,
  referenceImageInputs: any[],
  size: '1024x1024' | '1536x1024' | '1024x1536' | 'auto',
  quality: 'high' | 'medium' | 'low' | 'auto',
  userId?: string,
  taskId?: string,
  imageNumber?: number,
  roomName?: string
): Promise<string[]> {
  const logPrefix = imageNumber ? `[OpenAIImageService-Image${imageNumber}]` : '[OpenAIImageService]';

  console.log(`${logPrefix} Starting single image generation`);

  // Create streaming request (no 'n' parameter since Responses API doesn't support it)
  const stream = await openai.responses.create({
    model: 'gpt-4o-mini',
    input: [
      {
        role: 'user',
        content: [{ type: 'input_text', text: finalPrompt }, ...(referenceImageInputs as any[])],
      },
    ],
    tools: [
      {
        type: 'image_generation',
        partial_images: 3,
        size: size, // Pass size directly on the tool object
        quality: quality, // Pass quality directly on the tool object
        // No 'n' parameter - Responses API doesn't support it
      } as any,
    ],
    stream: true,
  });

  const imageUrls: string[] = [];
  let partialImageCount = 0;
  let finalImageCount = 0;
  let latestReceivedImageBase64: string | undefined = undefined;
  let revisedPromptFromStream: string | undefined = undefined;
  let isFirstPartialImage = true;

  // Process streaming events
  for await (const event of stream) {
    console.log(`${logPrefix} Stream event type: ${event.type}`);

    // Type assertion for streaming events (SDK types may not be complete yet)
    const eventAny = event as any;

    if (eventAny.type === 'response.image_generation_call.partial_image') {
      partialImageCount++;
      const idx = eventAny.partial_image_index;
      latestReceivedImageBase64 = eventAny.partial_image_b64;

      console.log(`${logPrefix} 📸 Received partial image ${partialImageCount} (index: ${idx})`);

      // DO NOT upload partial image to R2
      // Emit streaming update with base64 data via Cloudflare Workers
      if (userId && taskId && roomName) {
        const updatePayload: any = {
          imageBase64: latestReceivedImageBase64,
          partialIndex: idx,
        };
        if (isFirstPartialImage) {
          updatePayload.type = 'initial_partial_image';
          isFirstPartialImage = false;
        } else {
          updatePayload.type = 'update_partial_image';
        }

        console.log(`${logPrefix} 🔄 Broadcasting partial image ${partialImageCount} to room: ${roomName}`);
        await emitTaskUpdate(
          userId,
          roomName,
          taskId,
          'processing',
          Math.min((partialImageCount / 3) * 100, 95),
          updatePayload
        );
      }
    } else if (eventAny.type === 'response.image_generation_call.completed') {
      console.log(`${logPrefix} Image generation tool call completed.`);
      if (eventAny.revised_prompt) {
        revisedPromptFromStream = eventAny.revised_prompt;
        console.log(`${logPrefix} Revised prompt from stream: ${revisedPromptFromStream?.substring(0, 100)}...`);
      }
    } else if (eventAny.type === 'response.output_item.done') {
      if (eventAny.item?.type === 'image_generation_call') {
        finalImageCount++;
        console.log(`${logPrefix} 🎯 Final image data received in output_item.done`);

        const finalImageBase64 = eventAny.item.result || latestReceivedImageBase64;
        const finalRevisedPrompt = eventAny.item.revised_prompt || revisedPromptFromStream;

        if (finalRevisedPrompt) {
          console.log(`${logPrefix} Revised prompt for final image: ${finalRevisedPrompt?.substring(0, 100)}...`);
        }

        if (!finalImageBase64) {
          console.error(`${logPrefix} No final image data found in output_item.done or from latest partial`);
          continue; // Skip if no image data
        }

        // Upload final image to R2
        try {
          const imageBuffer = Buffer.from(finalImageBase64, 'base64');
          const fileName = `responses-image-${uuidv4()}.jpeg`; // Assuming jpeg, adjust if format varies

          const r2Url = await uploadToR2({
            fileBuffer: imageBuffer,
            fileName: fileName,
            key: 'generated-images',
            contentType: 'image/jpeg', // Assuming jpeg
          });

          console.log(`${logPrefix} ✅ Uploaded final image to R2: ${r2Url}`);
          imageUrls.push(r2Url);

          // Emit final image update via Cloudflare Workers if userId provided
          if (userId && taskId && roomName) {
            console.log(`${logPrefix} 🎯 Broadcasting final image to room: ${roomName}`);
            await emitTaskUpdate(
              userId,
              roomName,
              taskId,
              'processing', // Keep as processing until all images are done
              100, // This single image is complete
              {
                type: 'final_image',
                imageUrl: r2Url,
                revisedPrompt: finalRevisedPrompt,
              }
            );
          }
        } catch (uploadError) {
          console.error(`${logPrefix} Error uploading final image:`, uploadError);
          // Optionally emit an error specific to this image
        }
      }
    } else if (eventAny.type === 'response.image_generation_call.failed') {
      console.error(`${logPrefix} Image generation call failed. This is unexpected and should not happen.`);
    }
  }

  if (imageUrls.length === 0 && finalImageCount === 0) {
    throw new Error('No final images were successfully processed and uploaded from the stream.');
  }

  console.log(`${logPrefix} ✅ Successfully generated ${imageUrls.length} image(s)`);
  return imageUrls;
}
