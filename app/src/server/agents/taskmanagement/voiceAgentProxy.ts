/**
 * Enhanced Voice Agent Proxy
 *
 * This file provides an enhanced version of the voice agent proxy that supports
 * asynchronous processing with placeholders.
 */

import { v4 as uuid } from 'uuid';
import { prisma } from 'wasp/server';
import { processTask } from './taskProcessor';
import { emitPlaceholder } from './canvasEmitter';

/**
 * Enhanced voice agent proxy
 */
export const enhancedVoiceAgentProxy = async (req: any, res: any, context: any) => {
  try {
    // Extract request data from query parameters
    const request = req.query.request;
    const roomName = req.query.roomName;
    const userId = req.query.userId ?? 1;
    const productId = req.query.productId;
    const modelId = req.query.modelId;

    // Log what we extracted
    console.log('[EnhancedVoiceAgentProxy] Extracted request:', request);
    console.log('[EnhancedVoiceAgentProxy] Extracted roomName:', roomName);
    console.log('[EnhancedVoiceAgentProxy] Extracted userId:', userId);

    // Validate required fields
    if (!request) {
      return res.status(400).json({
        success: false,
        message: 'Request is required',
        error: 'Request is required',
      });
    }

    if (!roomName) {
      return res.status(400).json({
        success: false,
        message: 'Room name is required',
        error: 'Room name is required',
      });
    }

    // Check if the request is for generic HTML generation, which is no longer supported
    if (typeof request === 'string' && request.startsWith('generate_html:')) {
      console.warn(`[EnhancedVoiceAgentProxy] Received request for unsupported 'generate_html'. Request: ${request}`);
      return res.status(400).json({
        success: false,
        message: 'Generic HTML generation is no longer supported.',
        error: 'Unsupported request type',
      });
    }

    // Re-use placeholder passed by the client if any, otherwise create one
    const placeholderId = req.query.placeholderId || `placeholder-${uuid()}`;
    const clientLoadingCardId = req.query.clientLoadingCardId || placeholderId;

    const emitServerSidePlaceholder = !req.query.placeholderId; // only emit if backend had to create it

    if (!req.query.placeholderId) {
      console.log('[EnhancedVoiceAgentProxy] Created placeholder ID (server generated):', placeholderId);
    } else {
      console.log('[EnhancedVoiceAgentProxy] Using client supplied placeholder ID:', placeholderId);
    }

    // Create a task record in the database
    const task = await prisma.agentTask.create({
      data: {
        userId,
        requestType: 'pending_intent', // Will be updated after intent determination
        request: request.substring(0, 10000), // Truncate very long content
        status: 'pending',
        placeholderId,
        result: {
          roomName,
          productId,
          modelId,
        },
        metadata: {
          client_loadingCardId: clientLoadingCardId,
        },
      },
    });

    console.log('[EnhancedVoiceAgentProxy] Created task record:', task.id);

    // Emit a placeholder only if backend generated it (SSR / non-browser calls)
    if (emitServerSidePlaceholder) {
      console.log(`[EnhancedVoiceAgentProxy] Emitting placeholder because client did not.`);
      emitPlaceholder(userId, placeholderId, request, task.id);
    }

    // Start processing the task asynchronously
    processTask(task.id).catch((error) => {
      console.error(`[EnhancedVoiceAgentProxy] Error processing task ${task.id}:`, error);
    });

    // Return immediately with success
    return res.json({
      success: true,
      taskId: task.id,
      placeholderId,
      message: "I'm processing your request. You'll see the results in your workspace shortly.",
      requiresCanvas: true,
    });
  } catch (error) {
    console.error('[EnhancedVoiceAgentProxy] Error:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while processing your request.',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};
