/**
 * Tool Executor
 *
 * This file provides functions for executing tools.
 */

// Im confused about this file will comeback to it, take a look at Taskprocessor.ts for more tool usage

import { ToolName } from './toolDefinitions';
import { addEvent } from '../core/threadService';
import { safeEmit } from '../utils/emitUtils';
import { processNextStep } from '../core/eventHandlers';
import { getEmitter } from '../../../websocket/emitters';

import { prisma } from 'wasp/server';

/**
 * Execute an intent based on its type
 */
export async function executeIntent(threadId: string, userId: string, intent: any): Promise<void> {
  const intentType = intent.intent as ToolName;

  switch (intentType) {
    case 'generate_image':
      await executeGenerateImage(threadId, userId, intent);
      break;

    case 'generate_newsletter':
      await executeGenerateNewsletter(threadId, userId, intent);
      break;

    case 'request_human_input':
      await executeRequestHumanInput(threadId, userId, intent);
      break;

    case 'done_for_now':
      await executeDoneForNow(threadId, userId, intent);
      break;

    default:
      throw new Error(`Unknown intent type: ${intentType}`);
  }
}

/**
 * Execute the generate_image intent
 */
async function executeGenerateImage(threadId: string, userId: string, intent: any): Promise<void> {
  try {
    // Notify the user that we're generating an image
    safeEmit(userId, 'update_progress', {
      threadId,
      message: 'Generating image...',
      progress: 0,
    });

    // Generate the image
    const result = await generateImage(intent.prompt, intent.aspectRatio, intent.negativePrompt);

    // Add the result to the thread
    await addEvent(threadId, 'generate_image_result', result);

    // Notify the user that the image is ready
    safeEmit(userId, 'update_progress', {
      threadId,
      message: 'Image generated successfully',
      progress: 100,
    });

    // Continue processing
    await processNextStep(threadId);
  } catch (error: any) {
    console.error(`[ToolExecutor] Error executing generate_image:`, error);

    // Add the error to the thread
    await addEvent(threadId, 'error', {
      message: error.message || 'Unknown error',
      stack: error.stack || 'No stack trace',
    });

    // Notify the user
    safeEmit(userId, 'update_progress', {
      threadId,
      message: `Error generating image: ${error.message || 'Unknown error'}`,
      progress: 0,
      error: true,
    });

    // Continue processing to let the LLM handle the error
    await processNextStep(threadId);
  }
}

/**
 * Execute the generate_newsletter intent
 */
async function executeGenerateNewsletter(threadId: string, userId: string, intent: any): Promise<void> {
  try {
    // Notify the user that we're generating a newsletter
    safeEmit(userId, 'update_progress', {
      threadId,
      message: 'Generating newsletter...',
      progress: 0,
      type: 'generate_newsletter',
      requestType: 'generate_newsletter',
      taskId: intent.taskId, // Include the task ID
      placeholderId: intent.placeholderId, // Include the placeholder ID
    });

    // NOTE: Newsletter generation now uses the new AI-powered system in taskProcessor.ts
    // This old template-based approach has been replaced by the agent-based newsletter flow
    // The new system handles: outline generation → approval → section prompts → image generation → HTML assembly

    console.log(
      `[ToolExecutor] Newsletter generation request received - this should be handled by the new agent system`
    );

    // For now, return a placeholder result to avoid breaking the flow
    // In practice, newsletter generation should go through the new agent system
    const result = {
      success: true,
      message: 'Newsletter generation initiated through new agent system',
      mobileHtml: '<div>Newsletter generation in progress...</div>',
      desktopHtml: '<div>Newsletter generation in progress...</div>',
    };

    // Add the result to the thread
    await addEvent(threadId, 'generate_newsletter_result', result);

    // Notify the user that the newsletter is ready
    safeEmit(userId, 'update_progress', {
      threadId,
      message: 'Newsletter generated successfully',
      progress: 100,
      result: result,
      type: 'generate_newsletter',
      requestType: 'generate_newsletter',
      taskId: intent.taskId,
      placeholderId: intent.placeholderId,
    });

    // Also emit a direct canvas update with the result
    if (intent.taskId && intent.placeholderId) {
      const emitter = getEmitter();
      if (emitter) {
        console.log(`[ToolExecutor] Emitting direct canvas update for newsletter task ${intent.taskId}`);
        emitter.emitCanvasElementAdd(userId, {
          elementId: intent.placeholderId,
          elementType: 'generate_newsletter',
          position: { x: 100, y: 100 },
          size: { width: 400, height: 600 },
          content: {
            placeholderId: intent.placeholderId,
            taskId: intent.taskId,
            requestType: 'generate_newsletter',
            status: 'completed',
            result: result,
          },
          taskId: intent.taskId,
        });
      }
    }

    // Continue processing
    await processNextStep(threadId);
  } catch (error: any) {
    console.error(`[ToolExecutor] Error executing generate_newsletter:`, error);

    // Add the error to the thread
    await addEvent(threadId, 'error', {
      message: error.message || 'Unknown error',
      stack: error.stack || 'No stack trace',
    });

    // Notify the user
    safeEmit(userId, 'update_progress', {
      threadId,
      message: `Error generating newsletter: ${error.message || 'Unknown error'}`,
      progress: 0,
      error: true,
    });

    // Continue processing to let the LLM handle the error
    await processNextStep(threadId);
  }
}

/**
 * Execute the request_human_input intent
 */
async function executeRequestHumanInput(threadId: string, userId: string, intent: any): Promise<void> {
  try {
    const taskId = intent.originalTaskId;

    if (!taskId) {
      console.error(
        '[ToolExecutor] executeRequestHumanInput: originalTaskId is missing in the intent object. Cannot update task.',
        intent
      );
      safeEmit(userId, 'request_human_input', {
        threadId,
        question: intent.question,
        context: intent.context,
        options: intent.options,
        error: 'Task context missing, unable to save state.',
      });
      await addEvent(threadId, 'error', {
        message: 'Tool execution error: originalTaskId missing for request_human_input',
      });
      return;
    }

    console.log(`[ToolExecutor] executeRequestHumanInput for task ${taskId}. Outline:`, intent.question);

    const existingTask = await prisma.agentTask.findUnique({ where: { id: taskId } });

    if (existingTask) {
      const updatedDataForTask: any = {
        status: 'pending_user_approval',
        result: {
          ...(typeof existingTask.result === 'object' && existingTask.result !== null ? existingTask.result : {}),
          human_input_request: {
            type: 'newsletter_outline_approval',
            timestamp: new Date().toISOString(),
            question: intent.question,
            agentContext: intent.context,
            options: intent.options,
            message: 'Newsletter outline ready for your approval.',
          },
        },
        updatedAt: new Date(),
      };

      await prisma.agentTask.delete({ where: { id: taskId } });
      await prisma.agentTask.create({
        data: {
          ...existingTask,
          ...updatedDataForTask,
          id: existingTask.id,
          userId: existingTask.userId,
          requestType: existingTask.requestType,
          request: existingTask.request,
          placeholderId: existingTask.placeholderId,
          metadata: typeof existingTask.metadata === 'object' ? existingTask.metadata : {},
          x: existingTask.x,
          y: existingTask.y,
          width: existingTask.width,
          height: existingTask.height,
          result: updatedDataForTask.result,
          status: updatedDataForTask.status,
        },
      });

      console.log(`[ToolExecutor] Task ${taskId} updated to pending_user_approval with newsletter outline.`);
    } else {
      console.error(`[ToolExecutor] Task ${taskId} not found. Cannot update status for human input request.`);
      await addEvent(threadId, 'error', { message: `Task ${taskId} not found during request_human_input.` });
    }

    safeEmit(userId, 'request_human_input', {
      threadId,
      taskId: taskId,
      question: intent.question,
      context: intent.context,
      options: intent.options,
    });
  } catch (error: any) {
    console.error(`[ToolExecutor] Error executing request_human_input for task ${intent.originalTaskId}:`, error);
    await addEvent(threadId, 'error', {
      message: error.message || 'Unknown error during request_human_input',
      stack: error.stack || 'No stack trace',
    });
    safeEmit(userId, 'update_progress', {
      threadId,
      taskId: intent.originalTaskId,
      message: `Error requesting human input: ${error.message || 'Unknown error'}`,
      progress: 0,
      error: true,
    });
  }
}

/**
 * Execute the done_for_now intent
 */
async function executeDoneForNow(threadId: string, userId: string, intent: any): Promise<void> {
  try {
    // Notify the user that we're done
    safeEmit(userId, 'agent_complete', {
      threadId,
      message: intent.message,
      taskId: intent.taskId,
      placeholderId: intent.placeholderId,
      result: intent.result,
      type: intent.type || 'generic',
      requestType: intent.requestType,
    });

    // Also emit a direct canvas update with the result
    if (intent.taskId && intent.placeholderId) {
      const emitter = getEmitter();
      if (emitter) {
        console.log(`[ToolExecutor] Emitting direct canvas update for completed task ${intent.taskId}`);
        emitter.emitCanvasElementAdd(userId, {
          elementId: intent.placeholderId,
          elementType: intent.requestType || 'generic',
          position: { x: 100, y: 100 },
          size: { width: 400, height: 300 },
          content: {
            placeholderId: intent.placeholderId,
            taskId: intent.taskId,
            requestType: intent.requestType,
            status: 'completed',
            result: intent.result, // Include the result
          },
          taskId: intent.taskId,
        });
      }
    }

    // Don't continue processing - we're done
  } catch (error: any) {
    console.error(`[ToolExecutor] Error executing done_for_now:`, error);

    // Add the error to the thread
    await addEvent(threadId, 'error', {
      message: error.message || 'Unknown error',
      stack: error.stack || 'No stack trace',
    });

    // Notify the user
    safeEmit(userId, 'update_progress', {
      threadId,
      message: `Error completing task: ${error.message || 'Unknown error'}`,
      progress: 0,
      error: true,
    });

    // Continue processing to let the LLM handle the error
    await processNextStep(threadId);
  }
}

/**
 * Generate an image based on a prompt
 */
async function generateImage(prompt: string, aspectRatio?: string, negativePrompt?: string): Promise<any> {
  try {
    console.log(`[ImageHandler] Generating image with prompt: ${prompt}`);

    // This is a placeholder - in a real implementation, you would call your image generation service
    // For now, we'll just return a placeholder URL with a real image
    const imageUrl = `https://oliviatest.xyz/giftplaceholder.png`;

    return {
      success: true,
      imageUrl,
      prompt,
      aspectRatio,
      negativePrompt,
    };
  } catch (error: any) {
    console.error(`[ImageHandler] Error generating image:`, error);
    throw error;
  }
}
