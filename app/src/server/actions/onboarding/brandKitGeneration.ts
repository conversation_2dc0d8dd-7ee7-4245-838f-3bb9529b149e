import { HttpError } from 'wasp/server';
import OpenAI from 'openai';
import { authenticateUser } from '../../helpers';

const openRouter = new OpenAI({
  baseURL: 'https://openrouter.ai/api/v1',
  apiKey: process.env.OPENROUTER_API_KEY!,
  defaultHeaders: {
    'HTTP-Referer': 'https://olivia.ai',
    'X-Title': 'Olivia Brand Kit Generator',
  },
});

interface BrandKitInput {
  organizationId: string;
  brandName: string;
  websiteData?: any;
  brandGuideData?: any;
  shopifyData?: any;
  assetData?: any;
}

type BrandKitOutput = {
  id: string;
  name: string;
  organizationId: string;
};

export const generateBrandKit = async (args: BrandKitInput, context: any): Promise<BrandKitOutput> => {
  const user = authenticateUser(context);

  const { organizationId, brandName, websiteData, brandGuideData, shopifyData, assetData } = args;

  try {
    console.log(`[BrandKitGeneration] Generating brand kit for ${brandName}`);

    // Verify organization exists and user has access
    const organization = await context.entities.Organization.findUniqueOrThrow({
      where: { id: organizationId },
      include: {
        memberships: {
          where: { userId: user.id },
        },
      },
    });

    if (organization.memberships.length === 0) {
      throw new HttpError(403, 'User does not have access to this organization');
    }

    const generator = new BrandKitGenerator({
      organizationId,
      brandName,
      websiteData,
      brandGuideData,
      shopifyData,
      assetData,
    });

    const brandKit = await generator.generate();

    // Store brand kit
    const storedBrandKit = await storeBrandKit(context, organizationId, user.id, brandName, brandKit);

    return {
      id: storedBrandKit.id,
      name: storedBrandKit.name,
      organizationId: storedBrandKit.organizationId,
    };
  } catch (error) {
    console.error('[BrandKitGeneration] Generation failed:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    throw new HttpError(500, `Brand kit generation failed: ${errorMessage}`);
  }
};

class BrandKitGenerator {
  private data: any;

  constructor(data: any) {
    this.data = data;
  }

  async generate(): Promise<any> {
    const synthesisPrompt = this.buildSynthesisPrompt();

    const response = await openRouter.chat.completions.create({
      model: 'anthropic/claude-3.5-sonnet',
      messages: [{ role: 'user', content: synthesisPrompt }],
      response_format: {
        type: 'json_schema',
        json_schema: {
          name: 'brand_kit',
          strict: true,
          schema: {
            type: 'object',
            properties: {
              brandIdentity: {
                type: 'object',
                properties: {
                  mission: { type: 'string' },
                  vision: { type: 'string' },
                  values: { type: 'array', items: { type: 'string' } },
                  positioning: { type: 'string' },
                  personality: { type: 'string' },
                },
                required: ['mission', 'vision', 'values', 'positioning', 'personality'],
                additionalProperties: false,
              },
              visualIdentity: {
                type: 'object',
                properties: {
                  primaryColors: { type: 'array', items: { type: 'string' } },
                  secondaryColors: { type: 'array', items: { type: 'string' } },
                  typography: {
                    type: 'object',
                    properties: {
                      primary: { type: 'string' },
                      secondary: { type: 'string' },
                      hierarchy: { type: 'array', items: { type: 'string' } },
                    },
                    required: ['primary', 'secondary', 'hierarchy'],
                    additionalProperties: false,
                  },
                  logoGuidelines: { type: 'string' },
                  imageStyle: { type: 'string' },
                },
                required: ['primaryColors', 'secondaryColors', 'typography', 'logoGuidelines', 'imageStyle'],
                additionalProperties: false,
              },
              voiceAndTone: {
                type: 'object',
                properties: {
                  brandVoice: { type: 'string' },
                  toneAttributes: { type: 'array', items: { type: 'string' } },
                  communicationStyle: { type: 'string' },
                  dosDonts: {
                    type: 'object',
                    properties: {
                      dos: { type: 'array', items: { type: 'string' } },
                      donts: { type: 'array', items: { type: 'string' } },
                    },
                    required: ['dos', 'donts'],
                    additionalProperties: false,
                  },
                },
                required: ['brandVoice', 'toneAttributes', 'communicationStyle', 'dosDonts'],
                additionalProperties: false,
              },
              messaging: {
                type: 'object',
                properties: {
                  tagline: { type: 'string' },
                  valuePropositions: { type: 'array', items: { type: 'string' } },
                  keyMessages: { type: 'array', items: { type: 'string' } },
                  elevatorPitch: { type: 'string' },
                },
                required: ['tagline', 'valuePropositions', 'keyMessages', 'elevatorPitch'],
                additionalProperties: false,
              },
              applicationGuidelines: {
                type: 'object',
                properties: {
                  logoUsage: { type: 'string' },
                  colorUsage: { type: 'string' },
                  typographyUsage: { type: 'string' },
                  imageryGuidelines: { type: 'string' },
                },
                required: ['logoUsage', 'colorUsage', 'typographyUsage', 'imageryGuidelines'],
                additionalProperties: false,
              },
            },
            required: ['brandIdentity', 'visualIdentity', 'voiceAndTone', 'messaging', 'applicationGuidelines'],
            additionalProperties: false,
          },
        },
      },
    });

    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error('No content received from AI');
    }

    return JSON.parse(content);
  }

  private buildSynthesisPrompt(): string {
    return `Create a comprehensive brand kit for "${this.data.brandName}" using this data:

WEBSITE DATA: ${JSON.stringify(this.data.websiteData, null, 2).slice(0, 5000)}
BRAND GUIDE: ${JSON.stringify(this.data.brandGuideData, null, 2).slice(0, 5000)}
SHOPIFY DATA: ${JSON.stringify(this.data.shopifyData, null, 2).slice(0, 3000)}
ASSETS: ${JSON.stringify(this.data.assetData, null, 2).slice(0, 2000)}

Generate a complete, professional brand kit including:
1. Brand identity (mission, vision, values, positioning)
2. Visual identity (colors, typography, logo guidelines)
3. Voice and tone guidelines
4. Key messaging and taglines
5. Application guidelines

Ensure consistency across all elements and professional quality suitable for immediate use.

Colors should be in hex format (e.g., #FF0000).
Typography should include actual font names.
All text should be professionally written and ready for use in brand guidelines.`;
  }
}

// Store brand kit function
async function storeBrandKit(
  context: any,
  organizationId: string,
  userId: string,
  brandName: string,
  brandKit: any
): Promise<any> {
  // Map the AI-generated brand kit to the database schema
  const brandKitData = {
    name: brandName,
    userId: userId,
    organizationId: organizationId,

    // Visual Identity
    primaryColors: brandKit.visualIdentity?.primaryColors || [],
    secondaryColors: brandKit.visualIdentity?.secondaryColors || [],
    accentColors: [], // Will be populated later if needed
    typography: brandKit.visualIdentity?.typography || {},
    logoVariations: [],
    moodboardImages: [],

    // Color guidelines text
    primaryColorUsageText: brandKit.applicationGuidelines?.colorUsage || '',
    secondaryColorUsageText: '',
    accentColorUsageText: '',
    colorHierarchyText: '',
    colorAccessibilityText: '',
    colorCombinationsText: '',
    colorVariationsText: '',

    // Logo Guidelines
    logoUsageGuidelinesText: brandKit.applicationGuidelines?.logoUsage || '',
    logoSizingText: '',
    logoClearSpaceText: '',
    logoColorUsageText: '',
    logoFileFormatsText: '',
    logoMisuseText: '',

    // Photography Guidelines
    photoStyleText: brandKit.visualIdentity?.imageStyle || '',
    preferredAnglesText: '',
    lightingPreferencesText: '',
    backgroundStylesText: '',
    propGuidelinesText: '',
    commonScenesText: '',
    photoDosDonts: {},

    // Brand Voice
    brandPersonalityText: brandKit.brandIdentity?.personality || '',
    tonalityText: brandKit.voiceAndTone?.brandVoice || '',
    brandValuesText: brandKit.brandIdentity?.values?.join(', ') || '',
    targetEmotionsText: '',

    // Language & Phrasing
    writingStyleText: brandKit.voiceAndTone?.communicationStyle || '',
    preferredTermsText: brandKit.voiceAndTone?.dosDonts?.dos?.join(', ') || '',
    avoidedTermsText: brandKit.voiceAndTone?.dosDonts?.donts?.join(', ') || '',
    grammarRulesText: '',
    contentStructureText: '',
    localizationRulesText: '',
    commonMistakesText: '',

    // Taglines
    primaryTaglinesText: brandKit.messaging?.tagline || '',
    secondaryTaglinesText: '',
    campaignTaglinesText: '',
    keyMessagesText: brandKit.messaging?.keyMessages?.join('\n') || '',
    valuePropositionsText: brandKit.messaging?.valuePropositions?.join('\n') || '',
    taglineGuidelinesText: '',
    trademarkInfoText: '',

    // Target Audience
    primaryAudienceText: '',
    primaryAudienceGoalsText: '',
    primaryAudienceChallengesText: '',
    secondaryAudienceText: '',
    secondaryAudienceGoalsText: '',
    secondaryAudienceChallengesText: '',
    tertiaryAudienceText: '',
    tertiaryAudienceGoalsText: '',
    audiencePersonasText: '',
    audienceValuePropsText: '',
    communicationChannelsText: '',
    audienceMetricsText: '',

    // Data Visualization
    preferredChartTypesText: '',
    chartExamplesText: '',
    dataVizColorsText: '',
    dataVizTypographyText: '',
    dataVizLayoutText: '',
    dataVizInteractivityText: '',
    dataVizAccessibilityText: '',
    dataVizBestPracticesText: '',
    dataVizMistakesText: '',

    // Web Design
    webTypographyText: brandKit.applicationGuidelines?.typographyUsage || '',
    webSpacingText: '',
    uiComponentExamplesText: '',
    uiComponentsText: '',
    buttonStylesText: '',
    formGuidelinesText: '',
    responsiveGuidelinesText: '',
    webAccessibilityText: '',
    performanceGuidelinesText: '',
    animationGuidelinesText: '',

    // Social Media
    platformGuidelinesText: '',
    socialMediaExamplesText: '',
    visualStyleGuidelinesText: '',
    contentTypesText: '',
    socialToneGuidelinesText: '',
    hashtagGuidelinesText: '',
    engagementGuidelinesText: '',
    postingScheduleText: '',
    socialComplianceText: '',
    analyticsGuidelinesText: '',

    // AI Generation Settings
    promptKeywordsText: '',
    avoidanceTermsText: '',
    preferredSettings: {},

    // Metadata
    tags: ['auto-generated'],
    coverImage: '',
  };

  const createdBrandKit = await context.entities.BrandKit.create({
    data: brandKitData,
  });

  return createdBrandKit;
}
