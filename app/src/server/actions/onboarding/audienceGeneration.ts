import { HttpError } from 'wasp/server';
import OpenAI from 'openai';
import { authenticateUser } from '../../helpers';

const openRouter = new OpenAI({
  baseURL: 'https://openrouter.ai/api/v1',
  apiKey: process.env.OPENROUTER_API_KEY!,
  defaultHeaders: {
    'HTTP-Referer': 'https://olivia.ai',
    'X-Title': 'Olivia Audience Generator',
  },
});

interface AudienceInput {
  organizationId: string;
  brandKit: any;
  websiteData?: any;
  shopifyData?: any;
  numberOfAudiences?: number;
}

type AudienceOutput = {
  id: number;
  personaName: string;
  organizationId: string;
};

export const generateAudiences = async (args: AudienceInput, context: any): Promise<AudienceOutput[]> => {
  const user = authenticateUser(context);

  const { organizationId, brandKit, websiteData, shopifyData, numberOfAudiences = 3 } = args;

  try {
    console.log(`[AudienceGeneration] Generating ${numberOfAudiences} audiences for organization ${organizationId}`);

    // Verify organization exists and user has access
    const organization = await context.entities.Organization.findUniqueOrThrow({
      where: { id: organizationId },
      include: {
        memberships: {
          where: { userId: user.id },
        },
      },
    });

    if (organization.memberships.length === 0) {
      throw new HttpError(403, 'User does not have access to this organization');
    }

    const generator = new AudienceGenerator({
      organizationId,
      brandKit,
      websiteData,
      shopifyData,
      numberOfAudiences,
    });

    const audiences = await generator.generate();

    // Store audiences
    const storedAudiences = await storeAudiences(context, organizationId, user.id, audiences);

    return storedAudiences.map((audience) => ({
      id: audience.id,
      personaName: audience.personaName || '',
      organizationId: audience.organizationId,
    }));
  } catch (error) {
    console.error('[AudienceGeneration] Generation failed:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    throw new HttpError(500, `Audience generation failed: ${errorMessage}`);
  }
};

class AudienceGenerator {
  private data: any;

  constructor(data: any) {
    this.data = data;
  }

  async generate(): Promise<any[]> {
    const prompt = `Generate ${this.data.numberOfAudiences} distinct target audience personas based on:

BRAND KIT: ${JSON.stringify(this.data.brandKit, null, 2).slice(0, 5000)}
WEBSITE DATA: ${JSON.stringify(this.data.websiteData, null, 2).slice(0, 3000)}
SHOPIFY DATA: ${JSON.stringify(this.data.shopifyData, null, 2).slice(0, 3000)}

Create comprehensive personas including demographics, psychographics, behaviors, pain points, and brand relationship.`;

    const response = await openRouter.chat.completions.create({
      model: 'anthropic/claude-3.5-sonnet',
      messages: [{ role: 'user', content: prompt }],
      response_format: {
        type: 'json_schema',
        json_schema: {
          name: 'audiences',
          strict: true,
          schema: {
            type: 'object',
            properties: {
              audiences: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    name: { type: 'string' },
                    demographics: {
                      type: 'object',
                      properties: {
                        ageRange: { type: 'string' },
                        gender: { type: 'string' },
                        income: { type: 'string' },
                        education: { type: 'string' },
                        location: { type: 'string' },
                        occupation: { type: 'string' },
                      },
                      required: ['ageRange', 'gender', 'income', 'education', 'location', 'occupation'],
                      additionalProperties: false,
                    },
                    psychographics: {
                      type: 'object',
                      properties: {
                        values: { type: 'array', items: { type: 'string' } },
                        interests: { type: 'array', items: { type: 'string' } },
                        lifestyle: { type: 'string' },
                        personality: { type: 'string' },
                      },
                      required: ['values', 'interests', 'lifestyle', 'personality'],
                      additionalProperties: false,
                    },
                    behaviors: {
                      type: 'object',
                      properties: {
                        shoppingHabits: { type: 'string' },
                        mediaConsumption: { type: 'array', items: { type: 'string' } },
                        brandLoyalty: { type: 'string' },
                        decisionFactors: { type: 'array', items: { type: 'string' } },
                      },
                      required: ['shoppingHabits', 'mediaConsumption', 'brandLoyalty', 'decisionFactors'],
                      additionalProperties: false,
                    },
                    painPoints: { type: 'array', items: { type: 'string' } },
                    goals: { type: 'array', items: { type: 'string' } },
                    brandRelationship: { type: 'string' },
                    marketingChannels: { type: 'array', items: { type: 'string' } },
                    messagingTone: { type: 'string' },
                    bio: { type: 'string' },
                    quote: { type: 'string' },
                  },
                  required: [
                    'name',
                    'demographics',
                    'psychographics',
                    'behaviors',
                    'painPoints',
                    'goals',
                    'brandRelationship',
                    'marketingChannels',
                    'messagingTone',
                    'bio',
                    'quote',
                  ],
                  additionalProperties: false,
                },
              },
            },
            required: ['audiences'],
            additionalProperties: false,
          },
        },
      },
    });

    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error('No content received from AI');
    }

    return JSON.parse(content).audiences;
  }
}

// Store audiences function
async function storeAudiences(context: any, organizationId: string, userId: string, audiences: any[]): Promise<any[]> {
  // Delete existing audiences for this organization
  await context.entities.Audience.deleteMany({
    where: {
      organizationId: organizationId,
      userId: userId,
    },
  });

  // Create new audiences
  const createdAudiences: any[] = [];

  for (const audience of audiences) {
    const audienceData = {
      userId: userId,
      organizationId: organizationId,
      personaName: audience.name,
      bio: audience.bio,
      quote: audience.quote,

      // Demographics
      age: audience.demographics.ageRange,
      gender: audience.demographics.gender,
      occupation: audience.demographics.occupation,
      incomeLevel: audience.demographics.income,
      education: audience.demographics.education,
      location: audience.demographics.location,

      // Goals and challenges
      goals: audience.goals.join(', '),
      challenges: audience.painPoints.join(', '),
      motivations: audience.psychographics.values.join(', '),
      problemsToSolve: audience.painPoints.join(', '),

      // Psychographics
      attitudes: audience.psychographics.personality,
      interests: audience.psychographics.interests.join(', '),
      lifestyle: audience.psychographics.lifestyle,
      hobbies: audience.psychographics.interests.join(', '),

      // Behaviors
      onlineActivities: audience.behaviors.mediaConsumption.join(', '),
      buyingHabits: audience.behaviors.shoppingHabits,
      mediaConsumption: audience.behaviors.mediaConsumption.join(', '),

      // Professional (if applicable)
      jobTitle: audience.demographics.occupation,
      seniority: '',
      decisionMakingPower: audience.behaviors.brandLoyalty,
      toolsUsed: '',
      budget: audience.demographics.income,
      professionalObjective: audience.goals.join(', '),
      challengesAndPainPoints: audience.painPoints.join(', '),

      // Purchase factors
      price: '',
      quality: audience.behaviors.decisionFactors.join(', '),
      convenience: '',
      obstaclesToPurchase: audience.painPoints.join(', '),
    };

    const createdAudience = await context.entities.Audience.create({
      data: audienceData,
    });

    createdAudiences.push(createdAudience);
  }

  return createdAudiences;
}
