/**
 * Synthesis WebSocket
 *
 * This file provides WebSocket functionality for AI synthesis progress updates.
 */
import { getIO } from '../../../websocket/emitters';
import { USER_ROOM_CHANNEL } from '../../../websocket/constants';

// Progress update interface
interface SynthesisProgress {
  stage: string;
  progress: number;
  message: string;
  completed: boolean;
  timestamp?: string;
  metadata?: any;
}

// Synthesis session interface
interface SynthesisSession {
  sessionId: string;
  userId: string;
  organizationId: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: number;
  currentStage?: string;
  completedStages: string[];
  errors: any[];
}

// Global storage for active synthesis sessions (in-memory)
const activeSessions = new Map<string, SynthesisSession>();

// Database persistence for session recovery
import { prisma } from 'wasp/server';

/**
 * Emit synthesis progress to a specific user's room
 */
export async function emitSynthesisProgress(
  sessionId: string,
  userId: number,
  progress: SynthesisProgress
): Promise<void> {
  try {
    // Get the Socket.IO instance
    const io = getIO();
    if (!io) {
      console.warn('[SynthesisWebSocket] Socket.IO instance not available');
      return;
    }

    // Add timestamp if not provided
    const progressWithTimestamp = {
      ...progress,
      timestamp: progress.timestamp || new Date().toISOString(),
      sessionId,
    };

    // Emit to user's room
    const userRoom = `synthesis_${sessionId}_${userId}`;
    io.to(userRoom).emit('synthesis_progress', progressWithTimestamp);

    // Also emit to general user room for fallback
    const generalUserRoom = USER_ROOM_CHANNEL + userId;
    io.to(generalUserRoom).emit('synthesis_progress', progressWithTimestamp);

    console.log(`[SynthesisWebSocket] Progress emitted to ${userRoom}:`, progressWithTimestamp);

    // Update session status
    updateSessionProgress(sessionId, progress);
  } catch (error) {
    console.error('[SynthesisWebSocket] Failed to emit progress:', error);
  }
}

/**
 * Emit synthesis completion
 */
export async function emitSynthesisComplete(sessionId: string, userId: number, result: any): Promise<void> {
  try {
    const io = getIO();
    if (!io) {
      console.warn('[SynthesisWebSocket] Socket.IO instance not available');
      return;
    }

    const completionData = {
      sessionId,
      result,
      timestamp: new Date().toISOString(),
      status: 'completed',
    };

    // Emit to user's rooms
    const userRoom = `synthesis_${sessionId}_${userId}`;
    const generalUserRoom = USER_ROOM_CHANNEL + userId;

    io.to(userRoom).emit('synthesis_complete', completionData);
    io.to(generalUserRoom).emit('synthesis_complete', completionData);

    console.log(`[SynthesisWebSocket] Completion emitted to ${userRoom}`);

    // Update session status
    const session = activeSessions.get(sessionId);
    if (session) {
      session.status = 'completed';
      activeSessions.set(sessionId, session);
    }
  } catch (error) {
    console.error('[SynthesisWebSocket] Failed to emit completion:', error);
  }
}

/**
 * Emit synthesis error
 */
export async function emitSynthesisError(sessionId: string, userId: number, error: string | Error): Promise<void> {
  try {
    const io = getIO();
    if (!io) {
      console.warn('[SynthesisWebSocket] Socket.IO instance not available');
      return;
    }

    const errorData = {
      sessionId,
      error: error instanceof Error ? error.message : error,
      timestamp: new Date().toISOString(),
      status: 'failed',
    };

    // Emit to user's rooms
    const userRoom = `synthesis_${sessionId}_${userId}`;
    const generalUserRoom = USER_ROOM_CHANNEL + userId;

    io.to(userRoom).emit('synthesis_error', errorData);
    io.to(generalUserRoom).emit('synthesis_error', errorData);

    console.log(`[SynthesisWebSocket] Error emitted to ${userRoom}:`, errorData);

    // Update session status
    const session = activeSessions.get(sessionId);
    if (session) {
      session.status = 'failed';
      session.errors.push(errorData);
      activeSessions.set(sessionId, session);
    }
  } catch (error) {
    console.error('[SynthesisWebSocket] Failed to emit error:', error);
  }
}

/**
 * Start a new synthesis session with database persistence
 */
export async function startSynthesisSession(sessionId: string, userId: string, organizationId: string): Promise<void> {
  const session: SynthesisSession = {
    sessionId,
    userId,
    organizationId,
    status: 'running',
    startTime: Date.now(),
    completedStages: [],
    errors: [],
  };

  // Store in memory for fast access
  activeSessions.set(sessionId, session);

  // Persist to database for recovery
  try {
    await prisma.onboardingSession.upsert({
      where: { sessionId },
      update: {
        status: session.status,
        currentStage: session.currentStage,
        completedStages: JSON.stringify(session.completedStages),
        errors: JSON.stringify(session.errors),
        updatedAt: new Date(),
      },
      create: {
        sessionId,
        userId,
        organizationId,
        status: session.status,
        currentStage: session.currentStage,
        completedStages: JSON.stringify(session.completedStages),
        errors: JSON.stringify(session.errors),
        startTime: new Date(session.startTime),
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });
    console.log(`[SynthesisWebSocket] Started and persisted session: ${sessionId}`);
  } catch (error) {
    console.error(`[SynthesisWebSocket] Failed to persist session ${sessionId}:`, error);
    // Continue with in-memory session even if DB fails
  }
}

/**
 * Get synthesis session status with database fallback
 */
export async function getSynthesisSession(sessionId: string): Promise<SynthesisSession | null> {
  // First check in-memory cache
  let session = activeSessions.get(sessionId);

  if (session) {
    return session;
  }

  // If not in memory, try to recover from database
  try {
    const dbSession = await prisma.onboardingSession.findUnique({
      where: { sessionId },
    });

    if (dbSession) {
      // Reconstruct session from database
      session = {
        sessionId: dbSession.sessionId,
        userId: dbSession.userId,
        organizationId: dbSession.organizationId,
        status: dbSession.status as 'running' | 'completed' | 'failed' | 'cancelled',
        startTime: dbSession.startTime.getTime(),
        currentStage: dbSession.currentStage || undefined,
        completedStages: dbSession.completedStages ? JSON.parse(dbSession.completedStages) : [],
        errors: dbSession.errors ? JSON.parse(dbSession.errors) : [],
      };

      // Restore to memory cache
      activeSessions.set(sessionId, session);
      console.log(`[SynthesisWebSocket] Recovered session from database: ${sessionId}`);

      return session;
    }
  } catch (error) {
    console.error(`[SynthesisWebSocket] Failed to recover session ${sessionId}:`, error);
  }

  return null;
}

/**
 * Check if user has any active synthesis sessions
 */
export async function getUserActiveSessions(userId: string): Promise<SynthesisSession[]> {
  try {
    const dbSessions = await prisma.onboardingSession.findMany({
      where: {
        userId,
        status: 'running',
        // Only sessions from last 24 hours
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000),
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    const sessions: SynthesisSession[] = [];

    for (const dbSession of dbSessions) {
      const session: SynthesisSession = {
        sessionId: dbSession.sessionId,
        userId: dbSession.userId,
        organizationId: dbSession.organizationId,
        status: dbSession.status as 'running' | 'completed' | 'failed' | 'cancelled',
        startTime: dbSession.startTime.getTime(),
        currentStage: dbSession.currentStage || undefined,
        completedStages: dbSession.completedStages ? JSON.parse(dbSession.completedStages) : [],
        errors: dbSession.errors ? JSON.parse(dbSession.errors) : [],
      };

      // Restore to memory cache
      activeSessions.set(session.sessionId, session);
      sessions.push(session);
    }

    return sessions;
  } catch (error) {
    console.error(`[SynthesisWebSocket] Failed to get user sessions for ${userId}:`, error);
    return [];
  }
}

/**
 * Update session progress
 */
function updateSessionProgress(sessionId: string, progress: SynthesisProgress): void {
  const session = activeSessions.get(sessionId);
  if (!session) return;

  session.currentStage = progress.stage;

  // Add to completed stages if this stage is completed
  if (progress.completed && !session.completedStages.includes(progress.stage)) {
    session.completedStages.push(progress.stage);
  }

  activeSessions.set(sessionId, session);
}

/**
 * Clean up completed sessions (call this periodically)
 */
export function cleanupSynthesisSessions(): void {
  const now = Date.now();
  const maxAge = 24 * 60 * 60 * 1000; // 24 hours

  for (const [sessionId, session] of activeSessions.entries()) {
    const age = now - session.startTime;
    if (age > maxAge || session.status === 'completed' || session.status === 'failed') {
      activeSessions.delete(sessionId);
      console.log(`[SynthesisWebSocket] Cleaned up session: ${sessionId}`);
    }
  }
}

/**
 * List all active synthesis sessions (for debugging/monitoring)
 */
export function getActiveSynthesisSessions(): SynthesisSession[] {
  return Array.from(activeSessions.values());
}

/**
 * WebSocket event handlers for synthesis
 * This extends the existing WebSocket functionality
 */
export function setupSynthesisWebSocketHandlers(socket: any, io: any): void {
  // Handle synthesis session joining
  socket.on('join_synthesis', async ({ sessionId, userId }: { sessionId: string; userId: number }) => {
    if (!sessionId || !userId) {
      socket.emit('synthesis_error', { message: 'sessionId and userId are required' });
      return;
    }

    const roomName = `synthesis_${sessionId}_${userId}`;
    socket.join(roomName);

    // Also join general user room
    const generalUserRoom = USER_ROOM_CHANNEL + userId;
    socket.join(generalUserRoom);

    console.log(`[SynthesisWebSocket] Socket ${socket.id} joined synthesis room: ${roomName}`);

    // Send current session status if available
    const session = await getSynthesisSession(sessionId);
    if (session) {
      socket.emit('synthesis_session_status', {
        sessionId,
        status: session.status,
        currentStage: session.currentStage,
        completedStages: session.completedStages,
        errors: session.errors,
      });
    }

    socket.emit('synthesis_joined', { roomName, sessionId });
  });

  // Handle synthesis session leaving
  socket.on('leave_synthesis', ({ sessionId, userId }: { sessionId: string; userId: number }) => {
    if (!sessionId || !userId) return;

    const roomName = `synthesis_${sessionId}_${userId}`;
    socket.leave(roomName);

    console.log(`[SynthesisWebSocket] Socket ${socket.id} left synthesis room: ${roomName}`);
  });

  // Handle synthesis status request
  socket.on('get_synthesis_status', async ({ sessionId }: { sessionId: string }) => {
    const session = await getSynthesisSession(sessionId);
    if (session) {
      socket.emit('synthesis_session_status', {
        sessionId,
        status: session.status,
        currentStage: session.currentStage,
        completedStages: session.completedStages,
        errors: session.errors,
        startTime: session.startTime,
      });
    } else {
      socket.emit('synthesis_session_status', {
        sessionId,
        status: 'not_found',
      });
    }
  });

  // Handle audience import via WebSocket
  socket.on(
    'importAudience',
    async ({ taskId, brandKitId, productId }: { taskId: string; brandKitId: string; productId: number }) => {
      const userId = socket.data.userId;
      const userRoom = USER_ROOM_CHANNEL + userId;

      if (!userId) {
        socket.emit('error', { message: 'Authentication required' });
        return;
      }

      console.log(
        `[SynthesisWebSocket] Import audience request from user ${userId} for brandKit: ${brandKitId}, product: ${productId}`
      );

      try {
        // Import the modular audience action
        const { importAudience } = await import(
          '../../../features/audience/infrastructure/wasp/actions/import-audience.action'
        );

        // Get the user from the database to create proper AuthUser context
        const user = await (global as any).prisma.user.findUnique({
          where: { id: userId },
          include: {
            auth: {
              include: {
                identities: true,
              },
            },
          },
        });

        if (!user) {
          socket.emit('error', { message: 'User not found' });
          return;
        }

        // Create proper context for the action
        const context = {
          user,
          entities: (global as any).prisma || require('@prisma/client').PrismaClient(),
        };

        // Call the import action with proper input structure
        const input = {
          taskId,
          brandKitId,
          productId,
          organizationId: user.organizationId || '', // Get from user if available
        };
        const result = await importAudience(input, context);

        // Emit success result
        io.to(userRoom).emit('result', result);
      } catch (error) {
        console.error('[Import Audience] Error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        io.to(userRoom).emit('error', { message: errorMessage });
      }
    }
  );
}

// Set up periodic cleanup
setInterval(cleanupSynthesisSessions, 60 * 60 * 1000); // Run every hour
