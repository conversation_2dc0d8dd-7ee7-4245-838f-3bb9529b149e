import { google } from 'googleapis';
import { HttpError } from 'wasp/server';
import type { ProxyGoogleDriveImage } from 'wasp/server/operations';
import { getGoogleRefreshToken } from '../../services/googleOAuthService';
import axios from 'axios';

/**
 * Creates and returns a Google OAuth2 client using Redis-stored tokens
 * @param userId The user ID to get the token for
 */
async function getOAuth2Client(userId: string) {
  const clientId = process.env.GOOGLE_CLIENT_ID;
  const clientSecret = process.env.GOOGLE_CLIENT_SECRET;
  const redirectUri = process.env.SERVER_URL + '/api/google/oauth/callback';

  if (!clientId || !clientSecret) {
    throw new Error(
      'Missing Google OAuth credentials. Please set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET environment variables.'
    );
  }

  const oauth2Client = new google.auth.OAuth2(clientId, clientSecret, redirectUri);

  const tokenData = await getGoogleRefreshToken(userId);

  if (tokenData) {
    oauth2Client.setCredentials({
      refresh_token: tokenData.refreshToken,
    });
    return oauth2Client;
  }

  return oauth2Client;
}

/**
 * Proxies an image from Google Drive to avoid CORS issues
 * This function fetches the image from Google Drive and returns it as a base64 data URL
 */
export const proxyGoogleDriveImage: ProxyGoogleDriveImage<{ fileId: string }, { base64: string }> = async (
  args,
  context
) => {
  // Check if user is authenticated
  if (!context.user) {
    throw new HttpError(401, 'You must be logged in to access Google Drive files');
  }

  try {
    console.log('Proxying Google Drive image:', args.fileId);

    // Get OAuth2 client with credentials
    const oauth2Client = await getOAuth2Client(context.user.id);
    const drive = google.drive({ version: 'v3', auth: oauth2Client });

    // Get the file metadata to determine content type
    const fileMetadata = await drive.files.get({
      fileId: args.fileId,
      fields: 'mimeType',
    });

    // Get the file content
    const response = await drive.files.get(
      {
        fileId: args.fileId,
        alt: 'media',
      },
      { responseType: 'arraybuffer' }
    );

    // Convert to base64
    const buffer = Buffer.from(response.data as any);
    const base64 = buffer.toString('base64');
    const contentType = fileMetadata.data.mimeType || 'image/jpeg';

    // Return with proper data URL prefix
    return {
      base64: `data:${contentType};base64,${base64}`,
    };
  } catch (error: any) {
    console.error('Error proxying Google Drive image:');
    console.error('Error message:', error.message);
    console.error('Error stack:', error.stack);

    if (error.response) {
      console.error('API Error Response:', error.response.data);
      console.error('API Error Status:', error.response.status);
    }

    // Try alternative method using axios if the Google API fails
    try {
      console.log('Trying alternative method to fetch image');

      // Get a direct download URL
      const downloadUrl = `https://www.googleapis.com/drive/v3/files/${args.fileId}?alt=media`;

      // Get OAuth2 client with credentials
      const oauth2Client = await getOAuth2Client(context.user.id);

      // Get access token
      const { token } = await oauth2Client.getAccessToken();

      if (!token) {
        throw new Error('Failed to get access token');
      }

      // Fetch the image with the access token
      const response = await axios.get(downloadUrl, {
        responseType: 'arraybuffer',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      // Convert to base64
      const buffer = Buffer.from(response.data);
      const base64 = buffer.toString('base64');
      const contentType = response.headers['content-type'] || 'image/jpeg';

      // Return with proper data URL prefix
      return {
        base64: `data:${contentType};base64,${base64}`,
      };
    } catch (altError) {
      console.error('Alternative method also failed:', altError);
      throw new HttpError(500, 'Failed to proxy Google Drive image');
    }
  }
};
