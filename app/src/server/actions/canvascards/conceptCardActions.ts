import { v4 as uuid } from 'uuid';
import axios from 'axios';
import { processTask, processTaskWithReferenceImages } from '../../agents/taskmanagement/taskProcessor';
import { prisma } from 'wasp/server';
import { getEmitter } from '../../../websocket/emitters';
import { getProductDetailsFromModelId, createSystemPrompt } from '../../agents/llm/utils';
import { generateConceptCardPreview } from '../../agents/services/imagegen/openaiImageService';
import { getReferenceImages } from '../../agents/services/reference/referenceService';

// Type definitions
interface OpenRouterOptions {
  model?: string;
  response_format?: {
    type: string;
    [key: string]: any;
  };
  [key: string]: any;
}

interface OpenRouterMessage {
  role: string;
  content: string | OpenRouterMessageContent[];
}

interface OpenRouterMessageContent {
  type: string;
  text?: string;
  image_url?: {
    url: string;
  };
}

interface ConceptCardResult {
  concept: string;
  prompt: string;
  questions: string[];
  previewImageUrl?: string;
}

// Define the ConceptCard type to match the database schema
interface ConceptCard {
  id: string;
  userId: number;
  concept: string;
  prompt: string;
  questions: string;
  answers: string | null;
  status: string;
  taskId: string | null;
  previewImageUrl: string | null;
  productId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

interface CreateConceptCardParams {
  request: string;
  userId?: number;
  productId?: string;
  referenceImages?: string[];
  aspectRatio?: '1024x1024' | '1536x1024' | '1024x1536';
  canvasId?: string;
}

interface UpdateConceptCardParams {
  cardId: string;
  questionIndex: number;
  answer: string;
  userId?: number;
  productId?: string;
  referenceImages?: string[];
}

interface GenerateFromConceptCardParams {
  cardId: string;
  userId?: number;
  productId?: string | number;
  referenceImages?: string[];
  aspectRatio?: '1024x1024' | '1536x1024' | '1024x1536';
  numImages?: number;
  canvasId?: string;
}

// OpenRouter API configuration
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;
const SITE_URL = process.env.SITE_URL || 'https://example.com';
const SITE_NAME = process.env.SITE_NAME || 'My SaaS App';

/**
 * Call OpenRouter API
 */
async function callOpenRouter(
  messages: OpenRouterMessage[],
  options: OpenRouterOptions = {},
  referenceImages: Array<{ imageUrl?: string }> = []
) {
  try {
    // Prepare the request body
    const requestBody: any = {
      model: options.model || 'openai/gpt-4.1',
      messages: [...messages], // Clone the messages array
      response_format: options.response_format || { type: 'text' },
    };

    // Add reference images if available
    if (referenceImages && referenceImages.length > 0) {
      console.log(
        `[ConceptCardActions] Processing ${referenceImages.length} reference images:`,
        JSON.stringify(referenceImages)
      );

      const imageUrls = referenceImages
        .filter((img) => img && typeof img.imageUrl === 'string')
        .map((img) => img.imageUrl)
        .slice(0, 5); // Limit to 5 images

      console.log(`[ConceptCardActions] Filtered image URLs for OpenRouter call:`, JSON.stringify(imageUrls));
      console.log(`[ConceptCardActions] Including ${imageUrls.length} reference images in OpenRouter call`);

      if (imageUrls.length > 0) {
        // Find the user message (typically the last one)
        const userMessageIndex = requestBody.messages.findIndex((msg: OpenRouterMessage) => msg.role === 'user');

        if (userMessageIndex !== -1) {
          const userMessage = requestBody.messages[userMessageIndex];
          const originalUserText =
            typeof userMessage.content === 'string' ? userMessage.content : 'Invalid user message content'; // Handle potential array case incorrectly added before

          // Create a new content array for the user message
          const contentArray: OpenRouterMessageContent[] = [{ type: 'text', text: originalUserText }];

          // Add each image URL to the content array
          imageUrls.forEach((url) => {
            if (url) {
              console.log(`[ConceptCardActions] Adding image URL to USER content array:`, url);
              contentArray.push({
                type: 'image_url',
                image_url: { url },
              } as OpenRouterMessageContent);
            }
          });

          console.log(
            `[ConceptCardActions] Final USER content array has ${contentArray.length} items (1 text + ${contentArray.length - 1} images)`
          );

          // Update the user message with the new content array
          requestBody.messages[userMessageIndex].content = contentArray;
          console.log(`[ConceptCardActions] Updated USER message with images`);
        } else {
          console.warn(`[ConceptCardActions] Could not find user message to attach images to.`);
        }
      } else {
        console.log(`[ConceptCardActions] No valid image URLs found in reference images`);
      }
    } else {
      console.log(`[ConceptCardActions] No reference images provided for OpenRouter call`);
    }

    const response = await axios.post('https://openrouter.ai/api/v1/chat/completions', requestBody, {
      headers: {
        Authorization: `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': SITE_URL,
        'X-Title': SITE_NAME,
        'Content-Type': 'application/json',
      },
    });

    return response.data.choices[0].message.content;
  } catch (error) {
    console.error('[OpenRouter] Error calling API:', error);
    throw error;
  }
}

// Import Cloudflare Workers utilities at the top of the file
import { emitConceptCardCreate, emitConceptCardUpdate, getCanvasRoomId } from '../../utils/cloudflareWorkers';

/**
 * Emit a canvas event via Cloudflare Workers
 */
async function emitCanvasEvent(userId: string, eventType: string, data: any, canvasId?: string) {
  try {
    // FAIL FAST: Require canvasId - no fallbacks to prevent chaos
    if (!canvasId) {
      const error = `[CloudflareEmitter] ❌ FAIL FAST: canvasId is required for ${eventType}. No fallbacks allowed.`;
      console.error(error);
      throw new Error(error);
    }

    const roomId = getCanvasRoomId(canvasId);

    console.log(`[CloudflareEmitter] 🏠 ROOM ID DETERMINATION:`);
    console.log(`[CloudflareEmitter] - Canvas ID: ${canvasId}`);
    console.log(`[CloudflareEmitter] - User ID: ${userId}`);
    console.log(`[CloudflareEmitter] - Final Room ID: ${roomId}`);
    console.log(`[CloudflareEmitter] Emitting ${eventType} to user ${userId} in room ${roomId}:`, data);

    // Use the appropriate emitter method based on the event
    if (eventType === 'createConceptCard') {
      const success = await emitConceptCardCreate(userId, roomId, data);
      if (success) {
        console.log(`[CloudflareEmitter] Emitted createConceptCard event to user ${userId}`);
      } else {
        console.error(`[CloudflareEmitter] Failed to emit createConceptCard event to user ${userId}`);
      }
    } else if (eventType === 'updateConceptCard') {
      const success = await emitConceptCardUpdate(userId, roomId, data.id, data);
      if (success) {
        console.log(`[CloudflareEmitter] Emitted updateConceptCard event to user ${userId}`);
      } else {
        console.error(`[CloudflareEmitter] Failed to emit updateConceptCard event to user ${userId}`);
      }
    } else {
      console.warn(`[CloudflareEmitter] Unknown event type: ${eventType}`);
    }
  } catch (error) {
    console.error(`[CloudflareEmitter] Error emitting ${eventType}:`, error);
  }
}

/**
 * Create a concept card from a user request
 */
export const createConceptCard = async (
  {
    request,
    userId,
    loadingCardId,
    productId,
    referenceImages,
    aspectRatio,
    canvasId,
  }: CreateConceptCardParams & { loadingCardId?: string },
  context: any
) => {
  // Get user ID from context if not provided
  const effectiveUserId = userId || context?.user?.id || 1;

  // Add detailed logging for user ID resolution
  console.log(`[ConceptCardActions] 🔍 USER ID RESOLUTION:`);
  console.log(`[ConceptCardActions] - Passed userId: ${userId}`);
  console.log(`[ConceptCardActions] - Context user ID: ${context?.user?.id}`);
  console.log(`[ConceptCardActions] - Effective user ID: ${effectiveUserId}`);

  try {
    console.log(`[ConceptCardActions] Creating concept card for request: ${request}`);
    console.log(`[ConceptCardActions] Aspect ratio: ${aspectRatio || 'not specified, will use default'}`);

    // If referenceImages are provided, use them directly
    const refImagesToUse = referenceImages && referenceImages.length > 0 ? referenceImages : undefined;

    // Generate initial concept, prompt, questions, and preview image using OpenRouter
    const { concept, prompt, questions, previewImageUrl } = await generateInitialConcept(
      request,
      productId,
      context,
      refImagesToUse,
      aspectRatio
    );

    // Create a unique ID for the card
    const cardId = `concept-${uuid()}`;

    // Store the aspect ratio in the answers field as metadata
    const initialAnswers = { aspectRatio: aspectRatio || '1024x1024' };

    // Create a concept card record in the database
    await prisma.conceptCard.create({
      data: {
        id: cardId,
        userId: effectiveUserId,
        concept,
        prompt,
        questions: JSON.stringify(questions),
        status: 'questioning',
        answers: JSON.stringify(initialAnswers), // Store aspect ratio in answers as metadata
        previewImageUrl, // Include the preview image URL
        productId, // Store the product ID
        canvasId, // Store the canvas ID for bulletproof room targeting
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    // Generate suggestion chips for the initial prompt
    console.log(`[ConceptCardActions] Generating initial suggestion chips for prompt: ${prompt.substring(0, 50)}...`);
    let suggestionChips: string[] = [];
    try {
      suggestionChips = await generateSuggestionChips(prompt);
      console.log(
        `[ConceptCardActions] Generated ${suggestionChips.length} initial suggestion chips:`,
        suggestionChips
      );

      // If no chips were generated, use fallback chips
      if (suggestionChips.length === 0) {
        suggestionChips = [
          'Add more vibrant colors',
          'Make it more minimalist',
          'Change lighting style',
          'Add a tagline',
        ];
        console.log(`[ConceptCardActions] Using fallback suggestion chips:`, suggestionChips);
      }
    } catch (error) {
      console.error(`[ConceptCardActions] Error generating initial suggestion chips:`, error);

      // Use fallback chips on error
      suggestionChips = [
        'Add more vibrant colors',
        'Make it more minimalist',
        'Change lighting style',
        'Add a tagline',
      ];
      console.log(`[ConceptCardActions] Using fallback suggestion chips after error:`, suggestionChips);
    }

    // Prepare the card data for the client
    const cardData = {
      id: cardId,
      concept,
      prompt, // Include the prompt
      questions,
      position: { x: 100, y: 100 }, // Default position
      answers: [],
      status: 'questioning',
      loadingCardId, // Include the loading card ID if provided
      previewImageUrl, // Include the preview image URL
      productId, // Include the product ID
      suggestionChips, // Include suggestion chips
    };

    // Emit an event to create the card on the canvas
    console.log(`[ConceptCardActions] 🚀 EMITTING CONCEPT CARD:`);
    console.log(`[ConceptCardActions] - Target user ID: ${effectiveUserId}`);
    console.log(`[ConceptCardActions] - Card ID: ${cardData.id}`);
    console.log(`[ConceptCardActions] - Canvas ID: ${canvasId}`);
    console.log(`[ConceptCardActions] - Event: createConceptCard`);

    // Add loadingCardId to the cardData being emitted
    const eventData = {
      ...cardData,
      loadingCardId: loadingCardId, // Ensure loadingCardId is passed in the event
    };

    await emitCanvasEvent(effectiveUserId, 'createConceptCard', eventData, canvasId);

    return cardData;
  } catch (error) {
    console.error('[ConceptCardActions] Error creating concept card:', error);
    throw error;
  }
};

/**
 * Generate initial concept, prompt, and questions using OpenRouter
 */
async function generateInitialConcept(
  request: string,
  productId?: string,
  context?: any,
  referenceImagesOverride?: string[],
  aspectRatio?: '1024x1024' | '1536x1024' | '1024x1536'
): Promise<ConceptCardResult> {
  try {
    // Get product information if productId is provided
    let brandSystemPrompt = '';
    // Use the reference images passed directly from the client (via referenceImagesOverride)
    const refImagesToUse: string[] =
      referenceImagesOverride && referenceImagesOverride.length > 0 ? referenceImagesOverride : [];
    console.log(`[generateInitialConcept] Received ${refImagesToUse.length} reference images from client.`);

    if (productId) {
      try {
        console.log(`[ConceptCardActions] Getting product details for ID: ${productId}`);
        const product = await getProductDetailsFromModelId(productId, context);

        if (product) {
          console.log(`[ConceptCardActions] Found product: ${product.name}`);
          brandSystemPrompt = createSystemPrompt(product);
          console.log(`[ConceptCardActions] Created brand system prompt with length: ${brandSystemPrompt.length}`);

          // **** REMOVED the call to getReferenceImages from DB ****
          // We now rely solely on the referenceImagesOverride passed from the client.
        } else {
          console.log(`[ConceptCardActions] No product found for ID: ${productId}`);
        }
      } catch (error) {
        console.error(`[ConceptCardActions] Error getting product details:`, error);
      }
    }

    // Convert string[] to Array<{ imageUrl?: string }> for callOpenRouter if needed
    const referenceImagesForOpenRouter: Array<{ imageUrl?: string }> = refImagesToUse.map((url) => ({ imageUrl: url }));

    // Create a system prompt for OpenRouter
    const systemPrompt = `You are an AI assistant that helps create content generation concepts.
    ${referenceImagesForOpenRouter.length > 0 ? 'IMPORTANT: Look carefully at the provided reference image(s). Analyze their visual style, composition, and key elements.' : ''}
    Given a user request${referenceImagesForOpenRouter.length > 0 ? ' and the reference image(s)' : ''}, create:
    1. A concise title for the concept (e.g., "Lifestyle Instagram Ad" or "Apple Product Photo")
    2. An initial prompt that would work well for generating this content${referenceImagesForOpenRouter.length > 0 ? ' in the style of the reference image(s)' : ''}
    3. Three follow-up questions to refine the concept further

    Format your response as JSON with the following structure:
    {
      "concept": "Concept title",
      "prompt": "Initial prompt",
      "questions": ["Question 1", "Question 2", "Question 3"]
    }

    ${brandSystemPrompt ? '## Brand Information\n\n' + brandSystemPrompt + '\n\nIncorporate the brand information above into your concept, prompt, and questions when relevant.' : ''}
    ${referenceImagesForOpenRouter.length > 0 ? '\n\n## Reference Images\nPay close attention to the provided reference image(s). Use them as inspiration for style, composition, and visual elements in your concept and prompt.' : ''}`;

    // Create messages for OpenRouter
    const messages = [
      {
        role: 'system',
        content: systemPrompt,
      },
      {
        role: 'user',
        content: request,
      },
    ];

    // Call OpenRouter with reference images
    const response = await callOpenRouter(
      messages,
      {
        model: 'openai/gpt-4.1',
        response_format: { type: 'json_object' },
      },
      referenceImagesForOpenRouter
    ); // Pass the formatted array

    // Parse the response
    let parsedContent;
    try {
      // Try to parse the response as JSON
      parsedContent = JSON.parse(response);
    } catch (e) {
      // If parsing fails, try to extract JSON from the text
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        parsedContent = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('Failed to parse response as JSON');
      }
    }

    // Validate the response
    if (!parsedContent.concept || !parsedContent.prompt || !Array.isArray(parsedContent.questions)) {
      throw new Error('Invalid response format from OpenRouter');
    }

    // Generate a preview image for the prompt
    console.log(`[ConceptCardActions] Generating preview image for prompt`);
    console.log(`[ConceptCardActions] Passing ${refImagesToUse.length} reference images to preview generator`);
    const previewImageUrl = await generateConceptCardPreview(parsedContent.prompt, refImagesToUse, aspectRatio); // Pass the original string[] and aspect ratio
    console.log(`[ConceptCardActions] Preview image generated: ${previewImageUrl}`);

    return {
      concept: parsedContent.concept,
      prompt: parsedContent.prompt,
      questions: parsedContent.questions,
      previewImageUrl,
    };
  } catch (error) {
    console.error('[ConceptCardActions] Error generating initial concept:', error);
    throw error;
  }
}

/**
 * Update a concept card with a user input
 */
export const updateConceptCard = async (
  { cardId, questionIndex, answer, userId, productId, referenceImages }: UpdateConceptCardParams,
  context: any
) => {
  // Get user ID from context if not provided
  const effectiveUserId = userId || context?.user?.id || 1;
  try {
    console.log(`[ConceptCardActions] Updating concept card ${cardId} with input: ${answer}`);

    // Get the current card
    const card = (await prisma.conceptCard.findUnique({
      where: { id: cardId },
    })) as ConceptCard | null;

    if (!card) {
      throw new Error(`Concept card ${cardId} not found`);
    }

    // Parse questions and answers
    const questions = JSON.parse(card.questions || '[]');
    const answers = JSON.parse(card.answers || '{}');

    // Update the answer (preserving aspectRatio if it exists)
    if (typeof answers === 'object' && !Array.isArray(answers)) {
      // New format: answers is an object with metadata
      answers[`answer_${questionIndex}`] = answer;
    } else {
      // Legacy format: convert array to object and preserve aspectRatio
      const legacyAnswers = Array.isArray(answers) ? answers : [];
      const newAnswers: any = { aspectRatio: '1024x1024' }; // Default aspect ratio for legacy cards

      // Convert legacy array answers to new object format
      legacyAnswers.forEach((ans, idx) => {
        newAnswers[`answer_${idx}`] = ans;
      });

      // Add the new answer
      newAnswers[`answer_${questionIndex}`] = answer;

      // Replace answers with new format
      Object.assign(answers, newAnswers);
    }

    // Use the effective product ID
    const effectiveProductId = productId || card.productId || undefined;

    // Log the reference images
    console.log(`[ConceptCardActions] Received ${referenceImages?.length || 0} reference images from client`);

    // Update the prompt based on the input and get a new preview image and suggestion chips
    const { updatedPrompt, previewImageUrl, suggestionChips } = await updatePrompt(
      card.prompt,
      questions[0] || 'How would you like to refine this concept?',
      answer,
      effectiveProductId,
      context,
      referenceImages
    );

    // Update the card in the database
    await prisma.conceptCard.update({
      where: { id: cardId },
      data: {
        prompt: updatedPrompt,
        answers: JSON.stringify(answers),
        previewImageUrl,
        productId: productId || card.productId, // Update the product ID if provided
        updatedAt: new Date(),
      },
    });

    // Prepare the updated card data for the client
    const updatedCardData = {
      id: cardId,
      concept: card.concept,
      prompt: updatedPrompt, // Include the updated prompt
      questions,
      answers: answers, // Return the answers object directly
      status: 'questioning',
      previewImageUrl, // Include the preview image URL
      productId: card.productId || productId, // Include the product ID from the card or the parameter
      suggestionChips, // Include the suggestion chips
    };

    // Emit an event to update the card on the canvas
    await emitCanvasEvent(effectiveUserId, 'updateConceptCard', updatedCardData);

    return updatedCardData;
  } catch (error) {
    console.error('[ConceptCardActions] Error updating concept card:', error);
    throw error;
  }
};

/**
 * Generate suggestion chips for refining a prompt
 */
async function generateSuggestionChips(prompt: string): Promise<string[]> {
  try {
    // Create a system prompt for OpenRouter
    const systemPrompt = `You are an AI assistant that helps refine image generation prompts.
    Given a current prompt, generate 4 different short text suggestions for adjustments that could improve the image.
    These should be concise (3-6 words), actionable, and diverse in the aspects they address (style, lighting, composition, elements, etc.).

    IMPORTANT: Format your response EXACTLY as a JSON array of strings, with no additional text or explanation.

    Example response format:
    ["Add dramatic lighting", "Make it more minimalist", "Include a person", "Change to vintage style"]

    DO NOT include any other text, explanation, or formatting in your response. ONLY return the JSON array.`;

    // Create messages for OpenRouter
    const messages = [
      {
        role: 'system',
        content: systemPrompt,
      },
      {
        role: 'user',
        content: `Current prompt: "${prompt}"

        Generate 4 short suggestion chips for refining this prompt. Return ONLY a JSON array of strings with no additional text.`,
      },
    ];

    // Call OpenRouter
    console.log(`[generateSuggestionChips] Calling OpenRouter with prompt: ${prompt.substring(0, 50)}...`);
    const response = await callOpenRouter(messages, {
      model: 'openai/gpt-4.1',
      response_format: { type: 'json_object' },
    });
    console.log(`[generateSuggestionChips] OpenRouter response:`, response);

    // Parse the response
    try {
      // Handle empty or invalid response
      if (!response || response.trim() === '') {
        console.error('[generateSuggestionChips] Empty response from OpenRouter');
        return ['Add more vibrant colors', 'Make it more minimalist', 'Change lighting style', 'Add a tagline'];
      }

      // First try to parse as a direct array
      const parsedResponse = JSON.parse(response);
      if (Array.isArray(parsedResponse)) {
        console.log(`[generateSuggestionChips] Parsed ${parsedResponse.length} suggestions directly from array`);
        if (parsedResponse.length > 0) {
          return parsedResponse.slice(0, 5); // Limit to 5 suggestions
        }
      }
      // Then try to parse as an object with a suggestions property
      else if (parsedResponse.suggestions && Array.isArray(parsedResponse.suggestions)) {
        console.log(`[generateSuggestionChips] Parsed ${parsedResponse.suggestions.length} suggestions from object`);
        if (parsedResponse.suggestions.length > 0) {
          return parsedResponse.suggestions.slice(0, 5);
        }
      }
      // Finally, try to find any array property in the response
      else {
        for (const key in parsedResponse) {
          if (Array.isArray(parsedResponse[key]) && parsedResponse[key].length > 0) {
            console.log(
              `[generateSuggestionChips] Found array in property ${key} with ${parsedResponse[key].length} items`
            );
            return parsedResponse[key].slice(0, 5);
          }
        }
      }

      console.error('[generateSuggestionChips] Could not find suggestions in response:', response);
      // Return fallback suggestions if no valid suggestions found
      return ['Add more vibrant colors', 'Make it more minimalist', 'Change lighting style', 'Add a tagline'];
    } catch (error) {
      console.error('[generateSuggestionChips] Error parsing suggestion chips:', error);
      // Return fallback suggestions on error
      return ['Add more vibrant colors', 'Make it more minimalist', 'Change lighting style', 'Add a tagline'];
    }
  } catch (error) {
    console.error('[generateSuggestionChips] Error generating suggestion chips:', error);
    // Return fallback suggestions on error
    return ['Add more vibrant colors', 'Make it more minimalist', 'Change lighting style', 'Add a tagline'];
  }
}

/**
 * Update the prompt based on a user input
 */
async function updatePrompt(
  currentPrompt: string,
  question: string,
  answer: string,
  productId?: string,
  context?: any,
  clientReferenceImages?: string[]
): Promise<{ updatedPrompt: string; previewImageUrl: string; suggestionChips: string[] }> {
  try {
    // Get product information if productId is provided
    let brandSystemPrompt = '';

    // Start with client-provided reference images if available
    const refImagesToUse: string[] = clientReferenceImages || [];
    console.log(`[updatePrompt] Using ${refImagesToUse.length} client-provided reference images`);

    // Convert string[] to Array<{ imageUrl?: string }> for callOpenRouter
    let referenceImages: Array<{ imageUrl?: string }> = refImagesToUse.map((url) => ({ imageUrl: url }));

    if (productId) {
      try {
        console.log(`[ConceptCardActions] Getting product details for ID: ${productId}`);
        const product = await getProductDetailsFromModelId(productId, context);

        if (product) {
          console.log(`[ConceptCardActions] Found product: ${product.name}`);
          brandSystemPrompt = createSystemPrompt(product);
          console.log(`[ConceptCardActions] Created brand system prompt with length: ${brandSystemPrompt.length}`);

          // Get reference images for the product (using DB lookup here)
          const userId = context?.user?.id || 1;
          const modelId = product.modelId;

          if (modelId) {
            console.log(`[ConceptCardActions] Getting reference images for model ID: ${modelId}`);
            try {
              const productIdNum = product.id;
              const refImagesResult = await getReferenceImages(
                userId,
                String(modelId),
                typeof productIdNum === 'number' ? String(productIdNum) : productIdNum
              );
              console.log(`[ConceptCardActions] Reference images raw result:`, JSON.stringify(refImagesResult));
              referenceImages = Array.isArray(refImagesResult) ? refImagesResult.map((url) => ({ imageUrl: url })) : [];
              console.log(`[ConceptCardActions] Formatted reference images:`, JSON.stringify(referenceImages));
              console.log(`[ConceptCardActions] Found ${referenceImages.length} reference images for prompt update`);
            } catch (error) {
              console.error(`[ConceptCardActions] Error retrieving reference images:`, error);
              referenceImages = [];
            }
          }
        } else {
          console.log(`[ConceptCardActions] No product found for ID: ${productId}`);
        }
      } catch (error) {
        console.error(`[ConceptCardActions] Error getting product details:`, error);
      }
    }

    // Create a system prompt for OpenRouter
    const systemPrompt = `You are an AI assistant that helps refine content generation prompts.
    ${referenceImages.length > 0 ? 'IMPORTANT: Look carefully at the provided reference image(s). Analyze their visual style, composition, and key elements.' : ''}
    Given a current prompt, a question, and the user's answer${referenceImages.length > 0 ? ', along with the reference image(s),' : ''}, update the prompt to incorporate the new information.
    Return only the updated prompt text with no additional explanation.
    ${referenceImages.length > 0 ? 'Be sure to maintain the style and visual elements shown in the reference image(s).' : ''}

    ${brandSystemPrompt ? '## Brand Information\n\n' + brandSystemPrompt + '\n\nEnsure the updated prompt aligns with the brand information above.' : ''}
    ${referenceImages.length > 0 ? '\n\n## Reference Images\nPay close attention to the provided reference image(s). Use them as inspiration for style, composition, and visual elements in your updated prompt.' : ''}`;

    // Create messages for OpenRouter
    const messages = [
      {
        role: 'system',
        content: systemPrompt,
      },
      {
        role: 'user',
        content: `Current prompt: "${currentPrompt}"
        Question: "${question}"
        Answer: "${answer}"

        Please update the prompt.`,
      },
    ];

    // Call OpenRouter with reference images
    const response = await callOpenRouter(
      messages,
      {
        model: 'openai/gpt-4.1',
      },
      referenceImages
    );

    // Clean up the response (remove quotes if present)
    // Using a regex that works in ES2015
    const updatedPrompt = response.replace(/^["'](.*)["']$/, '$1').trim();

    // Generate a new preview image for the updated prompt
    console.log(`[ConceptCardActions] Generating new preview image for updated prompt`);

    // Use the original client-provided reference images for the preview
    // This ensures consistency between what the client sees and what's used for generation
    console.log(`[ConceptCardActions] Passing ${refImagesToUse.length} reference images to preview generator`);
    const previewImageUrl = await generateConceptCardPreview(updatedPrompt, refImagesToUse);
    console.log(`[ConceptCardActions] New preview image generated: ${previewImageUrl}`);

    // Generate suggestion chips for the updated prompt
    console.log(
      `[ConceptCardActions] Generating suggestion chips for updated prompt: ${updatedPrompt.substring(0, 50)}...`
    );
    try {
      let suggestionChips = await generateSuggestionChips(updatedPrompt);
      console.log(`[ConceptCardActions] Generated ${suggestionChips.length} suggestion chips:`, suggestionChips);

      // If no chips were generated, use fallback chips
      if (suggestionChips.length === 0) {
        suggestionChips = [
          'Add more vibrant colors',
          'Make it more minimalist',
          'Change lighting style',
          'Add a tagline',
        ];
        console.log(`[ConceptCardActions] Using fallback suggestion chips:`, suggestionChips);
      }

      return { updatedPrompt, previewImageUrl, suggestionChips };
    } catch (error) {
      console.error(`[ConceptCardActions] Error generating suggestion chips:`, error);

      // Use fallback chips on error
      const fallbackChips = [
        'Add more vibrant colors',
        'Make it more minimalist',
        'Change lighting style',
        'Add a tagline',
      ];
      console.log(`[ConceptCardActions] Using fallback suggestion chips after error:`, fallbackChips);

      return { updatedPrompt, previewImageUrl, suggestionChips: fallbackChips };
    }
  } catch (error) {
    console.error('[ConceptCardActions] Error updating prompt:', error);
    throw error;
  }
}

/**
 * Generate content from a concept card
 */
export const generateFromConceptCard = async (
  { cardId, userId, productId, referenceImages, aspectRatio, numImages, canvasId }: GenerateFromConceptCardParams,
  context: any
) => {
  // Get user ID from context if not provided
  const effectiveUserId = userId || context?.user?.id || 1;
  try {
    console.log(`[ConceptCardActions] Generating content from concept card ${cardId}`);

    // Get the concept card
    const card = (await prisma.conceptCard.findUnique({
      where: { id: cardId },
      // No need to include product here, we get productId from args
    })) as ConceptCard | null;

    if (!card) {
      throw new Error(`Concept card ${cardId} not found`);
    }

    // Use the productId from the card if not provided in the parameters
    const effectiveProductId = productId || card.productId;

    // Extract the aspect ratio from the concept card's stored answers
    let storedAspectRatio: '1024x1024' | '1536x1024' | '1024x1536' = '1024x1024';
    try {
      const cardAnswers = JSON.parse(card.answers || '{}');
      if (cardAnswers.aspectRatio && ['1024x1024', '1536x1024', '1024x1536'].includes(cardAnswers.aspectRatio)) {
        storedAspectRatio = cardAnswers.aspectRatio;
        console.log(`[generateFromConceptCard] Retrieved stored aspect ratio: ${storedAspectRatio}`);
      } else {
        console.log(
          `[generateFromConceptCard] No valid aspect ratio found in card answers, using default: ${storedAspectRatio}`
        );
      }
    } catch (error) {
      console.log(
        `[generateFromConceptCard] Error parsing card answers for aspect ratio, using default: ${storedAspectRatio}`
      );
    }

    // Use the stored aspect ratio from the card, falling back to parameter or default
    const effectiveAspectRatio = storedAspectRatio || aspectRatio || '1024x1024';
    console.log(`[generateFromConceptCard] Using effective aspect ratio: ${effectiveAspectRatio}`);

    // Start with the passed-in reference images, but filter out base64 data for database storage
    let allReferenceImagesForThisCard: string[] = referenceImages || [];
    console.log(
      `[generateFromConceptCard] Starting with ${allReferenceImagesForThisCard.length} reference images passed from client.`
    );

    // Separate URLs from base64 data for database storage
    const urlOnlyReferences = allReferenceImagesForThisCard.filter(
      (img) => typeof img === 'string' && !img.startsWith('data:')
    );
    console.log(
      `[generateFromConceptCard] Filtered to ${urlOnlyReferences.length} URL-only references for database storage.`
    );

    // If we have a productId, try to get the product reference image
    if (effectiveProductId) {
      try {
        // Get the product details using the same method as in createConceptCard
        console.log(`[generateFromConceptCard] Getting product details for ID: ${effectiveProductId}`);
        const product = await getProductDetailsFromModelId(String(effectiveProductId), context);

        if (product) {
          console.log(`[generateFromConceptCard] Found product: ${product.name}`);

          // If the product has a reference_file_id, use it
          if (product.reference_file_id) {
            console.log(`[generateFromConceptCard] Found product reference image: ${product.reference_file_id}`);

            // Check if the reference image is already in the arrays
            if (!allReferenceImagesForThisCard.includes(product.reference_file_id)) {
              // Add the product reference image to the beginning of both arrays
              allReferenceImagesForThisCard = [product.reference_file_id, ...allReferenceImagesForThisCard];
              urlOnlyReferences.unshift(product.reference_file_id);
              console.log(`[generateFromConceptCard] Added product reference image to reference images`);
            }
          } else {
            console.log(`[generateFromConceptCard] No reference_file_id found for product ${effectiveProductId}`);
          }
        } else {
          console.log(`[generateFromConceptCard] No product found for ID: ${effectiveProductId}`);

          // Look up the PhotographyModel first, then get the associated product
          try {
            // First, find the photography model using the model ID
            const photographyModel = await prisma.photographyModel.findUnique({
              where: { id: String(effectiveProductId) },
              include: { product: { select: { reference_file_id: true } } },
            });

            if (photographyModel?.product?.reference_file_id) {
              console.log(
                `[generateFromConceptCard] Found product reference image from DB: ${photographyModel.product.reference_file_id}`
              );

              // Check if the reference image is already in the arrays
              if (!allReferenceImagesForThisCard.includes(photographyModel.product.reference_file_id)) {
                // Add the product reference image to the beginning of both arrays
                allReferenceImagesForThisCard = [
                  photographyModel.product.reference_file_id,
                  ...allReferenceImagesForThisCard,
                ];
                urlOnlyReferences.unshift(photographyModel.product.reference_file_id);
                console.log(`[generateFromConceptCard] Added product reference image from DB to reference images`);
              }
            }
          } catch (dbError) {
            console.error(`[generateFromConceptCard] Error fetching product from DB:`, dbError);
          }
        }
      } catch (error) {
        console.error(`[generateFromConceptCard] Error getting product details:`, error);
      }
    }

    console.log(
      `[generateFromConceptCard] Using ${allReferenceImagesForThisCard.length} reference images for task creation.`
    );

    // Update the card status to generating
    await prisma.conceptCard.update({
      where: { id: cardId },
      data: {
        status: 'generating',
        updatedAt: new Date(),
      },
    });

    // Determine the content type based on the prompt
    const requestType = 'generate_image';

    // The productId is actually the PhotographyModel ID, so we can use it directly
    let photographyModelId: string | undefined = undefined;
    if (effectiveProductId) {
      try {
        // Look up the photography model directly
        const photographyModel = await prisma.photographyModel.findUnique({
          where: { id: String(effectiveProductId) },
          select: { modelId: true },
        });

        if (photographyModel?.modelId) {
          photographyModelId = photographyModel.modelId;
        }
        console.log(
          `[generateFromConceptCard] Using photography model ID: ${effectiveProductId}, found modelId: ${photographyModelId}`
        );
      } catch (e) {
        console.error(
          `[generateFromConceptCard] Failed to lookup modelId for photography model ${effectiveProductId}:`,
          e
        );
      }
    }

    // Create a placeholder ID for the first task (or single task)
    const basePlaceholderId = `placeholder-${uuid()}`;

    // 🛡️ BULLETPROOF CANVAS ID DETERMINATION
    let effectiveCanvasId = canvasId;

    // If canvasId is missing, look it up from the concept card database record
    if (!effectiveCanvasId) {
      console.log(`[generateFromConceptCard] ⚠️ canvasId is missing! Looking up from concept card database...`);
      try {
        const conceptCard = await prisma.conceptCard.findUnique({
          where: { id: cardId },
          select: { canvasId: true },
        });

        if (conceptCard?.canvasId) {
          effectiveCanvasId = conceptCard.canvasId;
          console.log(`[generateFromConceptCard] ✅ Found canvasId from database: ${effectiveCanvasId}`);
        } else {
          console.log(`[generateFromConceptCard] ❌ No canvasId found in database for concept card ${cardId}`);
        }
      } catch (error) {
        console.error(`[generateFromConceptCard] Error looking up canvasId from database:`, error);
      }
    }

    // FAIL FAST: Require effectiveCanvasId - no fallbacks to prevent chaos
    if (!effectiveCanvasId) {
      const error = `[generateFromConceptCard] ❌ FAIL FAST: canvasId is required. No fallbacks allowed.`;
      console.error(error);
      throw new Error(error);
    }

    const roomName = getCanvasRoomId(effectiveCanvasId);
    console.log(`[generateFromConceptCard] 🏠 BULLETPROOF ROOM ID DETERMINATION:`);
    console.log(`[generateFromConceptCard] - Original Canvas ID: ${canvasId}`);
    console.log(`[generateFromConceptCard] - Effective Canvas ID: ${effectiveCanvasId}`);
    console.log(`[generateFromConceptCard] - User ID: ${effectiveUserId}`);
    console.log(`[generateFromConceptCard] - Final Room Name: ${roomName}`);

    // 🚨 VALIDATION: Ensure we never broadcast to wrong room silently
    if (!effectiveCanvasId && roomName.startsWith('user-')) {
      console.warn(
        `[generateFromConceptCard] ⚠️ WARNING: Broadcasting to user room ${roomName} instead of canvas room. This may cause partial images to not appear on canvas.`
      );
    }

    if (numImages && numImages > 1) {
      console.log(`[generateFromConceptCard] Creating ${numImages} separate tasks for multiple image generation`);

      // Create multiple tasks, each generating 1 image
      type CreatedTaskInfo = { task: any; placeholderId: string; imageIndex: number };
      const taskPromises: Promise<CreatedTaskInfo>[] = [];
      for (let i = 0; i < numImages; i++) {
        const taskPlaceholderId = i === 0 ? basePlaceholderId : `placeholder-${uuid()}`;

        const taskPromise: Promise<CreatedTaskInfo> = prisma.agentTask
          .create({
            data: {
              userId: effectiveUserId,
              requestType,
              request: card.prompt,
              status: 'pending',
              placeholderId: taskPlaceholderId,
              result: {
                conceptCardId: cardId,
                roomName: roomName,
                modelId: photographyModelId,
                productId: productId,
                referenceImages: urlOnlyReferences,
                size: effectiveAspectRatio,
                numImages: 1, // Each task generates 1 image
              },
            },
          })
          .then((task) => ({
            task,
            placeholderId: taskPlaceholderId,
            imageIndex: i + 1,
          }));

        taskPromises.push(taskPromise);
      }

      // Wait for all tasks to be created
      const createdTasks: CreatedTaskInfo[] = await Promise.all(taskPromises);

      // Emit canvas events for all tasks
      for (const { task, placeholderId, imageIndex } of createdTasks) {
        console.log(
          `[generateFromConceptCard] Emitting canvasElementAdd for task ${task.id} (image ${imageIndex}/${numImages})`
        );

        await emitCanvasEvent(
          effectiveUserId,
          'task_created',
          {
            elementId: placeholderId,
            elementType: 'generate_image',
            position: { x: 100 + (imageIndex - 1) * 50, y: 100 + (imageIndex - 1) * 50 }, // Offset each image slightly
            size: { width: 400, height: 300 },
            content: {
              type: 'task_created',
              taskId: task.id,
              placeholderId: placeholderId,
              requestType: task.requestType,
              status: task.status,
              timestamp: new Date().toISOString(),
            },
            taskId: task.id,
          },
          effectiveCanvasId
        );
      }

      // Update the concept card with the first task ID for tracking
      await prisma.conceptCard.update({
        where: { id: cardId },
        data: {
          taskId: createdTasks[0].task.id,
          updatedAt: new Date(),
        },
      });

      // Emit an event to update the card status on the canvas
      await emitCanvasEvent(
        effectiveUserId,
        'updateConceptCard',
        {
          id: cardId,
          status: 'generating',
        },
        effectiveCanvasId
      );

      // Start processing all tasks asynchronously
      for (const { task } of createdTasks) {
        processTaskWithReferenceImages(task.id, allReferenceImagesForThisCard).catch((error) => {
          console.error(`[ConceptCardActions] Error processing task ${task.id}:`, error);
        });
      }

      console.log(`[generateFromConceptCard] Created ${createdTasks.length} tasks for multiple image generation`);

      // Return the first task ID for compatibility (though multiple tasks are created)
      return createdTasks[0].task.id;
    } else {
      // Single image generation (existing logic)
      const placeholderId = basePlaceholderId;

      // Create a task record in the database
      const task = await prisma.agentTask.create({
        data: {
          userId: effectiveUserId,
          requestType,
          request: card.prompt,
          status: 'pending',
          placeholderId,
          result: {
            conceptCardId: cardId,
            roomName: roomName,
            modelId: photographyModelId,
            productId: productId,
            referenceImages: urlOnlyReferences,
            size: effectiveAspectRatio,
            numImages: 1, // Single image
          },
        },
      });

      // Update the concept card with the task ID
      await prisma.conceptCard.update({
        where: { id: cardId },
        data: {
          taskId: task.id,
          updatedAt: new Date(),
        },
      });

      // Emit an event to update the card status on the canvas
      await emitCanvasEvent(
        effectiveUserId,
        'updateConceptCard',
        {
          id: cardId,
          status: 'generating',
        },
        effectiveCanvasId
      );

      // Start processing the task asynchronously with full reference images
      processTaskWithReferenceImages(task.id, allReferenceImagesForThisCard).catch((error) => {
        console.error(`[ConceptCardActions] Error processing task ${task.id}:`, error);
      });

      return task.id;
    }

    // This should not be reached
    throw new Error('Unexpected code path in generateFromConceptCard');
  } catch (error) {
    console.error(`[ConceptCardActions] Error in generateFromConceptCard:`, error);
    throw error;
  }
};
