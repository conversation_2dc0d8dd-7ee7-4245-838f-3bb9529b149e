/**
 * 🛍️ Product Repository Implementation
 *
 * @description Database implementation of the product repository
 * @responsibility Handle all product data access operations
 * @dependencies Prisma, domain interfaces
 * @ai_context Repository implementation for product data persistence
 */

import { Prisma } from '@prisma/client';
import { Product } from '../../domain/entities/product.entity';
import {
  IProductRepository,
  ProductNotFoundError,
  ProductValidationError,
  ProductDuplicateError,
  ProductPermissionError,
} from '../../domain/repositories/product.repository';
import {
  CreateProductInput,
  UpdateProductInput,
  ProductListOptions,
  ProductSearchResult,
  ProductStatistics,
  ProductFilters,
} from '../../domain/types';

export class ProductRepositoryImpl implements IProductRepository {
  constructor(private prisma: any) { }

  async create(input: CreateProductInput, userId: number): Promise<Product> {
    try {
      const productData = {
        ...input,
        userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const product = await this.prisma.product.create({
        data: {
          ...productData,
          user: {
            connect: { id: userId },
          },
          organization: {
            connect: { id: input.organizationId },
          },
        },
      });

      return product;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ProductDuplicateError('sku', input.sku || '');
        }
      }
      throw error;
    }
  }

  async findById(id: number, userId?: number): Promise<Product | null> {
    const where: Prisma.ProductWhereInput = { id };
    if (userId) {
      where.userId = userId.toString();
    }

    return await this.prisma.product.findFirst({ where });
  }

  async findMany(options: ProductListOptions, userId?: number): Promise<Product[]> {
    const where: Prisma.ProductWhereInput = {};

    if (userId) {
      where.userId = userId.toString();
    }

    if (options.filters) {
      if (options.filters.organizationId) {
        where.organizationId = options.filters.organizationId;
      }
      if (options.filters.productType) {
        where.productType = options.filters.productType;
      }
      if (options.filters.status) {
        where.analysisStatus = options.filters.status;
      }
      if (options.filters.analysisStatus) {
        where.analysisStatus = options.filters.analysisStatus;
      }
      if (options.filters.searchQuery) {
        where.OR = [
          { name: { contains: options.filters.searchQuery, mode: 'insensitive' } },
          { description: { contains: options.filters.searchQuery, mode: 'insensitive' } },
          { brandName: { contains: options.filters.searchQuery, mode: 'insensitive' } },
        ];
      }
      if (options.filters.priceRange) {
        where.price = {
          gte: options.filters.priceRange.min,
          lte: options.filters.priceRange.max,
        };
      }
    }

    let orderBy: Prisma.ProductOrderByWithRelationInput = {};
    if (options.sort) {
      orderBy = { [options.sort.field]: options.sort.order };
    } else {
      orderBy = { id: 'desc' };
    }

    const skip = options.pagination ? (options.pagination.page - 1) * options.pagination.limit : undefined;
    const take = options.pagination?.limit;

    return await this.prisma.product.findMany({
      where,
      orderBy,
      skip,
      take,
    });
  }

  async search(options: ProductListOptions, userId?: number): Promise<ProductSearchResult> {
    const products = await this.findMany(options, userId);
    const totalCount = await this.count(options.filters || {}, userId);

    // Build facets
    const facets = {
      categories: await this.getFacetCounts('productType', options.filters?.organizationId, userId),
      brands: await this.getFacetCounts('brandName', options.filters?.organizationId, userId),
      priceRanges: await this.getPriceRangeFacets(options.filters?.organizationId, userId),
      availability: (await this.getFacetCounts('availability', options.filters?.organizationId, userId)).map((f) => ({
        status: f.name,
        count: f.count,
      })),
    };

    return {
      products,
      totalCount,
      facets,
    };
  }

  async update(id: number, input: UpdateProductInput, userId: number): Promise<Product> {
    const existingProduct = await this.findById(id, userId);
    if (!existingProduct) {
      throw new ProductNotFoundError(id);
    }

    try {
      const product = await this.prisma.product.update({
        where: { id },
        data: {
          ...input,
          updatedAt: new Date(),
        },
      });

      return product;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ProductDuplicateError('sku', input.sku || '');
        }
      }
      throw error;
    }
  }

  async delete(id: number, userId: number): Promise<boolean> {
    const existingProduct = await this.findById(id, userId);
    if (!existingProduct) {
      throw new ProductNotFoundError(id);
    }

    await this.prisma.product.delete({
      where: { id },
    });

    return true;
  }

  async count(filters: ProductFilters, userId?: number): Promise<number> {
    const where: Prisma.ProductWhereInput = {};

    if (userId) {
      where.userId = userId.toString();
    }

    if (filters.organizationId) {
      where.organizationId = filters.organizationId;
    }
    if (filters.productType) {
      where.productType = filters.productType;
    }
    if (filters.status) {
      where.analysisStatus = filters.status;
    }
    if (filters.analysisStatus) {
      where.analysisStatus = filters.analysisStatus;
    }

    return await this.prisma.product.count({ where });
  }

  async getStatistics(organizationId: string, userId?: number): Promise<ProductStatistics> {
    const where: Prisma.ProductWhereInput = { organizationId };
    if (userId) {
      where.userId = userId.toString();
    }

    const [totalProducts, activeProducts, pendingAnalysis, completedAnalysis, avgPrice, topCategories, recentlyAdded] =
      await Promise.all([
        this.prisma.product.count({ where }),
        this.prisma.product.count({ where: { ...where, analysisStatus: 'COMPLETED' } }),
        this.prisma.product.count({ where: { ...where, analysisStatus: 'PENDING' } }),
        this.prisma.product.count({ where: { ...where, analysisStatus: 'COMPLETED' } }),
        this.prisma.product.aggregate({ where, _avg: { price: true } }),
        this.getTopCategories(organizationId, userId),
        this.prisma.product.count({
          where: {
            ...where,
            id: { gte: 0 }, // Use a simple condition instead of createdAt
          },
        }),
      ]);

    return {
      totalProducts,
      activeProducts,
      pendingAnalysis,
      completedAnalysis,
      averagePrice: avgPrice._avg.price || 0,
      topCategories,
      recentlyAdded,
    };
  }

  async findByOrganization(organizationId: string, options?: ProductListOptions): Promise<Product[]> {
    const filters = { ...options?.filters, organizationId };
    return await this.findMany({ ...options, filters });
  }

  async findBySku(sku: string, organizationId: string): Promise<Product | null> {
    return await this.prisma.product.findFirst({
      where: { sku, organizationId },
    });
  }

  async findByType(productType: string, organizationId: string, options?: ProductListOptions): Promise<Product[]> {
    const filters = { ...options?.filters, organizationId, productType };
    return await this.findMany({ ...options, filters });
  }

  async findPendingAnalysis(organizationId: string): Promise<Product[]> {
    return await this.prisma.product.findMany({
      where: {
        organizationId,
        analysisStatus: 'PENDING',
      },
      orderBy: { createdAt: 'asc' },
    });
  }

  async updateAnalysisStatus(id: number, status: string, completedAt?: Date): Promise<Product> {
    return await this.prisma.product.update({
      where: { id },
      data: {
        analysisStatus: status,
        ...(completedAt && { analysisCompletedAt: completedAt }),
        updatedAt: new Date(),
      },
    });
  }

  async addSimilarImages(id: number, images: any): Promise<Product> {
    return await this.prisma.product.update({
      where: { id },
      data: {
        similarImages: images,
        updatedAt: new Date(),
      },
    });
  }

  async updateImages(id: number, images: string[]): Promise<Product> {
    return await this.prisma.product.update({
      where: { id },
      data: {
        images,
        updatedAt: new Date(),
      },
    });
  }

  async findForModelTraining(organizationId: string): Promise<Product[]> {
    return await this.prisma.product.findMany({
      where: {
        organizationId,
        images: {
          not: { equals: [] },
        },
      },
      orderBy: { id: 'desc' },
    });
  }

  async bulkCreate(products: CreateProductInput[], userId: number): Promise<Product[]> {
    const createdProducts: Product[] = [];

    for (const productInput of products) {
      const product = await this.create(productInput, userId);
      createdProducts.push(product);
    }

    return createdProducts;
  }

  async bulkUpdate(updates: Array<{ id: number; data: UpdateProductInput }>, userId: number): Promise<Product[]> {
    const updatedProducts: Product[] = [];

    for (const update of updates) {
      const product = await this.update(update.id, update.data, userId);
      updatedProducts.push(product);
    }

    return updatedProducts;
  }

  async bulkDelete(ids: number[], userId: number): Promise<boolean> {
    for (const id of ids) {
      await this.delete(id, userId);
    }
    return true;
  }

  async exists(id: number, userId?: number): Promise<boolean> {
    const product = await this.findById(id, userId);
    return !!product;
  }

  async generateSku(name: string, userId: number): Promise<string> {
    const baseSkus = name
      .toUpperCase()
      .replace(/[^A-Z0-9]/g, '')
      .substring(0, 6);
    let counter = 1;
    let sku = `${baseSkus}-${counter.toString().padStart(3, '0')}`;

    while (await this.prisma.product.findFirst({ where: { sku } })) {
      counter++;
      sku = `${baseSkus}-${counter.toString().padStart(3, '0')}`;
    }

    return sku;
  }

  async findDuplicates(
    organizationId: string,
    criteria: {
      name?: string;
      sku?: string;
      originalUrl?: string;
    }
  ): Promise<Product[]> {
    const where: Prisma.ProductWhereInput = { organizationId };

    if (criteria.name || criteria.sku || criteria.originalUrl) {
      where.OR = [];
      if (criteria.name) where.OR.push({ name: criteria.name });
      if (criteria.sku) where.OR.push({ sku: criteria.sku });
      if (criteria.originalUrl) where.OR.push({ originalUrl: criteria.originalUrl });
    }

    return await this.prisma.product.findMany({ where });
  }

  async archive(ids: number[], userId: number): Promise<boolean> {
    await this.prisma.product.updateMany({
      where: { id: { in: ids }, userId: userId.toString() },
      data: { status: 'ARCHIVED', updatedAt: new Date() },
    });
    return true;
  }

  async restore(ids: number[], userId: number): Promise<boolean> {
    await this.prisma.product.updateMany({
      where: { id: { in: ids }, userId: userId.toString() },
      data: { status: 'ACTIVE', updatedAt: new Date() },
    });
    return true;
  }

  async getRecentlyViewed(userId: number, limit: number = 10): Promise<Product[]> {
    // This would require a separate tracking table in a real implementation
    return await this.prisma.product.findMany({
      where: { userId: userId.toString() },
      orderBy: { updatedAt: 'desc' },
      take: limit,
    });
  }

  async trackView(productId: number, userId: number): Promise<void> {
    // This would update a view tracking table in a real implementation
    // For now, just update the product's updatedAt timestamp
    await this.prisma.product.update({
      where: { id: productId },
      data: { updatedAt: new Date() },
    });
  }

  async getPopular(organizationId: string, limit: number = 10): Promise<Product[]> {
    // This would use view counts in a real implementation
    return await this.prisma.product.findMany({
      where: { organizationId },
      orderBy: { createdAt: 'desc' },
      take: limit,
    });
  }

  async findRelated(productId: number, limit: number = 5): Promise<Product[]> {
    const product = await this.findById(productId);
    if (!product) return [];

    return await this.prisma.product.findMany({
      where: {
        organizationId: product.organizationId,
        productType: product.productType,
        id: { not: productId },
      },
      take: limit,
    });
  }

  async updateMetadata(id: number, metadata: Record<string, any>): Promise<Product> {
    return await this.prisma.product.update({
      where: { id },
      data: {
        metadata,
        updatedAt: new Date(),
      },
    });
  }

  async clearMetadata(id: number, keys?: string[]): Promise<Product> {
    const product = await this.findById(id);
    if (!product) throw new ProductNotFoundError(id);

    let newMetadata = product.metadata || {};

    if (keys) {
      keys.forEach((key) => delete newMetadata[key]);
    } else {
      newMetadata = {};
    }

    return await this.updateMetadata(id, newMetadata);
  }

  async findByMetadata(organizationId: string, metadata: Record<string, any>): Promise<Product[]> {
    // This would require JSON queries in a real implementation
    return await this.prisma.product.findMany({
      where: { organizationId },
    });
  }

  // Private helper methods
  private async getFacetCounts(
    field: string,
    organizationId?: string,
    userId?: number
  ): Promise<Array<{ name: string; count: number }>> {
    const where: any = {};
    if (organizationId) where.organizationId = organizationId;
    if (userId) where.userId = userId.toString();

    const results = await this.prisma.product.groupBy({
      by: [field],
      where,
      _count: true,
    });

    return results.map((result: any) => ({
      name: result[field] || 'Unknown',
      count: result._count,
    }));
  }

  private async getPriceRangeFacets(
    organizationId?: string,
    userId?: number
  ): Promise<Array<{ range: string; count: number }>> {
    // This would implement price range faceting in a real implementation
    return [
      { range: '$0-$50', count: 0 },
      { range: '$50-$100', count: 0 },
      { range: '$100+', count: 0 },
    ];
  }

  private async getTopCategories(
    organizationId: string,
    userId?: number
  ): Promise<Array<{ category: string; count: number }>> {
    const facets = await this.getFacetCounts('productType', organizationId, userId);
    return facets.map((f) => ({ category: f.name, count: f.count }));
  }
}
