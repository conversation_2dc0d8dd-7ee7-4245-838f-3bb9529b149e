import { type GetImportJobs, type GetImportJob } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';

export const getImportJobs: GetImportJobs = async (_args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'Not authenticated');
  }

  return context.entities.ImportJob.findMany({
    where: { userId: context.user.id },
    orderBy: { createdAt: 'desc' },
  });
};

export const getImportJob: GetImportJob<{ id: string }> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'Not authenticated');
  }

  const importJob = await context.entities.ImportJob.findFirst({
    where: {
      id: String(args.id),
      userId: context.user.id,
    },
    select: {
      id: true,
      status: true,
      selectedProductCount: true,
      importedProductCount: true,
      errors: true,
      analysisInProgress: true,
      currentlyAnalyzing: true,
      analysisQueue: true,
      createdAt: true,
      updatedAt: true,
      completedAt: true,
      selectedProducts: true,
      importedProducts: true,
      brandKitStatus: true,
      brandKitError: true,
      brandKitStartedAt: true,
      brandKitCompletedAt: true,
    },
  });

  if (!importJob) {
    throw new HttpError(404, 'Import job not found');
  }

  // Calculate progress percentage
  const progress =
    importJob.selectedProductCount > 0
      ? Math.round((importJob.importedProductCount / importJob.selectedProductCount) * 100)
      : 0;

  // Get current stage details
  const currentStage = importJob.analysisInProgress
    ? {
        status: 'IMPORTING',
        currentlyAnalyzing: importJob.currentlyAnalyzing,
        remainingInQueue: importJob.analysisQueue.length,
      }
    : importJob.status === 'COMPLETED'
      ? { status: 'COMPLETED' }
      : { status: importJob.status };

  // Format any errors
  const formattedErrors = ((importJob.errors as any[]) || []).map((error) => ({
    message: error.message,
    timestamp: error.timestamp || new Date(),
  }));

  return {
    ...importJob,
    progress,
    currentStage,
    errors: formattedErrors,
    isComplete: importJob.status === 'COMPLETED',
    hasFailed: importJob.status === 'FAILED' || formattedErrors.length > 0,
  };
};
