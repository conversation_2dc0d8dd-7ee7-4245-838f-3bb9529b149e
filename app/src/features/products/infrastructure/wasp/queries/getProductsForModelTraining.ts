import { HttpError } from 'wasp/server';
import { type Product } from 'wasp/entities';
import { type GetProductsForModelTraining } from 'wasp/server/operations';

export const getProductsForModelTraining: GetProductsForModelTraining<void, Product[]> = async (_args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  return context.entities.Product.findMany({
    where: {
      userId: context.user.id,
      images: {
        has: '_', // This ensures the array has at least one element
      },
    },
    orderBy: {
      id: 'desc',
    },
  });
};
