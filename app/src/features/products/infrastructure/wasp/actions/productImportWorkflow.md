# Olivia Product Import Workflow: Background Processing Strategy

## 🎯 Problem Statement
- Product scraping takes significant time (87-93 seconds)
- Users need flexibility to start, pause, and resume imports
- Maintain a smooth, non-blocking user experience

## 🏗️ Proposed Architecture

### 1. Import Job Model
```typescript
model ImportJob {
  id            String      @id @default(cuid())
  userId        String
  websiteUrl    String
  status        ImportStatus
  progress      Float        @default(0)
  totalProducts Int          @default(0)
  importedProducts Int       @default(0)
  products      Json[]
  startedAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  completedAt   DateTime?
}

enum ImportStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
  PAUSED
}
```

### 2. Workflow Stages

#### Stage 1: Initial Discovery (87 seconds)
- User submits website URL
- System creates ImportJob record
- Perform initial product page discovery
- Return list of potential products
- User selects products to import

#### Stage 2: Background Import
- Mark selected products in ImportJob
- Trigger asynchronous import process
- Update job status and progress
- Allow user to navigate away

### 3. Client-Side State Management
```typescript
interface ImportState {
  jobId: string;
  websiteUrl: string;
  status: ImportStatus;
  progress: number;
  products: DetectedProduct[];
  selectedProducts: DetectedProduct[];
}
```

### 4. Key Actions

#### Create Import Job
```typescript
async function createImportJob(url: string, userId: string) {
  const job = await prisma.importJob.create({
    data: {
      userId,
      websiteUrl: url,
      status: 'PENDING'
    }
  });
  return job;
}
```

#### Update Job Progress
```typescript
async function updateImportJobProgress(jobId: string, progress: number, importedProducts: number) {
  await prisma.importJob.update({
    where: { id: jobId },
    data: {
      progress,
      importedProducts,
      status: progress === 100 ? 'COMPLETED' : 'IN_PROGRESS'
    }
  });
}
```

### 5. UI/UX Considerations

#### Import Modal Enhancements
- Show overall import progress
- Display currently processing product
- Provide option to pause/resume
- Background job indicator

#### Product Grid
- Confidence score visualization
- Selectable with checkbox
- Progress overlay for each product

### 6. Resumability Features
- Store partial import state
- Allow continuing interrupted imports
- Prevent duplicate imports

## 🚀 Implementation Strategy

### Frontend
- Create persistent import state
- Use WebSocket or polling for updates
- Implement resumable import UI

### Backend
- Implement job queue system
- Use worker threads for processing
- Robust error handling
- Detailed logging

## 🔮 Advanced Features
- Email notifications for long imports
- Import history tracking
- Retry mechanisms for failed imports

## 💡 User Experience Flow
1. Enter website URL
2. Wait for initial product discovery
3. Select products to import
4. Start background import
5. Navigate away or continue working
6. Check import status later
7. Review imported products

**Powered by Olivia AI - Intelligent, Flexible Product Importing**
