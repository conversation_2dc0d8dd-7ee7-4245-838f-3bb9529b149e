import { type FindSimilarImages } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import axios from 'axios';

type SimilarImage = {
  url: string;
  title: string;
  source: string;
  thumbnail?: string;
};

type FindSimilarImagesInput = {
  productName: string;
  existingImages?: string[];
  forceRefresh?: boolean;
};

export const findSimilarImages: FindSimilarImages<FindSimilarImagesInput, { images: SimilarImage[] }> = async (
  args,
  context
) => {
  if (!context.user) {
    throw new HttpError(401, 'Not authenticated');
  }

  try {
    // Get product to get its brand name
    const product = await context.entities.Product.findFirst({
      where: {
        name: args.productName,
        userId: context.user.id,
      },
    });

    if (!product) {
      throw new HttpError(404, 'Product not found');
    }

    // Check cache unless force refresh requested
    if (!args.forceRefresh && product.similarImages) {
      const cache = JSON.parse(product.similarImages as string);
      if (cache.images?.length > 0) {
        // Filter out any newly added product images
        const existingUrls = new Set(args.existingImages || []);
        const filteredImages = cache.images.filter((img: SimilarImage) => !existingUrls.has(img.url));

        // If we still have enough images, return from cache
        if (filteredImages.length >= 5) {
          return { images: filteredImages };
        }
      }
    }

    // Construct search query using both product name and brand name if available
    const searchQuery = product.brandName ? `${product.brandName} ${args.productName}` : args.productName;

    const response = await axios.get('https://www.googleapis.com/customsearch/v1', {
      params: {
        key: process.env.GOOGLE_API_KEY,
        cx: process.env.GOOGLE_SEARCH_ENGINE_ID,
        q: searchQuery,
        searchType: 'image',
        num: 10,
        imgSize: 'xlarge', // Only large or bigger images
        imgType: 'photo', // Only photos, no clipart
        safe: 'active',
      },
    });

    if (!response.data.items) {
      return { images: [] };
    }

    // Filter out any existing images by URL
    const existingUrls = new Set(args.existingImages || []);
    const images = response.data.items
      .filter((item: any) => !existingUrls.has(item.link))
      .map((item: any) => ({
        url: item.link,
        title: item.title || '',
        source: item.displayLink || '',
        thumbnail: item.image?.thumbnailLink,
      }));

    // Update cache
    await context.entities.Product.update({
      where: { id: product.id },
      data: {
        similarImages: JSON.stringify({
          images,
          lastUpdated: new Date().toISOString(),
        }),
      },
    });

    return { images };
  } catch (error) {
    console.error('Failed to fetch similar images:', error);
    throw new HttpError(500, 'Failed to fetch similar images');
  }
};
