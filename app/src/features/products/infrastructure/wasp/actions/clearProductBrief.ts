import { HttpError } from 'wasp/server';

type ClearBriefInput = {
  productId: number;
};

type ProductBriefReviews = {
  brief?: any;
  briefGeneratedAt?: string;
  [key: string]: any;
};

export const clearProductBrief = async (args: ClearBriefInput, context: any) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  try {
    // Get the product to ensure it belongs to the user
    const product = await context.entities.Product.findFirst({
      where: {
        id: args.productId,
        userId: context.user.id,
      },
    });

    if (!product) {
      throw new HttpError(404, 'Product not found');
    }

    // Get the current reviews JSON
    let reviewsData: ProductBriefReviews = {};
    if (product.reviews) {
      try {
        reviewsData = JSON.parse(product.reviews as string);
        // Remove the brief property
        if (reviewsData.brief) {
          delete reviewsData.brief;
        }
        if (reviewsData.briefGeneratedAt) {
          delete reviewsData.briefGeneratedAt;
        }
      } catch (error) {
        // If there's an error parsing JSON, just use an empty object
        reviewsData = {};
      }
    }

    // Update the product with the modified reviews data
    await context.entities.Product.update({
      where: { id: args.productId },
      data: {
        reviews: JSON.stringify(reviewsData),
      },
    });
  } catch (error) {
    console.error('Failed to clear product brief:', error);
    throw new HttpError(
      500,
      `Failed to clear product brief: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};
