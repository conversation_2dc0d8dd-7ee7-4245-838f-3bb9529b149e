/**
 * Converts a model instance to a JSON string example
 */
export function modelToJson(modelInstance: any): string {
  return JSON.stringify(modelInstance, null, 2);
}

/**
 * Product list model example
 */
export const ProductListExample = {
  products: [
    {
      name: 'Example Product',
      imageUrl: 'https://example.com/image.jpg',
      productUrl: 'https://example.com/product',
    },
  ],
};

/**
 * Product details model example
 */
export const ProductDetailsExample = {
  name: 'Example Product',
  description: 'A detailed product description',
  images: [
    {
      url: 'https://example.com/image.jpg',
      alt: 'Product image description',
    },
  ],
  price: 99.99,
  features: ['Feature 1', 'Feature 2'],
  type: 'Electronics',
  variants: [
    {
      name: 'Basic',
      price: 99.99,
    },
  ],
};

/**
 * Creates an optimized prompt with JSON example
 */
export function createOptimizedPrompt(basePrompt: string, jsonExample: any): string {
  const exampleJson = modelToJson(jsonExample);

  return `${basePrompt}

Please provide your response in this exact JSON format:
${exampleJson}

IMPORTANT:
1. Return ONLY the JSON object
2. Do not include any markdown formatting (no \`\`\`json)
3. Do not include any explanatory text
4. The response must be valid JSON that can be parsed directly
5. All fields must match the example structure exactly
6. Use the exact same field names as shown
7. Maintain proper JSON syntax with quotes around strings

Example of CORRECT response format:
${exampleJson}

Example of INCORRECT response format:
\`\`\`json
${exampleJson}
\`\`\`

Remember: Return ONLY the JSON object, nothing else!`;
}
