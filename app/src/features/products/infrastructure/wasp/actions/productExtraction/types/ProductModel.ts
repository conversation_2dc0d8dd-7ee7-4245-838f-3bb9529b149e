import { z } from 'zod';

// Base product model that defines the expected structure
export const ProductModel = z.object({
  name: z.string(),
  imageUrl: z.string().url(),
  productUrl: z.string().url(),
});

export const ProductListModel = z.object({
  products: z.array(ProductModel),
});

export const ProductDetailsModel = z.object({
  name: z.string(),
  description: z.string(),
  images: z.array(
    z.object({
      url: z.string().url(),
      alt: z.string(),
    })
  ),
  price: z.number(),
  features: z.array(z.string()),
  type: z.enum([
    'Electronics',
    'Apparel',
    'Home & Living',
    'Beauty',
    'Health & Wellness',
    'Software',
    'Sports & Outdoors',
    'Automotive',
    'Food & Beverage',
    'Arts & Crafts',
    'Pet Supplies',
    'Service',
  ]),
  variants: z
    .array(
      z.object({
        name: z.string(),
        price: z.number(),
      })
    )
    .optional(),
});

// Example instances for prompting
export const ProductListExample = {
  products: [
    {
      name: 'Example Product',
      imageUrl: 'https://example.com/image.jpg',
      productUrl: 'https://example.com/product',
    },
  ],
};

export const ProductDetailsExample = {
  name: 'Example Product',
  description: 'A detailed product description',
  images: [
    {
      url: 'https://example.com/image.jpg',
      alt: 'Product image description',
    },
  ],
  price: 99.99,
  features: ['Feature 1', 'Feature 2'],
  type: 'Electronics',
  variants: [
    {
      name: 'Basic',
      price: 99.99,
    },
  ],
};
