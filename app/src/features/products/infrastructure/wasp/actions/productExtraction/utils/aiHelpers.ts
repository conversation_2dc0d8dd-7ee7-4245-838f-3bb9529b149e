const VALID_PRODUCT_TYPES = [
  'Electronics',
  'Apparel',
  'Home & Living',
  'Beauty',
  'Health & Wellness',
  'Software',
  'Sports & Outdoors',
  'Automotive',
  'Food & Beverage',
  'Arts & Crafts',
  'Pet Supplies',
  'Service',
] as const;

type ValidProductType = (typeof VALID_PRODUCT_TYPES)[number];

export function validateProductType(type: string): ValidProductType {
  const normalizedType = type.trim();

  // Try exact match first
  if (VALID_PRODUCT_TYPES.includes(normalizedType as ValidProductType)) {
    return normalizedType as ValidProductType;
  }

  // Try case-insensitive match
  const lowerType = normalizedType.toLowerCase();
  const match = VALID_PRODUCT_TYPES.find((validType) => validType.toLowerCase() === lowerType);
  if (match) {
    return match;
  }

  // Try fuzzy match based on keywords
  const typeKeywords = new Map([
    ['Electronics', ['tech', 'gadget', 'device', 'computer', 'phone', 'electronic']],
    ['Apparel', ['clothing', 'wear', 'fashion', 'dress', 'shirt', 'pants', 'shoes']],
    ['Home & Living', ['furniture', 'decor', 'home', 'living', 'household']],
    ['Beauty', ['makeup', 'cosmetic', 'skincare', 'beauty']],
    ['Health & Wellness', ['health', 'wellness', 'fitness', 'medical', 'supplement']],
    ['Software', ['app', 'program', 'digital', 'software', 'subscription']],
    ['Sports & Outdoors', ['sport', 'outdoor', 'athletic', 'exercise', 'fitness']],
    ['Automotive', ['car', 'vehicle', 'auto', 'motorcycle', 'automotive']],
    ['Food & Beverage', ['food', 'drink', 'beverage', 'grocery', 'snack']],
    ['Arts & Crafts', ['art', 'craft', 'creative', 'hobby', 'supplies']],
    ['Pet Supplies', ['pet', 'dog', 'cat', 'animal', 'supplies']],
    ['Service', ['service', 'subscription', 'consulting', 'maintenance']],
  ]);

  for (const [validType, keywords] of typeKeywords) {
    if (keywords.some((keyword) => lowerType.includes(keyword.toLowerCase()))) {
      return validType as ValidProductType;
    }
  }

  // Default fallback
  console.warn(`Unknown product type "${type}", defaulting to "Home & Living"`);
  return 'Home & Living';
}
