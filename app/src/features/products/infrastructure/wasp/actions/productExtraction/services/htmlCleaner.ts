import * as cheerio from 'cheerio';

export class HTMLCleaner {
  // Basic HTML preprocessing for product details pages
  static preprocessHTML(html: string): string {
    const $ = cheerio.load(html);

    // Remove script tags
    $('script, noscript').remove();

    // Remove style tags and inline styles
    $('style').remove();
    $('[style]').removeAttr('style');

    // Remove comments
    $('*')
      .contents()
      .filter((_, node) => node.type === 'comment')
      .remove();

    // Remove tracking and analytics attributes
    const trackingAttributes = [
      'data-track',
      'data-analytics',
      'data-ga',
      'data-gtm',
      'class*="track"',
      'class*="analytics"',
      'id*="track"',
      'id*="analytics"',
    ];

    trackingAttributes.forEach((attr) => {
      $(`[${attr}]`).removeAttr(attr);
    });

    // Remove inline event handlers
    const eventHandlers = [
      'onclick',
      'onmouseover',
      'onmouseout',
      'onload',
      'onerror',
      'onsubmit',
      'onchange',
      'onkeydown',
      'onkeyup',
    ];

    eventHandlers.forEach((handler) => {
      $('*').removeAttr(handler);
    });

    // Remove meta tags
    $('meta').remove();

    // Remove unnecessary link tags
    $('link[rel="stylesheet"], link[rel="preload"], link[rel="prefetch"]').remove();

    // Remove iframe and embed tags
    $('iframe, embed, object').remove();

    // Trim excessive whitespace
    return $.html().replace(/\s+/g, ' ').trim();
  }
}
