import { HttpError } from 'wasp/server';
import { PRODUCT_DETAILS_PROMPT } from '../config/prompts';
import { fetchWithRetry, resetSession } from '../utils/webScraping';
import { LLMLogger } from '../utils/llmLogger';
import { extractJson, createJsonPrompt } from '../utils/jsonExtractor';
import { ProductDetailsExample } from '../utils/modelToJson';
import { sitemapParser } from '../utils/sitemapParser';
// Define the type locally with all possible stages
type DiscoveryStage =
  | 'fetching'
  | 'cleaning'
  | 'gemini'
  | 'haiku'
  | 'complete'
  | 'error'
  | 'fetching_sitemap'
  | 'detecting_regions'
  | 'extracting_products';
import type { ExtractedProduct } from '../types/extractionTypes';
import { ai, modelName, generationConfig } from '../config/aiConfig';
import * as cheerio from 'cheerio';

interface ProductInfo {
  name: string;
  imageUrl?: string;
  productUrl: string;
  [key: string]: any; // Allow additional properties
}

interface ProductDiscoveryResult {
  products: ProductInfo[];
  region?: string; // Added to track which region's products were imported
  availableRegions?: string[]; // List of available regions if multiple found
  cleanedHTML?: string;
}

interface ProductDetails extends ExtractedProduct {
  variants?: Array<{
    name: string;
    price: number;
  }>;
}

type ProgressCallback = (stage: DiscoveryStage) => void;

export class ProductScraper {
  private cleanedHTMLCache: Map<string, string> = new Map();

  public async extractProductInfoFromPage(url: string): Promise<{ name: string; imageUrl?: string }> {
    try {
      const html = await fetchWithRetry(url);
      const $ = cheerio.load(html);

      // Common BigCommerce selectors
      const selectors = {
        name: [
          'meta[property="og:image:title"]',
          'meta[property="og:image:alt"]',
          'meta[property="og:title"]',
          '.productView-title',
          'h1.product-title',
          '[data-test-id="product-title"]',
          '[itemprop="name"]',
          'h1:contains("Product")',
          'h1',
        ],
        image: [
          'meta[property="og:image"]',
          'meta[name="twitter:image"]',
          '.productView-image img',
          '.product-main-image img',
          '[data-test-id="product-image"] img',
          '[itemprop="image"]',
          '.product-image img',
          'img.product-image',
          'img[src*="product"]',
          'img[src*="images.ctfassets.net"]',
          'img[src*="C1N1"]',
        ],
      };

      // Try each name selector until we find one that works
      let name = '';
      for (const selector of selectors.name) {
        const element = $(selector).first();
        if (element.length) {
          name = element.text().trim();
          break;
        }
      }

      // If we still don't have a name, try to extract it from the URL
      if (!name) {
        const urlParts = new URL(url).pathname.split('/');
        const lastPart = urlParts[urlParts.length - 1] || urlParts[urlParts.length - 2];
        if (lastPart) {
          name = lastPart
            .replace(/-/g, ' ')
            .replace(/\.(html?|php|aspx?)$/, '')
            .split(' ')
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
        }
      }

      // Try each image selector until we find one that works
      let imageUrl;
      for (const selector of selectors.image) {
        const element = $(selector).first();
        if (element.length) {
          // Check for content attribute (used in meta tags) first, then src or data-src
          imageUrl = element.attr('content') || element.attr('src') || element.attr('data-src');
          if (imageUrl) {
            imageUrl = this.ensureProtocol(imageUrl, url);
            break;
          }
        }
      }

      return { name, imageUrl };
    } catch (error) {
      console.error(`Error extracting info from ${url}:`, error);
      return { name: '', imageUrl: undefined };
    }
  }

  private ensureProtocol(url: string, baseUrl: string): string {
    if (url.startsWith('//')) {
      return `https:${url}`;
    }
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      try {
        return new URL(url, baseUrl).toString();
      } catch (error) {
        console.error('Failed to parse URL:', url, error);
        return `https://${url}`;
      }
    }
    return url;
  }

  private validateGeminiResponse(response: any): string {
    if (!response?.candidates?.[0]?.content?.parts?.[0]?.text) {
      throw new Error('Invalid Gemini response structure');
    }
    return response.candidates[0].content.parts[0].text;
  }

  async extractProducts(
    homeUrl: string,
    onProgress: ProgressCallback,
    region?: string
  ): Promise<ProductDiscoveryResult> {
    console.log(`[Product Scraper] Starting sitemap extraction from: ${homeUrl}`);

    // Reset session before starting new import
    resetSession();
    console.log(`[Product Scraper] Reset web scraping session`);

    try {
      // 1. First get sitemap to check for regions
      onProgress('fetching_sitemap');
      const baseUrl = new URL(homeUrl).origin;
      const mainSitemap = await fetchWithRetry(`${baseUrl}/sitemap.xml`);

      // 2. Detect and handle regions
      onProgress('detecting_regions');
      const regionInfo = await sitemapParser.detectRegions(mainSitemap);

      // Handle regional sitemaps
      let selectedRegion = region;
      // Check for regional sitemaps
      if (regionInfo) {
        const { regions, defaultRegion } = regionInfo;
        // If we have regions and no specific region was requested, use default
        if (regions.length > 0 && !selectedRegion && defaultRegion) {
          console.log(`[Product Scraper] Using default region: ${defaultRegion}`);
          selectedRegion = defaultRegion;
        }

        // If we have multiple regions, store them for UI display
        if (regions.length > 1) {
          console.log(`[Product Scraper] Multiple regions found: ${regions.map((r) => r.region).join(', ')}`);
        }
      }

      // 3. Get product sitemap for selected region
      onProgress('extracting_products');
      const sitemapUrl = await sitemapParser.findProductSitemap(baseUrl, selectedRegion);
      if (!sitemapUrl) {
        throw new Error('Could not find product sitemap');
      }
      console.log(`[Product Scraper] Found product sitemap at: ${sitemapUrl}`);

      // 4. Extract product URLs and details
      const urlList = await sitemapParser.extractProductsFromSitemap(sitemapUrl);
      if (!urlList.length) {
        throw new Error('No products found in sitemap');
      }
      console.log(`[Product Scraper] Found ${urlList.length} product URLs in sitemap`);

      // 5. Extract additional product info from each page
      const products = await Promise.all(
        urlList.map(async (item) => {
          const { name, imageUrl } = await this.extractProductInfoFromPage(item.productUrl);
          return {
            name: name || item.name,
            imageUrl: imageUrl || undefined,
            productUrl: item.productUrl,
          };
        })
      );

      // 6. Build result object
      onProgress('complete');
      // Build result object with available data
      const result: ProductDiscoveryResult = { products };

      // Add regional information if available
      if (selectedRegion) {
        result.region = selectedRegion;
      }

      // Add available regions if we have multiple
      if (regionInfo?.regions.length && regionInfo.regions.length > 1) {
        result.availableRegions = regionInfo.regions.map((r) => r.region);
      }

      // Log results
      console.log(`[Product Scraper] Successfully extracted ${products.length} products from: ${homeUrl}`);
      selectedRegion && console.log(`[Product Scraper] Products are from region: ${selectedRegion}`);
      result.availableRegions &&
        console.log(`[Product Scraper] Available regions: ${result.availableRegions.join(', ')}`);

      return result;
    } catch (error) {
      onProgress('error');
      console.error('[Product Scraper] Error extracting products:', error);
      throw new HttpError(
        500,
        `Failed to extract products: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async getProductDetails(productUrl: string, productName: string): Promise<ProductDetails> {
    console.log(`[Product Scraper] Getting details for product: ${productName}`);

    try {
      let cleanedHTML = this.cleanedHTMLCache.get(productUrl);
      if (!cleanedHTML) {
        const rawHTML = await fetchWithRetry(productUrl);
        cleanedHTML = rawHTML; // No need for preprocessing with new approach
      }

      console.log(`[Product Scraper] Extracting product details with Gemini`);
      const detailsPrompt = createJsonPrompt(
        `${cleanedHTML}\n\n${PRODUCT_DETAILS_PROMPT(productName)}`,
        JSON.stringify(ProductDetailsExample, null, 2)
      );

      const req = {
        model: modelName,
        contents: [
          {
            role: 'user',
            parts: [{ text: detailsPrompt }],
          },
        ],
        config: generationConfig,
      };

      const detailsResult = await ai.models.generateContent(req as any);
      const detailsResponse = this.validateGeminiResponse(detailsResult);
      console.log('[Product Scraper] Raw Gemini response:', detailsResponse);

      const detailsJson = extractJson(detailsResponse);
      console.log('[Product Scraper] Extracted JSON:', JSON.stringify(detailsJson, null, 2));

      LLMLogger.logGeminiOutput('product_details', detailsPrompt, detailsResponse);

      if (!detailsJson) {
        throw new Error('Failed to extract valid JSON from Gemini response');
      }

      if (!this.validateProductDetails(detailsJson)) {
        console.error('[Product Scraper] Invalid product details format:', JSON.stringify(detailsJson, null, 2));
        throw new Error('Invalid product details format - missing required fields');
      }

      // Ensure all image URLs are absolute
      detailsJson.images = detailsJson.images.map((img) => ({
        ...img,
        url: this.ensureProtocol(img.url, productUrl),
      }));

      console.log(`[Product Scraper] Successfully extracted details from: ${productUrl}`);
      return detailsJson;
    } catch (error) {
      console.error('[Product Scraper] Error getting product details:', error);
      throw new HttpError(
        500,
        `Failed to get product details: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private validateProductDetails(details: any): details is ProductDetails {
    if (!details || typeof details !== 'object') {
      console.error('[Product Scraper] Details is null or not an object');
      return false;
    }

    const requiredFields = {
      name: 'string',
      description: 'string',
      images: 'array',
      price: 'number',
      features: 'array',
      type: 'string',
    };

    for (const [field, type] of Object.entries(requiredFields)) {
      if (!(field in details)) {
        console.error(`[Product Scraper] Missing required field: ${field}`);
        return false;
      }

      if (type === 'array' && !Array.isArray(details[field])) {
        console.error(`[Product Scraper] Field ${field} should be an array`);
        return false;
      } else if (type !== 'array' && typeof details[field] !== type) {
        console.error(`[Product Scraper] Field ${field} should be type ${type}, got ${typeof details[field]}`);
        return false;
      }
    }

    if (
      !details.images.every(
        (img: any) => img && typeof img === 'object' && typeof img.url === 'string' && typeof img.alt === 'string'
      )
    ) {
      console.error('[Product Scraper] Invalid image format in images array');
      return false;
    }

    return true;
  }
}

export const productScraper = new ProductScraper();
