/**
 * Extends the search to capture nested JSON structures
 */
function extendSearch(text: string, span: [number, number]): string {
  // Extend the search to try to capture nested structures
  const [start, end] = span;
  let nestCount = 0;

  for (let i = start; i < text.length; i++) {
    if (text[i] === '{') {
      nestCount += 1;
    } else if (text[i] === '}') {
      nestCount -= 1;
      if (nestCount === 0) {
        return text.substring(start, i + 1);
      }
    }
  }
  return text.substring(start, end);
}

/**
 * Extracts JSON objects from text response
 */
export function extractJson(text: string): any | null {
  // First remove any markdown formatting
  const cleanText = text.replace(/```json\n?|\n?```/g, '').trim();

  // Try parsing the entire text first
  try {
    return JSON.parse(cleanText);
  } catch (e) {
    console.log('[JSON Extractor] Full text parse failed, trying to extract J<PERSON><PERSON>');
  }

  // This pattern matches a string that starts with '{' and ends with '}'
  const pattern = /\{[^{}]*\}/g;
  const matches = Array.from(cleanText.matchAll(pattern));

  if (!matches.length) {
    console.error('[JSON Extractor] No JSON objects found in text:', cleanText);
    return null;
  }

  for (const match of matches) {
    const jsonStr = match[0];
    try {
      // Try to parse the direct match
      return JSON.parse(jsonStr);
    } catch (e) {
      try {
        // If direct parse fails, try extended search
        const extendedJson = extendSearch(cleanText, [match.index!, match.index! + jsonStr.length]);
        return JSON.parse(extendedJson);
      } catch (e) {
        console.error('[JSON Extractor] Failed to parse JSON:', e);
        continue;
      }
    }
  }

  return null;
}

/**
 * Creates a prompt that will return consistent JSON
 */
export function createJsonPrompt(basePrompt: string, exampleJson: string): string {
  return `${basePrompt}

Please provide your response in this exact JSON format:
${exampleJson}

IMPORTANT:
1. Return ONLY the JSON object
2. Do not include any markdown formatting (no \`\`\`json)
3. Do not include any explanatory text
4. The response must be valid JSON that can be parsed directly
5. All fields must match the example structure exactly
6. Use the exact same field names as shown
7. Maintain proper JSON syntax with quotes around strings

Example of CORRECT response format:
${exampleJson}

Example of INCORRECT response format:
\`\`\`json
${exampleJson}
\`\`\`

Remember: Return ONLY the JSON object, nothing else!`;
}
