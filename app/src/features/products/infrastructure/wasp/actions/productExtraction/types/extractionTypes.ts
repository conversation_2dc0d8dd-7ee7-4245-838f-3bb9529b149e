export interface ExtractedProduct {
  name: string;
  description: string;
  images: Array<{ url: string; alt?: string }>;
  price?: number;
  features?: string[];
  type?: string;
  confidence?: number;
  [key: string]: any;
}

export interface ExtractionResult {
  products?: ExtractedProduct[];
  confidence?: number;
  [key: string]: any;
}

export interface ExtractionGuidelines {
  taskType: string;
  jsonSchema?: Record<string, any>;
  specificInstructions?: string;
  requiredFields?: string[];
}

export interface ScrapingResult {
  cleanedContent: string;
  extractedData: ExtractionResult;
  metadata: {
    sourceUrl: string;
    extractionTimestamp: string;
    confidence: number;
    duration: number;
  };
}
