import fs from 'fs';
import path from 'path';

export class LL<PERSON>ogger {
  // Save logs in the app's root directory
  private static logDir = path.join(process.cwd(), 'my-saas', 'app', 'llm-logs');

  private static ensureLogDir() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
    console.log(`[LLM Logger] Log directory created at: ${this.logDir}`);
  }

  private static getTimestamp(): string {
    return new Date().toISOString().replace(/[:.]/g, '-');
  }

  static logGeminiOutput(context: string, prompt: string, response: string) {
    this.ensureLogDir();
    const timestamp = this.getTimestamp();
    const filename = `gemini_${context}_${timestamp}.txt`;
    const filePath = path.join(this.logDir, filename);

    const content = `
Timestamp: ${new Date().toISOString()}
Context: ${context}

=== Prompt ===
${prompt}

=== Response ===
${response}
`;

    fs.writeFileSync(filePath, content);
    console.log(`[LLM Logger] Gemini output saved to ${filePath}`);
  }

  static logClaudeOutput(context: string, prompt: string, response: string) {
    this.ensureLogDir();
    const timestamp = this.getTimestamp();
    const filename = `claude_${context}_${timestamp}.txt`;
    const filePath = path.join(this.logDir, filename);

    const content = `
Timestamp: ${new Date().toISOString()}
Context: ${context}

=== Prompt ===
${prompt}

=== Response ===
${response}
`;

    fs.writeFileSync(filePath, content);
    console.log(`[LLM Logger] Claude output saved to ${filePath}`);
  }

  static getLogDir(): string {
    return this.logDir;
  }
}
