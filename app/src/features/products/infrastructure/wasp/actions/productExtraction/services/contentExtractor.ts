import * as fs from 'fs/promises';
import * as path from 'path';
import { ai, modelName, generationConfig } from '../config/aiConfig';
import { LOG_PATHS, ensureLogsDirectories } from '../config/paths';
import { ExtractionGuidelines, ExtractionResult } from '../types/extractionTypes';

export class ContentExtractor {
  static async extractContent(
    cleanedHTML: string,
    guidelines: ExtractionGuidelines,
    url: string
  ): Promise<ExtractionResult> {
    await ensureLogsDirectories();
    const startTime = Date.now();

    // Prepare extraction instructions
    const extractionInstructions = `
            Task: ${guidelines.taskType}
            
            ${guidelines.specificInstructions || ''}

            Extraction Guidelines:
            - Be precise and comprehensive
            - ONLY return structured JSON matching the specified schema NOTHING ELSE!
            ${
              guidelines.requiredFields
                ? `
            Required Fields: ${guidelines.requiredFields.join(', ')}
            `
                : ''
            }
            `;

    console.log(`[Content Extraction] Starting extraction with Gemini`);
    let extractionCompletion;
    try {
      const req = {
        model: modelName,
        contents: [
          {
            role: 'user',
            parts: [
              {
                text: `Extract ${guidelines.taskType} from cleaned HTML:

${cleanedHTML}

${extractionInstructions}

Expected JSON Schema:
${JSON.stringify(guidelines.jsonSchema || {}, null, 2)}

ONLY OUTPUT THE JSON NOTHING ELSE!`,
              },
            ],
          },
        ],
        config: generationConfig,
      };

      extractionCompletion = await ai.models.generateContent(req as any);

      // Log the raw API response
      console.log('[Content Extraction] Gemini API Response:', JSON.stringify(extractionCompletion, null, 2));
    } catch (error) {
      console.error('[Content Extraction] API call failed:', error);
      extractionCompletion = null;
    }

    // Save input and output to logs
    const timestamp = new Date().toISOString().replace(/:/g, '-');

    // Save input (cleaned HTML + instructions)
    const inputLogFilename = `gemini_input_${timestamp}.txt`;
    const inputLogPath = path.join(LOG_PATHS.CONTENT_EXTRACTION, inputLogFilename);
    await fs.writeFile(
      inputLogPath,
      `
Task Type: ${guidelines.taskType}
URL: ${url}
Timestamp: ${timestamp}

Instructions:
${extractionInstructions}

Expected Schema:
${JSON.stringify(guidelines.jsonSchema || {}, null, 2)}

Cleaned HTML Input:
${cleanedHTML}
        `,
      'utf-8'
    );

    // Save output (extracted JSON)
    const outputLogFilename = `gemini_output_${timestamp}.json`;
    const outputLogPath = path.join(LOG_PATHS.CONTENT_EXTRACTION, outputLogFilename);

    let extractedData: ExtractionResult = {
      products: [],
      confidence: 0.7,
    };

    if (extractionCompletion?.candidates?.[0]?.content?.parts?.[0]?.text) {
      try {
        const rawContent = extractionCompletion.candidates[0].content.parts[0].text;
        console.log('[Content Extraction] Raw Gemini output:', rawContent);

        const parsedData = JSON.parse(rawContent);
        extractedData = {
          ...extractedData,
          ...parsedData,
        };
        await fs.writeFile(outputLogPath, JSON.stringify(extractedData, null, 2), 'utf-8');
        console.log(`[Content Extraction] Gemini output saved to: ${outputLogPath}`);
      } catch (parseError) {
        console.error('[Content Extraction] Failed to parse Gemini output:', parseError);
        await fs.writeFile(
          outputLogPath,
          JSON.stringify(
            {
              error: 'Failed to parse output',
              rawContent: extractionCompletion.candidates[0].content.parts[0].text,
            },
            null,
            2
          ),
          'utf-8'
        );
      }
    } else {
      console.error('[Content Extraction] No valid content in Gemini response');
      await fs.writeFile(
        outputLogPath,
        JSON.stringify(
          {
            error: 'No valid content in response',
            response: extractionCompletion,
          },
          null,
          2
        ),
        'utf-8'
      );
    }

    const duration = Date.now() - startTime;
    console.log(`[Content Extraction] Extraction completed`);
    console.log(`[Content Extraction] Duration: ${duration}ms`);

    return extractedData;
  }
}
