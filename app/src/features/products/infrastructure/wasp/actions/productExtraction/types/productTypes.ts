export interface ProductImage {
  url: string;
  alt?: string;
  selected: boolean;
  [key: string]: any; // Add index signature for SuperJSON compatibility
}

export interface DetectedProduct {
  id: number;
  name: string;
  imageUrl: string | null;
  confidence: number;
  selected: boolean;
  originalUrl: string;
  images: ProductImage[];
  metadata: Record<string, any>;
  [key: string]: any; // Add index signature for SuperJSON compatibility
}

export interface ImportProductResult {
  products: Array<{
    id: number;
    name: string;
    description: string;
    imageUrl: string | null;
    confidence: number;
    price: number | null;
    currency: string;
    sku: string;
    type: string;
    features: string[];
    selected: boolean;
    originalUrl: string;
    images: ProductImage[];
    metadata: Record<string, any>;
    [key: string]: any; // Add index signature for SuperJSON compatibility
  }>;
}

export interface ProductImportResult {
  products: DetectedProduct[];
  cleanedHTML: string;
  metadata: Record<string, any>;
  [key: string]: any; // Add index signature for SuperJSON compatibility
}

export interface ProductCreateData {
  name: string;
  productType: string;
  description: string;
  price: number;
  currency: string;
  availability: string;
  features: string[];
  systemRequirements: string;
  dimensions: string;
  format: string;
  targetAudience: string;
  usp: string;
  keywords: string[];
  certifications: string[];
  legalDisclaimers: string[];
  images: string[];
  videos: string[];
  shippingOptions: string[];
  returnPolicy: string;
  reviews: string;
  ratings: string;
  customerSupport: string;
  manufacturer: string;
  releaseDate: string;
  sku: string;
  analysisStatus?: string;
  analysisCompletedAt?: Date;
  [key: string]: any; // Add index signature for SuperJSON compatibility
}
