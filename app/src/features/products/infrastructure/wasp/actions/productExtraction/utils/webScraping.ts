import { HttpError } from 'wasp/server';
import * as cheerio from 'cheerio';
import { URL } from 'url';

const MAX_RETRIES = 3;
const MIN_RETRY_DELAY = 2000; // 2 seconds
const MAX_RETRY_DELAY = 5000; // 5 seconds

// Custom error class for Cloudflare protection that should not be retried
class CloudflareProtectionError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'CloudflareProtectionError';
  }
}

// List of common Chrome versions
const CHROME_VERSIONS = ['120.0.0.0', '121.0.0.0', '119.0.0.0'];
// List of common platforms
const PLATFORMS = ['Windows NT 10.0; Win64; x64', 'Macintosh; Intel Mac OS X 10_15_7', 'X11; Linux x86_64'];

function getRandomUserAgent(): string {
  const chromeVersion = CHROME_VERSIONS[Math.floor(Math.random() * CHROME_VERSIONS.length)];
  const platform = PLATFORMS[Math.floor(Math.random() * PLATFORMS.length)];
  return `Mozilla/5.0 (${platform}) AppleWebKit/537.36 (KHTML, like G<PERSON>o) Chrome/${chromeVersion} Safari/537.36`;
}

function getRandomDelay(): number {
  return Math.floor(Math.random() * (MAX_RETRY_DELAY - MIN_RETRY_DELAY + 1) + MIN_RETRY_DELAY);
}

// Store cookies between requests
let cookieJar: string[] = [];

// Keep track of visited pages to maintain realistic navigation
const visitedPages = new Set<string>();

/**
 * Reset all session data (cookies, visited pages) to start fresh
 */
export function resetSession(): void {
  cookieJar = [];
  visitedPages.clear();
  console.log('[Web Scraping] Session reset: cleared cookies and visited pages');
}

// Helper function to ensure URLs have protocol
function ensureProtocol(url: string, baseUrl?: string): string {
  try {
    if (url.startsWith('//')) {
      return `https:${url}`;
    }
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      // If we have a baseUrl, try to resolve relative URL
      if (baseUrl) {
        return new URL(url, baseUrl).toString();
      }
      // Otherwise, assume https
      return `https://${url}`;
    }
    return url;
  } catch (error) {
    console.error('Failed to parse URL:', url, error);
    throw new HttpError(400, `Invalid URL: ${url}`);
  }
}

async function visitHomepage(url: string, isHomepageVisit = false): Promise<void> {
  if (isHomepageVisit) return; // Prevent recursion

  const urlObj = new URL(url);
  const homepage = `${urlObj.protocol}//${urlObj.hostname}`;

  if (!visitedPages.has(homepage)) {
    console.log(`[Web Scraping] Visiting homepage first: ${homepage}`);
    try {
      await fetchWithRetry(homepage, MAX_RETRIES, true);
      visitedPages.add(homepage);

      // Add a delay after visiting homepage to seem more human-like
      const delay = getRandomDelay();
      await new Promise((resolve) => setTimeout(resolve, delay));
    } catch (error) {
      // If homepage fails with Cloudflare protection, propagate the error immediately
      if (error instanceof HttpError && error.statusCode === 403) {
        throw error;
      }
      // For other errors, still add to visited pages to prevent infinite retries
      visitedPages.add(homepage);
      throw error;
    }
  }
}

export async function fetchWithRetry(url: string, retries = MAX_RETRIES, isHomepageVisit = false): Promise<string> {
  // Visit homepage first if we haven't already
  await visitHomepage(url, isHomepageVisit);
  // Ensure URL has protocol before fetching
  const fullUrl = ensureProtocol(url);
  console.log(`[Web Scraping] Attempt ${MAX_RETRIES - retries + 1} to fetch ${fullUrl}`);

  try {
    // Get base domain for referrer
    const urlObj = new URL(fullUrl);
    const baseDomain = `${urlObj.protocol}//${urlObj.hostname}`;

    const headers = {
      'User-Agent': getRandomUserAgent(),
      Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br',
      Connection: 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      'Cache-Control': 'no-cache',
      Pragma: 'no-cache',
      'Sec-Ch-Ua': '"Not A(Brand";v="99", "Google Chrome";v="121", "Chromium";v="121"',
      'Sec-Ch-Ua-Mobile': '?0',
      'Sec-Ch-Ua-Platform': '"macOS"',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Sec-Fetch-User': '?1',
      Referer: baseDomain,
      ...(cookieJar.length > 0 && { Cookie: cookieJar.join('; ') }),
    };

    // Add random delay before request
    await new Promise((resolve) => setTimeout(resolve, Math.floor(Math.random() * 1000)));

    const response = await fetch(fullUrl, {
      headers,
      credentials: 'include',
    });

    // Store cookies from response
    const setCookies = response.headers.get('set-cookie');
    if (setCookies) {
      cookieJar = setCookies.split(',').map((cookie) => cookie.split(';')[0]);
    }

    if (!response.ok) {
      const responseText = await response.text();
      console.error(`[Web Scraping] HTTP ${response.status} ${response.statusText} for ${fullUrl}`);
      console.error(`[Web Scraping] Response headers:`, Object.fromEntries(response.headers.entries()));
      console.error(`[Web Scraping] Response body preview:`, responseText.substring(0, 1000));
      throw new Error(`HTTP error! status: ${response.status} ${response.statusText}`);
    }

    const content = await response.text();

    // Debug logging to see what we're actually getting
    console.log(`[Web Scraping] Response status: ${response.status} for ${fullUrl}`);
    console.log(`[Web Scraping] Content length: ${content.length} bytes`);
    console.log(`[Web Scraping] Content preview:`, content.substring(0, 300));

    // Check for Cloudflare protection - be more specific to avoid false positives
    const hasCloudflareChallenge =
      content.includes('Verifying your connection') ||
      content.includes('cf_chl_opt') ||
      content.includes('DDoS protection by Cloudflare') ||
      (content.includes('cloudflare') && content.includes('challenge'));

    if (hasCloudflareChallenge) {
      console.warn(`[Web Scraping] Cloudflare protection detected on ${fullUrl}`);
      console.warn(`[Web Scraping] Cloudflare indicators found in content`);
      throw new CloudflareProtectionError('Cloudflare protection detected - automated access blocked');
    }

    console.log(`[Web Scraping] Successfully fetched ${fullUrl} (${content.length} bytes)`);
    return content;
  } catch (error) {
    console.error(`[Web Scraping] Error fetching ${fullUrl}:`, error);

    // Don't retry if it's Cloudflare protection - fail immediately
    if (error instanceof CloudflareProtectionError) {
      console.error(`[Web Scraping] Cloudflare protection detected - not retrying`);
      throw new HttpError(403, error.message);
    }

    if (retries > 1) {
      console.log(`[Web Scraping] Retrying... (${retries - 1} attempts remaining)`);
      const delay = getRandomDelay();
      console.log(`[Web Scraping] Waiting ${delay}ms before retry...`);
      await new Promise((resolve) => setTimeout(resolve, delay));
      return fetchWithRetry(url, retries - 1);
    }

    console.error(`[Web Scraping] All retry attempts failed for ${fullUrl}`);
    throw new HttpError(
      500,
      `Failed to fetch URL after ${MAX_RETRIES} attempts: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

export async function getUrlsFromWebsite(baseUrl: string): Promise<string[]> {
  try {
    const html = await fetchWithRetry(baseUrl);
    const $ = cheerio.load(html);
    const links = new Set<string>();

    // Extract all links
    $('a').each((_, element) => {
      const href = $(element).attr('href');
      if (href) {
        try {
          // Handle protocol-relative and relative URLs
          const absoluteUrl = ensureProtocol(href, baseUrl);
          // Only include URLs from the same domain
          if (absoluteUrl.startsWith(baseUrl)) {
            links.add(absoluteUrl);
          }
        } catch (error) {
          console.warn('Invalid URL:', href);
        }
      }
    });

    return Array.from(links);
  } catch (error) {
    console.error('Failed to get URLs from website:', error);
    return [];
  }
}
