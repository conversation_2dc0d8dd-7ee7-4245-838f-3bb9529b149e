import { GoogleGenAI } from '@google/genai';

// Initialize Vertex AI with proper auth - same pattern as audience imports
const ai = new GoogleGenAI({
  vertexai: true,
  project: process.env.GOOGLE_CLOUD_PROJECT!,
  location: 'global',
});

// Model configuration
const modelName = 'gemini-2.5-pro-preview-06-05';
const generationConfig = {
  temperature: 0.1,
  topP: 0.8,
  topK: 40,
  maxOutputTokens: 8192,
};

// Export AI instance and config for use in ProductScraper
export { ai, modelName, generationConfig };

// Model Parameters
export const GEMINI_PARAMS = {
  temperature: 0.1,
  topP: 0.8,
  topK: 40,
  maxOutputTokens: 8192,
};
