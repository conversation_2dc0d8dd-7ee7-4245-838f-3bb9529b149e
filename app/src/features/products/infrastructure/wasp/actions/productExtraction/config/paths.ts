import * as path from 'path';

export const LOG_PATHS = {
  HTML_CLEANING: path.join(process.cwd(), '.wasp', 'out', 'server', 'logs', 'html_cleaning'),
  CONTENT_EXTRACTION: path.join(process.cwd(), '.wasp', 'out', 'server', 'logs', 'content_extraction'),
};

import fs from 'fs/promises';

export async function ensureLogsDirectories() {
  try {
    await fs.mkdir(LOG_PATHS.HTML_CLEANING, { recursive: true });
    await fs.mkdir(LOG_PATHS.CONTENT_EXTRACTION, { recursive: true });
  } catch (error) {
    console.error('Failed to create logs directories:', error);
  }
}
