import { XMLParser } from 'fast-xml-parser';
import { fetchWithRetry } from './webScraping';

type ProductInfo = {
  name: string;
  productUrl: string;
  imageUrl?: string;
};

interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: string;
  'image:image'?: {
    'image:loc': string;
    'image:title'?: string;
    'image:caption'?: string;
  };
}

interface RegionalSitemap {
  region: string;
  url: string;
  lastmod?: string;
}

interface SitemapIndex {
  sitemapindex: {
    sitemap: Array<{
      loc: string;
      lastmod?: string;
    }>;
  };
}

interface ProductSitemap {
  urlset?: {
    url: SitemapUrl[];
  };
  sitemapindex?: {
    sitemap: Array<{
      loc: string;
      lastmod?: string;
    }>;
  };
}

interface RegionInfo {
  regions: RegionalSitemap[];
  defaultRegion?: string;
}

export class SitemapParser {
  private parser: XMLParser;

  constructor() {
    this.parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: '',
      textNodeName: 'text',
      isArray: (name, jpath, isLeafNode, isAttribute) => {
        return name === 'url' || name === 'sitemap';
      },
    });
  }

  async detectRegions(sitemapContent: string): Promise<RegionInfo | null> {
    try {
      const sitemap = this.parser.parse(sitemapContent) as SitemapIndex;
      if (!sitemap.sitemapindex?.sitemap) return null;

      const regions: RegionalSitemap[] = [];
      let defaultRegion: string | undefined;

      for (const item of sitemap.sitemapindex.sitemap) {
        // Look for regional identifiers in sitemap URLs
        const regionMatches = item.loc.match(/(?:Product_Sitemap|sitemap[_-]products?)_([A-Z]{2})\.xml/i);
        if (regionMatches) {
          const region = regionMatches[1].toUpperCase();
          regions.push({
            region,
            url: item.loc,
            lastmod: item.lastmod,
          });
          // Default to US if available, otherwise first region found
          if (region === 'US' || !defaultRegion) {
            defaultRegion = region;
          }
        }
      }

      return regions.length > 0 ? { regions, defaultRegion } : null;
    } catch (error) {
      console.error('[Sitemap Parser] Error detecting regions:', error);
      return null;
    }
  }

  async findProductSitemap(baseUrl: string, region?: string): Promise<string | null> {
    try {
      console.log(`[Sitemap Parser] Looking for sitemap at: ${baseUrl}`);

      // Try common sitemap locations
      const sitemapLocations = [
        '/sitemap.xml',
        '/sitemap_index.xml',
        '/sitemap-index.xml',
        '/xmlsitemap.php', // BigCommerce style
      ];

      console.log(`[Sitemap Parser] Checking common sitemap locations: ${sitemapLocations.join(', ')}`);
      let sitemapContent: string | null = null;

      for (const location of sitemapLocations) {
        try {
          const sitemapUrl = `${baseUrl}${location}`;
          console.log(`[Sitemap Parser] Attempting to fetch sitemap from: ${sitemapUrl}`);
          sitemapContent = await fetchWithRetry(sitemapUrl);
          console.log(`[Sitemap Parser] Successfully fetched sitemap from: ${sitemapUrl}`);
          console.log(`[Sitemap Parser] Sitemap content length: ${sitemapContent.length} bytes`);

          // Special handling for BigCommerce style sitemaps
          if (location === '/xmlsitemap.php') {
            console.log(`[Sitemap Parser] Processing BigCommerce style sitemap`);
            const sitemapIndex = this.parser.parse(sitemapContent) as SitemapIndex;
            console.log(`[Sitemap Parser] Parsed sitemap structure:`, JSON.stringify(sitemapIndex, null, 2));
            const productSitemap = sitemapIndex.sitemapindex.sitemap.find(
              (sitemap) => sitemap.loc.includes('type=products') || sitemap.loc.includes('products.xml')
            );
            if (productSitemap) {
              console.log(`[Sitemap Parser] Found BigCommerce product sitemap at: ${productSitemap.loc}`);
              return productSitemap.loc;
            }
            continue;
          }

          // Standard sitemap handling
          console.log(`[Sitemap Parser] Processing standard sitemap`);
          const sitemapIndex = this.parser.parse(sitemapContent) as SitemapIndex;
          console.log(
            `[Sitemap Parser] Available sitemaps:`,
            sitemapIndex.sitemapindex.sitemap.map((s) => s.loc)
          );

          // Check for regional sitemaps
          const regionInfo = await this.detectRegions(sitemapContent);
          if (regionInfo) {
            console.log(
              `[Sitemap Parser] Detected regions:`,
              regionInfo.regions.map((r) => r.region)
            );
            if (region) {
              const regionalSitemap = regionInfo.regions.find((r) => r.region === region.toUpperCase());
              if (regionalSitemap) {
                console.log(`[Sitemap Parser] Found regional product sitemap for ${region} at: ${regionalSitemap.url}`);
                return regionalSitemap.url;
              }
            } else if (regionInfo.defaultRegion) {
              const defaultSitemap = regionInfo.regions.find((r) => r.region === regionInfo.defaultRegion);
              if (defaultSitemap) {
                console.log(
                  `[Sitemap Parser] Using default region ${regionInfo.defaultRegion} sitemap at: ${defaultSitemap.url}`
                );
                return defaultSitemap.url;
              }
            }
          }

          // Fallback to standard product sitemap search
          const productSitemap = sitemapIndex.sitemapindex.sitemap.find(
            (sitemap) => sitemap.loc.toLowerCase().includes('products') || sitemap.loc.toLowerCase().includes('product')
          );
          if (productSitemap) {
            console.log(`[Sitemap Parser] Found standard product sitemap at: ${productSitemap.loc}`);
            return productSitemap.loc;
          }
        } catch (error) {
          console.error(`[Sitemap Parser] Error processing ${location}:`, error);
          continue; // Try next location if this one fails
        }
      }

      // If no sitemap found in standard locations, try robots.txt
      try {
        const robotsTxt = await fetchWithRetry(`${baseUrl}/robots.txt`);
        const sitemapMatch = robotsTxt.match(/Sitemap:\s*(.+)/i);
        if (sitemapMatch) {
          return sitemapMatch[1].trim();
        }
      } catch (error) {
        console.error('Error checking robots.txt:', error);
      }

      return null;
    } catch (error) {
      console.error('Error finding product sitemap:', error);
      return null;
    }
  }

  async extractProductsFromSitemap(sitemapUrl: string): Promise<
    Array<{
      name: string;
      productUrl: string;
      imageUrl?: string;
    }>
  > {
    try {
      console.log(`[Sitemap Parser] Fetching product sitemap from: ${sitemapUrl}`);
      const sitemapXml = await fetchWithRetry(sitemapUrl);
      console.log(`[Sitemap Parser] Successfully fetched sitemap (${sitemapXml.length} bytes)`);

      console.log(`[Sitemap Parser] Parsing sitemap XML`);
      const sitemap = this.parser.parse(sitemapXml) as ProductSitemap;
      console.log(`[Sitemap Parser] Parsed sitemap structure:`, JSON.stringify(sitemap, null, 2));

      if (sitemap.urlset?.url) {
        return this.extractProductsFromUrlset(sitemap.urlset.url);
      } else if (sitemap.sitemapindex?.sitemap) {
        const nestedProductSitemap = sitemap.sitemapindex.sitemap.find(
          (s) => s.loc.toLowerCase().includes('products') || s.loc.toLowerCase().includes('product')
        );
        if (nestedProductSitemap) {
          console.log(`[Sitemap Parser] Found nested product sitemap, fetching: ${nestedProductSitemap.loc}`);
          const nestedContent = await fetchWithRetry(nestedProductSitemap.loc);
          const nestedSitemap = this.parser.parse(nestedContent) as ProductSitemap;
          if (!nestedSitemap.urlset?.url) {
            throw new Error('Nested sitemap does not contain product URLs');
          }
          return this.extractProductsFromUrlset(nestedSitemap.urlset.url);
        }
      }

      throw new Error('No product URLs found in sitemap');
    } catch (error) {
      console.error('Error extracting products from sitemap:', error);
      throw error;
    }
  }

  private extractProductsFromUrlset(urls: SitemapUrl[]): ProductInfo[] {
    console.log(`[Sitemap Parser] Found ${urls.length} total URLs in sitemap`);

    const productUrls = urls.filter((url) => {
      const isProduct =
        url.loc.includes('/products/') || // Shopify style
        url.loc.match(/\/(p|product)\//) || // Common patterns
        url.loc.match(/\/([\w-]+)\/?$/); // Generic product endpoint
      return isProduct;
    });

    console.log(`[Sitemap Parser] Identified ${productUrls.length} product URLs`);

    const products = productUrls.map((url) => {
      // Extract product name from URL
      const urlParts = url.loc.split('/');
      const productHandle = urlParts[urlParts.length - 1].replace(/\/$/, '');
      // Try sitemap image title/caption first, fall back to URL-based name
      const name =
        url['image:image']?.['image:title'] ||
        url['image:image']?.['image:caption'] ||
        productHandle
          .split('-')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');

      // Get image URL from image:image tag if it exists
      const imageUrl = url['image:image']?.['image:loc'];

      return {
        name,
        productUrl: url.loc,
        imageUrl: imageUrl || undefined,
      };
    });

    console.log(`[Sitemap Parser] Successfully extracted ${products.length} products`);
    return products;
  }

  async getAllProducts(baseUrl: string, region?: string): Promise<ProductInfo[]> {
    const productSitemapUrl = await this.findProductSitemap(baseUrl, region);
    if (!productSitemapUrl) {
      throw new Error('Could not find product sitemap');
    }

    return this.extractProductsFromSitemap(productSitemapUrl);
  }
}

export const sitemapParser = new SitemapParser();
