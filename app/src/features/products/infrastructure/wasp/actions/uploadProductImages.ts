import { type UploadProductImages } from 'wasp/server/operations';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import sharp from 'sharp';
import { extname } from 'path';

// Cloudflare R2 Configuration
const r2Client = new S3Client({
  region: 'auto',
  endpoint: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.R2_BUCKET_ACCESS_KEY!,
    secretAccessKey: process.env.R2_BUCKET_SECRET_KEY!,
  },
});

const TARGET_FILE_SIZE = 6 * 1024 * 1024; // 6MB

const getR2ImageUrl = (folderPath: string, fileName: string) =>
  `${process.env.R2_PUBLIC_URL}/${folderPath}/${fileName}`;

async function compressImage(buffer: Buffer): Promise<Buffer> {
  let quality = 85; // Start with slightly lower quality since target is 6MB
  let compressedBuffer = buffer;
  let attempt = 0;
  const maxAttempts = 5;

  while (compressedBuffer.length > TARGET_FILE_SIZE && attempt < maxAttempts) {
    try {
      let sharpImage = sharp(buffer);

      // Get image metadata
      const metadata = await sharp(compressedBuffer).metadata();
      const width = metadata.width;

      // Resize based on attempt number
      if (width && (width > 1800 || attempt > 0)) {
        const targetWidth = attempt === 0 ? 1800 : Math.min(1800, Math.floor(width * 0.8));
        sharpImage = sharpImage.resize(targetWidth, null, { withoutEnlargement: true });
      }

      // Convert to JPEG with current quality
      compressedBuffer = await sharpImage.jpeg({ quality }).toBuffer();

      // Reduce quality more aggressively
      quality = Math.max(quality - 12, 55); // Lower minimum quality to 55
      attempt++;

      console.log(
        `Compression attempt ${attempt}: size=${(compressedBuffer.length / 1024 / 1024).toFixed(2)}MB, quality=${quality}`
      );
    } catch (error) {
      console.error('Error compressing image:', error);
      throw error;
    }
  }

  return compressedBuffer;
}

export const uploadProductImages: UploadProductImages<
  {
    images: {
      fileBuffer: string;
      fileName: string;
      contentType: string;
    }[];
    productId: number;
  },
  {
    success: boolean;
    urls?: string[];
    error?: string;
  }
> = async (args, context) => {
  try {
    // Validate user authentication
    if (!context.user || !context.user.id) {
      throw new Error('User is not authenticated');
    }

    const { images, productId } = args;
    const folderPath = `Products/${context.user.id}/${productId}`;
    const uploadedUrls: string[] = [];

    // Validate input
    if (!images || images.length === 0) {
      throw new Error('No images provided');
    }

    // Get current product
    const product = await context.entities.Product.findUnique({
      where: { id: productId },
    });

    if (!product) {
      throw new Error('Product not found');
    }

    // Upload each image
    for (const image of images) {
      // Convert base64 string to Buffer
      const buffer = Buffer.from(image.fileBuffer, 'base64');

      // Check if compression is needed (if over 6MB)
      let processedBuffer: Buffer;
      if (buffer.length > TARGET_FILE_SIZE) {
        console.log(`Compressing image of size ${(buffer.length / 1024 / 1024).toFixed(2)}MB`);
        processedBuffer = await compressImage(buffer);
        console.log(`Compressed to ${(processedBuffer.length / 1024 / 1024).toFixed(2)}MB`);
      } else {
        // If under 6MB, just convert to JPEG with high quality
        processedBuffer = await sharp(buffer).jpeg({ quality: 90 }).toBuffer();
      }

      // Generate a unique file name
      const uniqueFileName = `product-${Date.now()}-${image.fileName.replace(extname(image.fileName), '')}.jpg`;

      const params = {
        Bucket: process.env.R2_BUCKET_NAME!,
        Key: `${folderPath}/${uniqueFileName}`,
        Body: processedBuffer,
        ContentType: 'image/jpeg',
      };

      const command = new PutObjectCommand(params);
      await r2Client.send(command);

      // Generate and store R2 image URL
      const r2ImageUrl = getR2ImageUrl(folderPath, uniqueFileName);
      uploadedUrls.push(r2ImageUrl);

      console.log(`Uploaded image: ${r2ImageUrl}`);
    }

    // Combine existing images with new ones
    const updatedImages = [...(product.images || []), ...uploadedUrls];

    // Update product with combined image URLs
    await context.entities.Product.update({
      where: { id: productId },
      data: {
        images: updatedImages,
      },
    });

    return {
      success: true,
      urls: uploadedUrls,
    };
  } catch (error) {
    console.error('Product Images Upload Error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown upload error',
    };
  }
};
