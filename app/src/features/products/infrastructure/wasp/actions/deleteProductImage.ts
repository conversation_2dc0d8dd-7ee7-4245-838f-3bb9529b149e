import { HttpError } from 'wasp/server';
import { S3Client, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { type DeleteProductImage } from 'wasp/server/operations';

type DeleteProductImageArgs = {
  productId: number;
  imageUrl: string;
};

type DeleteProductImageResult = {
  success: boolean;
  message: string;
};

// Cloudflare R2 Configuration
const r2Client = new S3Client({
  region: 'auto',
  endpoint: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.R2_BUCKET_ACCESS_KEY!,
    secretAccessKey: process.env.R2_BUCKET_SECRET_KEY!,
  },
});

// Helper function to extract key from R2 URL
const getKeyFromUrl = (url: string): string => {
  const publicUrl = process.env.R2_PUBLIC_URL;
  if (!publicUrl) throw new Error('R2_PUBLIC_URL not configured');
  return url.replace(publicUrl + '/', '');
};

export const deleteProductImage: DeleteProductImage<DeleteProductImageArgs, DeleteProductImageResult> = async (
  args,
  context
) => {
  try {
    // Validate user authentication
    if (!context.user || !context.user.id) {
      throw new HttpError(401, 'User not authenticated');
    }

    // Get the product and verify ownership
    const product = await context.entities.Product.findFirst({
      where: {
        id: args.productId,
        userId: context.user.id,
      },
    });

    if (!product) {
      throw new HttpError(404, 'Product not found or access denied');
    }

    // Verify the image URL exists in the product's images
    if (!product.images.includes(args.imageUrl)) {
      throw new HttpError(404, 'Image not found in product');
    }

    // Extract the key from the URL
    const key = getKeyFromUrl(args.imageUrl);

    // Delete from R2
    const deleteCommand = new DeleteObjectCommand({
      Bucket: process.env.R2_BUCKET_NAME!,
      Key: key,
    });

    await r2Client.send(deleteCommand);

    // Update product's images array
    const updatedImages = product.images.filter((url: string) => url !== args.imageUrl);
    await context.entities.Product.update({
      where: { id: args.productId },
      data: {
        images: updatedImages,
      },
    });

    return {
      success: true,
      message: 'Image deleted successfully',
    };
  } catch (error) {
    console.error('Delete Product Image Error:', error);
    throw new HttpError(500, error instanceof Error ? error.message : 'Failed to delete image');
  }
};
