import { type GenerateProductDescription } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { GoogleGenAI } from '@google/genai';
import { Product } from 'wasp/entities';
import fetch from 'node-fetch';

type GenerateProductDescriptionInput = {
  productId: number;
};

// Initialize Vertex AI with proper auth - same pattern as audience imports
const ai = new GoogleGenAI({
  vertexai: true,
  project: process.env.GOOGLE_CLOUD_PROJECT!,
  location: 'global',
});

// Model configuration
const modelName = 'gemini-2.5-pro-preview-06-05';
const generationConfig = {
  temperature: 0.3,
  topP: 0.8,
  topK: 40,
  maxOutputTokens: 8192,
};

// Helper function to convert image URL to base64
async function getImageAsBase64(url: string): Promise<string> {
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`Failed to fetch image: ${response.statusText}`);

    const buffer = await response.buffer();
    return buffer.toString('base64');
  } catch (error) {
    console.error('Error fetching image:', error);
    throw error;
  }
}

export const generateProductDescription: GenerateProductDescription<GenerateProductDescriptionInput, Product> = async (
  args,
  context
) => {
  if (!context.user) {
    throw new HttpError(401, 'Not authorized');
  }

  try {
    // Get product with images
    const product = await context.entities.Product.findUnique({
      where: { id: args.productId },
    });

    if (!product) {
      throw new HttpError(404, 'Product not found');
    }

    if (!product.name || product.name.trim() === '') {
      throw new HttpError(400, 'Product name must be set before generating description');
    }

    if (!product.images || product.images.length < 2) {
      throw new HttpError(400, 'Product must have at least 2 images');
    }

    // Take up to first 8 images and convert them to base64
    const imagesToAnalyze = product.images.slice(0, 8);
    const base64Images = await Promise.all(imagesToAnalyze.map(getImageAsBase64));

    const prompt = `You are a product photography expert. Analyze these ${product.name} images and create a highly detailed description that captures every visual aspect of the ${product.name}. Focus on:

1. Physical Characteristics
- Materials and textures
- Colors and finishes
- Dimensions and proportions
- Unique design elements
- Construction details

2. Visual Details
- Surface patterns or textures
- Visible features and components
- Quality indicators
- Any text or branding visible
- Special finishes or treatments

3. Functional Elements
- Visible mechanisms or parts
- Usage-related features
- Safety features if visible
- Ergonomic design elements

Format your response as a single, detailed paragraph that thoroughly describes the ${product.name} from a visual perspective. Make the description specific enough that it could help in generating accurate product photos.

Example output: "The teething mitt features a soft, textured silicone surface with raised bumps arranged in a honeycomb pattern across the chewing area. The mitt portion is crafted from breathable, baby-pink cotton fabric with a subtle crosshatch texture. The opening has an adjustable elastic band in white, ensuring a secure fit on tiny wrists. The silicone section is approximately 2.5 inches in diameter and features varying bump heights for different sensory experiences. The stitching is precise and reinforced at stress points, with double-lined seams visible in matching pink thread. The product maintains a consistent pastel pink color throughout, with the silicone section having a slightly pearlescent finish that catches light. The overall design is ergonomic, with a curved shape that naturally fits a baby's grip."

Analyze the provided ${product.name} images and create a similarly detailed description that captures every visual aspect of the ${product.name}.`;

    // Generate description using Gemini
    const req = {
      model: modelName,
      contents: [
        {
          role: 'user',
          parts: [
            { text: prompt },
            ...base64Images.map((base64) => ({
              inlineData: {
                mimeType: 'image/jpeg',
                data: base64,
              },
            })),
          ],
        },
      ],
      config: generationConfig,
    };

    const result = await ai.models.generateContent(req as any);

    if (!result?.candidates?.[0]?.content?.parts?.[0]?.text) {
      throw new Error('Invalid response from Gemini');
    }

    const detailedDescription = result.candidates[0].content.parts[0].text;

    // Update product with generated description
    const updatedProduct = await context.entities.Product.update({
      where: { id: args.productId },
      data: {
        detailedDescription,
        detailedDescriptionGeneratedAt: new Date(),
      },
    });

    return updatedProduct;
  } catch (error) {
    console.error('Failed to generate product description:', error);
    throw new HttpError(
      500,
      `Failed to generate description: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};
