import { type GetProductBrief } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import OpenAI from 'openai';

// Initialize <PERSON> for precise product brief generation
const claude<PERSON><PERSON><PERSON> = new OpenAI({
  baseURL: 'https://openrouter.ai/api/v1',
  apiKey: process.env.OPENROUTER_API_KEY!,
  defaultHeaders: {
    'HTTP-Referer': 'https://olivia.ai',
    'X-Title': 'Olivia AI Product Brief Generator',
  },
});

type CreateBriefInput = {
  productId: number;
  productName: string;
  description: string;
  type: string;
  features: string[];
  cleanedHTML: string;
};

export type ProductBrief = {
  objective: string;
  coreMessaging: {
    productName: string;
    tagline: string;
    positioningStatement: string;
    keyAttributes: {
      functionalBenefits: string[];
      technicalFeatures: string[];
      emotionalBenefits: string[];
      uniqueSellingProposition: string;
    };
    marketingPillars: string[];
  };
  visualBranding: {
    packagingDesign: {
      visualThemes: string[];
      informationHierarchy: string[];
    };
    photographyGuidance: {
      mood: string;
      propsAndSetting: string[];
      lighting: string;
      productStyling: string[];
    };
  };
  copywriting: {
    toneOfVoice: string[];
    heroBanner: string;
    productDescription: string;
    socialMediaTeasers: string[];
    emailCampaign: {
      subjectLines: string[];
      marketingSnippet: string;
    };
  };
  targetAudience: {
    primary: {
      demographics: string;
      psychographics: string[];
      behavior: string[];
    };
    secondary: {
      segments: string[];
      characteristics: string[];
    };
  };
  salesAndMarketing: {
    campaigns: Array<{
      name: string;
      description: string;
      elements: string[];
    }>;
    influencerStrategy: {
      type: string;
      goals: string[];
      contentIdeas: string[];
    };
  };
  contentDeliverables: {
    photography: {
      studioShots: string[];
      lifestyleShots: string[];
      closeups: string[];
    };
    video: {
      shortForm: string[];
      longForm: string[];
    };
    graphics: string[];
  };
};

export const getProductBrief: GetProductBrief<CreateBriefInput, ProductBrief> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  try {
    const briefCompletion = await claudeHaiku.chat.completions.create({
      model: 'anthropic/claude-3-5-haiku-20241022:beta',
      messages: [
        {
          role: 'system',
          content:
            'You are an expert at creating comprehensive product briefs for marketing, sales, design, and photography teams. You excel at analyzing product details and creating detailed, actionable briefs that guide creative teams.',
        },
        {
          role: 'user',
          content: `Create a comprehensive product brief for ${args.productName} based on this information:

Product Name: ${args.productName}
Product Type: ${args.type}
Description: ${args.description}
Features: ${args.features.join(', ')}

Additional Product Details from Website:
${args.cleanedHTML}

Create a detailed brief that follows this exact structure:

{
    "objective": "Brief statement explaining the purpose of this document in guiding designers, copywriters, and marketers",
    "coreMessaging": {
        "productName": "Full product name",
        "tagline": "One compelling line that captures the product's essence",
        "positioningStatement": "Clear statement of what makes this product unique and valuable",
        "keyAttributes": {
            "functionalBenefits": ["List of practical benefits and advantages"],
            "technicalFeatures": ["List of technical specifications and capabilities"],
            "emotionalBenefits": ["List of emotional benefits and feelings"],
            "uniqueSellingProposition": "What makes this product stand out in the market"
        },
        "marketingPillars": ["3-4 core marketing messages that define the product's value"]
    },
    "visualBranding": {
        "packagingDesign": {
            "visualThemes": ["Key visual themes to incorporate in design"],
            "informationHierarchy": ["Order of information importance on packaging/materials"]
        },
        "photographyGuidance": {
            "mood": "Description of desired mood and atmosphere",
            "propsAndSetting": ["List of recommended props and settings"],
            "lighting": "Specific lighting requirements and style",
            "productStyling": ["How to style and present the product"]
        }
    },
    "copywriting": {
        "toneOfVoice": ["List of tone characteristics"],
        "heroBanner": "Compelling hero banner text for website/ads",
        "productDescription": "Detailed product description for marketing materials",
        "socialMediaTeasers": ["3-4 engaging social media post ideas"],
        "emailCampaign": {
            "subjectLines": ["3-4 attention-grabbing email subject lines"],
            "marketingSnippet": "Email marketing message that drives action"
        }
    },
    "targetAudience": {
        "primary": {
            "demographics": "Detailed description of primary target audience",
            "psychographics": ["List of psychological characteristics"],
            "behavior": ["List of behavioral patterns and preferences"]
        },
        "secondary": {
            "segments": ["List of secondary audience segments"],
            "characteristics": ["Key characteristics of secondary audiences"]
        }
    },
    "salesAndMarketing": {
        "campaigns": [
            {
                "name": "Campaign name",
                "description": "Campaign description and objectives",
                "elements": ["List of campaign elements and tactics"]
            }
        ],
        "influencerStrategy": {
            "type": "Type of influencers to target",
            "goals": ["List of influencer campaign goals"],
            "contentIdeas": ["Content ideas for influencers"]
        }
    },
    "contentDeliverables": {
        "photography": {
            "studioShots": ["List of required studio product shots"],
            "lifestyleShots": ["List of required lifestyle/context shots"],
            "closeups": ["List of required detail/feature shots"]
        },
        "video": {
            "shortForm": ["List of short-form video content ideas"],
            "longForm": ["List of long-form video content ideas"]
        },
        "graphics": ["List of required graphic assets and visuals"]
    }
}

Ensure your response exactly matches this structure, replacing the example values with detailed, relevant content for ${args.productName}. Make the brief as comprehensive as possible, adapting the content to be appropriate for a ${args.type} product. Focus on creating actionable guidance for the creative team.`,
        },
      ],
      response_format: { type: 'json_object' },
    });

    const brief = JSON.parse(briefCompletion.choices[0]?.message?.content || '{}');

    // Store brief in product reviews field as JSON string
    const reviewsData = {
      brief,
      briefGeneratedAt: new Date().toISOString(),
    };

    await context.entities.Product.update({
      where: { id: args.productId },
      data: {
        reviews: JSON.stringify(reviewsData),
      },
    });

    return brief;
  } catch (error) {
    console.error('Product Brief Generation Error:', error);
    throw new HttpError(
      500,
      `Failed to generate product brief: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};
