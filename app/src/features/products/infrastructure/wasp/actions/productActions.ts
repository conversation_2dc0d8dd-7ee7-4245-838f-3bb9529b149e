import { type CreateProduct, type UpdateProduct, type DeleteProduct } from 'wasp/server/operations';
import { Product } from 'wasp/entities';
import { HttpError } from 'wasp/server';
import { type ProductCreateData } from './productExtraction';
import { S3Client, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { Prisma } from '@prisma/client';
function authenticateUser(context: any) {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }
  return context.user;
}

// Cloudflare R2 Configuration
const r2Client = new S3Client({
  region: 'auto',
  endpoint: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.R2_BUCKET_ACCESS_KEY!,
    secretAccessKey: process.env.R2_BUCKET_SECRET_KEY!,
  },
});

// Helper function to extract key from R2 URL
const getKeyFromUrl = (url: string): string => {
  const publicUrl = process.env.R2_PUBLIC_URL;
  if (!publicUrl) throw new Error('R2_PUBLIC_URL not configured');
  return url.replace(publicUrl + '/', '');
};

// Helper function to generate a unique SKU
const generateSKU = async (context: any, name: string, userId: number): Promise<string> => {
  // Create a base SKU from the product name and user ID
  const baseSlug = name
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '') // Remove non-alphanumeric characters
    .slice(0, 10); // Take first 10 characters

  const userPrefix = userId.toString().padStart(4, '0');
  const timestamp = Date.now().toString().slice(-4);
  const baseSKU = `${userPrefix}-${baseSlug}-${timestamp}`;

  // Check if this SKU exists
  const existingProduct = await context.entities.Product.findUnique({
    where: { sku: baseSKU },
  });

  if (!existingProduct) {
    return baseSKU;
  }

  // If SKU exists, append a random number until we find a unique one
  let counter = 1;
  while (true) {
    const newSKU = `${baseSKU}-${counter}`;
    const exists = await context.entities.Product.findUnique({
      where: { sku: newSKU },
    });
    if (!exists) {
      return newSKU;
    }
    counter++;
  }
};

export const createProduct: CreateProduct<Partial<ProductCreateData> & { organizationId: string }, Product> = async (
  args,
  context
) => {
  const currentUser = authenticateUser(context);

  const productData = {
    name: args.name ?? '',
    productType: args.productType ?? '',
    description: args.description ?? '',
    price: args.price ?? 0,
    currency: args.currency ?? '',
    availability: args.availability ?? '',
    features: args.features ?? [],
    systemRequirements: args.systemRequirements ?? '',
    dimensions: args.dimensions ?? '',
    format: args.format ?? '',
    targetAudience: args.targetAudience ?? '',
    usp: args.usp ?? '',
    keywords: args.keywords ?? [],
    certifications: args.certifications ?? [],
    legalDisclaimers: args.legalDisclaimers ?? [],
    images: args.images ?? [],
    videos: args.videos ?? [],
    shippingOptions: args.shippingOptions ?? [],
    returnPolicy: args.returnPolicy ?? '',
    reviews: args.reviews ?? '[]',
    ratings: args.ratings ?? '[]',
    customerSupport: args.customerSupport ?? '',
    manufacturer: args.manufacturer ?? '',
    releaseDate: args.releaseDate ?? '',
    // Generate a unique SKU if none is provided
    sku: args.sku || (await generateSKU(context, args.name ?? '', currentUser.id)),
    // Set analysis status to COMPLETED for manually created products
    analysisStatus: args.analysisStatus ?? 'COMPLETED',
    analysisCompletedAt: args.analysisStatus === 'COMPLETED' ? new Date() : undefined,
  };

  const newProduct = await context.entities.Product.create({
    data: {
      ...productData,
      user: {
        connect: {
          id: currentUser.id,
        },
      },
      organization: { connect: { id: args.organizationId } },
    },
  });

  return newProduct;
};

export const updateProduct: UpdateProduct<Partial<Product>, Product> = async (args, context) => {
  const currentUser = authenticateUser(context);

  const { id, similarImages, ...rest } = args;

  if (!id) {
    throw new HttpError(400, 'Product ID is required');
  }

  // First check if the product belongs to the user
  const product = await context.entities.Product.findFirst({
    where: {
      id,
      userId: currentUser.id,
    },
  });

  if (!product) {
    throw new HttpError(404, 'Product not found');
  }

  return await context.entities.Product.update({
    where: { id },
    data: {
      ...rest,
      ...(similarImages && { similarImages: similarImages }),
    } as Prisma.ProductUpdateInput,
  });
};

export const deleteProduct: DeleteProduct<{ id: number }, Product> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const { id } = args;

  // First check if the product belongs to the user
  const product = await context.entities.Product.findFirst({
    where: {
      id,
      userId: context.user.id,
    },
  });

  if (!product) {
    throw new HttpError(404, 'Product not found');
  }

  // Delete all associated images from R2
  if (product.images && product.images.length > 0) {
    for (const imageUrl of product.images) {
      try {
        const key = getKeyFromUrl(imageUrl);
        const deleteCommand = new DeleteObjectCommand({
          Bucket: process.env.R2_BUCKET_NAME!,
          Key: key,
        });
        await r2Client.send(deleteCommand);
        console.log(`Deleted image: ${imageUrl}`);
      } catch (error) {
        console.error(`Failed to delete image ${imageUrl}:`, error);
        // Continue with other deletions even if one fails
      }
    }
  }

  // Delete the product
  return await context.entities.Product.delete({
    where: { id },
  });
};
