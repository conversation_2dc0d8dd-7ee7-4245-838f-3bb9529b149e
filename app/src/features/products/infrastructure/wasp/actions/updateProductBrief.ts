import { type UpdateProductBrief } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { ProductBrief } from './getProductBrief';

type UpdateBriefInput = {
  productId: number;
  brief: ProductBrief;
};

export const updateProductBrief: UpdateProductBrief<UpdateBriefInput, void> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  if (!args.productId) {
    throw new HttpError(400, 'Product ID is required');
  }

  try {
    const product = await context.entities.Product.findUnique({
      where: {
        id: Number(args.productId), // Convert to number to ensure correct type
      },
    });

    if (!product) {
      throw new HttpError(404, 'Product not found');
    }

    // Get existing reviews data
    const reviewsData = product.reviews ? JSON.parse(product.reviews as string) : {};

    // Update brief while preserving other review data
    const updatedReviewsData = {
      ...reviewsData,
      brief: args.brief,
      briefUpdatedAt: new Date().toISOString(),
    };

    // Save updated brief back to product
    await context.entities.Product.update({
      where: {
        id: Number(args.productId), // Convert to number to ensure correct type
      },
      data: {
        reviews: JSON.stringify(updatedReviewsData),
      },
    });
  } catch (error) {
    console.error('Failed to update product brief:', error);
    throw new HttpError(500, `Failed to update brief: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};
