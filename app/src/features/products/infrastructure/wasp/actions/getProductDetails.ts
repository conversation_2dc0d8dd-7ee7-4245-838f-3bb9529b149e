import { type GetProductDetails } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import sharp from 'sharp';
import axios from 'axios';
import { type ProductImage, type ImportProductResult } from './productExtraction';
import { productScraper } from './productExtraction/services/productScraper';

// Cloudflare R2 Configuration
const r2Client = new S3Client({
  region: 'auto',
  endpoint: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.R2_BUCKET_ACCESS_KEY!,
    secretAccessKey: process.env.R2_BUCKET_SECRET_KEY!,
  },
});

const getR2ImageUrl = (folderPath: string, fileName: string) =>
  `${process.env.R2_PUBLIC_URL}/${folderPath}/${fileName}`;

// Helper function to ensure URL has protocol
function ensureProtocol(url: string): string {
  if (url.startsWith('//')) {
    return `https:${url}`;
  }
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return `https://${url}`;
  }
  return url;
}

// Helper function to download and upload image to R2
async function downloadAndUploadImage(imageUrl: string, productId: number, userId: number): Promise<string> {
  try {
    // Ensure URL has protocol
    const fullUrl = ensureProtocol(imageUrl);
    console.log('Processing image:', fullUrl);

    // Download image
    const response = await axios.get(fullUrl, {
      responseType: 'arraybuffer',
      maxRedirects: 5,
      timeout: 10000, // 10 second timeout
    });
    const buffer = Buffer.from(response.data);

    // Convert to JPG and optimize
    const jpgBuffer = await sharp(buffer).jpeg({ quality: 90 }).toBuffer();

    // Generate unique filename
    const uniqueFileName = `product-${Date.now()}-${Math.random().toString(36).substring(7)}.jpg`;
    const folderPath = `Products/${userId}/${productId}`;

    // Upload to R2
    const params = {
      Bucket: process.env.R2_BUCKET_NAME!,
      Key: `${folderPath}/${uniqueFileName}`,
      Body: jpgBuffer,
      ContentType: 'image/jpeg',
    };

    const command = new PutObjectCommand(params);
    await r2Client.send(command);

    // Return R2 URL
    return getR2ImageUrl(folderPath, uniqueFileName);
  } catch (error) {
    console.error('Failed to process image:', imageUrl, error);
    throw error;
  }
}

type ProductDetailsResponse = {
  product: ImportProductResult['products'][0];
  [key: string]: any; // Add index signature for SuperJSON compatibility
};

export const getProductDetails: GetProductDetails<
  {
    productName: string;
    productUrl: string;
    productId: number;
    userId: number;
  },
  ProductDetailsResponse
> = async (args, context) => {
  const { productName, productUrl, productId, userId } = args;

  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  try {
    // Get detailed product info using ProductScraper
    console.log(`[Product Details] Getting details for: ${productName}`);
    const productDetails = await productScraper.getProductDetails(productUrl, productName);

    // Process and upload images to R2
    const processedImages: ProductImage[] = [];
    if (productDetails.images && productDetails.images.length > 0) {
      for (const img of productDetails.images) {
        try {
          const imageUrl = img.url;
          // Download and upload to R2
          const r2Url = await downloadAndUploadImage(imageUrl, productId, userId);

          processedImages.push({
            url: r2Url,
            alt: img.alt || productName,
            selected: false,
          });
        } catch (error) {
          console.error('Failed to process image:', error);
          // Continue with other images if one fails
        }
      }
    }

    const response: ProductDetailsResponse = {
      product: {
        id: productId,
        name: productDetails.name,
        description: productDetails.description,
        imageUrl: processedImages[0]?.url || null,
        confidence: 0.85,
        price: productDetails.price || 0,
        currency: 'USD',
        sku: '',
        type: productDetails.type || 'Unknown',
        features: productDetails.features || [],
        selected: false,
        originalUrl: productUrl,
        images: processedImages,
        metadata: {
          ...productDetails,
          sourceUrl: productUrl,
          extractionTimestamp: new Date().toISOString(),
        },
      },
    };

    return response;
  } catch (error) {
    console.error('Product Details Extraction Error:', error);
    throw new HttpError(
      500,
      `Failed to get product details: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};
