import { HttpError, prisma } from 'wasp/server';
import { type SelectProductsForImport, type StartProductImport } from 'wasp/server/operations';
import { Prisma } from '@prisma/client';
import { getProductDetails } from './getProductDetails';
import { getProductBrief } from './getProductBrief';
import { generateProductDescription } from './generateProductDescription';
import { DiscoveryStage } from '../../../domain/types';

// Define the types locally to avoid serialization issues
interface DetectedProduct {
  [key: string]: any; // Add index signature for SuperJSON compatibility
  id: number;
  name: string;
  description?: string;
  imageUrl?: string;
  productUrl?: string;
  confidence?: number;
  selected: boolean;
  brandName?: string;
  price?: number;
  currency?: string;
  type?: string;
  sku?: string;
  features?: string[];
  images?: Array<{
    url: string;
    selected: boolean;
  }>;
  metadata?: {
    productDetailsUrl?: string;
    cleanedHTML?: string;
    discoveredAt?: string;
  };
}

type SelectProductsForImportInput = {
  id: string;
  selectedProducts: DetectedProduct[];
  manualUrls?: string[];
  organizationId: string;
};

type StartProductImportInput = {
  id: string;
  organizationId: string;
};

type StartProductImportOutput = {
  id: string;
  status: string;
};

function authenticateUser(context: any) {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }
  return context.user;
}

const MAX_CONCURRENT_ANALYSIS = 2;

// Helper function to get base URL from product URL
function getBaseUrl(url?: string): string {
  if (!url) return 'https://aeropress.com';
  try {
    const urlObj = new URL(url);
    return `${urlObj.protocol}//${urlObj.host}`;
  } catch {
    return 'https://aeropress.com';
  }
}

// Helper function to get product URL from various sources
function getProductUrl(product: DetectedProduct, baseUrl?: string): string | undefined {
  // Try direct URLs first
  if (product.productUrl) return product.productUrl;
  if (product.metadata?.productDetailsUrl) return product.metadata.productDetailsUrl;

  // If we have a base URL and product name, construct the URL
  if (baseUrl && product.name) {
    const slug = product.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-') // Replace non-alphanumeric with hyphens
      .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
    return `${baseUrl}/products/${slug}`;
  }

  return undefined;
}

// Phase 2: Create job for selected products and start background processing
export const selectProductsForImport: SelectProductsForImport<SelectProductsForImportInput, any> = async (
  args,
  context
) => {
  const currentUser = authenticateUser(context);

  // Get base URL from first product's details URL
  const baseUrl = getBaseUrl(args.selectedProducts[0]?.metadata?.productDetailsUrl);

  // Create import job for selected products and manual URLs
  const importJob = await prisma.importJob.create({
    data: {
      id: args.id,
      websiteUrl: baseUrl,
      status: 'SELECTION_PENDING',
      userId: currentUser.id,
      selectedProducts: args.selectedProducts as any,
      selectedProductCount: args.selectedProducts.length + (args.manualUrls?.length || 0),
      products: [],
      importedProducts: [],
      errors: [],
      analysisInProgress: false,
      currentlyAnalyzing: [],
      analysisQueue: [],
      maxConcurrentAnalysis: MAX_CONCURRENT_ANALYSIS,
      organizationId: args.organizationId,
      metadata: JSON.stringify({
        manualUrls: args.manualUrls || [],
        currentStage: 'complete' as DiscoveryStage,
      }),
    },
  });

  // Log just the count, not the full list
  console.log(`[Import Job] Created job ${importJob.id} with ${importJob.selectedProductCount} products`);

  return {
    id: importJob.id,
    selectedProductCount: importJob.selectedProductCount,
  };
};

// Phase 2: Start background processing of selected products
export const startProductImport: StartProductImport<StartProductImportInput, StartProductImportOutput> = async (
  args,
  context
) => {
  const currentUser = authenticateUser(context);

  const importJob = await prisma.importJob.findFirst({
    where: {
      id: args.id,
      userId: currentUser.id,
    },
  });

  if (!importJob) {
    throw new HttpError(404, 'Import job not found');
  }

  if (importJob.status !== 'SELECTION_PENDING') {
    throw new HttpError(400, 'Import job is not ready for import');
  }

  const selectedProductsData = importJob.selectedProducts as unknown as DetectedProduct[];

  if (!selectedProductsData?.length) {
    throw new HttpError(400, 'No products selected for import');
  }

  try {
    // Get base URL from import job or first product's details URL
    const baseUrl = importJob.websiteUrl || getBaseUrl(selectedProductsData[0]?.metadata?.productDetailsUrl);

    // Create basic products from selected products and manual URLs
    const selectedProducts = await createInitialProducts(
      selectedProductsData,
      currentUser.id,
      args.organizationId,
      baseUrl
    );

    // Create placeholder products for manual URLs
    const manualProducts = await Promise.all(
      ((JSON.parse(importJob.metadata as string)?.manualUrls as string[]) || []).map(async (url) => {
        const urlParts = url.split('/');
        const name = urlParts[urlParts.length - 1]?.replace(/-/g, ' ') || 'Product';

        return prisma.product.create({
          data: {
            name,
            brandName: '',
            description: 'Processing...',
            price: 0,
            currency: 'USD',
            productType: 'Unknown',
            images: [],
            features: [],
            sku: `SKU-${Date.now()}`,
            availability: 'In Stock',
            targetAudience: 'Not Specified',
            usp: 'Processing...',
            reviews: '[]',
            ratings: '[]',
            customerSupport: 'Standard Support',
            manufacturer: 'Unknown Manufacturer',
            releaseDate: new Date().toISOString(),
            analysisStatus: 'PENDING',
            originalUrl: url,
            similarImages: { images: [], lastUpdated: new Date().toISOString() } as Prisma.InputJsonValue,
            user: { connect: { id: currentUser.id } },
            organization: { connect: { id: args.organizationId } },
          },
        });
      })
    );

    const products = [...selectedProducts, ...manualProducts];

    if (!products.length) {
      throw new HttpError(500, 'Failed to create any products');
    }

    // Get the first created product to return
    const firstProduct = products[0];

    // Start the import process with analysis queue
    const updatedJob = await prisma.importJob.update({
      where: { id: args.id },
      data: {
        status: 'IMPORTING',
        importedProductCount: 0,
        analysisInProgress: true,
        analysisQueue: products.map((p) => p.id.toString()),
        currentlyAnalyzing: [],
        updatedAt: new Date(),
      },
    });

    // Start background analysis queue
    processAnalysisQueue(updatedJob.id, baseUrl, context).catch((error) => {
      console.error('Background analysis failed:', error);
    });

    // Return the first created product along with job status
    return {
      id: firstProduct.id.toString(),
      status: updatedJob.status,
    };
  } catch (error) {
    throw new HttpError(500, 'Failed to start import process');
  }
};

// Helper function to create initial products with basic info
async function createInitialProducts(
  products: DetectedProduct[],
  userId: string,
  organizationId: string,
  baseUrl: string
) {
  const createdProducts: any[] = [];
  for (const product of products) {
    try {
      // Get product URL from various sources
      const productUrl = getProductUrl(product, baseUrl);
      if (!productUrl) {
        console.warn(`[Import Job] No URL found for product ${product.name}, skipping...`);
        continue;
      }

      const newProduct = await prisma.product.create({
        data: {
          name: product.name,
          brandName: product.brandName || '',
          description: product.description || '',
          price: product.price || 0,
          currency: product.currency || 'USD',
          productType: product.type || 'Unknown',
          images: product.imageUrl ? [product.imageUrl] : [],
          features: [],
          sku: product.sku || `SKU-${Date.now()}`,
          availability: 'In Stock',
          targetAudience: 'Not Specified',
          usp: 'No unique selling point provided',
          reviews: '[]',
          ratings: '[]',
          customerSupport: 'Standard Support',
          manufacturer: 'Unknown Manufacturer',
          releaseDate: new Date().toISOString(),
          analysisStatus: 'PENDING',
          originalUrl: productUrl,
          similarImages: { images: [], lastUpdated: new Date().toISOString() } as Prisma.InputJsonValue,
          user: {
            connect: { id: userId },
          },
          organization: { connect: { id: organizationId } },
        },
      });
      createdProducts.push(newProduct);
      console.log(`[Import Job] Created product ${newProduct.id} with URL: ${productUrl}`);
    } catch (error: any) {
      console.error(`[Import Job] Failed to create initial product ${product.name}:`, error);
    }
  }
  return createdProducts;
}

// Background queue processor for detailed product analysis
async function processAnalysisQueue(jobId: string, baseUrl: string, context: any) {
  while (true) {
    const importJob = await prisma.importJob.findUnique({
      where: { id: jobId },
    });

    if (!importJob) break;

    if (!importJob.analysisInProgress) break;

    // Check if we can start more analysis jobs
    if ((importJob.currentlyAnalyzing?.length || 0) >= (importJob.maxConcurrentAnalysis || MAX_CONCURRENT_ANALYSIS)) {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      continue;
    }

    // Get next product to analyze
    const nextProductId = importJob.analysisQueue?.[0];
    if (!nextProductId) {
      // Queue is empty, check if we're done
      if ((importJob.currentlyAnalyzing?.length || 0) === 0) {
        await prisma.importJob.update({
          where: { id: jobId },
          data: {
            analysisInProgress: false,
            status: 'COMPLETED',
            completedAt: new Date(),
          },
        });
        break;
      }
      await new Promise((resolve) => setTimeout(resolve, 1000));
      continue;
    }

    // Start analysis for next product
    try {
      // Move product from queue to in-progress
      await prisma.importJob.update({
        where: { id: jobId },
        data: {
          analysisQueue: {
            set: importJob.analysisQueue?.filter((id) => id !== nextProductId) || [],
          },
          currentlyAnalyzing: {
            push: nextProductId,
          },
        },
      });

      // Get product details
      const product = await prisma.product.findUnique({
        where: { id: parseInt(nextProductId) },
      });

      if (!product) {
        console.error(`Product ${nextProductId} not found`);
        continue;
      }

      // Get product URL from originalUrl field
      const productUrl = product.originalUrl;
      if (!productUrl) {
        throw new Error(`Product URL not found for ${product.name}`);
      }

      // Update analysis status
      await prisma.product.update({
        where: { id: parseInt(nextProductId) },
        data: {
          analysisStatus: 'IN_PROGRESS',
          analysisStartedAt: new Date(),
        },
      });

      // Perform detailed analysis with new ProductScraper
      const analysisResult = await getProductDetails(
        {
          productName: product.name,
          productUrl: productUrl,
          productId: parseInt(nextProductId),
          userId: context.user.id,
        },
        context
      );

      // Generate product brief
      const brief = await getProductBrief(
        {
          productId: parseInt(nextProductId),
          productName: product.name,
          description: analysisResult.product.description,
          type: analysisResult.product.type,
          features: analysisResult.product.features,
          cleanedHTML: '',
        },
        context
      );

      // Update product with detailed info and brief
      const updatedProduct = await prisma.product.update({
        where: { id: parseInt(nextProductId) },
        data: {
          description: analysisResult.product.description,
          features: analysisResult.product.features,
          images: analysisResult.product.images.map((img) => img.url),
          price: analysisResult.product.price || 0,
          currency: analysisResult.product.currency || 'USD',
          productType: analysisResult.product.type || 'Unknown',
          analysisStatus: 'COMPLETED',
          analysisCompletedAt: new Date(),
          reviews: JSON.stringify({ brief, briefGeneratedAt: new Date().toISOString() }),
        },
      });

      // Now that we have all images, try to generate the product description
      try {
        await generateProductDescription({ productId: updatedProduct.id }, context);
      } catch (error) {
        console.error(`Failed to generate description for product ${updatedProduct.id}:`, error);
      }

      // Remove from currently analyzing
      await prisma.importJob.update({
        where: { id: jobId },
        data: {
          currentlyAnalyzing: {
            set: importJob.currentlyAnalyzing?.filter((id) => id !== nextProductId) || [],
          },
          importedProductCount: {
            increment: 1,
          },
        },
      });

      // Log the URL used for this product
      console.log(`[Product Import] Product ${nextProductId} was imported from: ${productUrl}`);
    } catch (error: any) {
      console.error(`Analysis failed for product ${nextProductId}:`, error);

      // Update product with error
      await prisma.product.update({
        where: { id: parseInt(nextProductId) },
        data: {
          analysisStatus: 'FAILED',
          analysisError: error.message || 'Unknown error during analysis',
        },
      });

      // Remove from currently analyzing and add error
      await prisma.importJob.update({
        where: { id: jobId },
        data: {
          currentlyAnalyzing: {
            set: importJob.currentlyAnalyzing?.filter((id) => id !== nextProductId) || [],
          },
          errors: {
            push: {
              message: `Analysis failed for product ${nextProductId}: ${error.message}`,
              timestamp: new Date(),
            },
          },
        },
      });
    }
  }
}
