/**
 * 🛍️ Product Domain Service
 *
 * @description Business logic and use cases for products
 * @responsibility Orchestrates product operations and enforces business rules
 * @dependencies Product repository, domain entities
 * @ai_context Core business logic service for product operations
 */

import { Product, ProductEntity, ProductStatus, AnalysisStatus } from '../entities/product.entity';
import { IProductRepository, ProductNotFoundError, ProductValidationError } from '../repositories/product.repository';
import {
  CreateProductInput,
  UpdateProductInput,
  ProductListOptions,
  ProductSearchResult,
  ProductStatistics,
  DetectedProduct,
  ProductAnalysisResult,
  ProductBrief,
  ProductBriefInput,
} from '../types';

export class ProductService {
  constructor(private productRepository: IProductRepository) {}

  /**
   * Create a new product with validation
   */
  async createProduct(input: CreateProductInput, userId: number): Promise<Product> {
    // Validate input
    this.validateCreateInput(input);

    // Generate SKU if not provided
    if (!input.sku) {
      input.sku = await this.productRepository.generateSku(input.name, userId);
    }

    // Check for duplicates
    await this.checkForDuplicates(input, userId);

    // Set default analysis status for manual products
    if (!input.analysisStatus) {
      input.analysisStatus = AnalysisStatus.COMPLETED;
    }

    // Set default status
    if (!input.status) {
      input.status = ProductStatus.ACTIVE;
    }

    return await this.productRepository.create(input, userId);
  }

  /**
   * Update an existing product
   */
  async updateProduct(id: number, input: UpdateProductInput, userId: number): Promise<Product> {
    // Check if product exists and user has permission
    const existingProduct = await this.productRepository.findById(id, userId);
    if (!existingProduct) {
      throw new ProductNotFoundError(id);
    }

    // Validate update input
    this.validateUpdateInput(input);

    // Check for SKU conflicts if SKU is being updated
    if (input.sku && input.sku !== existingProduct.sku) {
      const duplicateProduct = await this.productRepository.findBySku(input.sku, existingProduct.organizationId);
      if (duplicateProduct && duplicateProduct.id !== id) {
        throw new ProductValidationError(`Product with SKU '${input.sku}' already exists`);
      }
    }

    return await this.productRepository.update(id, input, userId);
  }

  /**
   * Delete a product
   */
  async deleteProduct(id: number, userId: number): Promise<boolean> {
    const product = await this.productRepository.findById(id, userId);
    if (!product) {
      throw new ProductNotFoundError(id);
    }

    return await this.productRepository.delete(id, userId);
  }

  /**
   * Get product by ID
   */
  async getProduct(id: number, userId?: number): Promise<Product | null> {
    return await this.productRepository.findById(id, userId);
  }

  /**
   * Get products with filtering and pagination
   */
  async getProducts(options: ProductListOptions, userId?: number): Promise<Product[]> {
    return await this.productRepository.findMany(options, userId);
  }

  /**
   * Search products with advanced filtering
   */
  async searchProducts(options: ProductListOptions, userId?: number): Promise<ProductSearchResult> {
    return await this.productRepository.search(options, userId);
  }

  /**
   * Get product statistics
   */
  async getProductStatistics(organizationId: string, userId?: number): Promise<ProductStatistics> {
    return await this.productRepository.getStatistics(organizationId, userId);
  }

  /**
   * Import products from detected products
   */
  async importProducts(
    detectedProducts: DetectedProduct[],
    organizationId: string,
    userId: number
  ): Promise<Product[]> {
    const createInputs: CreateProductInput[] = detectedProducts
      .filter((dp) => dp.selected)
      .map((dp) => this.mapDetectedProductToCreateInput(dp, organizationId));

    return await this.productRepository.bulkCreate(createInputs, userId);
  }

  /**
   * Analyze product and update analysis status
   */
  async analyzeProduct(id: number, userId: number): Promise<ProductAnalysisResult> {
    const product = await this.productRepository.findById(id, userId);
    if (!product) {
      throw new ProductNotFoundError(id);
    }

    try {
      // Update status to in progress
      await this.productRepository.updateAnalysisStatus(id, AnalysisStatus.IN_PROGRESS);

      // Perform analysis (this would integrate with AI services)
      const analysisResult = await this.performProductAnalysis(product);

      // Update product with analysis results
      if (analysisResult.extractedData) {
        await this.productRepository.update(
          id,
          {
            id,
            features: analysisResult.extractedData.features,
            keywords: analysisResult.extractedData.keywords,
            targetAudience: analysisResult.extractedData.targetAudience,
            usp: analysisResult.extractedData.usp,
            description: analysisResult.extractedData.description,
          },
          userId
        );
      }

      // Add similar images if found
      if (analysisResult.similarImages) {
        await this.productRepository.addSimilarImages(id, {
          images: analysisResult.similarImages,
          lastUpdated: new Date().toISOString(),
        });
      }

      // Update analysis status to completed
      await this.productRepository.updateAnalysisStatus(id, AnalysisStatus.COMPLETED, new Date());

      return {
        ...analysisResult,
        analysisStatus: AnalysisStatus.COMPLETED,
      };
    } catch (error) {
      // Update analysis status to failed
      await this.productRepository.updateAnalysisStatus(id, AnalysisStatus.FAILED);

      return {
        productId: id,
        analysisStatus: AnalysisStatus.FAILED,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Generate product brief
   */
  async generateProductBrief(input: ProductBriefInput): Promise<ProductBrief> {
    const product = await this.productRepository.findById(input.productId);
    if (!product) {
      throw new ProductNotFoundError(input.productId);
    }

    // This would integrate with AI services to generate the brief
    const brief = await this.generateBriefContent(product, input);

    return {
      productId: input.productId,
      brief,
      generatedAt: new Date(),
      version: 1,
    };
  }

  /**
   * Find similar products
   */
  async findSimilarProducts(productId: number, limit: number = 5): Promise<Product[]> {
    return await this.productRepository.findRelated(productId, limit);
  }

  /**
   * Bulk operations
   */
  async bulkUpdateProducts(
    updates: Array<{ id: number; data: UpdateProductInput }>,
    userId: number
  ): Promise<Product[]> {
    // Validate all updates first
    for (const update of updates) {
      this.validateUpdateInput(update.data);
    }

    return await this.productRepository.bulkUpdate(updates, userId);
  }

  async bulkDeleteProducts(ids: number[], userId: number): Promise<boolean> {
    return await this.productRepository.bulkDelete(ids, userId);
  }

  /**
   * Archive/restore operations
   */
  async archiveProducts(ids: number[], userId: number): Promise<boolean> {
    return await this.productRepository.archive(ids, userId);
  }

  async restoreProducts(ids: number[], userId: number): Promise<boolean> {
    return await this.productRepository.restore(ids, userId);
  }

  // Private helper methods

  private validateCreateInput(input: CreateProductInput): void {
    if (!input.name?.trim()) {
      throw new ProductValidationError('Product name is required');
    }

    if (!input.description?.trim()) {
      throw new ProductValidationError('Product description is required');
    }

    if (input.price < 0) {
      throw new ProductValidationError('Product price must be non-negative');
    }

    if (!input.currency?.trim()) {
      throw new ProductValidationError('Product currency is required');
    }

    if (!input.organizationId?.trim()) {
      throw new ProductValidationError('Organization ID is required');
    }
  }

  private validateUpdateInput(input: UpdateProductInput): void {
    if (input.name !== undefined && !input.name?.trim()) {
      throw new ProductValidationError('Product name cannot be empty');
    }

    if (input.price !== undefined && input.price < 0) {
      throw new ProductValidationError('Product price must be non-negative');
    }

    if (input.currency !== undefined && !input.currency?.trim()) {
      throw new ProductValidationError('Product currency cannot be empty');
    }
  }

  private async checkForDuplicates(input: CreateProductInput, userId: number): Promise<void> {
    // Check for SKU duplicates
    if (input.sku) {
      const existingProduct = await this.productRepository.findBySku(input.sku, input.organizationId);
      if (existingProduct) {
        throw new ProductValidationError(`Product with SKU '${input.sku}' already exists`);
      }
    }

    // Check for URL duplicates if importing
    if (input.originalUrl) {
      const duplicates = await this.productRepository.findDuplicates(input.organizationId, {
        originalUrl: input.originalUrl,
      });
      if (duplicates.length > 0) {
        throw new ProductValidationError(`Product from URL '${input.originalUrl}' already exists`);
      }
    }
  }

  private mapDetectedProductToCreateInput(
    detectedProduct: DetectedProduct,
    organizationId: string
  ): CreateProductInput {
    return {
      name: detectedProduct.name,
      productType: detectedProduct.type || 'General',
      brandName: detectedProduct.brandName,
      description: detectedProduct.description || '',
      price: detectedProduct.price || 0,
      currency: detectedProduct.currency || 'USD',
      availability: 'IN_STOCK',
      features: detectedProduct.features || [],
      targetAudience: '',
      usp: '',
      keywords: [],
      certifications: [],
      legalDisclaimers: [],
      images: detectedProduct.images?.map((img) => img.url) || [],
      videos: [],
      shippingOptions: [],
      returnPolicy: '',
      reviews: '',
      ratings: '',
      customerSupport: '',
      manufacturer: '',
      releaseDate: '',
      sku: detectedProduct.sku,
      originalUrl: detectedProduct.productUrl,
      metadata: detectedProduct.metadata,
      analysisStatus: AnalysisStatus.PENDING,
      organizationId,
    };
  }

  private async performProductAnalysis(product: Product): Promise<Omit<ProductAnalysisResult, 'analysisStatus'>> {
    // This would integrate with actual AI analysis services
    // For now, return a placeholder implementation
    return {
      productId: product.id,
      extractedData: {
        features: product.features,
        keywords: product.keywords,
        targetAudience: product.targetAudience,
        usp: product.usp,
        description: product.description,
      },
    };
  }

  private async generateBriefContent(product: Product, input: ProductBriefInput): Promise<string> {
    // This would integrate with AI services to generate the brief
    // For now, return a placeholder implementation
    const description = input.description || product.description;
    return `Product Brief for ${product.name}: ${description}`;
  }
}

// Service factory
export function createProductService(repository: IProductRepository): ProductService {
  return new ProductService(repository);
}
