/**
 * 🛍️ Product Domain Types
 *
 * @description Core types for the product domain
 * @responsibility Type definitions for product business logic
 * @dependencies None (pure types)
 * @ai_context Domain types for product feature
 */

import { Product, ProductStatus, AnalysisStatus } from '../entities/product.entity';

// Product Creation Types
export interface CreateProductInput {
  name: string;
  productType: string;
  brandName?: string;
  description: string;
  price: number;
  currency: string;
  availability: string;
  features: string[];
  systemRequirements?: string;
  dimensions?: string;
  format?: string;
  targetAudience: string;
  usp: string;
  keywords: string[];
  certifications: string[];
  legalDisclaimers: string[];
  images: string[];
  videos: string[];
  shippingOptions: string[];
  returnPolicy: string;
  reviews: string;
  ratings: string;
  customerSupport: string;
  manufacturer: string;
  releaseDate: string;
  sku?: string;
  originalUrl?: string;
  metadata?: Record<string, any>;
  analysisStatus?: AnalysisStatus;
  status?: ProductStatus;
  organizationId: string;
}

// Product Update Types
export interface UpdateProductInput {
  id: number;
  name?: string;
  productType?: string;
  brandName?: string;
  description?: string;
  price?: number;
  currency?: string;
  availability?: string;
  features?: string[];
  systemRequirements?: string;
  dimensions?: string;
  format?: string;
  targetAudience?: string;
  usp?: string;
  keywords?: string[];
  certifications?: string[];
  legalDisclaimers?: string[];
  images?: string[];
  videos?: string[];
  shippingOptions?: string[];
  returnPolicy?: string;
  reviews?: string;
  ratings?: string;
  customerSupport?: string;
  manufacturer?: string;
  releaseDate?: string;
  sku?: string;
  originalUrl?: string;
  metadata?: Record<string, any>;
  similarImages?: any;
  referenceFileId?: string;
  analysisStatus?: AnalysisStatus;
  status?: ProductStatus;
}

// Product Query Types
export interface ProductFilters {
  organizationId?: string;
  productType?: string;
  status?: ProductStatus;
  analysisStatus?: AnalysisStatus;
  searchQuery?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  availability?: string;
  brandName?: string;
}

export interface ProductSortOptions {
  field: 'name' | 'price' | 'createdAt' | 'updatedAt' | 'analysisCompletedAt';
  order: 'asc' | 'desc';
}

export interface ProductListOptions {
  filters?: ProductFilters;
  sort?: ProductSortOptions;
  pagination?: {
    page: number;
    limit: number;
  };
}

// Product Import Types
export interface DetectedProduct {
  id: number;
  name: string;
  description?: string;
  imageUrl?: string;
  productUrl?: string;
  confidence?: number;
  selected: boolean;
  brandName?: string;
  price?: number;
  currency?: string;
  type?: string;
  sku?: string;
  features?: string[];
  images?: Array<{
    url: string;
    selected: boolean;
  }>;
  metadata?: {
    productDetailsUrl?: string;
    cleanedHTML?: string;
    discoveredAt?: string;
  };
}

export type DiscoveryStage = 'fetching' | 'cleaning' | 'gemini' | 'haiku' | 'complete' | 'error';

export interface ImportProgress {
  stage: DiscoveryStage;
  message: string;
  progress: number;
  error?: string;
}

export interface ProductImportJob {
  id: string;
  url: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: ImportProgress;
  detectedProducts: DetectedProduct[];
  selectedProductIds: number[];
  createdAt: Date;
  completedAt?: Date;
  error?: string;
}

// Product Analysis Types
export interface ProductAnalysisResult {
  productId: number;
  analysisStatus: AnalysisStatus;
  extractedData?: {
    features: string[];
    keywords: string[];
    targetAudience: string;
    usp: string;
    description: string;
  };
  similarImages?: Array<{
    url: string;
    similarity: number;
    source: string;
  }>;
  error?: string;
}

// Product Brief Types
export interface ProductBrief {
  productId: number;
  brief: string;
  generatedAt: Date;
  version: number;
}

export interface ProductBriefInput {
  productId: number;
  description?: string;
  customPrompt?: string;
}

// Product Statistics Types
export interface ProductStatistics {
  totalProducts: number;
  activeProducts: number;
  pendingAnalysis: number;
  completedAnalysis: number;
  averagePrice: number;
  topCategories: Array<{
    category: string;
    count: number;
  }>;
  recentlyAdded: number;
}

// Product Search Types
export interface ProductSearchResult {
  products: Product[];
  totalCount: number;
  facets: {
    categories: Array<{ name: string; count: number }>;
    brands: Array<{ name: string; count: number }>;
    priceRanges: Array<{ range: string; count: number }>;
    availability: Array<{ status: string; count: number }>;
  };
}

// Product Image Types
export interface ProductImageUpload {
  file: File;
  productId: number;
  isPrimary?: boolean;
  alt?: string;
}

export interface ProductImageResult {
  url: string;
  thumbnailUrl?: string;
  alt?: string;
  size: number;
  dimensions?: {
    width: number;
    height: number;
  };
}

// Re-export entity types
export type { Product, ProductImage, ProductMetadata, SimilarImages } from '../entities/product.entity';
export { ProductStatus, AnalysisStatus } from '../entities/product.entity';
