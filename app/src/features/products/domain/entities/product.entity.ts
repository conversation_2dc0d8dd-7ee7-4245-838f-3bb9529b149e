/**
 * 🛍️ Product Domain Entity
 *
 * @description Core product business entity with domain logic
 * @responsibility Encapsulates product data and business rules
 * @dependencies None (pure domain entity)
 * @ai_context This is the central product entity for the domain layer
 */

export interface ProductImage {
  url: string;
  alt?: string;
  selected?: boolean;
}

export interface ProductMetadata {
  productDetailsUrl?: string;
  cleanedHTML?: string;
  discoveredAt?: string;
  importSource?: string;
  [key: string]: any;
}

export interface SimilarImages {
  images: Array<{
    url: string;
    similarity?: number;
    source?: string;
  }>;
  lastUpdated: string | null;
}

export enum ProductStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  ARCHIVED = 'ARCHIVED',
}

export enum AnalysisStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}

export interface Product {
  id: number;
  name: string;
  productType: string;
  brandName?: string;
  description: string;
  price: number;
  currency: string;
  availability: string;
  features: string[];
  systemRequirements?: string;
  dimensions?: string;
  format?: string;
  targetAudience: string;
  usp: string;
  keywords: string[];
  certifications: string[];
  legalDisclaimers: string[];
  images: string[];
  videos: string[];
  shippingOptions: string[];
  returnPolicy: string;
  reviews: string;
  ratings: string;
  customerSupport: string;
  manufacturer: string;
  releaseDate: string;
  sku: string;
  originalUrl?: string;
  metadata?: ProductMetadata;
  similarImages?: SimilarImages;
  referenceFileId?: string;
  analysisStatus: AnalysisStatus;
  analysisCompletedAt?: Date;
  status: ProductStatus;
  userId: number;
  organizationId: string;
  createdAt: Date;
  updatedAt: Date;
}

export class ProductEntity implements Product {
  constructor(
    public id: number,
    public name: string,
    public productType: string,
    public description: string,
    public price: number,
    public currency: string,
    public availability: string,
    public features: string[],
    public targetAudience: string,
    public usp: string,
    public keywords: string[],
    public certifications: string[],
    public legalDisclaimers: string[],
    public images: string[],
    public videos: string[],
    public shippingOptions: string[],
    public returnPolicy: string,
    public reviews: string,
    public ratings: string,
    public customerSupport: string,
    public manufacturer: string,
    public releaseDate: string,
    public sku: string,
    public analysisStatus: AnalysisStatus,
    public status: ProductStatus,
    public userId: number,
    public organizationId: string,
    public createdAt: Date,
    public updatedAt: Date,
    public brandName?: string,
    public systemRequirements?: string,
    public dimensions?: string,
    public format?: string,
    public originalUrl?: string,
    public metadata?: ProductMetadata,
    public similarImages?: SimilarImages,
    public referenceFileId?: string,
    public analysisCompletedAt?: Date
  ) {}

  /**
   * Check if product analysis is complete
   */
  isAnalysisComplete(): boolean {
    return this.analysisStatus === AnalysisStatus.COMPLETED;
  }

  /**
   * Check if product is active and available
   */
  isAvailable(): boolean {
    return this.status === ProductStatus.ACTIVE && this.availability !== 'OUT_OF_STOCK';
  }

  /**
   * Get primary product image
   */
  getPrimaryImage(): string | null {
    return this.images.length > 0 ? this.images[0] : null;
  }

  /**
   * Get formatted price with currency
   */
  getFormattedPrice(): string {
    return `${this.currency} ${this.price.toFixed(2)}`;
  }

  /**
   * Check if product has similar images
   */
  hasSimilarImages(): boolean {
    return !!(this.similarImages?.images && this.similarImages.images.length > 0);
  }

  /**
   * Get product confidence score (for imported products)
   */
  getConfidenceScore(): number | null {
    return this.metadata?.confidence || null;
  }

  /**
   * Update analysis status
   */
  updateAnalysisStatus(status: AnalysisStatus, completedAt?: Date): void {
    this.analysisStatus = status;
    if (status === AnalysisStatus.COMPLETED && completedAt) {
      this.analysisCompletedAt = completedAt;
    }
  }

  /**
   * Add similar images
   */
  addSimilarImages(images: Array<{ url: string; similarity?: number; source?: string }>): void {
    this.similarImages = {
      images,
      lastUpdated: new Date().toISOString(),
    };
  }

  /**
   * Validate product data
   */
  validate(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.name?.trim()) {
      errors.push('Product name is required');
    }

    if (!this.description?.trim()) {
      errors.push('Product description is required');
    }

    if (this.price < 0) {
      errors.push('Product price must be non-negative');
    }

    if (!this.currency?.trim()) {
      errors.push('Product currency is required');
    }

    if (!this.sku?.trim()) {
      errors.push('Product SKU is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

/**
 * Factory function to create a new Product entity
 */
export function createProduct(data: Partial<Product>): ProductEntity {
  const now = new Date();

  return new ProductEntity(
    data.id || 0,
    data.name || '',
    data.productType || '',
    data.description || '',
    data.price || 0,
    data.currency || 'USD',
    data.availability || 'IN_STOCK',
    data.features || [],
    data.targetAudience || '',
    data.usp || '',
    data.keywords || [],
    data.certifications || [],
    data.legalDisclaimers || [],
    data.images || [],
    data.videos || [],
    data.shippingOptions || [],
    data.returnPolicy || '',
    data.reviews || '',
    data.ratings || '',
    data.customerSupport || '',
    data.manufacturer || '',
    data.releaseDate || '',
    data.sku || '',
    data.analysisStatus || AnalysisStatus.PENDING,
    data.status || ProductStatus.DRAFT,
    data.userId || 0,
    data.organizationId || '',
    data.createdAt || now,
    data.updatedAt || now,
    data.brandName,
    data.systemRequirements,
    data.dimensions,
    data.format,
    data.originalUrl,
    data.metadata,
    data.similarImages,
    data.referenceFileId,
    data.analysisCompletedAt
  );
}
