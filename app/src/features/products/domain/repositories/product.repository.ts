/**
 * 🛍️ Product Repository Interface
 *
 * @description Repository interface for product data access
 * @responsibility Defines data access contract for products
 * @dependencies Product domain types
 * @ai_context Repository pattern interface for product data operations
 */

import { Product } from '../entities/product.entity';
import {
  CreateProductInput,
  UpdateProductInput,
  ProductListOptions,
  ProductSearchResult,
  ProductStatistics,
  ProductFilters,
} from '../types';

export interface IProductRepository {
  /**
   * Create a new product
   */
  create(input: CreateProductInput, userId: number): Promise<Product>;

  /**
   * Find product by ID
   */
  findById(id: number, userId?: number): Promise<Product | null>;

  /**
   * Find products with filters and pagination
   */
  findMany(options: ProductListOptions, userId?: number): Promise<Product[]>;

  /**
   * Search products with advanced filtering
   */
  search(options: ProductListOptions, userId?: number): Promise<ProductSearchResult>;

  /**
   * Update product by ID
   */
  update(id: number, input: UpdateProductInput, userId: number): Promise<Product>;

  /**
   * Delete product by ID
   */
  delete(id: number, userId: number): Promise<boolean>;

  /**
   * Count products with filters
   */
  count(filters: ProductFilters, userId?: number): Promise<number>;

  /**
   * Get product statistics
   */
  getStatistics(organizationId: string, userId?: number): Promise<ProductStatistics>;

  /**
   * Find products by organization
   */
  findByOrganization(organizationId: string, options?: ProductListOptions): Promise<Product[]>;

  /**
   * Find products by SKU
   */
  findBySku(sku: string, organizationId: string): Promise<Product | null>;

  /**
   * Find products by type
   */
  findByType(productType: string, organizationId: string, options?: ProductListOptions): Promise<Product[]>;

  /**
   * Find products with pending analysis
   */
  findPendingAnalysis(organizationId: string): Promise<Product[]>;

  /**
   * Update product analysis status
   */
  updateAnalysisStatus(id: number, status: string, completedAt?: Date): Promise<Product>;

  /**
   * Add similar images to product
   */
  addSimilarImages(id: number, images: any): Promise<Product>;

  /**
   * Update product images
   */
  updateImages(id: number, images: string[]): Promise<Product>;

  /**
   * Find products for model training
   */
  findForModelTraining(organizationId: string): Promise<Product[]>;

  /**
   * Bulk create products
   */
  bulkCreate(products: CreateProductInput[], userId: number): Promise<Product[]>;

  /**
   * Bulk update products
   */
  bulkUpdate(updates: Array<{ id: number; data: UpdateProductInput }>, userId: number): Promise<Product[]>;

  /**
   * Bulk delete products
   */
  bulkDelete(ids: number[], userId: number): Promise<boolean>;

  /**
   * Check if product exists
   */
  exists(id: number, userId?: number): Promise<boolean>;

  /**
   * Generate unique SKU
   */
  generateSku(name: string, userId: number): Promise<string>;

  /**
   * Find duplicate products
   */
  findDuplicates(
    organizationId: string,
    criteria: {
      name?: string;
      sku?: string;
      originalUrl?: string;
    }
  ): Promise<Product[]>;

  /**
   * Archive products
   */
  archive(ids: number[], userId: number): Promise<boolean>;

  /**
   * Restore archived products
   */
  restore(ids: number[], userId: number): Promise<boolean>;

  /**
   * Get recently viewed products
   */
  getRecentlyViewed(userId: number, limit?: number): Promise<Product[]>;

  /**
   * Track product view
   */
  trackView(productId: number, userId: number): Promise<void>;

  /**
   * Get popular products
   */
  getPopular(organizationId: string, limit?: number): Promise<Product[]>;

  /**
   * Find related products
   */
  findRelated(productId: number, limit?: number): Promise<Product[]>;

  /**
   * Update product metadata
   */
  updateMetadata(id: number, metadata: Record<string, any>): Promise<Product>;

  /**
   * Clear product metadata
   */
  clearMetadata(id: number, keys?: string[]): Promise<Product>;

  /**
   * Find products by metadata
   */
  findByMetadata(organizationId: string, metadata: Record<string, any>): Promise<Product[]>;
}

/**
 * Product repository options for configuration
 */
export interface ProductRepositoryOptions {
  enableCaching?: boolean;
  cacheTimeout?: number;
  enableAuditLog?: boolean;
  enableSoftDelete?: boolean;
  defaultPageSize?: number;
  maxPageSize?: number;
}

/**
 * Product repository error types
 */
export class ProductRepositoryError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ProductRepositoryError';
  }
}

export class ProductNotFoundError extends ProductRepositoryError {
  constructor(id: number) {
    super(`Product with ID ${id} not found`, 'PRODUCT_NOT_FOUND', { id });
  }
}

export class ProductValidationError extends ProductRepositoryError {
  constructor(message: string, details?: any) {
    super(message, 'PRODUCT_VALIDATION_ERROR', details);
  }
}

export class ProductDuplicateError extends ProductRepositoryError {
  constructor(field: string, value: string) {
    super(`Product with ${field} '${value}' already exists`, 'PRODUCT_DUPLICATE', { field, value });
  }
}

export class ProductPermissionError extends ProductRepositoryError {
  constructor(userId: number, productId: number) {
    super(`User ${userId} does not have permission to access product ${productId}`, 'PRODUCT_PERMISSION_DENIED', {
      userId,
      productId,
    });
  }
}
