/**
 * 📊 Dashboard Feature Module
 *
 * @description Main dashboard providing overview, quick actions, and insights
 * @responsibility Central hub for user activity and system status
 * @dependencies Brand kits, assets, audiences, products, cloud storage
 * @ai_context This is the main landing page after login - shows everything at a glance
 */

// Domain exports
export type {
  DashboardStats,
  RecentActivity as RecentActivityType,
  CloudStorageStatus,
  QuickAction,
  DashboardData,
  DashboardFilters,
} from './domain/types';
export * from './domain/constants';

// Infrastructure exports
export { getDashboardStats } from './infrastructure/wasp/queries/get-dashboard-stats.query';

// Presentation exports
export { default as DashboardPage } from './presentation/pages/DashboardPage';
export { OverviewStats } from './presentation/components/overview-stats';
export { QuickActions } from './presentation/components/quick-actions';
export { RecentActivity } from './presentation/components/recent-activity';
export { CloudStatus } from './presentation/components/cloud-status';
