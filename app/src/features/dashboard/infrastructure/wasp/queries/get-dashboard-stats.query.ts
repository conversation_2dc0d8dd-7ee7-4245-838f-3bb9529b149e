/**
 * 📊 Get Dashboard Stats Query
 */

import { GetDashboardStats } from 'wasp/server/operations';
import type { DashboardData, DashboardFilters } from '../../../domain/types';

export const getDashboardStats: GetDashboardStats<
  { organizationId: string; filters?: DashboardFilters },
  DashboardData
> = async (args, context) => {
  if (!context.user) {
    throw new Error('User must be authenticated');
  }

  const { organizationId, filters = { timeRange: '30d' } } = args;

  try {
    // Calculate date range
    const now = new Date();
    const daysBack = parseInt(filters.timeRange.replace('d', '')) || 30;
    const startDate = new Date(now.getTime() - daysBack * 24 * 60 * 60 * 1000);

    // Get basic stats
    const [
      totalAssets,
      totalBrandKits,
      totalAudiences,
      totalProducts,
      recentAssets,
      recentBrandKits,
      recentAudiences,
      recentProducts,
    ] = await Promise.all([
      // Total counts
      context.entities.Asset.count({
        where: { organizationId, deletedAt: null },
      }),
      context.entities.BrandKit.count({
        where: { organizationId, deletedAt: null },
      }),
      context.entities.Audience.count({
        where: { organizationId },
      }),
      context.entities.Product.count({
        where: { organizationId },
      }),

      // Recent activity
      context.entities.Asset.findMany({
        where: {
          organizationId,
          deletedAt: null,
          uploadedAt: { gte: startDate },
        },
        orderBy: { uploadedAt: 'desc' },
        take: 5,
        select: {
          id: true,
          fileName: true,
          fileUrl: true,
          uploadedAt: true,
          isGenerated: true,
        },
      }),
      context.entities.BrandKit.findMany({
        where: {
          organizationId,
          deletedAt: null,
          createdAt: { gte: startDate },
        },
        orderBy: { createdAt: 'desc' },
        take: 3,
        select: {
          id: true,
          name: true,
          createdAt: true,
        },
      }),
      context.entities.Audience.findMany({
        where: {
          organizationId,
        },
        orderBy: { id: 'desc' },
        take: 3,
        select: {
          id: true,
          personaName: true,
        },
      }),
      context.entities.Product.findMany({
        where: {
          organizationId,
        },
        orderBy: { id: 'desc' },
        take: 3,
        select: {
          id: true,
          name: true,
        },
      }),
    ]);

    // Calculate storage usage (simplified)
    const storageUsed = totalAssets * 1024 * 1024; // Rough estimate
    const storageLimit = 10 * 1024 * 1024 * 1024; // 10GB default

    // Build recent activity
    const recentActivity = [
      ...recentAssets.map((asset) => ({
        id: `asset-${asset.id}`,
        type: asset.isGenerated ? ('asset_generated' as const) : ('asset_upload' as const),
        title: asset.isGenerated ? 'Generated new asset' : 'Uploaded new asset',
        description: asset.fileName || 'Untitled asset',
        timestamp: asset.uploadedAt,
        metadata: {
          assetId: asset.id.toString(),
          thumbnailUrl: asset.fileUrl,
        },
      })),
      ...recentBrandKits.map((brandKit) => ({
        id: `brandkit-${brandKit.id}`,
        type: 'brand_kit_created' as const,
        title: 'Created brand kit',
        description: brandKit.name,
        timestamp: brandKit.createdAt,
        metadata: {
          brandKitId: brandKit.id,
        },
      })),
      ...recentAudiences.map((audience) => ({
        id: `audience-${audience.id}`,
        type: 'audience_generated' as const,
        title: 'Generated audience',
        description: audience.personaName || 'Unnamed audience',
        timestamp: new Date(), // Use current date since no createdAt field
        metadata: {
          audienceId: audience.id,
        },
      })),
      ...recentProducts.map((product) => ({
        id: `product-${product.id}`,
        type: 'product_added' as const,
        title: 'Added product',
        description: product.name,
        timestamp: new Date(), // Use current date since no createdAt field
        metadata: {
          productId: product.id.toString(),
        },
      })),
    ]
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, 10);

    // Mock cloud storage status (would be real in production)
    const cloudStorage = [
      {
        provider: 'google_drive' as const,
        isConnected: false,
        totalFiles: 0,
      },
      {
        provider: 'onedrive' as const,
        isConnected: false,
        totalFiles: 0,
      },
      {
        provider: 'dropbox' as const,
        isConnected: false,
        totalFiles: 0,
      },
    ];

    return {
      stats: {
        totalAssets,
        totalBrandKits,
        totalAudiences,
        totalProducts,
        storageUsed,
        storageLimit,
        recentUploads: recentAssets.length,
        generatedAssetsThisMonth: recentAssets.filter((a) => a.isGenerated).length,
      },
      recentActivity,
      cloudStorage,
      quickActions: [], // Will be populated on frontend
    };
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    throw new Error('Failed to fetch dashboard data');
  }
};
