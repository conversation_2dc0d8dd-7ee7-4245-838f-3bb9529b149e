import React from 'react';
import { useQuery, getDashboardStats, getCanvases, getSharedCanvases } from 'wasp/client/operations';
import { useOrganizationState } from '../../../../organization/store';
import { BarChart3, TrendingUp, ExternalLink, Calendar } from 'lucide-react';
import { ActiveSessionChecker } from '../../../onboarding/infrastructure/hooks/useActiveSessionRedirect';
import { SharedCanvasSection } from '../../../canvas/presentation/components/sharing/SharedCanvasCard';

const DashboardPage: React.FC = () => {
  // Get organization ID from organization state
  const { selectedOrganizationId } = useOrganizationState();
  const {
    data: dashboardData,
    isLoading,
    error,
  } = useQuery(
    getDashboardStats,
    {
      organizationId: selectedOrganizationId!,
      filters: { timeRange: '30d' },
    },
    { enabled: !!selectedOrganizationId }
  );

  // Fetch recent canvas projects
  const { data: canvasProjects, isLoading: canvasLoading } = useQuery(
    getCanvases,
    { organizationId: selectedOrganizationId },
    { enabled: !!selectedOrganizationId }
  );

  // Fetch shared canvases
  const {
    data: sharedCanvases,
    isLoading: sharedCanvasesLoading,
  } = useQuery(
    getSharedCanvases,
    {},
    { enabled: !!selectedOrganizationId }
  );

  // Get 3 most recent projects
  const recentProjects =
    canvasProjects?.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()).slice(0, 3) || [];

  // Transform shared canvases to match expected interface
  const transformedSharedCanvases = (sharedCanvases || []).map((sharedCanvas: any) => ({
    id: sharedCanvas.id,
    canvas: {
      id: sharedCanvas.canvasId || sharedCanvas.canvas?.id,
      name: sharedCanvas.canvas?.name || 'Untitled Canvas',
      description: sharedCanvas.canvas?.description,
      thumbnail: sharedCanvas.canvas?.thumbnail,
      updatedAt: sharedCanvas.canvas?.updatedAt || sharedCanvas.sharedAt,
    },
    permission: sharedCanvas.permission,
    sharedBy: {
      username: sharedCanvas.sharedBy?.username || 'Unknown User',
      email: sharedCanvas.sharedBy?.email,
    },
    sharedAt: sharedCanvas.sharedAt,
    accessCount: sharedCanvas.accessCount || 0,
    lastAccessedAt: sharedCanvas.lastAccessedAt,
  }));

  if (!selectedOrganizationId) {
    return (
      <div className='min-h-screen bg-[#F0EFE9] dark:bg-black flex items-center justify-center'>
        <div className='text-center'>
          <h2 className='text-2xl font-bold text-[#1F2419] dark:text-white mb-4'>No Organization Found</h2>
          <p className='text-[#676D50] dark:text-gray-400'>Please join an organization to access the dashboard.</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='min-h-screen bg-[#F0EFE9] dark:bg-black p-6'>
        <div className='max-w-7xl mx-auto'>
          <div className='bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6'>
            <h2 className='text-lg font-semibold text-red-800 dark:text-red-200 mb-2'>Failed to load dashboard</h2>
            <p className='text-red-600 dark:text-red-300'>{error.message || 'An unexpected error occurred'}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <ActiveSessionChecker>
      <div className='min-h-screen bg-[#F0EFE9] dark:bg-black text-[#1F2419] dark:text-white font-sans'>
        {/* Main Content - Dynamic responsive width */}
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12'>
          {/* Header */}
          <div className='mb-8 sm:mb-12'>
            <h1 className='font-display text-3xl sm:text-4xl lg:text-5xl font-bold text-[#676D50] dark:text-white mb-3 sm:mb-4'>
              Dashboard
            </h1>
            <p className='text-[#676D50]/70 dark:text-gray-400 text-base sm:text-lg leading-relaxed max-w-3xl'>
              Welcome back! Here's an overview of your creative productivity.
            </p>
          </div>

          {/* KPI Tiles Row */}
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6'>
            {/* Designs Generated */}
            <div className='bg-white dark:bg-gray-800 rounded-2xl p-6 border border-[#E8E4D4] dark:border-gray-700 hover:shadow-xl hover:shadow-[#676D50]/10 transition-all duration-300'>
              <div className='flex items-center justify-between mb-4'>
                <div className='w-14 h-14 bg-gradient-to-br from-[#676D50] to-[#849068] rounded-2xl flex items-center justify-center shadow-lg'>
                  <BarChart3 className='w-7 h-7 text-white' />
                </div>
                <div className='text-right'>
                  <div className='text-3xl font-bold text-[#676D50] dark:text-white font-display'>
                    {isLoading ? '...' : '1,247'}
                  </div>
                </div>
              </div>
              <div>
                <h3 className='text-sm font-medium text-[#676D50]/80 dark:text-gray-400 mb-1 uppercase tracking-wide'>
                  Designs Generated
                </h3>
                <p className='text-xs text-[#676D50]/60 dark:text-gray-500'>+89 this month</p>
              </div>
            </div>

            {/* Hours Saved */}
            <div className='bg-white dark:bg-gray-800 rounded-2xl p-6 border border-[#E8E4D4] dark:border-gray-700 hover:shadow-xl hover:shadow-[#676D50]/10 transition-all duration-300'>
              <div className='flex items-center justify-between mb-4'>
                <div className='w-14 h-14 bg-gradient-to-br from-[#849068] to-[#B5B178] rounded-2xl flex items-center justify-center shadow-lg'>
                  <TrendingUp className='w-7 h-7 text-white' />
                </div>
                <div className='text-right'>
                  <div className='text-3xl font-bold text-[#676D50] dark:text-white font-display'>
                    {isLoading ? '...' : '342'}
                  </div>
                </div>
              </div>
              <div>
                <h3 className='text-sm font-medium text-[#676D50]/80 dark:text-gray-400 mb-1 uppercase tracking-wide'>
                  Hours Saved
                </h3>
                <p className='text-xs text-[#676D50]/60 dark:text-gray-500'>vs manual design</p>
              </div>
            </div>

            {/* Assets Stored */}
            <div className='bg-white dark:bg-gray-800 rounded-2xl p-6 border border-[#E8E4D4] dark:border-gray-700 hover:shadow-xl hover:shadow-[#676D50]/10 transition-all duration-300'>
              <div className='flex items-center justify-between mb-4'>
                <div className='w-14 h-14 bg-gradient-to-br from-[#B5B178] to-[#676D50] rounded-2xl flex items-center justify-center shadow-lg'>
                  <BarChart3 className='w-7 h-7 text-white' />
                </div>
                <div className='text-right'>
                  <div className='text-3xl font-bold text-[#676D50] dark:text-white font-display'>
                    {isLoading ? '...' : dashboardData?.stats?.totalAssets?.toLocaleString() || '156'}
                  </div>
                </div>
              </div>
              <div>
                <h3 className='text-sm font-medium text-[#676D50]/80 dark:text-gray-400 mb-1 uppercase tracking-wide'>
                  Assets Stored
                </h3>
                <p className='text-xs text-[#676D50]/60 dark:text-gray-500'>
                  +{dashboardData?.stats?.recentUploads || '12'} this month
                </p>
              </div>
            </div>
          </div>

          {/* Recent Projects Section */}
          <div className='mt-8 sm:mt-12'>
            <div className='flex flex-col sm:flex-row sm:items-center justify-between mb-4 sm:mb-6 gap-2'>
              <h2 className='font-display text-xl sm:text-2xl lg:text-3xl font-semibold text-[#676D50] dark:text-white'>
                Recent Projects
              </h2>
              <a
                href='/canvas/projects'
                className='text-[#676D50] hover:text-[#849068] dark:text-gray-400 dark:hover:text-white transition-colors text-sm font-medium flex items-center gap-1 self-start sm:self-auto'
              >
                View all
                <ExternalLink size={14} />
              </a>
            </div>

            {canvasLoading ? (
              <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6'>
                {[1, 2, 3].map((i) => (
                  <div
                    key={i}
                    className='bg-white dark:bg-gray-800 rounded-xl p-4 border border-[#E8E4D4] dark:border-gray-700 animate-pulse'
                  >
                    <div className='aspect-[4/3] bg-gray-200 dark:bg-gray-700 rounded-lg mb-3'></div>
                    <div className='h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2'></div>
                    <div className='h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3'></div>
                  </div>
                ))}
              </div>
            ) : recentProjects.length > 0 ? (
              <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6'>
                {recentProjects.map((project) => (
                  <a
                    key={project.id}
                    href={`/canvas/${project.id}`}
                    className='group bg-white dark:bg-gray-800 rounded-xl border border-[#E8E4D4] dark:border-gray-700 overflow-hidden hover:shadow-lg hover:shadow-[#676D50]/10 transition-all duration-300 hover:border-[#676D50]/30'
                  >
                    {/* Project Thumbnail */}
                    <div className='aspect-[4/3] bg-gradient-to-br from-[#676D50]/10 to-[#849068]/10 overflow-hidden'>
                      {project.thumbnail ? (
                        <img
                          src={project.thumbnail}
                          alt={project.name}
                          className='w-full h-full object-cover group-hover:scale-105 transition-transform duration-300'
                        />
                      ) : (
                        <div className='w-full h-full flex items-center justify-center'>
                          <span className='text-4xl'>🎨</span>
                        </div>
                      )}
                    </div>

                    {/* Project Info */}
                    <div className='p-4'>
                      <h3 className='font-medium text-[#676D50] dark:text-white mb-1 line-clamp-1 group-hover:text-[#849068] transition-colors'>
                        {project.name}
                      </h3>
                      {project.description && (
                        <p className='text-sm text-[#676D50]/70 dark:text-gray-400 mb-2 line-clamp-2'>
                          {project.description}
                        </p>
                      )}
                      <div className='flex items-center gap-1 text-xs text-[#676D50]/60 dark:text-gray-500'>
                        <Calendar size={12} />
                        <span>
                          {new Date(project.updatedAt).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric',
                            year:
                              new Date(project.updatedAt).getFullYear() !== new Date().getFullYear()
                                ? 'numeric'
                                : undefined,
                          })}
                        </span>
                      </div>
                    </div>
                  </a>
                ))}
              </div>
            ) : (
              <div className='text-center py-8'>
                <div className='w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-[#676D50]/10 to-[#849068]/10 rounded-2xl flex items-center justify-center'>
                  <span className='text-2xl'>🎨</span>
                </div>
                <h3 className='font-medium text-[#676D50] dark:text-white mb-2'>No projects yet</h3>
                <p className='text-[#676D50]/70 dark:text-gray-400 text-sm mb-4'>
                  Create your first canvas project to get started
                </p>
                <a
                  href='/canvas/projects'
                  className='inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-[#676D50] to-[#849068] hover:from-[#849068] hover:to-[#676D50] text-white rounded-lg transition-all duration-300 font-medium text-sm shadow-sm hover:shadow-md transform hover:scale-[1.02]'
                >
                  Create Project
                  <ExternalLink size={14} />
                </a>
              </div>
            )}
          </div>

          {/* Shared Canvases Section */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl border border-[#E8E4D4] dark:border-gray-700 p-6 sm:p-8">
            <SharedCanvasSection
              sharedCanvases={transformedSharedCanvases}
              onCanvasClick={(canvasId) => window.location.href = `/canvas/${canvasId}`}
              isLoading={sharedCanvasesLoading}
            />
          </div>
        </div>
      </div>
    </ActiveSessionChecker>
  );
};

export default DashboardPage;
