import React from 'react';
import { Link } from 'wasp/client/router';
import { Clock, Upload, Sparkles, Palette, Users, Package, ExternalLink } from 'lucide-react';
import type { RecentActivity as RecentActivityType } from '../../../domain/types';

interface RecentActivityProps {
  activities: RecentActivityType[];
  isLoading?: boolean;
}

export const RecentActivity: React.FC<RecentActivityProps> = ({ activities, isLoading }) => {
  const getActivityIcon = (type: RecentActivityType['type']) => {
    switch (type) {
      case 'asset_upload':
        return Upload;
      case 'asset_generated':
        return Sparkles;
      case 'brand_kit_created':
        return Palette;
      case 'audience_generated':
        return Users;
      case 'product_added':
        return Package;
      default:
        return Clock;
    }
  };

  const getActivityColor = (type: RecentActivityType['type']) => {
    switch (type) {
      case 'asset_upload':
        return 'bg-gradient-to-br from-[#676D50] to-[#849068]';
      case 'asset_generated':
        return 'bg-gradient-to-br from-[#849068] to-[#B5B178]';
      case 'brand_kit_created':
        return 'bg-gradient-to-br from-[#B5B178] to-[#676D50]';
      case 'audience_generated':
        return 'bg-gradient-to-br from-[#676D50]/80 to-[#849068]/80';
      case 'product_added':
        return 'bg-gradient-to-br from-[#849068]/90 to-[#B5B178]/90';
      default:
        return 'bg-gradient-to-br from-[#676D50]/60 to-[#849068]/60';
    }
  };

  const getActivityLink = (activity: RecentActivityType) => {
    const { type, metadata } = activity;

    switch (type) {
      case 'asset_upload':
      case 'asset_generated':
        return metadata?.assetId ? `/assets/${metadata.assetId}` : '/assets';
      case 'brand_kit_created':
        return metadata?.brandKitId ? `/brand-kits/${metadata.brandKitId}` : '/brand-kits';
      case 'audience_generated':
        return metadata?.audienceId ? `/audiences/${metadata.audienceId}` : '/audiences';
      case 'product_added':
        return metadata?.productId ? `/products/${metadata.productId}` : '/products';
      default:
        return '#';
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;

    return date.toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className='bg-white dark:bg-gray-800 rounded-2xl p-6 border border-[#E8E4D4] dark:border-gray-700 shadow-sm'>
        <h2 className='font-display text-2xl font-semibold text-[#676D50] dark:text-white mb-6'>Recent Activity</h2>
        <div className='space-y-4'>
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className='flex items-center space-x-4 animate-pulse'>
              <div className='w-12 h-12 bg-[#F6F3E5] dark:bg-gray-700 rounded-2xl'></div>
              <div className='flex-1'>
                <div className='w-32 h-4 bg-[#F6F3E5] dark:bg-gray-700 rounded mb-2'></div>
                <div className='w-48 h-3 bg-[#F6F3E5] dark:bg-gray-700 rounded'></div>
              </div>
              <div className='w-16 h-3 bg-[#F6F3E5] dark:bg-gray-700 rounded'></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className='bg-white dark:bg-gray-800 rounded-2xl p-6 border border-[#E8E4D4] dark:border-gray-700 shadow-sm'>
      <div className='flex items-center justify-between mb-6'>
        <h2 className='font-display text-2xl font-semibold text-[#676D50] dark:text-white'>Recent Activity</h2>
        <a
          href='/dashboard/activity'
          className='text-sm text-[#676D50] dark:text-blue-400 hover:text-[#849068] dark:hover:text-blue-300 flex items-center transition-colors'
        >
          View all
          <ExternalLink className='w-3 h-3 ml-1' />
        </a>
      </div>

      {activities.length === 0 ? (
        <div className='text-center py-12'>
          <div className='w-20 h-20 bg-gradient-to-br from-[#676D50]/10 to-[#849068]/10 rounded-3xl flex items-center justify-center mx-auto mb-4'>
            <Clock className='w-10 h-10 text-[#676D50]/40 dark:text-gray-600' />
          </div>
          <p className='text-[#676D50]/70 dark:text-gray-400 font-medium'>No recent activity</p>
          <p className='text-sm text-[#676D50]/50 dark:text-gray-500 mt-2'>Start creating to see your activity here</p>
        </div>
      ) : (
        <div className='space-y-4'>
          {activities.map((activity) => {
            const Icon = getActivityIcon(activity.type);
            const colorClass = getActivityColor(activity.type);
            const link = getActivityLink(activity);

            return (
              <a
                key={activity.id}
                href={link}
                className='flex items-center space-x-4 p-4 rounded-2xl hover:bg-[#F6F3E5]/50 dark:hover:bg-gray-700/50 transition-all duration-300 group hover:shadow-sm'
              >
                <div
                  className={`w-12 h-12 ${colorClass} rounded-2xl flex items-center justify-center flex-shrink-0 shadow-sm`}
                >
                  <Icon className='w-6 h-6 text-white' />
                </div>

                <div className='flex-1 min-w-0'>
                  <p className='text-sm font-medium text-[#676D50] dark:text-white group-hover:text-[#849068] dark:group-hover:text-blue-400 transition-colors'>
                    {activity.title}
                  </p>
                  <p className='text-sm text-[#676D50]/60 dark:text-gray-400 truncate'>{activity.description}</p>
                </div>

                <div className='flex items-center space-x-3 flex-shrink-0'>
                  {activity.metadata?.thumbnailUrl && (
                    <img
                      src={activity.metadata.thumbnailUrl}
                      alt=''
                      className='w-10 h-10 rounded-xl object-cover border border-[#E8E4D4]'
                    />
                  )}
                  <span className='text-xs text-[#676D50]/50 dark:text-gray-400 font-medium'>
                    {formatTimeAgo(activity.timestamp)}
                  </span>
                </div>
              </a>
            );
          })}
        </div>
      )}
    </div>
  );
};
