import React from 'react';
import { Link } from 'wasp/client/router';
import { Cloud, CheckCircle, XCircle, AlertCircle, ExternalLink } from 'lucide-react';
import type { CloudStorageStatus } from '../../../domain/types';

interface CloudStatusProps {
  cloudStorage: CloudStorageStatus[];
  isLoading?: boolean;
}

export const CloudStatus: React.FC<CloudStatusProps> = ({ cloudStorage, isLoading }) => {
  const getProviderName = (provider: CloudStorageStatus['provider']) => {
    switch (provider) {
      case 'google_drive':
        return 'Google Drive';
      case 'onedrive':
        return 'OneDrive';
      case 'dropbox':
        return 'Dropbox';
      default:
        return provider;
    }
  };

  const getProviderIcon = (provider: CloudStorageStatus['provider']) => {
    // Using emoji for now, could be replaced with actual provider icons
    switch (provider) {
      case 'google_drive':
        return '📁';
      case 'onedrive':
        return '☁️';
      case 'dropbox':
        return '📦';
      default:
        return '💾';
    }
  };

  const getStatusIcon = (status: CloudStorageStatus) => {
    if (status.error) {
      return <AlertCircle className='w-5 h-5 text-red-500' />;
    }
    if (status.isConnected) {
      return <CheckCircle className='w-5 h-5 text-green-500' />;
    }
    return <XCircle className='w-5 h-5 text-gray-400' />;
  };

  const getStatusText = (status: CloudStorageStatus) => {
    if (status.error) {
      return 'Error';
    }
    if (status.isConnected) {
      return 'Connected';
    }
    return 'Not connected';
  };

  const getStatusColor = (status: CloudStorageStatus) => {
    if (status.error) {
      return 'text-red-600 dark:text-red-400';
    }
    if (status.isConnected) {
      return 'text-green-600 dark:text-green-400';
    }
    return 'text-gray-500 dark:text-gray-400';
  };

  const connectedCount = cloudStorage.filter((cs) => cs.isConnected).length;
  const totalCount = cloudStorage.length;

  if (isLoading) {
    return (
      <div className='bg-white dark:bg-gray-800 rounded-2xl p-6 border border-[#E8E4D4] dark:border-gray-700 shadow-sm'>
        <h2 className='font-display text-2xl font-semibold text-[#676D50] dark:text-white mb-6'>Cloud Storage</h2>
        <div className='space-y-4'>
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className='flex items-center justify-between animate-pulse'>
              <div className='flex items-center space-x-3'>
                <div className='w-12 h-12 bg-[#F6F3E5] dark:bg-gray-700 rounded-2xl'></div>
                <div>
                  <div className='w-24 h-4 bg-[#F6F3E5] dark:bg-gray-700 rounded mb-1'></div>
                  <div className='w-16 h-3 bg-[#F6F3E5] dark:bg-gray-700 rounded'></div>
                </div>
              </div>
              <div className='w-5 h-5 bg-[#F6F3E5] dark:bg-gray-700 rounded-full'></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className='bg-white dark:bg-gray-800 rounded-2xl p-6 border border-[#E8E4D4] dark:border-gray-700 shadow-sm'>
      <div className='flex items-center justify-between mb-6'>
        <div>
          <h2 className='font-display text-2xl font-semibold text-[#676D50] dark:text-white'>Cloud Storage</h2>
          <p className='text-sm text-[#676D50]/70 dark:text-gray-400 mt-1'>
            {connectedCount} of {totalCount} services connected
          </p>
        </div>
        <a
          href='/assets?tab=cloud'
          className='text-sm text-[#676D50] dark:text-blue-400 hover:text-[#849068] dark:hover:text-blue-300 flex items-center transition-colors'
        >
          Manage
          <ExternalLink className='w-3 h-3 ml-1' />
        </a>
      </div>

      <div className='space-y-4'>
        {cloudStorage.map((storage) => (
          <div
            key={storage.provider}
            className='flex items-center justify-between p-4 rounded-2xl border border-[#E8E4D4] dark:border-gray-700 hover:bg-[#F6F3E5]/30 dark:hover:bg-gray-700/50 transition-all duration-300 hover:shadow-sm'
          >
            <div className='flex items-center space-x-4'>
              <div className='w-12 h-12 bg-gradient-to-br from-[#676D50]/10 to-[#849068]/10 dark:bg-gray-700 rounded-2xl flex items-center justify-center'>
                <span className='text-xl'>{getProviderIcon(storage.provider)}</span>
              </div>
              <div>
                <h3 className='font-medium text-[#676D50] dark:text-white'>{getProviderName(storage.provider)}</h3>
                <div className='flex items-center space-x-2 text-sm'>
                  <span className={getStatusColor(storage)}>{getStatusText(storage)}</span>
                  {storage.isConnected && storage.totalFiles !== undefined && (
                    <>
                      <span className='text-[#676D50]/30'>•</span>
                      <span className='text-[#676D50]/60 dark:text-gray-400'>
                        {storage.totalFiles.toLocaleString()} files
                      </span>
                    </>
                  )}
                </div>
                {storage.error && <p className='text-xs text-red-600 dark:text-red-400 mt-1'>{storage.error}</p>}
                {storage.isConnected && storage.lastSync && (
                  <p className='text-xs text-[#676D50]/50 dark:text-gray-400 mt-1'>
                    Last sync: {storage.lastSync.toLocaleDateString()}
                  </p>
                )}
              </div>
            </div>

            <div className='flex items-center space-x-2'>
              {getStatusIcon(storage)}
              {!storage.isConnected && (
                <a
                  href={`/assets?tab=cloud&connect=${storage.provider}`}
                  className='text-xs bg-gradient-to-r from-[#676D50] to-[#849068] text-white px-4 py-2 rounded-full hover:from-[#849068] hover:to-[#676D50] transition-all duration-300 font-medium shadow-sm hover:shadow-md'
                >
                  Connect
                </a>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Overall status indicator */}
      <div className='mt-6 pt-6 border-t border-[#E8E4D4] dark:border-gray-700'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center space-x-3'>
            <div className='w-8 h-8 bg-gradient-to-br from-[#676D50]/10 to-[#849068]/10 rounded-xl flex items-center justify-center'>
              <Cloud className='w-4 h-4 text-[#676D50]' />
            </div>
            <span className='text-sm text-[#676D50]/70 dark:text-gray-400 font-medium'>Overall Status</span>
          </div>
          <div className='flex items-center space-x-3'>
            {connectedCount === totalCount ? (
              <CheckCircle className='w-5 h-5 text-[#849068]' />
            ) : connectedCount > 0 ? (
              <AlertCircle className='w-5 h-5 text-yellow-500' />
            ) : (
              <XCircle className='w-5 h-5 text-[#676D50]/40' />
            )}
            <span className='text-sm font-medium text-[#676D50] dark:text-white'>
              {connectedCount === totalCount
                ? 'All Connected'
                : connectedCount > 0
                  ? 'Partially Connected'
                  : 'Not Connected'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
