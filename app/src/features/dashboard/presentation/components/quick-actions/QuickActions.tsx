import React from 'react';
import { Link } from 'wasp/client/router';
import { Plus, ArrowRight } from 'lucide-react';
import type { QuickAction } from '../../../domain/types';
import { DEFAULT_QUICK_ACTIONS } from '../../../domain/constants';

interface QuickActionsProps {
  actions?: QuickAction[];
  onActionClick?: (actionId: string) => void;
}

export const QuickActions: React.FC<QuickActionsProps> = ({ actions = DEFAULT_QUICK_ACTIONS, onActionClick }) => {
  const handleActionClick = (action: QuickAction) => {
    if (action.onClick) {
      action.onClick();
    }
    if (onActionClick) {
      onActionClick(action.id);
    }
  };

  return (
    <div className='bg-white dark:bg-gray-800 rounded-2xl p-8 border border-[#E8E4D4] dark:border-gray-700 shadow-sm'>
      <div className='flex items-center justify-between mb-8'>
        <h2 className='font-display text-2xl font-semibold text-[#676D50] dark:text-white'>Quick Actions</h2>
        <div className='w-10 h-10 bg-gradient-to-br from-[#676D50]/10 to-[#849068]/10 rounded-2xl flex items-center justify-center'>
          <Plus className='w-5 h-5 text-[#676D50]' />
        </div>
      </div>

      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
        {actions.map((action) => {
          if (action.href) {
            return (
              <a
                key={action.id}
                href={action.href}
                className={`
                  group relative p-6 rounded-2xl border-2 border-dashed border-[#E8E4D4] dark:border-gray-700
                  hover:border-[#B5B178] dark:hover:border-gray-600 hover:bg-[#F6F3E5]/50 dark:hover:bg-gray-700/50
                  transition-all duration-300 text-left block hover:shadow-lg hover:shadow-[#676D50]/5
                  ${action.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:scale-105'}
                `}
              >
                <div className='flex items-start justify-between'>
                  <div className='flex-1'>
                    <div className='flex items-center mb-3'>
                      <div className='w-12 h-12 bg-gradient-to-br from-[#676D50]/10 to-[#849068]/10 rounded-2xl flex items-center justify-center mr-4'>
                        <span className='text-2xl'>{action.icon}</span>
                      </div>
                      <h3 className='font-display font-semibold text-[#676D50] dark:text-white group-hover:text-[#849068] dark:group-hover:text-gray-200 text-lg'>
                        {action.title}
                      </h3>
                    </div>
                    <p className='text-sm text-[#676D50]/70 dark:text-gray-400 leading-relaxed ml-16'>
                      {action.description}
                    </p>
                  </div>
                  <ArrowRight className='w-5 h-5 text-[#676D50]/40 group-hover:text-[#849068] dark:group-hover:text-gray-300 transition-all duration-300 ml-2 flex-shrink-0 group-hover:translate-x-1' />
                </div>

                {/* Colored accent */}
                <div className='absolute top-0 left-0 w-2 h-full bg-gradient-to-b from-[#676D50] to-[#849068] rounded-l-2xl opacity-0 group-hover:opacity-100 transition-all duration-300'></div>
              </a>
            );
          } else {
            return (
              <button
                key={action.id}
                onClick={() => handleActionClick(action)}
                disabled={action.disabled}
                className={`
                  group relative p-6 rounded-2xl border-2 border-dashed border-[#E8E4D4] dark:border-gray-700
                  hover:border-[#B5B178] dark:hover:border-gray-600 hover:bg-[#F6F3E5]/50 dark:hover:bg-gray-700/50
                  transition-all duration-300 text-left w-full hover:shadow-lg hover:shadow-[#676D50]/5
                  ${action.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:scale-105'}
                `}
              >
                <div className='flex items-start justify-between'>
                  <div className='flex-1'>
                    <div className='flex items-center mb-3'>
                      <div className='w-12 h-12 bg-gradient-to-br from-[#676D50]/10 to-[#849068]/10 rounded-2xl flex items-center justify-center mr-4'>
                        <span className='text-2xl'>{action.icon}</span>
                      </div>
                      <h3 className='font-display font-semibold text-[#676D50] dark:text-white group-hover:text-[#849068] dark:group-hover:text-gray-200 text-lg'>
                        {action.title}
                      </h3>
                    </div>
                    <p className='text-sm text-[#676D50]/70 dark:text-gray-400 leading-relaxed ml-16'>
                      {action.description}
                    </p>
                  </div>
                  <ArrowRight className='w-5 h-5 text-[#676D50]/40 group-hover:text-[#849068] dark:group-hover:text-gray-300 transition-all duration-300 ml-2 flex-shrink-0 group-hover:translate-x-1' />
                </div>

                {/* Colored accent */}
                <div className='absolute top-0 left-0 w-2 h-full bg-gradient-to-b from-[#676D50] to-[#849068] rounded-l-2xl opacity-0 group-hover:opacity-100 transition-all duration-300'></div>
              </button>
            );
          }
        })}
      </div>

      {/* Custom action hint */}
      <div className='mt-8 pt-6 border-t border-[#E8E4D4] dark:border-gray-700'>
        <div className='text-center'>
          <div className='inline-flex items-center px-4 py-2 bg-gradient-to-r from-[#676D50]/5 to-[#849068]/5 rounded-full'>
            <span className='text-lg mr-2'>💡</span>
            <p className='text-xs text-[#676D50]/70 dark:text-gray-400'>
              These actions adapt based on your current workflow and progress
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
