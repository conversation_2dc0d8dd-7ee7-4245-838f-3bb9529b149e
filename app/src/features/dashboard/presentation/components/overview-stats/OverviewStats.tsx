import React from 'react';
import { Bar<PERSON>hart3, Users, Palette, Package, HardDrive, TrendingUp } from 'lucide-react';
import type { DashboardStats } from '../../../domain/types';

interface OverviewStatsProps {
  stats: DashboardStats;
  isLoading?: boolean;
}

export const OverviewStats: React.FC<OverviewStatsProps> = ({ stats, isLoading }) => {
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const storagePercentage = (stats.storageUsed / stats.storageLimit) * 100;

  const statCards = [
    {
      title: 'Total Assets',
      value: stats.totalAssets.toLocaleString(),
      icon: BarChart3,
      color: 'bg-gradient-to-br from-[#676D50] to-[#849068]',
      change: `+${stats.recentUploads} this month`,
    },
    {
      title: 'Brand Kits',
      value: stats.totalBrandKits.toLocaleString(),
      icon: Palette,
      color: 'bg-gradient-to-br from-[#849068] to-[#B5B178]',
      change: 'Active brand systems',
    },
    {
      title: 'Audiences',
      value: stats.totalAudiences.toLocaleString(),
      icon: Users,
      color: 'bg-gradient-to-br from-[#B5B178] to-[#676D50]',
      change: 'Target personas',
    },
    {
      title: 'Products',
      value: stats.totalProducts.toLocaleString(),
      icon: Package,
      color: 'bg-gradient-to-br from-[#676D50]/80 to-[#849068]/80',
      change: 'In catalog',
    },
    {
      title: 'AI Generated',
      value: stats.generatedAssetsThisMonth.toLocaleString(),
      icon: TrendingUp,
      color: 'bg-gradient-to-br from-[#849068]/90 to-[#B5B178]/90',
      change: 'This month',
    },
    {
      title: 'Storage Used',
      value: formatBytes(stats.storageUsed),
      icon: HardDrive,
      color: 'bg-gradient-to-br from-[#B5B178]/70 to-[#676D50]/70',
      change: `${storagePercentage.toFixed(1)}% of ${formatBytes(stats.storageLimit)}`,
    },
  ];

  if (isLoading) {
    return (
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
        {Array.from({ length: 6 }).map((_, i) => (
          <div
            key={i}
            className='bg-white dark:bg-gray-800 rounded-2xl p-6 border border-[#E8E4D4] dark:border-gray-700 shadow-sm'
          >
            <div className='animate-pulse'>
              <div className='flex items-center justify-between mb-4'>
                <div className='w-12 h-12 bg-[#F6F3E5] dark:bg-gray-700 rounded-2xl'></div>
                <div className='w-16 h-4 bg-[#F6F3E5] dark:bg-gray-700 rounded'></div>
              </div>
              <div className='w-20 h-8 bg-[#F6F3E5] dark:bg-gray-700 rounded mb-2'></div>
              <div className='w-24 h-3 bg-[#F6F3E5] dark:bg-gray-700 rounded'></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
      {statCards.map((card) => {
        const Icon = card.icon;
        return (
          <div
            key={card.title}
            className='bg-white dark:bg-gray-800 rounded-2xl p-6 border border-[#E8E4D4] dark:border-gray-700 hover:shadow-xl hover:shadow-[#676D50]/10 transition-all duration-300 hover:scale-105'
          >
            <div className='flex items-center justify-between mb-4'>
              <div className={`w-14 h-14 ${card.color} rounded-2xl flex items-center justify-center shadow-lg`}>
                <Icon className='w-7 h-7 text-white' />
              </div>
              <div className='text-right'>
                <div className='text-3xl font-bold text-[#676D50] dark:text-white font-display'>{card.value}</div>
              </div>
            </div>
            <div>
              <h3 className='text-sm font-medium text-[#676D50]/80 dark:text-gray-400 mb-1 uppercase tracking-wide'>
                {card.title}
              </h3>
              <p className='text-xs text-[#676D50]/60 dark:text-gray-500'>{card.change}</p>
            </div>

            {/* Storage progress bar */}
            {card.title === 'Storage Used' && (
              <div className='mt-4'>
                <div className='w-full bg-[#F6F3E5] dark:bg-gray-700 rounded-full h-3 shadow-inner'>
                  <div
                    className={`h-3 rounded-full transition-all duration-500 ${
                      storagePercentage > 80
                        ? 'bg-gradient-to-r from-red-400 to-red-500'
                        : storagePercentage > 60
                          ? 'bg-gradient-to-r from-yellow-400 to-orange-500'
                          : 'bg-gradient-to-r from-[#849068] to-[#B5B178]'
                    }`}
                    style={{ width: `${Math.min(storagePercentage, 100)}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};
