/**
 * 📊 Dashboard Domain Constants
 */

import { QuickAction } from './types';

export const DASHBOARD_ROUTES = {
  MAIN: '/dashboard',
  ANALYTICS: '/dashboard/analytics',
  SETTINGS: '/dashboard/settings',
} as const;

export const TIME_RANGES = {
  WEEK: '7d',
  MONTH: '30d',
  QUARTER: '90d',
  YEAR: '1y',
} as const;

export const ACTIVITY_TYPES = {
  ALL: 'all',
  UPLOADS: 'uploads',
  GENERATIONS: 'generations',
  BRAND_KITS: 'brand_kits',
  AUDIENCES: 'audiences',
} as const;

export const DEFAULT_QUICK_ACTIONS: QuickAction[] = [
  {
    id: 'create-brand-kit',
    title: 'Create Brand Kit',
    description: 'Start a new brand kit from scratch or import from website',
    icon: '🎨',
    href: '/brand-kits/new',
    color: 'bg-blue-500',
  },
  {
    id: 'generate-asset',
    title: 'Generate Asset',
    description: 'Create marketing assets with AI assistance',
    icon: '✨',
    href: '/canvas',
    color: 'bg-purple-500',
  },
  {
    id: 'upload-assets',
    title: 'Upload Assets',
    description: 'Add new files to your asset library',
    icon: '📁',
    href: '/assets',
    color: 'bg-green-500',
  },
  {
    id: 'create-audience',
    title: 'Create Audience',
    description: 'Define new target audience personas',
    icon: '👥',
    href: '/audiences/new',
    color: 'bg-orange-500',
  },
  {
    id: 'add-products',
    title: 'Add Products',
    description: 'Expand your product catalog',
    icon: '🛍️',
    href: '/products/new',
    color: 'bg-pink-500',
  },
  {
    id: 'connect-storage',
    title: 'Connect Cloud Storage',
    description: 'Link Google Drive, OneDrive, or Dropbox',
    icon: '☁️',
    href: '/assets?tab=cloud',
    color: 'bg-indigo-500',
  },
];

export const STORAGE_LIMITS = {
  FREE: 1024 * 1024 * 1024, // 1GB
  PRO: 10 * 1024 * 1024 * 1024, // 10GB
  ENTERPRISE: 100 * 1024 * 1024 * 1024, // 100GB
} as const;
