/**
 * 📊 Dashboard Domain Types
 */

export interface DashboardStats {
  totalAssets: number;
  totalBrandKits: number;
  totalAudiences: number;
  totalProducts: number;
  storageUsed: number;
  storageLimit: number;
  recentUploads: number;
  generatedAssetsThisMonth: number;
}

export interface RecentActivity {
  id: string;
  type: 'asset_upload' | 'brand_kit_created' | 'audience_generated' | 'product_added' | 'asset_generated';
  title: string;
  description: string;
  timestamp: Date;
  metadata?: {
    assetId?: string;
    brandKitId?: string;
    audienceId?: string | number;
    productId?: string;
    thumbnailUrl?: string;
  };
}

export interface CloudStorageStatus {
  provider: 'google_drive' | 'onedrive' | 'dropbox';
  isConnected: boolean;
  totalFiles?: number;
  lastSync?: Date;
  error?: string;
}

export interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: string;
  href?: string;
  onClick?: () => void;
  color: string;
  disabled?: boolean;
}

export interface DashboardData {
  [key: string]: any;
  stats: DashboardStats;
  recentActivity: RecentActivity[];
  cloudStorage: CloudStorageStatus[];
  quickActions: QuickAction[];
}

export interface DashboardFilters {
  [key: string]: any;
  timeRange: '7d' | '30d' | '90d' | '1y';
  activityType?: 'all' | 'uploads' | 'generations' | 'brand_kits' | 'audiences';
}
