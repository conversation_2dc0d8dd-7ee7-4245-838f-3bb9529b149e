/**
 * 📝 Update Brand Kit Details Action
 *
 * @description WASP action for updating specific brand kit details (alternative to full update)
 * @responsibility Handles partial brand kit updates with proper validation and access control
 * @dependencies BrandKitService, BrandKitRepository, authentication
 * @ai_context This action integrates the new modular brand kit domain with WASP's action system
 *
 * @example
 * ```typescript
 * await updateBrandKitDetails({
 *   id: "brandkit_123",
 *   name: "Updated Brand Kit Name",
 *   tags: ["updated", "modern"]
 * });
 * ```
 */

import { type UpdateBrandKitDetails } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { authenticateUser } from '../../../../../server/helpers';
import { handlePrismaError, isPrismaError } from '../../../../../server/utils/error-helper';
import { z } from 'zod';

/**
 * 📝 Update Brand Kit Details Input Schema
 * @ai_context Validation schema for brand kit detail updates
 */
const UpdateBrandKitDetailsInputSchema = z.object({
  id: z.string().min(1, 'Brand kit ID is required'),

  // Basic details
  name: z
    .string()
    .min(1, 'Brand kit name is required')
    .max(100, 'Brand kit name must be 100 characters or less')
    .optional(),
  tags: z.array(z.string().max(50, 'Tags must be 50 characters or less')).max(10, 'Maximum 10 tags allowed').optional(),

  // Visual Identity
  primaryColors: z.array(z.string()).optional(),
  secondaryColors: z.array(z.string()).optional(),
  accentColors: z.array(z.string()).optional(),
  primaryColorUsageText: z.string().optional(),
  secondaryColorUsageText: z.string().optional(),
  accentColorUsageText: z.string().optional(),
  colorHierarchyText: z.string().optional(),
  colorAccessibilityText: z.string().optional(),
  colorCombinationsText: z.string().optional(),
  colorVariationsText: z.string().optional(),
  typography: z.any().optional(),
  logoVariations: z.array(z.string()).optional(),
  moodboardImages: z.array(z.string()).optional(),

  // Logo Guidelines
  logoUsageGuidelinesText: z.string().optional(),
  logoSizingText: z.string().optional(),
  logoClearSpaceText: z.string().optional(),
  logoColorUsageText: z.string().optional(),
  logoFileFormatsText: z.string().optional(),
  logoMisuseText: z.string().optional(),

  // Photography Guidelines
  photoStyleText: z.string().optional(),
  preferredAnglesText: z.string().optional(),
  lightingPreferencesText: z.string().optional(),
  backgroundStylesText: z.string().optional(),
  propGuidelinesText: z.string().optional(),
  commonScenesText: z.string().optional(),
  photoDosDonts: z.any().optional(),

  // Brand Voice
  brandPersonalityText: z.string().optional(),
  tonalityText: z.string().optional(),
  brandValuesText: z.string().optional(),
  targetEmotionsText: z.string().optional(),

  // Language & Phrasing
  writingStyleText: z.string().optional(),
  preferredTermsText: z.string().optional(),
  avoidedTermsText: z.string().optional(),
  grammarRulesText: z.string().optional(),
  contentStructureText: z.string().optional(),
  localizationRulesText: z.string().optional(),
  commonMistakesText: z.string().optional(),

  // Taglines
  primaryTaglinesText: z.string().optional(),
  secondaryTaglinesText: z.string().optional(),
  campaignTaglinesText: z.string().optional(),
  keyMessagesText: z.string().optional(),
  valuePropositionsText: z.string().optional(),
  taglineGuidelinesText: z.string().optional(),
  trademarkInfoText: z.string().optional(),

  // Target Audience
  primaryAudienceText: z.string().optional(),
  primaryAudienceGoalsText: z.string().optional(),
  primaryAudienceChallengesText: z.string().optional(),
  secondaryAudienceText: z.string().optional(),
  secondaryAudienceGoalsText: z.string().optional(),
  secondaryAudienceChallengesText: z.string().optional(),
  tertiaryAudienceText: z.string().optional(),
  tertiaryAudienceGoalsText: z.string().optional(),
  audiencePersonasText: z.string().optional(),
  audienceValuePropsText: z.string().optional(),
  communicationChannelsText: z.string().optional(),
  audienceMetricsText: z.string().optional(),

  // Data Visualization
  preferredChartTypesText: z.string().optional(),
  chartExamplesText: z.string().optional(),
  dataVizColorsText: z.string().optional(),
  dataVizTypographyText: z.string().optional(),
  dataVizLayoutText: z.string().optional(),
  dataVizInteractivityText: z.string().optional(),
  dataVizAccessibilityText: z.string().optional(),
  dataVizBestPracticesText: z.string().optional(),
  dataVizMistakesText: z.string().optional(),

  // Web Design
  webTypographyText: z.string().optional(),
  webSpacingText: z.string().optional(),
  uiComponentExamplesText: z.string().optional(),
  uiComponentsText: z.string().optional(),
  buttonStylesText: z.string().optional(),
  formGuidelinesText: z.string().optional(),
  responsiveGuidelinesText: z.string().optional(),
  webAccessibilityText: z.string().optional(),
  performanceGuidelinesText: z.string().optional(),
  animationGuidelinesText: z.string().optional(),

  // Social Media
  platformGuidelinesText: z.string().optional(),
  socialMediaExamplesText: z.string().optional(),
  visualStyleGuidelinesText: z.string().optional(),
  contentTypesText: z.string().optional(),
  socialToneGuidelinesText: z.string().optional(),
  hashtagGuidelinesText: z.string().optional(),
  engagementGuidelinesText: z.string().optional(),
  postingScheduleText: z.string().optional(),
  socialComplianceText: z.string().optional(),
  analyticsGuidelinesText: z.string().optional(),

  // AI Generation Settings
  promptKeywordsText: z.string().optional(),
  avoidanceTermsText: z.string().optional(),
  preferredSettings: z.any().optional(),
});

export type UpdateBrandKitDetailsInput = z.infer<typeof UpdateBrandKitDetailsInputSchema>;

/**
 * 📤 Update Brand Kit Details Output
 * @ai_context Response format for brand kit detail updates
 */
export interface UpdateBrandKitDetailsOutput {
  success: boolean;
  [key: string]: any; // Index signature for SuperJSON compatibility
}

/**
 * 📝 Update Brand Kit Details Action
 * @ai_context WASP action implementation for updating brand kit details
 */
export const updateBrandKitDetails: UpdateBrandKitDetails<
  UpdateBrandKitDetailsInput,
  UpdateBrandKitDetailsOutput
> = async (args, context) => {
  // Authenticate user
  const user = authenticateUser(context);

  try {
    // Validate input
    const validatedInput = UpdateBrandKitDetailsInputSchema.parse(args);
    const { id, ...updateData } = validatedInput;

    // Check if brand kit exists and user has access
    const existingBrandKit = await context.entities.BrandKit.findUnique({
      where: {
        id,
        deletedAt: null, // Only allow updates to non-deleted brand kits
      },
    });

    if (!existingBrandKit) {
      throw new HttpError(404, 'Brand kit not found');
    }

    if (existingBrandKit.userId !== user.id) {
      throw new HttpError(403, 'You do not have permission to update this brand kit');
    }

    // Update brand kit in database
    await context.entities.BrandKit.update({
      where: { id },
      data: {
        ...updateData,
        updatedAt: new Date(),
      },
    });

    // TODO: Add domain event emission when EventBus is integrated
    // this.eventBus.publish(EventType.BRAND_KIT_DETAILS_UPDATED, {
    //   brandKitId: id,
    //   changes: updateData,
    //   userId: existingBrandKit.userId,
    //   organizationId: existingBrandKit.organizationId
    // }, {
    //   userId: existingBrandKit.userId,
    //   organizationId: existingBrandKit.organizationId
    // });

    // Return success response
    return {
      success: true,
    };
  } catch (error) {
    console.error('[UpdateBrandKitDetails] Error updating brand kit details:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((err) => `${err.path.join('.')}: ${err.message}`);
      throw new HttpError(400, `Validation error: ${errorMessages.join(', ')}`);
    }

    // Handle Prisma errors
    if (isPrismaError(error)) {
      throw handlePrismaError(error);
    }

    // Re-throw HTTP errors
    if (error instanceof HttpError) {
      throw error;
    }

    // Handle other errors
    if (error instanceof Error) {
      throw new HttpError(400, error.message);
    }

    // Generic server error
    throw new HttpError(500, 'An unexpected error occurred while updating brand kit details');
  }
};
