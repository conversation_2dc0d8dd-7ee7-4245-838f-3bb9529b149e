import { HttpError } from 'wasp/server';
import { uploadToR2 } from '../../../../../server/libs/r2';

// Define the input and output types
type UploadFontInput = {
  dataUrl: string;
  brandKitId: string;
  fontName: string;
};

type UploadFontOutput = {
  success: boolean;
  url?: string;
  fontName?: string;
  error?: string;
};

export const uploadFont = async (
  { dataUrl, brandKitId, fontName }: UploadFontInput,
  context: any
): Promise<UploadFontOutput> => {
  // Ensure user is authenticated
  if (!context.user) {
    throw new HttpError(401, 'Not authorized');
  }

  try {
    console.log('[UploadFont] Starting font upload for brand kit:', brandKitId);

    // Validate brand kit access
    const brandKit = await context.entities.BrandKit.findFirst({
      where: {
        id: brandKitId,
        userId: context.user.id,
      },
    });

    if (!brandKit) {
      throw new HttpError(403, 'Brand kit not found or access denied');
    }

    // Extract base64 data from data URL
    const base64Data = dataUrl.split(',')[1];

    // Determine content type from data URL
    const contentType = dataUrl.split(';')[0].split(':')[1] || 'font/woff2';

    // Convert base64 to buffer
    const fileBuffer = Buffer.from(base64Data, 'base64');

    // Create a unique filename
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 10);

    // Determine file extension from content type
    let fileExtension = 'woff2';
    if (contentType.includes('woff2')) fileExtension = 'woff2';
    else if (contentType.includes('woff')) fileExtension = 'woff';
    else if (contentType.includes('truetype') || contentType.includes('ttf')) fileExtension = 'ttf';
    else if (contentType.includes('opentype') || contentType.includes('otf')) fileExtension = 'otf';

    const fileName = `font-${brandKitId}-${timestamp}-${randomId}.${fileExtension}`;

    // Upload to R2 with a specific key for brand kit fonts
    const r2Url = await uploadToR2({
      fileBuffer,
      fileName,
      key: 'brand-kit-fonts',
      contentType,
    });

    console.log('[UploadFont] Upload successful:', r2Url);

    // Get current brand kit data to update fonts
    const currentBrandKit = await context.entities.BrandKit.findUnique({
      where: { id: brandKitId },
    });

    // Parse existing typography data or create new
    let typography: any = {};
    if (currentBrandKit?.typography) {
      try {
        // Handle both string and object cases
        typography =
          typeof currentBrandKit.typography === 'string'
            ? JSON.parse(currentBrandKit.typography)
            : currentBrandKit.typography;
      } catch (error) {
        console.warn('[UploadFont] Failed to parse typography data, creating new:', error);
        typography = {};
      }
    }

    // Initialize customFonts array if it doesn't exist
    if (!typography.customFonts) {
      typography.customFonts = [];
    }

    // Add the new font to custom fonts
    typography.customFonts.push({
      name: fontName,
      url: r2Url,
      uploadedAt: new Date().toISOString(),
    });

    // Update the brand kit with the new typography data
    await context.entities.BrandKit.update({
      where: { id: brandKitId },
      data: { typography: JSON.stringify(typography) },
    });

    console.log('[UploadFont] Brand kit updated with new font');

    return {
      success: true,
      url: r2Url,
      fontName,
    };
  } catch (error) {
    console.error('[UploadFont] Upload failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown upload error',
    };
  }
};
