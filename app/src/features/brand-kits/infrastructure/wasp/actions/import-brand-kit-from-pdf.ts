/**
 * 📄 Import Brand Kit From PDF Action
 *
 * @description WASP action for importing brand kits from PDF files
 * @responsibility Handles PDF import with AI analysis and brand kit creation
 * @dependencies BrandKitService, BrandKitRepository, AI services, authentication
 * @ai_context This action integrates the new modular brand kit domain with WASP's action system
 *
 * @example
 * ```typescript
 * const result = await importBrandKitFromPDF({
 *   url: "data:application/pdf;base64,JVBERi0xLjQ...",
 *   name: "Acme Corp Brand Kit",
 *   organizationId: "org_123"
 * });
 * ```
 */

import { type ImportBrandKitFromPDF } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { authenticateUser } from '../../../../../server/helpers';
import { handlePrismaError, isPrismaError } from '../../../../../server/utils/error-helper';
import { extractTextFromPDF } from '../../../../../server/utils/pdfExtractor';
import { convertPDFToImages } from '../../../../../server/utils/imageCapture';
import { analyzeBrandKit } from '../utils/analyze-brand-kit';
import { z } from 'zod';

/**
 * 📝 Import Brand Kit From PDF Input Schema
 * @ai_context Validation schema for PDF import
 */
const ImportBrandKitFromPDFInputSchema = z.object({
  url: z.string().min(1, 'PDF data is required'),
  name: z.string().min(1, 'Brand kit name is required').max(100, 'Brand kit name must be 100 characters or less'),
  organizationId: z.string().min(1, 'Organization ID is required'),
});

export type ImportBrandKitFromPDFInput = z.infer<typeof ImportBrandKitFromPDFInputSchema>;

/**
 * 📤 Import Brand Kit From PDF Output
 * @ai_context Response format for PDF import - matches existing action
 */
export interface ImportBrandKitFromPDFOutput {
  id?: string;
  error?: string;
  [key: string]: any; // Index signature for SuperJSON compatibility
}

/**
 * 📄 Import Brand Kit From PDF Action
 * @ai_context WASP action implementation for importing brand kits from PDF
 */
export const importBrandKitFromPDF: ImportBrandKitFromPDF<
  ImportBrandKitFromPDFInput,
  ImportBrandKitFromPDFOutput
> = async (args, context) => {
  // Authenticate user
  const user = authenticateUser(context);

  try {
    // Validate input
    const validatedInput = ImportBrandKitFromPDFInputSchema.parse(args);

    // Verify organization access
    const organization = await context.entities.Organization.findUnique({
      where: { id: validatedInput.organizationId },
      include: {
        memberships: {
          where: { userId: user.id },
        },
      },
    });

    if (!organization) {
      throw new HttpError(404, 'Organization not found');
    }

    if (organization.memberships.length === 0) {
      throw new HttpError(403, 'You do not have access to this organization');
    }

    console.log('Starting PDF import process...');

    // Convert base64 to Buffer
    console.log('Converting base64 to buffer...');
    const base64Data = validatedInput.url.replace(/^data:application\/pdf;base64,/, '');
    const pdfBuffer = Buffer.from(base64Data, 'base64');

    // Extract text from PDF
    console.log('Extracting text from PDF...');
    const extractedText = await extractTextFromPDF(pdfBuffer);

    if (!extractedText || extractedText.trim().length === 0) {
      throw new Error('Could not extract text from PDF. Please ensure the PDF contains readable text.');
    }

    // Convert PDF to images for visual analysis
    console.log('Converting PDF to images...');
    const imageBuffers = await convertPDFToImages(pdfBuffer);

    // Convert image buffers to base64 strings for analysis
    const images = imageBuffers.map((buffer) => `data:image/png;base64,${buffer.toString('base64')}`);

    // Analyze brand kit using AI
    console.log('Analyzing brand kit with AI...');
    const content = await analyzeBrandKit(extractedText, images);

    if (!content) {
      throw new Error('Failed to analyze brand kit content. Please try again or contact support.');
    }

    // Create brand kit with analyzed content
    console.log('Creating brand kit with analyzed data...');
    const brandKit = await context.entities.BrandKit.create({
      data: {
        name: validatedInput.name,
        userId: user.id,
        organizationId: organization.id,
        ...content,
        // Initialize empty arrays for fields that might not be set
        tags: content.tags || [],
        logoVariations: content.logoVariations || [],
        moodboardImages: content.moodboardImages || [],
        primaryColors: content.primaryColors || [],
        secondaryColors: content.secondaryColors || [],
        accentColors: content.accentColors || [],
      },
    });

    // TODO: Add domain event emission when EventBus is integrated
    // this.eventBus.publish(EventType.BRAND_KIT_IMPORTED, {
    //   brandKitId: brandKit.id,
    //   name: brandKit.name,
    //   source: 'PDF',
    //   userId: user.id,
    //   organizationId: organization.id
    // }, {
    //   userId: user.id,
    //   organizationId: organization.id
    // });

    return { id: brandKit.id };
  } catch (error) {
    console.error('[ImportBrandKitFromPDF] Error importing brand kit:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((err) => `${err.path.join('.')}: ${err.message}`);
      return { error: `Validation error: ${errorMessages.join(', ')}` };
    }

    // Handle Prisma errors
    if (isPrismaError(error)) {
      return { error: 'Database error occurred while creating brand kit' };
    }

    // Handle HTTP errors
    if (error instanceof HttpError) {
      return { error: error.message };
    }

    // Handle other errors
    if (error instanceof Error) {
      return { error: error.message };
    }

    // Generic error
    return { error: 'An unexpected error occurred while importing the brand kit' };
  }
};
