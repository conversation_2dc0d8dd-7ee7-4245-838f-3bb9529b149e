/**
 * 📂 Category Field Operations
 *
 * @description WASP actions for managing brand kit category field values
 * @responsibility Handles category field CRUD operations with proper validation and access control
 * @dependencies BrandKitService, BrandKitRepository, authentication
 * @ai_context These actions integrate the new modular brand kit domain with WASP's action system
 *
 * @example
 * ```typescript
 * await insertCategoryFieldValue({
 *   brandKitId: "brandkit_123",
 *   fieldId: "field_456",
 *   value: "Modern and clean"
 * });
 * ```
 */

import {
  type InsertCategoryFieldValue,
  type UpdateCategoryFieldValue,
  type DeleteCategoryFieldValue,
} from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { authenticateUser } from '../../../../../server/helpers';
import { handlePrismaError, isPrismaError } from '../../../../../server/utils/error-helper';
import { z } from 'zod';

/**
 * 📝 Insert Category Field Value Input Schema
 */
const InsertCategoryFieldValueInputSchema = z.object({
  brandKitId: z.string().min(1, 'Brand kit ID is required'),
  categoryFieldId: z.number().int().positive('Category field ID is required'),
  brandCategoryId: z.number().int().positive('Brand category ID is required'),
  value: z.string().min(1, 'Value is required').max(1000, 'Value must be 1000 characters or less'),
});

/**
 * 📝 Update Category Field Value Input Schema
 */
const UpdateCategoryFieldValueInputSchema = z.object({
  id: z.number().int().positive('Category field value ID is required'),
  value: z.string().min(1, 'Value is required').max(1000, 'Value must be 1000 characters or less'),
});

/**
 * 📝 Delete Category Field Value Input Schema
 */
const DeleteCategoryFieldValueInputSchema = z.object({
  id: z.number().int().positive('Category field value ID is required'),
});

export type InsertCategoryFieldValueInput = z.infer<typeof InsertCategoryFieldValueInputSchema>;
export type UpdateCategoryFieldValueInput = z.infer<typeof UpdateCategoryFieldValueInputSchema>;
export type DeleteCategoryFieldValueInput = z.infer<typeof DeleteCategoryFieldValueInputSchema>;

/**
 * 📤 Category Field Operations Output
 */
export interface CategoryFieldOperationOutput {
  success: boolean;
  id?: number;
  [key: string]: any; // Index signature for SuperJSON compatibility
}

/**
 * ➕ Insert Category Field Value Action
 */
export const insertCategoryFieldValue: InsertCategoryFieldValue<
  InsertCategoryFieldValueInput,
  CategoryFieldOperationOutput
> = async (args, context) => {
  // Authenticate user
  const user = authenticateUser(context);

  try {
    // Validate input
    const validatedInput = InsertCategoryFieldValueInputSchema.parse(args);

    // Check if brand kit exists and user has access
    const brandKit = await context.entities.BrandKit.findUnique({
      where: {
        id: validatedInput.brandKitId,
        deletedAt: null,
      },
    });

    if (!brandKit) {
      throw new HttpError(404, 'Brand kit not found');
    }

    if (brandKit.userId !== user.id) {
      throw new HttpError(403, 'You do not have permission to modify this brand kit');
    }

    // Create category field value
    const categoryFieldValue = await context.entities.CategoryFieldValue.create({
      data: {
        brandKitId: validatedInput.brandKitId,
        categoryFieldId: validatedInput.categoryFieldId,
        brandCategoryId: validatedInput.brandCategoryId,
        value: validatedInput.value,
      },
    });

    return {
      success: true,
      id: categoryFieldValue.id,
    };
  } catch (error) {
    console.error('[InsertCategoryFieldValue] Error:', error);

    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((err) => `${err.path.join('.')}: ${err.message}`);
      throw new HttpError(400, `Validation error: ${errorMessages.join(', ')}`);
    }

    if (isPrismaError(error)) {
      throw handlePrismaError(error);
    }

    if (error instanceof HttpError) {
      throw error;
    }

    throw new HttpError(500, 'An unexpected error occurred while creating category field value');
  }
};

/**
 * ✏️ Update Category Field Value Action
 */
export const updateCategoryFieldValue: UpdateCategoryFieldValue<
  UpdateCategoryFieldValueInput,
  CategoryFieldOperationOutput
> = async (args, context) => {
  // Authenticate user
  const user = authenticateUser(context);

  try {
    // Validate input
    const validatedInput = UpdateCategoryFieldValueInputSchema.parse(args);

    // Check if category field value exists and user has access
    const categoryFieldValue = await context.entities.CategoryFieldValue.findUnique({
      where: { id: validatedInput.id },
      include: {
        brandKit: true,
      },
    });

    if (!categoryFieldValue) {
      throw new HttpError(404, 'Category field value not found');
    }

    if (categoryFieldValue.brandKit.userId !== user.id) {
      throw new HttpError(403, 'You do not have permission to modify this category field value');
    }

    // Update category field value
    await context.entities.CategoryFieldValue.update({
      where: { id: validatedInput.id },
      data: {
        value: validatedInput.value,
        updatedAt: new Date(),
      },
    });

    return {
      success: true,
      id: validatedInput.id,
    };
  } catch (error) {
    console.error('[UpdateCategoryFieldValue] Error:', error);

    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((err) => `${err.path.join('.')}: ${err.message}`);
      throw new HttpError(400, `Validation error: ${errorMessages.join(', ')}`);
    }

    if (isPrismaError(error)) {
      throw handlePrismaError(error);
    }

    if (error instanceof HttpError) {
      throw error;
    }

    throw new HttpError(500, 'An unexpected error occurred while updating category field value');
  }
};

/**
 * 🗑️ Delete Category Field Value Action
 */
export const deleteCategoryFieldValue: DeleteCategoryFieldValue<
  DeleteCategoryFieldValueInput,
  CategoryFieldOperationOutput
> = async (args, context) => {
  // Authenticate user
  const user = authenticateUser(context);

  try {
    // Validate input
    const validatedInput = DeleteCategoryFieldValueInputSchema.parse(args);

    // Check if category field value exists and user has access
    const categoryFieldValue = await context.entities.CategoryFieldValue.findUnique({
      where: { id: validatedInput.id },
      include: {
        brandKit: true,
      },
    });

    if (!categoryFieldValue) {
      throw new HttpError(404, 'Category field value not found');
    }

    if (categoryFieldValue.brandKit.userId !== user.id) {
      throw new HttpError(403, 'You do not have permission to delete this category field value');
    }

    // Delete category field value
    await context.entities.CategoryFieldValue.delete({
      where: { id: validatedInput.id },
    });

    return {
      success: true,
      id: validatedInput.id,
    };
  } catch (error) {
    console.error('[DeleteCategoryFieldValue] Error:', error);

    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((err) => `${err.path.join('.')}: ${err.message}`);
      throw new HttpError(400, `Validation error: ${errorMessages.join(', ')}`);
    }

    if (isPrismaError(error)) {
      throw handlePrismaError(error);
    }

    if (error instanceof HttpError) {
      throw error;
    }

    throw new HttpError(500, 'An unexpected error occurred while deleting category field value');
  }
};
