/**
 * 🎨 Create Brand Kit WASP Action
 *
 * @description WASP action for creating new brand kits
 * @responsibility Handles HTTP requests for brand kit creation
 * @dependencies BrandKitService, repository implementation, authentication
 * @ai_context This is the WASP action that handles brand kit creation requests from the frontend
 *
 * @example
 * ```typescript
 * // Frontend usage
 * import { createBrandKit } from 'wasp/client/operations';
 *
 * const result = await createBrandKit({
 *   name: "Acme Corp Brand",
 *   organizationId: "org_123",
 *   tags: ["corporate", "modern"]
 * });
 * ```
 */

import { type CreateBrandKit } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { authenticateUser } from '../../../../../server/helpers';
import { handlePrismaError, isPrismaError } from '../../../../../server/utils/error-helper';
import { z } from 'zod';

/**
 * 📝 Create Brand Kit Input Schema
 * @ai_context Validation schema for brand kit creation
 */
const CreateBrandKitInputSchema = z.object({
  name: z
    .string()
    .min(1, 'Brand kit name is required')
    .max(100, 'Brand kit name must be 100 characters or less')
    .trim(),
  organizationId: z.string().min(1, 'Organization ID is required'),
  tags: z.array(z.string().max(50, 'Tags must be 50 characters or less')).max(10, 'Maximum 10 tags allowed').optional(),
  initialData: z
    .object({
      primaryColors: z.array(z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid color format')).optional(),
      secondaryColors: z.array(z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid color format')).optional(),
      accentColors: z.array(z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid color format')).optional(),
      typography: z.any().optional(),
      logoVariations: z.array(z.string().url('Invalid URL format')).optional(),
      moodboardImages: z.array(z.string().url('Invalid URL format')).optional(),
    })
    .optional(),
});

export type CreateBrandKitInput = z.infer<typeof CreateBrandKitInputSchema>;

/**
 * 📤 Create Brand Kit Output
 * @ai_context Response format for brand kit creation - matches existing action
 */
export interface CreateBrandKitOutput {
  id: string;
  [key: string]: any; // Index signature for SuperJSON compatibility
}

/**
 * 🎨 Create Brand Kit Action
 * @ai_context WASP action implementation for creating brand kits (simplified version)
 */
export const createBrandKit: CreateBrandKit<CreateBrandKitInput, CreateBrandKitOutput> = async (args, context) => {
  // Authenticate user
  const user = authenticateUser(context);

  try {
    // Validate input
    const validatedInput = CreateBrandKitInputSchema.parse(args);

    // Verify organization access
    const organization = await context.entities.Organization.findUniqueOrThrow({
      where: { id: validatedInput.organizationId },
    });

    // Create brand kit using direct Prisma approach (like existing actions)
    const payload = {
      name: validatedInput.name,
      userId: user.id,
      tags: validatedInput.tags || [],
      organizationId: organization.id,

      // Initialize with empty values (following existing pattern)
      primaryColors: validatedInput.initialData?.primaryColors || [],
      secondaryColors: validatedInput.initialData?.secondaryColors || [],
      accentColors: validatedInput.initialData?.accentColors || [],
      typography: validatedInput.initialData?.typography || {},
      logoVariations: validatedInput.initialData?.logoVariations || [],
      moodboardImages: validatedInput.initialData?.moodboardImages || [],

      // Initialize other fields with empty values
      photoStyleText: '',
      preferredAnglesText: '',
      lightingPreferencesText: '',
      backgroundStylesText: '',
      propGuidelinesText: '',
      commonScenesText: '',
      photoDosDonts: {},
      brandPersonalityText: '',
      tonalityText: '',
      brandValuesText: '',
      targetEmotionsText: '',
      writingStyleText: '',
      preferredTermsText: '',
      avoidedTermsText: '',
      grammarRulesText: '',
      contentStructureText: '',
      localizationRulesText: '',
      commonMistakesText: '',
      primaryTaglinesText: '',
      secondaryTaglinesText: '',
      campaignTaglinesText: '',
      keyMessagesText: '',
      valuePropositionsText: '',
      taglineGuidelinesText: '',
      trademarkInfoText: '',
      primaryAudienceText: '',
      primaryAudienceGoalsText: '',
      primaryAudienceChallengesText: '',
      secondaryAudienceText: '',
      secondaryAudienceGoalsText: '',
      secondaryAudienceChallengesText: '',
      tertiaryAudienceText: '',
      tertiaryAudienceGoalsText: '',
      audiencePersonasText: '',
      audienceValuePropsText: '',
      communicationChannelsText: '',
      audienceMetricsText: '',
      preferredChartTypesText: '',
      chartExamplesText: '',
      dataVizColorsText: '',
      dataVizTypographyText: '',
      dataVizLayoutText: '',
      dataVizInteractivityText: '',
      dataVizAccessibilityText: '',
      dataVizBestPracticesText: '',
      dataVizMistakesText: '',
      webTypographyText: '',
      webSpacingText: '',
      uiComponentExamplesText: '',
      uiComponentsText: '',
      buttonStylesText: '',
      formGuidelinesText: '',
      responsiveGuidelinesText: '',
      webAccessibilityText: '',
      performanceGuidelinesText: '',
      animationGuidelinesText: '',
      platformGuidelinesText: '',
      socialMediaExamplesText: '',
      visualStyleGuidelinesText: '',
      contentTypesText: '',
      socialToneGuidelinesText: '',
      hashtagGuidelinesText: '',
      engagementGuidelinesText: '',
      postingScheduleText: '',
      socialComplianceText: '',
      analyticsGuidelinesText: '',
      promptKeywordsText: '',
      avoidanceTermsText: '',
      preferredSettings: {},
    };

    const brandKit = await context.entities.BrandKit.create({
      data: payload,
    });

    // Return response (matches existing action output format)
    return {
      id: brandKit.id,
    };
  } catch (error) {
    console.error('[CreateBrandKit] Error creating brand kit:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((err) => `${err.path.join('.')}: ${err.message}`);
      throw new HttpError(400, `Validation error: ${errorMessages.join(', ')}`);
    }

    // Handle Prisma errors
    if (isPrismaError(error)) {
      throw handlePrismaError(error);
    }

    // Handle other errors
    if (error instanceof Error) {
      throw new HttpError(400, error.message);
    }

    // Generic server error
    throw new HttpError(500, 'An unexpected error occurred while creating the brand kit');
  }
};
