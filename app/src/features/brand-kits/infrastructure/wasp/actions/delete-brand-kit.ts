/**
 * 🗑️ Delete Brand Kit Action
 *
 * @description WASP action for soft-deleting brand kits
 * @responsibility Handles brand kit deletion with proper validation and access control
 * @dependencies BrandKitService, BrandKitRepository, authentication
 * @ai_context This action integrates the new modular brand kit domain with WASP's action system
 *
 * @example
 * ```typescript
 * await deleteBrandKit({ id: "brandkit_123" });
 * ```
 */

import { type DeleteBrandKit } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { authenticateUser } from '../../../../../server/helpers';
import { handlePrismaError, isPrismaError } from '../../../../../server/utils/error-helper';
import { z } from 'zod';

/**
 * 📝 Delete Brand Kit Input Schema
 * @ai_context Validation schema for brand kit deletion
 */
const DeleteBrandKitInputSchema = z.object({
  id: z.string().min(1, 'Brand kit ID is required'),
});

export type DeleteBrandKitInput = z.infer<typeof DeleteBrandKitInputSchema>;

/**
 * 📤 Delete Brand Kit Output
 * @ai_context Response format for brand kit deletion - matches existing action
 */
export interface DeleteBrandKitOutput {
  success: boolean;
  [key: string]: any; // Index signature for SuperJSON compatibility
}

/**
 * 🗑️ Delete Brand Kit Action
 * @ai_context WASP action implementation for deleting brand kits (soft delete)
 */
export const deleteBrandKit: DeleteBrandKit<DeleteBrandKitInput, DeleteBrandKitOutput> = async (args, context) => {
  // Authenticate user
  const user = authenticateUser(context);

  try {
    // Validate input
    const validatedInput = DeleteBrandKitInputSchema.parse(args);

    // Check if brand kit exists and user has access
    const existingBrandKit = await context.entities.BrandKit.findUnique({
      where: {
        id: validatedInput.id,
        deletedAt: null, // Only allow deletion of non-deleted brand kits
      },
    });

    if (!existingBrandKit) {
      throw new HttpError(404, 'Brand kit not found');
    }

    if (existingBrandKit.userId !== user.id) {
      throw new HttpError(403, 'You do not have permission to delete this brand kit');
    }

    // Perform soft delete by setting deletedAt timestamp
    await context.entities.BrandKit.update({
      where: { id: validatedInput.id },
      data: {
        deletedAt: new Date(),
        updatedAt: new Date(),
      },
    });

    // TODO: Add domain event emission when EventBus is integrated
    // this.eventBus.publish(EventType.BRAND_KIT_DELETED, {
    //   brandKitId: validatedInput.id,
    //   name: existingBrandKit.name,
    //   userId: existingBrandKit.userId,
    //   organizationId: existingBrandKit.organizationId
    // }, {
    //   userId: existingBrandKit.userId,
    //   organizationId: existingBrandKit.organizationId
    // });

    // Return success response
    return {
      success: true,
    };
  } catch (error) {
    console.error('[DeleteBrandKit] Error deleting brand kit:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((err) => `${err.path.join('.')}: ${err.message}`);
      throw new HttpError(400, `Validation error: ${errorMessages.join(', ')}`);
    }

    // Handle Prisma errors
    if (isPrismaError(error)) {
      throw handlePrismaError(error);
    }

    // Re-throw HTTP errors
    if (error instanceof HttpError) {
      throw error;
    }

    // Handle other errors
    if (error instanceof Error) {
      throw new HttpError(400, error.message);
    }

    // Generic server error
    throw new HttpError(500, 'An unexpected error occurred while deleting the brand kit');
  }
};
