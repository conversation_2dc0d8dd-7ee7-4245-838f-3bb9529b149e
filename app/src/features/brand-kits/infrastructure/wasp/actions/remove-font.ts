import { HttpError } from 'wasp/server';

// Define the input and output types
type RemoveFontInput = {
  brandKitId: string;
  fontName: string;
};

type RemoveFontOutput = {
  success: boolean;
  error?: string;
};

export const removeFont = async (
  { brandKitId, fontName }: RemoveFontInput,
  context: any
): Promise<RemoveFontOutput> => {
  // Ensure user is authenticated
  if (!context.user) {
    throw new HttpError(401, 'Not authorized');
  }

  try {
    console.log('[RemoveFont] Starting font removal for brand kit:', brandKitId, 'font:', fontName);

    // Validate brand kit access
    const brandKit = await context.entities.BrandKit.findFirst({
      where: {
        id: brandKitId,
        userId: context.user.id,
      },
    });

    if (!brandKit) {
      throw new HttpError(403, 'Brand kit not found or access denied');
    }

    // Get current brand kit data to update fonts
    const currentBrandKit = await context.entities.BrandKit.findUnique({
      where: { id: brandKitId },
    });

    if (!currentBrandKit) {
      throw new HttpError(404, 'Brand kit not found');
    }

    // Parse existing typography data
    let typography: any = {};
    if (currentBrandKit?.typography) {
      try {
        // Handle both string and object cases
        typography =
          typeof currentBrandKit.typography === 'string'
            ? JSON.parse(currentBrandKit.typography)
            : currentBrandKit.typography;
      } catch (error) {
        console.warn('[RemoveFont] Failed to parse typography data:', error);
        throw new HttpError(500, 'Invalid typography data format');
      }
    }

    // Check if customFonts array exists
    if (!typography.customFonts || !Array.isArray(typography.customFonts)) {
      throw new HttpError(404, 'No custom fonts found');
    }

    // Find the font to remove
    const fontIndex = typography.customFonts.findIndex((font: any) => font.name === fontName);

    if (fontIndex === -1) {
      throw new HttpError(404, `Font "${fontName}" not found`);
    }

    // Remove the font from the array
    typography.customFonts.splice(fontIndex, 1);

    // Update the brand kit with the new typography data
    await context.entities.BrandKit.update({
      where: { id: brandKitId },
      data: { typography: JSON.stringify(typography) },
    });

    console.log('[RemoveFont] Font removed successfully from brand kit');

    // TODO: Optionally delete the font file from R2 storage
    // This would require additional logic to check if the font is used elsewhere
    // For now, we'll leave the file in R2 to avoid breaking other brand kits that might use it

    return {
      success: true,
    };
  } catch (error) {
    console.error('[RemoveFont] Font removal failed:', error);

    if (error instanceof HttpError) {
      throw error; // Re-throw HTTP errors to maintain status codes
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown removal error',
    };
  }
};
