/**
 * 📸 Upload Brand Kit Images Action
 *
 * @description WASP action for uploading brand kit images (logos, moodboard, etc.)
 * @responsibility Handles image uploads with proper validation and access control
 * @dependencies BrandKitService, BrandKitRepository, authentication
 * @ai_context This action integrates the new modular brand kit domain with WASP's action system
 *
 * @example
 * ```typescript
 * await uploadBrandKitImages({
 *   brandKitId: "brandkit_123",
 *   images: [{ url: "https://...", type: "logo" }]
 * });
 * ```
 */

import { type UploadBrandKitImages } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { authenticateUser } from '../../../../../server/helpers';
import { handlePrismaError, isPrismaError } from '../../../../../server/utils/error-helper';
import { z } from 'zod';

/**
 * 📝 Upload Brand Kit Images Input Schema
 * @ai_context Validation schema for brand kit image uploads
 */
const UploadBrandKitImagesInputSchema = z.object({
  brandKitId: z.string().min(1, 'Brand kit ID is required'),
  images: z
    .array(
      z.object({
        url: z.string().url('Invalid image URL'),
        type: z.enum(['logo', 'moodboard', 'cover']),
        name: z.string().optional(),
        description: z.string().optional(),
      })
    )
    .min(1, 'At least one image is required')
    .max(20, 'Maximum 20 images allowed'),
});

export type UploadBrandKitImagesInput = z.infer<typeof UploadBrandKitImagesInputSchema>;

/**
 * 📤 Upload Brand Kit Images Output
 * @ai_context Response format for brand kit image uploads
 */
export interface UploadBrandKitImagesOutput {
  success: boolean;
  uploadedCount: number;
  [key: string]: any; // Index signature for SuperJSON compatibility
}

/**
 * 📸 Upload Brand Kit Images Action
 * @ai_context WASP action implementation for uploading brand kit images
 */
export const uploadBrandKitImages: UploadBrandKitImages<UploadBrandKitImagesInput, UploadBrandKitImagesOutput> = async (
  args,
  context
) => {
  // Authenticate user
  const user = authenticateUser(context);

  try {
    // Validate input
    const validatedInput = UploadBrandKitImagesInputSchema.parse(args);

    // Check if brand kit exists and user has access
    const existingBrandKit = await context.entities.BrandKit.findUnique({
      where: {
        id: validatedInput.brandKitId,
        deletedAt: null, // Only allow uploads to non-deleted brand kits
      },
    });

    if (!existingBrandKit) {
      throw new HttpError(404, 'Brand kit not found');
    }

    if (existingBrandKit.userId !== user.id) {
      throw new HttpError(403, 'You do not have permission to upload images to this brand kit');
    }

    // Separate images by type
    const logoImages = validatedInput.images.filter((img) => img.type === 'logo');
    const moodboardImages = validatedInput.images.filter((img) => img.type === 'moodboard');
    const coverImages = validatedInput.images.filter((img) => img.type === 'cover');

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date(),
    };

    // Add logo variations
    if (logoImages.length > 0) {
      const currentLogos = Array.isArray(existingBrandKit.logoVariations)
        ? (existingBrandKit.logoVariations as string[])
        : [];
      updateData.logoVariations = [...currentLogos, ...logoImages.map((img) => img.url)];
    }

    // Add moodboard images
    if (moodboardImages.length > 0) {
      const currentMoodboard = Array.isArray(existingBrandKit.moodboardImages)
        ? (existingBrandKit.moodboardImages as string[])
        : [];
      updateData.moodboardImages = [...currentMoodboard, ...moodboardImages.map((img) => img.url)];
    }

    // Set cover image (use the first cover image)
    if (coverImages.length > 0) {
      updateData.coverImage = coverImages[0].url;
    }

    // Update brand kit in database
    await context.entities.BrandKit.update({
      where: { id: validatedInput.brandKitId },
      data: updateData,
    });

    // TODO: Add domain event emission when EventBus is integrated
    // this.eventBus.publish(EventType.BRAND_KIT_IMAGES_UPLOADED, {
    //   brandKitId: validatedInput.brandKitId,
    //   imageCount: validatedInput.images.length,
    //   userId: existingBrandKit.userId,
    //   organizationId: existingBrandKit.organizationId
    // }, {
    //   userId: existingBrandKit.userId,
    //   organizationId: existingBrandKit.organizationId
    // });

    // Return success response
    return {
      success: true,
      uploadedCount: validatedInput.images.length,
    };
  } catch (error) {
    console.error('[UploadBrandKitImages] Error uploading images:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((err) => `${err.path.join('.')}: ${err.message}`);
      throw new HttpError(400, `Validation error: ${errorMessages.join(', ')}`);
    }

    // Handle Prisma errors
    if (isPrismaError(error)) {
      throw handlePrismaError(error);
    }

    // Re-throw HTTP errors
    if (error instanceof HttpError) {
      throw error;
    }

    // Handle other errors
    if (error instanceof Error) {
      throw new HttpError(400, error.message);
    }

    // Generic server error
    throw new HttpError(500, 'An unexpected error occurred while uploading images');
  }
};
