/**
 * 🌐 Import Brand Kit From URL Action
 *
 * @description WASP action for importing brand kits from URLs (websites or PDFs)
 * @responsibility Handles URL import with AI analysis and brand kit creation
 * @dependencies BrandKitService, BrandKitRepository, AI services, authentication
 * @ai_context This action integrates the new modular brand kit domain with WASP's action system
 *
 * @example
 * ```typescript
 * const result = await importBrandKitFromURL({
 *   url: "https://example.com/brand-guidelines",
 *   name: "Acme Corp Brand Kit",
 *   organizationId: "org_123"
 * });
 * ```
 */

import { type ImportBrandKitFromURL } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { authenticateUser } from '../../../../../server/helpers';
import { handlePrismaError, isPrismaError } from '../../../../../server/utils/error-helper';
import { getCleanTextFromUrl } from '../../../../../server/utils/htmlCleaner';
import { analyzeBrandKit } from '../utils/analyze-brand-kit';
import { z } from 'zod';
import fetch from 'node-fetch';

/**
 * 📝 Import Brand Kit From URL Input Schema
 * @ai_context Validation schema for URL import
 */
const ImportBrandKitFromURLInputSchema = z.object({
  url: z.string().url('Invalid URL format'),
  name: z.string().min(1, 'Brand kit name is required').max(100, 'Brand kit name must be 100 characters or less'),
  organizationId: z.string().min(1, 'Organization ID is required'),
  importJobId: z.string().optional(),
});

export type ImportBrandKitFromURLInput = z.infer<typeof ImportBrandKitFromURLInputSchema>;

/**
 * 📤 Import Brand Kit From URL Output
 * @ai_context Response format for URL import - matches existing action
 */
export interface ImportBrandKitFromURLOutput {
  id?: string;
  error?: string;
  [key: string]: any; // Index signature for SuperJSON compatibility
}

/**
 * 🌐 Import Brand Kit From URL Action
 * @ai_context WASP action implementation for importing brand kits from URLs
 */
export const importBrandKitFromURL: ImportBrandKitFromURL<
  ImportBrandKitFromURLInput,
  ImportBrandKitFromURLOutput
> = async (args, context) => {
  // Authenticate user
  const user = authenticateUser(context);

  try {
    // Validate input
    const validatedInput = ImportBrandKitFromURLInputSchema.parse(args);

    // Verify organization access
    const organization = await context.entities.Organization.findUnique({
      where: { id: validatedInput.organizationId },
      include: {
        memberships: {
          where: { userId: user.id },
        },
      },
    });

    if (!organization) {
      throw new HttpError(404, 'Organization not found');
    }

    if (organization.memberships.length === 0) {
      throw new HttpError(403, 'You do not have access to this organization');
    }

    console.log('Starting URL import process...');

    // Check if URL is accessible
    console.log('Checking URL accessibility...');
    const response = await fetch(validatedInput.url, {
      method: 'HEAD',
      timeout: 10000,
    });

    if (!response.ok) {
      throw new Error(`URL is not accessible: ${response.status} ${response.statusText}`);
    }

    // Extract content from URL
    console.log('Extracting content from URL...');
    const extractedText = await getCleanTextFromUrl(validatedInput.url);

    if (!extractedText || extractedText.trim().length === 0) {
      throw new Error(
        'Could not extract meaningful content from the URL. Please ensure the page contains readable text.'
      );
    }

    // Analyze brand kit using AI (no images for URL import)
    console.log('Analyzing brand kit with AI...');
    const content = await analyzeBrandKit(extractedText, []);

    if (!content) {
      throw new Error('Failed to analyze brand kit content. Please try again or contact support.');
    }

    // Create brand kit with analyzed content
    console.log('Creating brand kit with analyzed data...');
    const brandKit = await context.entities.BrandKit.create({
      data: {
        name: validatedInput.name,
        userId: user.id,
        organizationId: organization.id,
        ...content,
        // Initialize empty arrays for fields that might not be set
        tags: content.tags || [],
        logoVariations: content.logoVariations || [],
        moodboardImages: content.moodboardImages || [],
        primaryColors: content.primaryColors || [],
        secondaryColors: content.secondaryColors || [],
        accentColors: content.accentColors || [],
      },
    });

    // Update import job if provided
    if (validatedInput.importJobId) {
      try {
        await context.entities.ImportJob.update({
          where: { id: validatedInput.importJobId },
          data: {
            status: 'COMPLETED',
            brandKitStatus: 'COMPLETED',
            brandKitCompletedAt: new Date(),
            completedAt: new Date(),
          },
        });
      } catch (importJobError) {
        console.warn('Failed to update import job:', importJobError);
        // Don't fail the entire operation if import job update fails
      }
    }

    // TODO: Add domain event emission when EventBus is integrated
    // this.eventBus.publish(EventType.BRAND_KIT_IMPORTED, {
    //   brandKitId: brandKit.id,
    //   name: brandKit.name,
    //   source: 'URL',
    //   url: validatedInput.url,
    //   userId: user.id,
    //   organizationId: organization.id
    // }, {
    //   userId: user.id,
    //   organizationId: organization.id
    // });

    return { id: brandKit.id };
  } catch (error) {
    console.error('[ImportBrandKitFromURL] Error importing brand kit:', error);

    // Update import job with error if provided
    if (args.importJobId) {
      try {
        await context.entities.ImportJob.update({
          where: { id: args.importJobId },
          data: {
            status: 'FAILED',
            brandKitStatus: 'FAILED',
            brandKitError: error instanceof Error ? error.message : 'Unknown error',
            brandKitCompletedAt: new Date(),
            completedAt: new Date(),
          },
        });
      } catch (importJobError) {
        console.warn('Failed to update import job with error:', importJobError);
      }
    }

    // Handle validation errors
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((err) => `${err.path.join('.')}: ${err.message}`);
      return { error: `Validation error: ${errorMessages.join(', ')}` };
    }

    // Handle Prisma errors
    if (isPrismaError(error)) {
      return { error: 'Database error occurred while creating brand kit' };
    }

    // Handle HTTP errors
    if (error instanceof HttpError) {
      return { error: error.message };
    }

    // Handle other errors
    if (error instanceof Error) {
      return { error: error.message };
    }

    // Generic error
    return { error: 'An unexpected error occurred while importing the brand kit' };
  }
};
