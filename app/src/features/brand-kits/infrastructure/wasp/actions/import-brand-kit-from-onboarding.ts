/**
 * 🚀 Import Brand Kit From Onboarding Action
 *
 * @description WASP action for creating brand kits during onboarding using website analysis data
 * @responsibility Handles brand kit creation with Gemini AI analysis during onboarding flow
 * @dependencies BrandKit entity, analyzeBrandKit utility, authentication
 * @ai_context This action integrates our Gemini brand kit analysis with the onboarding flow
 */

import type { ImportBrandKitFromOnboarding } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { analyzeBrandKit } from '../utils/analyze-brand-kit';

// Helper function to get favicon URL from domain
const getFaviconUrl = (websiteUrl: string): string => {
  try {
    const url = new URL(websiteUrl.startsWith('http') ? websiteUrl : `https://${websiteUrl}`);
    return `https://www.google.com/s2/favicons?domain=${url.hostname}&sz=128`;
  } catch {
    return '';
  }
};

type ImportBrandKitFromOnboardingInput = {
  name: string;
  organizationId: string;
  websiteContent?: any; // Cleaned website text content
  websiteAnalysisData?: any; // Legacy support (deprecated)
  brandGuideData?: any; // Brand guide images + text from PDF processing
  formData?: any;
};

type ImportBrandKitFromOnboardingOutput = {
  id: string;
  name: string;
  organizationId: string;
};

/**
 * 🎨 Import Brand Kit From Onboarding
 * @description Creates a brand kit during onboarding using Gemini AI analysis
 */
export const importBrandKitFromOnboarding: ImportBrandKitFromOnboarding<
  ImportBrandKitFromOnboardingInput,
  ImportBrandKitFromOnboardingOutput
> = async (args, context) => {
  // Authenticate user
  if (!context.user) {
    throw new HttpError(401, 'User must be authenticated');
  }

  const user = context.user;
  const { name, organizationId, websiteContent, websiteAnalysisData, brandGuideData, formData } = args;

  console.log('[ImportBrandKitFromOnboarding] Input parameters:');
  console.log('  - name:', name);
  console.log('  - organizationId:', organizationId);
  console.log('  - websiteContent:', websiteContent ? 'present' : 'missing');
  console.log('  - websiteAnalysisData (legacy):', websiteAnalysisData ? 'present' : 'missing');
  console.log('  - brandGuideData:', brandGuideData ? 'present' : 'missing');
  if (brandGuideData) {
    console.log('  - brandGuideData details:', {
      hasTextContent: !!brandGuideData.textContent,
      hasImages: !!(brandGuideData.imageGcsUris && brandGuideData.imageGcsUris.length > 0),
      imageCount: brandGuideData.imageGcsUris?.length || 0,
      metadata: brandGuideData.metadata,
    });
  }

  try {
    console.log('[ImportBrandKitFromOnboarding] Starting brand kit creation...');

    // Validate organization access
    const organization = await context.entities.Organization.findFirst({
      where: {
        id: organizationId,
        memberships: {
          some: { userId: user.id },
        },
      },
    });

    if (!organization) {
      throw new HttpError(403, 'User does not have access to this organization');
    }

    // Extract website content for analysis
    let websiteTextContent = '';

    if (websiteContent?.cleanedText) {
      // Use cleaned website text content
      websiteTextContent = websiteContent.cleanedText;
      console.log(
        `[ImportBrandKitFromOnboarding] Using cleaned website content: ${websiteTextContent.length} characters`
      );
    } else if (websiteAnalysisData) {
      // Fallback to legacy website analysis data
      console.log('[ImportBrandKitFromOnboarding] Falling back to legacy website analysis data');
      const contentSources = [
        websiteAnalysisData.seoMetadata?.title,
        websiteAnalysisData.seoMetadata?.description,
        websiteAnalysisData.contentAnalysis?.brandVoice,
        websiteAnalysisData.contentAnalysis?.tonality,
        websiteAnalysisData.contentAnalysis?.keyMessages?.join(' '),
        websiteAnalysisData.contentAnalysis?.valuePropositions?.join(' '),
        websiteAnalysisData.structuredData?.companyInfo?.description,
        JSON.stringify(websiteAnalysisData.brandElements || {}),
        JSON.stringify(websiteAnalysisData.contentAnalysis || {}),
      ].filter(Boolean);

      websiteTextContent = contentSources.join('\n\n');
    }

    // Add form data context
    if (formData) {
      const formContext = [
        `Company: ${formData.companyName || formData.brandName || ''}`,
        `Industry: ${formData.industry || ''}`,
        `Business Type: ${formData.businessType || ''}`,
        `Target Audience: ${formData.targetAudience || ''}`,
        `Brand Values: ${formData.brandValues || ''}`,
        `Primary Goals: ${formData.primaryGoals || ''}`,
      ]
        .filter((line) => line.split(': ')[1])
        .join('\n');

      websiteTextContent = `${formContext}\n\n${websiteTextContent}`;
    }

    // Prepare content for analysis - combine website and brand guide data
    let finalContent = websiteTextContent;
    let brandGuideImages: string[] = [];

    // Add brand guide data if available (raw images + text for unified analysis)
    if (brandGuideData?.textContent || brandGuideData?.imageGcsUris) {
      console.log('[ImportBrandKitFromOnboarding] Including brand guide content for unified analysis...');

      // Add brand guide text content
      if (brandGuideData.textContent) {
        const brandGuideContext = `\n\nBRAND GUIDE CONTENT:\n${brandGuideData.textContent}`;
        finalContent = `${websiteTextContent}${brandGuideContext}`;
      }

      // Include brand guide images for visual analysis
      if (brandGuideData.imageGcsUris && Array.isArray(brandGuideData.imageGcsUris)) {
        brandGuideImages = brandGuideData.imageGcsUris;
        console.log(
          `[ImportBrandKitFromOnboarding] Including ${brandGuideImages.length} brand guide images for analysis`
        );
      }
    }

    if (!finalContent || finalContent.trim().length === 0) {
      throw new HttpError(400, 'No content available for brand kit analysis');
    }

    console.log('[ImportBrandKitFromOnboarding] Analyzing brand kit with Gemini...');
    console.log(`[ImportBrandKitFromOnboarding] Content length: ${finalContent.length} characters`);
    if (brandGuideData) {
      console.log(`[ImportBrandKitFromOnboarding] Brand guide metadata:`, brandGuideData.metadata);
    }

    // Use our Gemini analysis function with combined content
    const analyzedContent = await analyzeBrandKit(finalContent, brandGuideImages);

    console.log('[ImportBrandKitFromOnboarding] Creating brand kit with analyzed data...');

    // Extract website URL and use favicon for logo (reliable approach)
    const websiteUrl = websiteAnalysisData?.websiteUrl || formData?.websiteUrl || '';

    // Use favicon as logo - reliable and consistent approach
    const logoUrl = websiteUrl ? getFaviconUrl(websiteUrl) : '';

    console.log('🎨 Logo URL (Favicon):', {
      websiteUrl: websiteUrl,
      faviconLogoUrl: logoUrl,
    });

    // Create brand kit with analyzed content - ensure proper data types
    const brandKit = await context.entities.BrandKit.create({
      data: {
        name: name.trim(),
        userId: user.id,
        organizationId: organization.id,
        websiteUrl: websiteUrl,
        logoUrl: logoUrl,
        ...analyzedContent,
        // Ensure required arrays are initialized
        tags: [...(analyzedContent.tags || []), 'ai-generated', 'onboarding'],
        logoVariations: analyzedContent.logoVariations || [],
        moodboardImages: analyzedContent.moodboardImages || [],
        primaryColors: analyzedContent.primaryColors || [],
        secondaryColors: analyzedContent.secondaryColors || [],
        accentColors: analyzedContent.accentColors || [],

        // Convert arrays to strings for text fields
        brandValuesText: Array.isArray(analyzedContent.brandValuesText)
          ? analyzedContent.brandValuesText.join('\n\n')
          : analyzedContent.brandValuesText || '',
        targetEmotionsText: Array.isArray(analyzedContent.targetEmotionsText)
          ? analyzedContent.targetEmotionsText.join(', ')
          : analyzedContent.targetEmotionsText || '',
        preferredTermsText: Array.isArray(analyzedContent.preferredTermsText)
          ? analyzedContent.preferredTermsText.join(', ')
          : analyzedContent.preferredTermsText || '',
        avoidedTermsText: Array.isArray(analyzedContent.avoidedTermsText)
          ? analyzedContent.avoidedTermsText.join(', ')
          : analyzedContent.avoidedTermsText || '',
        avoidanceTermsText: Array.isArray(analyzedContent.avoidanceTermsText)
          ? analyzedContent.avoidanceTermsText.join(', ')
          : analyzedContent.avoidanceTermsText || '',

        // Convert logo guideline arrays to strings
        logoUsageGuidelinesText: Array.isArray(analyzedContent.logoUsageGuidelinesText)
          ? analyzedContent.logoUsageGuidelinesText.join('\n\n')
          : analyzedContent.logoUsageGuidelinesText || '',
        logoSizingText: Array.isArray(analyzedContent.logoSizingText)
          ? analyzedContent.logoSizingText.join('\n\n')
          : analyzedContent.logoSizingText || '',
        logoClearSpaceText: Array.isArray(analyzedContent.logoClearSpaceText)
          ? analyzedContent.logoClearSpaceText.join('\n\n')
          : analyzedContent.logoClearSpaceText || '',
        logoColorUsageText: Array.isArray(analyzedContent.logoColorUsageText)
          ? analyzedContent.logoColorUsageText.join('\n\n')
          : analyzedContent.logoColorUsageText || '',
        logoMisuseText: Array.isArray(analyzedContent.logoMisuseText)
          ? analyzedContent.logoMisuseText.join('\n\n')
          : analyzedContent.logoMisuseText || '',

        // Convert photography guideline arrays to strings
        preferredAnglesText: Array.isArray(analyzedContent.preferredAnglesText)
          ? analyzedContent.preferredAnglesText.join('\n\n')
          : analyzedContent.preferredAnglesText || '',
        lightingPreferencesText: Array.isArray(analyzedContent.lightingPreferencesText)
          ? analyzedContent.lightingPreferencesText.join('\n\n')
          : analyzedContent.lightingPreferencesText || '',
        backgroundStylesText: Array.isArray(analyzedContent.backgroundStylesText)
          ? analyzedContent.backgroundStylesText.join('\n\n')
          : analyzedContent.backgroundStylesText || '',
        propGuidelinesText: Array.isArray(analyzedContent.propGuidelinesText)
          ? analyzedContent.propGuidelinesText.join('\n\n')
          : analyzedContent.propGuidelinesText || '',
        commonScenesText: Array.isArray(analyzedContent.commonScenesText)
          ? analyzedContent.commonScenesText.join('\n\n')
          : analyzedContent.commonScenesText || '',
        secondaryTaglinesText: Array.isArray(analyzedContent.secondaryTaglinesText)
          ? analyzedContent.secondaryTaglinesText.join('\n\n')
          : analyzedContent.secondaryTaglinesText || '',
        keyMessagesText: Array.isArray(analyzedContent.keyMessagesText)
          ? analyzedContent.keyMessagesText.join('\n\n')
          : analyzedContent.keyMessagesText || '',
        valuePropositionsText: Array.isArray(analyzedContent.valuePropositionsText)
          ? analyzedContent.valuePropositionsText.join('\n\n')
          : analyzedContent.valuePropositionsText || '',
      },
    });

    console.log('[ImportBrandKitFromOnboarding] Brand kit created successfully:', brandKit.id);

    return {
      id: brandKit.id,
      name: brandKit.name,
      organizationId: brandKit.organizationId,
    };
  } catch (error) {
    console.error('[ImportBrandKitFromOnboarding] Failed to create brand kit:', error);

    if (error instanceof HttpError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    throw new HttpError(500, `Failed to create brand kit: ${errorMessage}`);
  }
};
