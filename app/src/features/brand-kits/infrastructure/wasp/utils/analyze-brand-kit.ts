/**
 * 🤖 Brand Kit Analysis Utilities
 *
 * @description Utilities for analyzing brand kit content using AI
 * @responsibility Handles AI-powered analysis of text and images for brand kit creation
 * @ai_context This utility provides AI analysis for brand kit import functionality using Gemini 2.5 Pro Preview
 */

import { GoogleGenAI } from '@google/genai';

// Initialize Vertex AI with the same approach as audience import
const ai = new GoogleGenAI({
  vertexai: true,
  project: process.env.GOOGLE_CLOUD_PROJECT!,
  location: 'global',
});

const model = 'gemini-2.5-pro-preview-06-05';

// Generation config for brand kit analysis
const generationConfig = {
  maxOutputTokens: 8192, // Sufficient for comprehensive brand analysis
  temperature: 0.3, // Low temperature for consistent, factual analysis
  topP: 0.8,
  responseMimeType: 'application/json',
};

/**
 * 📝 Analyze Brand Kit Content
 * @description Analyzes text and images to extract comprehensive brand kit information using Gemini AI
 * @param text Extracted text content from website or document
 * @param images Array of image URLs, GCS URIs (gs://), or base64 data (optional)
 * @returns Analyzed brand kit content matching database schema
 */
export async function analyzeBrandKit(text: string, images: string[] = []): Promise<any> {
  try {
    console.log('Analyzing brand kit content with Gemini AI...');
    console.log(`Content length: ${text.length} characters, Images: ${images.length}`);

    if (!text || text.trim().length === 0) {
      if (images.length === 0) {
        throw new Error('No text content or images provided for analysis');
      }
      // If no text but we have images, use a minimal text prompt
      text = 'Analyze the provided images for brand information.';
    }

    // Build comprehensive analysis prompt with image context
    const prompt = buildBrandKitAnalysisPrompt(text, images.length > 0);

    // Prepare content for Gemini - images first, then text (best practice)
    const contentParts: any[] = [];

    // Add images first (Gemini best practice for multimodal)
    if (images && images.length > 0) {
      console.log(`Processing ${images.length} images for analysis...`);

      images.forEach((image, index) => {
        if (image.startsWith('data:image/')) {
          // Handle base64 data URLs
          const base64Data = image.split(',')[1];
          const mimeType = image.split(';')[0].split(':')[1];
          contentParts.push({
            inlineData: {
              mimeType: mimeType,
              data: base64Data,
            },
          });
          console.log(`Added image ${index + 1} (${mimeType}, base64) for analysis`);
        } else if (image.startsWith('gs://')) {
          // Handle Google Cloud Storage URIs - Gemini can access GCS directly
          const mimeType = image.includes('.png') ? 'image/png' : 'image/jpeg';
          contentParts.push({
            fileData: {
              mimeType: mimeType,
              fileUri: image,
            },
          });
          console.log(`Added image ${index + 1} (${mimeType}, GCS URI) for analysis: ${image}`);
        } else if (image.startsWith('http')) {
          // Handle R2 URLs - Gemini can access public URLs
          contentParts.push({
            fileData: {
              mimeType: 'image/png', // Default to PNG, could be enhanced to detect
              fileUri: image,
            },
          });
          console.log(`Added image ${index + 1} (URL) for analysis`);
        }
      });
    }

    // Add text prompt after images
    contentParts.push({ text: prompt });

    console.log('Sending request to Gemini for brand kit analysis...');
    const req = {
      model: model,
      contents: [
        {
          role: 'user',
          parts: contentParts,
        },
      ],
      config: generationConfig,
    };

    const response = await ai.models.generateContent(req as any);

    if (!response?.candidates?.[0]?.content?.parts?.[0]?.text) {
      throw new Error('Invalid response from Gemini - no content generated');
    }

    const responseText = response.candidates[0].content.parts[0].text;
    console.log('Received response from Gemini, parsing...');

    try {
      const analyzedContent = JSON.parse(responseText);

      // Log the raw AI response for debugging
      console.log('🤖 Raw AI Analysis Response:', JSON.stringify(analyzedContent, null, 2));

      // Validate and structure the response
      const brandKitContent = structureBrandKitContent(analyzedContent, images);

      // Log the structured brand kit content for debugging
      console.log(
        '🏗️ Structured Brand Kit Content:',
        JSON.stringify(
          {
            primaryColors: brandKitContent.primaryColors,
            secondaryColors: brandKitContent.secondaryColors,
            accentColors: brandKitContent.accentColors,
            tags: brandKitContent.tags,
          },
          null,
          2
        )
      );

      console.log('Brand kit analysis completed successfully');
      return brandKitContent;
    } catch (parseError) {
      console.error('Failed to parse Gemini response:', parseError);
      console.error('Raw response:', responseText);
      throw new Error(
        `Failed to parse Gemini response: ${parseError instanceof Error ? parseError.message : 'Unknown parsing error'}`
      );
    }
  } catch (error) {
    console.error('Error analyzing brand kit content:', error);
    throw error; // Re-throw the error to fail fast
  }
}

/**
 * 🏗️ Build comprehensive brand kit analysis prompt
 */
function buildBrandKitAnalysisPrompt(text: string, hasImages: boolean = false): string {
  const imageContext = hasImages
    ? `\n\nIMAGES PROVIDED:\nI have also provided brand guide images/pages that contain visual brand information. Please analyze both the text content below AND the provided images to extract comprehensive brand information. Pay special attention to colors, typography, logos, and visual elements shown in the images.`
    : '';

  return `Analyze the following website/document content${hasImages ? ' and provided images' : ''} and extract comprehensive brand information.${imageContext}

CONTENT TO ANALYZE:
${text.substring(0, 50000)} // Limit content size

Extract and analyze the following brand elements:

1. **VISUAL IDENTITY**${hasImages ? ' (analyze both text and provided images)' : ''}
   - Primary colors (hex codes) - main brand colors${hasImages ? ' (extract from images if visible)' : ''}
   - Secondary colors (hex codes) - supporting colors${hasImages ? ' (extract from images if visible)' : ''}
   - Accent colors (hex codes) - highlight/call-to-action colors${hasImages ? ' (extract from images if visible)' : ''}
   - Typography information (font families, styles, hierarchy)${hasImages ? ' (identify fonts shown in images)' : ''}
   - Visual style and design patterns${hasImages ? ' (analyze visual elements in images)' : ''}

2. **BRAND VOICE & PERSONALITY**
   - Brand personality traits and characteristics
   - Tone of voice (formal, casual, friendly, authoritative, etc.)
   - Brand values and core beliefs
   - Target emotions the brand wants to evoke

3. **LANGUAGE & COMMUNICATION**
   - Writing style and communication approach
   - Preferred terminology and language choices
   - Terms or phrases to avoid
   - Key messages and value propositions

4. **TAGLINES & MESSAGING**
   - Primary taglines or slogans
   - Key marketing messages
   - Value propositions
   - Brand positioning statements

5. **LOGO GUIDELINES** (if logo is visible or mentioned)
   - Logo usage guidelines and best practices
   - Logo sizing requirements and minimum sizes
   - Clear space requirements around the logo
   - Logo color variations and usage contexts
   - Logo misuse examples and what to avoid

6. **PHOTOGRAPHY GUIDELINES**
   - Photography style and aesthetic preferences
   - Preferred camera angles and perspectives
   - Lighting preferences (natural, studio, etc.)
   - Background styles and settings
   - Props to use or avoid
   - Common scenes and scenarios

7. **VISUAL GUIDELINES**
   - Image composition and aesthetic
   - Overall visual design approach

Return a comprehensive JSON object with the following structure:

{
  "visualIdentity": {
    "primaryColors": ["#hex1", "#hex2", "#hex3"],
    "secondaryColors": ["#hex1", "#hex2", "#hex3"],
    "accentColors": ["#hex1", "#hex2"],
    "typography": {
      "headings": {
        "fontFamily": "Font Name",
        "weights": ["400", "600", "700"],
        "sizes": {
          "h1": "48px",
          "h2": "36px",
          "h3": "24px",
          "h4": "20px",
          "h5": "18px",
          "h6": "16px"
        }
      },
      "body": {
        "fontFamily": "Font Name",
        "weights": ["400", "500"],
        "baseSize": "16px",
        "lineHeight": "1.5"
      }
    },
    "colorUsage": {
      "primary": "Description of when/how to use primary colors",
      "secondary": "Description of when/how to use secondary colors",
      "accent": "Description of when/how to use accent colors"
    },
    "colorAccessibility": "Guidelines for color accessibility, contrast ratios, and inclusive design practices"
  },
  "brandVoice": {
    "personality": "Detailed description of brand personality",
    "tonality": "Description of tone of voice",
    "values": "Core brand values and beliefs",
    "targetEmotions": "Emotions the brand wants to evoke"
  },
  "language": {
    "writingStyle": "Description of writing style and approach",
    "preferredTerms": "Preferred terminology and phrases",
    "avoidedTerms": "Terms or phrases to avoid",
    "keyMessages": "Primary marketing messages",
    "valuePropositions": "Core value propositions",
    "grammarRules": "Specific grammar rules, punctuation preferences, and writing conventions"
  },
  "taglines": {
    "primary": "Main tagline or slogan",
    "secondary": "Secondary taglines",
    "keyMessages": "Key brand messages",
    "guidelines": "Guidelines for creating and using taglines, including tone, length, and context"
  },
  "logoGuidelines": {
    "usage": "General logo usage guidelines and best practices",
    "sizing": "Logo sizing requirements, minimum sizes, and scaling guidelines",
    "clearSpace": "Clear space requirements and spacing around the logo",
    "colorUsage": "Logo color variations and when to use each version",
    "misuse": "Examples of logo misuse and what to avoid"
  },
  "photographyGuidelines": {
    "style": "Photography style and aesthetic preferences",
    "preferredAngles": "Preferred camera angles and perspectives",
    "lighting": "Lighting preferences and requirements",
    "backgrounds": "Background styles and settings",
    "props": "Props to use or avoid in photography",
    "commonScenes": "Common scenes and scenarios for brand photography"
  },
  "visualGuidelines": {
    "designApproach": "Overall visual design approach"
  }
}

Analyze the content thoroughly and extract as much relevant brand information as possible. If certain information is not available in the content, provide reasonable inferences based on the overall brand context, or leave fields empty if no reasonable inference can be made.

Focus on extracting actionable brand guidelines that would help maintain consistency across all brand communications and visual materials.`;
}

/**
 * 🏗️ Structure brand kit content from AI response
 */
function structureBrandKitContent(analyzedContent: any, images: string[] = []): any {
  const visual = analyzedContent.visualIdentity || {};
  const voice = analyzedContent.brandVoice || {};
  const language = analyzedContent.language || {};
  const taglines = analyzedContent.taglines || {};
  const logoGuidelines = analyzedContent.logoGuidelines || {};
  const photographyGuidelines = analyzedContent.photographyGuidelines || {};
  const guidelines = analyzedContent.visualGuidelines || {};

  return {
    // Basic fields
    tags: ['ai-generated'],
    logoVariations: [],
    moodboardImages: images || [],

    // Colors
    primaryColors: visual.primaryColors || [],
    secondaryColors: visual.secondaryColors || [],
    accentColors: visual.accentColors || [],

    // Color usage guidelines
    primaryColorUsageText: visual.colorUsage?.primary || '',
    secondaryColorUsageText: visual.colorUsage?.secondary || '',
    accentColorUsageText: visual.colorUsage?.accent || '',
    colorHierarchyText: '',
    colorAccessibilityText: visual.colorAccessibility || '',
    colorCombinationsText: '',
    colorVariationsText: '',

    // Typography
    typography: visual.typography || {},

    // Logo Guidelines
    logoUsageGuidelinesText: logoGuidelines.usage || '',
    logoSizingText: logoGuidelines.sizing || '',
    logoClearSpaceText: logoGuidelines.clearSpace || '',
    logoColorUsageText: logoGuidelines.colorUsage || '',
    logoFileFormatsText: '',
    logoMisuseText: logoGuidelines.misuse || '',

    // Photography Guidelines
    photoStyleText: photographyGuidelines.style || '',
    preferredAnglesText: photographyGuidelines.preferredAngles || '',
    lightingPreferencesText: photographyGuidelines.lighting || '',
    backgroundStylesText: photographyGuidelines.backgrounds || '',
    propGuidelinesText: photographyGuidelines.props || '',
    commonScenesText: photographyGuidelines.commonScenes || '',
    photoDosDonts: { dos: '', donts: '' },

    // Brand Voice
    brandPersonalityText: voice.personality || '',
    tonalityText: voice.tonality || '',
    brandValuesText: voice.values || '',
    targetEmotionsText: voice.targetEmotions || '',

    // Language & Phrasing
    writingStyleText: language.writingStyle || '',
    preferredTermsText: language.preferredTerms || '',
    avoidedTermsText: language.avoidedTerms || '',
    grammarRulesText: language.grammarRules || '',
    contentStructureText: '',
    localizationRulesText: '',
    commonMistakesText: '',

    // Taglines
    primaryTaglinesText: taglines.primary || '',
    secondaryTaglinesText: taglines.secondary || '',
    campaignTaglinesText: '',
    keyMessagesText: language.keyMessages || taglines.keyMessages || '',
    valuePropositionsText: language.valuePropositions || '',
    taglineGuidelinesText: taglines.guidelines || '',
    trademarkInfoText: '',

    // Target Audience (basic placeholders)
    primaryAudienceText: '',
    primaryAudienceGoalsText: '',
    primaryAudienceChallengesText: '',
    secondaryAudienceText: '',
    secondaryAudienceGoalsText: '',
    secondaryAudienceChallengesText: '',
    tertiaryAudienceText: '',
    tertiaryAudienceGoalsText: '',
    audiencePersonasText: '',
    audienceValuePropsText: '',
    communicationChannelsText: '',
    audienceMetricsText: '',

    // Web Design
    webTypographyText: '',
    webSpacingText: '',
    uiComponentExamplesText: '',
    uiComponentsText: '',
    buttonStylesText: '',
    formGuidelinesText: '',
    responsiveGuidelinesText: '',
    webAccessibilityText: '',
    performanceGuidelinesText: '',
    animationGuidelinesText: '',

    // Social Media
    platformGuidelinesText: '',
    socialMediaExamplesText: '',
    visualStyleGuidelinesText: guidelines.designApproach || '',
    contentTypesText: '',
    socialToneGuidelinesText: voice.tonality || '',
    hashtagGuidelinesText: '',
    engagementGuidelinesText: '',
    postingScheduleText: '',
    socialComplianceText: '',
    analyticsGuidelinesText: '',

    // AI Generation Settings
    promptKeywordsText: extractKeywords(voice.personality, language.preferredTerms),
    avoidanceTermsText: language.avoidedTerms || '',
    preferredSettings: {
      quality: 'high',
      style: guidelines.designApproach || 'professional',
      format: 'jpg',
    },
  };
}

/**
 * 🔄 Extract keywords for AI generation
 */
function extractKeywords(personality?: string, preferredTerms?: string): string {
  const keywords: string[] = [];

  if (personality) {
    keywords.push(personality);
  }

  if (preferredTerms) {
    keywords.push(preferredTerms);
  }

  return keywords.join(', ');
}

// No fallbacks - we fail fast! 🔥
