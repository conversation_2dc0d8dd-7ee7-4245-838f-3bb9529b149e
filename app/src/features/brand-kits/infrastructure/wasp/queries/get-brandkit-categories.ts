/**
 * 📂 Get Brand Kit Categories Query
 *
 * @description WASP query for fetching brand kit categories
 * @responsibility Handles category retrieval with proper validation and access control
 * @dependencies BrandKitService, BrandKitRepository, authentication
 * @ai_context This query integrates the new modular brand kit domain with WASP's query system
 *
 * @example
 * ```typescript
 * const categories = await getBrandkitCategories();
 * ```
 */

import { type GetBrandkitCategories } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { authenticateUser } from '../../../../../server/helpers';
import { z } from 'zod';

/**
 * 📝 Get Brand Kit Categories Input Schema
 * @ai_context Validation schema for category retrieval (no input required)
 */
const GetBrandkitCategoriesInputSchema = z.object({}).optional();

export type GetBrandkitCategoriesInput = z.infer<typeof GetBrandkitCategoriesInputSchema> | undefined;

/**
 * 📤 Get Brand Kit Categories Output
 * @ai_context Response format for brand kit categories
 */
export interface BrandCategoryItem {
  id: number;
  name: string;
  description: string;
  fields?: CategoryFieldItem[];
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
  [key: string]: any; // Index signature for SuperJSON compatibility
}

export interface CategoryFieldItem {
  id: number;
  name: string;
  fieldType: string;
  required: boolean;
  limit: number;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
  [key: string]: any; // Index signature for SuperJSON compatibility
}

export type GetBrandkitCategoriesOutput = BrandCategoryItem[];

/**
 * 📂 Get Brand Kit Categories Query
 * @ai_context WASP query implementation for fetching brand kit categories
 */
export const getBrandkitCategories: GetBrandkitCategories<
  GetBrandkitCategoriesInput,
  GetBrandkitCategoriesOutput
> = async (args, context) => {
  // Authenticate user
  const user = authenticateUser(context);

  try {
    // Validate input (optional)
    if (args) {
      GetBrandkitCategoriesInputSchema.parse(args);
    }

    // Fetch brand kit categories from database
    const categories = await context.entities.BrandCategory.findMany({
      include: {
        categoryFields: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    // Transform the data to match expected format
    const transformedCategories: BrandCategoryItem[] = categories.map((category) => ({
      id: category.id,
      name: category.name,
      description: category.description,
      fields: category.categoryFields?.map((field) => ({
        id: field.id,
        name: field.name,
        fieldType: field.fieldType,
        required: field.required,
        limit: field.limit,
        createdAt: field.createdAt,
        updatedAt: field.updatedAt,
        deletedAt: field.deletedAt,
      })),
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
      deletedAt: category.deletedAt,
    }));

    return transformedCategories;
  } catch (error) {
    console.error('[GetBrandkitCategories] Error fetching categories:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((err) => `${err.path.join('.')}: ${err.message}`);
      throw new HttpError(400, `Validation error: ${errorMessages.join(', ')}`);
    }

    // Re-throw HTTP errors
    if (error instanceof HttpError) {
      throw error;
    }

    // Handle other errors
    if (error instanceof Error) {
      throw new HttpError(400, error.message);
    }

    // Generic server error
    throw new HttpError(500, 'An unexpected error occurred while fetching brand kit categories');
  }
};
