/**
 * 🎨 Get Brand Kit Data For Project Query
 *
 * @description WASP query for fetching brand kit data specifically for project use
 * @responsibility Handles brand kit data retrieval for project integration
 * @dependencies BrandKitService, BrandKitRepository, authentication
 * @ai_context This query integrates the new modular brand kit domain with WASP's query system
 *
 * @example
 * ```typescript
 * const brandKitData = await getBrandKitDataForProject({ projectId: "project_123" });
 * ```
 */

import { type GetBrandKitDataForProject } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { authenticateUser } from '../../../../../server/helpers';
import { z } from 'zod';

/**
 * 📝 Get Brand Kit Data For Project Input Schema
 * @ai_context Validation schema for project brand kit data retrieval
 */
const GetBrandKitDataForProjectInputSchema = z.object({
  projectId: z.string().min(1, 'Project ID is required'),
});

export type GetBrandKitDataForProjectInput = z.infer<typeof GetBrandKitDataForProjectInputSchema>;

/**
 * 📤 Get Brand Kit Data For Project Output
 * @ai_context Response format for project brand kit data
 */
export interface ProjectBrandKitData {
  id: string;
  name: string;

  // Visual Identity (essential for projects)
  primaryColors: string[];
  secondaryColors: string[];
  accentColors: string[];
  typography: any;
  logoVariations: string[];

  // Brand Voice (for content generation)
  brandPersonalityText: string;
  tonalityText: string;
  brandValuesText: string;
  writingStyleText: string;

  // AI Generation Settings (for project AI features)
  promptKeywordsText: string;
  avoidanceTermsText: string;
  preferredSettings: any;

  // Photography Guidelines (for image generation)
  photoStyleText: string;
  preferredAnglesText: string;
  lightingPreferencesText: string;

  // Project metadata
  organizationId: string;
  createdAt: Date;
  updatedAt: Date;

  [key: string]: any; // Index signature for SuperJSON compatibility
}

export type GetBrandKitDataForProjectOutput = ProjectBrandKitData | null;

/**
 * 🎨 Get Brand Kit Data For Project Query
 * @ai_context WASP query implementation for fetching brand kit data for projects
 */
export const getBrandKitDataForProject: GetBrandKitDataForProject<
  GetBrandKitDataForProjectInput,
  GetBrandKitDataForProjectOutput
> = async (args, context) => {
  // Authenticate user
  const user = authenticateUser(context);

  try {
    // Validate input
    const validatedInput = GetBrandKitDataForProjectInputSchema.parse(args);

    // Fetch project to get associated brand kit
    const project = await context.entities.Project.findUnique({
      where: { id: validatedInput.projectId },
      include: {
        brandKit: true,
      },
    });

    if (!project) {
      throw new HttpError(404, 'Project not found');
    }

    // Check if user has access to the project
    if (project.userId !== user.id) {
      throw new HttpError(403, 'You do not have permission to access this project');
    }

    // If no brand kit is associated with the project, return null
    if (!project.brandKit || project.brandKit.deletedAt) {
      return null;
    }

    // Transform brand kit data for project use
    const projectBrandKitData: ProjectBrandKitData = {
      id: project.brandKit.id,
      name: project.brandKit.name,

      // Visual Identity
      primaryColors: Array.isArray(project.brandKit.primaryColors) ? (project.brandKit.primaryColors as string[]) : [],
      secondaryColors: Array.isArray(project.brandKit.secondaryColors)
        ? (project.brandKit.secondaryColors as string[])
        : [],
      accentColors: Array.isArray(project.brandKit.accentColors) ? (project.brandKit.accentColors as string[]) : [],
      typography: project.brandKit.typography || {},
      logoVariations: Array.isArray(project.brandKit.logoVariations)
        ? (project.brandKit.logoVariations as string[])
        : [],

      // Brand Voice
      brandPersonalityText: project.brandKit.brandPersonalityText || '',
      tonalityText: project.brandKit.tonalityText || '',
      brandValuesText: project.brandKit.brandValuesText || '',
      writingStyleText: project.brandKit.writingStyleText || '',

      // AI Generation Settings
      promptKeywordsText: project.brandKit.promptKeywordsText || '',
      avoidanceTermsText: project.brandKit.avoidanceTermsText || '',
      preferredSettings: project.brandKit.preferredSettings || {},

      // Photography Guidelines
      photoStyleText: project.brandKit.photoStyleText || '',
      preferredAnglesText: project.brandKit.preferredAnglesText || '',
      lightingPreferencesText: project.brandKit.lightingPreferencesText || '',

      // Metadata
      organizationId: project.brandKit.organizationId,
      createdAt: project.brandKit.createdAt,
      updatedAt: project.brandKit.updatedAt,
    };

    return projectBrandKitData;
  } catch (error) {
    console.error('[GetBrandKitDataForProject] Error fetching brand kit data for project:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((err) => `${err.path.join('.')}: ${err.message}`);
      throw new HttpError(400, `Validation error: ${errorMessages.join(', ')}`);
    }

    // Re-throw HTTP errors
    if (error instanceof HttpError) {
      throw error;
    }

    // Handle other errors
    if (error instanceof Error) {
      throw new HttpError(400, error.message);
    }

    // Generic server error
    throw new HttpError(500, 'An unexpected error occurred while fetching brand kit data for project');
  }
};
