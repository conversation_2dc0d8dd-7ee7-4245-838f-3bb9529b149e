/**
 * 🔍 Get Brand Kit By ID Query
 *
 * @description WASP query for fetching a single brand kit by ID
 * @responsibility Handles brand kit retrieval with proper validation and access control
 * @dependencies BrandKitService, BrandKitRepository, authentication
 * @ai_context This query integrates the new modular brand kit domain with WASP's query system
 *
 * @example
 * ```typescript
 * const brandKit = await getBrandKitById({ id: "brandkit_123" });
 * ```
 */

import { type GetBrandKitById } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { authenticateUser } from '../../../../../server/helpers';
import { z } from 'zod';

/**
 * 📝 Get Brand Kit By ID Input Schema
 * @ai_context Validation schema for brand kit retrieval
 */
const GetBrandKitByIdInputSchema = z.object({
  id: z.string().min(1, 'Brand kit ID is required'),
});

export type GetBrandKitByIdInput = z.infer<typeof GetBrandKitByIdInputSchema>;

/**
 * 📤 Get Brand Kit By ID Output
 * @ai_context Response format for brand kit retrieval - matches existing query output
 */
export interface GetBrandKitByIdOutput {
  id: string;
  name: string;
  userId: string | number;
  organizationId: string;
  tags: string[];

  // Visual Identity
  primaryColors: string[];
  secondaryColors: string[];
  accentColors: string[];
  primaryColorUsageText: string;
  secondaryColorUsageText: string;
  accentColorUsageText: string;
  colorHierarchyText: string;
  colorAccessibilityText: string;
  colorCombinationsText: string;
  colorVariationsText: string;
  typography: any;
  logoVariations: string[];
  moodboardImages: string[];

  // Logo Guidelines
  logoUsageGuidelinesText: string;
  logoSizingText: string;
  logoClearSpaceText: string;
  logoColorUsageText: string;
  logoFileFormatsText: string;
  logoMisuseText: string;

  // Photography Guidelines
  photoStyleText: string;
  preferredAnglesText: string;
  lightingPreferencesText: string;
  backgroundStylesText: string;
  propGuidelinesText: string;
  commonScenesText: string;
  photoDosDonts: any;

  // Brand Voice
  brandPersonalityText: string;
  tonalityText: string;
  brandValuesText: string;
  targetEmotionsText: string;

  // Language & Phrasing
  writingStyleText: string;
  preferredTermsText: string;
  avoidedTermsText: string;
  grammarRulesText: string;
  contentStructureText: string;
  localizationRulesText: string;
  commonMistakesText: string;

  // Taglines
  primaryTaglinesText: string;
  secondaryTaglinesText: string;
  campaignTaglinesText: string;
  keyMessagesText: string;
  valuePropositionsText: string;
  taglineGuidelinesText: string;
  trademarkInfoText: string;

  // Target Audience
  primaryAudienceText: string;
  primaryAudienceGoalsText: string;
  primaryAudienceChallengesText: string;
  secondaryAudienceText: string;
  secondaryAudienceGoalsText: string;
  secondaryAudienceChallengesText: string;
  tertiaryAudienceText: string;
  tertiaryAudienceGoalsText: string;
  audiencePersonasText: string;
  audienceValuePropsText: string;
  communicationChannelsText: string;
  audienceMetricsText: string;

  // Data Visualization
  preferredChartTypesText: string;
  chartExamplesText: string;
  dataVizColorsText: string;
  dataVizTypographyText: string;
  dataVizLayoutText: string;
  dataVizInteractivityText: string;
  dataVizAccessibilityText: string;
  dataVizBestPracticesText: string;
  dataVizMistakesText: string;

  // Web Design
  webTypographyText: string;
  webSpacingText: string;
  uiComponentExamplesText: string;
  uiComponentsText: string;
  buttonStylesText: string;
  formGuidelinesText: string;
  responsiveGuidelinesText: string;
  webAccessibilityText: string;
  performanceGuidelinesText: string;
  animationGuidelinesText: string;

  // Social Media
  platformGuidelinesText: string;
  socialMediaExamplesText: string;
  visualStyleGuidelinesText: string;
  contentTypesText: string;
  socialToneGuidelinesText: string;
  hashtagGuidelinesText: string;
  engagementGuidelinesText: string;
  postingScheduleText: string;
  socialComplianceText: string;
  analyticsGuidelinesText: string;

  // AI Generation Settings
  promptKeywordsText: string;
  avoidanceTermsText: string;
  preferredSettings: any;

  // Metadata
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;

  [key: string]: any; // Index signature for SuperJSON compatibility
}

/**
 * 🔍 Get Brand Kit By ID Query
 * @ai_context WASP query implementation for fetching brand kits
 */
export const getBrandKitById: GetBrandKitById<GetBrandKitByIdInput, GetBrandKitByIdOutput> = async (args, context) => {
  // Authenticate user
  const user = authenticateUser(context);

  try {
    // Validate input
    const validatedInput = GetBrandKitByIdInputSchema.parse(args);

    // Fetch brand kit from database
    const brandKit = await context.entities.BrandKit.findUnique({
      where: {
        id: validatedInput.id,
        deletedAt: null, // Only get non-deleted brand kits
      },
    });

    if (!brandKit) {
      throw new HttpError(404, 'Brand kit not found');
    }

    // Check access permissions
    if (String(brandKit.userId) !== String(user.id)) {
      throw new HttpError(403, 'You do not have permission to access this brand kit');
    }

    // Return brand kit data (already in the correct format)
    return brandKit as GetBrandKitByIdOutput;
  } catch (error) {
    console.error('[GetBrandKitById] Error fetching brand kit:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((err) => `${err.path.join('.')}: ${err.message}`);
      throw new HttpError(400, `Validation error: ${errorMessages.join(', ')}`);
    }

    // Re-throw HTTP errors
    if (error instanceof HttpError) {
      throw error;
    }

    // Handle other errors
    if (error instanceof Error) {
      throw new HttpError(400, error.message);
    }

    // Generic server error
    throw new HttpError(500, 'An unexpected error occurred while fetching the brand kit');
  }
};
