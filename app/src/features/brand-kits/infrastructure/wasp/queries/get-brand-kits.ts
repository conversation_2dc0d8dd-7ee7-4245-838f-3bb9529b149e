/**
 * 📋 Get Brand Kits Query
 *
 * @description WASP query for fetching brand kits list by organization
 * @responsibility Handles brand kit listing with proper validation and access control
 * @dependencies BrandKitService, BrandKitRepository, authentication
 * @ai_context This query integrates the new modular brand kit domain with WASP's query system
 *
 * @example
 * ```typescript
 * const brandKits = await getBrandKits({ organizationId: "org_123" });
 * ```
 */

import { type GetBrandKits } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { authenticateUser } from '../../../../../server/helpers';
import { z } from 'zod';

/**
 * 📝 Get Brand Kits Input Schema
 * @ai_context Validation schema for brand kits listing
 */
const GetBrandKitsInputSchema = z.object({
  organizationId: z.string().min(1, 'Organization ID is required'),
});

export type GetBrandKitsInput = z.infer<typeof GetBrandKitsInputSchema>;

/**
 * 📤 Get Brand Kits Output
 * @ai_context Response format for brand kits listing - matches existing query output
 */
export interface BrandKitListItem {
  id: string;
  name: string;
  userId: string;
  organizationId: string;
  tags: string[];
  logoUrl?: string | null;
  websiteUrl?: string | null;

  // Metadata
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;

  [key: string]: any; // Index signature for SuperJSON compatibility
}

export type GetBrandKitsOutput = BrandKitListItem[];

/**
 * 📋 Get Brand Kits Query
 * @ai_context WASP query implementation for fetching brand kits list
 */
export const getBrandKits: GetBrandKits<GetBrandKitsInput, GetBrandKitsOutput> = async (args, context) => {
  // Authenticate user
  const user = authenticateUser(context);

  try {
    // Validate input
    const validatedInput = GetBrandKitsInputSchema.parse(args);

    // Verify organization access
    const organization = await context.entities.Organization.findUnique({
      where: { id: validatedInput.organizationId },
      include: {
        memberships: {
          where: { userId: user.id },
        },
      },
    });

    if (!organization) {
      throw new HttpError(404, 'Organization not found');
    }

    if (organization.memberships.length === 0) {
      throw new HttpError(403, 'You do not have access to this organization');
    }

    // Fetch brand kits from database
    const brandKits = await context.entities.BrandKit.findMany({
      where: {
        organizationId: validatedInput.organizationId,
        deletedAt: null, // Only get non-deleted brand kits
      },
      select: {
        id: true,
        name: true,
        userId: true,
        organizationId: true,
        tags: true,
        logoUrl: true,
        websiteUrl: true,
        logoVariations: true, // We'll extract the first logo as logoUrl fallback
        createdAt: true,
        updatedAt: true,
        deletedAt: true,
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });

    // Transform the data to match expected format
    const transformedBrandKits: BrandKitListItem[] = brandKits.map((brandKit) => ({
      id: brandKit.id,
      name: brandKit.name,
      userId: brandKit.userId,
      organizationId: brandKit.organizationId,
      tags: brandKit.tags,
      logoUrl:
        brandKit.logoUrl ||
        (Array.isArray(brandKit.logoVariations) && brandKit.logoVariations.length > 0
          ? (brandKit.logoVariations[0] as string)
          : null),
      websiteUrl: brandKit.websiteUrl || null,
      createdAt: brandKit.createdAt,
      updatedAt: brandKit.updatedAt,
      deletedAt: brandKit.deletedAt,
    }));

    return transformedBrandKits;
  } catch (error) {
    console.error('[GetBrandKits] Error fetching brand kits:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((err) => `${err.path.join('.')}: ${err.message}`);
      throw new HttpError(400, `Validation error: ${errorMessages.join(', ')}`);
    }

    // Re-throw HTTP errors
    if (error instanceof HttpError) {
      throw error;
    }

    // Handle other errors
    if (error instanceof Error) {
      throw new HttpError(400, error.message);
    }

    // Generic server error
    throw new HttpError(500, 'An unexpected error occurred while fetching brand kits');
  }
};
