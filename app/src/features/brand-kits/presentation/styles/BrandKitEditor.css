/* Brand Kit Editor Custom Styles - Onboarding Style */

/* Main Layout */
.brand-kit-editor {
  display: flex;
  height: 100vh;
  background-color: #F0EFE9;
  font-family: 'Inter', system-ui, -apple-system, 'Segoe UI', sans-serif;
  position: relative;
}

.brand-kit-editor.dark {
  background-color: #000000;
}

/* Background texture like onboarding */
.brand-kit-editor::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-image: url('/images/textures/texture-header-02-HOR-light.gif');
  background-repeat: no-repeat;
  background-position: center top;
  opacity: 0.1;
  pointer-events: none;
  z-index: 1;
}

/* Loading State */
.brand-kit-loading {
  min-height: 100vh;
  background-color: #F8F4DF;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-kit-loading.dark {
  background-color: #111827;
}

.brand-kit-loading-content {
  text-align: center;
}

.brand-kit-loading-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto 1rem;
  background: linear-gradient(135deg, #676D50, #849068);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

.brand-kit-loading-icon-inner {
  width: 2rem;
  height: 2rem;
  background-color: #F8F4DF;
  border-radius: 0.5rem;
}

.brand-kit-loading-text {
  color: #676D50;
  font-weight: 500;
}

.brand-kit-loading.dark .brand-kit-loading-text {
  color: white;
}

/* Sidebar - Onboarding Style */
.brand-kit-sidebar {
  width: 20rem;
  background: rgba(132, 144, 104, 0.95);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(181, 177, 120, 0.3);
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  position: relative;
  z-index: 10;
}

.brand-kit-sidebar.dark {
  background: rgba(31, 41, 55, 0.95);
  border-right-color: rgba(75, 85, 99, 0.5);
}

.brand-kit-sidebar-content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.brand-kit-sidebar-header {
  margin-bottom: 1.5rem;
  flex-shrink: 0;
}

.brand-kit-sidebar-title {
  font-family: 'Drephonic', 'Georgia', serif;
  font-size: 1.25rem;
  font-weight: 700;
  color: #F8F4DF;
  margin-bottom: 0.5rem;
}

.brand-kit-sidebar.dark .brand-kit-sidebar-title {
  color: white;
}

.brand-kit-sidebar-subtitle {
  color: rgba(248, 244, 223, 0.7);
  font-size: 0.875rem;
}

.brand-kit-sidebar.dark .brand-kit-sidebar-subtitle {
  color: rgba(209, 213, 219, 1);
}

.brand-kit-sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
  overflow-y: auto;
  padding-right: 0.5rem;
}

/* Custom scrollbar for sidebar */
.brand-kit-sidebar-nav::-webkit-scrollbar {
  width: 6px;
}

.brand-kit-sidebar-nav::-webkit-scrollbar-track {
  background: rgba(103, 109, 80, 0.1);
  border-radius: 3px;
}

.brand-kit-sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(248, 244, 223, 0.3);
  border-radius: 3px;
}

.brand-kit-sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(248, 244, 223, 0.5);
}

.brand-kit-sidebar-button {
  width: 100%;
  text-align: left;
  padding: 1rem;
  border-radius: 0.75rem;
  transition: all 0.2s ease;
  border: none;
  background: none;
  cursor: pointer;
  transform: scale(1);
}

.brand-kit-sidebar-button:hover {
  background-color: rgba(103, 109, 80, 0.2);
  transform: scale(1.01);
}

.brand-kit-sidebar-button.active {
  background-color: #F8F4DF;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: scale(1.02);
}

.brand-kit-sidebar-button-title {
  font-weight: 500;
  font-size: 0.875rem;
  color: #F8F4DF;
}

.brand-kit-sidebar-button.active .brand-kit-sidebar-button-title {
  color: #676D50;
}

.brand-kit-sidebar-button:hover .brand-kit-sidebar-button-title {
  color: white;
}

.brand-kit-sidebar-button-description {
  font-size: 0.75rem;
  margin-top: 0.25rem;
  color: rgba(248, 244, 223, 0.7);
}

.brand-kit-sidebar-button.active .brand-kit-sidebar-button-description {
  color: rgba(103, 109, 80, 0.7);
}

.brand-kit-sidebar-button:hover .brand-kit-sidebar-button-description {
  color: rgba(255, 255, 255, 0.8);
}

/* Main Content - Onboarding Style */
.brand-kit-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100vh;
  position: relative;
  z-index: 10;
}

.brand-kit-header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(181, 177, 120, 0.2);
  padding: 1rem 2rem;
  flex-shrink: 0;
}

.brand-kit-header.dark {
  background: rgba(31, 41, 55, 0.9);
  border-bottom-color: rgba(75, 85, 99, 0.5);
}

.brand-kit-header-content {
  max-width: min(90vw, 80rem);
  margin: 0 auto;
}

.brand-kit-save-button-container {
  display: flex;
  justify-content: center;
}

.brand-kit-save-button {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  background-color: #676D50;
  color: #F8F4DF;
  border-radius: 0.75rem;
  border: none;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.brand-kit-save-button:hover {
  background-color: #849068;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.brand-kit-save-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Content Area - Onboarding Style */
.brand-kit-content {
  flex: 1;
  overflow: auto;
  padding: 3rem 2rem;
  background-color: #F8F4DF;
  display: flex;
  justify-content: center;
}

.brand-kit-content.dark {
  background-color: #000000;
}

.brand-kit-content-wrapper {
  width: 100%;
  max-width: min(90vw, 80rem);
  transition: opacity 0.15s ease;
}

.brand-kit-content-wrapper.transitioning {
  opacity: 0;
}

/* Form Card - Onboarding Style */
.brand-kit-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 1.5rem;
  border: 1px solid rgba(181, 177, 120, 0.2);
  padding: 2rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.brand-kit-card.dark {
  background: rgba(0, 0, 0, 0.8);
  border-color: rgba(26, 26, 26, 0.3);
}

/* Remove card title since we're using header title */
.brand-kit-card-title {
  display: none;
}

/* Form Elements */
.brand-kit-form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .brand-kit-form-grid.two-columns {
    grid-template-columns: 1fr 1fr;
  }
}

.brand-kit-form-group {
  display: flex;
  flex-direction: column;
}

.brand-kit-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #676D50;
  margin-bottom: 0.75rem;
}

.brand-kit-card.dark .brand-kit-label {
  color: white;
}

.brand-kit-input,
.brand-kit-textarea {
  width: 100%;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
  border: 2px solid rgba(181, 177, 120, 0.3);
  border-radius: 0.75rem;
  font-size: 1rem;
  color: #1F2419;
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.brand-kit-input:focus,
.brand-kit-textarea:focus {
  outline: none;
  border-color: #676D50;
  box-shadow: 0 0 0 3px rgba(103, 109, 80, 0.2);
  background: rgba(255, 255, 255, 0.95);
}

.brand-kit-card.dark .brand-kit-input,
.brand-kit-card.dark .brand-kit-textarea {
  background: rgba(31, 41, 55, 0.8);
  border-color: rgba(75, 85, 99, 0.5);
  color: white;
}

.brand-kit-card.dark .brand-kit-input:focus,
.brand-kit-card.dark .brand-kit-textarea:focus {
  background: rgba(0, 0, 0, 0.95);
}

.brand-kit-input::placeholder,
.brand-kit-textarea::placeholder {
  color: rgba(103, 109, 80, 0.6);
}

.brand-kit-textarea {
  resize: vertical;
  min-height: 6rem;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .brand-kit-sidebar {
    width: 16rem;
  }

  .brand-kit-content {
    padding: 1rem;
  }

  .brand-kit-card {
    padding: 1.5rem;
  }

  .brand-kit-content-wrapper {
    max-width: 95vw;
  }
}

/* Large screens - scale content proportionally */
@media (min-width: 1200px) {
  .brand-kit-content {
    padding: 4rem 2rem;
  }

  .brand-kit-card {
    padding: 2.5rem;
    margin-bottom: 2.5rem;
  }
}

/* Extra large screens - maintain good proportions */
@media (min-width: 1600px) {
  .brand-kit-content {
    padding: 5rem 3rem;
  }

  .brand-kit-card {
    padding: 3rem;
    margin-bottom: 3rem;
  }

  .brand-kit-content-wrapper {
    max-width: min(85vw, 90rem);
  }
}

/* Ultra-wide screens - prevent content from getting too wide */
@media (min-width: 2000px) {
  .brand-kit-content-wrapper {
    max-width: min(80vw, 100rem);
  }
}
