/**
 * 📋 Brand Kits Dashboard Page
 *
 * @description Main dashboard page for viewing and managing brand kits
 * @responsibility Displays brand kits list, handles creation and deletion
 * @dependencies BrandKitService, modular brand kit infrastructure
 * @ai_context This page is part of the modular brand kit presentation layer
 */

import React, { useState } from 'react';
import { useQuery, useAction, getBrandKits, deleteBrandKit } from 'wasp/client/operations';
import { Plus, Search, Filter, Grid, List } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { BrandKitGrid } from '../components/BrandKitGrid';
import { BrandKitImportModal } from '../components/modals/BrandKitImportModal';
import { useOrganizationState } from '../../../../organization/store';
import { Button } from '../../../../shared';
import { ActiveSessionChecker } from '../../../onboarding/infrastructure/hooks/useActiveSessionRedirect';

const BrandKitsPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showImportModal, setShowImportModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  // Get organization ID from organization state
  const { selectedOrganizationId } = useOrganizationState();

  // Fetch brand kits
  const {
    data: brandKits,
    isLoading,
    error,
    refetch,
  } = useQuery(getBrandKits, { organizationId: selectedOrganizationId! }, { enabled: !!selectedOrganizationId });

  // Delete brand kit action
  const deleteBrandKitAction = useAction(deleteBrandKit);

  // Handle brand kit deletion
  const handleDelete = async (brandKitId: string) => {
    if (!confirm('Are you sure you want to delete this brand kit? This action cannot be undone.')) {
      return;
    }

    setIsDeleting(brandKitId);
    try {
      await deleteBrandKitAction({ id: brandKitId });
      toast.success('Brand kit deleted successfully');
      refetch();
    } catch (error) {
      console.error('Error deleting brand kit:', error);
      toast.error('Failed to delete brand kit');
    } finally {
      setIsDeleting(null);
    }
  };

  // Filter brand kits based on search term
  const filteredBrandKits =
    brandKits?.filter(
      (brandKit) =>
        brandKit.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        brandKit.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    ) || [];

  if (!selectedOrganizationId) {
    return (
      <div className='min-h-screen bg-canvas-cream flex items-center justify-center'>
        <div className='text-center'>
          <h2 className='text-2xl font-bold text-gray-900 mb-4'>No Organization Found</h2>
          <p className='text-gray-600'>Please join an organization to access brand kits.</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className='min-h-screen bg-canvas-cream flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-olive-600 mx-auto mb-4'></div>
          <p className='text-gray-600'>Loading brand kits...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='min-h-screen bg-canvas-cream flex items-center justify-center'>
        <div className='text-center'>
          <h2 className='text-2xl font-bold text-red-600 mb-4'>Error Loading Brand Kits</h2>
          <p className='text-gray-600 mb-4'>There was an error loading your brand kits.</p>
          <button
            onClick={() => refetch()}
            className='px-4 py-2 bg-olive-600 text-white rounded-lg hover:bg-olive-700 transition-colors'
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <ActiveSessionChecker>
      <div className='min-h-screen bg-[#F0EFE9] dark:bg-black text-[#1F2419] dark:text-white font-sans p-8'>
        <div className='max-w-7xl mx-auto'>
          {/* Header */}
          <div className='flex justify-between items-center mb-8'>
            <h1 className='font-display text-4xl font-bold text-[#676D50] dark:text-white'>Brand Kits</h1>
            <Button
              onClick={() => setShowImportModal(true)}
              variant='primary'
              size='lg'
              icon={<Plus size={20} />}
              iconPosition='left'
            >
              Create Brand Kit
            </Button>
          </div>
          {/* Search and Filters */}
          <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 space-y-4 sm:space-y-0'>
            <div className='relative flex-1 max-w-md'>
              <Search size={20} className='absolute left-4 top-1/2 transform -translate-y-1/2 text-[#676D50]/50' />
              <input
                type='text'
                placeholder='Search brand kits...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='w-full pl-12 pr-4 py-3 bg-white dark:bg-gray-800 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] text-[#1F2419] dark:text-white placeholder-[#676D50]/50 shadow-sm'
              />
            </div>

            <div className='flex items-center space-x-4'>
              <div className='flex items-center bg-white dark:bg-gray-800 rounded-xl border border-[#B5B178]/30 dark:border-gray-600 shadow-sm'>
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-3 ${viewMode === 'grid' ? 'bg-[#676D50]/10 text-[#676D50]' : 'text-[#676D50]/50 hover:text-[#676D50]'} rounded-l-xl transition-all duration-200`}
                >
                  <Grid size={20} />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-3 ${viewMode === 'list' ? 'bg-[#676D50]/10 text-[#676D50]' : 'text-[#676D50]/50 hover:text-[#676D50]'} rounded-r-xl transition-all duration-200`}
                >
                  <List size={20} />
                </button>
              </div>
            </div>
          </div>

          {/* Brand Kits Grid/List */}
          {filteredBrandKits.length === 0 ? (
            <div className='text-center py-16'>
              <div className='w-32 h-32 mx-auto mb-6 bg-gradient-to-br from-[#676D50]/10 to-[#849068]/10 rounded-3xl flex items-center justify-center'>
                <Plus size={48} className='text-[#676D50]/60' />
              </div>
              <h3 className='font-display text-2xl font-semibold text-[#676D50] dark:text-white mb-3'>
                {searchTerm ? 'No brand kits found' : 'Your brand journey begins here'}
              </h3>
              <p className='text-[#676D50]/70 dark:text-gray-300 text-lg mb-8 max-w-md mx-auto'>
                {searchTerm
                  ? "Try adjusting your search terms to find what you're looking for"
                  : 'Create your first brand kit and establish a cohesive visual identity'}
              </p>
              {!searchTerm && (
                <button
                  onClick={() => setShowImportModal(true)}
                  className='px-8 py-4 bg-gradient-to-r from-[#676D50] to-[#849068] hover:from-[#849068] hover:to-[#676D50] text-[#F8F4DF] rounded-xl transition-all duration-300 font-medium shadow-lg hover:shadow-xl transform hover:scale-105'
                >
                  Create Your First Brand Kit
                </button>
              )}
            </div>
          ) : (
            <BrandKitGrid brandKits={filteredBrandKits} onDelete={handleDelete} />
          )}
        </div>

        {/* Import Modal */}
        {showImportModal && (
          <BrandKitImportModal
            isOpen={showImportModal}
            onClose={() => setShowImportModal(false)}
            onSuccess={(brandKitId) => {
              setShowImportModal(false);
              refetch();
              toast.success('Brand kit created successfully!');
            }}
          />
        )}
      </div>
    </ActiveSessionChecker>
  );
};

export default BrandKitsPage;
