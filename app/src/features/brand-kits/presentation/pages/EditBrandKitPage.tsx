/**
 * ✏️ Edit Brand Kit Page
 *
 * @description Page for editing brand kit details and guidelines
 * @responsibility Provides interface for comprehensive brand kit editing
 * @dependencies BrandKitEditor component, modular brand kit infrastructure
 * @ai_context This page is part of the modular brand kit presentation layer
 */

import React from 'react';
import { useParams } from 'react-router-dom';
import { BrandKitEditor } from '../components/BrandKitEditor';

const EditBrandKitPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const brandKitId = id!;

  return <BrandKitEditor brandKitId={brandKitId} />;
};

export default EditBrandKitPage;
