/**
 * 📋 Brand Kit Editor Sections
 *
 * @description Section definitions for brand kit editor
 * @responsibility Defines available sections and their properties
 * @dependencies None
 * @ai_context These constants are part of the modular brand kit presentation layer
 */

export type SectionId =
  | 'logos'
  | 'colors'
  | 'typography'
  | 'imagery'
  | 'photography'
  | 'brand-voice'
  | 'language'
  | 'taglines'
  | 'ai-generation';

export interface Section {
  id: SectionId;
  title: string;
  description: string;
  category: 'visual' | 'voice' | 'ai';
}

export const sections: Section[] = [
  {
    id: 'logos',
    title: 'Logos & Assets',
    description: 'Logo variations, usage guidelines, and brand assets',
    category: 'visual',
  },
  {
    id: 'colors',
    title: 'Color Palette',
    description: 'Primary, secondary, and accent colors with usage guidelines',
    category: 'visual',
  },
  {
    id: 'typography',
    title: 'Typography',
    description: 'Font families, weights, and typographic hierarchy',
    category: 'visual',
  },
  {
    id: 'imagery',
    title: 'Imagery Style',
    description: 'Visual style guidelines and moodboard',
    category: 'visual',
  },
  {
    id: 'photography',
    title: 'Photography',
    description: 'Photo style, angles, lighting, and composition guidelines',
    category: 'visual',
  },
  {
    id: 'brand-voice',
    title: 'Brand Voice',
    description: 'Personality, tonality, values, and emotional guidelines',
    category: 'voice',
  },
  {
    id: 'language',
    title: 'Language & Phrasing',
    description: 'Writing style, preferred terms, and grammar rules',
    category: 'voice',
  },
  {
    id: 'taglines',
    title: 'Taglines & Messaging',
    description: 'Key messages, taglines, and value propositions',
    category: 'voice',
  },
  {
    id: 'ai-generation',
    title: 'AI Generation',
    description: 'Keywords, settings, and AI generation preferences',
    category: 'ai',
  },
];
