/**
 * 📝 Brand Kit Presentation Types
 *
 * @description Type definitions for brand kit presentation layer
 * @responsibility Provides type safety for brand kit UI components
 * @dependencies Domain types, form types
 * @ai_context These types are part of the modular brand kit presentation layer
 */

// Basic brand kit form data structure
export interface BrandKitFormData {
  name: string;

  // Visual Identity
  primaryColors: string[];
  secondaryColors: string[];
  accentColors: string[];
  primaryColorUsageText: string;
  secondaryColorUsageText: string;
  accentColorUsageText: string;
  colorHierarchyText: string;
  colorAccessibilityText: string;
  colorCombinationsText: string;
  colorVariationsText: string;
  typography: {
    headings: {
      fontFamily: string;
      weights: string[];
      sizes: {
        h1: string;
        h2: string;
        h3: string;
        h4: string;
        h5: string;
        h6: string;
      };
    };
    body: {
      fontFamily: string;
      weights: string[];
      baseSize: string;
      lineHeight: string;
    };
  };
  logoVariations: string[];
  moodboardImages: string[];

  // Logo Guidelines
  logoUsageGuidelinesText: string;
  logoSizingText: string;
  logoClearSpaceText: string;
  logoColorUsageText: string;
  logoFileFormatsText: string;
  logoMisuseText: string;

  // Photography Guidelines
  photoStyleText: string;
  preferredAnglesText: string;
  lightingPreferencesText: string;
  backgroundStylesText: string;
  propGuidelinesText: string;
  commonScenesText: string;
  photoDosDonts: {
    dos: string;
    donts: string;
  };

  // Brand Voice
  brandPersonalityText: string;
  tonalityText: string;
  brandValuesText: string;
  targetEmotionsText: string;

  // Language & Phrasing
  writingStyleText: string;
  preferredTermsText: string;
  avoidedTermsText: string;
  grammarRulesText: string;
  contentStructureText: string;
  localizationRulesText: string;
  commonMistakesText: string;

  // Taglines
  primaryTaglinesText: string;
  secondaryTaglinesText: string;
  campaignTaglinesText: string;
  keyMessagesText: string;
  valuePropositionsText: string;
  taglineGuidelinesText: string;
  trademarkInfoText: string;

  // AI Generation Settings
  promptKeywordsText: string;
  avoidanceTermsText: string;
  preferredSettings: {
    quality: string;
    style: string;
    format: string;
  };
}
