/**
 * 📄 Brand Kit PDF Import Component
 *
 * @description Component for importing brand kits from PDF files
 * @responsibility Handles PDF upload and processing for brand kit creation
 * @dependencies PDF processing utilities, modular brand kit infrastructure
 * @ai_context This component is part of the modular brand kit presentation layer
 */

import React, { useState } from 'react';
import { useAction, importBrandKitFromPDF } from 'wasp/client/operations';
import { useOrganizationState } from '../../../../../organization/store';
import { Upload } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface BrandKitImportProps {
  isOpen: boolean;
  onSuccess: (brandKitId: string) => void;
  onClose: () => void;
}

export const BrandKitImport: React.FC<BrandKitImportProps> = ({ isOpen, onSuccess, onClose }) => {
  const [brandName, setBrandName] = useState('');
  const [isImporting, setIsImporting] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const { selectedOrganizationId } = useOrganizationState();
  const importBrandKitFromPDFAction = useAction(importBrandKitFromPDF);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === 'application/pdf') {
      setSelectedFile(file);
    } else {
      toast.error('Please select a PDF file');
    }
  };

  const handleImport = async () => {
    if (!selectedFile || !brandName.trim() || !selectedOrganizationId) {
      toast.error('Please provide a brand name and select a PDF file');
      return;
    }

    setIsImporting(true);
    try {
      // Convert file to base64
      const reader = new FileReader();
      reader.onload = async () => {
        const base64Data = reader.result as string;

        const result = await importBrandKitFromPDFAction({
          url: base64Data,
          name: brandName.trim(),
          organizationId: selectedOrganizationId!,
        });

        toast.success('Brand kit imported successfully');
        if (onSuccess && result.id) {
          onSuccess(result.id);
        }
        onClose();
      };
      reader.readAsDataURL(selectedFile);
    } catch (error) {
      console.error('Error importing brand kit:', error);
      toast.error('Failed to import brand kit');
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <div className='space-y-6'>
      <div className='text-center'>
        <Upload size={48} className='mx-auto text-gray-400 mb-4' />
        <h3 className='text-lg font-semibold text-gray-900 mb-2'>Import from PDF</h3>
        <p className='text-gray-600'>Upload your brand guidelines PDF and we'll extract the key information</p>
      </div>

      <div className='space-y-4'>
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-2'>Brand Kit Name</label>
          <input
            type='text'
            value={brandName}
            onChange={(e) => setBrandName(e.target.value)}
            className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-olive-500 focus:border-olive-500'
            placeholder='Enter brand kit name...'
          />
        </div>

        <div>
          <label className='block text-sm font-medium text-gray-700 mb-2'>PDF File</label>
          <input
            type='file'
            accept='.pdf'
            onChange={handleFileSelect}
            className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-olive-500 focus:border-olive-500'
          />
          {selectedFile && <p className='mt-2 text-sm text-gray-600'>Selected: {selectedFile.name}</p>}
        </div>
      </div>

      <div className='flex justify-end space-x-4'>
        <button onClick={onClose} className='px-4 py-2 text-gray-600 hover:text-gray-800'>
          Cancel
        </button>
        <button
          onClick={handleImport}
          disabled={isImporting || !selectedFile || !brandName.trim()}
          className='px-6 py-2 bg-olive-600 text-white rounded-lg hover:bg-olive-700 disabled:bg-gray-300 disabled:cursor-not-allowed'
        >
          {isImporting ? 'Importing...' : 'Import Brand Kit'}
        </button>
      </div>
    </div>
  );
};
