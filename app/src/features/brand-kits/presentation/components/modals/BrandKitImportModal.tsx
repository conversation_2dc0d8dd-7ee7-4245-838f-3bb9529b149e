/**
 * 📥 Brand Kit Import Modal
 *
 * @description Modal for importing brand kits from various sources
 * @responsibility Provides interface for brand kit creation and import
 * @dependencies Import components, modular brand kit infrastructure
 * @ai_context This modal is part of the modular brand kit presentation layer
 */

import React, { useState } from 'react';
import { X, Upload, Link, FileText } from 'lucide-react';
import { BrandKitImport } from './BrandKitImport';
import { BrandKitImportURL } from './BrandKitImportURL';

interface BrandKitImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (brandKitId: string) => void;
}

type ImportMethod = 'pdf' | 'url' | 'manual';

export const BrandKitImportModal: React.FC<BrandKitImportModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const [selectedMethod, setSelectedMethod] = useState<ImportMethod>('manual');

  if (!isOpen) return null;

  const importMethods = [
    {
      id: 'manual' as const,
      title: 'Create Manually',
      description: 'Start with a blank brand kit and fill in details manually',
      icon: FileText,
      color: 'bg-blue-500',
    },
    {
      id: 'pdf' as const,
      title: 'Import from PDF',
      description: 'Upload a brand guidelines PDF to extract information',
      icon: Upload,
      color: 'bg-green-500',
    },
    {
      id: 'url' as const,
      title: 'Import from URL',
      description: 'Import brand information from a website or online guidelines',
      icon: Link,
      color: 'bg-purple-500',
    },
  ];

  const handleManualCreate = () => {
    // For manual creation, we'll redirect to the create page
    // This is a simplified version - you might want to implement a proper manual creation flow
    onClose();
    window.location.href = '/brand-kits/create';
  };

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
      <div className='bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden'>
        {/* Header */}
        <div className='flex items-center justify-between p-6 border-b border-gray-200'>
          <h2 className='text-2xl font-bold text-gray-900'>Create Brand Kit</h2>
          <button onClick={onClose} className='p-2 hover:bg-gray-100 rounded-lg transition-colors'>
            <X size={24} className='text-gray-500' />
          </button>
        </div>

        {/* Content */}
        <div className='p-6'>
          {selectedMethod === 'manual' && (
            <div className='text-center py-8'>
              <div className='w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center'>
                <FileText size={32} className='text-blue-600' />
              </div>
              <h3 className='text-xl font-semibold text-gray-900 mb-2'>Manual Creation</h3>
              <p className='text-gray-600 mb-6'>
                Start with a blank brand kit and fill in your brand guidelines manually.
              </p>
              <button
                onClick={handleManualCreate}
                className='px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors'
              >
                Create Blank Brand Kit
              </button>
            </div>
          )}

          {selectedMethod === 'pdf' && <BrandKitImport isOpen={true} onSuccess={onSuccess} onClose={onClose} />}

          {selectedMethod === 'url' && <BrandKitImportURL isOpen={true} onSuccess={onSuccess} onClose={onClose} />}
        </div>

        {/* Method Selection */}
        <div className='border-t border-gray-200 p-6'>
          <h3 className='text-lg font-semibold text-gray-900 mb-4'>Choose Import Method</h3>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            {importMethods.map((method) => {
              const Icon = method.icon;
              return (
                <button
                  key={method.id}
                  onClick={() => setSelectedMethod(method.id)}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    selectedMethod === method.id
                      ? 'border-olive-500 bg-olive-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className={`w-12 h-12 mx-auto mb-3 ${method.color} rounded-lg flex items-center justify-center`}>
                    <Icon size={24} className='text-white' />
                  </div>
                  <h4 className='font-semibold text-gray-900 mb-1'>{method.title}</h4>
                  <p className='text-sm text-gray-600'>{method.description}</p>
                </button>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};
