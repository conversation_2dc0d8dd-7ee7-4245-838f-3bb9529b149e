/**
 * 🌐 Brand Kit URL Import Component
 *
 * @description Component for importing brand kits from URLs
 * @responsibility Handles URL processing and content extraction for brand kit creation
 * @dependencies URL processing utilities, modular brand kit infrastructure
 * @ai_context This component is part of the modular brand kit presentation layer
 */

import React, { useState } from 'react';
import { useAction, importBrandKitFromURL } from 'wasp/client/operations';
import { useOrganizationState } from '../../../../../organization/store';
import { Link } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface BrandKitImportURLProps {
  isOpen: boolean;
  onSuccess: (brandKitId: string) => void;
  onClose: () => void;
}

export const BrandKitImportURL: React.FC<BrandKitImportURLProps> = ({ isOpen, onSuccess, onClose }) => {
  const [brandName, setBrandName] = useState('');
  const [url, setUrl] = useState('');
  const [isImporting, setIsImporting] = useState(false);

  const { selectedOrganizationId } = useOrganizationState();
  const importBrandKitFromURLAction = useAction(importBrandKitFromURL);

  const handleImport = async () => {
    if (!url.trim() || !brandName.trim() || !selectedOrganizationId) {
      toast.error('Please provide a brand name and URL');
      return;
    }

    setIsImporting(true);
    try {
      const result = await importBrandKitFromURLAction({
        url: url.trim(),
        name: brandName.trim(),
        organizationId: selectedOrganizationId!,
      });

      toast.success('Brand kit imported successfully');
      if (onSuccess && result.id) {
        onSuccess(result.id);
      }
      onClose();
    } catch (error) {
      console.error('Error importing brand kit:', error);
      toast.error('Failed to import brand kit');
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <div className='space-y-6'>
      <div className='text-center'>
        <Link size={48} className='mx-auto text-gray-400 mb-4' />
        <h3 className='text-lg font-semibold text-gray-900 mb-2'>Import from URL</h3>
        <p className='text-gray-600'>Import brand information from a website or online brand guidelines</p>
      </div>

      <div className='space-y-4'>
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-2'>Brand Kit Name</label>
          <input
            type='text'
            value={brandName}
            onChange={(e) => setBrandName(e.target.value)}
            className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-olive-500 focus:border-olive-500'
            placeholder='Enter brand kit name...'
          />
        </div>

        <div>
          <label className='block text-sm font-medium text-gray-700 mb-2'>Website URL</label>
          <input
            type='url'
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-olive-500 focus:border-olive-500'
            placeholder='https://example.com/brand-guidelines'
          />
        </div>
      </div>

      <div className='flex justify-end space-x-4'>
        <button onClick={onClose} className='px-4 py-2 text-gray-600 hover:text-gray-800'>
          Cancel
        </button>
        <button
          onClick={handleImport}
          disabled={isImporting || !url.trim() || !brandName.trim()}
          className='px-6 py-2 bg-olive-600 text-white rounded-lg hover:bg-olive-700 disabled:bg-gray-300 disabled:cursor-not-allowed'
        >
          {isImporting ? 'Importing...' : 'Import Brand Kit'}
        </button>
      </div>
    </div>
  );
};
