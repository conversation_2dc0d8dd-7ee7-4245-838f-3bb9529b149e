/**
 * 📝 Brand Kit Editor Main Content
 *
 * @description Main content area for brand kit editor sections
 * @responsibility Displays and manages brand kit editing sections
 * @dependencies Section components, modular brand kit infrastructure
 * @ai_context This component is part of the modular brand kit presentation layer
 */

import React from 'react';
import { useWatch } from 'react-hook-form';
import { Building, Plus, X, Edit3, Upload } from 'lucide-react';
import { SectionId } from '../constants/sections';
import { Card, Input, TextArea, Label, FormGroup, FormGrid, SaveButton } from '../../../../shared';
import { uploadLogo, uploadFont, removeFont } from 'wasp/client/operations';

// Font Selector with Search and Upload
interface FontSelectorProps {
  value: string;
  onChange: (fontFamily: string) => void;
  placeholder?: string;
  brandKitId: string;
  brandKitData?: any;
  onFontUploaded: () => void;
}

const FontSelector: React.FC<FontSelectorProps> = ({
  value,
  onChange,
  placeholder = 'Search or select a font...',
  brandKitId,
  brandKitData,
  onFontUploaded,
}) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const [searchTerm, setSearchTerm] = React.useState(value || '');
  const [customFonts, setCustomFonts] = React.useState<{ name: string; url: string }[]>([]);
  const [isUploading, setIsUploading] = React.useState(false);
  const [selectedIndex, setSelectedIndex] = React.useState(-1);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  // Popular Google Fonts list
  const googleFonts = [
    'Inter',
    'Roboto',
    'Open Sans',
    'Lato',
    'Montserrat',
    'Oswald',
    'Source Sans Pro',
    'Raleway',
    'PT Sans',
    'Lora',
    'Nunito',
    'Poppins',
    'Playfair Display',
    'Merriweather',
    'Ubuntu',
    'Mukti',
    'Fira Sans',
    'Work Sans',
    'Crimson Text',
    'Libre Baskerville',
  ];

  // System fonts
  const systemFonts = [
    'Arial',
    'Helvetica',
    'Times New Roman',
    'Georgia',
    'Verdana',
    'Courier New',
    'Trebuchet MS',
    'Arial Black',
    'Impact',
    'Comic Sans MS',
    'Palatino',
    'Garamond',
  ];

  // Load custom fonts from brand kit data
  React.useEffect(() => {
    const loadExistingFonts = async () => {
      if (brandKitData?.typography) {
        try {
          const typography =
            typeof brandKitData.typography === 'string' ? JSON.parse(brandKitData.typography) : brandKitData.typography;

          if (typography.customFonts && typography.customFonts.length > 0) {
            // Set the custom fonts in state FIRST
            setCustomFonts(typography.customFonts);

            // Load fonts for preview sequentially
            for (const font of typography.customFonts) {
              const loaded = await loadCustomFontFromUrl(font.url, font.name);
              if (!loaded) {
                console.error(`Failed to load font: ${font.name}`);
              }
            }

            // Trigger refresh callback
            onFontUploaded?.();
          } else {
            setCustomFonts([]);
          }
        } catch (error) {
          console.error('Error parsing typography data:', error);
          setCustomFonts([]);
        }
      } else {
        setCustomFonts([]);
      }
    };

    loadExistingFonts();
  }, [brandKitData]);

  // Filter fonts based on search term
  const filteredFonts = React.useMemo(() => {
    const term = searchTerm.toLowerCase();
    const allFonts = [
      ...customFonts.map((f) => ({ name: f.name, type: 'custom' as const, url: f.url })),
      ...googleFonts.map((f) => ({ name: f, type: 'google' as const })),
      ...systemFonts.map((f) => ({ name: f, type: 'system' as const })),
    ];

    return allFonts.filter((font) => font.name.toLowerCase().includes(term)).slice(0, 10); // Limit to 10 results
  }, [searchTerm, customFonts]);

  // Reset selected index when filtered fonts change
  React.useEffect(() => {
    setSelectedIndex(-1);
  }, [filteredFonts]);

  // Scroll selected item into view
  const scrollToSelectedItem = (index: number) => {
    if (dropdownRef.current) {
      const dropdown = dropdownRef.current;
      const selectedItem = dropdown.children[index] as HTMLElement;
      if (selectedItem) {
        selectedItem.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth',
        });
      }
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === 'ArrowDown' || e.key === 'Enter') {
        setIsOpen(true);
        setSelectedIndex(0);
        e.preventDefault();
        // Scroll to first item after dropdown opens
        setTimeout(() => scrollToSelectedItem(0), 50);
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        const nextIndex = selectedIndex < filteredFonts.length - 1 ? selectedIndex + 1 : 0;
        setSelectedIndex(nextIndex);
        scrollToSelectedItem(nextIndex);
        break;

      case 'ArrowUp':
        e.preventDefault();
        const prevIndex = selectedIndex > 0 ? selectedIndex - 1 : filteredFonts.length - 1;
        setSelectedIndex(prevIndex);
        scrollToSelectedItem(prevIndex);
        break;

      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < filteredFonts.length) {
          const selectedFont = filteredFonts[selectedIndex];
          selectFont(selectedFont.name);
        }
        break;

      case 'Escape':
        e.preventDefault();
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Select a font
  const selectFont = (fontName: string) => {
    onChange(fontName);
    setSearchTerm(fontName);
    setIsOpen(false);
    setSelectedIndex(-1);
  };

  const handleFontUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validExtensions = ['.woff', '.woff2', '.ttf', '.otf'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

    if (!validExtensions.includes(fileExtension)) {
      alert('Please upload a valid font file (.woff, .woff2, .ttf, or .otf)');
      return;
    }

    setIsUploading(true);

    try {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const dataUrl = e.target?.result as string;
          const fontName = file.name.replace(/\.[^/.]+$/, '');

          const result = await uploadFont({
            dataUrl,
            brandKitId,
            fontName,
          });

          if (result.success && result.url) {
            // Add to custom fonts list
            const newFont = { name: fontName, url: result.url };
            setCustomFonts((prev) => [...prev, newFont]);

            // Load the font for immediate preview
            const loaded = await loadCustomFontFromUrl(result.url, fontName);
            if (loaded) {
              // Trigger refresh
              onFontUploaded?.();
            }

            // Select the uploaded font
            onChange(fontName);
            setSearchTerm(fontName);
            setIsOpen(false);

            onFontUploaded();
          } else {
            console.error(`Upload failed:`, result.error);
            alert(`Failed to upload font: ${result.error}`);
          }
        } catch (error) {
          console.error('❌ Font upload error:', error);
          alert('Failed to upload font. Please try again.');
        } finally {
          setIsUploading(false);
          event.target.value = '';
        }
      };

      reader.readAsDataURL(file);
    } catch (error) {
      console.error('❌ Font upload error:', error);
      alert('Failed to upload font. Please try again.');
      setIsUploading(false);
    }
  };

  const removeCustomFont = async (fontName: string) => {
    const confirmed = window.confirm(`Remove "${fontName}" from your fonts?`);
    if (!confirmed) return;

    try {
      const result = await removeFont({ brandKitId, fontName });

      if (result.success) {
        setCustomFonts((prev) => prev.filter((f) => f.name !== fontName));

        // If this font was selected, clear the selection
        if (value === fontName) {
          onChange('');
          setSearchTerm('');
        }

        onFontUploaded();
      } else {
        alert(`Failed to remove font: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ Font removal error:', error);
      alert('Failed to remove font. Please try again.');
    }
  };

  return (
    <div className='relative'>
      <div className='flex gap-2'>
        {/* Search Input */}
        <div className='flex-1 relative'>
          <input
            ref={inputRef}
            type='text'
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setIsOpen(true);
              setSelectedIndex(-1);
            }}
            onFocus={() => setIsOpen(true)}
            onBlur={() => setTimeout(() => setIsOpen(false), 200)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className='w-full px-3 py-2 border border-[#E8E4D4] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-transparent'
            autoComplete='off'
          />

          {/* Dropdown */}
          {isOpen && (
            <div
              ref={dropdownRef}
              className='absolute top-full left-0 right-0 mt-1 bg-white border border-[#E8E4D4] rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto'
            >
              {filteredFonts.length > 0 ? (
                filteredFonts.map((font, index) => (
                  <div
                    key={`${font.type}-${font.name}`}
                    onClick={() => selectFont(font.name)}
                    className={`flex items-center justify-between px-3 py-2 cursor-pointer transition-colors ${
                      index === selectedIndex ? 'bg-[#676D50] text-white' : 'hover:bg-[#F6F3E5]'
                    }`}
                  >
                    <div className='flex items-center gap-2'>
                      <span style={{ fontFamily: font.name }} className='font-medium'>
                        {font.name}
                      </span>
                      <span className={`text-xs ${index === selectedIndex ? 'text-white/70' : 'text-[#676D50]/60'}`}>
                        {font.type === 'custom' ? 'Custom' : font.type === 'google' ? 'Google' : 'System'}
                      </span>
                    </div>

                    {font.type === 'custom' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          removeCustomFont(font.name);
                        }}
                        className={`p-1 transition-colors rounded ${
                          index === selectedIndex
                            ? 'text-red-200 hover:text-white hover:bg-red-500/20'
                            : 'text-red-500 hover:text-red-700 hover:bg-red-50'
                        }`}
                        title={`Remove ${font.name}`}
                      >
                        ✕
                      </button>
                    )}
                  </div>
                ))
              ) : (
                <div className='px-3 py-2 text-[#676D50]/60 text-sm'>No fonts found. Try uploading a custom font.</div>
              )}
            </div>
          )}
        </div>

        {/* Upload Button */}
        <label className='cursor-pointer inline-flex items-center gap-2 px-3 py-2 bg-[#676D50] text-white rounded-lg hover:bg-[#849068] transition-colors text-sm whitespace-nowrap'>
          {isUploading ? (
            <>
              <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin'></div>
              Uploading...
            </>
          ) : (
            <>
              <svg className='w-4 h-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12'
                />
              </svg>
              Upload
            </>
          )}
          <input
            type='file'
            accept='.woff,.woff2,.ttf,.otf'
            onChange={handleFontUpload}
            className='hidden'
            disabled={isUploading}
          />
        </label>
      </div>
    </div>
  );
};

// Font Upload Component

// Simple font loading tracking
const loadedFonts = new Set<string>(); // Just track which fonts are loaded
const fontLoadPromises = new Map<string, Promise<boolean>>(); // Prevent duplicate loading

// Simple font loading function
const loadCustomFontFromUrl = async (fontUrl: string, fontName: string): Promise<boolean> => {
  // Check if already loaded
  if (loadedFonts.has(fontName)) {
    return true;
  }

  // Check if already loading
  if (fontLoadPromises.has(fontName)) {
    return await fontLoadPromises.get(fontName)!;
  }

  // Start loading
  const loadPromise = (async () => {
    try {
      // Create unique font family name
      const customFontFamily = `CustomFont_${fontName.replace(/[^a-zA-Z0-9]/g, '_')}`;
      const fontFace = new FontFace(customFontFamily, `url(${fontUrl})`);
      await fontFace.load();
      document.fonts.add(fontFace);

      // Mark as loaded
      loadedFonts.add(fontName);

      return true;
    } catch (error) {
      console.warn(`Failed to load custom font "${fontName}":`, error);
      return false;
    } finally {
      // Clean up promise
      fontLoadPromises.delete(fontName);
    }
  })();

  // Store promise to prevent duplicate loading
  fontLoadPromises.set(fontName, loadPromise);
  return await loadPromise;
};

// Font Preview Component
interface FontPreviewProps {
  fontFamily: string;
  text?: string;
  className?: string;
  forceRefresh?: number; // Add this to force re-evaluation when fonts are uploaded
}

const FontPreview: React.FC<FontPreviewProps> = ({
  fontFamily,
  text = 'The quick brown fox jumps over the lazy dog',
  className = '',
  forceRefresh = 0,
}) => {
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [hasError, setHasError] = React.useState(false);

  // Simple font checking
  React.useEffect(() => {
    if (!fontFamily || fontFamily.trim() === '') {
      setIsLoaded(false);
      setHasError(false);
      return;
    }

    // Check if this is a custom font we've loaded
    if (loadedFonts.has(fontFamily)) {
      setIsLoaded(true);
      setHasError(false);
      return;
    }

    // Check if it's a system font
    const systemFonts = [
      'Arial',
      'Helvetica',
      'Times',
      'Times New Roman',
      'Courier',
      'Courier New',
      'Verdana',
      'Georgia',
      'Palatino',
      'Garamond',
      'Bookman',
      'Comic Sans MS',
      'Trebuchet MS',
      'Arial Black',
      'Impact',
      'system-ui',
      'sans-serif',
      'serif',
      'monospace',
    ];

    const isSystemFont = systemFonts.some((sysFont) => fontFamily.toLowerCase().includes(sysFont.toLowerCase()));

    if (isSystemFont) {
      setIsLoaded(true);
      setHasError(false);
      return;
    }

    // Check if font is already loaded in document
    const allLoadedFonts = Array.from(document.fonts).map((font) => font.family);
    if (allLoadedFonts.includes(fontFamily)) {
      setIsLoaded(true);
      setHasError(false);
      return;
    }

    // Try Google Fonts as last resort
    const loadFont = async () => {
      try {
        // Load the font
        const link = document.createElement('link');
        link.href = `https://fonts.googleapis.com/css2?family=${fontFamily.replace(/ /g, '+')}:wght@400;600;700&display=swap`;
        link.rel = 'stylesheet';
        document.head.appendChild(link);

        // Wait for font to load
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Simple check - if we get here, assume it worked
        setIsLoaded(true);
        setHasError(false);
      } catch (error) {
        setHasError(true);
        setIsLoaded(false);
      }
    };

    loadFont();
  }, [fontFamily, forceRefresh]);

  if (!fontFamily || fontFamily.trim() === '') {
    return (
      <div
        className={`p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 ${className}`}
      >
        <p className='text-gray-500 dark:text-gray-400 text-sm italic'>Enter a font family to see preview</p>
      </div>
    );
  }

  return (
    <div
      className={`p-4 bg-gradient-to-br from-[#F6F3E5] to-[#F0EFE9] rounded-lg border border-[#E8E4D4] ${className}`}
    >
      <div className='space-y-2'>
        <p className='text-xs text-[#676D50]/70 font-medium mb-2'>{fontFamily}</p>
        <div
          style={{
            fontFamily: (() => {
              if (!isLoaded) return 'system-ui, sans-serif';

              // Check if this is a custom font
              if (loadedFonts.has(fontFamily)) {
                const customFontFamily = `CustomFont_${fontFamily.replace(/[^a-zA-Z0-9]/g, '_')}`;
                return customFontFamily;
              }

              // Use the actual font family name
              return fontFamily;
            })(),
            fontSize: '18px',
            lineHeight: '1.4',
          }}
          className={`text-[#676D50] transition-all duration-300 ${isLoaded ? 'opacity-100' : 'opacity-70'}`}
        >
          {text}
        </div>
        {hasError && (
          <p className='text-xs text-amber-600 mt-1'>
            "{fontFamily}" not available in Google Fonts - showing system fallback. For custom fonts, upload them to
            your website or use a web font service.
          </p>
        )}
        {!isLoaded && !hasError && (
          <p className='text-xs text-[#676D50]/50 mt-1'>Loading "{fontFamily}" from Google Fonts...</p>
        )}
      </div>
    </div>
  );
};

interface MainContentProps {
  selectedSection: SectionId;
  isTransitioning: boolean;
  register: any;
  control: any;
  setValue: any;
  isDirty: boolean;
  onSubmit: () => void;
  brandKitId: string;
  isSaving: boolean;
  brandKitData?: any;
}

// Helper function to get favicon URL from domain
const getFaviconUrl = (websiteUrl: string): string => {
  try {
    const url = new URL(websiteUrl.startsWith('http') ? websiteUrl : `https://${websiteUrl}`);
    return `https://www.google.com/s2/favicons?domain=${url.hostname}&sz=128`;
  } catch {
    return '';
  }
};

// Helper function to get brand kit logo
const getBrandKitLogo = (brandKitData: any) => {
  // Priority: logoUrl > logoVariations > favicon from websiteUrl > fallback
  if (brandKitData?.logoUrl) {
    return {
      type: 'image' as const,
      src: brandKitData.logoUrl,
      alt: `${brandKitData.name} logo`,
    };
  }

  if (brandKitData?.logoVariations && brandKitData.logoVariations.length > 0) {
    return {
      type: 'image' as const,
      src: brandKitData.logoVariations[0],
      alt: `${brandKitData.name} logo`,
    };
  }

  if (brandKitData?.websiteUrl) {
    const faviconUrl = getFaviconUrl(brandKitData.websiteUrl);
    if (faviconUrl) {
      return {
        type: 'image' as const,
        src: faviconUrl,
        alt: `${brandKitData.name} favicon`,
      };
    }
  }

  // Fallback to generic icon
  return {
    type: 'icon' as const,
    component: Building,
    alt: 'Brand icon',
  };
};

// Unified Logo Manager Component
interface UnifiedLogoManagerProps {
  control: any;
  setValue: any;
  register: any;
  brandKitData?: any;
  brandKitId?: string;
  currentLogoUrl?: string;
  handleLogoUpload: (file: File) => void;
}

const UnifiedLogoManager: React.FC<UnifiedLogoManagerProps> = ({
  control,
  setValue,
  register,
  brandKitData,
  brandKitId,
  currentLogoUrl,
  handleLogoUpload,
}) => {
  const [variations, setVariations] = React.useState<string[]>([]);
  const [newVariationUrl, setNewVariationUrl] = React.useState('');

  // Watch the logoVariations field
  const watchedVariations = useWatch({ control, name: 'logoVariations', defaultValue: [] });
  const watchedLogoUrl = useWatch({ control, name: 'logoUrl', defaultValue: '' });

  // Initialize variations from form data
  React.useEffect(() => {
    if (Array.isArray(watchedVariations)) {
      setVariations(watchedVariations);
    }
  }, [watchedVariations]);

  // Get main logo with current form value taking priority
  const getMainLogo = () => {
    if (watchedLogoUrl && watchedLogoUrl.trim()) {
      return {
        type: 'image' as const,
        src: watchedLogoUrl,
        alt: `${brandKitData?.name || 'Brand'} logo`,
      };
    }
    return getBrandKitLogo(brandKitData);
  };

  // Add new variation
  const addVariation = () => {
    if (newVariationUrl.trim()) {
      const updatedVariations = [...variations, newVariationUrl.trim()];
      setVariations(updatedVariations);
      setValue('logoVariations', updatedVariations, { shouldDirty: true });
      setNewVariationUrl('');
    }
  };

  // Remove variation
  const removeVariation = (index: number) => {
    const updatedVariations = variations.filter((_, i) => i !== index);
    setVariations(updatedVariations);
    setValue('logoVariations', updatedVariations, { shouldDirty: true });
  };

  // Update variation URL
  const updateVariation = (index: number, newUrl: string) => {
    const updatedVariations = [...variations];
    updatedVariations[index] = newUrl;
    setVariations(updatedVariations);
    setValue('logoVariations', updatedVariations, { shouldDirty: true });
  };

  // Handle file upload for variation
  const handleVariationUpload = async (file: File, index?: number) => {
    try {
      console.log('📸 Logo variation upload started:', file.name);

      if (!brandKitId) {
        console.error('❌ No brand kit ID provided for logo variation upload');
        return;
      }

      // Convert file to data URL for upload
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const dataUrl = e.target?.result as string;
          console.log('📸 Starting R2 upload for logo variation...');

          // Upload to R2 storage
          const result = await uploadLogo({ dataUrl, brandKitId });

          if (result.success && result.url) {
            console.log('📸 R2 upload successful for variation:', result.url);

            if (index !== undefined) {
              // Update existing variation
              updateVariation(index, result.url);
            } else {
              // Add new variation
              const updatedVariations = [...variations, result.url];
              setVariations(updatedVariations);
              setValue('logoVariations', updatedVariations, { shouldDirty: true });
            }
          } else {
            console.error('❌ R2 upload failed:', result.error);
          }
        } catch (uploadError) {
          console.error('❌ Upload error:', uploadError);
        }
      };

      reader.readAsDataURL(file);
    } catch (error) {
      console.error('❌ Logo variation upload failed:', error);
    }
  };

  const mainLogo = getMainLogo();

  return (
    <div className='bg-white rounded-2xl border border-[#E8E4D4] overflow-hidden'>
      {/* Main Logo Section */}
      <div className='p-6 bg-gradient-to-br from-[#F6F3E5] to-[#F0EFE9] border-b border-[#E8E4D4]'>
        <div className='flex items-center gap-6'>
          {/* Main Logo Display */}
          <div className='relative group'>
            <div className='w-24 h-24 rounded-xl border-2 border-[#676D50]/20 bg-white flex items-center justify-center p-3 shadow-sm'>
              {mainLogo.type === 'image' ? (
                <img
                  src={mainLogo.src}
                  alt={mainLogo.alt}
                  className='w-full h-full object-contain'
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = 'none';
                  }}
                />
              ) : (
                <Building className='w-12 h-12 text-[#676D50]' />
              )}
            </div>

            {/* Edit Main Logo Button */}
            <button
              type='button'
              className='absolute -bottom-2 -right-2 w-8 h-8 bg-[#676D50] text-white rounded-full flex items-center justify-center shadow-lg hover:bg-[#849068] transition-colors opacity-0 group-hover:opacity-100'
              onClick={() => {
                const fileInput = document.getElementById('main-logo-upload') as HTMLInputElement;
                fileInput?.click();
              }}
            >
              <Edit3 size={14} />
            </button>

            {/* Hidden File Input for Main Logo */}
            <input
              id='main-logo-upload'
              type='file'
              accept='image/*'
              className='hidden'
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  handleLogoUpload(file);
                }
              }}
            />
          </div>

          {/* Brand Info */}
          <div className='flex-1'>
            <h4 className='font-display text-xl font-bold text-[#676D50] mb-1'>Primary Logo</h4>
            <p className='text-[#676D50]/70 text-sm mb-3'>Your main brand logo used across all materials</p>

            {/* Main Logo URL Input */}
            <input
              {...register('logoUrl')}
              type='url'
              placeholder='https://example.com/logo.png'
              className='w-full px-3 py-2 text-sm border border-[#E8E4D4] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-transparent bg-white'
            />
          </div>
        </div>
      </div>

      {/* Logo Variations Section */}
      <div className='p-6'>
        <div className='flex items-center justify-between mb-4'>
          <div>
            <h4 className='font-display text-lg font-semibold text-[#676D50] mb-1'>Logo Variations</h4>
            <p className='text-[#676D50]/70 text-sm'>
              Different versions of your logo (horizontal, vertical, icon-only, etc.)
            </p>
          </div>
        </div>

        {/* Existing Variations Grid */}
        {variations.length > 0 && (
          <div className='grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4 mb-6'>
            {variations.map((variation, index) => (
              <div
                key={index}
                className='group relative bg-[#F6F3E5] border border-[#E8E4D4] rounded-xl p-3 hover:shadow-md transition-all'
              >
                {/* Variation Image */}
                <div className='relative aspect-square bg-white rounded-lg overflow-hidden mb-2 border border-[#E8E4D4]'>
                  {variation ? (
                    <img
                      src={variation}
                      alt={`Logo variation ${index + 1}`}
                      className='w-full h-full object-contain p-2'
                      onError={(e) => {
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  ) : (
                    <div className='w-full h-full flex items-center justify-center'>
                      <Building className='w-6 h-6 text-[#676D50]/40' />
                    </div>
                  )}

                  {/* Edit Button */}
                  <button
                    type='button'
                    className='absolute top-1 right-1 w-6 h-6 bg-[#676D50] text-white rounded-full flex items-center justify-center shadow-lg hover:bg-[#849068] transition-colors opacity-0 group-hover:opacity-100'
                    onClick={() => {
                      const fileInput = document.getElementById(`variation-upload-${index}`) as HTMLInputElement;
                      fileInput?.click();
                    }}
                  >
                    <Edit3 size={10} />
                  </button>

                  {/* Remove Button */}
                  <button
                    type='button'
                    className='absolute top-1 left-1 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100'
                    onClick={() => removeVariation(index)}
                  >
                    <X size={10} />
                  </button>

                  {/* Hidden File Input */}
                  <input
                    id={`variation-upload-${index}`}
                    type='file'
                    accept='image/*'
                    className='hidden'
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        handleVariationUpload(file, index);
                      }
                    }}
                  />
                </div>

                {/* Variation URL Input */}
                <input
                  type='url'
                  value={variation}
                  onChange={(e) => updateVariation(index, e.target.value)}
                  placeholder='Logo URL...'
                  className='w-full px-2 py-1 text-xs border border-[#E8E4D4] rounded focus:outline-none focus:ring-1 focus:ring-[#676D50] focus:border-transparent bg-white'
                />
              </div>
            ))}
          </div>
        )}

        {/* Add New Variation */}
        <div className='border-2 border-dashed border-[#E8E4D4] rounded-xl p-4 bg-[#F6F3E5]/30'>
          <div className='flex flex-col sm:flex-row gap-3'>
            <input
              type='url'
              value={newVariationUrl}
              onChange={(e) => setNewVariationUrl(e.target.value)}
              placeholder='https://example.com/logo-variation.png'
              className='flex-1 px-3 py-2 border border-[#E8E4D4] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-transparent bg-white'
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addVariation();
                }
              }}
            />
            <div className='flex gap-2'>
              <button
                type='button'
                onClick={addVariation}
                disabled={!newVariationUrl.trim()}
                className='px-4 py-2 bg-[#676D50] text-white rounded-lg hover:bg-[#849068] transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 text-sm'
              >
                <Plus size={14} />
                Add URL
              </button>
              <button
                type='button'
                onClick={() => {
                  const fileInput = document.getElementById('new-variation-upload') as HTMLInputElement;
                  fileInput?.click();
                }}
                className='px-4 py-2 bg-[#849068] text-white rounded-lg hover:bg-[#676D50] transition-colors flex items-center gap-2 text-sm'
              >
                <Upload size={14} />
                Upload
              </button>
            </div>
          </div>

          {/* Hidden File Input for New Variation */}
          <input
            id='new-variation-upload'
            type='file'
            accept='image/*'
            className='hidden'
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) {
                handleVariationUpload(file);
              }
            }}
          />

          <p className='text-xs text-[#676D50]/60 mt-2'>
            Add different logo variations by entering a URL or uploading an image
          </p>
        </div>
      </div>
    </div>
  );
};

// Logo Variations Manager Component (Legacy - keeping for compatibility)
interface LogoVariationsManagerProps {
  control: any;
  setValue: any;
  brandKitId?: string;
}

const LogoVariationsManager: React.FC<LogoVariationsManagerProps> = ({ control, setValue, brandKitId }) => {
  const [variations, setVariations] = React.useState<string[]>([]);
  const [newVariationUrl, setNewVariationUrl] = React.useState('');

  // Watch the logoVariations field
  const watchedVariations = useWatch({ control, name: 'logoVariations', defaultValue: [] });

  // Initialize variations from form data
  React.useEffect(() => {
    if (Array.isArray(watchedVariations)) {
      setVariations(watchedVariations);
    }
  }, [watchedVariations]);

  // Add new variation
  const addVariation = () => {
    if (newVariationUrl.trim()) {
      const updatedVariations = [...variations, newVariationUrl.trim()];
      setVariations(updatedVariations);
      setValue('logoVariations', updatedVariations, { shouldDirty: true });
      setNewVariationUrl('');
    }
  };

  // Remove variation
  const removeVariation = (index: number) => {
    const updatedVariations = variations.filter((_, i) => i !== index);
    setVariations(updatedVariations);
    setValue('logoVariations', updatedVariations, { shouldDirty: true });
  };

  // Update variation URL
  const updateVariation = (index: number, newUrl: string) => {
    const updatedVariations = [...variations];
    updatedVariations[index] = newUrl;
    setVariations(updatedVariations);
    setValue('logoVariations', updatedVariations, { shouldDirty: true });
  };

  // Handle file upload for variation
  const handleVariationUpload = async (file: File, index?: number) => {
    try {
      console.log('📸 Logo variation upload started:', file.name);

      if (!brandKitId) {
        console.error('❌ No brand kit ID provided for logo variation upload');
        return;
      }

      // Convert file to data URL for upload
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const dataUrl = e.target?.result as string;
          console.log('📸 Starting R2 upload for logo variation...');

          // Upload to R2 storage
          const result = await uploadLogo({ dataUrl, brandKitId });

          if (result.success && result.url) {
            console.log('📸 R2 upload successful for variation:', result.url);

            if (index !== undefined) {
              // Update existing variation
              updateVariation(index, result.url);
            } else {
              // Add new variation
              const updatedVariations = [...variations, result.url];
              setVariations(updatedVariations);
              setValue('logoVariations', updatedVariations, { shouldDirty: true });
            }
          } else {
            console.error('❌ R2 upload failed:', result.error);
          }
        } catch (uploadError) {
          console.error('❌ Upload error:', uploadError);
        }
      };

      reader.readAsDataURL(file);
    } catch (error) {
      console.error('❌ Logo variation upload failed:', error);
    }
  };

  return (
    <div className='space-y-4'>
      {/* Existing Variations */}
      {variations.length > 0 && (
        <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4'>
          {variations.map((variation, index) => (
            <div
              key={index}
              className='group relative bg-white border border-[#E8E4D4] rounded-xl p-4 hover:shadow-md transition-all'
            >
              {/* Variation Image */}
              <div className='relative aspect-square bg-gradient-to-br from-[#676D50]/10 to-[#849068]/10 rounded-lg overflow-hidden mb-3'>
                {variation ? (
                  <img
                    src={variation}
                    alt={`Logo variation ${index + 1}`}
                    className='w-full h-full object-contain'
                    onError={(e) => {
                      (e.target as HTMLImageElement).style.display = 'none';
                    }}
                  />
                ) : (
                  <div className='w-full h-full flex items-center justify-center'>
                    <Building className='w-8 h-8 text-[#676D50]/40' />
                  </div>
                )}

                {/* Edit Button */}
                <button
                  type='button'
                  className='absolute top-2 right-2 w-8 h-8 bg-[#676D50] text-white rounded-full flex items-center justify-center shadow-lg hover:bg-[#849068] transition-colors opacity-0 group-hover:opacity-100'
                  onClick={() => {
                    const fileInput = document.getElementById(`variation-upload-${index}`) as HTMLInputElement;
                    fileInput?.click();
                  }}
                >
                  <Edit3 size={14} />
                </button>

                {/* Remove Button */}
                <button
                  type='button'
                  className='absolute top-2 left-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100'
                  onClick={() => removeVariation(index)}
                >
                  <X size={14} />
                </button>

                {/* Hidden File Input */}
                <input
                  id={`variation-upload-${index}`}
                  type='file'
                  accept='image/*'
                  className='hidden'
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleVariationUpload(file, index);
                    }
                  }}
                />
              </div>

              {/* Variation URL Input */}
              <input
                type='url'
                value={variation}
                onChange={(e) => updateVariation(index, e.target.value)}
                placeholder='https://example.com/logo-variation.png'
                className='w-full px-3 py-2 text-sm border border-[#E8E4D4] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-transparent'
              />
            </div>
          ))}
        </div>
      )}

      {/* Add New Variation */}
      <div className='border-2 border-dashed border-[#E8E4D4] rounded-xl p-6'>
        <div className='flex flex-col sm:flex-row gap-3'>
          <input
            type='url'
            value={newVariationUrl}
            onChange={(e) => setNewVariationUrl(e.target.value)}
            placeholder='https://example.com/logo-variation.png'
            className='flex-1 px-4 py-3 border border-[#E8E4D4] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-transparent'
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                addVariation();
              }
            }}
          />
          <div className='flex gap-2'>
            <button
              type='button'
              onClick={addVariation}
              disabled={!newVariationUrl.trim()}
              className='px-4 py-3 bg-[#676D50] text-white rounded-lg hover:bg-[#849068] transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2'
            >
              <Plus size={16} />
              Add URL
            </button>
            <button
              type='button'
              onClick={() => {
                const fileInput = document.getElementById('new-variation-upload') as HTMLInputElement;
                fileInput?.click();
              }}
              className='px-4 py-3 bg-[#849068] text-white rounded-lg hover:bg-[#676D50] transition-colors flex items-center gap-2'
            >
              <Upload size={16} />
              Upload
            </button>
          </div>
        </div>

        {/* Hidden File Input for New Variation */}
        <input
          id='new-variation-upload'
          type='file'
          accept='image/*'
          className='hidden'
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) {
              handleVariationUpload(file);
            }
          }}
        />

        <p className='text-xs text-[#676D50]/60 mt-3'>
          Add different logo variations (horizontal, vertical, icon-only, etc.) by entering a URL or uploading an image
        </p>
      </div>
    </div>
  );
};

const renderSectionContent = (
  sectionId: SectionId,
  register: any,
  control: any,
  setValue: any,
  brandKitData?: any,
  brandKitId?: string,
  currentLogoUrl?: string,
  headingFont?: string,
  bodyFont?: string
) => {
  // State to track font uploads and force preview refresh
  const [fontRefreshCounter, setFontRefreshCounter] = React.useState(0);
  // Handle logo upload
  const handleLogoUpload = async (file: File) => {
    try {
      console.log('📸 Logo upload started:', file.name);

      if (!brandKitId) {
        console.error('❌ No brand kit ID provided for logo upload');
        return;
      }

      // Create a temporary URL for immediate preview
      const tempUrl = URL.createObjectURL(file);
      console.log('📸 Created temporary URL:', tempUrl);

      // Update the logoUrl field for immediate preview
      setValue('logoUrl', tempUrl, { shouldDirty: true, shouldValidate: true });
      console.log('📸 Form field updated with temporary URL for preview');

      // Convert file to data URL for upload
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const dataUrl = e.target?.result as string;
          console.log('📸 Starting R2 upload...');

          // Upload to R2 storage
          const result = await uploadLogo({ dataUrl, brandKitId });

          if (result.success && result.url) {
            console.log('📸 R2 upload successful:', result.url);
            // Update form with permanent R2 URL
            setValue('logoUrl', result.url, { shouldDirty: true, shouldValidate: true });
            console.log('📸 Form updated with permanent R2 URL');
          } else {
            console.error('❌ R2 upload failed:', result.error);
            // Revert to original logo on failure
            setValue('logoUrl', brandKitData?.logoUrl || '', { shouldDirty: true, shouldValidate: true });
          }
        } catch (uploadError) {
          console.error('❌ Upload error:', uploadError);
          // Revert to original logo on failure
          setValue('logoUrl', brandKitData?.logoUrl || '', { shouldDirty: true, shouldValidate: true });
        }
      };

      reader.readAsDataURL(file);
    } catch (error) {
      console.error('❌ Logo upload failed:', error);
    }
  };

  // Enhanced color input component with visual swatches
  const ColorInput = ({ name, label, placeholder }: { name: string; label: string; placeholder: string }) => {
    const [colors, setColors] = React.useState<string[]>([]);
    const [inputValue, setInputValue] = React.useState('');

    // Get the field registration and current value from form
    const fieldRegistration = register(name);
    const watchedValue = useWatch({ control, name, defaultValue: '' });

    // Initialize with existing form values - handle both arrays and strings
    React.useEffect(() => {
      let currentValue = watchedValue || '';

      // If the value is an array (from database), convert to comma-separated string
      if (Array.isArray(currentValue)) {
        currentValue = currentValue.join(', ');
      }

      // Ensure it's a string
      if (typeof currentValue !== 'string') {
        currentValue = '';
      }

      setInputValue(currentValue);
    }, [watchedValue]);

    // Parse colors from comma-separated string
    React.useEffect(() => {
      if (inputValue && typeof inputValue === 'string') {
        const parsedColors = inputValue
          .split(',')
          .map((color: string) => color.trim())
          .filter((color: string) => color.match(/^#[0-9A-Fa-f]{6}$/));
        setColors(parsedColors);
      } else {
        setColors([]);
      }
    }, [inputValue]);

    const addColor = () => {
      const newColors = [...colors, '#676D50'];
      const newValue = newColors.join(', ');
      setInputValue(newValue);
      fieldRegistration.onChange({ target: { value: newValue } });
    };

    const updateColor = (index: number, newColor: string) => {
      const newColors = [...colors];
      newColors[index] = newColor;
      const newValue = newColors.join(', ');
      setInputValue(newValue);
      fieldRegistration.onChange({ target: { value: newValue } });
    };

    const removeColor = (index: number) => {
      const newColors = colors.filter((_, i) => i !== index);
      const newValue = newColors.join(', ');
      setInputValue(newValue);
      fieldRegistration.onChange({ target: { value: newValue } });
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setInputValue(value);
      fieldRegistration.onChange(e);
    };

    return (
      <FormGroup>
        <Label htmlFor={name}>{label}</Label>

        {/* Color Swatches */}
        <div className='flex flex-wrap gap-3 mb-3'>
          {colors.map((color, index) => (
            <div key={index} className='relative group'>
              <div className='flex flex-col items-center'>
                <label
                  className='block w-12 h-12 rounded-xl shadow-sm cursor-pointer hover:shadow-md transition-all duration-200 border-2 border-[#E8E4D4] hover:border-[#B5B178]'
                  style={{ backgroundColor: color }}
                >
                  <input
                    type='color'
                    value={color}
                    onChange={(e) => updateColor(index, e.target.value)}
                    className='sr-only'
                  />
                </label>
                <span className='text-xs text-[#676D50]/70 mt-1 font-mono'>{color}</span>
                <button
                  type='button'
                  onClick={() => removeColor(index)}
                  className='absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center hover:bg-red-600'
                >
                  ×
                </button>
              </div>
            </div>
          ))}

          {/* Add Color Button */}
          <button
            type='button'
            onClick={addColor}
            className='w-12 h-12 rounded-xl border-2 border-dashed border-[#B5B178] flex items-center justify-center text-[#676D50] hover:text-[#849068] hover:border-[#849068] transition-colors duration-200'
          >
            <span className='text-xl'>+</span>
          </button>
        </div>

        {/* Text Input for Manual Entry */}
        <Input
          id={name}
          name={name}
          type='text'
          placeholder={placeholder}
          className='text-sm font-mono'
          value={inputValue}
          onChange={handleInputChange}
          ref={fieldRegistration.ref}
        />
        <p className='text-xs text-[#676D50]/60 mt-1'>
          Enter hex colors separated by commas (e.g., #676D50, #849068) or use the color swatches above
        </p>
      </FormGroup>
    );
  };

  // Helper for creating text area fields
  const TextAreaField = ({
    name,
    label,
    placeholder,
    rows = 4,
  }: {
    name: string;
    label: string;
    placeholder: string;
    rows?: number;
  }) => (
    <FormGroup>
      <Label htmlFor={name}>{label}</Label>
      <TextArea {...register(name)} id={name} rows={rows} placeholder={placeholder} />
    </FormGroup>
  );

  switch (sectionId) {
    case 'logos':
      console.log('🔍 DEBUG - currentLogoUrl from parameter:', currentLogoUrl);
      console.log('🔍 DEBUG - brandKitData:', brandKitData);

      // Get logo with current form value taking priority
      const getBrandKitLogoWithFormData = (brandKitData: any, formLogoUrl: string) => {
        console.log('🔍 DEBUG - getBrandKitLogoWithFormData called with:', {
          formLogoUrl,
          brandKitData: !!brandKitData,
        });

        // Priority: form logoUrl > brandKitData logoUrl > logoVariations > favicon > fallback
        if (formLogoUrl && formLogoUrl.trim()) {
          console.log('🔍 DEBUG - Using form logoUrl:', formLogoUrl);
          return {
            type: 'image' as const,
            src: formLogoUrl,
            alt: `${brandKitData?.name || 'Brand'} logo`,
          };
        }

        console.log('🔍 DEBUG - Falling back to getBrandKitLogo');
        return getBrandKitLogo(brandKitData);
      };

      const brandLogo = getBrandKitLogoWithFormData(brandKitData, currentLogoUrl || '');
      console.log('🔍 DEBUG - Final brandLogo:', brandLogo);

      return (
        <Card variant='default'>
          <Card.Content>
            {/* Unified Logo Management Section */}
            <div className='space-y-6'>
              {/* Header */}
              <div className='text-center'>
                <h3 className='font-display text-2xl font-bold text-[#676D50] mb-2'>Brand Logos & Variations</h3>
                <p className='text-[#676D50]/70'>
                  Manage your primary logo and all its variations in one unified interface
                </p>
              </div>

              {/* Unified Logo Grid */}
              <UnifiedLogoManager
                control={control}
                setValue={setValue}
                register={register}
                brandKitData={brandKitData}
                brandKitId={brandKitId}
                currentLogoUrl={currentLogoUrl}
                handleLogoUpload={handleLogoUpload}
              />
            </div>

            <FormGrid columns={2}>
              <FormGroup>
                <Label htmlFor='logoUsageGuidelinesText'>Logo Usage Guidelines</Label>
                <TextArea
                  {...register('logoUsageGuidelinesText')}
                  id='logoUsageGuidelinesText'
                  rows={4}
                  placeholder='Describe how the logo should be used...'
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor='logoSizingText'>Logo Sizing Guidelines</Label>
                <TextArea
                  {...register('logoSizingText')}
                  id='logoSizingText'
                  rows={4}
                  placeholder='Minimum sizes, scaling guidelines...'
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor='logoClearSpaceText'>Clear Space Requirements</Label>
                <TextArea
                  {...register('logoClearSpaceText')}
                  id='logoClearSpaceText'
                  rows={4}
                  placeholder='Minimum clear space around logo...'
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor='logoColorUsageText'>Logo Color Usage</Label>
                <TextArea
                  {...register('logoColorUsageText')}
                  id='logoColorUsageText'
                  rows={4}
                  placeholder='When to use different logo variations...'
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor='logoMisuseText'>Logo Misuse Guidelines</Label>
                <TextArea
                  {...register('logoMisuseText')}
                  id='logoMisuseText'
                  rows={4}
                  placeholder='What not to do with the logo...'
                />
              </FormGroup>
            </FormGrid>
          </Card.Content>
        </Card>
      );

    case 'colors':
      return (
        <Card variant='default'>
          <Card.Content>
            <FormGrid columns={2}>
              <ColorInput name='primaryColors' label='Primary Colors' placeholder='e.g., #676D50, #849068' />
              <FormGroup>
                <Label htmlFor='primaryColorUsageText'>Primary Color Usage</Label>
                <TextArea
                  {...register('primaryColorUsageText')}
                  id='primaryColorUsageText'
                  rows={3}
                  placeholder='When and how to use primary colors...'
                />
              </FormGroup>

              <ColorInput name='secondaryColors' label='Secondary Colors' placeholder='e.g., #B5B178, #A6884C' />
              <FormGroup>
                <Label htmlFor='secondaryColorUsageText'>Secondary Color Usage</Label>
                <TextArea
                  {...register('secondaryColorUsageText')}
                  id='secondaryColorUsageText'
                  rows={3}
                  placeholder='When and how to use secondary colors...'
                />
              </FormGroup>

              <ColorInput name='accentColors' label='Accent Colors' placeholder='e.g., #9EA581, #F8F4DF' />
              <FormGroup>
                <Label htmlFor='accentColorUsageText'>Accent Color Usage</Label>
                <TextArea
                  {...register('accentColorUsageText')}
                  id='accentColorUsageText'
                  rows={3}
                  placeholder='When and how to use accent colors...'
                />
              </FormGroup>

              <div style={{ gridColumn: '1 / -1' }}>
                <FormGroup>
                  <Label htmlFor='colorAccessibilityText'>Color Accessibility Guidelines</Label>
                  <TextArea
                    {...register('colorAccessibilityText')}
                    id='colorAccessibilityText'
                    rows={4}
                    placeholder='Contrast ratios, accessibility considerations...'
                  />
                </FormGroup>
              </div>
            </FormGrid>
          </Card.Content>
        </Card>
      );

    case 'typography':
      return (
        <Card variant='default'>
          <Card.Content>
            <div className='space-y-8'>
              {/* Heading Typography */}
              <div className='space-y-4'>
                <h3 className='text-lg font-semibold text-[#676D50] border-b border-[#E8E4D4] pb-2'>
                  Heading Typography
                </h3>

                <FormGrid columns={2}>
                  <FormGroup>
                    <Label htmlFor='typography.headings.fontFamily'>Heading Font Family</Label>
                    <FontSelector
                      value={headingFont || ''}
                      onChange={(fontFamily) => {
                        setValue('typography.headings.fontFamily', fontFamily, { shouldDirty: true });
                        setFontRefreshCounter((prev) => prev + 1);
                      }}
                      placeholder='Search or upload heading font...'
                      brandKitId={brandKitId || ''}
                      brandKitData={brandKitData}
                      onFontUploaded={() => {
                        setFontRefreshCounter((prev) => prev + 1);
                      }}
                    />
                  </FormGroup>

                  <FormGroup>
                    <Label htmlFor='typography.headings.weights'>Font Weights</Label>
                    <Input
                      {...register('typography.headings.weights')}
                      id='typography.headings.weights'
                      type='text'
                      placeholder='e.g., 400, 600, 700'
                    />
                  </FormGroup>
                </FormGrid>

                {/* Heading Font Preview */}
                <div className='mt-4'>
                  <Label className='text-sm font-medium text-[#676D50] mb-2 block'>Preview</Label>
                  <FontPreview
                    fontFamily={headingFont || ''}
                    text='Heading Typography Preview - The Quick Brown Fox'
                    className='mb-2'
                    forceRefresh={fontRefreshCounter}
                  />
                  <FontPreview
                    fontFamily={headingFont || ''}
                    text='H1 Large Heading Style'
                    className='text-2xl font-bold'
                    forceRefresh={fontRefreshCounter}
                  />
                </div>
              </div>

              {/* Body Typography */}
              <div className='space-y-4'>
                <h3 className='text-lg font-semibold text-[#676D50] border-b border-[#E8E4D4] pb-2'>Body Typography</h3>

                <FormGrid columns={2}>
                  <FormGroup>
                    <Label htmlFor='typography.body.fontFamily'>Body Font Family</Label>
                    <FontSelector
                      value={bodyFont || ''}
                      onChange={(fontFamily) => {
                        setValue('typography.body.fontFamily', fontFamily, { shouldDirty: true });
                        setFontRefreshCounter((prev) => prev + 1);
                      }}
                      placeholder='Search or upload body font...'
                      brandKitId={brandKitId || ''}
                      brandKitData={brandKitData}
                      onFontUploaded={() => {
                        setFontRefreshCounter((prev) => prev + 1);
                      }}
                    />
                  </FormGroup>

                  <FormGroup>
                    <Label htmlFor='typography.body.baseSize'>Base Font Size</Label>
                    <Input
                      {...register('typography.body.baseSize')}
                      id='typography.body.baseSize'
                      type='text'
                      placeholder='e.g., 16px'
                    />
                  </FormGroup>
                </FormGrid>

                {/* Body Font Preview */}
                <div className='mt-4'>
                  <Label className='text-sm font-medium text-[#676D50] mb-2 block'>Preview</Label>
                  <FontPreview
                    fontFamily={bodyFont || ''}
                    text='This is how your body text will appear. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation.'
                    forceRefresh={fontRefreshCounter}
                  />
                </div>
              </div>
            </div>
          </Card.Content>
        </Card>
      );

    case 'imagery':
      return (
        <Card variant='default'>
          <Card.Header>
            <Card.Title>Imagery Style</Card.Title>
          </Card.Header>
          <Card.Content>
            <TextAreaField
              name='photoStyleText'
              label='Visual Style Guidelines'
              placeholder='Overall visual style, mood, aesthetic...'
            />
          </Card.Content>
        </Card>
      );

    case 'photography':
      return (
        <Card variant='default'>
          <Card.Header>
            <Card.Title>Photography Guidelines</Card.Title>
          </Card.Header>
          <Card.Content>
            <FormGrid columns={2}>
              <TextAreaField
                name='preferredAnglesText'
                label='Preferred Angles'
                placeholder='Camera angles, perspectives...'
              />

              <TextAreaField
                name='lightingPreferencesText'
                label='Lighting Preferences'
                placeholder='Natural light, studio lighting...'
              />

              <TextAreaField
                name='backgroundStylesText'
                label='Background Styles'
                placeholder='Background preferences, settings...'
              />

              <TextAreaField name='propGuidelinesText' label='Prop Guidelines' placeholder='Props to use or avoid...' />

              <TextAreaField name='commonScenesText' label='Common Scenes' placeholder='Typical scenes, scenarios...' />
            </FormGrid>
          </Card.Content>
        </Card>
      );

    case 'brand-voice':
      return (
        <Card variant='default'>
          <Card.Header>
            <Card.Title>Brand Voice</Card.Title>
          </Card.Header>
          <Card.Content>
            <FormGrid columns={2}>
              <TextAreaField
                name='brandPersonalityText'
                label='Brand Personality'
                placeholder='Personality traits, characteristics...'
              />

              <TextAreaField name='tonalityText' label='Tonality' placeholder='Tone of voice, communication style...' />

              <TextAreaField name='brandValuesText' label='Brand Values' placeholder='Core values, principles...' />

              <TextAreaField
                name='targetEmotionsText'
                label='Target Emotions'
                placeholder='Emotions to evoke in audience...'
              />
            </FormGrid>
          </Card.Content>
        </Card>
      );

    case 'language':
      return (
        <Card variant='default'>
          <Card.Header>
            <Card.Title>Language & Phrasing</Card.Title>
          </Card.Header>
          <Card.Content>
            <FormGrid columns={2}>
              <TextAreaField name='writingStyleText' label='Writing Style' placeholder='Writing style guidelines...' />

              <TextAreaField
                name='preferredTermsText'
                label='Preferred Terms'
                placeholder='Words and phrases to use...'
              />

              <TextAreaField
                name='avoidedTermsText'
                label='Avoided Terms'
                placeholder='Words and phrases to avoid...'
              />

              <TextAreaField
                name='grammarRulesText'
                label='Grammar Rules'
                placeholder='Specific grammar preferences...'
              />
            </FormGrid>
          </Card.Content>
        </Card>
      );

    case 'taglines':
      return (
        <Card variant='default'>
          <Card.Header>
            <Card.Title>Taglines & Messaging</Card.Title>
          </Card.Header>
          <Card.Content>
            <FormGrid columns={2}>
              <TextAreaField
                name='primaryTaglinesText'
                label='Primary Taglines'
                placeholder='Main taglines and slogans...'
              />

              <TextAreaField
                name='keyMessagesText'
                label='Key Messages'
                placeholder='Core messages to communicate...'
              />

              <TextAreaField
                name='valuePropositionsText'
                label='Value Propositions'
                placeholder='Unique value propositions...'
              />

              <TextAreaField
                name='taglineGuidelinesText'
                label='Tagline Guidelines'
                placeholder='How to use taglines effectively...'
              />
            </FormGrid>
          </Card.Content>
        </Card>
      );

    case 'ai-generation':
      return (
        <Card variant='default'>
          <Card.Header>
            <Card.Title>AI Generation Settings</Card.Title>
          </Card.Header>
          <Card.Content>
            <FormGrid columns={2}>
              <TextAreaField
                name='promptKeywordsText'
                label='Prompt Keywords'
                placeholder='Keywords to include in AI prompts...'
              />

              <TextAreaField
                name='avoidanceTermsText'
                label='Avoidance Terms'
                placeholder='Terms to avoid in AI generation...'
              />
            </FormGrid>
          </Card.Content>
        </Card>
      );

    default:
      return (
        <Card variant='default'>
          <Card.Content>
            <p className='text-gray-600'>Section content not implemented yet.</p>
          </Card.Content>
        </Card>
      );
  }
};

export const MainContent: React.FC<MainContentProps> = ({
  selectedSection,
  isTransitioning,
  register,
  control,
  setValue,
  isDirty,
  onSubmit,
  isSaving,
  brandKitId,
  brandKitData,
}) => {
  // Move all useWatch hooks to the top level to avoid conditional hook calls
  const currentLogoUrl = useWatch({ control, name: 'logoUrl', defaultValue: '' });
  const headingFont = useWatch({ control, name: 'typography.headings.fontFamily', defaultValue: '' });
  const bodyFont = useWatch({ control, name: 'typography.body.fontFamily', defaultValue: '' });

  return (
    <div className='brand-kit-main'>
      {/* Simple header with save button */}
      {isDirty && (
        <div className='brand-kit-header'>
          <div className='brand-kit-header-content'>
            <div className='brand-kit-save-button-container'>
              <SaveButton onClick={onSubmit} saving={isSaving} saved={false}>
                Save Changes
              </SaveButton>
            </div>
          </div>
        </div>
      )}

      {/* Content - Onboarding Style Layout */}
      <div className='brand-kit-content'>
        <div className={`brand-kit-content-wrapper ${isTransitioning ? 'transitioning' : ''}`}>
          {renderSectionContent(
            selectedSection,
            register,
            control,
            setValue,
            brandKitData,
            brandKitId,
            currentLogoUrl,
            headingFont,
            bodyFont
          )}
        </div>
      </div>
    </div>
  );
};
