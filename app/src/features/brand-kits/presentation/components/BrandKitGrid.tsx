/**
 * 🎨 Brand Kit Grid Component - MODULAR VERSION
 *
 * @description Displays brand kits in a responsive grid layout with cards
 * @responsibility Renders brand kit cards with preview images, actions, and delete functionality
 * @dependencies BrandKitListItem type, shared helpers, Lucide icons
 * @modular_context This is the modular version of BrandKitGrid specifically for the brand-kits feature
 *
 * @example
 * ```tsx
 * <BrandKitGrid
 *   brandKits={brandKits}
 *   onDelete={(id) => handleDelete(id)}
 * />
 * ```
 */

import React from 'react';
import { Link } from 'react-router-dom';
import { X, Camera, Palette, Building } from 'lucide-react';
import { getBrandkitLandingPages } from '../../../../shared/helpers';
import { BrandKitListItem } from '../../index';

interface BrandKitGridProps {
  brandKits: BrandKitListItem[];
  onDelete: (id: string) => void;
}

// Helper function to get favicon URL from domain
const getFaviconUrl = (websiteUrl: string): string => {
  try {
    const url = new URL(websiteUrl.startsWith('http') ? websiteUrl : `https://${websiteUrl}`);
    return `https://www.google.com/s2/favicons?domain=${url.hostname}&sz=64`;
  } catch {
    return '';
  }
};

// Helper function to get brand kit cover image
const getBrandKitCoverImage = (brandKit: BrandKitListItem): string => {
  // Priority: logoUrl > favicon from websiteUrl > default
  if (brandKit.logoUrl) {
    return brandKit.logoUrl;
  }

  if (brandKit.websiteUrl) {
    return getFaviconUrl(brandKit.websiteUrl);
  }

  return '';
};

// Helper function to get brand logo/icon
const getBrandIcon = (brandKit: BrandKitListItem) => {
  // First, try to get uploaded logo
  if (brandKit.logoVariations && brandKit.logoVariations.length > 0) {
    return {
      type: 'image' as const,
      src: brandKit.logoVariations[0],
      alt: `${brandKit.name} logo`,
    };
  }

  // If no logo, try to get favicon from website
  if (brandKit.websiteUrl && typeof brandKit.websiteUrl === 'string') {
    const faviconUrl = getFaviconUrl(brandKit.websiteUrl as string);
    if (faviconUrl) {
      return {
        type: 'image' as const,
        src: faviconUrl,
        alt: `${brandKit.name} favicon`,
      };
    }
  }

  // Fallback to generic icon
  return {
    type: 'icon' as const,
    component: Building,
    alt: 'Brand icon',
  };
};

const BrandKitCard: React.FC<{ brandKit: BrandKitListItem; onDelete: (id: string) => void }> = ({
  brandKit,
  onDelete,
}) => {
  const landingPage = getBrandkitLandingPages(brandKit)?.[0];
  const brandIcon = getBrandIcon(brandKit);
  const coverImage = getBrandKitCoverImage(brandKit);

  // Get first color from each color array or default
  const primaryColor = brandKit.primaryColors?.[0] || '#4A5568';
  const accentColor = brandKit.accentColors?.[0] || '#718096';

  return (
    <article
      className='group relative bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl'
      style={{
        borderTop: `4px solid ${primaryColor}`,
        borderBottom: `4px solid ${accentColor}`,
      }}
    >
      {/* Delete Button */}
      <button
        onClick={(e) => {
          e.stopPropagation();
          onDelete(brandKit.id);
        }}
        className='absolute top-3 right-3 bg-red-500 text-white p-1.5 rounded-full hover:bg-red-600 transition duration-300 z-10 opacity-0 group-hover:opacity-100'
      >
        <X size={14} />
      </button>

      {/* Preview Image */}
      <div className='relative h-48 bg-gray-100 dark:bg-gray-800 overflow-hidden'>
        <Link to={`/edit-brand-kit/${brandKit.id}`} className='block w-full h-full'>
          {coverImage ||
          brandKit.coverImage ||
          (Array.isArray(brandKit.moodboardImages) && brandKit.moodboardImages[0]) ? (
            <img
              src={
                coverImage ||
                brandKit.coverImage ||
                (Array.isArray(brandKit.moodboardImages) ? brandKit.moodboardImages[0] : undefined)
              }
              alt={`${brandKit.name} brand preview`}
              className='w-full h-full object-cover transition-all duration-300'
              onError={(e) => {
                // Fallback to palette icon if image fails to load
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                const parent = target.parentElement;
                if (parent && !parent.querySelector('.fallback-icon')) {
                  const fallbackDiv = document.createElement('div');
                  fallbackDiv.className =
                    'fallback-icon w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800';
                  fallbackDiv.innerHTML =
                    '<svg width="48" height="48" fill="currentColor" class="text-gray-300 dark:text-gray-500"><path d="M12 2l3.09 6.26L22 9l-5.91 5.74L17.18 22 12 19.27 6.82 22l1.09-7.26L2 9l6.91-.74L12 2z"></path></svg>';
                  parent.appendChild(fallbackDiv);
                }
              }}
            />
          ) : (
            <div className='w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800'>
              <Palette size={48} className='text-gray-300 dark:text-gray-500' />
            </div>
          )}
          <div className='absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-300'></div>
        </Link>
      </div>

      {/* Content */}
      <div className='p-6'>
        {/* Brand Icon and Title */}
        <div className='flex items-center mb-4'>
          <div className='w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center mr-3 overflow-hidden'>
            {brandIcon.type === 'image' ? (
              <img
                src={brandIcon.src as string}
                alt={brandIcon.alt}
                className='w-full h-full object-contain'
                onError={(e) => {
                  // Fallback to generic icon if image fails to load
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  const parent = target.parentElement;
                  if (parent) {
                    const fallbackIcon = document.createElement('div');
                    fallbackIcon.innerHTML =
                      '<svg width="24" height="24" fill="currentColor" class="text-gray-400"><path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z"></path></svg>';
                    parent.appendChild(fallbackIcon);
                  }
                }}
              />
            ) : (
              <Building size={24} className='text-gray-400 dark:text-gray-500' />
            )}
          </div>
          <div className='flex-1'>
            <Link to={`/edit-brand-kit/${brandKit.id}`}>
              <h3 className='text-xl font-semibold text-gray-800 dark:text-white hover:text-olive-600 dark:hover:text-olive-400 transition-colors duration-200 line-clamp-2'>
                {brandKit.name}
              </h3>
            </Link>
          </div>
        </div>

        {/* Action Buttons */}
        <div className='flex justify-between items-center pt-3 border-t border-gray-100 dark:border-gray-700'>
          <Link
            to={`/edit-brand-kit/${brandKit.id}`}
            className='flex items-center px-3 py-2 text-sm text-olive-600 dark:text-olive-400 hover:text-olive-700 dark:hover:text-olive-300 transition-colors duration-200 hover:bg-olive-50 dark:hover:bg-gray-700 rounded-lg'
          >
            <Palette size={16} className='mr-2' />
            Edit Brand
          </Link>
          <Link
            to={`/photography/${brandKit.id}`}
            className='flex items-center px-4 py-2 text-sm bg-olive-600 text-white rounded-lg hover:bg-olive-700 transition-colors duration-200 shadow-sm'
          >
            <Camera size={16} className='mr-2' />
            Generate Photos
          </Link>
        </div>
      </div>
    </article>
  );
};

/**
 * 🎨 Brand Kit Grid Component
 *
 * Renders a responsive grid of brand kit cards with preview images and actions
 */
export const BrandKitGrid: React.FC<BrandKitGridProps> = ({ brandKits, onDelete }) => {
  return (
    <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
      {brandKits.map((brandKit) => (
        <BrandKitCard key={brandKit.id} brandKit={brandKit} onDelete={onDelete} />
      ))}
    </div>
  );
};

export default BrandKitGrid;
