/**
 * 📋 Brand Kit Editor Sidebar
 *
 * @description Sidebar navigation for brand kit editor sections
 * @responsibility Provides section navigation and search functionality
 * @dependencies Section definitions, modular brand kit infrastructure
 * @ai_context This component is part of the modular brand kit presentation layer
 */

import React from 'react';
import { sections, SectionId } from '../constants/sections';

interface SidebarProps {
  selectedSection: SectionId;
  onSectionChange: (sectionId: SectionId) => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ selectedSection, onSectionChange }) => {
  return (
    <div className='brand-kit-sidebar'>
      <div className='brand-kit-sidebar-content'>
        <div className='brand-kit-sidebar-header'>
          <h2 className='brand-kit-sidebar-title'>Brand Kit Editor</h2>
          <p className='brand-kit-sidebar-subtitle'>Craft your visual identity</p>
        </div>

        <nav className='brand-kit-sidebar-nav'>
          {sections.map((section) => (
            <button
              key={section.id}
              onClick={() => onSectionChange(section.id)}
              className={`brand-kit-sidebar-button ${selectedSection === section.id ? 'active' : ''}`}
            >
              <div className='brand-kit-sidebar-button-title'>{section.title}</div>
              <div className='brand-kit-sidebar-button-description'>{section.description}</div>
            </button>
          ))}
        </nav>
      </div>
    </div>
  );
};
