/**
 * ✏️ Brand Kit Editor Component
 *
 * @description Main editor component for brand kit management
 * @responsibility Provides comprehensive brand kit editing interface
 * @dependencies Modular brand kit infrastructure, form components
 * @ai_context This component is part of the modular brand kit presentation layer
 */

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useQuery, useAction, getBrandKitById, updateBrandKit } from 'wasp/client/operations';
import { Sidebar } from './Sidebar';
import { MainContent } from './MainContent';
import { BrandKitFormData } from '../types';
import { SectionId, sections } from '../constants/sections';
import '../styles/BrandKitEditor.css';

interface BrandKitEditorProps {
  brandKitId: string;
}

export const BrandKitEditor: React.FC<BrandKitEditorProps> = ({ brandKitId }) => {
  // Get initial section from URL or default to 'logos'
  const getInitialSection = (): SectionId => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const sectionParam = urlParams.get('section') as SectionId;

      // Validate that the section exists in our sections
      if (sectionParam && sections.some((section) => section.id === sectionParam)) {
        return sectionParam;
      }
    }
    return 'logos';
  };

  const [selectedSection, setSelectedSection] = useState<SectionId>(getInitialSection);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Data fetching
  const { data: brandKitData, isLoading } = useQuery(getBrandKitById, { id: brandKitId });
  const updateBrandKitAction = useAction(updateBrandKit);
  const [isSaving, setIsSaving] = useState(false);

  // Calculate form default values from brandKitData during rendering
  const formDefaultValues = useMemo(() => {
    if (!brandKitData) {
      return {
        name: '',
        // Add default values for all form fields
        primaryColors: [],
        secondaryColors: [],
        accentColors: [],
        primaryColorUsageText: '',
        secondaryColorUsageText: '',
        accentColorUsageText: '',
        colorHierarchyText: '',
        colorAccessibilityText: '',
        colorCombinationsText: '',
        colorVariationsText: '',
        typography: {
          headings: {
            fontFamily: '',
            weights: [],
            sizes: {
              h1: '',
              h2: '',
              h3: '',
              h4: '',
              h5: '',
              h6: '',
            },
          },
          body: {
            fontFamily: '',
            weights: [],
            baseSize: '',
            lineHeight: '',
          },
        },
        logoVariations: [],
        moodboardImages: [],
        logoUsageGuidelinesText: '',
        logoSizingText: '',
        logoClearSpaceText: '',
        logoColorUsageText: '',
        logoFileFormatsText: '',
        logoMisuseText: '',
        photoStyleText: '',
        preferredAnglesText: '',
        lightingPreferencesText: '',
        backgroundStylesText: '',
        propGuidelinesText: '',
        commonScenesText: '',
        photoDosDonts: {
          dos: '',
          donts: '',
        },
        brandPersonalityText: '',
        tonalityText: '',
        brandValuesText: '',
        targetEmotionsText: '',
        writingStyleText: '',
        preferredTermsText: '',
        avoidedTermsText: '',
        grammarRulesText: '',
        contentStructureText: '',
        localizationRulesText: '',
        commonMistakesText: '',
        primaryTaglinesText: '',
        secondaryTaglinesText: '',
        campaignTaglinesText: '',
        keyMessagesText: '',
        valuePropositionsText: '',
        taglineGuidelinesText: '',
        trademarkInfoText: '',

        promptKeywordsText: '',
        avoidanceTermsText: '',
        preferredSettings: {
          quality: 'high',
          style: 'natural',
          format: 'jpg',
        },
      };
    }

    // Transform brandKitData to form structure during rendering
    return {
      name: brandKitData.name || '',

      // Visual Identity
      primaryColors: brandKitData.primaryColors || [],
      secondaryColors: brandKitData.secondaryColors || [],
      accentColors: brandKitData.accentColors || [],
      primaryColorUsageText: brandKitData.primaryColorUsageText || '',
      secondaryColorUsageText: brandKitData.secondaryColorUsageText || '',
      accentColorUsageText: brandKitData.accentColorUsageText || '',
      colorHierarchyText: brandKitData.colorHierarchyText || '',
      colorAccessibilityText: brandKitData.colorAccessibilityText || '',
      colorCombinationsText: brandKitData.colorCombinationsText || '',
      colorVariationsText: brandKitData.colorVariationsText || '',
      typography:
        typeof brandKitData.typography === 'object' && brandKitData.typography !== null
          ? (brandKitData.typography as any)
          : {
              headings: {
                fontFamily: '',
                weights: [],
                sizes: { h1: '', h2: '', h3: '', h4: '', h5: '', h6: '' },
              },
              body: {
                fontFamily: '',
                weights: [],
                baseSize: '',
                lineHeight: '',
              },
            },
      logoUrl: brandKitData.logoUrl || '',
      logoVariations: (brandKitData.logoVariations as string[]) || [],
      moodboardImages: brandKitData.moodboardImages || [],

      // Logo Guidelines
      logoUsageGuidelinesText: brandKitData.logoUsageGuidelinesText || '',
      logoSizingText: brandKitData.logoSizingText || '',
      logoClearSpaceText: brandKitData.logoClearSpaceText || '',
      logoColorUsageText: brandKitData.logoColorUsageText || '',
      logoFileFormatsText: brandKitData.logoFileFormatsText || '',
      logoMisuseText: brandKitData.logoMisuseText || '',

      // Photography Guidelines
      photoStyleText: brandKitData.photoStyleText || '',
      preferredAnglesText: brandKitData.preferredAnglesText || '',
      lightingPreferencesText: brandKitData.lightingPreferencesText || '',
      backgroundStylesText: brandKitData.backgroundStylesText || '',
      propGuidelinesText: brandKitData.propGuidelinesText || '',
      commonScenesText: brandKitData.commonScenesText || '',
      photoDosDonts:
        typeof brandKitData.photoDosDonts === 'object' && brandKitData.photoDosDonts !== null
          ? (brandKitData.photoDosDonts as any)
          : { dos: '', donts: '' },

      // Brand Voice
      brandPersonalityText: brandKitData.brandPersonalityText || '',
      tonalityText: brandKitData.tonalityText || '',
      brandValuesText: brandKitData.brandValuesText || '',
      targetEmotionsText: brandKitData.targetEmotionsText || '',

      // Language & Phrasing
      writingStyleText: brandKitData.writingStyleText || '',
      preferredTermsText: brandKitData.preferredTermsText || '',
      avoidedTermsText: brandKitData.avoidedTermsText || '',
      grammarRulesText: brandKitData.grammarRulesText || '',
      contentStructureText: brandKitData.contentStructureText || '',
      localizationRulesText: brandKitData.localizationRulesText || '',
      commonMistakesText: brandKitData.commonMistakesText || '',

      // Taglines
      primaryTaglinesText: brandKitData.primaryTaglinesText || '',
      secondaryTaglinesText: brandKitData.secondaryTaglinesText || '',
      campaignTaglinesText: brandKitData.campaignTaglinesText || '',
      keyMessagesText: brandKitData.keyMessagesText || '',
      valuePropositionsText: brandKitData.valuePropositionsText || '',
      taglineGuidelinesText: brandKitData.taglineGuidelinesText || '',
      trademarkInfoText: brandKitData.trademarkInfoText || '',

      // AI Generation Settings
      promptKeywordsText: brandKitData.promptKeywordsText || '',
      avoidanceTermsText: brandKitData.avoidanceTermsText || '',
      preferredSettings:
        typeof brandKitData.preferredSettings === 'object' && brandKitData.preferredSettings !== null
          ? (brandKitData.preferredSettings as any)
          : {
              quality: 'high',
              style: 'natural',
              format: 'jpg',
            },
    };
  }, [brandKitData]);

  // Form setup with empty defaults initially
  const form = useForm<BrandKitFormData>();

  const {
    register,
    control,
    handleSubmit,
    formState: { isDirty },
    reset,
    setValue,
  } = form;

  // Use useEffect to reset form when brandKitData loads (proper React Hook Form pattern)
  useEffect(() => {
    if (brandKitData) {
      reset(formDefaultValues);
    }
  }, [brandKitData, formDefaultValues, reset, brandKitId]);

  // Handle browser back/forward navigation and URL changes
  useEffect(() => {
    const handlePopState = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const sectionParam = urlParams.get('section') as SectionId;

      if (sectionParam && sections.some((section) => section.id === sectionParam)) {
        console.log('🔄 BrandKitEditor: Browser navigation detected, changing to section:', sectionParam);
        setSelectedSection(sectionParam);
      }
    };

    // Listen for browser back/forward navigation
    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, []);

  // Update URL when section changes
  const updateUrlSection = useCallback((sectionId: SectionId) => {
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.searchParams.set('section', sectionId);
      window.history.replaceState({}, '', url.toString());
      console.log('🔄 BrandKitEditor: Updated URL section to:', sectionId);
    }
  }, []);

  // Handle section change with transition
  const handleSectionChange = useCallback(
    (sectionId: SectionId) => {
      if (sectionId === selectedSection) return;

      setIsTransitioning(true);

      // Update URL immediately
      updateUrlSection(sectionId);

      setTimeout(() => {
        setSelectedSection(sectionId);
        setIsTransitioning(false);
      }, 150);
    },
    [selectedSection, updateUrlSection]
  );

  // Handle form submission
  const onSubmit = useCallback(
    async (data: BrandKitFormData) => {
      if (!isDirty) return;

      setIsSaving(true);
      try {
        console.log('🔄 BrandKitEditor: Submitting form data:', data);

        // Preserve existing customFonts when updating typography
        const updatedData = { ...data };
        if (data.typography && brandKitData?.typography) {
          console.log('🔄 BrandKitEditor: Preserving customFonts in typography');

          // Get existing typography data
          const existingTypography =
            typeof brandKitData.typography === 'string' ? JSON.parse(brandKitData.typography) : brandKitData.typography;

          // Merge form typography with existing customFonts
          updatedData.typography = {
            ...data.typography,
            customFonts: existingTypography.customFonts || [],
          } as any;

          console.log('🔄 BrandKitEditor: Updated typography with preserved customFonts:', updatedData.typography);
        }

        await updateBrandKitAction({
          id: brandKitId,
          ...updatedData,
        });

        console.log('✅ BrandKitEditor: Form submitted successfully');
      } catch (error) {
        console.error('❌ BrandKitEditor: Error saving brand kit:', error);
      } finally {
        setIsSaving(false);
      }
    },
    [brandKitId, isDirty, updateBrandKitAction, brandKitData]
  );

  if (isLoading) {
    return (
      <div className='brand-kit-loading'>
        <div className='brand-kit-loading-content'>
          <div className='brand-kit-loading-icon'>
            <div className='brand-kit-loading-icon-inner'></div>
          </div>
          <div className='brand-kit-loading-text'>Loading brand kit...</div>
        </div>
      </div>
    );
  }

  return (
    <div className='brand-kit-editor'>
      <Sidebar selectedSection={selectedSection} onSectionChange={handleSectionChange} />

      <MainContent
        selectedSection={selectedSection}
        isTransitioning={isTransitioning}
        register={register}
        control={control}
        setValue={setValue}
        isDirty={isDirty}
        onSubmit={handleSubmit(onSubmit)}
        brandKitId={brandKitId}
        isSaving={isSaving}
        brandKitData={brandKitData}
      />
    </div>
  );
};
