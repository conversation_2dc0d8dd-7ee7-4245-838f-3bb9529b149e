/**
 * 🎨 Brand Kits Feature Exports
 *
 * @description Central export point for the brand kits feature
 * @responsibility Provides clean imports for brand kit functionality
 * @dependencies All brand kit modules
 * @ai_context This is the main entry point for the brand kits feature
 *
 * @example
 * ```typescript
 * import { BrandKit, BrandKitService, createBrandKit } from '@features/brand-kits';
 * import { BrandKitStatus, ColorPalette } from '@features/brand-kits';
 * ```
 */

// Domain Layer Exports
export * from './domain/entities/brand-kit.entity';
export * from './domain/services/brand-kit.service';
export * from './domain/repositories/brand-kit.repository';

// Infrastructure Layer Exports
export { createBrandKit } from './infrastructure/wasp/actions/create-brand-kit';
export { updateBrandKit } from './infrastructure/wasp/actions/update-brand-kit';
export { updateBrandKitDetails } from './infrastructure/wasp/actions/update-brand-kit-details';
export { deleteBrandKit } from './infrastructure/wasp/actions/delete-brand-kit';
export { uploadBrandKitImages } from './infrastructure/wasp/actions/upload-brand-kit-images';
export { importBrandKitFromPDF } from './infrastructure/wasp/actions/import-brand-kit-from-pdf';
export { importBrandKitFromURL } from './infrastructure/wasp/actions/import-brand-kit-from-url';
export {
  insertCategoryFieldValue,
  updateCategoryFieldValue,
  deleteCategoryFieldValue,
} from './infrastructure/wasp/actions/category-field-operations';
export { getBrandKits } from './infrastructure/wasp/queries/get-brand-kits';
export { getBrandKitById } from './infrastructure/wasp/queries/get-brand-kit-by-id';
export { getBrandkitCategories } from './infrastructure/wasp/queries/get-brandkit-categories';
export { getBrandKitDataForProject } from './infrastructure/wasp/queries/get-brand-kit-data-for-project';
export { BrandKitRepositoryImpl } from './infrastructure/database/brand-kit.repository.impl';

// Presentation Layer Exports
export { default as BrandKitsPage } from './presentation/pages/BrandKitsPage';
export { default as EditBrandKitPage } from './presentation/pages/EditBrandKitPage';
export { BrandKitEditor } from './presentation/components/BrandKitEditor';
export { BrandKitGrid } from './presentation/components/BrandKitGrid';
export { BrandKitImportModal } from './presentation/components/modals/BrandKitImportModal';

// Re-export commonly used items for convenience
export { BrandKit, BrandKitStatus } from './domain/entities/brand-kit.entity';
export { BrandKitService } from './domain/services/brand-kit.service';

// Type exports
export type {
  ColorPalette,
  Typography,
  PhotographyGuidelines,
  BrandVoice,
  AIGenerationSettings,
} from './domain/entities/brand-kit.entity';

export type {
  CreateBrandKitInput as CreateBrandKitServiceInput,
  UpdateBrandKitInput,
  ListBrandKitsOptions,
} from './domain/services/brand-kit.service';

export type {
  IBrandKitRepository,
  BrandKitListOptions,
  BrandKitSearchOptions,
  BrandKitStatistics,
} from './domain/repositories/brand-kit.repository';

export type {
  CreateBrandKitInput as CreateBrandKitActionInput,
  CreateBrandKitOutput,
} from './infrastructure/wasp/actions/create-brand-kit';

export type {
  UpdateBrandKitInput as UpdateBrandKitActionInput,
  UpdateBrandKitOutput,
} from './infrastructure/wasp/actions/update-brand-kit';

export type {
  GetBrandKitByIdInput as GetBrandKitByIdQueryInput,
  GetBrandKitByIdOutput,
} from './infrastructure/wasp/queries/get-brand-kit-by-id';

export type {
  GetBrandKitsInput as GetBrandKitsQueryInput,
  GetBrandKitsOutput,
  BrandKitListItem,
} from './infrastructure/wasp/queries/get-brand-kits';

export type {
  DeleteBrandKitInput as DeleteBrandKitActionInput,
  DeleteBrandKitOutput,
} from './infrastructure/wasp/actions/delete-brand-kit';

export type {
  UpdateBrandKitDetailsInput as UpdateBrandKitDetailsActionInput,
  UpdateBrandKitDetailsOutput,
} from './infrastructure/wasp/actions/update-brand-kit-details';

export type {
  UploadBrandKitImagesInput as UploadBrandKitImagesActionInput,
  UploadBrandKitImagesOutput,
} from './infrastructure/wasp/actions/upload-brand-kit-images';

export type {
  ImportBrandKitFromPDFInput as ImportBrandKitFromPDFActionInput,
  ImportBrandKitFromPDFOutput,
} from './infrastructure/wasp/actions/import-brand-kit-from-pdf';

export type {
  ImportBrandKitFromURLInput as ImportBrandKitFromURLActionInput,
  ImportBrandKitFromURLOutput,
} from './infrastructure/wasp/actions/import-brand-kit-from-url';

export type {
  InsertCategoryFieldValueInput,
  UpdateCategoryFieldValueInput,
  DeleteCategoryFieldValueInput,
  CategoryFieldOperationOutput,
} from './infrastructure/wasp/actions/category-field-operations';

export type {
  GetBrandkitCategoriesInput as GetBrandkitCategoriesQueryInput,
  GetBrandkitCategoriesOutput,
  BrandCategoryItem,
  CategoryFieldItem,
} from './infrastructure/wasp/queries/get-brandkit-categories';

export type {
  GetBrandKitDataForProjectInput as GetBrandKitDataForProjectQueryInput,
  GetBrandKitDataForProjectOutput,
  ProjectBrandKitData,
} from './infrastructure/wasp/queries/get-brand-kit-data-for-project';
