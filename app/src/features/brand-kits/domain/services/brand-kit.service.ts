/**
 * 🎨 Brand Kit Domain Service
 *
 * @description Core business logic for brand kit operations
 * @responsibility Orchestrates brand kit creation, updates, and business rules
 * @dependencies BrandKit entity, repository interfaces, event bus
 * @ai_context This service contains all the business logic for brand kit management
 *
 * @example
 * ```typescript
 * const service = new BrandKitService(repository, eventBus);
 *
 * const brandKit = await service.createBrandKit({
 *   name: "Acme Corp",
 *   userId: 123,
 *   organizationId: "org_456"
 * });
 *
 * await service.updateBrandKit(brandKit.id, {
 *   primaryColors: ["#FF6B6B"]
 * });
 * ```
 */

import { BrandKit, BrandKitStatus } from '../entities/brand-kit.entity';
import type { IBrandKitRepository } from '../repositories/brand-kit.repository';
// Temporarily removed EventBus dependency to fix import issues
// import { EventBus, EventType } from '../../../core';
import { v4 as uuidv4 } from 'uuid';

/**
 * 🏗️ Brand Kit Creation Input
 * @ai_context Data required to create a new brand kit
 */
export interface CreateBrandKitInput {
  name: string;
  userId: string;
  organizationId: string;
  tags?: string[];
  initialData?: {
    primaryColors?: string[];
    secondaryColors?: string[];
    accentColors?: string[];
    typography?: any;
    logoVariations?: string[];
    moodboardImages?: string[];
  };
}

/**
 * 🔄 Brand Kit Update Input
 * @ai_context Data for updating an existing brand kit
 */
export interface UpdateBrandKitInput {
  name?: string;
  tags?: string[];
  primaryColors?: string[];
  secondaryColors?: string[];
  accentColors?: string[];
  typography?: any;
  logoVariations?: string[];
  moodboardImages?: string[];
  photoStyleText?: string;
  preferredAnglesText?: string;
  lightingPreferencesText?: string;
  backgroundStylesText?: string;
  propGuidelinesText?: string;
  commonScenesText?: string;
  photoDosDonts?: any;
  brandPersonalityText?: string;
  tonalityText?: string;
  brandValuesText?: string;
  targetEmotionsText?: string;
  writingStyleText?: string;
  preferredTermsText?: string;
  avoidedTermsText?: string;
  promptKeywordsText?: string;
  avoidanceTermsText?: string;
  preferredSettings?: any;
  // Note: status is managed through deletedAt field
}

/**
 * 📊 Brand Kit List Options
 * @ai_context Options for listing brand kits
 */
export interface ListBrandKitsOptions {
  organizationId?: string;
  userId?: string;
  // Note: status is managed through deletedAt field
  tags?: string[];
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

/**
 * 🎨 Brand Kit Domain Service
 * @ai_context Core business logic for brand kit management
 */
export class BrandKitService {
  constructor(
    private readonly repository: IBrandKitRepository
    // Temporarily removed EventBus dependency
    // private readonly eventBus: EventBus
  ) { }

  /**
   * 🏗️ Create a new brand kit
   * @param input Brand kit creation data
   * @returns Created brand kit
   */
  async createBrandKit(input: CreateBrandKitInput): Promise<BrandKit> {
    // Validate input
    this.validateCreateInput(input);

    // Check for duplicate names within organization
    const existingBrandKit = await this.repository.findByNameAndOrganization(input.name, input.organizationId);

    if (existingBrandKit) {
      throw new Error(`Brand kit with name "${input.name}" already exists in this organization`);
    }

    // Create brand kit entity
    const brandKit = BrandKit.create({
      id: uuidv4(),
      name: input.name,
      userId: input.userId,
      organizationId: input.organizationId,
      tags: input.tags,
      colorPalette: {
        primary: input.initialData?.primaryColors || [],
        secondary: input.initialData?.secondaryColors || [],
        accent: input.initialData?.accentColors || [],
        neutral: [],
      },
      typography: input.initialData?.typography,
      logoVariations: input.initialData?.logoVariations,
      moodboardImages: input.initialData?.moodboardImages,
    });

    // Validate the created brand kit
    const validation = brandKit.validate();
    if (!validation.isValid) {
      throw new Error(`Invalid brand kit data: ${validation.errors.join(', ')}`);
    }

    // Save to repository
    await this.repository.save(brandKit);

    // TODO: Emit domain event when EventBus is available
    // this.eventBus.publish(EventType.BRAND_KIT_CREATED, {
    //   brandKitId: brandKit.id,
    //   name: brandKit.name,
    //   userId: brandKit.userId,
    //   organizationId: brandKit.organizationId
    // }, {
    //   userId: brandKit.userId,
    //   organizationId: brandKit.organizationId
    // });

    return brandKit;
  }

  /**
   * 🔄 Update an existing brand kit
   * @param id Brand kit ID
   * @param input Update data
   * @returns Updated brand kit
   */
  async updateBrandKit(id: string, input: UpdateBrandKitInput): Promise<BrandKit> {
    // Find existing brand kit
    const existingBrandKit = await this.repository.findById(id);
    if (!existingBrandKit) {
      throw new Error(`Brand kit with ID ${id} not found`);
    }

    // Check for name conflicts if name is being updated
    if (input.name && input.name !== existingBrandKit.name) {
      const conflictingBrandKit = await this.repository.findByNameAndOrganization(
        input.name,
        existingBrandKit.organizationId
      );

      if (conflictingBrandKit && conflictingBrandKit.id !== id) {
        throw new Error(`Brand kit with name "${input.name}" already exists in this organization`);
      }
    }

    // Create updated entity data
    const entityData = existingBrandKit.toEntity();
    const updatedData = {
      ...entityData,
      ...input,
      updatedAt: new Date(),
    };

    // Create new brand kit instance with updated data
    const updatedBrandKit = BrandKit.fromEntity(updatedData as any);

    // Validate the updated brand kit
    const validation = updatedBrandKit.validate();
    if (!validation.isValid) {
      throw new Error(`Invalid brand kit data: ${validation.errors.join(', ')}`);
    }

    // Save to repository
    await this.repository.save(updatedBrandKit);

    // TODO: Emit domain event when EventBus is available
    // this.eventBus.publish(EventType.BRAND_KIT_UPDATED, {
    //   brandKitId: updatedBrandKit.id,
    //   name: updatedBrandKit.name,
    //   changes: input,
    //   userId: updatedBrandKit.userId,
    //   organizationId: updatedBrandKit.organizationId
    // }, {
    //   userId: updatedBrandKit.userId,
    //   organizationId: updatedBrandKit.organizationId
    // });

    return updatedBrandKit;
  }

  /**
   * 🔍 Get brand kit by ID
   * @param id Brand kit ID
   * @returns Brand kit or null if not found
   */
  async getBrandKitById(id: string): Promise<BrandKit | null> {
    return await this.repository.findById(id);
  }

  /**
   * 📋 List brand kits with filtering and pagination
   * @param options List options
   * @returns Paginated brand kits
   */
  async listBrandKits(options: ListBrandKitsOptions = {}) {
    return await this.repository.findMany(options);
  }

  /**
   * 🗑️ Delete a brand kit
   * @param id Brand kit ID
   * @param userId User performing the deletion
   */
  async deleteBrandKit(id: string, userId: string): Promise<void> {
    const brandKit = await this.repository.findById(id);
    if (!brandKit) {
      throw new Error(`Brand kit with ID ${id} not found`);
    }

    // Check permissions (user must own the brand kit)
    if (brandKit.userId !== userId) {
      throw new Error('You do not have permission to delete this brand kit');
    }

    // Soft delete
    await this.repository.delete(id);

    // TODO: Emit domain event when EventBus is available
    // this.eventBus.publish(EventType.BRAND_KIT_DELETED, {
    //   brandKitId: id,
    //   name: brandKit.name,
    //   userId: brandKit.userId,
    //   organizationId: brandKit.organizationId
    // }, {
    //   userId: brandKit.userId,
    //   organizationId: brandKit.organizationId
    // });
  }

  /**
   * 📊 Get brand kit analytics
   * @param organizationId Organization ID
   * @returns Analytics data
   */
  async getBrandKitAnalytics(organizationId: string) {
    const brandKits = await this.repository.findByOrganization(organizationId);

    const analytics = {
      total: brandKits.length,
      byStatus: {} as Record<BrandKitStatus, number>,
      completionStats: {
        complete: 0,
        incomplete: 0,
        averageCompletion: 0,
      },
      recentActivity: brandKits.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime()).slice(0, 5),
    };

    // Calculate status distribution (based on deletedAt)
    analytics.byStatus[BrandKitStatus.ACTIVE] = brandKits.filter((bk) => !bk.deletedAt).length;
    analytics.byStatus[BrandKitStatus.ARCHIVED] = brandKits.filter((bk) => bk.deletedAt).length;

    // Calculate completion stats
    const completionData = brandKits.map((bk) => bk.getCompletionStatus());
    analytics.completionStats.complete = completionData.filter((c) => c.isComplete).length;
    analytics.completionStats.incomplete = completionData.filter((c) => !c.isComplete).length;
    analytics.completionStats.averageCompletion = Math.round(
      completionData.reduce((sum, c) => sum + c.completionPercentage, 0) / brandKits.length
    );

    return analytics;
  }

  /**
   * 🔍 Search brand kits
   * @param query Search query
   * @param organizationId Organization ID
   * @returns Matching brand kits
   */
  async searchBrandKits(query: string, organizationId: string): Promise<BrandKit[]> {
    if (!query.trim()) {
      return [];
    }

    return await this.repository.search(query, organizationId);
  }

  /**
   * ✅ Validate create input
   * @param input Input to validate
   */
  private validateCreateInput(input: CreateBrandKitInput): void {
    if (!input.name?.trim()) {
      throw new Error('Brand kit name is required');
    }

    if (input.name.length > 100) {
      throw new Error('Brand kit name must be 100 characters or less');
    }

    if (!input.userId?.trim()) {
      throw new Error('Valid user ID is required');
    }

    if (!input.organizationId?.trim()) {
      throw new Error('Organization ID is required');
    }

    // Validate tags
    if (input.tags) {
      if (input.tags.length > 10) {
        throw new Error('Maximum 10 tags allowed');
      }

      for (const tag of input.tags) {
        if (!tag.trim()) {
          throw new Error('Tags cannot be empty');
        }
        if (tag.length > 50) {
          throw new Error('Tags must be 50 characters or less');
        }
      }
    }

    // Validate initial colors if provided
    if (input.initialData?.primaryColors) {
      for (const color of input.initialData.primaryColors) {
        if (!/^#[0-9A-Fa-f]{6}$/.test(color)) {
          throw new Error(`Invalid color format: ${color}`);
        }
      }
    }
  }
}
