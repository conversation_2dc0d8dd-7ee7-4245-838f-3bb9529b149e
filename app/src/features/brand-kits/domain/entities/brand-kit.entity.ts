/**
 * 🎨 Brand Kit Domain Entity
 *
 * @description Core domain model for brand identity management
 * @responsibility Encapsulates brand kit business rules and validation
 * @dependencies None (pure domain model)
 * @ai_context This entity represents a complete brand identity including colors, typography, logos, and guidelines
 *
 * @example
 * ```typescript
 * const brandKit = new BrandKit({
 *   name: "Acme Corp Brand",
 *   primaryColors: ["#FF6B6B", "#4ECDC4"],
 *   typography: { heading: "Montserrat", body: "Open Sans" }
 * });
 *
 * brandKit.addColor("#FF6B6B", "primary");
 * brandKit.validateColors(); // Returns validation result
 * ```
 */

import { z } from 'zod';
import type { BrandKit as BrandKitEntity } from 'wasp/entities';

/**
 * 🎨 Color Palette Schema
 * @ai_context Defines the structure for brand color management
 */
export const ColorPaletteSchema = z.object({
  primary: z
    .array(z.string().regex(/^#[0-9A-Fa-f]{6}$/))
    .min(1)
    .max(5),
  secondary: z.array(z.string().regex(/^#[0-9A-Fa-f]{6}$/)).max(10),
  accent: z.array(z.string().regex(/^#[0-9A-Fa-f]{6}$/)).max(5),
  neutral: z.array(z.string().regex(/^#[0-9A-Fa-f]{6}$/)).max(10),
});

export type ColorPalette = z.infer<typeof ColorPaletteSchema>;

/**
 * 📝 Typography Schema
 * @ai_context Defines typography guidelines structure
 */
export const TypographySchema = z.object({
  heading: z.object({
    fontFamily: z.string(),
    fontWeight: z.number().optional(),
    fontSize: z.string().optional(),
    lineHeight: z.string().optional(),
  }),
  body: z.object({
    fontFamily: z.string(),
    fontWeight: z.number().optional(),
    fontSize: z.string().optional(),
    lineHeight: z.string().optional(),
  }),
  caption: z
    .object({
      fontFamily: z.string(),
      fontWeight: z.number().optional(),
      fontSize: z.string().optional(),
      lineHeight: z.string().optional(),
    })
    .optional(),
});

export type Typography = z.infer<typeof TypographySchema>;

/**
 * 📸 Photography Guidelines Schema
 * @ai_context Defines photography style guidelines
 */
export const PhotographyGuidelinesSchema = z.object({
  style: z.string(),
  preferredAngles: z.array(z.string()),
  lightingPreferences: z.array(z.string()),
  backgroundStyles: z.array(z.string()),
  propGuidelines: z.array(z.string()),
  commonScenes: z.array(z.string()),
  dosDonts: z.object({
    dos: z.array(z.string()),
    donts: z.array(z.string()),
  }),
});

export type PhotographyGuidelines = z.infer<typeof PhotographyGuidelinesSchema>;

/**
 * 🗣️ Brand Voice Schema
 * @ai_context Defines brand voice and messaging guidelines
 */
export const BrandVoiceSchema = z.object({
  personality: z.array(z.string()),
  tonality: z.array(z.string()),
  values: z.array(z.string()),
  targetEmotions: z.array(z.string()),
  writingStyle: z.string(),
  preferredTerms: z.array(z.string()),
  avoidedTerms: z.array(z.string()),
});

export type BrandVoice = z.infer<typeof BrandVoiceSchema>;

/**
 * 🤖 AI Generation Settings Schema
 * @ai_context Defines settings for AI-powered content generation
 */
export const AIGenerationSettingsSchema = z.object({
  promptKeywords: z.array(z.string()),
  avoidanceTerms: z.array(z.string()),
  preferredStyles: z.array(z.string()),
  qualitySettings: z.object({
    imageQuality: z.enum(['standard', 'high', 'ultra']).default('high'),
    textComplexity: z.enum(['simple', 'moderate', 'complex']).default('moderate'),
  }),
});

export type AIGenerationSettings = z.infer<typeof AIGenerationSettingsSchema>;

/**
 * 📊 Brand Kit Status Enum
 * @ai_context Possible states of a brand kit (for future use)
 * @note Currently not stored in database - using deletedAt for soft delete
 */
export enum BrandKitStatus {
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED',
}

/**
 * 🎨 Brand Kit Domain Entity Class
 * @ai_context Core domain model with business logic and validation
 */
export class BrandKit {
  private constructor(
    public readonly id: string,
    public readonly name: string,
    public readonly userId: string,
    public readonly organizationId: string,
    public readonly colorPalette: ColorPalette,
    public readonly typography: Typography,
    public readonly logoVariations: string[],
    public readonly moodboardImages: string[],
    public readonly photographyGuidelines: PhotographyGuidelines,
    public readonly brandVoice: BrandVoice,
    public readonly aiGenerationSettings: AIGenerationSettings,
    public readonly tags: string[],
    public readonly createdAt: Date,
    public readonly updatedAt: Date,
    public readonly deletedAt: Date | null = null
  ) {}

  /**
   * 🏗️ Create a new brand kit
   * @param data Brand kit creation data
   * @returns New BrandKit instance
   */
  static create(data: {
    id: string;
    name: string;
    userId: string;
    organizationId: string;
    colorPalette?: Partial<ColorPalette>;
    typography?: Partial<Typography>;
    logoVariations?: string[];
    moodboardImages?: string[];
    photographyGuidelines?: Partial<PhotographyGuidelines>;
    brandVoice?: Partial<BrandVoice>;
    aiGenerationSettings?: Partial<AIGenerationSettings>;
    tags?: string[];
  }): BrandKit {
    // Validate required fields
    if (!data.name.trim()) {
      throw new Error('Brand kit name is required');
    }

    if (data.name.length > 100) {
      throw new Error('Brand kit name must be 100 characters or less');
    }

    // Set defaults
    const colorPalette: ColorPalette = {
      primary: data.colorPalette?.primary || [],
      secondary: data.colorPalette?.secondary || [],
      accent: data.colorPalette?.accent || [],
      neutral: data.colorPalette?.neutral || [],
    };

    const typography: Typography = {
      heading: data.typography?.heading || { fontFamily: 'Inter' },
      body: data.typography?.body || { fontFamily: 'Inter' },
      caption: data.typography?.caption,
    };

    const photographyGuidelines: PhotographyGuidelines = {
      style: data.photographyGuidelines?.style || '',
      preferredAngles: data.photographyGuidelines?.preferredAngles || [],
      lightingPreferences: data.photographyGuidelines?.lightingPreferences || [],
      backgroundStyles: data.photographyGuidelines?.backgroundStyles || [],
      propGuidelines: data.photographyGuidelines?.propGuidelines || [],
      commonScenes: data.photographyGuidelines?.commonScenes || [],
      dosDonts: data.photographyGuidelines?.dosDonts || { dos: [], donts: [] },
    };

    const brandVoice: BrandVoice = {
      personality: data.brandVoice?.personality || [],
      tonality: data.brandVoice?.tonality || [],
      values: data.brandVoice?.values || [],
      targetEmotions: data.brandVoice?.targetEmotions || [],
      writingStyle: data.brandVoice?.writingStyle || '',
      preferredTerms: data.brandVoice?.preferredTerms || [],
      avoidedTerms: data.brandVoice?.avoidedTerms || [],
    };

    const aiGenerationSettings: AIGenerationSettings = {
      promptKeywords: data.aiGenerationSettings?.promptKeywords || [],
      avoidanceTerms: data.aiGenerationSettings?.avoidanceTerms || [],
      preferredStyles: data.aiGenerationSettings?.preferredStyles || [],
      qualitySettings: data.aiGenerationSettings?.qualitySettings || {
        imageQuality: 'high',
        textComplexity: 'moderate',
      },
    };

    const now = new Date();

    return new BrandKit(
      data.id,
      data.name.trim(),
      data.userId,
      data.organizationId,
      colorPalette,
      typography,
      data.logoVariations || [],
      data.moodboardImages || [],
      photographyGuidelines,
      brandVoice,
      aiGenerationSettings,
      data.tags || [],
      now,
      now,
      null // deletedAt
    );
  }

  /**
   * 🏗️ Create from database entity
   * @param entity Database entity
   * @returns BrandKit domain model
   */
  static fromEntity(entity: BrandKitEntity): BrandKit {
    // Parse JSON fields safely
    const typography = entity.typography
      ? typeof entity.typography === 'string'
        ? JSON.parse(entity.typography)
        : entity.typography
      : { heading: { fontFamily: 'Inter' }, body: { fontFamily: 'Inter' } };

    return new BrandKit(
      entity.id,
      entity.name,
      entity.userId,
      entity.organizationId,
      {
        primary: entity.primaryColors || [],
        secondary: entity.secondaryColors || [],
        accent: entity.accentColors || [],
        neutral: [], // Add neutral colors if needed
      },
      typography,
      (entity.logoVariations as string[]) || [],
      entity.moodboardImages || [],
      {
        style: entity.photoStyleText || '',
        preferredAngles:
          entity.preferredAnglesText
            ?.split(',')
            .map((s) => s.trim())
            .filter(Boolean) || [],
        lightingPreferences:
          entity.lightingPreferencesText
            ?.split(',')
            .map((s) => s.trim())
            .filter(Boolean) || [],
        backgroundStyles:
          entity.backgroundStylesText
            ?.split(',')
            .map((s) => s.trim())
            .filter(Boolean) || [],
        propGuidelines:
          entity.propGuidelinesText
            ?.split(',')
            .map((s) => s.trim())
            .filter(Boolean) || [],
        commonScenes:
          entity.commonScenesText
            ?.split(',')
            .map((s) => s.trim())
            .filter(Boolean) || [],
        dosDonts: (entity.photoDosDonts as { dos: string[]; donts: string[] }) || { dos: [], donts: [] },
      },
      {
        personality:
          entity.brandPersonalityText
            ?.split(',')
            .map((s) => s.trim())
            .filter(Boolean) || [],
        tonality:
          entity.tonalityText
            ?.split(',')
            .map((s) => s.trim())
            .filter(Boolean) || [],
        values:
          entity.brandValuesText
            ?.split(',')
            .map((s) => s.trim())
            .filter(Boolean) || [],
        targetEmotions:
          entity.targetEmotionsText
            ?.split(',')
            .map((s) => s.trim())
            .filter(Boolean) || [],
        writingStyle: entity.writingStyleText || '',
        preferredTerms:
          entity.preferredTermsText
            ?.split(',')
            .map((s) => s.trim())
            .filter(Boolean) || [],
        avoidedTerms:
          entity.avoidedTermsText
            ?.split(',')
            .map((s) => s.trim())
            .filter(Boolean) || [],
      },
      {
        promptKeywords:
          entity.promptKeywordsText
            ?.split(',')
            .map((s) => s.trim())
            .filter(Boolean) || [],
        avoidanceTerms:
          entity.avoidanceTermsText
            ?.split(',')
            .map((s) => s.trim())
            .filter(Boolean) || [],
        preferredStyles: [],
        qualitySettings: (entity.preferredSettings as any) || { imageQuality: 'high', textComplexity: 'moderate' },
      },
      entity.tags || [],
      entity.createdAt,
      entity.updatedAt,
      entity.deletedAt
    );
  }

  /**
   * ✅ Validate the brand kit data
   * @returns Validation result with errors if any
   */
  validate(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate color palette
    try {
      ColorPaletteSchema.parse(this.colorPalette);
    } catch (error) {
      errors.push('Invalid color palette format');
    }

    // Validate typography
    try {
      TypographySchema.parse(this.typography);
    } catch (error) {
      errors.push('Invalid typography format');
    }

    // Validate photography guidelines
    try {
      PhotographyGuidelinesSchema.parse(this.photographyGuidelines);
    } catch (error) {
      errors.push('Invalid photography guidelines format');
    }

    // Validate brand voice
    try {
      BrandVoiceSchema.parse(this.brandVoice);
    } catch (error) {
      errors.push('Invalid brand voice format');
    }

    // Validate AI generation settings
    try {
      AIGenerationSettingsSchema.parse(this.aiGenerationSettings);
    } catch (error) {
      errors.push('Invalid AI generation settings format');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 📊 Check if brand kit is complete
   * @returns Completion status and missing sections
   */
  getCompletionStatus(): {
    isComplete: boolean;
    completionPercentage: number;
    missingSections: string[];
  } {
    const sections = [
      { name: 'colors', complete: this.colorPalette.primary.length > 0 },
      { name: 'typography', complete: this.typography.heading.fontFamily !== '' },
      { name: 'logos', complete: this.logoVariations.length > 0 },
      { name: 'photography', complete: this.photographyGuidelines.style !== '' },
      { name: 'brand_voice', complete: this.brandVoice.personality.length > 0 },
      { name: 'ai_settings', complete: this.aiGenerationSettings.promptKeywords.length > 0 },
    ];

    const completedSections = sections.filter((section) => section.complete);
    const missingSections = sections.filter((section) => !section.complete).map((section) => section.name);

    return {
      isComplete: missingSections.length === 0,
      completionPercentage: Math.round((completedSections.length / sections.length) * 100),
      missingSections,
    };
  }

  /**
   * 🔄 Convert to database entity format
   * @returns Data suitable for database storage
   */
  toEntity(): Partial<BrandKitEntity> {
    return {
      id: this.id,
      name: this.name,
      userId: this.userId,
      organizationId: this.organizationId,
      primaryColors: this.colorPalette.primary,
      secondaryColors: this.colorPalette.secondary,
      accentColors: this.colorPalette.accent,
      typography: this.typography,
      logoVariations: this.logoVariations as any,
      moodboardImages: this.moodboardImages,
      photoStyleText: this.photographyGuidelines.style,
      preferredAnglesText: this.photographyGuidelines.preferredAngles.join(', '),
      lightingPreferencesText: this.photographyGuidelines.lightingPreferences.join(', '),
      backgroundStylesText: this.photographyGuidelines.backgroundStyles.join(', '),
      propGuidelinesText: this.photographyGuidelines.propGuidelines.join(', '),
      commonScenesText: this.photographyGuidelines.commonScenes.join(', '),
      photoDosDonts: this.photographyGuidelines.dosDonts as any,
      brandPersonalityText: this.brandVoice.personality.join(', '),
      tonalityText: this.brandVoice.tonality.join(', '),
      brandValuesText: this.brandVoice.values.join(', '),
      targetEmotionsText: this.brandVoice.targetEmotions.join(', '),
      writingStyleText: this.brandVoice.writingStyle,
      preferredTermsText: this.brandVoice.preferredTerms.join(', '),
      avoidedTermsText: this.brandVoice.avoidedTerms.join(', '),
      promptKeywordsText: this.aiGenerationSettings.promptKeywords.join(', '),
      avoidanceTermsText: this.aiGenerationSettings.avoidanceTerms.join(', '),
      preferredSettings: this.aiGenerationSettings.qualitySettings as any,
      tags: this.tags,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      deletedAt: this.deletedAt,
    };
  }
}
