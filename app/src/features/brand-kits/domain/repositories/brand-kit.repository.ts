/**
 * 🗄️ Brand Kit Repository Interface
 *
 * @description Contract for brand kit data operations
 * @responsibility Defines data access methods for brand kits
 * @dependencies BrandKit entity, shared repository types
 * @ai_context This interface defines how brand kit data is accessed and stored
 *
 * @example
 * ```typescript
 * class BrandKitRepositoryImpl implements IBrandKitRepository {
 *   async save(brandKit: BrandKit): Promise<void> {
 *     // Implementation using Prisma
 *   }
 *
 *   async findById(id: string): Promise<BrandKit | null> {
 *     // Implementation using Prisma
 *   }
 * }
 * ```
 */

import type { BrandKit, BrandKitStatus } from '../entities/brand-kit.entity';
// Temporarily using inline type definition
// import type { PaginatedResult } from '../../../core';

/**
 * 📄 Paginated Result Type
 * @ai_context Temporary inline definition until core imports are fixed
 */
export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * 📊 Brand Kit List Options
 * @ai_context Options for filtering and paginating brand kit lists
 */
export interface BrandKitListOptions {
  organizationId?: string;
  userId?: string;
  status?: BrandKitStatus;
  tags?: string[];
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

/**
 * 🔍 Brand Kit Search Options
 * @ai_context Options for searching brand kits
 */
export interface BrandKitSearchOptions {
  query: string;
  organizationId?: string;
  userId?: string;
  includeArchived?: boolean;
  limit?: number;
}

/**
 * 📊 Brand Kit Statistics
 * @ai_context Statistical data about brand kits
 */
export interface BrandKitStatistics {
  totalCount: number;
  statusCounts: Record<BrandKitStatus, number>;
  averageCompletionPercentage: number;
  recentlyUpdated: BrandKit[];
  mostUsedTags: Array<{ tag: string; count: number }>;
}

/**
 * 🗄️ Brand Kit Repository Interface
 * @ai_context Contract for all brand kit data operations
 */
export interface IBrandKitRepository {
  /**
   * 💾 Save a brand kit (create or update)
   * @param brandKit Brand kit to save
   * @returns Promise that resolves when saved
   */
  save(brandKit: BrandKit): Promise<void>;

  /**
   * 🔍 Find brand kit by ID
   * @param id Brand kit ID
   * @returns Brand kit or null if not found
   */
  findById(id: string): Promise<BrandKit | null>;

  /**
   * 🔍 Find brand kit by name and organization
   * @param name Brand kit name
   * @param organizationId Organization ID
   * @returns Brand kit or null if not found
   */
  findByNameAndOrganization(name: string, organizationId: string): Promise<BrandKit | null>;

  /**
   * 📋 Find brand kits by organization
   * @param organizationId Organization ID
   * @returns Array of brand kits
   */
  findByOrganization(organizationId: string): Promise<BrandKit[]>;

  /**
   * 📋 Find brand kits by user
   * @param userId User ID
   * @returns Array of brand kits
   */
  findByUser(userId: number): Promise<BrandKit[]>;

  /**
   * 📋 Find brand kits with filtering and pagination
   * @param options List options
   * @returns Paginated brand kits
   */
  findMany(options?: BrandKitListOptions): Promise<PaginatedResult<BrandKit>>;

  /**
   * 🔍 Search brand kits
   * @param query Search query
   * @param organizationId Organization ID
   * @param options Additional search options
   * @returns Matching brand kits
   */
  search(query: string, organizationId: string, options?: Partial<BrandKitSearchOptions>): Promise<BrandKit[]>;

  /**
   * 🗑️ Delete a brand kit (soft delete)
   * @param id Brand kit ID
   * @returns Promise that resolves when deleted
   */
  delete(id: string): Promise<void>;

  /**
   * 🗑️ Permanently delete a brand kit
   * @param id Brand kit ID
   * @returns Promise that resolves when permanently deleted
   */
  permanentlyDelete(id: string): Promise<void>;

  /**
   * 🔄 Restore a soft-deleted brand kit
   * @param id Brand kit ID
   * @returns Promise that resolves when restored
   */
  restore(id: string): Promise<void>;

  /**
   * ✅ Check if brand kit exists
   * @param id Brand kit ID
   * @returns True if exists, false otherwise
   */
  exists(id: string): Promise<boolean>;

  /**
   * 🔢 Count brand kits
   * @param options Filter options
   * @returns Count of matching brand kits
   */
  count(options?: Partial<BrandKitListOptions>): Promise<number>;

  /**
   * 📊 Get brand kit statistics
   * @param organizationId Organization ID
   * @returns Statistical data
   */
  getStatistics(organizationId: string): Promise<BrandKitStatistics>;

  /**
   * 🏷️ Get all unique tags
   * @param organizationId Organization ID
   * @returns Array of unique tags
   */
  getAllTags(organizationId: string): Promise<string[]>;

  /**
   * 🏷️ Get popular tags
   * @param organizationId Organization ID
   * @param limit Maximum number of tags to return
   * @returns Array of popular tags with counts
   */
  getPopularTags(organizationId: string, limit?: number): Promise<Array<{ tag: string; count: number }>>;

  /**
   * 📅 Find brand kits updated since date
   * @param since Date to check from
   * @param organizationId Organization ID
   * @returns Brand kits updated since the date
   */
  findUpdatedSince(since: Date, organizationId: string): Promise<BrandKit[]>;

  /**
   * 📅 Find brand kits created in date range
   * @param startDate Start date
   * @param endDate End date
   * @param organizationId Organization ID
   * @returns Brand kits created in the range
   */
  findCreatedInRange(startDate: Date, endDate: Date, organizationId: string): Promise<BrandKit[]>;

  /**
   * 🔄 Update brand kit status
   * @param id Brand kit ID
   * @param status New status
   * @returns Promise that resolves when updated
   */
  updateStatus(id: string, status: BrandKitStatus): Promise<void>;

  /**
   * 🏷️ Add tags to brand kit
   * @param id Brand kit ID
   * @param tags Tags to add
   * @returns Promise that resolves when updated
   */
  addTags(id: string, tags: string[]): Promise<void>;

  /**
   * 🏷️ Remove tags from brand kit
   * @param id Brand kit ID
   * @param tags Tags to remove
   * @returns Promise that resolves when updated
   */
  removeTags(id: string, tags: string[]): Promise<void>;

  /**
   * 📋 Find brand kits by tags
   * @param tags Tags to search for
   * @param organizationId Organization ID
   * @param matchAll Whether to match all tags or any tag
   * @returns Brand kits with matching tags
   */
  findByTags(tags: string[], organizationId: string, matchAll?: boolean): Promise<BrandKit[]>;

  /**
   * 🔄 Bulk update brand kits
   * @param ids Brand kit IDs
   * @param updates Updates to apply
   * @returns Promise that resolves when updated
   */
  bulkUpdate(ids: string[], updates: Partial<BrandKit>): Promise<void>;

  /**
   * 📊 Get completion statistics
   * @param organizationId Organization ID
   * @returns Completion statistics
   */
  getCompletionStatistics(organizationId: string): Promise<{
    totalBrandKits: number;
    completeBrandKits: number;
    averageCompletion: number;
    completionBySection: Record<string, number>;
  }>;
}
