/**
 * 🎙️ Voice Agent Room Hook - MODULAR VERSION
 * @description Hook for managing voice agent room names in modular canvas
 * @responsibility Provide stable room names for voice agent functionality
 * @ai_context Migrated from client/hooks/useVoiceAgentRoom.ts to modular structure
 */

import { useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';

/**
 * 🎙️ Voice Agent Room Hook
 * @description Returns a stable room name (existing one from localStorage or a new UUID-based one)
 * @returns Stable room name for voice agent functionality
 */
export function useVoiceAgentRoom(): string {
  // `useRef` ensures the value never changes for this React lifetime.
  const roomRef = useRef<string>();

  if (!roomRef.current) {
    // Only access localStorage in browser environment
    if (typeof window !== 'undefined') {
      // 1. Try to reuse any previously stored room.
      const stored = localStorage.getItem('currentVoiceAgentRoom');

      // 2. If none, mint a fresh one.
      roomRef.current = stored ?? `voice-agent-${uuidv4()}`;

      // 3. Persist it (does nothing if it was already there).
      localStorage.setItem('currentVoiceAgentRoom', roomRef.current);

      // Removed spammy room name log
    } else {
      // Fallback for SSR
      roomRef.current = `voice-agent-ssr-placeholder`;
    }
  }

  return roomRef.current;
}
