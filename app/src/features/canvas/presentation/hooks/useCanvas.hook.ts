/**
 * 🎨 Canvas Hook
 *
 * @description React hook for canvas operations and state management
 * @responsibility Connects presentation layer to domain services
 * @dependencies Canvas service, WASP operations, canvas store
 * @ai_context This hook provides a clean interface between UI and business logic
 *
 * @example
 * ```typescript
 * function CanvasComponent({ canvasId }: { canvasId: string }) {
 *   const {
 *     canvas,
 *     loading,
 *     error,
 *     addImage,
 *     selectTool,
 *     selectElements
 *   } = useCanvas(canvasId);
 *
 *   return <div>Canvas content...</div>;
 * }
 * ```
 */

import { useState, useEffect, useCallback } from 'react';
import { useQuery, useAction } from 'wasp/client/operations';
import {
  Canvas,
  CanvasState,
  CanvasToolType,
  ImageElement,
  TextElement,
  HtmlElement,
} from '../../domain/entities/canvas.entity';
import {
  createCanvas as createCanvasAction,
  updateCanvas as updateCanvasAction,
  getCanvas as getCanvasQuery,
  getCanvases as getCanvasesQuery,
} from 'wasp/client/operations';

/**
 * 🎨 Canvas Hook Return Type
 * @ai_context Complete interface for canvas operations
 */
export interface UseCanvasReturn {
  // State
  canvas: CanvasState | null;
  loading: boolean;
  error: string | null;

  // Canvas properties
  activeTool: CanvasToolType;
  selectedIds: string[];
  scale: number;
  position: { x: number; y: number };
  selection: {
    visible: boolean;
    x: number;
    y: number;
    width: number;
    height: number;
  };
  canvasSize: { width: number; height: number };

  // Canvas operations
  createCanvas: (
    name: string,
    options?: {
      canvasSize?: { width: number; height: number };
      isCollaborative?: boolean;
    }
  ) => Promise<void>;
  updateCanvas: (updates: {
    name?: string;
    canvasSize?: { width: number; height: number };
    isCollaborative?: boolean;
  }) => Promise<void>;
  saveCanvasState: () => Promise<void>;

  // Tool operations
  selectTool: (tool: CanvasToolType) => void;

  // Element operations
  selectElements: (elementIds: string[]) => void;
  addImage: (imageData: {
    src: string;
    x: number;
    y: number;
    width: number;
    height: number;
    rotation?: number;
    taskId?: string;
  }) => Promise<void>;
  addText: (textData: {
    text: string;
    x: number;
    y: number;
    width: number;
    height: number;
    fontSize: number;
    fontFamily: string;
    color: string;
    rotation?: number;
  }) => Promise<void>;
  addHtmlElement: (htmlData: {
    html: string;
    x: number;
    y: number;
    width: number;
    height: number;
    css?: string;
    backgroundColor?: string;
    rotation?: number;
  }) => Promise<void>;

  // Transform operations
  setCanvasZoom: (scale: number) => void;
  setCanvasPosition: (position: { x: number; y: number }) => void;

  // Utility operations
  refreshCanvas: () => Promise<void>;
  resetCanvas: () => void;
}

/**
 * 🎨 Canvas Hook Implementation
 * @ai_context Main hook for canvas functionality
 */
export function useCanvas(canvasId?: string): UseCanvasReturn {
  // Local state for canvas operations
  const [localCanvas, setLocalCanvas] = useState<CanvasState | null>(null);
  const [activeTool, setActiveTool] = useState<CanvasToolType>(CanvasToolType.SELECT);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [selection, setSelection] = useState({
    visible: false,
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  });

  // WASP operations
  const createCanvasOp = useAction(createCanvasAction);
  const updateCanvasOp = useAction(updateCanvasAction);

  // Query for existing canvas
  const {
    data: canvasData,
    isLoading: queryLoading,
    error: queryError,
    refetch: refetchCanvas,
  } = useQuery(getCanvasQuery, canvasId ? { id: canvasId } : undefined, {
    enabled: !!canvasId,
  });

  // 🚨 IMPROVED: Conditional useEffect to avoid unnecessary syncing
  // ✅ Only sync when canvasData changes AND localCanvas is different
  // This avoids the anti-pattern of syncing on every render
  useEffect(() => {
    if (canvasData && (!localCanvas || localCanvas.id !== canvasData.id)) {
      // Convert database canvas to our domain model
      const canvasState: CanvasState = {
        id: canvasData.id,
        name: canvasData.name,
        userId: canvasData.userId,
        organizationId: canvasData.organizationId || '',
        status: 'active' as any,
        images: [],
        texts: [],
        htmlElements: [],
        canvasSize: { width: 1920, height: 1080 },
        activeTool: CanvasToolType.SELECT,
        selectedIds: [],
        scale: 1,
        position: { x: 0, y: 0 },
        selection: { visible: false, x: 0, y: 0, width: 0, height: 0 },
        isCollaborative: false,
        collaborators: [],
        createdAt: canvasData.createdAt,
        updatedAt: canvasData.updatedAt,
        deletedAt: null,
      };

      setLocalCanvas(canvasState);
    }
  }, [canvasData, localCanvas]);

  /**
   * 🏗️ Create new canvas
   */
  const createCanvas = useCallback(
    async (
      name: string,
      options?: {
        canvasSize?: { width: number; height: number };
        isCollaborative?: boolean;
      }
    ) => {
      try {
        const result = await createCanvasOp({
          name,
          description: '',
          organizationId: undefined, // Will use user's default organization
        });

        if (result) {
          // Convert to our domain model
          const canvasState: CanvasState = {
            id: result.id,
            name: result.name,
            userId: result.userId,
            organizationId: result.organizationId || '',
            status: 'active' as any,
            images: [],
            texts: [],
            htmlElements: [],
            canvasSize: options?.canvasSize || { width: 1920, height: 1080 },
            activeTool: CanvasToolType.SELECT,
            selectedIds: [],
            scale: 1,
            position: { x: 0, y: 0 },
            selection: { visible: false, x: 0, y: 0, width: 0, height: 0 },
            isCollaborative: options?.isCollaborative || false,
            collaborators: [],
            createdAt: result.createdAt,
            updatedAt: result.updatedAt,
            deletedAt: null,
          };

          setLocalCanvas(canvasState);
        }
      } catch (error) {
        console.error('Failed to create canvas:', error);
        throw error;
      }
    },
    [createCanvasOp]
  );

  /**
   * 🔄 Update canvas properties
   */
  const updateCanvas = useCallback(
    async (updates: { name?: string; canvasSize?: { width: number; height: number }; isCollaborative?: boolean }) => {
      if (!canvasId) {
        throw new Error('Canvas ID is required for updates');
      }

      try {
        const result = await updateCanvasOp({
          id: canvasId,
          name: updates.name,
          description: undefined,
          data: localCanvas?.canvasSize ? { canvasSize: updates.canvasSize || localCanvas.canvasSize } : undefined,
        });

        if (result) {
          // Update local canvas state
          if (localCanvas) {
            const updatedCanvas: CanvasState = {
              ...localCanvas,
              name: result.name,
              updatedAt: result.updatedAt,
            };
            setLocalCanvas(updatedCanvas);
          }
        }
      } catch (error) {
        console.error('Failed to update canvas:', error);
        throw error;
      }
    },
    [canvasId, updateCanvasOp, localCanvas]
  );

  /**
   * 💾 Save current canvas state
   */
  const saveCanvasState = useCallback(async () => {
    if (!localCanvas) {
      throw new Error('No canvas to save');
    }

    try {
      const result = await updateCanvasOp({
        id: localCanvas.id,
        data: {
          activeTool,
          selectedIds,
          scale,
          position,
          canvasSize: localCanvas.canvasSize,
        },
      });

      if (result) {
        const updatedCanvas: CanvasState = {
          ...localCanvas,
          updatedAt: result.updatedAt,
        };
        setLocalCanvas(updatedCanvas);
      }
    } catch (error) {
      console.error('Failed to save canvas state:', error);
      throw error;
    }
  }, [localCanvas, activeTool, selectedIds, scale, position, updateCanvasOp]);

  /**
   * 🎯 Select tool
   */
  const selectTool = useCallback((tool: CanvasToolType) => {
    setActiveTool(tool);
  }, []);

  /**
   * 🎯 Select elements
   */
  const selectElements = useCallback((elementIds: string[]) => {
    setSelectedIds(elementIds);
  }, []);

  /**
   * 🖼️ Add image to canvas
   */
  const addImage = useCallback(
    async (imageData: {
      src: string;
      x: number;
      y: number;
      width: number;
      height: number;
      rotation?: number;
      taskId?: string;
    }) => {
      if (!localCanvas) {
        throw new Error('Canvas is required to add image');
      }

      // For now, just update local state
      // TODO: Implement proper canvas element persistence
      const newImage: ImageElement = {
        id: `img_${Date.now()}`,
        type: 'image',
        src: imageData.src,
        x: imageData.x,
        y: imageData.y,
        width: imageData.width,
        height: imageData.height,
        rotation: imageData.rotation || 0,
        taskId: imageData.taskId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const updatedCanvas: CanvasState = {
        ...localCanvas,
        images: [...localCanvas.images, newImage],
        updatedAt: new Date(),
      };

      setLocalCanvas(updatedCanvas);
    },
    [localCanvas]
  );

  /**
   * 📝 Add text to canvas
   */
  const addText = useCallback(
    async (textData: {
      text: string;
      x: number;
      y: number;
      width: number;
      height: number;
      fontSize: number;
      fontFamily: string;
      color: string;
      rotation?: number;
    }) => {
      if (!localCanvas) {
        throw new Error('Canvas is required to add text');
      }

      // For now, just update local state
      const newText: TextElement = {
        id: `txt_${Date.now()}`,
        type: 'text',
        text: textData.text,
        x: textData.x,
        y: textData.y,
        width: textData.width,
        height: textData.height,
        fontSize: textData.fontSize,
        fontFamily: textData.fontFamily,
        color: textData.color,
        rotation: textData.rotation || 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const updatedCanvas: CanvasState = {
        ...localCanvas,
        texts: [...localCanvas.texts, newText],
        updatedAt: new Date(),
      };

      setLocalCanvas(updatedCanvas);
    },
    [localCanvas]
  );

  /**
   * 🌐 Add HTML element to canvas
   */
  const addHtmlElement = useCallback(
    async (htmlData: {
      html: string;
      x: number;
      y: number;
      width: number;
      height: number;
      css?: string;
      backgroundColor?: string;
      rotation?: number;
    }) => {
      if (!localCanvas) {
        throw new Error('Canvas is required to add HTML element');
      }

      // For now, just update local state
      const newHtml: HtmlElement = {
        id: `html_${Date.now()}`,
        type: 'html',
        html: htmlData.html,
        x: htmlData.x,
        y: htmlData.y,
        width: htmlData.width,
        height: htmlData.height,
        css: htmlData.css,
        backgroundColor: htmlData.backgroundColor,
        rotation: htmlData.rotation || 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const updatedCanvas: CanvasState = {
        ...localCanvas,
        htmlElements: [...localCanvas.htmlElements, newHtml],
        updatedAt: new Date(),
      };

      setLocalCanvas(updatedCanvas);
    },
    [localCanvas]
  );

  /**
   * 🔍 Set canvas zoom
   */
  const setCanvasZoom = useCallback((newScale: number) => {
    const clampedScale = Math.max(0.1, Math.min(10, newScale));
    setScale(clampedScale);
  }, []);

  /**
   * 📍 Set canvas position
   */
  const setCanvasPosition = useCallback((newPosition: { x: number; y: number }) => {
    setPosition(newPosition);
  }, []);

  /**
   * 🔄 Refresh canvas from server
   */
  const refreshCanvas = useCallback(async () => {
    if (canvasId) {
      await refetchCanvas();
    }
  }, [canvasId, refetchCanvas]);

  /**
   * 🔄 Reset canvas to initial state
   */
  const resetCanvas = useCallback(() => {
    setLocalCanvas(null);
    setActiveTool(CanvasToolType.SELECT);
    setSelectedIds([]);
    setScale(1);
    setPosition({ x: 0, y: 0 });
    setSelection({ visible: false, x: 0, y: 0, width: 0, height: 0 });
  }, []);

  // Determine canvas size
  const canvasSize = localCanvas?.canvasSize || { width: 1920, height: 1080 };

  return {
    // State
    canvas: localCanvas,
    loading: queryLoading,
    error: queryError?.message || null,

    // Canvas properties
    activeTool,
    selectedIds,
    scale,
    position,
    selection,
    canvasSize,

    // Canvas operations
    createCanvas,
    updateCanvas,
    saveCanvasState,

    // Tool operations
    selectTool,

    // Element operations
    selectElements,
    addImage,
    addText,
    addHtmlElement,

    // Transform operations
    setCanvasZoom,
    setCanvasPosition,

    // Utility operations
    refreshCanvas,
    resetCanvas,
  };
}
