import { useEffect } from 'react';
import { useElementDomainStore, DomainImageElement, ElementDomainState } from '../../domain/state';
import { useShallow } from 'zustand/react/shallow';

export const useCanvasEventListeners = () => {
  const { images, addImage, updateImage } = useElementDomainStore(useShallow((state: ElementDomainState) => ({
    images: state.images,
    addImage: state.addImage,
    updateImage: state.updateImage,
  })));

  // 🔌 Canvas element add event handler
  useEffect(() => {
    const handleCanvasElementAdd = (event: CustomEvent) => {
      const data = event.detail;
      try {
        const content = data.content || data;
        const imageUrl = content.imageUrls?.[0] || content.imageUrl || content.src;
        const taskId = content.taskId || data.taskId;

        if (imageUrl && taskId) {
          const existingImage = images.find((img: DomainImageElement) => img.taskId === taskId || img.id === `generated-${taskId}`);
          if (existingImage) {
            updateImage(existingImage.id, { url: imageUrl, opacity: 1.0 });
          } else {
            const newImage: DomainImageElement = {
              id: `generated-${taskId}`,
              url: imageUrl,
              x: content.x || 200,
              y: content.y || 200,
              width: content.width || 300,
              height: content.height || 300,
              rotation: 0,
              taskId: taskId,
            };
            addImage(newImage);
          }
        }
      } catch (error) {
        console.error('[Canvas] Error processing canvas_element_add:', error);
      }
    };
    window.addEventListener('canvas_element_add', handleCanvasElementAdd as EventListener);
    return () => window.removeEventListener('canvas_element_add', handleCanvasElementAdd as EventListener);
  }, [images, addImage, updateImage]);

  // 🔌 Canvas element update event handler
  useEffect(() => {
    const handleCanvasElementUpdate = (event: CustomEvent) => {
      const data = event.detail;
      try {
        if (data.content && data.content.imageUrls && data.content.imageUrls.length > 0) {
          const imageUrl = data.content.imageUrls[0];
          const taskId = data.content.taskId || data.taskId;
          const elementId = data.elementId || data.id;
          const imageToUpdate = images.find((img: DomainImageElement) => img.taskId === taskId || img.id === elementId || img.id === `generated-${taskId}`);
          if (imageToUpdate) {
            updateImage(imageToUpdate.id, { url: imageUrl });
          } else {
            const newImage: DomainImageElement = {
              id: `generated-${taskId}`,
              url: imageUrl,
              x: data.content.x || 200,
              y: data.content.y || 200,
              width: data.content.width || 300,
              height: data.content.height || 300,
              rotation: 0,
              taskId: taskId,
            };
            addImage(newImage);
          }
        }
      } catch (error) {
        console.error('[Canvas] Error processing canvas_element_update:', error);
      }
    };
    window.addEventListener('canvas_element_update', handleCanvasElementUpdate as EventListener);
    return () => window.removeEventListener('canvas_element_update', handleCanvasElementUpdate as EventListener);
  }, [images, addImage, updateImage]);

  // 🔌 Task update event handler for streaming partial images
  useEffect(() => {
    const handleTaskUpdate = (event: CustomEvent) => {
      const { taskId, result } = event.detail;
      try {
        if (result && (result.type === 'initial_partial_image' || result.type === 'update_partial_image') && result.imageBase64) {
          const imageDataUrl = `data:image/jpeg;base64,${result.imageBase64}`;
          const imageToUpdate = images.find((img: DomainImageElement) => img.taskId === taskId || img.id === `generated-${taskId}`);
          if (imageToUpdate) {
            updateImage(imageToUpdate.id, { url: imageDataUrl, opacity: 0.8 });
          } else {
            const newImage: DomainImageElement = {
              id: `generated-${taskId}`, url: imageDataUrl, x: 200, y: 200, width: 300, height: 300, rotation: 0, taskId: taskId, opacity: 0.8,
            };
            addImage(newImage);
          }
        } else if (result && result.type === 'final_image' && result.imageUrl) {
          const imageToUpdate = images.find((img: DomainImageElement) => img.taskId === taskId || img.id === `generated-${taskId}`);
          if (imageToUpdate) {
            updateImage(imageToUpdate.id, { url: result.imageUrl, opacity: 1.0 });
          }
        }
      } catch (error) {
        console.error('[Canvas] Error processing task_update:', error);
      }
    };
    window.addEventListener('task_update', handleTaskUpdate as EventListener);
    return () => window.removeEventListener('task_update', handleTaskUpdate as EventListener);
  }, [images, addImage, updateImage]);
}; 