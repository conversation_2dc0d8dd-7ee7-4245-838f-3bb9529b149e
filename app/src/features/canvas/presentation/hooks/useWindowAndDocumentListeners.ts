import { useEffect, useState, useCallback } from "react";
import { ToolService } from "../../domain/services";
import { DomainCanvasTool } from "../../domain/state";

export const useWindowAndDocumentListeners = (
    stageRef: React.RefObject<any>,
    chatBubbleVisible: boolean,
    handleCloseChatBubble: () => void,
) => {
    const [previousTool, setPreviousTool] = useState<DomainCanvasTool>(DomainCanvasTool.SELECT);
    const [isMiddleMousePanning, setIsMiddleMousePanning] = useState(false);

    // Handle window resize
    useEffect(() => {
        const handleResize = () => {
            // This logic might need to be passed back up to the Canvas component
            // if it needs to update the stage size directly.
            // For now, we just log it.
            console.log("Window resized");
        };
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    // Prevent browser zoom/scroll conflicts
    useEffect(() => {
        const preventPageZoom = (e: WheelEvent) => {
            if (e.ctrlKey) e.preventDefault();
        };
        const preventSafariZoom = (e: Event) => e.preventDefault();
        
        document.addEventListener('wheel', preventPageZoom, { passive: false, capture: true });
        document.addEventListener('gesturestart', preventSafariZoom, { passive: false });
        document.addEventListener('gesturechange', preventSafariZoom, { passive: false });

        return () => {
            document.removeEventListener('wheel', preventPageZoom, { capture: true });
            document.removeEventListener('gesturestart', preventSafariZoom);
            document.removeEventListener('gesturechange', preventSafariZoom);
        };
    }, []);

    // Middle mouse button panning
    useEffect(() => {
        let panData: { active: boolean; initialMousePosition: { x: number; y: number }; initialStagePosition: { x: number; y: number }; } | null = null;

        const handleMiddleMouseDown = (e: MouseEvent) => {
            if (e.button === 1 && stageRef.current) {
                e.preventDefault();
                const currentTool = ToolService.tools.getActive();
                setPreviousTool(currentTool);
                ToolService.tools.setActive(DomainCanvasTool.HAND);
                setIsMiddleMousePanning(true);
                document.body.style.cursor = 'grabbing';
                panData = { active: true, initialMousePosition: { x: e.clientX, y: e.clientY }, initialStagePosition: { ...ToolService.canvas.getPosition() } };
            }
        };

        const handleDocumentMouseMove = (e: MouseEvent) => {
            if (panData?.active && stageRef.current) {
                e.preventDefault();
                const { initialMousePosition, initialStagePosition } = panData;
                const dx = e.clientX - initialMousePosition.x;
                const dy = e.clientY - initialMousePosition.y;
                const newPosition = { x: initialStagePosition.x + dx, y: initialStagePosition.y + dy };
                ToolService.canvas.setPosition(newPosition);
                stageRef.current.position(newPosition);
                stageRef.current.batchDraw();
            }
        };

        const handleMiddleMouseUp = (e: MouseEvent) => {
            if (e.button === 1 && panData?.active) {
                e.preventDefault();
                document.body.style.cursor = '';
                ToolService.tools.setActive(previousTool);
                setIsMiddleMousePanning(false);
                panData = null;
            }
        };

        document.addEventListener('mousedown', handleMiddleMouseDown);
        document.addEventListener('mousemove', handleDocumentMouseMove);
        document.addEventListener('mouseup', handleMiddleMouseUp);

        return () => {
            document.removeEventListener('mousedown', handleMiddleMouseDown);
            document.removeEventListener('mousemove', handleDocumentMouseMove);
            document.removeEventListener('mouseup', handleMiddleMouseUp);
            document.body.style.cursor = '';
        };
    }, [previousTool, stageRef]);

    // Document click handler to close chat bubble
    useEffect(() => {
        const handleDocumentClick = (e: MouseEvent) => {
            if (chatBubbleVisible) {
                const bubbleElement = document.querySelector('.chat-bubble-container');
                const canvasElement = document.querySelector('.konvajs-content');
                const contextMenu = document.querySelector('.context-menu-container');

                const clickedOutside = 
                    (!bubbleElement || !bubbleElement.contains(e.target as Node)) &&
                    (!canvasElement || !canvasElement.contains(e.target as Node)) &&
                    (!contextMenu || !contextMenu.contains(e.target as Node));

                if (clickedOutside) {
                    handleCloseChatBubble();
                }
            }
        };
        document.addEventListener('click', handleDocumentClick);
        return () => document.removeEventListener('click', handleDocumentClick);
    }, [chatBubbleVisible, handleCloseChatBubble]);

    return { isMiddleMousePanning, setIsMiddleMousePanning };
}; 