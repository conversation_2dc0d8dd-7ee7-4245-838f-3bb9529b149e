/**
 * 🔌 Canvas Hooks - MODULAR EXPORTS
 * @description Exports for modular canvas hooks
 * @ai_context Modular canvas hook exports
 */

export { useAuthenticatedSocket } from './useAuthenticatedSocket';
export { useReferenceImages } from './useReferenceImages';
export { useVoiceAgentRoom } from './useVoiceAgentRoom';
export { usePendingTasks } from './usePendingTasks.hook';

// TODO: Add more modular hooks as we build them
// export { useCanvasWebSocket } from './useCanvasWebSocket';
// export { useCanvasEvents } from './useCanvasEvents';
// export { useCanvasKeyboard } from './useCanvasKeyboard';
