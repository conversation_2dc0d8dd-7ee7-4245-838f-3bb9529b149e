import { useCallback, useState, useMemo, useRef } from 'react';
import useWebSocket, { ReadyState } from 'react-use-websocket';
import { WebSocketMessage } from 'react-use-websocket/dist/lib/types';
import { useAuth } from 'wasp/client/auth';
import { DomainTextElement } from '../../domain/state';
import { ImageProperties } from '../components/elements/images/URLImage';

const WS_BASE_URL = 'https://canvas-workers.j-5bb.workers.dev';

interface UseCanvasSyncProps {
  roomId?: string;
  sharedCanvasToken?: string; // For shared canvas access
  guestUserInfo?: {
    id: string;
    name: string;
    email?: string;
  };
}

export type CollaborationUser = {
  user: {
    id: string;
    name: string;
    color: string;
  };
  isActive: boolean;
  lastSeen: number;
  cursor: Position | null;
};

export interface Position {
  x: number;
  y: number;
}

export type ElementProperties = DomainTextElement | ImageProperties;

export interface CanvasElement {
  id: string;
  type: 'image' | 'text';
  position: Position;
  properties: ElementProperties;
  zIndex: number;
  rotation?: number;
  scaleX?: number;
  scaleY?: number;
  createdBy: string;
  createdAt: number;
  updatedAt: number;
}

export interface BaseMessage {
  type: string;
  userId: string;
  timestamp: number;
  roomId: string;
}

export interface ElementCreateMessage extends BaseMessage {
  type: 'element_create';
  element: CanvasElement;
  version: number;
}

export interface ElementDeleteMessage extends BaseMessage {
  type: 'element_delete';
  elementId: string;
  version: number;
}

export interface Viewport {
  x: number;
  y: number;
  zoom: number;
}

export interface CanvasState {
  elements: Record<string, CanvasElement>;
  selectedIds: string[];
  viewport: Viewport;
  version: number;
}

export const useCanvasSync = ({
  roomId = 'testingRoom',
  sharedCanvasToken,
  guestUserInfo
}: UseCanvasSyncProps) => {
  const { data: user } = useAuth();
  const hasRequestedInitialState = useRef(false);

  // Generate stable guest user ID once and persist it in localStorage
  const stableGuestId = useRef<string | null>(null);
  const stableGuestName = useRef<string | null>(null);

  // Always create a new guest user for each session (don't persist across browser sessions)
  if (!stableGuestId.current) {
    stableGuestId.current = `shared_user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Start with a default guest name - the server will assign a proper number
    stableGuestName.current = 'Guest';

    // Store in sessionStorage instead of localStorage (clears when browser closes)
    sessionStorage.setItem('canvas_guest_user', JSON.stringify({
      id: stableGuestId.current,
      name: stableGuestName.current
    }));
  }

  // State for guest user name (can be updated)
  const [guestUserName, setGuestUserName] = useState<string>(stableGuestName.current || 'Guest (via Unknown User)');

  // Determine effective user (authenticated user or guest)
  const effectiveUser = useMemo(() => {
    if (user) {
      return {
        id: user.id.toString(),
        name: user.username || user.email || 'User',
        email: user.email
      };
    } else if (guestUserInfo) {
      return guestUserInfo;
    } else {
      // Fallback for shared canvas access without guest info - use stable ID and updatable name
      return {
        id: stableGuestId.current!,
        name: guestUserName,
        email: undefined
      };
    }
  }, [user, guestUserInfo, guestUserName]);

  // Generate user color based on stable user ID, not name (to prevent reconnections on name change)
  const userColor = useMemo(() => generateUserColor(effectiveUser.id), [effectiveUser.id]);

  const [canvasState, setCanvasState] = useState<CanvasState>({
    elements: {},
    selectedIds: [],
    viewport: { x: 0, y: 0, zoom: 1 },
    version: 0,
  });

  // Debug: console.log({ canvasState });

  const [users, setUsers] = useState<CollaborationUser[]>([]);
  const [userSelections, setUserSelections] = useState<Record<string, { visible: boolean; x: number; y: number; width: number; height: number; color: string }>>({});
  const [chatBubbles, setChatBubbles] = useState<Record<string, any>>({});
  const [collaborativeLoadingCards, setCollaborativeLoadingCards] = useState<Record<string, any>>({});
  const [collaborativeConceptCards, setCollaborativeConceptCards] = useState<Record<string, any>>({});

  const canvasStateRef = useRef(canvasState);
  canvasStateRef.current = canvasState;

  // Memoize WebSocket URL to prevent reconnections
  // Note: We include the initial name but don't update URL when name changes (to prevent reconnections)
  const initialUserName = useRef(effectiveUser?.name);
  if (!initialUserName.current) {
    initialUserName.current = effectiveUser?.name;
  }

  const wsUrl = useMemo(() => {
    if (!effectiveUser?.id) return null;
    const params = new URLSearchParams({
      userId: effectiveUser.id,
      userName: initialUserName.current || effectiveUser.name,
      userColor: userColor,
    });

    // Add shared canvas token if available
    if (sharedCanvasToken) {
      params.append('sharedToken', sharedCanvasToken);
    }

    return `${WS_BASE_URL}/ws/${roomId}?${params.toString()}`;
  }, [roomId, effectiveUser?.id, userColor, sharedCanvasToken]);

  const { sendMessage, sendJsonMessage, lastMessage, lastJsonMessage, readyState } = useWebSocket(
    wsUrl,
    {
      onOpen: () => {
        console.log('[WebSocket] Connected to canvas sync', {
          userId: effectiveUser?.id,
          userName: effectiveUser?.name,
          roomId,
          wsUrl
        });
        console.log('[WebSocket] 🏠 CLIENT CONNECTED TO ROOM:', roomId);

        // Only request initial state once per connection
        if (!hasRequestedInitialState.current && effectiveUser?.id) {
          hasRequestedInitialState.current = true;

          sendJsonMessage({
            type: 'state_request',
            userId: effectiveUser.id,
            timestamp: Date.now(),
            roomId,
          });

          console.log('[WebSocket] Requested initial state');
        }
      },
      onClose: (event) => {
        console.log('[WebSocket] Disconnected:', event.code, event.reason);
        // Reset flag so we can request state on reconnection
        hasRequestedInitialState.current = false;
      },
      onMessage: (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          handleWebSocketMessage(message);
        } catch (error) {
          console.error('[WebSocket] Error parsing message:', error);
        }
      },
      onError: (error) => {
        console.error('[WebSocket] Connection error:', error);
      },
      // Will attempt to reconnect on all close events
      shouldReconnect: () => true,
      // Add reconnection options to prevent excessive reconnection attempts
      reconnectAttempts: 10,
      reconnectInterval: 3000,
    },
    // Only enable WebSocket when we have a valid URL
    !!wsUrl
  );

  // Stabilize the message handler with minimal dependencies
  const handleWebSocketMessage = useCallback(
    (message: WebSocketMessage) => {
      // Parse message if it's a string
      const parsedMessage = typeof message === 'string' ? JSON.parse(message) : message;

      // Only log important messages to prevent spam
      if (parsedMessage.type !== 'cursor_move' && parsedMessage.type !== 'user_list') {
        console.log('[WebSocket] Message:', parsedMessage);
      }

      // Handle different message types
      switch (parsedMessage.type) {
        case 'state_response':
          console.log('[WebSocket] Received state response');
          if (parsedMessage.canvasState) {
            setCanvasState(parsedMessage.canvasState);
          }
          if (parsedMessage.users) {
            const activeUsers = Object.values(parsedMessage.users).filter(
              (u: any) => u.isActive
            ) as CollaborationUser[];
            setUsers(activeUsers);

            // Update guest name if server assigned a numbered guest name
            if (!user && effectiveUser?.id) {
              const currentUserFromServer = activeUsers.find(u => u.user.id === effectiveUser.id);
              if (currentUserFromServer && currentUserFromServer.user.name !== effectiveUser.name) {
                const serverAssignedName = currentUserFromServer.user.name;
                setGuestUserName(serverAssignedName);
                stableGuestName.current = serverAssignedName;

                // Update sessionStorage with server-assigned name
                sessionStorage.setItem('canvas_guest_user', JSON.stringify({
                  id: stableGuestId.current,
                  name: serverAssignedName
                }));
              }
            }
          }
          break;

        case 'user_join':
          console.log('[WebSocket] User joined:', parsedMessage.user?.name);
          setUsers((prev) => [
            ...prev.filter((u) => u.user.id !== parsedMessage.user.id),
            {
              user: parsedMessage.user,
              cursor: null,
              lastSeen: parsedMessage.timestamp,
              isActive: true,
            },
          ]);
          break;

        case 'user_leave':
          console.log('[WebSocket] User left:', parsedMessage.userId);
          setUsers((prev) => prev.filter((u) => u.user.id !== parsedMessage.userId));
          // Also clear any stale cursor data for this user
          setUsers((prev) => prev.map((u) =>
            u.user.id === parsedMessage.userId
              ? { ...u, cursor: null, isActive: false }
              : u
          ));
          // Clear user's selection
          setUserSelections((prev) => {
            const newSelections = { ...prev };
            delete newSelections[parsedMessage.userId];
            return newSelections;
          });
          break;

        case 'cursor_move':
          // Don't log cursor moves to prevent spam
          setUsers((prev) =>
            prev.map((u) =>
              u.user.id === parsedMessage.userId
                ? { ...u, cursor: parsedMessage.position, lastSeen: parsedMessage.timestamp }
                : u
            )
          );
          break;

        case 'element_create':
          setCanvasState((prev) => ({
            ...prev,
            elements: {
              ...prev.elements,
              [parsedMessage.element.id]: parsedMessage.element,
            },
            version: parsedMessage.version,
          }));
          break;

        case 'element_update':
          // Removed spammy element update log
          setCanvasState((prev) => {
            const element = prev.elements[parsedMessage.elementId];
            if (!element) return prev;

            return {
              ...prev,
              elements: {
                ...prev.elements,
                [parsedMessage.elementId]: {
                  ...element,
                  ...parsedMessage.changes,
                  updatedAt: Date.now(),
                },
              },
              version: parsedMessage.version,
            };
          });
          break;

        case 'element_delete':
          setCanvasState((prev) => {
            const newElements = { ...prev.elements };
            delete newElements[parsedMessage.elementId];

            return {
              ...prev,
              elements: newElements,
              selectedIds: prev.selectedIds.filter((id) => id !== parsedMessage.elementId),
              version: parsedMessage.version,
            };
          });
          break;

        case 'element_drag_update':
          // Handle real-time drag updates without version conflicts
          setCanvasState((prev) => {
            const element = prev.elements[parsedMessage.elementId];
            if (!element) return prev;

            return {
              ...prev,
              elements: {
                ...prev.elements,
                [parsedMessage.elementId]: {
                  ...element,
                  ...parsedMessage.changes,
                  // Don't update updatedAt for drag updates to avoid conflicts
                },
              },
              // Don't update version for drag updates
            };
          });
          break;

        case 'selection_update':
          console.log('[WebSocket] Selecting update:', parsedMessage);
          if (parsedMessage.userId !== effectiveUser?.id) {
            setCanvasState((prev) => ({
              ...prev,
              selectedIds: parsedMessage.selectedIds,
            }));
          }
          break;

        case 'user_name_update':
          console.log('[WebSocket] User name updated:', parsedMessage);
          setUsers((prev) =>
            prev.map((u) =>
              u.user.id === parsedMessage.userId
                ? { ...u, user: { ...u.user, name: parsedMessage.newName } }
                : u
            )
          );
          break;

        case 'drag_selection_update':
          // Removed spammy drag selection log
          // Find the user's color
          const userColor = users.find(u => u.user.id === parsedMessage.userId)?.user.color || '#9EA581';
          setUserSelections((prev) => ({
            ...prev,
            [parsedMessage.userId]: {
              ...parsedMessage.selection,
              color: userColor
            }
          }));
          break;

        case 'chat_bubble_create':
          console.log('[WebSocket] Chat bubble created:', parsedMessage);
          const createUserColor = users.find(u => u.user.id === parsedMessage.userId)?.user.color || '#9EA581';
          const createUserName = users.find(u => u.user.id === parsedMessage.userId)?.user.name || 'Unknown User';
          setChatBubbles((prev) => ({
            ...prev,
            [parsedMessage.bubbleId]: {
              id: parsedMessage.bubbleId,
              position: parsedMessage.position,
              userId: parsedMessage.userId,
              userName: createUserName,
              userColor: createUserColor,
              message: parsedMessage.message,
              isVisible: true,
              createdAt: parsedMessage.timestamp,
            }
          }));
          break;

        case 'chat_bubble_update':
          console.log('[WebSocket] Chat bubble updated:', parsedMessage);
          setChatBubbles((prev) => ({
            ...prev,
            [parsedMessage.bubbleId]: {
              ...prev[parsedMessage.bubbleId],
              message: parsedMessage.message,
            }
          }));
          break;

        case 'chat_bubble_close':
          console.log('[WebSocket] Chat bubble closed:', parsedMessage);
          setChatBubbles((prev) => {
            const newBubbles = { ...prev };
            delete newBubbles[parsedMessage.bubbleId];
            return newBubbles;
          });
          break;

        case 'loading_card_create':
          console.log('[WebSocket] Loading card created:', parsedMessage);

          // Use user info from message (more reliable than lookup)
          const loadingUserName = parsedMessage.userName || 'Unknown User';
          const loadingUserColor = parsedMessage.userColor || '#9EA581';

          console.log('[WebSocket] Loading card user info from message:', {
            userId: parsedMessage.userId,
            userName: loadingUserName,
            userColor: loadingUserColor
          });

          setCollaborativeLoadingCards((prev) => {
            const newCards = {
              ...prev,
              [parsedMessage.cardId]: {
                id: parsedMessage.cardId,
                position: parsedMessage.position,
                userId: parsedMessage.userId,
                userName: loadingUserName,
                userColor: loadingUserColor,
                requestText: parsedMessage.requestText,
                createdAt: parsedMessage.timestamp,
              }
            };
            console.log('[WebSocket] Updated collaborative loading cards:', newCards);
            return newCards;
          });
          break;

        case 'loading_card_remove':
          console.log('[WebSocket] Loading card removed:', parsedMessage);
          setCollaborativeLoadingCards((prev) => {
            const newCards = { ...prev };
            delete newCards[parsedMessage.cardId];
            return newCards;
          });

          // Also dispatch event to remove local loading card if it exists
          // This handles the case where a guest user's loading card exists in both local and collaborative state
          window.dispatchEvent(
            new CustomEvent('removeLoadingCard', {
              detail: { id: parsedMessage.cardId },
            })
          );
          break;

        case 'loading_card_update':
          console.log('[WebSocket] Loading card updated:', parsedMessage);

          // Check if this is a position update for a local loading card
          if (parsedMessage.updates.position) {
            // Dispatch event to update local loading card position
            window.dispatchEvent(
              new CustomEvent('updateLoadingCardPosition', {
                detail: {
                  id: parsedMessage.cardId,
                  position: parsedMessage.updates.position,
                },
              })
            );
          }

          // Also update collaborative loading cards
          setCollaborativeLoadingCards((prev) => {
            const existingCard = prev[parsedMessage.cardId];
            if (existingCard) {
              const updatedCard = {
                ...existingCard,
                ...parsedMessage.updates,
              };
              console.log('[WebSocket] Updated collaborative loading card:', updatedCard);
              return {
                ...prev,
                [parsedMessage.cardId]: updatedCard,
              };
            }
            console.log('[WebSocket] Loading card not found in collaborative state, skipping update');
            return prev;
          });
          break;

        case 'concept_card_create':
          console.log('[WebSocket] Concept card created:', parsedMessage);

          // Use user info from message (more reliable than lookup)
          const conceptUserName = parsedMessage.userName || 'Unknown User';
          const conceptUserColor = parsedMessage.userColor || '#9EA581';

          // Check if this concept card is from the current user
          // Convert both to strings for comparison since server sends string IDs
          const isCurrentUser = String(parsedMessage.userId) === String(effectiveUser?.id);

          if (isCurrentUser) {
            // For current user's concept cards, dispatch event to local KonvaConceptCards
            console.log('[WebSocket] Concept card is from current user, dispatching to local cards');
            window.dispatchEvent(
              new CustomEvent('createConceptCardLocal', {
                detail: {
                  cardData: parsedMessage.cardData,
                  cardId: parsedMessage.cardId,
                },
              })
            );

            // For current user, only remove collaborative loading cards (local loading card is handled by KonvaConceptCards)
            if (parsedMessage.cardData && parsedMessage.cardData.loadingCardId) {
              console.log('[WebSocket] Removing collaborative loading card for current user:', parsedMessage.cardData.loadingCardId);
              setCollaborativeLoadingCards((prev) => {
                const newCards = { ...prev };
                delete newCards[parsedMessage.cardData.loadingCardId];
                return newCards;
              });
            }
          } else {
            // For other users' concept cards, add to collaborative state
            console.log('[WebSocket] Concept card is from other user, adding to collaborative state');
            setCollaborativeConceptCards((prev) => ({
              ...prev,
              [parsedMessage.cardId]: {
                ...parsedMessage.cardData,
                userId: parsedMessage.userId,
                userName: conceptUserName,
                userColor: conceptUserColor,
                createdAt: parsedMessage.timestamp,
              }
            }));

            // For other users, remove their collaborative loading cards
            if (parsedMessage.cardData && parsedMessage.cardData.loadingCardId) {
              console.log('[WebSocket] Removing collaborative loading card for other user:', parsedMessage.cardData.loadingCardId);
              setCollaborativeLoadingCards((prev) => {
                const newCards = { ...prev };
                delete newCards[parsedMessage.cardData.loadingCardId];
                return newCards;
              });
            }
          }
          break;

        case 'concept_card_update':
          console.log('[WebSocket] Concept card updated:', parsedMessage);

          // Check if this is a position update for a local card (created by current user)
          if (parsedMessage.updates.position) {
            // Dispatch event to update local concept card position
            window.dispatchEvent(
              new CustomEvent('updateConceptCardPosition', {
                detail: {
                  cardId: parsedMessage.cardId,
                  position: parsedMessage.updates.position,
                },
              })
            );
          }

          // Check if this is a status update for a local card
          if (parsedMessage.updates.status) {
            // Check if this is a numImages update (format: "numImages:3")
            if (parsedMessage.updates.status.startsWith('numImages:')) {
              const numImages = parseInt(parsedMessage.updates.status.split(':')[1]);
              console.log('[WebSocket] Updating local concept card numImages:', parsedMessage.cardId, numImages);

              // Dispatch event to update local concept card numImages
              window.dispatchEvent(
                new CustomEvent('updateConceptCardNumImages', {
                  detail: {
                    cardId: parsedMessage.cardId,
                    numImages: numImages,
                  },
                })
              );
            } else if (parsedMessage.updates.status.startsWith('inputText:')) {
              const inputText = parsedMessage.updates.status.substring('inputText:'.length);
              console.log('[WebSocket] Updating local concept card inputText:', parsedMessage.cardId, inputText);

              // Dispatch event to update local concept card inputText
              window.dispatchEvent(
                new CustomEvent('updateConceptCardInputText', {
                  detail: {
                    cardId: parsedMessage.cardId,
                    inputText: inputText,
                  },
                })
              );
            } else {
              // Regular status update
              window.dispatchEvent(
                new CustomEvent('updateConceptCardStatus', {
                  detail: {
                    cardId: parsedMessage.cardId,
                    status: parsedMessage.updates.status,
                  },
                })
              );
            }
          }

          // Also update collaborative concept cards (for cards created by others)
          console.log('[WebSocket] Current collaborative concept cards:', collaborativeConceptCards);
          console.log('[WebSocket] Updating card:', parsedMessage.cardId, 'with updates:', parsedMessage.updates);
          setCollaborativeConceptCards((prev) => {
            const existingCard = prev[parsedMessage.cardId];
            console.log('[WebSocket] Existing collaborative card data:', existingCard);

            // Only update if the card exists in collaborative state
            if (existingCard) {
              let updatedCard = {
                ...existingCard,
                ...parsedMessage.updates,
              };

              // Handle special status updates for collaborative cards
              if (parsedMessage.updates.status) {
                if (parsedMessage.updates.status.startsWith('numImages:')) {
                  // Handle numImages updates
                  const numImages = parseInt(parsedMessage.updates.status.split(':')[1]);
                  updatedCard = {
                    ...updatedCard,
                    numImages: numImages,
                  };
                  // Don't update status field for numImages updates
                  delete updatedCard.status;
                } else if (parsedMessage.updates.status.startsWith('inputText:')) {
                  // Handle inputText updates
                  const inputText = parsedMessage.updates.status.substring('inputText:'.length);
                  updatedCard = {
                    ...updatedCard,
                    inputText: inputText,
                  };
                  // Don't update status field for inputText updates
                  delete updatedCard.status;
                } else {
                  // Regular status update (like 'generating', 'completed', etc.)
                  updatedCard = {
                    ...updatedCard,
                    status: parsedMessage.updates.status,
                  };
                }
              }

              console.log('[WebSocket] Updated collaborative card data:', updatedCard);
              return {
                ...prev,
                [parsedMessage.cardId]: updatedCard,
              };
            }

            console.log('[WebSocket] Card not found in collaborative state, skipping update');
            return prev;
          });
          break;

        case 'concept_card_remove':
          console.log('[WebSocket] Concept card removed:', parsedMessage);

          // Dispatch event to remove local concept card (if it exists locally)
          window.dispatchEvent(
            new CustomEvent('removeConceptCardCollaborative', {
              detail: {
                cardId: parsedMessage.cardId,
              },
            })
          );

          // Remove from collaborative concept cards
          setCollaborativeConceptCards((prev) => {
            const newCards = { ...prev };
            delete newCards[parsedMessage.cardId];
            console.log('[WebSocket] Removed collaborative concept card:', parsedMessage.cardId);
            return newCards;
          });
          break;



        // Server-to-client message handlers (migrated from WASP WebSocket)
        case 'canvas_element_add':
          console.log('[WebSocket] 📥 canvas_element_add');
          // Dispatch event for Canvas component to handle
          window.dispatchEvent(
            new CustomEvent('canvas_element_add', {
              detail: parsedMessage,
            })
          );
          break;

        case 'canvas_element_update_server':
          console.log('[WebSocket] 📝 canvas_element_update');
          // Dispatch event for Canvas component to handle
          window.dispatchEvent(
            new CustomEvent('canvas_element_update', {
              detail: parsedMessage,
            })
          );
          break;

        case 'task_update':
          console.log('[WebSocket] 🔄 task_update');
          // Dispatch event for Canvas component to handle
          window.dispatchEvent(
            new CustomEvent('task_update', {
              detail: parsedMessage,
            })
          );
          break;

        case 'task_complete':
          console.log('[WebSocket] Task complete:', parsedMessage);
          // Dispatch event for components to handle
          window.dispatchEvent(
            new CustomEvent('task_complete', {
              detail: parsedMessage,
            })
          );
          break;

        case 'voice_agent_status':
          console.log('[WebSocket] Voice agent status:', parsedMessage);
          // Dispatch event for voice agent components to handle
          window.dispatchEvent(
            new CustomEvent('voice_agent_status', {
              detail: parsedMessage,
            })
          );
          break;

        case 'voice_agent_transcript':
          console.log('[WebSocket] Voice agent transcript:', parsedMessage);
          // Dispatch event for voice agent components to handle
          window.dispatchEvent(
            new CustomEvent('voice_agent_transcript', {
              detail: parsedMessage,
            })
          );
          break;

        case 'voice_agent_response':
          console.log('[WebSocket] Voice agent response:', parsedMessage);
          // Dispatch event for voice agent components to handle
          window.dispatchEvent(
            new CustomEvent('voice_agent_response', {
              detail: parsedMessage,
            })
          );
          break;

        case 'newsletter_card_status_update':
          console.log('[WebSocket] Newsletter card status update:', parsedMessage);
          // Dispatch event for newsletter components to handle
          window.dispatchEvent(
            new CustomEvent('newsletter_card_status_update', {
              detail: parsedMessage,
            })
          );
          break;

        case 'progress_update':
          console.log('[WebSocket] Progress update:', parsedMessage);
          // Dispatch event for progress components to handle
          window.dispatchEvent(
            new CustomEvent('progress_update', {
              detail: parsedMessage,
            })
          );
          break;

        default:
          if (process.env.NODE_ENV === 'development') {
            console.log('[WebSocket] Unhandled message type:', parsedMessage.type);
          }
      }
    },
    [setCanvasState, setUsers, effectiveUser?.id] // Add dependencies for state setters
  );

  // Canvas operations
  const createElement = useCallback(
    (elementData: Omit<CanvasElement, 'id' | 'createdAt' | 'updatedAt'>) => {
      const element: CanvasElement = {
        ...elementData,
        id: elementData.properties.id,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        createdBy: effectiveUser?.name ?? 'current-user',
      };

      console.log({ element, elementData });
      // Optimistic update
      setCanvasState((prev) => ({
        ...prev,
        elements: {
          ...prev.elements,
          [element.id]: element,
        },
        version: prev.version + 1,
      }));
      // Send to server
      const message: ElementCreateMessage = {
        type: 'element_create',
        userId: effectiveUser?.id ?? 'unknown',
        timestamp: Date.now(),
        roomId,
        element,
        version: canvasStateRef.current.version + 1,
      };
      sendJsonMessage(message);
    },
    [effectiveUser?.id, roomId, sendJsonMessage]
  );

  const updateThrottleRef = useRef<Map<string, NodeJS.Timeout>>(new Map());

  const updateElement = useCallback(
    (elementId: string, changes: Partial<CanvasElement>, throttle = true) => {
      // Optimistic update (always immediate for UI responsiveness)
      setCanvasState((prev) => {
        const element = prev.elements[elementId];
        if (!element) return prev;

        return {
          ...prev,
          elements: {
            ...prev.elements,
            [elementId]: {
              ...element,
              ...changes,
              updatedAt: Date.now(),
            },
          },
          version: prev.version + 1,
        };
      });

      // Send to server (with optional throttling for smooth dragging)
      const sendUpdate = (isDragUpdate = false) => {
        const baseMessage = {
          userId: effectiveUser?.id ?? 'unknown',
          timestamp: Date.now(),
          roomId,
          elementId,
          changes: {
            ...changes,
            updatedAt: isDragUpdate ? undefined : Date.now(),
          },
          version: canvasStateRef.current.version + 1,
        };

        const message = isDragUpdate
          ? { ...baseMessage, type: 'element_drag_update' as const }
          : { ...baseMessage, type: 'element_update' as const };

        sendJsonMessage(message);
      };

      if (throttle) {
        // Clear existing throttle for this element
        const existingThrottle = updateThrottleRef.current.get(elementId);
        if (existingThrottle) {
          clearTimeout(existingThrottle);
        }

        // Set new throttle for drag updates
        const newThrottle = setTimeout(() => {
          sendUpdate(true); // Send as drag update
          updateThrottleRef.current.delete(elementId);
        }, 50); // Faster updates for dragging (50ms)

        updateThrottleRef.current.set(elementId, newThrottle);
      } else {
        // Send immediately (for final updates like dragEnd)
        sendUpdate(false); // Send as regular update
      }
    },
    [effectiveUser?.id, roomId, sendJsonMessage]
  );

  const deleteElement = useCallback(
    (elementId: string) => {
      // Optimistic update
      setCanvasState((prev) => {
        const newElements = { ...prev.elements };
        delete newElements[elementId];

        return {
          ...prev,
          elements: newElements,
          selectedIds: prev.selectedIds.filter((id) => id !== elementId),
          version: prev.version + 1,
        };
      });

      // Send to server
      const message: ElementDeleteMessage = {
        type: 'element_delete',
        userId: effectiveUser?.id ?? 'unknown',
        timestamp: Date.now(),
        roomId,
        elementId,
        version: canvasStateRef.current.version + 1,
      };

      sendJsonMessage(message);
    },
    [effectiveUser?.id, roomId, sendJsonMessage]
  );

  const setSelectedIds = useCallback(
    (ids: string[]) => {
      setCanvasState((prev) => ({
        ...prev,
        selectedIds: ids,
      }));

      // Send to server
      sendJsonMessage({
        type: 'selection_update',
        userId: effectiveUser?.id ?? 'unknown',
        timestamp: Date.now(),
        roomId,
        selectedIds: ids,
      });
    },
    [effectiveUser?.id, roomId, sendJsonMessage]
  );

  const updateCursor = useCallback(
    (position: Position) => {
      sendJsonMessage({
        type: 'cursor_move',
        userId: effectiveUser?.id ?? 'unknown',
        timestamp: Date.now(),
        roomId,
        position,
      });
    },
    [effectiveUser?.id, roomId, sendJsonMessage]
  );

  const updateUserName = useCallback(
    (newName: string) => {
      // Update local state for guest users
      if (!user) {
        setGuestUserName(newName);
        stableGuestName.current = newName;

        // Persist to sessionStorage
        sessionStorage.setItem('canvas_guest_user', JSON.stringify({
          id: stableGuestId.current,
          name: newName
        }));
      }

      // Send name update to server
      sendJsonMessage({
        type: 'user_name_update',
        userId: effectiveUser?.id ?? 'unknown',
        timestamp: Date.now(),
        roomId,
        newName,
      });
    },
    [effectiveUser?.id, roomId, sendJsonMessage, user]
  );

  const updateSelection = useCallback((selection: { visible: boolean; x: number; y: number; width: number; height: number }) => {
    if (!effectiveUser?.id) return;

    // Removed spammy selection update log

    // Send drag selection update to server (throttled to avoid spam)
    sendJsonMessage({
      type: 'drag_selection_update',
      userId: effectiveUser.id,
      selection,
      timestamp: Date.now(),
      roomId,
    });
  }, [effectiveUser?.id, roomId, sendJsonMessage, userColor]);

  const createChatBubble = useCallback((bubble: { id: string; position: { x: number; y: number }; userId: string; message: string }) => {
    if (!effectiveUser?.id) return;

    sendJsonMessage({
      type: 'chat_bubble_create',
      userId: effectiveUser.id,
      bubbleId: bubble.id,
      position: bubble.position,
      message: bubble.message,
      timestamp: Date.now(),
      roomId,
    });
  }, [effectiveUser?.id, roomId, sendJsonMessage]);

  const updateChatBubble = useCallback((bubbleId: string, message: string) => {
    if (!effectiveUser?.id) return;

    sendJsonMessage({
      type: 'chat_bubble_update',
      userId: effectiveUser.id,
      bubbleId,
      message,
      timestamp: Date.now(),
      roomId,
    });
  }, [effectiveUser?.id, roomId, sendJsonMessage]);

  const closeChatBubble = useCallback((bubbleId: string) => {
    if (!effectiveUser?.id) return;

    sendJsonMessage({
      type: 'chat_bubble_close',
      userId: effectiveUser.id,
      bubbleId,
      timestamp: Date.now(),
      roomId,
    });
  }, [effectiveUser?.id, roomId, sendJsonMessage]);

  const createLoadingCard = useCallback((cardData: { id: string; position: { x: number; y: number }; requestText: string }) => {
    if (!effectiveUser?.id) return;

    console.log('🔄 [WebSocket] Creating loading card:', cardData);

    sendJsonMessage({
      type: 'loading_card_create',
      userId: effectiveUser.id,
      userName: effectiveUser.name || 'Unknown User',
      userColor: userColor,
      cardId: cardData.id,
      position: cardData.position,
      requestText: cardData.requestText,
      timestamp: Date.now(),
      roomId,
    });
  }, [effectiveUser?.id, effectiveUser?.name, userColor, roomId, sendJsonMessage]);

  const removeLoadingCard = useCallback((cardId: string) => {
    if (!effectiveUser?.id) return;

    console.log('🔄 [WebSocket] Removing loading card:', cardId);

    sendJsonMessage({
      type: 'loading_card_remove',
      userId: effectiveUser.id,
      cardId,
      timestamp: Date.now(),
      roomId,
    });
  }, [effectiveUser?.id, roomId, sendJsonMessage]);

  const updateLoadingCard = useCallback((cardId: string, updates: any) => {
    if (!effectiveUser?.id) return;

    console.log('🔄 [WebSocket] Updating loading card:', { cardId, updates });

    sendJsonMessage({
      type: 'loading_card_update',
      userId: effectiveUser.id,
      cardId,
      updates,
      timestamp: Date.now(),
      roomId,
    });
  }, [effectiveUser?.id, roomId, sendJsonMessage]);

  const createConceptCard = useCallback((cardData: any) => {
    if (!effectiveUser?.id) return;

    console.log('🎨 [WebSocket] Creating concept card:', cardData);

    sendJsonMessage({
      type: 'concept_card_create',
      userId: effectiveUser.id,
      userName: effectiveUser.name || 'Unknown User',
      userColor: userColor,
      cardId: cardData.id,
      cardData,
      timestamp: Date.now(),
      roomId,
    });
  }, [effectiveUser?.id, effectiveUser?.name, userColor, roomId, sendJsonMessage]);

  const updateConceptCard = useCallback((cardId: string, updates: any) => {
    if (!effectiveUser?.id) return;

    console.log('🎨 [WebSocket] Updating concept card:', { cardId, updates });

    sendJsonMessage({
      type: 'concept_card_update',
      userId: effectiveUser.id,
      cardId,
      updates,
      timestamp: Date.now(),
      roomId,
    });
  }, [effectiveUser?.id, roomId, sendJsonMessage]);

  const removeConceptCard = useCallback((cardId: string) => {
    if (!effectiveUser?.id) return;

    console.log('🎨 [WebSocket] Removing concept card:', cardId);

    sendJsonMessage({
      type: 'concept_card_remove',
      userId: effectiveUser.id,
      cardId,
      timestamp: Date.now(),
      roomId,
    });
  }, [effectiveUser?.id, roomId, sendJsonMessage]);

  const removeLocalConceptCard = useCallback((cardId: string) => {
    console.log('🗑️ [Local] Removing collaborative concept card locally:', cardId);

    setCollaborativeConceptCards((prev) => {
      const newCards = { ...prev };
      delete newCards[cardId];
      return newCards;
    });
  }, []);

  const uploadImage = useCallback(
    async (file: File): Promise<string | null> => {
      try {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('roomId', roomId);

        const response = await fetch(`${WS_BASE_URL}/api/upload`, {
          method: 'POST',
          body: formData,
        });

        const result = await response.json();

        if (result.success) {
          console.log({ result });
          // Convert relative URL to absolute URL using the API base
          const imageUrl = result.url.startsWith('/') ? `${WS_BASE_URL}${result.url}` : result.url;
          return imageUrl;
        } else {
          console.error('Image upload failed:', result.error);
          return null;
        }
      } catch (error) {
        console.error('Image upload error:', error);
        return null;
      }
    },
    [roomId]
  );

  // Memoize return value to prevent unnecessary re-renders
  const returnValue = useMemo(
    () => ({
      isConnected: readyState === ReadyState.OPEN,
      sendMessage,
      sendJsonMessage,
      lastMessage,
      lastJsonMessage,
      readyState,
      users,
      userSelections,
      canvasState,
      updateCursor,
      createElement,
      updateElement,
      setSelectedIds,
      uploadImage,
      deleteElement,
      updateUserName,
      updateSelection,
      createChatBubble,
      updateChatBubble,
      closeChatBubble,
      chatBubbles,
      createLoadingCard,
      removeLoadingCard,
      updateLoadingCard,
      createConceptCard,
      updateConceptCard,
      removeConceptCard,
      removeLocalConceptCard,
      collaborativeLoadingCards,
      collaborativeConceptCards,
      currentUserName: effectiveUser?.name,
      currentUserId: effectiveUser?.id,
      currentUserColor: userColor,
      isGuest: !user,
    }),
    [
      sendMessage,
      sendJsonMessage,
      lastMessage,
      lastJsonMessage,
      readyState,
      users,
      userSelections,
      canvasState,
      updateCursor,
      createElement,
      updateElement,
      setSelectedIds,
      uploadImage,
      deleteElement,
      updateUserName,
      updateSelection,
      createChatBubble,
      updateChatBubble,
      closeChatBubble,
      chatBubbles,
      createLoadingCard,
      removeLoadingCard,
      updateLoadingCard,
      createConceptCard,
      updateConceptCard,
      removeConceptCard,
      removeLocalConceptCard,
      collaborativeLoadingCards,
      collaborativeConceptCards,
      effectiveUser?.name,
      effectiveUser?.id,
      userColor,
      user,
    ]
  );

  return returnValue;
};

const generateUserColor = (username: string): string => {
  let hash = 0;
  for (let i = 0; i < username.length; i++) {
    hash = username.charCodeAt(i) + ((hash << 5) - hash);
  }

  const hue = Math.abs(hash) % 360;
  return `hsl(${hue}, 70%, 50%)`;
};

export function removeEmailDomain(email: string) {
  const atIndex = email.indexOf('@');
  if (atIndex !== -1) {
    return email.substring(0, atIndex);
  }
  return email;
}
