import { useState, useCallback } from 'react';

export const useChatBubbleManager = () => {
  const [chatBubble, setChatBubble] = useState({
    isVisible: false,
    position: { x: 0, y: 0 },
    productId: undefined as string | undefined,
  });

  const handleCloseChatBubble = useCallback(() => {
    setChatBubble((prev) => ({ ...prev, isVisible: false }));
  }, []);

  const handleContextMenu = useCallback((e: any, scale: number, position: {x: number, y: number}) => {
    e.evt.preventDefault();

    if (e.target.className === 'Image' || (e.target.findAncestor && e.target.findAncestor('.html-element-konva-group'))) {
      return;
    }

    const stage = e.target.getStage();
    if (!stage) return;

    const pointer = stage.getPointerPosition();
    if (!pointer) return;
    
    const stageContainer = stage.container();
    const containerRect = stageContainer.getBoundingClientRect();
    const screenX = containerRect.left + pointer.x;
    const screenY = containerRect.top + pointer.y;

    setChatBubble({
      isVisible: true,
      position: { x: screenX, y: screenY },
      productId: 'canvas-test', // TODO: Get actual product ID
    });

    const canvasX = (pointer.x - position.x) / scale;
    const canvasY = (pointer.y - position.y) / scale;

    window.dispatchEvent(
      new CustomEvent('canvas:rightClick', {
        detail: {
          position: { x: screenX, y: screenY },
          canvasPosition: { x: canvasX, y: canvasY },
        },
      })
    );
  }, []);

  return {
    chatBubble,
    handleCloseChatBubble,
    handleContextMenu,
  };
}; 