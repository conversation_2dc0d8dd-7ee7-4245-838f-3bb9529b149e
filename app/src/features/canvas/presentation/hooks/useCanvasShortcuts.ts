import { useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import useEventListener from '../../../../client/hooks/useEventListener';
import { CanvasElement } from './useCanvasSync';
import { calculateScaledDimensions } from '../utils';
import { ImageProperties } from '../components/elements/images/URLImage';

type CanvasState = {
  elements: { [key: string]: CanvasElement };
  selectedIds: string[];
};

export const useCanvasShortcuts = (
  canvasState: CanvasState,
  deleteElement: (id: string) => void,
  createElement: (element: Omit<CanvasElement, 'id'> & { properties: { id: string } }) => void,
  updateElement: (id: string, element: Partial<CanvasElement>) => void,
  uploadImage: (file: File) => Promise<string | null>
) => {
  const handleDeleteAction = useCallback(() => {
    canvasState.selectedIds.forEach((id) => {
      deleteElement(id);
    });
  }, [canvasState.selectedIds, deleteElement]);

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (e.key === 'Delete' || e.key === 'Backspace') {
        handleDeleteAction();
      }
    },
    [handleDeleteAction]
  );

  const handleRemoveImage = useCallback(() => {
    handleDeleteAction();
  }, [handleDeleteAction]);

  const handlePaste = useCallback(
    async (e: ClipboardEvent) => {
      if (!e.clipboardData) return;

      const items = Array.from(e.clipboardData.items);
      const imageItem = items.find((item) => item.type.startsWith('image/'));

      if (imageItem) {
        e.preventDefault();
        const file = imageItem.getAsFile();
        if (file) {
          const imageUrl = await uploadImage(file);
          if (imageUrl) {
            const img = new Image();
            img.onload = () => {
              const scaledDimensions = calculateScaledDimensions(img.naturalWidth, img.naturalHeight, 400, 400);
              createElement({
                type: 'image',
                position: { x: 100, y: 100 }, // Fallback position
                properties: {
                  id: `image-${uuidv4()}`,
                  url: imageUrl,
                  originalName: file.name,
                  size: scaledDimensions,
                },
                zIndex: Object.keys(canvasState.elements).length + 1,
                createdBy: 'current-user',
                createdAt: Date.now(),
                updatedAt: Date.now(),
              });
            };
            img.onerror = () => console.error('Failed to load uploaded image for paste');
            img.src = imageUrl;
          }
        }
      }
    },
    [uploadImage, createElement, canvasState.elements]
  );

  const handleDuplicateImage = useCallback(() => {
    canvasState.selectedIds.forEach((id) => {
      const element = canvasState.elements[id];
      if (element) {
        createElement({
          type: 'image',
          position: { x: element.position.x + 20, y: element.position.y + 20 },
          properties: {
            ...element.properties,
            id: `image-${uuidv4()}`,
            isReference: false,
          },
          zIndex: Object.keys(canvasState.elements).length + 1,
          createdBy: 'current-user',
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      }
    });
  }, [canvasState.elements, canvasState.selectedIds, createElement]);

  const handleSetImageAsReference = useCallback(() => {
    canvasState.selectedIds.forEach((id) => {
      const element = canvasState.elements[id];
      if (element && element.type === 'image' && !(element.properties as ImageProperties).isReference) {
        updateElement(id, {
          ...element,
          properties: {
            ...element.properties,
            isReference: true,
          },
        });
      }
    });
  }, [canvasState.elements, canvasState.selectedIds, updateElement]);

  const handleBringForward = useCallback(() => {
    canvasState.selectedIds.forEach((id) => {
      const element = canvasState.elements[id];
      if (element && element.type === 'image') {
        updateElement(id, {
          ...element,
          zIndex: element.zIndex + 1,
        });
      }
    });
  }, [canvasState.elements, canvasState.selectedIds, updateElement]);

  const handleSendBackward = useCallback(() => {
    canvasState.selectedIds.forEach((id) => {
      const element = canvasState.elements[id];
      if (element && element.type === 'image') {
        updateElement(id, {
          ...element,
          zIndex: element.zIndex - 1,
        });
      }
    });
  }, [canvasState.elements, canvasState.selectedIds, updateElement]);

  useEventListener('keydown', handleKeyDown);
  useEventListener('removeImage' as keyof WindowEventMap, handleRemoveImage);
  useEventListener('paste', handlePaste);
  useEventListener('duplicateImage' as keyof WindowEventMap, handleDuplicateImage);
  useEventListener('setImageAsReference' as keyof WindowEventMap, handleSetImageAsReference);
  useEventListener('bringForward' as keyof WindowEventMap, handleBringForward);
  useEventListener('sendBackward' as keyof WindowEventMap, handleSendBackward);
}; 