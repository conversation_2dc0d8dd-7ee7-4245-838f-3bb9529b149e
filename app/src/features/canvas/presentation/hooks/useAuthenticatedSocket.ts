/**
 * 🔌 Authenticated Socket Hook - CANVAS FEATURE
 * @description Canvas-specific wrapper for the core useAuthenticatedSocket
 * @responsibility Re-exports the core authenticated socket hook
 * @dependencies Core useAuthenticatedSocket
 * @ai_context This re-exports the core hook for backward compatibility in canvas features
 */

// Re-export the core authenticated socket hook
export { useAuthenticatedSocket } from '../../../../core/websocket/useAuthenticatedSocket';
