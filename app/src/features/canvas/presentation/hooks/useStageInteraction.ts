import { useCallback, useState } from 'react';
import Konva from 'konva';
import { useToolDomainStore, DomainCanvasTool, ToolDomainState } from '../../domain/state';
import { ToolService } from '../../domain/services';
import { useShallow } from 'zustand/react/shallow';

export const useStageInteraction = (
  stageRef: React.RefObject<Konva.Stage>,
  updateCursor: (point: { x: number; y: number }) => void,
  setSelectedIds: (ids: string[]) => void
) => {
  const [isPanning, setIsPanning] = useState(false);
  const { activeTool, scale, position } = useToolDomainStore(useShallow((state: ToolDomainState) => ({
    activeTool: state.activeTool,
    scale: state.scale,
    position: state.position,
  })));

  const handleMouseDown = useCallback(
    (e: Konva.KonvaEventObject<MouseEvent>) => {
      if (activeTool === DomainCanvasTool.HAND) {
        setIsPanning(true);
        const stage = e.target.getStage();
        if (stage) {
          const pointer = stage.getPointerPosition();
          if (pointer) {
            (window as any).__handPanning = {
              active: true,
              initialMousePosition: pointer,
              initialStagePosition: { ...position },
            };
          }
        }
      }
    },
    [activeTool, position]
  );

  const handleStageMouseMove = useCallback(
    (e: Konva.KonvaEventObject<MouseEvent>) => {
      const stage = e.target.getStage();
      if (!stage) return;

      const pointer = stage.getPointerPosition();
      if (pointer) {
        const canvasX = (pointer.x - position.x) / scale;
        const canvasY = (pointer.y - position.y) / scale;
        updateCursor({ x: canvasX, y: canvasY });
      }

      if (activeTool === DomainCanvasTool.HAND && isPanning && (window as any).__handPanning?.active) {
        if (pointer) {
          const { initialMousePosition, initialStagePosition } = (window as any).__handPanning;
          const dx = pointer.x - initialMousePosition.x;
          const dy = pointer.y - initialMousePosition.y;
          const newPosition = {
            x: initialStagePosition.x + dx,
            y: initialStagePosition.y + dy,
          };
          ToolService.canvas.setPosition(newPosition);
          stage.position(newPosition);
          stage.batchDraw();
        }
      }
    },
    [activeTool, isPanning, position, scale, updateCursor]
  );

  const handleMouseUp = useCallback(() => {
    if (activeTool === DomainCanvasTool.HAND && isPanning) {
      setIsPanning(false);
      (window as any).__handPanning = null;
    }
  }, [activeTool, isPanning]);

  const handleClick = useCallback(
    (e: Konva.KonvaEventObject<MouseEvent>) => {
      const clickedOnStage = e.target === e.target.getStage();
      if (clickedOnStage) {
        setSelectedIds([]);
        window.dispatchEvent(new CustomEvent('canvas:backgroundClick'));
      }
    },
    [setSelectedIds]
  );

  const handleWheel = useCallback((e: Konva.KonvaEventObject<WheelEvent>) => {
    e.evt.preventDefault();
    e.evt.stopPropagation();

    const stage = e.target.getStage();
    if (!stage) return;

    const pointer = stage.getPointerPosition();
    if (!pointer) return;
    
    const oldScale = stage.scaleX();
    const mousePointTo = {
      x: (pointer.x - stage.x()) / oldScale,
      y: (pointer.y - stage.y()) / oldScale,
    };

    if (e.evt.ctrlKey) {
      const zoomSensitivity = 0.0025;
      const scrollDelta = -e.evt.deltaY;
      const dampingFactor = 0.75;
      const dampedScrollDelta = scrollDelta * dampingFactor;
      let newScale = oldScale + dampedScrollDelta * zoomSensitivity;
      newScale = Math.max(0.1, Math.min(newScale, 10.0));
      
      const newPos = {
        x: pointer.x - mousePointTo.x * newScale,
        y: pointer.y - mousePointTo.y * newScale,
      };
      ToolService.zoom.setScale(newScale);
      ToolService.canvas.setPosition(newPos);

    } else {
      const panSensitivity = 1.0;
      const dx = e.evt.deltaX * panSensitivity;
      const dy = e.evt.deltaY * panSensitivity;
      const newPos = {
        x: stage.x() - dx,
        y: stage.y() - dy,
      };
      ToolService.canvas.setPosition(newPos);
    }
    stage.batchDraw();
  }, []);

  return {
    isPanning,
    handleMouseDown,
    handleStageMouseMove,
    handleMouseUp,
    handleClick,
    handleWheel,
  };
}; 