/**
 * 🖼️ Reference Images Hook - MODULAR VERSION
 * @description Simplified hook for managing reference images in modular canvas
 * @responsibility Provide reference image functionality for canvas features
 * @ai_context Simplified version of useReferenceImages for modular architecture
 */

import { useCallback } from 'react';
import {
  useQuery,
  useAction,
  getCanvasReferences,
  createCanvasReference,
  deleteCanvasReference,
} from 'wasp/client/operations';

/**
 * 🖼️ Simplified Reference Images Hook
 * @description Provides basic reference image functionality for modular canvas
 * @param roomName The room name to associate reference images with
 * @returns Functions for managing reference images
 */
export const useReferenceImages = (roomName: string) => {
  const modelId = localStorage.getItem('selectedModelId');
  const whiteboardId = modelId || '';

  // Query canvas references from database
  const { data: canvasReferences, isLoading } = useQuery(
    getCanvasReferences,
    { whiteboardId: whiteboardId ?? '' },
    { enabled: !!whiteboardId }
  );

  // Actions for managing canvas references
  const createCanvasReferenceAction = useAction(createCanvasReference);
  const deleteCanvasReferenceAction = useAction(deleteCanvasReference);

  // Function to get reference images (filter out base64 data URLs)
  const getReferenceImages = useCallback(() => {
    if (!canvasReferences) return [];
    return canvasReferences
      .map((ref: any) => ref.thumbUrl)
      .filter((url: string) => url && typeof url === 'string' && !url.startsWith('data:'));
  }, [canvasReferences]);

  // Function to check if an image is a reference image
  const isReferenceImage = useCallback(
    (imageUrl: string) => {
      try {
        if (!canvasReferences) return false;
        const referenceImages = canvasReferences.map((ref: any) => ref.thumbUrl);
        return referenceImages.includes(imageUrl);
      } catch (error) {
        console.error('[useReferenceImages] Error checking if image is a reference:', error);
        return false;
      }
    },
    [canvasReferences]
  );

  // Function to add a reference image
  const addReferenceImage = useCallback(
    async (imageUrl: string) => {
      if (!whiteboardId) {
        console.warn('[useReferenceImages] No whiteboardId provided, cannot add reference image');
        return false;
      }

      // Get current reference images from database
      const currentReferences = canvasReferences || [];
      const referenceImages = currentReferences.map((ref: any) => ref.thumbUrl);

      // Check if the image is already in the list
      if (!referenceImages.includes(imageUrl)) {
        try {
          console.log('[useReferenceImages] Adding reference image:', imageUrl);

          // Add to database
          await createCanvasReferenceAction({
            whiteboardId,
            thumbUrl: imageUrl,
          });

          return true;
        } catch (error) {
          console.error('[useReferenceImages] Error adding reference image:', error);
          return false;
        }
      }

      return false;
    },
    [whiteboardId, canvasReferences, createCanvasReferenceAction]
  );

  // Function to remove a reference image
  const removeReferenceImage = useCallback(
    async (imageUrl: string) => {
      if (!whiteboardId) {
        console.warn('[useReferenceImages] No whiteboardId provided, cannot remove reference image');
        return false;
      }

      // Get current reference images from database
      const currentReferences = canvasReferences || [];
      const referenceToRemove = currentReferences.find((ref: any) => ref.thumbUrl === imageUrl);

      if (referenceToRemove) {
        try {
          console.log('[useReferenceImages] Removing reference image:', imageUrl);

          // Remove from database
          await deleteCanvasReferenceAction({ id: referenceToRemove.id });

          return true;
        } catch (error) {
          console.error('[useReferenceImages] Error removing reference image:', error);
          return false;
        }
      }

      return false;
    },
    [whiteboardId, canvasReferences, deleteCanvasReferenceAction]
  );

  // Return the functions
  return {
    getReferenceImages,
    isReferenceImage,
    isLoading,
    addReferenceImage,
    removeReferenceImage,
    clearReferenceImages: async () => {}, // TODO: Implement if needed
    reorderReferenceImages: () => false, // TODO: Implement if needed
  };
};
