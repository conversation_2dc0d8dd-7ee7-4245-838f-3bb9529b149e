/**
 * 🔄 Pending Tasks Hook - MODULAR VERSION
 * @description Hook for managing pending tasks in modular canvas
 * @responsibility Handle pending task operations without external dependencies
 * @ai_context Replaces client/canvas/hooks/usePendingTasks with modular version
 */

import { useState, useCallback } from 'react';
import type { CanvasImage } from '../../domain/services/canvas-operations.service';

export interface PendingTask {
  id: string;
  taskId: string;
  status: 'IN_QUEUE' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  progress: number;
  x: number;
  y: number;
  width: number;
  height: number;
}

/**
 * 🔄 Pending Tasks Hook
 * @description Manages pending tasks for the modular canvas
 */
export const usePendingTasks = (images: CanvasImage[], setImages: (images: CanvasImage[]) => void) => {
  const [pendingTasks, setPendingTasks] = useState<PendingTask[]>([]);

  /**
   * 📝 Add Pending Image
   * @description Adds a pending image placeholder
   */
  const handleAddPendingImage = useCallback(
    (taskId: string, x: number, y: number, width: number, height: number) => {
      console.log('[usePendingTasks] Adding pending image:', { taskId, x, y, width, height });

      // Create pending task
      const pendingTask: PendingTask = {
        id: `pending-${taskId}`,
        taskId,
        status: 'IN_QUEUE',
        progress: 0,
        x,
        y,
        width,
        height,
      };

      // Add to pending tasks
      setPendingTasks((prev) => [...prev, pendingTask]);

      // Create pending image
      const pendingImage: CanvasImage = {
        id: `pending-${taskId}`,
        src: '/loading-placeholder.png', // Placeholder image
        x,
        y,
        width,
        height,
        taskId,
        isDragging: false,
      };

      // Add to images
      setImages([...images, pendingImage]);

      return pendingTask.id;
    },
    [images, setImages]
  );

  /**
   * 🔄 Update Pending Task
   * @description Updates a pending task status/progress
   */
  const updatePendingTask = useCallback(
    (taskId: string, updates: Partial<Pick<PendingTask, 'status' | 'progress'>>) => {
      console.log('[usePendingTasks] Updating pending task:', { taskId, updates });

      setPendingTasks((prev) => prev.map((task) => (task.taskId === taskId ? { ...task, ...updates } : task)));
    },
    []
  );

  /**
   * ✅ Complete Pending Task
   * @description Completes a pending task and replaces with final image
   */
  const completePendingTask = useCallback(
    (taskId: string, finalImageUrl: string) => {
      console.log('[usePendingTasks] Completing pending task:', { taskId, finalImageUrl });

      // Remove from pending tasks
      setPendingTasks((prev) => prev.filter((task) => task.taskId !== taskId));

      // Update the image with final URL
      const updatedImages = images.map((img) => (img.taskId === taskId ? { ...img, src: finalImageUrl } : img));

      setImages(updatedImages);
    },
    [images, setImages]
  );

  /**
   * ❌ Remove Pending Task
   * @description Removes a pending task (on failure)
   */
  const removePendingTask = useCallback(
    (taskId: string) => {
      console.log('[usePendingTasks] Removing pending task:', { taskId });

      // Remove from pending tasks
      setPendingTasks((prev) => prev.filter((task) => task.taskId !== taskId));

      // Remove from images
      const updatedImages = images.filter((img) => img.taskId !== taskId);
      setImages(updatedImages);
    },
    [images, setImages]
  );

  return {
    pendingTasks,
    handleAddPendingImage,
    updatePendingTask,
    completePendingTask,
    removePendingTask,
  };
};
