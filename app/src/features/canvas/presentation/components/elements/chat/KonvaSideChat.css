.refine-chat {
  width: 350px !important;
  background-color: white !important;
  border-radius: 12px !important;
  display: flex !important;
  flex-direction: column !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  overflow: hidden !important;
  border: 1px solid #2b4322 !important;
  min-height: 500px !important;
  max-height: 700px !important;
}

.refine-chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.refine-chat-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.refine-chat-logo-img {
  width: 24px;
  height: 24px;
}

.refine-chat-close-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #4b5563;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.refine-chat-close-btn:hover {
  background-color: #f3f4f6;
}

.refine-chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  min-height: 400px !important;
  max-height: 400px !important;
  border-bottom: 1px solid #e5e7eb;
}

.refine-chat-messages {
  display: flex;
  flex-direction: column;
  gap: 24px !important;
  padding-bottom: 20px !important;
}

.refine-chat-message {
  display: flex;
  flex-direction: column;
  max-width: 85%;
}

.user-message {
  align-self: flex-end;
}

.ai-message {
  align-self: flex-start;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word;
}

.refine-chat .refine-chat-messages .user-message .message-bubble {
  background-color: #f0f0f0 !important;
  color: #1f2937 !important;
  border-bottom-right-radius: 4px !important;
}

.refine-chat .refine-chat-messages .ai-message .message-bubble {
  background-color: #2b4322 !important;
  color: white !important;
  border-bottom-left-radius: 4px !important;
}

.loading-message {
  position: relative;
}

.loading-message::after {
  content: "...";
  position: absolute;
  right: 8px;
  bottom: 8px;
  animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
  0%, 20% { content: "."; }
  40%, 60% { content: ".."; }
  80%, 100% { content: "..."; }
}

/* Image-related styles removed as we're now using the history modal instead */

/* Removed persistent preview area CSS as it's not needed */

/* Paintbrush icon styles */
.paintbrush-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  background-color: #f3f4f6;
  border-radius: 4px;
  padding: 6px;
  cursor: pointer;
}


.refine-chat-input-container {
  display: flex;
  padding: 12px 16px;
  background-color: white;
}

.refine-chat-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
  width: calc(100% - 30px); /* Reduced width to accommodate the paintbrush icon */
}

.refine-chat-input:focus {
  border-color: #2b4322;
}

.refine-chat .refine-chat-input-container .refine-chat-send-btn {
  background-color: #2b4322 !important;
  color: white !important;
  border: none !important;
  border-radius: 4px !important;
  width: 36px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin-left: 8px !important;
  cursor: pointer !important;
  transition: background-color 0.2s !important;
  padding: 0 !important;
  min-width: 36px !important;
  max-width: 36px !important;
  min-height: 36px !important;
  max-height: 36px !important;
}

.refine-chat .refine-chat-input-container .refine-chat-send-btn:hover:not(:disabled) {
  background-color: #243a1c !important;
}

.refine-chat .refine-chat-input-container .refine-chat-send-btn:disabled {
  background-color: #9ca3af !important;
  cursor: not-allowed !important;
}

/* Markdown styling */
.markdown-content {
  font-size: 14px;
  line-height: 1.5;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3 {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.markdown-content h1 {
  font-size: 1.4em;
}

.markdown-content h2 {
  font-size: 1.2em;
}

.markdown-content h3 {
  font-size: 1.1em;
}

.markdown-content p {
  margin-bottom: 0.75em;
}

.markdown-content ul,
.markdown-content ol {
  margin-left: 1.5em;
  margin-bottom: 0.75em;
}

.markdown-content li {
  margin-bottom: 0.25em;
}

.markdown-content code {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
  font-size: 0.9em;
}

.markdown-content pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.5em;
  border-radius: 5px;
  overflow-x: auto;
  margin-bottom: 0.75em;
}

.markdown-content a {
  color: #2b4322;
  text-decoration: underline;
}

.markdown-content blockquote {
  border-left: 3px solid #2b4322;
  padding-left: 1em;
  margin-left: 0;
  color: #555;
}

.markdown-content img {
  max-width: 100%;
  border-radius: 5px;
}
