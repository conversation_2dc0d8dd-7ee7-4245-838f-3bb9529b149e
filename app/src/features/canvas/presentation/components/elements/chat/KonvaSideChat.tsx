import React, { useState, useEffect } from 'react';
import { Group } from 'react-konva';
import { Html } from 'react-konva-utils';
import { X, ArrowRight, Paintbrush } from 'lucide-react';
import { api } from 'wasp/client/api';
// 🧠 MIGRATED: Stub organization state for modular architecture
// import { useOrganizationState } from '../../../organization/store';

// 🧠 MIGRATED: Stub organization state hook
const useOrganizationState = () => ({
  selectedBrandKit: null,
  brandKits: [],
  selectedOrganizationId: null,
});
import ReactMarkdown from 'react-markdown';
import './KonvaSideChat.css';

interface KonvaSideChatProps {
  imageId: string;
  image: HTMLImageElement;
  x: number;
  onClose: () => void;
  onImageEdit?: (newImageUrl: string) => void; // New callback for image edits
  onMaskingModeChange: () => void;
}

interface ChatMessage {
  text: string;
  isUser: boolean;
  isLoading?: boolean;
  isImage?: boolean;
  isActive?: boolean; // Property to track the active image
  timestamp?: number; // Timestamp for ordering
}

interface ImageAnalysis {
  description: string;
  suggestedEdits: {
    suggestion1: string;
    suggestion2: string;
  };
}

const KonvaSideChat: React.FC<KonvaSideChatProps> = ({
  imageId,
  image,
  x,
  onClose,
  onImageEdit,
  onMaskingModeChange,
}) => {
  // Get the organization ID from the organization store
  const { selectedOrganizationId } = useOrganizationState();

  const [inputValue, setInputValue] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([
    { text: 'Analyzing image details...', isUser: false, timestamp: Date.now(), isLoading: true },
  ]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(true);
  const [activeImage, setActiveImage] = useState<string>(image.src);
  const [imageAnalysis, setImageAnalysis] = useState<ImageAnalysis>();
  const [imageChatId, setImageChatId] = useState<string | null>(null);
  const [imageHistory, setImageHistory] = useState<string[]>([image.src]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(true);
  const [pendingImageUpdate, setPendingImageUpdate] = useState<string | null>(null);

  // Function to get the current organization ID
  const getCurrentOrganizationId = (): string => {
    // Use the organization ID from the organization store
    if (selectedOrganizationId) {
      console.log('[KonvaSideChat] Using organization ID from store:', selectedOrganizationId);
      return selectedOrganizationId;
    }

    console.log('[KonvaSideChat] No organization ID found in store');
    throw new Error('No organization ID available. Please select an organization.');
  };

  // Handle image updates outside of render
  useEffect(() => {
    if (pendingImageUpdate && onImageEdit) {
      console.log('[KonvaSideChat] Applying pending image update:', pendingImageUpdate);
      onImageEdit(pendingImageUpdate);
      setPendingImageUpdate(null);
    }
  }, [pendingImageUpdate, onImageEdit]);

  // Initialize active image from props
  useEffect(() => {
    setActiveImage(image.src);
  }, [image.src]);

  // Load chat history or analyze the image when the component mounts
  useEffect(() => {
    loadChatHistory();
  }, []);

  // Function to load chat history from the database
  const loadChatHistory = async () => {
    setIsLoadingHistory(true);

    // Check if we have a valid organization ID before attempting to load chat history
    try {
      // This will throw an error if no organization ID is available
      getCurrentOrganizationId();
    } catch (error) {
      console.error('[KonvaSideChat] Cannot load chat history without organization ID:', error);
      // Fallback to local analysis without persistence
      setIsLoadingHistory(false);
      analyzeImage();
      return; // Exit early since we can't load chat history without an organization ID
    }

    try {
      // Try to fetch existing chat history from the database
      const response = await api.get('/api/imagechat/get', {
        params: { imageId },
      });

      if (response.data && response.data.chat) {
        const chat = response.data.chat;

        // Set the chat ID
        setImageChatId(chat.id);

        // Convert database messages to the format expected by the component
        const formattedMessages = chat.messages.map((msg: any) => ({
          text: msg.content,
          isUser: msg.role === 'user',
          timestamp: new Date(msg.createdAt).getTime(),
          isLoading: false,
          isImage: msg.imageVersionId !== null,
          isActive: msg.imageVersionId
            ? chat.imageHistory.find((v: any) => v.id === msg.imageVersionId)?.imageUrl === activeImage
            : false,
        }));

        // Set messages
        setMessages(
          formattedMessages.length > 0
            ? formattedMessages
            : [{ text: 'Analyzing image details...', isUser: false, timestamp: Date.now(), isLoading: true }]
        );

        // Set image history
        const historyUrls = chat.imageHistory.map((version: any) => version.imageUrl);
        if (historyUrls.length > 0) {
          setImageHistory(historyUrls);

          // Set the active image to the most recent one
          const latestImage = historyUrls[historyUrls.length - 1];
          setActiveImage(latestImage);

          // Update the image on canvas if it's different from the current one
          if (latestImage !== image.src && onImageEdit) {
            onImageEdit(latestImage);
          }
        } else {
          setImageHistory([image.src]);
        }

        // Set image analysis if available
        if (chat.imageAnalysis) {
          console.log('[KonvaSideChat] Using existing image analysis from database');
          setImageAnalysis(chat.imageAnalysis);
          setIsAnalyzing(false);

          // Check if we already have an analysis message in the formatted messages
          const hasAnalysisMessage = formattedMessages.some(
            (msg: ChatMessage) => !msg.isUser && msg.text.includes('suggestions for editing the image')
          );

          // If we don't have an analysis message but we have analysis data, add it to the messages
          if (!hasAnalysisMessage && chat.imageAnalysis.suggestedEdits) {
            const analysisMessage = {
              text: `Here are two suggestions for editing the image:\n\n1. ${chat.imageAnalysis.suggestedEdits.suggestion1}\n\n2. ${chat.imageAnalysis.suggestedEdits.suggestion2}`,
              isUser: false,
              timestamp: Date.now() - 1000, // Make it appear before any other messages
            };

            // Add the analysis message at the beginning of the messages array
            setMessages((prev) => [analysisMessage, ...prev.filter((msg) => !msg.isLoading)]);
          }
        } else {
          // Analyze the image if no analysis is available
          console.log('[KonvaSideChat] No existing analysis found, analyzing image');
          analyzeImage();
        }
      } else {
        // No existing chat found, create a new one
        createNewChat();
      }
    } catch (error) {
      console.error('[KonvaSideChat] Error loading chat history:', error);
      // If there's an error, create a new chat
      createNewChat();
    } finally {
      setIsLoadingHistory(false);
    }
  };

  // Function to create a new chat in the database
  const createNewChat = async () => {
    try {
      // Get the organization ID, which will throw an error if not available
      let organizationId;
      try {
        organizationId = getCurrentOrganizationId();
      } catch (error) {
        console.error('[KonvaSideChat] No valid organization ID available:', error);
        // Fallback to local analysis without persistence
        analyzeImage();
        return; // Exit early since we can't create a chat without an organization ID
      }

      console.log('[KonvaSideChat] Creating new chat with organization ID:', organizationId);

      const response = await api.post('/api/imagechat/create', {
        imageId,
        imageUrl: image.src,
        organizationId,
      });

      if (response.data && response.data.chat) {
        const chatId = response.data.chat.id;
        setImageChatId(chatId);

        // Check if the chat already has analysis
        if (response.data.chat.imageAnalysis) {
          console.log('[KonvaSideChat] Chat already has analysis, using existing analysis');
          setImageAnalysis(response.data.chat.imageAnalysis);
          setIsAnalyzing(false);

          // Create the message text from the existing analysis
          const analysis = response.data.chat.imageAnalysis;
          const messageText = `Here are two suggestions for editing the image:\n\n1. ${analysis.suggestedEdits.suggestion1}\n\n2. ${analysis.suggestedEdits.suggestion2}`;

          // Update the message with suggestions
          setMessages([
            {
              text: messageText,
              isUser: false,
              timestamp: Date.now(),
            },
          ]);

          // Check if the chat already has messages
          if (response.data.chat.messages && response.data.chat.messages.length > 0) {
            console.log('[KonvaSideChat] Chat already has messages, using existing messages');

            // Convert database messages to the format expected by the component
            const formattedMessages = response.data.chat.messages.map((msg: any) => ({
              text: msg.content,
              isUser: msg.role === 'user',
              timestamp: new Date(msg.createdAt).getTime(),
              isLoading: false,
              isImage: msg.imageVersionId !== null,
              isActive: msg.imageVersionId
                ? response.data.chat.imageHistory.find((v: any) => v.id === msg.imageVersionId)?.imageUrl ===
                  activeImage
                : false,
            }));

            // Set messages, keeping the analysis message at the top
            if (formattedMessages.length > 0) {
              setMessages(formattedMessages);
            }
          }
        } else {
          // Start image analysis for the new chat
          console.log('[KonvaSideChat] New chat created, starting image analysis');
          analyzeImage();
        }
      } else {
        console.error('[KonvaSideChat] Failed to create new chat');
        // Fallback to local analysis without persistence
        analyzeImage();
      }
    } catch (error) {
      console.error('[KonvaSideChat] Error creating new chat:', error);
      // Fallback to local analysis without persistence
      analyzeImage();
    }
  };

  // Function to save a message to the database
  const saveMessage = async (content: string, role: string, imageVersionId?: string) => {
    if (!imageChatId) return;

    try {
      await api.post('/api/imagechat/addMessage', {
        imageChatId,
        content,
        role,
        imageVersionId,
      });
    } catch (error) {
      console.error('[KonvaSideChat] Error saving message:', error);
    }
  };

  // Function to save an image version to the database
  const saveImageVersion = async (imageUrl: string) => {
    if (!imageChatId) return null;

    try {
      const response = await api.post('/api/imagechat/addImageVersion', {
        imageChatId,
        imageUrl,
      });

      if (response.data && response.data.imageVersion) {
        return response.data.imageVersion.id;
      }
      return null;
    } catch (error) {
      console.error('[KonvaSideChat] Error saving image version:', error);
      return null;
    }
  };

  // Function to analyze the image when the component mounts
  const analyzeImage = async () => {
    console.log('[KonvaSideChat] Starting image analysis');
    try {
      // Prepare the prompt for image analysis
      const analysisPrompt = `Analyze this image in extreme detail. Describe what you see, including colors, objects, composition, and style. Then suggest two specific edits that could enhance this image.

Please note the aspect ratio of the image (square, landscape, or portrait) in your analysis, as this will be maintained in any edits unless specifically requested otherwise.

Format your response using Markdown:
- Use **bold** for important elements
- Use *italic* for emphasis
- Use bullet points or numbered lists for suggestions
- Use headings for sections (## Heading)
- Use code blocks for technical details if needed`;

      // Call the API to analyze the image
      const response = await api.post('/api/agents/image-edit-chat', {
        userId: 1,
        message: analysisPrompt,
        imageUrl: image.src,
        isFirstMessage: true,
        conversationHistory: [],
        response_format: {
          type: 'json_schema',
          json_schema: {
            name: 'imageAnalysis',
            strict: true,
            schema: {
              type: 'object',
              properties: {
                description: {
                  type: 'string',
                  description: 'Detailed description of the image, including colors, objects, composition, and style',
                },
                suggestedEdits: {
                  type: 'object',
                  properties: {
                    suggestion1: {
                      type: 'string',
                      description: 'First specific edit suggestion that could enhance the image',
                    },
                    suggestion2: {
                      type: 'string',
                      description: 'Second specific edit suggestion that could enhance the image',
                    },
                  },
                  required: ['suggestion1', 'suggestion2'],
                  additionalProperties: false,
                },
              },
              required: ['description', 'suggestedEdits'],
              additionalProperties: false,
            },
          },
        },
      });

      // Parse the JSON response
      let analysisResult: ImageAnalysis;
      try {
        // Try to parse the JSON response
        analysisResult = JSON.parse(response.data.message);
      } catch (parseError) {
        console.error('Error parsing JSON response:', parseError);
        // Fallback to a simple structure if JSON parsing fails
        analysisResult = {
          description: "I couldn't analyze this image in detail.",
          suggestedEdits: {
            suggestion1: 'Try adjusting the colors or lighting.',
            suggestion2: 'Consider cropping or resizing the image.',
          },
        };
      }

      // Store the analysis result
      setImageAnalysis(analysisResult);

      // Create the message text with Markdown formatting
      const messageText = `## Image Analysis Suggestions

I've analyzed this image and identified its key characteristics. When editing, I'll maintain the original aspect ratio unless you specifically request a change.

Here are two suggestions for editing the image:

1. **${analysisResult.suggestedEdits.suggestion1}**

2. **${analysisResult.suggestedEdits.suggestion2}**`;

      // Update the message with suggestions
      setMessages([
        {
          text: messageText,
          isUser: false,
          timestamp: Date.now(),
        },
      ]);

      // Save the analysis to the database if we have a chat ID
      if (imageChatId) {
        console.log('[KonvaSideChat] Saving image analysis to database');
        try {
          // First update the image chat with the analysis
          await api.post('/api/imagechat/update', {
            imageChatId,
            imageAnalysis: analysisResult,
          });

          // Then save the analysis message
          await saveMessage(messageText, 'assistant');
          console.log('[KonvaSideChat] Analysis message saved to database');
        } catch (error) {
          console.error('[KonvaSideChat] Error saving image analysis:', error);
        }
      } else {
        console.log('[KonvaSideChat] No chat ID available, creating new chat with analysis');
        // If we don't have a chat ID yet, create a new chat with the analysis
        try {
          // Get the organization ID
          const organizationId = getCurrentOrganizationId();

          // Create a new chat with the analysis
          const response = await api.post('/api/imagechat/create', {
            imageId,
            imageUrl: image.src,
            organizationId,
          });

          if (response.data && response.data.chat) {
            const newChatId = response.data.chat.id;
            setImageChatId(newChatId);

            // Update the new chat with the analysis
            await api.post('/api/imagechat/update', {
              imageChatId: newChatId,
              imageAnalysis: analysisResult,
            });

            // Save the analysis message
            await api.post('/api/imagechat/addMessage', {
              imageChatId: newChatId,
              content: messageText,
              role: 'assistant',
            });

            console.log('[KonvaSideChat] Created new chat with analysis:', newChatId);
          }
        } catch (error) {
          console.error('[KonvaSideChat] Error creating new chat with analysis:', error);
        }
      }

      // Update analysis status
      setIsAnalyzing(false);
    } catch (error) {
      console.error('Error analyzing image:', error);

      // Error message
      const errorMessage = "I couldn't analyze this image. Please try describing what you'd like to edit.";

      // Update with error message
      setMessages([
        {
          text: errorMessage,
          isUser: false,
          timestamp: Date.now(),
        },
      ]);

      // Save the error message to the database
      if (imageChatId) {
        try {
          await saveMessage(errorMessage, 'assistant');
        } catch (dbError) {
          console.error('[KonvaSideChat] Error saving error message:', dbError);
        }
      }

      setIsAnalyzing(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  // Image history functionality has been moved to the ImageHistoryModal component

  const processRequest = async (request: string) => {
    if (isProcessing || isAnalyzing) return;

    // Create user message
    const userMessage = {
      text: request,
      isUser: true,
      timestamp: Date.now(),
    };

    // Add user message to UI
    setMessages((prev) => [...prev, userMessage]);

    // Save user message to database
    if (imageChatId) {
      try {
        await saveMessage(request, 'user');
      } catch (error) {
        console.error('[KonvaSideChat] Error saving user message:', error);
      }
    }

    // Add loading message
    setIsProcessing(true);
    setMessages((prev) => [
      ...prev,
      {
        text: `Processing your request...`,
        isUser: false,
        isLoading: true,
        timestamp: Date.now(),
      },
    ]);

    try {
      // Convert messages to the format expected by the API
      // Skip the loading message at the end
      const conversationHistory = messages
        .filter((msg) => !msg.isLoading && !msg.isImage) // Skip loading messages and images
        .map((msg) => ({
          role: msg.isUser ? 'user' : 'assistant',
          content: msg.text,
        }));

      console.log(`[KonvaSideChat] Sending conversation history with ${conversationHistory.length} messages`);

      // Include the image description and Markdown formatting instructions in the request
      const enhancedRequest = imageAnalysis
        ? `[Image description: ${imageAnalysis.description}]\n\n${request}\n\nRemember to maintain the original aspect ratio of the image in any edits unless I specifically request a change.\n\nPlease format your response using Markdown:
- Use **bold** for important elements
- Use *italic* for emphasis
- Use bullet points or numbered lists for steps or options
- Use headings for sections (## Heading)
- Use code blocks for technical details if needed`
        : `${request}\n\nRemember to maintain the original aspect ratio of the image in any edits unless I specifically request a change.\n\nPlease format your response using Markdown:
- Use **bold** for important elements
- Use *italic* for emphasis
- Use bullet points or numbered lists for steps or options
- Use headings for sections (## Heading)
- Use code blocks for technical details if needed`;

      // Get image dimensions
      const imageDimensions = { width: image.width, height: image.height };
      console.log(`[KonvaSideChat] Sending image dimensions: ${imageDimensions.width}x${imageDimensions.height}`);

      // 🧠 MIGRATED: Stub mask functionality for modular architecture
      // TODO: Replace with modular mask state when available
      const hasMask = false; // Simplified for now

      if (hasMask) {
        console.log(`[KonvaSideChat] Found mask for image ${imageId}, including in request`);
      }

      // Call our image edit chat API with the correct path
      const response = await api.post('/api/agents/image-edit-chat', {
        userId: 1, // You might want to get the actual user ID
        message: enhancedRequest,
        imageUrl: activeImage, // Use the active image instead of the original
        maskUrl: hasMask ? null : undefined, // 🧠 MIGRATED: Stub mask URL for modular architecture
        isFirstMessage: false, // We've already sent the first message with the analysis
        conversationHistory,
        imageDimensions, // Add dimensions to the request
      });

      // Process the response
      const aiResponseText = response.data.message;
      let imageVersionId: string | null = null;

      // Update messages
      setMessages((prev) => {
        let newMessages = [...prev];
        // Remove loading indicator
        newMessages.pop();
        // Add AI response
        newMessages.push({
          text: aiResponseText,
          isUser: false,
          timestamp: Date.now(),
        });

        // If an image was edited, add it and update the canvas
        if (response.data.wasEdited) {
          const newImageUrl = response.data.editedImageUrl;
          setActiveImage(newImageUrl);

          // Add the image to history
          setImageHistory((prev) => [...prev, newImageUrl]);

          // Add the image message
          newMessages.push({
            text: newImageUrl,
            isUser: false,
            isImage: true,
            isActive: true, // Mark as active
            timestamp: Date.now(),
          });

          // Update all other images to be inactive
          newMessages = newMessages.map((msg) =>
            msg.isImage && msg.text !== newImageUrl ? { ...msg, isActive: false } : msg
          );

          // Queue the image update to be handled by the useEffect hook
          console.log(`[KonvaSideChat] Updating canvas with new image: ${newImageUrl}`);
          setPendingImageUpdate(newImageUrl);

          // 🧠 MIGRATED: Stub mask clearing for modular architecture
          // TODO: Replace with modular mask state when available
          console.log('[KonvaSideChat] Mask clearing stubbed for modular architecture');

          // Save the new image version to the database
          if (imageChatId) {
            saveImageVersion(newImageUrl)
              .then((id) => {
                imageVersionId = id;
                // Now save the AI response with the image version ID
                if (imageVersionId) {
                  saveMessage(aiResponseText, 'assistant', imageVersionId);
                } else {
                  saveMessage(aiResponseText, 'assistant');
                }
              })
              .catch((error) => {
                console.error('[KonvaSideChat] Error saving image version:', error);
                // Still save the message without the image version ID
                saveMessage(aiResponseText, 'assistant');
              });
          }
        } else {
          // Save the AI response without an image version
          if (imageChatId) {
            saveMessage(aiResponseText, 'assistant');
          }
        }

        return newMessages;
      });
    } catch (error: any) {
      console.error('Error processing image edit request:', error);

      // Get detailed error information
      const errorMessage =
        error?.response?.data?.error ||
        error?.message ||
        'Sorry, I encountered an error while processing your request.';

      console.log('Error details:', {
        status: error?.response?.status,
        statusText: error?.response?.statusText,
        data: error?.response?.data,
        message: error?.message,
      });

      // Update messages with error
      setMessages((prev) => {
        const newMessages = [...prev];
        // Remove loading indicator
        newMessages.pop();
        // Add error message
        newMessages.push({
          text: errorMessage,
          isUser: false,
          timestamp: Date.now(),
        });
        return newMessages;
      });

      // Save the error message to the database
      if (imageChatId) {
        saveMessage(errorMessage, 'assistant');
      }
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!inputValue.trim() || isProcessing) return;

    const userMessage = inputValue;
    setInputValue('');

    processRequest(userMessage);
  };

  // Note: We no longer need the handleApplyMask function here
  // Mask application is now handled directly in the MaskDrawingLayer component

  return (
    <Group x={x}>
      {/* We no longer need the separate MaskEditor component */}
      {/* It's been replaced by the MaskDrawingLayer in the main canvas */}

      <Html
        divProps={{
          style: {
            position: 'absolute',
            zIndex: 1000,
            display: 'block', // Always show chat since we're using the direct drawing approach
          },
        }}
      >
        <div className='refine-chat'>
          <div className='refine-chat-header'>
            <div className='refine-chat-logo'>
              <img src='/logo.svg' alt='Logo' className='refine-chat-logo-img' />
              <span>Refine Image</span>
            </div>
            <button className='refine-chat-close-btn' onClick={onClose}>
              <X size={20} />
            </button>
          </div>

          {/* No persistent preview area needed since the current version is visible on the canvas */}

          <div className='refine-chat-content'>
            <div className='refine-chat-messages'>
              {messages.map((message, index) => (
                <div key={index} className={`refine-chat-message ${message.isUser ? 'user-message' : 'ai-message'}`}>
                  <div className={`message-bubble ${message.isLoading ? 'loading-message' : ''}`}>
                    {message.isImage ? (
                      <div>
                        <p>Image edited successfully.</p>
                        <p className='text-xs text-gray-500 mt-1'>View edit history to see all versions.</p>
                      </div>
                    ) : (
                      <div className='markdown-content'>
                        <ReactMarkdown>{message.text}</ReactMarkdown>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <form className='refine-chat-input-container' onSubmit={handleSubmit}>
            <div className='paintbrush-icon' onClick={onMaskingModeChange} title='Open mask editor'>
              <Paintbrush size={18} color='#2b4322' />
            </div>
            <input
              type='text'
              className='refine-chat-input'
              placeholder={isAnalyzing ? 'Analyzing image...' : ''}
              value={inputValue}
              onChange={handleInputChange}
              disabled={isProcessing || isAnalyzing}
            />
            <button
              type='submit'
              className='refine-chat-send-btn'
              disabled={!inputValue.trim() || isProcessing || isAnalyzing}
            >
              <ArrowRight size={20} />
            </button>
          </form>
        </div>
      </Html>
    </Group>
  );
};

export default KonvaSideChat;
