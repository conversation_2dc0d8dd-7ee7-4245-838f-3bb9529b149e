/**
 * 🖼️ Konva Image Menu - MODULAR VERSION
 * @description Image context menu component for modular canvas
 * @responsibility Provide image editing and management options
 * @ai_context Migrated from client/canvas/components/KonvaImageMenu.tsx to modular structure
 */

import React, { memo, useCallback, useMemo, useState } from 'react';
import { Group } from 'react-konva';
import { Html } from 'react-konva-utils';
import { Pencil, MessageCircle, Share2, MoreHorizontal, Trash, Copy, ArrowUp, ArrowDown, History } from 'lucide-react';
// 🧠 MIGRATED: Using modular types and state
import { useToolDomainStore, DomainCanvasTool } from '../../../../domain/state';

interface KonvaImageMenuProps {
  imageId: string;
  width: number;
  onEdit: () => void;
  onComment: (imageId: string) => void;
  onShare: (imageId: string) => void;
  onDelete: () => void;
  onDuplicate: (imageId: string) => void;
  onUseAsReference: (imageId: string) => void;
  onBringForward: (imageId: string) => void;
  onSendBackward: (imageId: string) => void;
  onShowHistory?: (imageId: string) => void; // New callback for showing edit history
}

const KonvaImageMenu: React.FC<KonvaImageMenuProps> = ({
  imageId,
  width,
  onEdit,
  onComment,
  onShare,
  onDelete,
  onDuplicate,
  onUseAsReference,
  onBringForward,
  onSendBackward,
  onShowHistory,
}) => {
  const [showMoreOptions, setShowMoreOptions] = useState(false);

  // 🧠 Using domain store instead of old canvas store
  const { activeTool } = useToolDomainStore();

  // Don't render the menu when in mask drawing mode
  if (activeTool === DomainCanvasTool.MASK) {
    return null;
  }

  // Position the menu at the top-right of the image
  const menuX = useMemo(() => width / 2 + 5, [width]);
  const menuY = -50;

  const toggleMoreOptions = () => setShowMoreOptions(!showMoreOptions);

  const handleMoreOptions = useCallback(() => {
    setShowMoreOptions(false);
  }, [showMoreOptions]);

  return (
    <Group x={menuX} y={menuY}>
      <Html
        divProps={{
          style: {
            position: 'absolute',
            zIndex: 1000,
          },
        }}
      >
        <div className='bg-white shadow-md rounded-md flex items-center p-1 space-x-1'>
          {/* Edit/Refine with AI */}
          <button
            className='p-2 hover:bg-gray-100 rounded-md transition-colors flex flex-col items-center'
            onClick={() => {
              onEdit();
              handleMoreOptions();
            }}
            title='Edit/Refine with AI'
          >
            <Pencil size={18} />
          </button>

          {/* History */}
          {onShowHistory && (
            <button
              className='p-2 hover:bg-gray-100 rounded-md transition-colors flex flex-col items-center'
              onClick={() => {
                onShowHistory(imageId);
                handleMoreOptions();
              }}
              title='Edit History'
            >
              <History size={18} />
            </button>
          )}

          {/* Comment */}
          <button
            className='p-2 hover:bg-gray-100 rounded-md transition-colors flex flex-col items-center'
            onClick={() => {
              onComment(imageId);
              handleMoreOptions();
            }}
            title='Add Comment'
          >
            <MessageCircle size={18} />
          </button>

          {/* Share */}
          <button
            className='p-2 hover:bg-gray-100 rounded-md transition-colors flex flex-col items-center'
            onClick={() => {
              onShare(imageId);
              handleMoreOptions();
            }}
            title='Share'
          >
            <Share2 size={18} />
          </button>

          {/* More Options */}
          <div className='relative'>
            <button
              className='p-2 hover:bg-gray-100 rounded-md transition-colors flex flex-col items-center'
              onClick={toggleMoreOptions}
              title='More Options'
            >
              <MoreHorizontal size={18} />
            </button>

            {/* Dropdown Menu */}
            {showMoreOptions && (
              <div className='absolute right-0 mt-1 bg-white shadow-lg rounded-md overflow-hidden w-48 z-50'>
                <ul className='py-1'>
                  <li>
                    <button
                      className='w-full px-3 py-2 text-left flex items-center gap-2 hover:bg-gray-100 transition-colors text-sm'
                      onClick={() => {
                        onDelete();
                        handleMoreOptions();
                      }}
                    >
                      <Trash size={16} />
                      <span>Delete</span>
                    </button>
                  </li>
                  <li>
                    <button
                      className='w-full px-3 py-2 text-left flex items-center gap-2 hover:bg-gray-100 transition-colors text-sm'
                      onClick={() => {
                        onDuplicate(imageId);
                        handleMoreOptions();
                      }}
                    >
                      <Copy size={16} />
                      <span>Duplicate</span>
                    </button>
                  </li>
                  <li>
                    <button
                      className='w-full px-3 py-2 text-left flex items-center gap-2 hover:bg-gray-100 transition-colors text-sm'
                      onClick={() => {
                        onUseAsReference(imageId);
                        handleMoreOptions();
                      }}
                    >
                      <Share2 size={16} />
                      <span>Use as Reference</span>
                    </button>
                  </li>
                  <li className='border-t border-gray-200 mt-1 pt-1'>
                    <button
                      className='w-full px-3 py-2 text-left flex items-center gap-2 hover:bg-gray-100 transition-colors text-sm'
                      onClick={() => {
                        onBringForward(imageId);
                        handleMoreOptions();
                      }}
                    >
                      <ArrowUp size={16} />
                      <span>Bring Forward</span>
                    </button>
                  </li>
                  <li>
                    <button
                      className='w-full px-3 py-2 text-left flex items-center gap-2 hover:bg-gray-100 transition-colors text-sm'
                      onClick={() => {
                        onSendBackward(imageId);
                        handleMoreOptions();
                      }}
                    >
                      <ArrowDown size={16} />
                      <span>Send Backward</span>
                    </button>
                  </li>
                </ul>
              </div>
            )}
          </div>
        </div>
      </Html>
    </Group>
  );
};

export default memo(KonvaImageMenu);
