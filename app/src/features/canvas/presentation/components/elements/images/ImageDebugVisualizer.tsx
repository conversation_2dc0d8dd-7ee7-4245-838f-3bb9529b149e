/**
 * 🖼️ Image Debug Visualizer - MODULAR VERSION
 * @description Debug visualization component for modular canvas
 * @responsibility Show debug information for images
 * @ai_context Migrated from client/canvas/components/ImageDebugVisualizer.tsx to modular structure
 */

import React from 'react';
import { Rect, Text, Circle, Group } from 'react-konva';
// 🧠 MIGRATED: Using modular types and state
import { ImageObject } from '../../../../domain/types';
import { useElementDomainStore, useToolDomainStore } from '../../../../domain/state';

interface ImageDebugVisualizerProps {
  image: ImageObject;
}

/**
 * ImageDebugVisualizer component renders debug visualizations for a specific image
 * including reference star and mask visualization.
 */
const ImageDebugVisualizer: React.FC<ImageDebugVisualizerProps> = ({ image }) => {
  // 🧠 Using domain stores instead of old canvas store
  const { selectedIds } = useElementDomainStore();
  const { scale } = useToolDomainStore();

  // 🚨 TODO: Add debug settings to domain state
  // For now, use simple debug mode (can be enhanced later)
  const debug = {
    enabled: true, // Always show debug for now
    showBorders: true,
    showPositions: true,
    showIds: true,
    borderColors: {
      selected: '#00ff00',
      mask: '#ff0000',
      image: '#0000ff',
    },
  };

  // 🚨 TODO: Add mask drawing to domain state
  const maskDrawing = {
    targetImageId: null,
    points: [],
    tool: 'brush',
    brushSize: 10,
  };

  // If debug mode is not enabled, don't render anything
  if (!debug.enabled) {
    return null;
  }

  // Determine if this image is the target for mask drawing
  const isMaskTarget = maskDrawing.targetImageId === image.id;

  // Determine if this image is selected
  const isSelected = selectedIds.includes(image.id);

  // Determine if this image is a reference image
  const isReference = image.isReference;

  // Use Group to contain all debug elements
  return (
    <Group>
      {/* Border around the image */}
      {debug.showBorders && (
        <Rect
          key={`debug-border-${image.id}`}
          x={image.x}
          y={image.y}
          width={image.width}
          height={image.height}
          stroke={
            isSelected
              ? debug.borderColors.selected
              : isMaskTarget
                ? debug.borderColors.mask
                : isReference
                  ? '#FFD700' // Gold color for reference images
                  : debug.borderColors.image
          }
          strokeWidth={2 / scale}
          dash={[10 / scale, 5 / scale]}
          perfectDrawEnabled={false}
          listening={false}
          rotation={image.rotation || 0}
        />
      )}

      {/* Position information */}
      {debug.showPositions && (
        <Text
          key={`debug-position-${image.id}`}
          x={image.x}
          y={image.y - 20 / scale}
          text={`x: ${Math.round(image.x)}, y: ${Math.round(image.y)}`}
          fontSize={14 / scale}
          fill={
            isMaskTarget
              ? debug.borderColors.mask
              : isReference
                ? '#FFD700' // Gold color for reference images
                : debug.borderColors.image
          }
          listening={false}
        />
      )}

      {/* ID information */}
      {debug.showIds && (
        <Text
          key={`debug-id-${image.id}`}
          x={image.x}
          y={image.y + image.height + 5 / scale}
          text={`id: ${image.id.substring(0, 8)}...${isReference ? ' (Reference)' : ''}`}
          fontSize={14 / scale}
          fill={
            isMaskTarget
              ? debug.borderColors.mask
              : isReference
                ? '#FFD700' // Gold color for reference images
                : debug.borderColors.image
          }
          listening={false}
        />
      )}

      {/* Reference star visualization */}
      {isReference && debug.showBorders && (
        <>
          {/* Circle around the star position */}
          <Circle
            key={`debug-star-${image.id}`}
            x={image.x + image.width - 20}
            y={image.y + 20}
            radius={25 / scale}
            stroke='#FFD700' // Gold color
            strokeWidth={2 / scale}
            dash={[5 / scale, 5 / scale]}
            perfectDrawEnabled={false}
            listening={false}
          />

          {/* Label for the star */}
          <Text
            key={`debug-star-label-${image.id}`}
            x={image.x + image.width - 60}
            y={image.y + 40}
            text='Reference Star'
            fontSize={12 / scale}
            fill='#FFD700' // Gold color
            listening={false}
          />
        </>
      )}

      {/* Mask visualization */}
      {isMaskTarget && maskDrawing.points.length > 0 && debug.showBorders && (
        <Text
          key={`debug-mask-label-${image.id}`}
          x={image.x}
          y={image.y - 40 / scale}
          text={`Mask: ${maskDrawing.tool} | Points: ${maskDrawing.points.filter((p) => p !== null).length} | Brush: ${maskDrawing.brushSize}`}
          fontSize={12 / scale}
          fill={debug.borderColors.mask}
          stroke='#000000'
          strokeWidth={0.5 / scale}
          listening={false}
        />
      )}
    </Group>
  );
};

export default ImageDebugVisualizer;
