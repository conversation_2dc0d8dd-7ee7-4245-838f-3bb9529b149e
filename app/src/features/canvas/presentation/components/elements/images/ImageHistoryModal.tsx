import React from 'react';
import { X } from 'lucide-react';
import { Group } from 'react-konva';
import { Html } from 'react-konva-utils';

interface ImageHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageHistory: string[];
  onSelectImage: (imageUrl: string) => void;
  currentImageUrl: string;
  x: number;
  y: number;
}

const ImageHistoryModal: React.FC<ImageHistoryModalProps> = ({
  isOpen,
  onClose,
  imageHistory,
  onSelectImage,
  currentImageUrl,
  x,
  y,
}) => {
  if (!isOpen) return null;

  return (
    <Group x={x} y={y}>
      <Html
        divProps={{
          style: {
            position: 'absolute',
            zIndex: 1000,
          },
        }}
      >
        <div className='w-[350px] bg-white rounded-[12px] flex flex-col shadow-[0_4px_12px_rgba(0,0,0,0.1)] overflow-hidden border border-[#2b4322] min-h-[400px] max-h-[700px]'>
          <div className='flex justify-between items-center p-[12px_16px] border-b border-[#e5e7eb]'>
            <h2 className='font-display text-[18px] font-semibold text-[#374151]'>Image Edit History</h2>
            <button
              onClick={onClose}
              className='bg-none border-none cursor-pointer text-[#4b5563] flex items-center justify-center p-1 rounded-md hover:bg-[#f3f4f6]'
            >
              <X size={20} />
            </button>
          </div>

          <div className='flex-1 overflow-y-auto p-4 min-h-[400px] max-h-[400px] border-b border-[#e5e7eb]'>
            {imageHistory.length === 0 ? (
              <p className='text-center text-gray-500'>No edit history available</p>
            ) : (
              <div className='grid grid-cols-4 gap-2'>
                {imageHistory.map((imageUrl, index) => (
                  <div
                    key={index}
                    className={`relative rounded-md overflow-hidden ${
                      imageUrl === currentImageUrl ? 'border-[2px] border-[#2b4322]' : 'border border-gray-300'
                    } hover:border-[#2b4322] hover:border-[2px] transition-colors cursor-pointer bg-white aspect-square`}
                    onClick={() => onSelectImage(imageUrl)}
                  >
                    <img src={imageUrl} alt={`Edit version ${index}`} className='w-full h-full object-cover' />
                    <div className='absolute bottom-0 left-0 right-0 bg-[rgba(43,67,34,0.8)] text-white py-0.5 text-[10px] text-center'>
                      {index === 0 ? 'Original' : `v${index}`}
                      {imageUrl === currentImageUrl && ' (Current)'}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className='p-[12px_16px] flex justify-end bg-white'>
            <button
              onClick={onClose}
              className='bg-[#2b4322] text-white border-none rounded-md px-4 py-2 cursor-pointer hover:bg-[#243a1c] transition-colors'
            >
              Close
            </button>
          </div>
        </div>
      </Html>
    </Group>
  );
};

export default ImageHistoryModal;
