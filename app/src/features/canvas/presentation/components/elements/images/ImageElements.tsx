/**
 * 🖼️ Modular Image Elements Component
 *
 * @description Handles rendering and interaction of image elements on canvas
 * @responsibility Image rendering, selection, and updates
 * @dependencies URLImage component, canvas store
 * @ai_context MIGRATED from inline canvas code to proper modular component
 */

import React, { useCallback } from 'react';
import URLImage from './URLImage'; // 🏗️ Using our own modular copy!
import { useElementDomainStore } from '../../../../domain/state'; // 🧠 Using pure domain state!
import { ElementService } from '../../../../domain/services'; // 🧠 Using domain service!
import { useShallow } from 'zustand/react/shallow';

/**
 * 🖼️ Image Elements Props
 */
export interface ImageElementsProps {
  className?: string;
}

/**
 * 🖼️ Image Elements Component
 * @ai_context Modular component for rendering all image elements
 */
export const ImageElements: React.FC<ImageElementsProps> = ({ className }) => {
  // Get canvas state from pure domain store with shallow comparison
  const { images, selectedIds } = useElementDomainStore(
    useShallow((state) => ({
      images: state.images,
      selectedIds: state.selectedIds,
    }))
  );

  // Debug: Log when component renders and what images it has
  console.log('[ImageElements] Rendering with:', {
    imageCount: images?.length || 0,
    imageIds: images?.map((img) => img.id) || [],
    selectedIds: selectedIds || [],
    timestamp: new Date().toISOString(),
  });

  // // Debug: Log detailed image data
  // if (images && images.length > 0) {
  //   console.log('[ImageElements] Image details:', images);
  // }

  /**
   * 🎯 Handle image selection - SelectionManager handles the actual toggle logic
   */
  const handleImageSelect = useCallback((imageId: string) => {
    console.log('[ImageElements] Image clicked:', imageId, '- SelectionManager will handle selection');
    // SelectionManager handles the actual selection/deselection logic
    // This is just for logging/debugging purposes
  }, []);

  /**
   * 🔄 Handle image changes - MEMOIZED to prevent infinite loops
   */
  const handleImageChange = useCallback((newAttrs: any) => {
    console.log('[ImageElements] Image changed:', newAttrs);
    // 🧠 Using domain service instead of direct canvas-store call
    ElementService.images.update(newAttrs.id, newAttrs);
  }, []); // 🔧 FIX: Memoized with empty deps to prevent recreation

  // Return null if no images
  if (!images || images.length === 0) {
    return null;
  }

  return (
    <>
      {images.map((image: any) => {
        // FAIL FAST: Require image dimensions - no fallbacks to prevent chaos
        if (!image.width || !image.height) {
          const error = `[ImageElements] ❌ FAIL FAST: width and height are required for image ${image.id}. No fallbacks allowed.`;
          console.error(error);
          throw new Error(error);
        }

        // 🔧 Map domain image format to URLImage expected props
        const imageProperties = {
          id: image.id,
          url: image.url,
          originalName: image.originalName || 'Generated Image',
          size: { width: image.width, height: image.height }, // Use actual image dimensions
          isReference: image.isReference || false,
          cropX: image.cropX,
          cropY: image.cropY,
          cropWidth: image.cropWidth,
          cropHeight: image.cropHeight,
        };

        return (
          <URLImage
            key={image.id}
            x={image.position?.x || 0}
            y={image.position?.y || 0}
            rotation={image.rotation || 0}
            scaleX={image.scaleX || 1}
            scaleY={image.scaleY || 1}
            draggable={true}
            properties={imageProperties}
            isSelected={selectedIds.includes(image.id)}
            zIndex={image.zIndex || 0}
            // SelectionManager handles all click events - no need for onClick/onTap here
            onDragStart={() => handleImageSelect(image.id)}
            onDragEnd={(e) => {
              const target = e.target;
              handleImageChange({
                id: image.id,
                position: { x: target.x(), y: target.y() },
              });
            }}
            onTransformEnd={(e) => {
              const target = e.target;
              handleImageChange({
                id: image.id,
                position: { x: target.x(), y: target.y() },
                rotation: target.rotation(),
                scaleX: target.scaleX(),
                scaleY: target.scaleY(),
              });
            }}
          />
        );
      })}
    </>
  );
};

export default ImageElements;
