/**
 * 🖼️ URL Image Component - MODULAR VERSION
 * @description Image rendering component for modular canvas
 * @responsibility Handle image display, interaction, and editing
 * @ai_context Migrated from client/canvas/components/URLImage.tsx to modular structure
 */

import React, { useRef, useEffect, useState, memo } from 'react';
import { Image, Transformer, Star, Group } from 'react-konva';
import useImage from 'use-image';
import Konva from 'konva';
// 🧠 MIGRATED: Using modular types instead of client types
import { URLImageProps } from '../../../../domain/types';
// 🧠 MIGRATED: Using modular components instead of client components
import KonvaImageMenu from '../menus/KonvaImageMenu';
import KonvaSideChat from '../chat/KonvaSideChat';
import ImageHistoryModal from './ImageHistoryModal';
import ImageDebugVisualizer from './ImageDebugVisualizer';
import '../chat/KonvaSideChat.css';
// 🧠 MIGRATED: Using modular state instead of old canvas store
import { useCanvasToolbarStore } from '../../../../domain/state';
import { useShallow } from 'zustand/react/shallow';
// 🧠 MIGRATED: Using modular services for element operations
import { ElementService } from '../../../../domain/services';

// Component to handle image rendering with Konva
const URLImage: React.FC<URLImageProps> = memo(({ image, isSelected, onSelect, onChange }) => {
  // const imageRef = useRef<Konva.Image>(null);
  // const trRef = useRef<Konva.Transformer>(null);
  // const groupRef = useRef<Konva.Group>(null);
  // const [showSideChat, setShowSideChat] = useState(false);
  // const [showHistoryModal, setShowHistoryModal] = useState(false);
  // const [imageHistory, setImageHistory] = useState<string[]>([]);
  // // 🧠 MIGRATED: Using modular state instead of old canvas store
  // const { activeTool, debug } = useCanvasToolbarStore(
  //   useShallow((state) => ({
  //     activeTool: state.activeTool,
  //     debug: state.debug,
  //   }))
  // );
  // // TODO: Add maskDrawing to modular state when masking is migrated
  // const maskDrawing = { maskImageUrl: null, targetImageId: null, opacity: 0.5 };
  // const imageSrc = typeof image.src === 'object' && image.src !== null ? (image.src as any).src || '' : image.src;
  // const [currentImageUrl, setCurrentImageUrl] = useState(imageSrc);
  // useEffect(() => {
  //   setCurrentImageUrl(imageSrc);
  //   if (imageSrc && !imageHistory.includes(imageSrc)) {
  //     setImageHistory((prev) => {
  //       if (prev.length === 0 || !prev.includes(imageSrc)) {
  //         return [imageSrc, ...prev];
  //       }
  //       return prev;
  //     });
  //   }
  // }, [imageSrc, imageHistory]);
  // const [img, status] = useImage(currentImageUrl);
  // const [maskImg] = useImage(maskDrawing.maskImageUrl || '');
  // useEffect(() => {
  //   if (groupRef.current) {
  //     console.log(`[IMAGE_PERSISTENCE_URLIMAGE] [URLImage ${image.id}] Props updated.`, {
  //       propX: image.x,
  //       propY: image.y,
  //       propWidth: image.width,
  //       propHeight: image.height,
  //       konvaNodeX: groupRef.current.x(),
  //       konvaNodeY: groupRef.current.y(),
  //       konvaNodeWidth: groupRef.current.width(),
  //       konvaNodeHeight: groupRef.current.height(),
  //       isSelected,
  //     });
  //   }
  // }, [image.x, image.y, image.width, image.height, image.id, isSelected]);
  // React.useEffect(() => {
  //   if (status !== 'loaded' || !img) return;
  //   // If the image has been cropped (cropWidth & cropHeight set) we should NOT auto-adjust its width/height
  //   // based on the *natural* dimensions of the source, because its visible aspect-ratio now intentionally
  //   // matches the crop window, not the full source image.  Running the adjustment logic in that case
  //   // causes an endless loop of width/height updates after every crop.
  //   if (image.cropWidth && image.cropHeight) {
  //     return; // Skip auto-resize when an explicit crop is present
  //   }
  //   const naturalW = (img instanceof HTMLImageElement ? img.naturalWidth : image.width) || image.width;
  //   const naturalH = (img instanceof HTMLImageElement ? img.naturalHeight : image.height) || image.height;
  //   if (!naturalW || !naturalH) return;
  //   const w = image.width;
  //   const h = image.height;
  //   if (Math.abs(w / h - naturalW / naturalH) < 0.001) return;
  //   let newW = w;
  //   let newH = h;
  //   if (w / h > naturalW / naturalH) {
  //     newW = Math.round((h * naturalW) / naturalH);
  //   } else {
  //     newH = Math.round((w * naturalH) / naturalW);
  //   }
  //   onChange({ ...image, width: newW, height: newH });
  // }, [status, img, image.id, image.src, image.cropWidth, image.cropHeight, onChange]); // 🔧 FIX: Only depend on properties that should trigger resize, not width/height
  // const handleEdit = (_imageId: string) => setShowSideChat((prevState) => !prevState);
  // const handleShowHistory = (_imageId: string) => setShowHistoryModal(true);
  // const handleCloseHistoryModal = () => setShowHistoryModal(false);
  // const handleSelectHistoryImage = (imageUrl: string) => {
  //   setCurrentImageUrl(imageUrl);
  //   onChange({ ...image, src: imageUrl });
  // };
  // const handleCloseSideChat = () => setShowSideChat(false);
  // const deselectImage = () => {
  //   if (onSelect) {
  //     // @ts-ignore
  //     onSelect(null);
  //   }
  //   if (trRef.current) {
  //     trRef.current.nodes([]);
  //     trRef.current.getLayer()?.batchDraw();
  //   }
  // };
  // const handleImageEdit = (newImageUrl: string) => {
  //   setCurrentImageUrl(newImageUrl);
  //   if (!imageHistory.includes(newImageUrl)) {
  //     setImageHistory((prev) => [newImageUrl, ...prev]);
  //   }
  //   onChange({ ...image, src: newImageUrl });
  // };
  // const handleComment = (imageId: string) =>
  //   window.dispatchEvent(new CustomEvent('commentImage', { detail: { imageId } }));
  // const handleShare = (imageId: string) => window.dispatchEvent(new CustomEvent('shareImage', { detail: { imageId } }));
  // const handleDelete = (imageId: string) =>
  //   window.dispatchEvent(new CustomEvent('removeImage', { detail: { imageId } }));
  // const handleDuplicate = (imageId: string) => {
  //   console.log('[URLImage] Duplicating image:', imageId);
  //   ElementService.images.duplicate(imageId);
  // };
  // const handleUseAsReference = (imageId: string) =>
  //   window.dispatchEvent(new CustomEvent('setImageAsReference', { detail: { imageId } }));
  // const handleBringForward = (imageId: string) =>
  //   window.dispatchEvent(new CustomEvent('bringForward', { detail: { imageId } }));
  // const handleSendBackward = (imageId: string) =>
  //   window.dispatchEvent(new CustomEvent('sendBackward', { detail: { imageId } }));
  // const handleContextMenu = (e: Konva.KonvaEventObject<MouseEvent>) => {
  //   e.evt.preventDefault();
  //   // Allow event to bubble to BaseScene's contextmenu listener
  //   // Konva's default behavior will bubble to stage if not stopped.
  //   // We are NOT calling e.evt.stopPropagation() or e.cancelBubble = true here anymore.
  //   console.log(`[URLImage ${image.id}] onContextMenu triggered. Letting event bubble to BaseScene.`);
  // };
  // useEffect(() => {
  //   const handleShowSideChatEvent = (event: CustomEvent) => {
  //     if (event.detail.imageId === image.id) setShowSideChat(true);
  //   };
  //   window.addEventListener('showSideChat', handleShowSideChatEvent as EventListener);
  //   return () => window.removeEventListener('showSideChat', handleShowSideChatEvent as EventListener);
  // }, [image.id]);
  // useEffect(() => {
  //   console.log(`[URLImage ${image.id}] Selection changed: isSelected=${isSelected}`);
  //   if (isSelected) {
  //     // Handle transformer if it exists (it's disabled for now)
  //     if (groupRef.current && trRef.current) {
  //       trRef.current.nodes([groupRef.current]);
  //       trRef.current.getLayer()?.batchDraw();
  //     }
  //     // Notify BaseScene about the selection
  //     console.log(`[URLImage ${image.id}] Dispatching selection event: true`);
  //     window.dispatchEvent(
  //       new CustomEvent('canvas:urlImageSelected', {
  //         detail: {
  //           imageId: image.id,
  //           isSelected: true,
  //         },
  //       })
  //     );
  //   } else {
  //     // Handle transformer if it exists (it's disabled for now)
  //     if (trRef.current) {
  //       trRef.current.nodes([]);
  //       trRef.current.getLayer()?.batchDraw();
  //     }
  //     // Notify BaseScene about the deselection
  //     console.log(`[URLImage ${image.id}] Dispatching selection event: false`);
  //     window.dispatchEvent(
  //       new CustomEvent('canvas:urlImageSelected', {
  //         detail: {
  //           imageId: image.id,
  //           isSelected: false,
  //         },
  //       })
  //     );
  //   }
  // }, [isSelected, image.id]);
  // const groupWidth = image.width;
  // const groupHeight = image.height;
  // return (
  //   <>
  //     {debug && <ImageDebugVisualizer image={image} />}
  //     <Group
  //       x={image.x}
  //       y={image.y}
  //       width={groupWidth}
  //       height={groupHeight}
  //       rotation={image.rotation}
  //       draggable={activeTool === 'hand' || activeTool === 'select'}
  //       id={image.id}
  //       onClick={onSelect}
  //       onTap={onSelect}
  //       onContextMenu={handleContextMenu}
  //       onDragEnd={(e: Konva.KonvaEventObject<DragEvent>) => {
  //         const node = e.target as Konva.Group;
  //         // Dispatch a custom event to signal the end of dragging
  //         window.dispatchEvent(
  //           new CustomEvent('canvas:elementDraggingEnd', {
  //             detail: {
  //               id: image.id,
  //               x: node.x(),
  //               y: node.y(),
  //               isDragging: false,
  //             },
  //           })
  //         );
  //       }}
  //       onDragMove={(e: Konva.KonvaEventObject<DragEvent>) => {
  //         // Store the initial position to help with drag calculations
  //         const node = e.target;
  //         // Store initial position attributes on the node itself
  //         node.setAttr('startX', node.x());
  //         node.setAttr('startY', node.y());
  //         // Also store stage info
  //         const stage = node.getStage();
  //         if (stage) {
  //           node.setAttr('stageStartScale', stage.scaleX());
  //           node.setAttr('stageStartX', stage.x());
  //           node.setAttr('stageStartY', stage.y());
  //         }
  //         // Dispatch a custom event for dragging to be consistent with BaseScene
  //         // This will be caught by the image-elements.tsx component
  //         window.dispatchEvent(
  //           new CustomEvent('canvas:elementDragging', {
  //             detail: {
  //               id: image.id,
  //               x: node.x(),
  //               y: node.y(),
  //               isDragging: true,
  //             },
  //           })
  //         );
  //       }}
  //       ref={groupRef}
  //     >
  //       {maskDrawing.maskImageUrl && maskDrawing.targetImageId === image.id && maskImg && (
  //         <Image
  //           image={maskImg}
  //           width={image.width}
  //           height={image.height}
  //           opacity={maskDrawing.opacity}
  //           listening={false}
  //         />
  //       )}
  //       <Image
  //         image={img}
  //         x={0}
  //         y={0}
  //         width={groupWidth}
  //         height={groupHeight}
  //         cropX={image.cropX}
  //         cropY={image.cropY}
  //         cropWidth={image.cropWidth}
  //         cropHeight={image.cropHeight}
  //         draggable={false}
  //         ref={imageRef}
  //         opacity={1}
  //       />
  //       {image.isReference && (
  //         <Star
  //           numPoints={5}
  //           innerRadius={10}
  //           outerRadius={20}
  //           fill='yellow'
  //           stroke='black'
  //           strokeWidth={1}
  //           x={image.width - 20}
  //           y={20}
  //           shadowColor='black'
  //           shadowBlur={5}
  //           shadowOffset={{ x: 2, y: 2 }}
  //           shadowOpacity={0.5}
  //         />
  //       )}
  //       {isSelected && (
  //         <KonvaImageMenu
  //           imageId={image.id}
  //           width={image.width}
  //           height={image.height}
  //           onEdit={handleEdit}
  //           onComment={handleComment}
  //           onShare={handleShare}
  //           onDelete={handleDelete}
  //           onDuplicate={handleDuplicate}
  //           onUseAsReference={handleUseAsReference}
  //           onBringForward={handleBringForward}
  //           onSendBackward={handleSendBackward}
  //           onShowHistory={handleShowHistory}
  //         />
  //       )}
  //       {isSelected && showSideChat && (
  //         <KonvaSideChat
  //           imageId={image.id}
  //           imageSrc={currentImageUrl}
  //           x={image.width + 20}
  //           y={0}
  //           onClose={handleCloseSideChat}
  //           onImageEdit={handleImageEdit}
  //           imageRef={imageRef}
  //           onDeselect={deselectImage}
  //         />
  //       )}
  //       {isSelected && showHistoryModal && (
  //         <ImageHistoryModal
  //           isOpen={true}
  //           onClose={handleCloseHistoryModal}
  //           imageHistory={imageHistory}
  //           onSelectImage={handleSelectHistoryImage}
  //           currentImageUrl={currentImageUrl}
  //           x={-370}
  //           y={0}
  //         />
  //       )}
  //     </Group>
  //     {/* URLImage transformer disabled - BaseScene ScaleTransformer handles image transformation */}
  //     {false && isSelected && activeTool !== 'mask' && groupRef.current && (
  //       <Transformer
  //         ref={trRef}
  //         boundBoxFunc={(oldBox, newBox) => {
  //           if (newBox.width < 5 || newBox.height < 5) return oldBox;
  //           return newBox;
  //         }}
  //         nodes={[groupRef.current]}
  //         onTransformEnd={() => {
  //           const node = groupRef.current;
  //           if (!node) return;
  //           const scaleX = node.scaleX();
  //           const scaleY = node.scaleY();
  //           const newAttrs = {
  //             ...image,
  //             x: node.x(),
  //             y: node.y(),
  //             width: Math.round(node.width() * scaleX),
  //             height: Math.round(node.height() * scaleY),
  //             rotation: node.rotation(),
  //           };
  //           onChange(newAttrs);
  //           node.scaleX(1);
  //           node.scaleY(1);
  //         }}
  //         borderStroke='#9EA581'
  //         borderStrokeWidth={2}
  //         anchorStroke='#9EA581'
  //         anchorFill='#9EA581'
  //         anchorStrokeWidth={1}
  //       />
  //     )}
  //   </>
  // );

  return null;
});

URLImage.displayName = 'URLImage';

export default URLImage;
