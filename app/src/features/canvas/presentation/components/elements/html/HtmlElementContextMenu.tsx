import React, { useLayoutEffect, useRef } from 'react';

interface HtmlElementContextMenuProps {
  isVisible: boolean;
  position: { x: number; y: number };
  isEditMode: boolean;
  onToggleEditMode: () => void;
  onDelete: () => void;
  onClose: () => void;
}

/**
 * Context menu that appears on right-click on an HTML (iframe) element.
 * It provides a single option to enter or exit the inline editing mode.
 */
const HtmlElementContextMenu: React.FC<HtmlElementContextMenuProps> = ({
  isVisible,
  position,
  isEditMode,
  onToggleEditMode,
  onDelete,
  onClose,
}) => {
  const menuRef = useRef<HTMLDivElement>(null);

  // Position the menu accurately once rendered to avoid viewport overflow
  useLayoutEffect(() => {
    if (isVisible && menuRef.current) {
      const menu = menuRef.current;
      const menuRect = menu.getBoundingClientRect();

      // Offset from cursor - position at top right by default
      // Start 5px to the right and 5px above the cursor
      let left = position.x + 5;
      let top = position.y - menuRect.height - 5;

      // Calculate available space to the right and bottom
      const rightSpace = window.innerWidth - left;
      const topSpace = top;

      // Adjust if menu would go off right edge
      if (rightSpace < menuRect.width) {
        left = position.x - menuRect.width - 5;
      }

      // Adjust if menu would go off top edge
      if (topSpace < 0) {
        top = position.y + 5; // Show below cursor instead
      }

      menu.style.left = `${left}px`;
      menu.style.top = `${top}px`;
    }
  }, [isVisible, position.x, position.y]);

  if (!isVisible) return null;

  const toggleLabel = isEditMode ? 'Exit Edit Mode' : 'Enter Edit Mode';

  return (
    <div
      ref={menuRef}
      className='fixed z-50 bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700'
      style={{ minWidth: '160px' }}
    >
      <ul className='py-1'>
        <li>
          <button
            className='w-full px-3 py-2 text-left flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-sm dark:text-gray-200'
            onClick={(e) => {
              e.stopPropagation();
              onToggleEditMode();
              onClose();
            }}
          >
            {toggleLabel}
          </button>
        </li>
        <li>
          <button
            className='w-full px-3 py-2 text-left flex items-center gap-2 hover:bg-red-100 dark:hover:bg-red-900 transition-colors text-sm text-red-600 dark:text-red-400'
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
              onClose();
            }}
          >
            Delete
          </button>
        </li>
      </ul>
    </div>
  );
};

export default HtmlElementContextMenu;
