import React, { useState, useEffect, useRef } from 'react';
import { X, Send, MessageSquare, Edit, History, Copy, Check } from 'lucide-react';
import { aiHtmlEdit } from 'wasp/client/operations';
import ReactMarkdown from 'react-markdown';

interface HtmlSideChatProps {
  htmlElementId: string;
  currentHtml: string;
  currentCss: string;
  x: number;
  y: number;
  onClose: () => void;
  onHtmlUpdate?: (newHtml: string, newCss: string) => void;
  targetBrandName?: string;
}

interface ChatMessage {
  text: string;
  isUser: boolean;
  isLoading?: boolean;
  timestamp?: number;
  isError?: boolean;
}

interface HtmlAnalysis {
  description: string;
  suggestedEdits: {
    suggestion1: string;
    suggestion2: string;
  };
}

const HtmlSideChat: React.FC<HtmlSideChatProps> = ({
  htmlElementId,
  currentHtml,
  currentCss,
  x,
  y,
  onClose,
  onHtmlUpdate,
  targetBrandName,
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    { text: 'Analyzing HTML content...', isUser: false, timestamp: Date.now(), isLoading: true },
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(true);
  const [htmlAnalysis, setHtmlAnalysis] = useState<HtmlAnalysis | null>(null);
  const [showHistory, setShowHistory] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Analyze HTML when component mounts
  useEffect(() => {
    analyzeHtml();
  }, []);

  // Mount / Unmount logging to help debug canvas side-effects
  useEffect(() => {
    console.log(`[HtmlSideChat] 📋 Mounted for htmlElementId=${htmlElementId}. Initial pos: {x:${x}, y:${y}}`);
    return () => {
      console.log(`[HtmlSideChat] 📋 Unmounted for htmlElementId=${htmlElementId}`);
    };
  }, []);

  // Function to analyze the HTML content
  const analyzeHtml = async () => {
    console.log('[HtmlSideChat] Starting HTML analysis');
    try {
      // Simple analysis based on HTML structure
      const parser = new DOMParser();
      const doc = parser.parseFromString(currentHtml, 'text/html');

      // Extract basic information
      const headings = doc.querySelectorAll('h1, h2, h3, h4, h5, h6');
      const paragraphs = doc.querySelectorAll('p');
      const images = doc.querySelectorAll('img');
      const buttons = doc.querySelectorAll('button, .button, [class*="btn"]');
      const sections = doc.querySelectorAll('[data-section-id]');

      const analysis: HtmlAnalysis = {
        description: `This HTML document contains ${sections.length} main sections, ${headings.length} headings, ${paragraphs.length} paragraphs, ${images.length} images, and ${buttons.length} interactive elements.`,
        suggestedEdits: {
          suggestion1:
            sections.length > 0
              ? `Customize the content in the "${sections[0]?.getAttribute('data-section-id')}" section`
              : 'Update the text content to match your brand voice',
          suggestion2: 'Modify the color scheme or layout to better align with your brand identity',
        },
      };

      setHtmlAnalysis(analysis);

      const messageText = `## HTML Content Analysis

I've analyzed your HTML content and identified its structure. Here are some suggestions for improvements:

1. **${analysis.suggestedEdits.suggestion1}**

2. **${analysis.suggestedEdits.suggestion2}**

### What would you like to edit? 

You can describe changes like:
- "Change the hero section text to..."
- "Update the color scheme to use blue and white"
- "Add a new section for testimonials"
- "Make the buttons larger and more prominent"`;

      setMessages([
        {
          text: messageText,
          isUser: false,
          timestamp: Date.now(),
        },
      ]);

      setIsAnalyzing(false);
    } catch (error) {
      console.error('Error analyzing HTML:', error);

      const errorMessage =
        "## ⚠️ Analysis Notice\n\nI couldn't analyze this HTML content in detail, but I'm ready to help! Please describe what you'd like to edit.";

      setMessages([
        {
          text: errorMessage,
          isUser: false,
          timestamp: Date.now(),
          isError: true,
        },
      ]);

      setIsAnalyzing(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const processRequest = async (request: string) => {
    if (isProcessing || isAnalyzing) return;

    // Create user message
    const userMessage = {
      text: request,
      isUser: true,
      timestamp: Date.now(),
    };

    // Add user message to UI
    setMessages((prev) => [...prev, userMessage]);

    // Add loading message
    setIsProcessing(true);
    setMessages((prev) => [
      ...prev,
      {
        text: `Processing your HTML edit request...`,
        isUser: false,
        isLoading: true,
        timestamp: Date.now(),
      },
    ]);

    try {
      // Call the aiHtmlEdit action
      const result = await aiHtmlEdit({
        currentFullHtml: currentHtml,
        cssContent: currentCss,
        userRequest: request,
        targetSelector: htmlElementId || 'body', // Use element ID or fallback to body
        targetBrandName: targetBrandName,
      });

      // Remove loading message and add response
      setMessages((prev) => prev.filter((msg) => !msg.isLoading));

      const responseMessage = {
        text: `## ✅ Changes Applied Successfully\n\n${result.explanation}\n\n---\n\n*The HTML and CSS have been updated. You can continue making additional changes.*`,
        isUser: false,
        timestamp: Date.now(),
      };

      setMessages((prev) => [...prev, responseMessage]);

      // Update the HTML content if callback is provided
      if (onHtmlUpdate) {
        onHtmlUpdate(result.updatedFullHtml, result.updatedCssContent);
      }
    } catch (error) {
      console.error('Error processing HTML edit request:', error);

      // Remove loading message and add error
      setMessages((prev) => prev.filter((msg) => !msg.isLoading));

      const errorMessage = {
        text: `## ❌ Error Processing Request\n\nSorry, I couldn't process your edit request. Please try rephrasing your request or be more specific about what you'd like to change.\n\n**Error Details:**\n\`${error instanceof Error ? error.message : 'Unknown error'}\``,
        isUser: false,
        timestamp: Date.now(),
        isError: true,
      };

      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim() && !isProcessing && !isAnalyzing) {
      const request = inputValue.trim();
      setInputValue('');
      await processRequest(request);
    }
  };

  const copyHtmlToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(currentHtml);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy HTML:', error);
    }
  };

  // Calculate position to ensure chat stays within viewport
  const chatStyle = {
    left: Math.min(x, window.innerWidth - 320), // 320px is chat width
    top: Math.min(y, window.innerHeight - 400), // 400px is approximate chat height
  };

  return (
    <div
      className='fixed z-50 bg-white border border-gray-200 rounded-lg shadow-xl'
      style={{
        ...chatStyle,
        width: '300px',
        height: '400px',
        maxHeight: '90vh',
      }}
    >
      {/* Header */}
      <div className='flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50 rounded-t-lg'>
        <div className='flex items-center space-x-2'>
          <Edit className='w-4 h-4 text-blue-600' />
          <span className='text-sm font-medium text-gray-900'>HTML Editor</span>
        </div>
        <div className='flex items-center space-x-1'>
          <button
            onClick={copyHtmlToClipboard}
            className='p-1 text-gray-400 hover:text-gray-600 transition-colors'
            title='Copy HTML'
          >
            {isCopied ? <Check className='w-4 h-4 text-green-600' /> : <Copy className='w-4 h-4' />}
          </button>
          <button
            onClick={() => setShowHistory(!showHistory)}
            className='p-1 text-gray-400 hover:text-gray-600 transition-colors'
            title='Toggle History'
          >
            <History className='w-4 h-4' />
          </button>
          <button onClick={onClose} className='p-1 text-gray-400 hover:text-red-600 transition-colors'>
            <X className='w-4 h-4' />
          </button>
        </div>
      </div>

      {/* Messages */}
      <div className='flex-1 overflow-y-auto p-3 space-y-3' style={{ height: '300px' }}>
        {messages.map((message, index) => (
          <div key={index} className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}>
            <div
              className={`max-w-[80%] p-2 rounded-lg text-xs ${
                message.isUser
                  ? 'bg-blue-600 text-white rounded-br-none'
                  : message.isError
                    ? 'bg-red-50 text-red-800 border border-red-200 rounded-bl-none'
                    : 'bg-gray-100 text-gray-800 rounded-bl-none'
              }`}
            >
              <div className='prose prose-xs max-w-none'>
                {message.isLoading ? (
                  <div className='flex items-center space-x-1'>
                    <div className='w-1 h-1 bg-gray-400 rounded-full animate-bounce'></div>
                    <div
                      className='w-1 h-1 bg-gray-400 rounded-full animate-bounce'
                      style={{ animationDelay: '0.1s' }}
                    ></div>
                    <div
                      className='w-1 h-1 bg-gray-400 rounded-full animate-bounce'
                      style={{ animationDelay: '0.2s' }}
                    ></div>
                    <span className='ml-2'>{message.text}</span>
                  </div>
                ) : message.isUser ? (
                  <div className='whitespace-pre-wrap'>{message.text}</div>
                ) : (
                  <ReactMarkdown
                    components={{
                      p: ({ children }) => <p className='mb-2 last:mb-0'>{children}</p>,
                      h1: ({ children }) => <h1 className='text-sm font-bold mb-1'>{children}</h1>,
                      h2: ({ children }) => <h2 className='text-xs font-bold mb-1'>{children}</h2>,
                      h3: ({ children }) => <h3 className='text-xs font-semibold mb-1'>{children}</h3>,
                      ul: ({ children }) => <ul className='list-disc list-inside mb-2'>{children}</ul>,
                      ol: ({ children }) => <ol className='list-decimal list-inside mb-2'>{children}</ol>,
                      li: ({ children }) => <li className='mb-1'>{children}</li>,
                      code: ({ children }) => <code className='bg-gray-200 px-1 rounded text-xs'>{children}</code>,
                      strong: ({ children }) => <strong className='font-semibold'>{children}</strong>,
                      em: ({ children }) => <em className='italic'>{children}</em>,
                    }}
                  >
                    {message.text}
                  </ReactMarkdown>
                )}
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className='border-t border-gray-200 p-3'>
        <form onSubmit={handleSubmit} className='flex space-x-2'>
          <input
            ref={inputRef}
            type='text'
            value={inputValue}
            onChange={handleInputChange}
            placeholder={
              isAnalyzing ? 'Analyzing...' : isProcessing ? 'Processing...' : 'Describe the changes you want...'
            }
            disabled={isProcessing || isAnalyzing}
            className='flex-1 text-xs border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed'
          />
          <button
            type='submit'
            disabled={!inputValue.trim() || isProcessing || isAnalyzing}
            className='px-2 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors'
          >
            <Send className='w-3 h-3' />
          </button>
        </form>
      </div>
    </div>
  );
};

export default HtmlSideChat;
