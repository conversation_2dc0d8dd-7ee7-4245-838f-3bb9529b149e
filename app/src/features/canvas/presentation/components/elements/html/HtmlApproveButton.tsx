import React, { useState } from 'react';
import { createPortal } from 'react-dom';
import { Check } from 'lucide-react';

interface HtmlApproveButtonProps {
  htmlElementId: string;
  x: number;
  y: number;
  width: number;
  height: number;
  onApprove: (id: string) => void;
}

interface ApprovalModalProps {
  isOpen: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

const ApprovalModal: React.FC<ApprovalModalProps> = ({ isOpen, onConfirm, onCancel }) => {
  console.log('[ApprovalModal] Rendering modal, isOpen:', isOpen);

  if (!isOpen) return null;

  // Render the modal as a portal to document.body to escape the canvas
  return createPortal(
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center' style={{ zIndex: 10000 }}>
      <div className='bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4'>
        <h3 className='text-lg font-semibold text-gray-900 mb-4'>Approve Wireframe</h3>
        <p className='text-gray-600 mb-6'>
          Are you sure you are done editing the wireframe? If so click "Yes" and it will be sent off for final website
          generation.
        </p>
        <div className='flex justify-end space-x-3'>
          <button
            onClick={onCancel}
            className='px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors'
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className='px-4 py-2 text-white bg-green-600 rounded-md hover:bg-green-700 transition-colors'
          >
            Yes
          </button>
        </div>
      </div>
    </div>,
    document.body
  );
};

const HtmlApproveButton: React.FC<HtmlApproveButtonProps> = ({ htmlElementId, x, y, width, height, onApprove }) => {
  const [showModal, setShowModal] = useState(false);

  console.log('[HtmlApproveButton] Rendering approve button for element:', htmlElementId, 'showModal:', showModal);

  // Position the button to the right of where HtmlEditMenu would be
  // HtmlEditMenu is positioned at x, y-50 and has about 5 buttons (40px each) = ~200px width
  const buttonStyle = {
    left: x + 220, // Position to the right of the edit menu
    top: y - 50, // Same vertical position as edit menu
    zIndex: 1000,
  };

  const handleApproveClick = () => {
    console.log('[HtmlApproveButton] Approve button clicked, showing modal');
    setShowModal(true);
  };

  const handleConfirm = () => {
    setShowModal(false);
    onApprove(htmlElementId);
  };

  const handleCancel = () => {
    setShowModal(false);
  };

  return (
    <>
      <div
        className='absolute'
        style={{
          ...buttonStyle,
          background: '#F9F7ED',
          border: '1px solid #E8E4D4',
          borderRadius: '8px',
          padding: '8px 12px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
          fontFamily: 'Arial, sans-serif',
        }}
      >
        <button
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            handleApproveClick();
          }}
          style={{
            background: 'rgba(200, 161, 60, 0.1)',
            border: 'none',
            margin: '0 2px',
            padding: '6px 10px',
            borderRadius: '3px',
            cursor: 'pointer',
            fontSize: '14px',
            color: '#C8A13C',
            opacity: 1,
            transition: 'all 0.2s ease',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontWeight: 'medium',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = 'rgba(200, 161, 60, 0.2)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'rgba(200, 161, 60, 0.1)';
          }}
          title='Approve wireframe for final generation'
        >
          <Check className='w-4 h-4' style={{ marginRight: '4px' }} />
          Approve
        </button>
      </div>

      <ApprovalModal isOpen={showModal} onConfirm={handleConfirm} onCancel={handleCancel} />
    </>
  );
};

export default HtmlApproveButton;
