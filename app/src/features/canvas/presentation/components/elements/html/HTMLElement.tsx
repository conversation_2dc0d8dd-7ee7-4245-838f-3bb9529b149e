/**
 * 🌐 HTML Element Component - MODULAR VERSION
 * @description HTML rendering component for modular canvas
 * @responsibility Handle HTML display, interaction, and editing
 * @ai_context Migrated from client/canvas/components/HTMLElement.tsx to modular structure
 */

import React, { useRef, useState, useEffect, memo } from 'react';
import { Group, Rect, Transformer } from 'react-konva';
import { Html } from 'react-konva-utils';
import Kon<PERSON> from 'konva';
// 🧠 MIGRATED: Using modular types instead of client types
import { HTMLElementProps } from '../../../../domain/types';
// 🧠 MIGRATED: Using modular components instead of client components
import NewsletterElement from '../newsletter/NewsletterElement';
import HtmlElementContextMenu from './HtmlElementContextMenu';
import HtmlEditMenu from './HtmlEditMenu';
import HtmlApproveButton from './HtmlApproveButton';
import HtmlSideChat from './HtmlSideChat';
// import { useSocketListener } from 'wasp/client/webSocket'; // REMOVED - migrated to Cloudflare Workers
import { approveHtmlForImageGeneration } from 'wasp/client/operations';

// Helper function to ensure Tailwind is present in the document
const ensureTailwind = (doc: Document) => {
  if (doc.getElementById('tailwindcss')) return; // already there

  // Use stylesheet link instead of script for better performance
  const link = doc.createElement('link');
  link.id = 'tailwindcss';
  link.rel = 'stylesheet';
  link.href = 'https://cdn.jsdelivr.net/npm/tailwindcss@3.3.2/dist/tailwind.min.css';
  doc.head.appendChild(link);

  // Log for debugging
  console.log('[HTMLElement] Injected Tailwind CSS stylesheet');
};

const HTMLElement: React.FC<HTMLElementProps> = memo(({ element, isSelected, onSelect, onChange, onDragging }) => {
  // If this is a newsletter, render the NewsletterElement component instead
  if (element.isNewsletter) {
    return <NewsletterElement element={element} isSelected={isSelected} onSelect={onSelect} onChange={onChange} />;
  }

  // Regular HTML element rendering

  const groupRef = useRef<Konva.Group>(null);
  const trRef = useRef<Konva.Transformer>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isScrollMode, setIsScrollMode] = useState(false);
  const [r2HtmlContent, setR2HtmlContent] = useState<string | null>(null);
  const [iframeLoaded, setIframeLoaded] = useState(false);
  const [contextMenuVisible, setContextMenuVisible] = useState(false);
  const [contextMenuPos, setContextMenuPos] = useState<{ x: number; y: number }>({ x: 0, y: 0 });

  // HTML editing state
  const [showHtmlEditMenu, setShowHtmlEditMenu] = useState(false);
  const [showHtmlSideChat, setShowHtmlSideChat] = useState(false);
  const [htmlSideChatPos, setHtmlSideChatPos] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const [currentHtmlContent, setCurrentHtmlContent] = useState<string>('');
  const [currentCssContent, setCurrentCssContent] = useState<string>('');

  // Removed throttling - using direct HTML updates

  // Effect to update chat position when element moves and chat is visible
  useEffect(() => {
    if (showHtmlSideChat) {
      const chatX = Math.max(10, element.x - 320); // 320px is chat width
      const chatY = element.y;
      setHtmlSideChatPos({ x: chatX, y: chatY });
      console.log(
        `[HTMLElement] Side chat visible, updating position for ${element.id} to {x: ${chatX}, y: ${chatY}} due to element prop change.`
      );
    }
  }, [element.x, element.y, element.width, element.height, showHtmlSideChat]); // Re-calculate if element position/size changes or chat visibility changes

  // Effect to send messages to iframe when isScrollMode changes
  useEffect(() => {
    if (iframeRef.current && iframeRef.current.contentWindow && iframeLoaded) {
      if (isScrollMode) {
        iframeRef.current.contentWindow.postMessage({ type: 'ENABLE_EDITING' }, '*');
        console.log('[HTMLElement] Sent ENABLE_EDITING message to iframe');
      } else {
        iframeRef.current.contentWindow.postMessage({ type: 'DISABLE_EDITING' }, '*');
        console.log('[HTMLElement] Sent DISABLE_EDITING message to iframe');
      }
    }
  }, [isScrollMode, iframeLoaded]);

  // Listen for HTML updates from the iframe
  useEffect(() => {
    const handleMessageFromIframe = (event: MessageEvent) => {
      // IMPORTANT: Add origin check in production if iframe content can come from different origins
      // if (event.origin !== 'https://your-expected-iframe-origin.com') return;

      if (event.data && event.data.type === 'HTML_UPDATED') {
        console.log('[HTMLElement] Received HTML_UPDATED from iframe. Payload length:', event.data.payload.html.length);
        const newHtml = event.data.payload.html;
        onChange({
          ...element,
          html: newHtml,
        });
      }
    };

    window.addEventListener('message', handleMessageFromIframe);
    return () => {
      window.removeEventListener('message', handleMessageFromIframe);
    };
  }, [element, onChange]); // Ensure element and onChange are in dependencies

  // Load HTML content from R2 if needed
  useEffect(() => {
    const loadHtmlFromR2 = async () => {
      try {
        // Log the element details for debugging
        console.log(`[HTMLElement] Element details:`, {
          id: element.id,
          htmlUrl: element.htmlUrl,
          htmlStoredInR2: element.htmlStoredInR2,
          hasHtml: element.html ? true : false,
          htmlLength: element.html?.length || 0,
        });

        // ALWAYS try to load from R2 URL if available, regardless of htmlStoredInR2 flag
        if (element.htmlUrl) {
          console.log(`[HTMLElement] Loading HTML from R2: ${element.htmlUrl}`);

          try {
            // Fetch the HTML content from R2
            const response = await fetch(element.htmlUrl);
            if (response.ok) {
              const htmlContent = await response.text();
              console.log(`[HTMLElement] Successfully loaded HTML from R2 (${htmlContent.length} bytes)`);

              // Check if the content is valid HTML
              if (htmlContent.includes('<!DOCTYPE html>') || htmlContent.includes('<html')) {
                setR2HtmlContent(htmlContent);
                console.log(`[HTMLElement] Set R2 HTML content (valid HTML document)`);
              } else {
                console.warn(`[HTMLElement] R2 content doesn't appear to be a valid HTML document`);
                // Still use it if it's all we have
                if (!element.html || element.html.length < htmlContent.length) {
                  setR2HtmlContent(htmlContent);
                }
              }
            } else {
              console.error(`[HTMLElement] Failed to load HTML from R2: ${response.status} ${response.statusText}`);
            }
          } catch (fetchError) {
            console.error(`[HTMLElement] Error fetching from R2:`, fetchError);
          }
        } else if (element.html) {
          console.log(`[HTMLElement] Using inline HTML content (${element.html.length} bytes)`);
        } else {
          console.warn(`[HTMLElement] No HTML content available (neither htmlUrl nor html)`);
        }
      } catch (error) {
        console.error(`[HTMLElement] Error loading HTML from R2:`, error);
      }
    };

    loadHtmlFromR2();
  }, [element.htmlUrl, element.htmlStoredInR2, element.html]);

  // Listen for updateHtmlImage events to update images in the iframe
  useEffect(() => {
    const handleUpdateImage = (event: Event) => {
      const customEvent = event as CustomEvent;
      const { htmlElementId, targetImageId, updatedImageUrl } = customEvent.detail;

      // Check if this update is for this specific HTMLElement instance
      if (htmlElementId === element.id && iframeRef.current) {
        const iframe = iframeRef.current;
        try {
          // Access the iframe's document
          const doc = iframe.contentDocument || iframe.contentWindow?.document;
          if (doc) {
            // Find the specific image element by its ID
            const imgElement = doc.getElementById(targetImageId) as HTMLImageElement | null;

            if (imgElement) {
              console.log(
                `[HTMLElement ${element.id}] Updating image #${targetImageId} src to ${updatedImageUrl.substring(0, 50)}...`
              );
              // Directly update the src attribute
              imgElement.src = updatedImageUrl;
            } else {
              console.warn(
                `[HTMLElement ${element.id}] Could not find image element with ID #${targetImageId} inside iframe for update.`
              );
              // Fallback: Try finding any placeholder and update it
              const placeholderImg = doc.querySelector(`img[src*="giftplaceholder.png"]`) as HTMLImageElement | null;
              if (placeholderImg) {
                console.log(`[HTMLElement ${element.id}] Fallback: Updating first found placeholder.`);
                placeholderImg.src = updatedImageUrl;
              } else {
                console.warn(`[HTMLElement ${element.id}] No placeholder images found to update.`);
              }
            }
          } else {
            console.warn(`[HTMLElement ${element.id}] Cannot access iframe document to update image.`);
          }
        } catch (error) {
          console.error(`[HTMLElement ${element.id}] Error updating image in iframe:`, error);
        }
      }
    };

    // Listen for the custom event
    window.addEventListener('updateHtmlImage', handleUpdateImage);

    // Cleanup listener on unmount
    return () => {
      window.removeEventListener('updateHtmlImage', handleUpdateImage);
    };
  }, [element.id]); // Dependency array includes element.id to ensure the correct ID is used

  // Listen for image generation progress updates via Cloudflare Workers
  useEffect(() => {
    const handleCanvasElementUpdate = (event: CustomEvent) => {
      const data = event.detail;
      // Check if this update is for this specific HTML element
      if (data.elementId === element.id && data.content) {
        const { type, message, progress, newHtmlUrl } = data.content;

        console.log(`[HTMLElement ${element.id}] Received image generation update:`, { type, message, progress });

      // Handle different types of image generation updates
      switch (type) {
        case 'image_generation_started':
          console.log(`[HTMLElement ${element.id}] Image generation started: ${message}`);
          // TODO: Show loading indicator
          break;

        case 'image_generation_progress':
          console.log(`[HTMLElement ${element.id}] Image generation progress: ${progress}% - ${message}`);
          // TODO: Update progress indicator
          break;

        case 'image_generation_completed':
          console.log(`[HTMLElement ${element.id}] Image generation completed: ${message}`);
          if (newHtmlUrl) {
            // Update the element with the new HTML URL
            onChange({
              ...element,
              htmlUrl: newHtmlUrl,
            });
            console.log(`[HTMLElement ${element.id}] Updated HTML URL to: ${newHtmlUrl}`);
          }
          // TODO: Hide loading indicator and show success message
          break;

        case 'image_generation_error':
          console.error(`[HTMLElement ${element.id}] Image generation error: ${message}`);
          // TODO: Show error message to user
          break;
      }
    }
  };

    window.addEventListener('canvas_element_update', handleCanvasElementUpdate as EventListener);

    return () => {
      window.removeEventListener('canvas_element_update', handleCanvasElementUpdate as EventListener);
    };
  }, [element.id]);

  // Update iframe content whenever HTML changes or CSS content changes
  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe) return;

    // Logic for setting iframe.srcdoc or iframe.src based on r2HtmlContent, element.html, element.htmlUrl
    // This part remains largely the same but ensures iframe is ready before CSS injection.
    let htmlToSet: string | null = null;
    let useSrcDoc = false;

    if (r2HtmlContent) {
      htmlToSet = r2HtmlContent;
      useSrcDoc = true;
      console.log(`[HTMLElement ${element.id}] Prioritizing R2 content for srcDoc.`);
    } else if (element.html) {
      htmlToSet = element.html;
      useSrcDoc = true;
      console.log(`[HTMLElement ${element.id}] Prioritizing element.html for srcDoc.`);
    }

    if (useSrcDoc && htmlToSet !== null) {
      if (iframe.srcdoc !== htmlToSet) {
        console.log(`[HTMLElement ${element.id}] Setting srcdoc.`);
        iframe.srcdoc = htmlToSet;
        // The onLoad event will handle iframeLoaded state and initial Tailwind injection
      } else {
        console.log(`[HTMLElement ${element.id}] srcdoc is already up to date.`);
        // If srcdoc hasn't changed, but CSS might have, ensure CSS is applied if iframe is loaded
        if (iframeLoaded && element.cssContent) {
          const doc = iframe.contentDocument;
          if (doc) {
            ensureTailwind(doc); // Ensure Tailwind first
            let styleTag = doc.getElementById('injected-styles') as HTMLStyleElement | null;
            if (!styleTag) {
              styleTag = doc.createElement('style');
              styleTag.id = 'injected-styles';
              doc.head.appendChild(styleTag);
              console.log(`[HTMLElement ${element.id}] Created <style id='injected-styles'> tag.`);
            }
            if (styleTag.innerHTML !== element.cssContent) {
              styleTag.innerHTML = element.cssContent;
              console.log(
                `[HTMLElement ${element.id}] Injected/Updated CSS content into <style> tag (${element.cssContent.length} chars).`
              );
            }
          }
        }
      }
    } else if (element.htmlUrl) {
      if (iframe.src !== element.htmlUrl) {
        console.log(`[HTMLElement ${element.id}] Setting iframe.src to: ${element.htmlUrl}`);
        iframe.src = element.htmlUrl;
        // onLoad will handle iframeLoaded state and Tailwind for srcdoc, but CSS injection won't apply to cross-origin src
      }
    } else if (iframeLoaded) {
      // No HTML/URL, but iframe is loaded (e.g. from initial skeleton)
      const doc = iframe.contentDocument;
      if (doc && doc.body.innerHTML !== '' && !element.html && !element.htmlUrl && !r2HtmlContent) {
        console.log(`[HTMLElement ${element.id}] Clearing iframe body as no HTML source is available.`);
        doc.body.innerHTML = '<div style="padding: 20px; color: #666;">No content available.</div>';
        ensureTailwind(doc); // Ensure Tailwind for the empty message too
        // Clear any injected CSS if HTML is cleared
        const styleTag = doc.getElementById('injected-styles') as HTMLStyleElement | null;
        if (styleTag) {
          styleTag.innerHTML = '';
        }
      }
    }
  }, [element.html, element.htmlUrl, element.cssContent, r2HtmlContent, iframeLoaded]); // Added element.cssContent to dependencies

  // onLoad handler to mark iframe as ready and inject CSS/Tailwind
  const handleIframeLoad = () => {
    console.log(`[HTMLElement] Iframe loaded for element ${element.id}`);
    setIframeLoaded(true);

    const iframe = iframeRef.current;
    if (!iframe || !iframe.contentDocument) {
      console.warn(`[HTMLElement ${element.id}] Iframe or contentDocument not available on load.`);
      return;
    }
    const doc = iframe.contentDocument;

    // If using direct URL via src attribute, we can't modify the content due to cross-origin restrictions
    const usingSrcAttribute = !!iframe.src && iframe.src !== 'about:blank' && !iframe.src.startsWith('data:');
    if (usingSrcAttribute) {
      console.log(
        `[HTMLElement ${element.id}] Using direct URL via src attribute, cannot inject CSS/Tailwind: ${iframe.src}`
      );
      return;
    }

    // Ensure Tailwind is present
    ensureTailwind(doc);

    // Inject CSS content if available
    if (element.cssContent) {
      let styleTag = doc.getElementById('injected-styles') as HTMLStyleElement | null;
      if (!styleTag) {
        styleTag = doc.createElement('style');
        styleTag.id = 'injected-styles';
        doc.head.appendChild(styleTag);
        console.log(`[HTMLElement ${element.id}] Created <style id='injected-styles'> tag on load.`);
      }
      if (styleTag.innerHTML !== element.cssContent) {
        styleTag.innerHTML = element.cssContent;
        console.log(
          `[HTMLElement ${element.id}] Injected CSS content into <style> tag on load (${element.cssContent.length} chars).`
        );
      }
    } else {
      // If no cssContent, ensure any pre-existing injected style tag is cleared
      const styleTag = doc.getElementById('injected-styles') as HTMLStyleElement | null;
      if (styleTag && styleTag.innerHTML !== '') {
        styleTag.innerHTML = '';
        console.log(
          `[HTMLElement ${element.id}] Cleared <style id='injected-styles'> tag on load as no cssContent provided.`
        );
      }
    }
  };

  // Attach transformer when selected
  useEffect(() => {
    if (isSelected && trRef.current && groupRef.current) {
      trRef.current.nodes([groupRef.current]);
      trRef.current.getLayer()?.batchDraw();
      console.log('[HTMLElement] Attached transformer');
    }
  }, [isSelected]);

  // Reset scroll mode when deselected
  useEffect(() => {
    if (!isSelected && isScrollMode) {
      setIsScrollMode(false);
      console.log('[HTMLElement] Reset scroll mode');
    }
  }, [isSelected, isScrollMode]);

  // Hide HTML edit menu when deselected
  useEffect(() => {
    if (!isSelected) {
      setShowHtmlEditMenu(false);
      console.log('[HTMLElement] Hide HTML edit menu (deselected)');
    }
  }, [isSelected]);

  // No longer using double-click for toggling edit mode

  // Context menu (right-click) handler to show menu
  const handleContextMenu = (clientX: number, clientY: number) => {
    setContextMenuPos({ x: clientX, y: clientY });
    setContextMenuVisible(true);
  };

  // Handle delete element
  const handleDeleteElement = () => {
    console.log('[HTMLElement] Deleting element:', element.id);
    // Dispatch a custom event to remove this element from the canvas
    window.dispatchEvent(
      new CustomEvent('removeCanvasElement', {
        detail: { id: element.id, elementId: element.id },
      })
    );
  };

  // Close context menu and HTML edit menu on outside click
  useEffect(() => {
    const handleClickOutside = () => {
      if (contextMenuVisible) setContextMenuVisible(false);
      if (showHtmlEditMenu) setShowHtmlEditMenu(false);
    };
    window.addEventListener('click', handleClickOutside);
    return () => window.removeEventListener('click', handleClickOutside);
  }, [contextMenuVisible, showHtmlEditMenu]);

  // Handle drag end
  const handleDragEnd = (e: Konva.KonvaEventObject<DragEvent>) => {
    console.log('[HTMLElement] Drag end:', { x: e.target.x(), y: e.target.y() });
    onChange({
      ...element,
      x: e.target.x(),
      y: e.target.y(),
      isDragging: false,
    });
  };

  // Handle transform end
  const handleTransformEnd = () => {
    const node = groupRef.current;
    if (!node) return;

    const scaleX = node.scaleX();
    const scaleY = node.scaleY();

    // Reset scale
    node.scaleX(1);
    node.scaleY(1);

    // Calculate new dimensions
    const newWidth = Math.max(50, element.width * scaleX);
    const newHeight = Math.max(50, element.height * scaleY);

    console.log('[HTMLElement] Transform end:', {
      x: node.x(),
      y: node.y(),
      width: newWidth,
      height: newHeight,
    });

    onChange({
      ...element,
      x: node.x(),
      y: node.y(),
      width: newWidth,
      height: newHeight,
      rotation: node.rotation(),
    });
  };

  // Check if the HTML content is a React app

  // Handle click on the group
  const handleClick = (e: Konva.KonvaEventObject<MouseEvent>) => {
    console.log('[HTMLElement] Clicked');
    if (!isSelected) {
      // Convert Konva event to React event
      const syntheticEvent = {
        shiftKey: e.evt.shiftKey,
        ctrlKey: e.evt.ctrlKey,
        altKey: e.evt.altKey,
        metaKey: e.evt.metaKey,
      } as React.MouseEvent;
      onSelect(syntheticEvent);
    } else {
      // When already selected, show the HTML edit menu
      setShowHtmlEditMenu(true);
    }
  };

  // HTML editing handlers
  const handleHtmlEdit = async (elementId: string) => {
    console.log('[HTMLElement] Starting HTML edit for:', elementId);
    window.dispatchEvent(new CustomEvent('canvas:htmlEditChatOpened', { detail: { elementId } }));
    // Ensure we have up-to-date HTML and CSS content
    let htmlContent = r2HtmlContent || element.html || '';
    // If we still have no HTML but we do have a remote URL, fetch it now (synchronously for this call)
    if (!htmlContent && element.htmlUrl) {
      try {
        console.log(`[HTMLElement] Fetching HTML from htmlUrl for edit: ${element.htmlUrl}`);
        const resp = await fetch(element.htmlUrl);
        if (resp.ok) {
          htmlContent = await resp.text();
          console.log('[HTMLElement] Fetched remote HTML (length:', htmlContent.length, ')');
        }
      } catch (err) {
        console.warn('[HTMLElement] Failed to fetch htmlUrl before edit:', err);
      }
    }

    const cssContent = element.cssContent || '';

    setCurrentHtmlContent(htmlContent);
    setCurrentCssContent(cssContent);

    // Position the side chat to the left of the element
    const chatX = Math.max(10, element.x - 320); // 320px is chat width
    const chatY = element.y;

    setHtmlSideChatPos({ x: chatX, y: chatY });
    setShowHtmlSideChat(true);
    setShowHtmlEditMenu(false);
  };

  const handleHtmlHistory = (elementId: string) => {
    console.log('[HTMLElement] Show HTML history for:', elementId);
    // TODO: Implement HTML history functionality
  };

  const handleCopyHtml = async (elementId: string) => {
    console.log('[HTMLElement] Copy HTML for:', elementId);
    try {
      const htmlContent = r2HtmlContent || element.html || '';
      await navigator.clipboard.writeText(htmlContent);
      console.log('[HTMLElement] HTML copied to clipboard');
    } catch (error) {
      console.error('[HTMLElement] Failed to copy HTML:', error);
    }
  };

  const handleViewCode = (elementId: string) => {
    console.log('[HTMLElement] View code for:', elementId);
    // TODO: Implement code viewer functionality
  };

  const handleHtmlUpdate = (newHtml: string, newCss: string) => {
    console.log('[HTMLElement] Updating HTML content:', {
      htmlLength: newHtml.length,
      cssLength: newCss.length,
    });

    // Update the element with new content
    onChange({
      ...element,
      html: newHtml,
      cssContent: newCss,
    });

    // Update local state
    setCurrentHtmlContent(newHtml);
    setCurrentCssContent(newCss);
  };

  const handleCloseSideChat = () => {
    console.log('[HTMLElement] Closing HtmlSideChat for', element.id);
    window.dispatchEvent(new CustomEvent('canvas:htmlEditChatClosed', { detail: { elementId: element.id } }));
    setShowHtmlSideChat(false);
  };

  const handleApprove = async (elementId: string) => {
    console.log('[HTMLElement] Approving wireframe for final generation:', elementId);

    try {
      // Get the HTML URL from the element
      const htmlUrl = element.htmlUrl;
      if (!htmlUrl) {
        console.error('[HTMLElement] No HTML URL found for element:', elementId);
        return;
      }

      console.log('[HTMLElement] Starting image generation for HTML:', htmlUrl);

      // Call the server action
      const result = await approveHtmlForImageGeneration({
        htmlElementId: elementId,
        htmlUrl: htmlUrl,
      });

      console.log('[HTMLElement] Image generation completed:', result);

      // The WebSocket events will handle updating the UI in real-time
      // The canvas element will be updated automatically when the new HTML URL is ready
    } catch (error: any) {
      console.error('[HTMLElement] Error during approval process:', error);
      // TODO: Show user-friendly error message
    }
  };

  return (
    <>
      <Group
        x={element.x}
        y={element.y}
        width={element.width}
        height={element.height}
        draggable={!isScrollMode}
        id={element.id}
        name='html-element-konva-group'
        ref={groupRef}
        onClick={handleClick}
        // Attach Konva contextmenu for when overlay is pointer-events:none
        onContextMenu={(e: Konva.KonvaEventObject<PointerEvent>) => {
          e.evt.preventDefault();
          e.evt.stopPropagation();
          handleContextMenu(e.evt.clientX, e.evt.clientY);
        }}
        onDragEnd={handleDragEnd}
        onDragMove={(e: Konva.KonvaEventObject<DragEvent>) => {
          if (isScrollMode) return;
          const node = e.target;
          node.setAttr('startX', node.x());
          node.setAttr('startY', node.y());

          // Also store stage info
          const stage = node.getStage();
          if (stage) {
            node.setAttr('stageStartScale', stage.scaleX());
            node.setAttr('stageStartX', stage.x());
            node.setAttr('stageStartY', stage.y());
          }

          // 🧠 MIGRATED: Fix onDragging callback - it expects a boolean, not an object
          onDragging?.(true);
        }}
        onTransformEnd={handleTransformEnd}
        listening={true}
      >
        {/* Background rect for the HTML element */}
        <Rect
          width={element.width}
          height={element.height}
          fill='transparent'
          stroke={isSelected ? '#9EA581' : 'rgba(0,0,0,0.1)'}
          strokeWidth={isSelected ? 2 : 1}
          cornerRadius={5}
          perfectDrawEnabled={false}
        />

        {/* HTML content */}
        <Html
          divProps={{
            style: {
              position: 'absolute',
              pointerEvents: isScrollMode ? 'auto' : 'none',
              zIndex: 1,
            },
            onContextMenu: (e: React.MouseEvent<HTMLDivElement>) => {
              e.preventDefault();
              e.stopPropagation();
              handleContextMenu(e.clientX, e.clientY);
            },
          }}
        >
          <div
            style={{
              width: `${element.width}px`,
              height: `${element.height}px`,
              overflow: isScrollMode ? 'auto' : 'hidden',
              border: isSelected ? '2px solid blue' : '1px solid rgba(0,0,0,0.1)',
              borderRadius: '5px',
              boxSizing: 'border-box',
              background: 'white', // 🧠 MIGRATED: Removed backgroundColor property (not in our type)
            }}
          >
            <iframe
              ref={iframeRef}
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                overflow: isScrollMode ? 'auto' : 'hidden',
                boxSizing: 'border-box',
              }}
              sandbox='allow-scripts allow-forms allow-popups allow-same-origin allow-modals'
              title={'HTML Content'}
              onLoad={handleIframeLoad}
              {...(r2HtmlContent || (element.html && element.html.length > 0)
                ? { srcDoc: r2HtmlContent || element.html }
                : { src: element.htmlUrl || undefined })}
            />

            {/* Scroll mode indicator */}
            {isScrollMode && (
              <div
                style={{
                  position: 'absolute',
                  top: '5px',
                  right: '5px',
                  background: 'rgba(0, 0, 0, 0.7)',
                  color: 'white',
                  padding: '3px 6px',
                  borderRadius: '3px',
                  fontSize: '10px',
                  cursor: 'pointer',
                  zIndex: 10,
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  setIsScrollMode(false);
                  console.log('[HTMLElement] Exit scroll mode');
                }}
              >
                Exit Scroll Mode
              </div>
            )}
          </div>
        </Html>
      </Group>

      {/* Transformer */}
      {isSelected && !isScrollMode && (
        <Transformer
          ref={trRef}
          boundBoxFunc={(oldBox, newBox) => {
            // Enforce minimum dimensions
            if (newBox.width < 50 || newBox.height < 50) {
              return oldBox;
            }
            return newBox;
          }}
          enabledAnchors={[
            'top-left',
            'top-center',
            'top-right',
            'middle-left',
            'middle-right',
            'bottom-left',
            'bottom-center',
            'bottom-right',
          ]}
          rotateEnabled={true}
          borderStroke='#9EA581'
          borderStrokeWidth={2}
          anchorStroke='#9EA581'
          anchorFill='#9EA581'
          anchorStrokeWidth={1}
        />
      )}

      {/* Context menu, wrapped in react-konva-utils <Html> */}
      {contextMenuVisible && (
        <Html>
          <HtmlElementContextMenu
            isVisible={true} // The outer condition handles visibility for the <Html> wrapper
            position={contextMenuPos}
            isEditMode={isScrollMode}
            onToggleEditMode={() => {
              setIsScrollMode((prev) => !prev);
              setContextMenuVisible(false); // Close menu after action
            }}
            onDelete={handleDeleteElement}
            onClose={() => setContextMenuVisible(false)}
          />
        </Html>
      )}

      {/* HTML Edit Menu, wrapped in react-konva-utils <Html> */}
      {showHtmlEditMenu && isSelected && !isScrollMode && (
        <Html>
          <HtmlEditMenu
            htmlElementId={element.id}
            x={element.x}
            y={element.y}
            width={element.width}
            height={element.height}
            onEdit={handleHtmlEdit}
            onHistory={handleHtmlHistory}
            onCopy={handleCopyHtml}
            onViewCode={handleViewCode}
          />
        </Html>
      )}

      {/* HTML Approve Button, wrapped in react-konva-utils <Html> */}
      {showHtmlEditMenu && isSelected && !isScrollMode && (
        <Html>
          <HtmlApproveButton
            htmlElementId={element.id}
            x={element.x}
            y={element.y}
            width={element.width}
            height={element.height}
            onApprove={handleApprove}
          />
        </Html>
      )}

      {/* HTML Side Chat for AI editing - Rendered via react-konva-utils Html portal */}
      {showHtmlSideChat && (
        <Html>
          <HtmlSideChat
            htmlElementId={element.id}
            currentHtml={currentHtmlContent}
            currentCss={currentCssContent}
            x={htmlSideChatPos.x}
            y={htmlSideChatPos.y}
            onClose={handleCloseSideChat}
            onHtmlUpdate={handleHtmlUpdate}
            targetBrandName={undefined} // TODO: Get from brand kit if needed
          />
        </Html>
      )}
    </>
  );
});

export default HTMLElement;
