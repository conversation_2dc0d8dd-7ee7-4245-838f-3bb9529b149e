/**
 * 🌐 Modular HTML Elements Component
 *
 * @description Handles rendering and interaction of HTML elements on canvas
 * @responsibility HTML rendering, selection, and updates
 * @dependencies HTMLElement component, canvas store
 * @ai_context MIGRATED from inline canvas code to proper modular component
 */

import React from 'react';
import HTMLElement from './HTMLElement'; // 🏗️ Using our own modular copy!
import { useElementDomainStore, useToolDomainStore } from '../../../../domain/state'; // 🧠 Using pure domain state!
import { ElementService } from '../../../../domain/services'; // 🧠 Using domain service!
import { useShallow } from 'zustand/react/shallow';

/**
 * 🌐 HTML Elements Props
 */
export interface HtmlElementsProps {
  className?: string;
}

/**
 * 🌐 HTML Elements Component
 * @ai_context Modular component for rendering all HTML elements
 */
export const HtmlElements: React.FC<HtmlElementsProps> = ({ className }) => {
  // Get canvas state from pure domain stores with shallow comparison
  const { htmlElements, selectedIds } = useElementDomainStore(
    useShallow((state) => ({
      htmlElements: state.htmlElements,
      selectedIds: state.selectedIds,
    }))
  );

  const { activeTool } = useToolDomainStore(
    useShallow((state) => ({
      activeTool: state.activeTool,
    }))
  );

  /**
   * 🎯 Handle HTML element selection
   */
  const handleHtmlSelect = (elementId: string, event?: React.MouseEvent) => {
    if (activeTool !== 'select') return;

    console.log('[HtmlElements] HTML element selected:', elementId);

    // Handle multi-select with Shift key
    const isMultiSelect = event ? event.shiftKey : false;
    if (isMultiSelect) {
      const newSelection = [...selectedIds];
      if (newSelection.includes(elementId)) {
        const index = newSelection.indexOf(elementId);
        newSelection.splice(index, 1);
      } else {
        newSelection.push(elementId);
      }
      // 🧠 Using domain service instead of direct canvas-store call
      ElementService.selection.select(newSelection);
    } else {
      // 🧠 Using domain service instead of direct canvas-store call
      ElementService.selection.select([elementId]);
    }
  };

  /**
   * 🔄 Handle HTML element changes
   */
  const handleHtmlChange = (newAttrs: any) => {
    console.log('[HtmlElements] HTML element changed:', newAttrs);

    // Remove isDragging from attributes before saving
    delete newAttrs.isDragging;

    // 🧠 Using domain service instead of direct canvas-store call
    ElementService.htmlElements.update(newAttrs.id, newAttrs);
  };

  /**
   * 🔄 Handle HTML element dragging
   */
  const handleHtmlDragging = (newAttrs: any) => {
    console.log('[HtmlElements] HTML element dragging:', newAttrs);
    handleHtmlChange(newAttrs);
  };

  // Return null if no HTML elements
  if (!htmlElements || htmlElements.length === 0) {
    return null;
  }

  return (
    <>
      {htmlElements.map((element: any) => (
        <HTMLElement
          key={element.id}
          element={element}
          isSelected={selectedIds.includes(element.id)}
          onSelect={(e) => handleHtmlSelect(element.id, e)}
          onChange={handleHtmlChange}
          onDragging={handleHtmlDragging}
        />
      ))}
    </>
  );
};

export default HtmlElements;
