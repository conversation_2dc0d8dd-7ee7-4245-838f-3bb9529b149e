import React from 'react';
import { Edit, History, Copy, Code, Settings } from 'lucide-react';

interface HtmlEditMenuProps {
  htmlElementId: string;
  x: number;
  y: number;
  width: number;
  height: number;
  onEdit: (id: string) => void;
  onHistory: (id: string) => void;
  onCopy: (id: string) => void;
  onViewCode: (id: string) => void;
}

const HtmlEditMenu: React.FC<HtmlEditMenuProps> = ({
  htmlElementId,
  x,
  y,
  width,
  height,
  onEdit,
  onHistory,
  onCopy,
  onViewCode,
}) => {
  // Position the menu above the HTML element
  const menuStyle = {
    left: x,
    top: y - 50, // Position 50px above the element
    zIndex: 1000,
  };

  return (
    <div
      className='absolute flex items-center space-x-1'
      style={{
        ...menuStyle,
        background: '#F9F7ED',
        border: '1px solid #E8E4D4',
        borderRadius: '8px',
        padding: '8px 12px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
        fontFamily: 'Arial, sans-serif',
      }}
    >
      {/* Edit Button */}
      <button
        onClick={() => onEdit(htmlElementId)}
        style={{
          background: 'none',
          border: 'none',
          margin: '0 2px',
          padding: '6px 10px',
          borderRadius: '3px',
          cursor: 'pointer',
          fontSize: '14px',
          color: '#566B46',
          opacity: 0.7,
          transition: 'all 0.2s ease',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '36px',
          height: '36px',
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.color = '#C8A13C';
          e.currentTarget.style.opacity = '1';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.color = '#566B46';
          e.currentTarget.style.opacity = '0.7';
        }}
        title='AI Edit HTML'
      >
        <Edit className='w-4 h-4' />
      </button>

      {/* History Button */}
      <button
        onClick={() => onHistory(htmlElementId)}
        style={{
          background: 'none',
          border: 'none',
          margin: '0 2px',
          padding: '6px 10px',
          borderRadius: '3px',
          cursor: 'pointer',
          fontSize: '14px',
          color: '#566B46',
          opacity: 0.7,
          transition: 'all 0.2s ease',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '36px',
          height: '36px',
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.color = '#C8A13C';
          e.currentTarget.style.opacity = '1';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.color = '#566B46';
          e.currentTarget.style.opacity = '0.7';
        }}
        title='View Edit History'
      >
        <History className='w-4 h-4' />
      </button>

      {/* Copy HTML Button */}
      <button
        onClick={() => onCopy(htmlElementId)}
        style={{
          background: 'none',
          border: 'none',
          margin: '0 2px',
          padding: '6px 10px',
          borderRadius: '3px',
          cursor: 'pointer',
          fontSize: '14px',
          color: '#566B46',
          opacity: 0.7,
          transition: 'all 0.2s ease',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '36px',
          height: '36px',
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.color = '#C8A13C';
          e.currentTarget.style.opacity = '1';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.color = '#566B46';
          e.currentTarget.style.opacity = '0.7';
        }}
        title='Copy HTML'
      >
        <Copy className='w-4 h-4' />
      </button>

      {/* View Code Button */}
      <button
        onClick={() => onViewCode(htmlElementId)}
        style={{
          background: 'none',
          border: 'none',
          margin: '0 2px',
          padding: '6px 10px',
          borderRadius: '3px',
          cursor: 'pointer',
          fontSize: '14px',
          color: '#566B46',
          opacity: 0.7,
          transition: 'all 0.2s ease',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '36px',
          height: '36px',
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.color = '#C8A13C';
          e.currentTarget.style.opacity = '1';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.color = '#566B46';
          e.currentTarget.style.opacity = '0.7';
        }}
        title='View HTML/CSS Code'
      >
        <Code className='w-4 h-4' />
      </button>

      {/* Settings Button */}
      <button
        style={{
          background: 'none',
          border: 'none',
          margin: '0 2px',
          padding: '6px 10px',
          borderRadius: '3px',
          cursor: 'pointer',
          fontSize: '14px',
          color: '#566B46',
          opacity: 0.7,
          transition: 'all 0.2s ease',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '36px',
          height: '36px',
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.color = '#C8A13C';
          e.currentTarget.style.opacity = '1';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.color = '#566B46';
          e.currentTarget.style.opacity = '0.7';
        }}
        title='HTML Element Settings'
      >
        <Settings className='w-4 h-4' />
      </button>
    </div>
  );
};

export default HtmlEditMenu;
