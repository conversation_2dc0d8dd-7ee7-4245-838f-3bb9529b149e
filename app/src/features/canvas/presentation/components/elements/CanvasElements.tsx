/* eslint-disable react/prop-types */
import React, { useCallback, useRef } from 'react';
import { CanvasElement } from '../../hooks/useCanvasSync';
import TextElement from './text/TextElement';
import { DomainTextElement } from '../../../domain/state';
import Konva from 'konva';
import { URLImage } from './images';
import { ImageProperties } from './images/URLImage';

interface CanvasElementsProps {
  element: CanvasElement;
  isSelected: boolean;
  onSelect?: (id: string) => void;
  onUpdate?: (id: string, changes: Partial<CanvasElement>, throttle?: boolean) => void;
}

const CanvasElements: React.FC<CanvasElementsProps> = ({
  element,
  onSelect,
  onUpdate,
  isSelected,
}: CanvasElementsProps) => {
  // Removed spammy canvas element log

  const dragThrottleRef = useRef<NodeJS.Timeout | null>(null);

  const handleDragMove = useCallback(
    (e: Konva.KonvaEventObject<DragEvent>) => {
      const target = e.target;

      // Clear existing throttle
      if (dragThrottleRef.current) {
        clearTimeout(dragThrottleRef.current);
      }

      // Throttle drag updates to every 50ms for smooth real-time updates
      dragThrottleRef.current = setTimeout(() => {
        onUpdate?.(
          element.id,
          {
            position: { x: target.x(), y: target.y() },
            properties: {
              ...element.properties, // 🚨 FIX: Preserve actual properties (including crop data)
              x: target.x(),
              y: target.y(),
            },
          },
          true
        ); // Use throttling for smooth updates
      }, 50);
    },
    [element.id, onUpdate]
  );

  const handleDragEnd = useCallback(
    (e: Konva.KonvaEventObject<DragEvent>) => {
      // Clear any pending throttled update
      if (dragThrottleRef.current) {
        clearTimeout(dragThrottleRef.current);
        dragThrottleRef.current = null;
      }

      const target = e.target;
      console.log('handleDragEnd: ', { element, x: target.x(), y: target.y() });
      onUpdate?.(
        element.id,
        {
          position: { x: target.x(), y: target.y() },
          properties: {
            ...element.properties, // 🚨 FIX: Preserve actual properties (including crop data)
            x: target.x(),
            y: target.y(),
          },
        },
        false
      ); // Send final position immediately
    },
    [element.id, onUpdate]
  );

  const handleTransformEnd = useCallback(
    (e: Konva.KonvaEventObject<Event>) => {
      const target = e.target;
      onUpdate?.(element.id, {
        position: { x: target.x(), y: target.y() },
        rotation: target.rotation(),
        scaleX: target.scaleX(),
        scaleY: target.scaleY(),
      });
    },
    [element.id, onUpdate]
  );

  const commonProps = {
    x: element.position.x,
    y: element.position.y,
    rotation: element.rotation || 0,
    scaleX: element.scaleX || 1,
    scaleY: element.scaleY || 1,
    draggable: true,
    onClick: () => onSelect?.(element.id),
    onTap: () => onSelect?.(element.id),
    onDragStart: () => onSelect?.(element.id),
    onDragMove: handleDragMove,
    onDragEnd: handleDragEnd,
    onTransformEnd: handleTransformEnd,
    isSelected,
  };

  switch (element.type) {
    case 'image': {
      const props = element.properties as ImageProperties;
      return <URLImage {...commonProps} properties={props} isSelected={isSelected} zIndex={element.zIndex} />;
    }
    case 'text': {
      const props = element.properties as DomainTextElement;

      return (
        <TextElement
          {...commonProps}
          text={props.text || ''}
          fontSize={props.fontSize || 16}
          fontFamily={props.fontFamily || 'Arial'}
          fill={props.fill || '#000000'}
          align={(props.align as 'left' | 'center' | 'right') || 'left'}
          onTextChange={(value) =>
            onUpdate?.(element.id, {
              properties: {
                ...(element.properties as DomainTextElement),
                text: value,
              },
            })
          }
        />
      );
    }

    default:
      console.warn(`Unknown element type: ${element.type}`);
      return null;
  }
};

export default CanvasElements;
