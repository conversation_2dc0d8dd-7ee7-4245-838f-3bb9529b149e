import React, { useRef, useState, useEffect } from 'react';
import { Group, Rect } from 'react-konva';
import { Html } from 'react-konva-utils';
import Konva from 'konva';
// 🧠 MIGRATED: Using modular types instead of client types
import { HTMLElementObject } from '../../../../domain/types';
// 🧠 MIGRATED: Stub throttle hook for modular architecture
const useThrottle = (value: any, delay: number) => value;

// 🧠 MIGRATED: Newsletter data interface for modular architecture
interface NewsletterData {
  title: string;
  sections: any[];
  style: string;
  colorScheme: string;
  currentView: 'mobile' | 'desktop';
  mobileHtml?: string;
  desktopHtml?: string;
}

interface NewsletterElementProps {
  element: HTMLElementObject & { newsletterData?: NewsletterData; backgroundColor?: string };
  isSelected: boolean;
  onSelect: (event?: React.MouseEvent) => void;
  onChange: (attrs: HTMLElementObject & { newsletterData?: NewsletterData; backgroundColor?: string }) => void;
}

const NewsletterElement: React.FC<NewsletterElementProps> = ({ element, isSelected, onSelect, onChange }) => {
  const groupRef = useRef<Konva.Group>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [iframeLoaded, setIframeLoaded] = useState(false);

  // Ensure we have newsletter data
  const newsletterData = element.newsletterData || {
    title: 'Newsletter',
    sections: [],
    style: 'modern',
    colorScheme: 'light',
    currentView: 'mobile' as 'mobile' | 'desktop',
  };

  // Throttle HTML content updates to prevent flashing
  const throttledHtml = useThrottle(
    newsletterData.currentView === 'mobile'
      ? newsletterData.mobileHtml || element.html
      : newsletterData.desktopHtml || element.html,
    500
  );

  // Handle iframe load
  const handleIframeLoad = () => {
    console.log(`[NewsletterElement] Iframe loaded for element ${element.id}`);
    setIframeLoaded(true);

    const iframe = iframeRef.current;
    if (!iframe) return;

    // Inject Tailwind CSS if needed
    if (iframe.contentDocument) {
      const doc = iframe.contentDocument;
      if (!doc.getElementById('tailwindcss')) {
        const link = doc.createElement('link');
        link.id = 'tailwindcss';
        link.rel = 'stylesheet';
        link.href = 'https://cdn.jsdelivr.net/npm/tailwindcss@3.3.2/dist/tailwind.min.css';
        doc.head.appendChild(link);
        console.log('[NewsletterElement] Injected Tailwind CSS stylesheet');
      }
    }
  };

  // Update iframe content when HTML changes
  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe || !iframeLoaded) return;

    const html = throttledHtml;
    if (!html) {
      console.warn(`[NewsletterElement] No HTML content available for view: ${newsletterData.currentView}`);
      return;
    }

    try {
      // Check if HTML is a full document or just content
      const isFullHtml = html.includes('<!DOCTYPE html>') || html.includes('<html');

      if (isFullHtml) {
        iframe.srcdoc = html;
      } else {
        const doc = iframe.contentDocument;
        if (doc) {
          doc.body.innerHTML = html;
        }
      }
    } catch (error) {
      console.error(`[NewsletterElement] Error updating iframe content:`, error);
    }
  }, [throttledHtml, iframeLoaded, newsletterData.currentView]);

  // Toggle between mobile and desktop views
  const toggleView = () => {
    const newView = newsletterData.currentView === 'mobile' ? 'desktop' : 'mobile';
    const updatedNewsletterData = {
      ...newsletterData,
      currentView: newView as 'mobile' | 'desktop',
    };

    onChange({
      ...element,
      newsletterData: updatedNewsletterData,
    });

    // Dispatch event for other components that might need to know
    window.dispatchEvent(
      new CustomEvent('toggleNewsletterView', {
        detail: {
          id: element.id,
          view: newView,
        },
      })
    );
  };

  // Handle drag end
  const handleDragEnd = (e: Konva.KonvaEventObject<DragEvent>) => {
    onChange({
      ...element,
      x: e.target.x(),
      y: e.target.y(),
    });
  };

  // Handle click on the group
  const handleClick = (e: Konva.KonvaEventObject<MouseEvent>) => {
    if (!isSelected) {
      // Convert Konva event to React event
      const syntheticEvent = {
        shiftKey: e.evt.shiftKey,
        ctrlKey: e.evt.ctrlKey,
        altKey: e.evt.altKey,
        metaKey: e.evt.metaKey,
      } as React.MouseEvent;
      onSelect(syntheticEvent);
    }
  };

  return (
    <>
      <Group
        x={element.x}
        y={element.y}
        width={element.width}
        height={element.height}
        draggable={true}
        id={element.id}
        ref={groupRef}
        onClick={handleClick}
        onDragEnd={handleDragEnd}
      >
        {/* Background rect for the newsletter element */}
        <Rect
          width={element.width}
          height={element.height}
          fill='transparent'
          stroke={isSelected ? 'blue' : 'rgba(0,0,0,0.1)'}
          strokeWidth={isSelected ? 2 : 1}
          cornerRadius={5}
          perfectDrawEnabled={false}
          listening={true}
        />

        {/* Newsletter content */}
        <Html
          divProps={{
            style: {
              position: 'absolute',
              pointerEvents: 'none',
              zIndex: 1,
            },
          }}
        >
          <div
            style={{
              width: `${element.width}px`,
              height: `${element.height}px`,
              overflow: 'hidden',
              border: isSelected ? '2px solid blue' : '1px solid rgba(0,0,0,0.1)',
              borderRadius: '5px',
              boxSizing: 'border-box',
              background: element.backgroundColor || 'white',
              position: 'relative',
              pointerEvents: isSelected ? 'auto' : 'none', // Enable pointer events when selected
            }}
          >
            <iframe
              ref={iframeRef}
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                overflow: 'hidden',
                boxSizing: 'border-box',
                pointerEvents: isSelected ? 'none' : 'auto', // Disable iframe pointer events when selected to allow toolbar interaction
              }}
              sandbox='allow-scripts allow-forms allow-popups allow-same-origin allow-modals'
              title={`Newsletter: ${newsletterData.title}`}
              onLoad={handleIframeLoad}
            />

            {/* View indicator */}
            <div
              style={{
                position: 'absolute',
                bottom: '5px',
                right: '5px',
                background: 'rgba(0, 0, 0, 0.5)',
                color: 'white',
                padding: '2px 5px',
                borderRadius: '3px',
                fontSize: '9px',
                pointerEvents: 'none',
              }}
            >
              {newsletterData.currentView === 'mobile' ? 'Mobile' : 'Desktop'}
            </div>

            {/* Render toolbar when selected */}
            {isSelected && (
              <div
                style={{
                  position: 'absolute',
                  top: '-40px',
                  left: '0px',
                  zIndex: 1000,
                  display: 'flex',
                  gap: '5px',
                  background: 'rgba(0, 0, 0, 0.7)',
                  borderRadius: '4px',
                  padding: '5px',
                  boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
                  color: 'white',
                  fontSize: '12px',
                  pointerEvents: 'auto',
                }}
              >
                <button
                  onClick={toggleView}
                  style={{
                    background: 'transparent',
                    border: '1px solid rgba(255,255,255,0.3)',
                    color: 'white',
                    padding: '3px 6px',
                    borderRadius: '3px',
                    fontSize: '10px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                  }}
                >
                  <span style={{ color: 'white' }}>
                    {newsletterData.currentView === 'mobile' ? 'Mobile' : 'Desktop'}
                  </span>
                  <svg width='12' height='12' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'>
                    <path
                      d='M7 10L12 15L17 10'
                      stroke='white'
                      strokeWidth='2'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                    />
                  </svg>
                </button>

                <button
                  onClick={() => {
                    // Open a dialog to edit newsletter properties
                    console.log('Edit newsletter properties');
                  }}
                  style={{
                    background: 'transparent',
                    border: '1px solid rgba(255,255,255,0.3)',
                    color: 'white',
                    padding: '3px 6px',
                    borderRadius: '3px',
                    fontSize: '10px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                  }}
                >
                  <span style={{ color: 'white' }}>Edit</span>
                  <svg width='12' height='12' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'>
                    <path
                      d='M11 4H4V20H20V13'
                      stroke='white'
                      strokeWidth='2'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                    />
                    <path
                      d='M18.5 2.5L21.5 5.5L12 15H9V12L18.5 2.5Z'
                      stroke='white'
                      strokeWidth='2'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                    />
                  </svg>
                </button>
              </div>
            )}
          </div>
        </Html>
      </Group>
    </>
  );
};

export default NewsletterElement;
