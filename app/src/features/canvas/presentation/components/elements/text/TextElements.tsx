/**
 * 📝 Modular Text Elements Component
 *
 * @description Handles rendering and interaction of text elements on canvas
 * @responsibility Text rendering, selection, editing, and updates
 * @dependencies TextElement component, canvas store
 * @ai_context MIGRATED from inline canvas code to proper modular component
 */

import React from 'react';
// import TextElement from './TextElement'; // 🏗️ Using our own modular copy!
// import { useElementDomainStore } from '../../../../domain/state'; // 🧠 Using pure domain state!
// import { ElementService } from '../../../../domain/services'; // 🧠 Using domain service!
// import { useShallow } from 'zustand/react/shallow';

/**
 * 📝 Text Elements Props
 */
export interface TextElementsProps {
  className?: string;
}

/**
 * 📝 Text Elements Component
 * @ai_context Modular component for rendering all text elements
 */
export const TextElements: React.FC<TextElementsProps> = ({ className }) => {
  // Get canvas state from pure domain store with shallow comparison
  // const { texts, selectedIds } = useElementDomainStore(useShallow((state) => ({
  //   texts: state.texts,
  //   selectedIds: state.selectedIds,
  // })));

  // /**
  //  * 🎯 Handle text selection
  //  */
  // const handleTextSelect = (textId: string) => {
  //   console.log('[TextElements] Text selected:', textId);
  //   // 🧠 Using domain service instead of direct canvas-store call
  //   ElementService.selection.select([textId]);
  // };

  // /**
  //  * 🔄 Handle text changes
  //  */
  // const handleTextChange = (newAttrs: any) => {
  //   console.log('[TextElements] Text changed:', newAttrs);
  //   // 🧠 Using domain service instead of direct canvas-store call
  //   ElementService.texts.update(newAttrs.id, newAttrs);
  // };

  // /**
  //  * 🔄 Handle text dragging
  //  */
  // const handleTextDragging = (newAttrs: any) => {
  //   console.log('[TextElements] Text dragging:', newAttrs);
  //   // 🧠 Using domain service instead of direct canvas-store call
  //   ElementService.texts.update(newAttrs.id, newAttrs);
  // };

  // // Return null if no texts
  // if (!texts || texts.length === 0) {
  //   return null;
  // }

  // return (
  //   <>
  //     {texts.map((text: any) => (
  //       <TextElement
  //         key={text.id}
  //         textNode={text}
  //         isSelected={selectedIds.includes(text.id)}
  //         onSelect={() => handleTextSelect(text.id)}
  //         onChange={handleTextChange}
  //         onDragging={handleTextDragging}
  //       />
  //     ))}
  //   </>
  // );

  return null;
};

export default TextElements;
