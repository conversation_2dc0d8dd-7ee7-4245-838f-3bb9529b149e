/**
 * ✏️ Text Element Component - MODULAR VERSION
 * @description Text rendering component for modular canvas
 * @responsibility Handle text display, interaction, and editing
 * @ai_context Migrated from client/canvas/components/TextElement.tsx to modular structure
 */

import React, { useRef, memo } from 'react';
import { Text, Transformer } from 'react-konva';
import Konva from 'konva';

export interface TextElementProps extends Partial<Konva.TextConfig> {
  text: string;
  fontSize: number;
  fontFamily: string;
  fill: string;
  align: 'left' | 'center' | 'right';
  isSelected: boolean;
  onTextChange: (text: string) => void;
}

const TextElement: React.FC<TextElementProps> = ({ onTextChange, isSelected, ...props }) => {
  const textRef = useRef<Konva.Text>(null);

  const handleDblClick = () => {
    if (!textRef.current) return;

    // Create textarea for editing
    const textPosition = textRef.current.absolutePosition();
    const stage = textRef.current.getStage();
    if (!stage) return;

    // Calculate position accounting for stage scale and position
    const stageScale = stage.scaleX();
    const areaPosition = {
      x: stage.container().offsetLeft + textPosition.x,
      y: stage.container().offsetTop + textPosition.y,
    };

    // Create and configure textarea
    const textarea = document.createElement('textarea');
    document.body.appendChild(textarea);

    textarea.value = props.text;
    textarea.style.position = 'absolute';
    textarea.style.top = `${areaPosition.y}px`;
    textarea.style.left = `${areaPosition.x}px`;
    textarea.style.width = `${(props.width ?? 0) * stageScale}px`; // Account for stage scale
    textarea.style.height = 'auto';
    textarea.style.fontSize = `${props.fontSize * stageScale}px`; // Account for stage scale
    textarea.style.border = 'none';
    textarea.style.padding = '0px';
    textarea.style.margin = '0px';
    textarea.style.overflow = 'hidden';
    textarea.style.background = 'none';
    textarea.style.outline = 'none';
    textarea.style.resize = 'none';
    textarea.style.fontFamily = props.fontFamily;
    textarea.style.color = props.fill;
    textarea.style.lineHeight = '1.2';
    textarea.style.textAlign = 'left';

    // Focus the textarea
    textarea.focus();

    // Function to remove the textarea
    function removeTextarea() {
      textarea.parentNode?.removeChild(textarea);
      window.removeEventListener('click', handleOutsideClick);
    }

    // Handle clicks outside the textarea
    function handleOutsideClick(e: MouseEvent) {
      if (e.target !== textarea) {
        // Only update if text has changed
        if (textarea.value !== props.text) {
          onTextChange(textarea.value);
        }
        removeTextarea();
      }
    }

    // Handle keyboard events
    textarea.addEventListener('keydown', (e: KeyboardEvent) => {
      // Enter without shift commits the change
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        if (textarea.value !== props.text) {
          onTextChange(textarea.value);
        }
        removeTextarea();
      }
      // Escape cancels the edit
      if (e.key === 'Escape') {
        removeTextarea();
      }
      // Allow textarea to grow with content
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
    });

    // Add input event to resize textarea as user types
    textarea.addEventListener('input', () => {
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
    });

    // Delay adding the click listener to prevent immediate triggering
    setTimeout(() => {
      window.addEventListener('click', handleOutsideClick);
    }, 10);
  };
  return (
    <>
      <Text ref={textRef} {...props} onDblClick={handleDblClick} onDblTap={handleDblClick} />

      {isSelected && (
        <Transformer
          borderStroke='#9EA581'
          borderStrokeWidth={2}
          padding={5}
          borderDash={[5, 5]}
          anchorSize={0}
          rotateEnabled={false}
          resizeEnabled={false}
          enabledAnchors={[]}
          nodes={[textRef.current]}
        />
      )}
    </>
  );
};

TextElement.displayName = 'TextElement';

export default memo(TextElement);
