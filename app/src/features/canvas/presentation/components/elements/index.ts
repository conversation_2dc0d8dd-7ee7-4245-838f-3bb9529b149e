/**
 * 🎨 Canvas Elements Index - ORGANIZED STRUCTURE
 *
 * @description Exports all modular canvas element components from organized folders
 * @responsibility Clean imports for canvas elements
 * @ai_context Central export point for all canvas element components
 */

// 📁 Main Element Containers
export { default as ImageElements } from './images/ImageElements';
export { default as TextElements } from './text/TextElements';
export { default as HtmlElements } from './html/HtmlElements';

export type { ImageElementsProps } from './images/ImageElements';
export type { TextElementsProps } from './text/TextElements';
export type { HtmlElementsProps } from './html/HtmlElements';

// 📁 Image Components
export { default as URLImage } from './images/URLImage';
export { default as ImageHistoryModal } from './images/ImageHistoryModal';
export { default as ImageDebugVisualizer } from './images/ImageDebugVisualizer';

// 📁 Text Components
export { default as TextElement } from './text/TextElement';

// 📁 HTML Components
export { default as HTMLElement } from './html/HTMLElement';
export { default as HtmlElementContextMenu } from './html/HtmlElementContextMenu';
export { default as HtmlEditMenu } from './html/HtmlEditMenu';
export { default as HtmlApproveButton } from './html/HtmlApproveButton';
export { default as HtmlSideChat } from './html/HtmlSideChat';

// 📁 Newsletter Components
export { default as NewsletterElement } from './newsletter/NewsletterElement';

// 📁 Menu Components
export { default as KonvaImageMenu } from './menus/KonvaImageMenu';

// 📁 Chat Components
export { default as KonvaSideChat } from './chat/KonvaSideChat';

// 📁 Canvas Elements
export { default as CanvasElements } from './CanvasElements';
