/**
 * 🖼️ Reference Modal Component - MODULAR VERSION
 * @description Modal for managing reference images instead of sidebar
 * @responsibility Display and manage reference images in a modal interface
 * @ai_context Modular replacement for old canvas reference sidebar
 */

import React, { useState, useEffect } from 'react';
import { X, Plus, Image as ImageIcon, Trash2 } from 'lucide-react';
import { useQuery, useAction } from 'wasp/client/operations';
import {
  getCanvasReferences,
  createCanvasReference,
  deleteCanvasReference,
  getModelProduct,
} from 'wasp/client/operations';
import { useReferenceImages } from '../../hooks';

/**
 * 🖼️ Reference Item Interface
 */
interface ReferenceItem {
  id: string;
  thumbUrl: string;
  fileId?: string;
}

/**
 * 🖼️ ReferenceModal Props
 */
interface ReferenceModalProps {
  isOpen: boolean;
  onClose: () => void;
  canvasId?: string;
}

/**
 * 🖼️ ReferenceModal Component
 * Modal for managing reference images
 */
const ReferenceModal: React.FC<ReferenceModalProps> = ({ isOpen, onClose, canvasId }) => {
  const [activeTab, setActiveTab] = useState<'canvas' | 'product'>('canvas');
  const [draggedImage, setDraggedImage] = useState<string | null>(null);

  // Get model ID from localStorage (following old pattern)
  const modelId = localStorage.getItem('selectedModelId') || canvasId || '';

  // Get reference images hook
  const { addReferenceImage, removeReferenceImage } = useReferenceImages(`voice-agent-${modelId}`);

  // Fetch canvas references
  const { data: canvasReferences = [], isLoading: isLoadingRefs } = useQuery(
    getCanvasReferences,
    { whiteboardId: modelId },
    { enabled: !!modelId && isOpen }
  );

  // Fetch current product for product references
  const { data: currentProduct } = useQuery(getModelProduct, { modelId }, { enabled: !!modelId && isOpen });

  // Actions
  const createCanvasReferenceAction = useAction(createCanvasReference);
  const deleteCanvasReferenceAction = useAction(deleteCanvasReference);

  // Handle add reference image (would integrate with media library)
  const handleAddReference = async () => {
    // TODO: Integrate with media library modal
    console.log('[ReferenceModal] Add reference clicked - media library integration needed');

    // For now, just show a message that this feature is coming
    alert('Media library integration coming soon! For now, use right-click "Set as Reference" on canvas images.');
  };

  // Handle remove reference
  const handleRemoveReference = async (referenceId: string) => {
    try {
      await deleteCanvasReferenceAction({ id: referenceId });
      console.log('[ReferenceModal] Removed reference:', referenceId);
    } catch (error) {
      console.error('[ReferenceModal] Error removing reference:', error);
    }
  };

  // Handle drag start
  const handleDragStart = (e: React.DragEvent, imageUrl: string) => {
    setDraggedImage(imageUrl);
    e.dataTransfer.setData('text/plain', imageUrl);
    e.dataTransfer.setData(
      'application/json',
      JSON.stringify({
        type: 'reference-image',
        url: imageUrl,
      })
    );
    console.log('[ReferenceModal] Started dragging image:', imageUrl);
  };

  // Handle drag end
  const handleDragEnd = () => {
    setDraggedImage(null);
  };

  // Don't render if not open
  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
      <div className='bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[80vh] overflow-hidden'>
        {/* Header */}
        <div className='flex items-center justify-between p-4 border-b border-gray-200'>
          <h2 className='text-lg font-semibold text-gray-900'>Reference Images</h2>
          <button onClick={onClose} className='p-2 hover:bg-gray-100 rounded-lg transition-colors'>
            <X className='w-5 h-5 text-gray-500' />
          </button>
        </div>

        {/* Tabs */}
        <div className='flex border-b border-gray-200'>
          <button
            onClick={() => setActiveTab('canvas')}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
              activeTab === 'canvas'
                ? 'text-[#566B46] border-b-2 border-[#9EA581] bg-[#F9F7ED]'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Canvas References
          </button>
          <button
            onClick={() => setActiveTab('product')}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
              activeTab === 'product'
                ? 'text-[#566B46] border-b-2 border-[#9EA581] bg-[#F9F7ED]'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Product Images
          </button>
        </div>

        {/* Content */}
        <div className='p-4 max-h-96 overflow-y-auto'>
          {activeTab === 'canvas' ? (
            /* Canvas References Tab */
            <div>
              {/* Add Button */}
              <button
                onClick={handleAddReference}
                className='w-full mb-4 p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-[#9EA581] hover:bg-[#F9F7ED] transition-colors flex items-center justify-center gap-2'
              >
                <Plus className='w-5 h-5 text-gray-400' />
                <span className='text-gray-600'>Add Reference Image</span>
              </button>

              {/* Reference Grid */}
              {isLoadingRefs ? (
                <div className='text-center py-8 text-gray-500'>Loading references...</div>
              ) : canvasReferences.length > 0 ? (
                <div className='grid grid-cols-3 gap-4'>
                  {canvasReferences.map((ref: ReferenceItem) => (
                    <div
                      key={ref.id}
                      className='relative group aspect-square rounded-lg overflow-hidden border-2 border-blue-500 bg-gray-100'
                    >
                      <img
                        src={ref.thumbUrl}
                        alt='Canvas Reference'
                        className='w-full h-full object-cover cursor-grab'
                        draggable
                        onDragStart={(e) => handleDragStart(e, ref.thumbUrl)}
                        onDragEnd={handleDragEnd}
                      />

                      {/* Remove Button */}
                      <button
                        onClick={() => handleRemoveReference(ref.id)}
                        className='absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600'
                        title='Remove reference'
                      >
                        <Trash2 className='w-3 h-3' />
                      </button>

                      {/* Drag Overlay */}
                      {draggedImage === ref.thumbUrl && (
                        <div className='absolute inset-0 bg-blue-500 bg-opacity-20 flex items-center justify-center'>
                          <span className='text-white text-sm font-medium'>Dragging...</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className='text-center py-8 text-gray-500'>
                  <ImageIcon className='w-12 h-12 mx-auto mb-2 text-gray-300' />
                  <p>No reference images yet</p>
                  <p className='text-sm'>Add images to use as references while designing</p>
                </div>
              )}
            </div>
          ) : (
            /* Product Images Tab */
            <div>
              {currentProduct?.images && currentProduct.images.length > 0 ? (
                <div className='grid grid-cols-3 gap-4'>
                  {currentProduct.images.map((imageUrl: string, index: number) => (
                    <div
                      key={index}
                      className='relative group aspect-square rounded-lg overflow-hidden border-2 border-[#676D50] bg-gray-100'
                    >
                      <img
                        src={imageUrl}
                        alt={`Product image ${index + 1}`}
                        className='w-full h-full object-cover cursor-grab'
                        draggable
                        onDragStart={(e) => handleDragStart(e, imageUrl)}
                        onDragEnd={handleDragEnd}
                      />

                      {/* Drag Overlay */}
                      {draggedImage === imageUrl && (
                        <div className='absolute inset-0 bg-green-500 bg-opacity-20 flex items-center justify-center'>
                          <span className='text-white text-sm font-medium'>Dragging...</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className='text-center py-8 text-gray-500'>
                  <ImageIcon className='w-12 h-12 mx-auto mb-2 text-gray-300' />
                  <p>No product images</p>
                  <p className='text-sm'>Select a product to see its images</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className='p-4 border-t border-gray-200 bg-gray-50'>
          <p className='text-sm text-gray-600'>💡 Drag images from here onto the canvas to add them as elements</p>
        </div>
      </div>
    </div>
  );
};

export default ReferenceModal;
