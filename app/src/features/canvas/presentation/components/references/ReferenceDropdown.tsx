/**
 * 🖼️ Reference Dropdown Component - MODULAR VERSION
 * @description Dropdown menu for managing reference images (appears above button)
 * @responsibility Display and manage reference images in a dropdown interface
 * @ai_context Modular replacement for reference modal - cleaner UX
 */

import React, { useState, useRef, useEffect } from 'react';
import { Plus, Image as ImageIcon, Trash2, Package, Star } from 'lucide-react';
import { useQuery, useAction } from 'wasp/client/operations';
import { getCanvasReferences, createCanvasReference, deleteCanvasReference, getProducts } from 'wasp/client/operations';
import { useOrganizationState } from '../../../../../organization/store';
import { useReferenceImages } from '../../hooks';
import { useElementDomainStore } from '../../../domain/state';

/**
 * 🖼️ Reference Item Interface
 */
interface ReferenceItem {
  id: string;
  thumbUrl: string;
  fileId?: string;
}

/**
 * 🖼️ ReferenceDropdown Props
 */
interface ReferenceDropdownProps {
  isOpen: boolean;
  onClose: () => void;
  buttonRef: React.RefObject<HTMLButtonElement>;
  canvasId?: string;
}

/**
 * 🖼️ ReferenceDropdown Component
 * Dropdown menu for managing reference images
 */
const ReferenceDropdown: React.FC<ReferenceDropdownProps> = ({ isOpen, onClose, buttonRef, canvasId }) => {
  const [activeTab, setActiveTab] = useState<'canvas' | 'product'>('canvas');
  const [draggedImage, setDraggedImage] = useState<string | null>(null);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { selectedOrganizationId } = useOrganizationState();

  // Get all canvas images from domain store
  const canvasImages = useElementDomainStore((state) => state.images);

  // Get product ID from localStorage (following old pattern)
  const selectedProductId = localStorage.getItem('selectedModelId') || canvasId || '';

  // Get reference images hook
  const { addReferenceImage, removeReferenceImage } = useReferenceImages(`voice-agent-${selectedProductId}`);

  // Fetch canvas references
  const { data: canvasReferences = [], isLoading: isLoadingRefs } = useQuery(
    getCanvasReferences,
    { whiteboardId: selectedProductId },
    { enabled: !!selectedProductId && isOpen }
  );

  // Fetch all products to find current product
  const { data: products = [] } = useQuery(
    getProducts,
    { organizationId: selectedOrganizationId },
    { enabled: !!selectedOrganizationId && isOpen }
  );

  // Find current product from the products list
  const currentProduct = selectedProductId ? products.find((p: any) => p.id.toString() === selectedProductId) : null;

  // Actions
  const createCanvasReferenceAction = useAction(createCanvasReference);
  const deleteCanvasReferenceAction = useAction(deleteCanvasReference);

  // Calculate position when dropdown opens
  useEffect(() => {
    if (isOpen && buttonRef.current) {
      const buttonRect = buttonRef.current.getBoundingClientRect();
      const dropdownHeight = 300; // Smaller dropdown height
      const dropdownWidth = 320;

      // Position right above the button with small gap
      const calculatedTop = buttonRect.top - dropdownHeight - 5; // 5px gap above button
      const calculatedLeft = buttonRect.left + buttonRect.width / 2 - dropdownWidth / 2;

      setPosition({
        top: calculatedTop,
        left: calculatedLeft,
      });
    }
  }, [isOpen, buttonRef]);

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, onClose, buttonRef]);

  // Check if an image URL is currently a reference
  const isImageReference = (imageUrl: string) => {
    return canvasReferences.some((ref: any) => ref.thumbUrl === imageUrl);
  };

  // Handle toggling canvas image as reference
  const handleToggleCanvasReference = async (imageUrl: string) => {
    const isCurrentlyReference = isImageReference(imageUrl);

    if (isCurrentlyReference) {
      // Remove from references
      const referenceToRemove = canvasReferences.find((ref: any) => ref.thumbUrl === imageUrl);
      if (referenceToRemove) {
        try {
          await deleteCanvasReferenceAction({ id: referenceToRemove.id });
          console.log('[ReferenceDropdown] Removed canvas image from references:', imageUrl);
        } catch (error) {
          console.error('[ReferenceDropdown] Error removing reference:', error);
        }
      }
    } else {
      // Add to references
      try {
        await createCanvasReferenceAction({
          whiteboardId: selectedProductId,
          thumbUrl: imageUrl,
        });
        console.log('[ReferenceDropdown] Added canvas image to references:', imageUrl);
      } catch (error) {
        console.error('[ReferenceDropdown] Error adding reference:', error);
      }
    }
  };

  // Handle toggling product image as reference
  const handleToggleProductReference = async (imageUrl: string) => {
    const isCurrentlyReference = isImageReference(imageUrl);

    if (isCurrentlyReference) {
      // Remove from references
      const referenceToRemove = canvasReferences.find((ref: any) => ref.thumbUrl === imageUrl);
      if (referenceToRemove) {
        try {
          await deleteCanvasReferenceAction({ id: referenceToRemove.id });
          console.log('[ReferenceDropdown] Removed product image from references:', imageUrl);
        } catch (error) {
          console.error('[ReferenceDropdown] Error removing reference:', error);
        }
      }
    } else {
      // Add to references
      try {
        await createCanvasReferenceAction({
          whiteboardId: selectedProductId,
          thumbUrl: imageUrl,
        });
        console.log('[ReferenceDropdown] Added product image to references:', imageUrl);
      } catch (error) {
        console.error('[ReferenceDropdown] Error adding reference:', error);
      }
    }
  };

  // Handle remove reference
  const handleRemoveReference = async (referenceId: string) => {
    try {
      await deleteCanvasReferenceAction({ id: referenceId });
      console.log('[ReferenceDropdown] Removed reference:', referenceId);
    } catch (error) {
      console.error('[ReferenceDropdown] Error removing reference:', error);
    }
  };

  // Handle drag start
  const handleDragStart = (e: React.DragEvent, imageUrl: string) => {
    setDraggedImage(imageUrl);
    e.dataTransfer.setData('text/plain', imageUrl);
    e.dataTransfer.setData(
      'application/json',
      JSON.stringify({
        type: 'reference-image',
        url: imageUrl,
      })
    );
    console.log('[ReferenceDropdown] Started dragging image:', imageUrl);
  };

  // Handle drag end
  const handleDragEnd = () => {
    setDraggedImage(null);
  };

  // Don't render if not open
  if (!isOpen) return null;

  return (
    <div
      ref={dropdownRef}
      className='fixed bg-white border border-[#E8E4D4] rounded-lg shadow-xl z-50 w-80 h-72 overflow-hidden flex flex-col'
      style={{
        top: `${position.top}px`,
        left: `${position.left}px`,
      }}
    >
      {/* Header */}
      <div className='p-3 border-b border-[#E8E4D4] bg-[#F9F7ED]'>
        <h3 className='text-sm font-semibold text-[#566B46]'>Reference Images</h3>
      </div>

      {/* Tabs */}
      <div className='flex border-b border-[#E8E4D4]'>
        <button
          onClick={() => setActiveTab('canvas')}
          className={`flex-1 px-3 py-2 text-xs font-medium transition-colors ${
            activeTab === 'canvas'
              ? 'text-[#566B46] border-b-2 border-[#9EA581] bg-[#F9F7ED]'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Canvas
        </button>
        <button
          onClick={() => setActiveTab('product')}
          className={`flex-1 px-3 py-2 text-xs font-medium transition-colors ${
            activeTab === 'product'
              ? 'text-[#566B46] border-b-2 border-[#9EA581] bg-[#F9F7ED]'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Product
        </button>
      </div>

      {/* Content */}
      <div className='p-3 flex-1 overflow-y-auto' style={{ maxHeight: '180px' }}>
        {activeTab === 'canvas' ? (
          /* Canvas Images Tab - Show ALL canvas images with stars on referenced ones */
          <div>
            {canvasImages.length > 0 ? (
              <div className='grid grid-cols-3 gap-2'>
                {canvasImages.map((image: any) => {
                  const isReference = isImageReference(image.url);
                  return (
                    <div
                      key={image.id}
                      className='relative group aspect-square rounded overflow-hidden border border-[#E8E4D4] bg-gray-100 cursor-pointer'
                      onClick={() => handleToggleCanvasReference(image.url)}
                    >
                      <img
                        src={image.url}
                        alt='Canvas Image'
                        className='w-full h-full object-cover'
                        draggable
                        onDragStart={(e) => handleDragStart(e, image.url)}
                        onDragEnd={handleDragEnd}
                      />

                      {/* Reference Star */}
                      <div className='absolute top-1 left-1'>
                        <Star
                          className={`w-4 h-4 ${
                            isReference ? 'text-yellow-500 fill-yellow-500' : 'text-white opacity-60 hover:opacity-100'
                          } transition-all`}
                        />
                      </div>

                      {/* Drag Overlay */}
                      {draggedImage === image.url && (
                        <div className='absolute inset-0 bg-[#9EA581] bg-opacity-20 flex items-center justify-center'>
                          <span className='text-white text-xs font-medium'>Dragging...</span>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className='text-center py-6 text-gray-500'>
                <ImageIcon className='w-8 h-8 mx-auto mb-2 text-gray-300' />
                <p className='text-xs'>No canvas images yet</p>
                <p className='text-xs text-gray-400'>Add images to canvas first</p>
              </div>
            )}
          </div>
        ) : (
          /* Product Images Tab - Show ALL product images with clickable stars */
          <div>
            {currentProduct?.images && currentProduct.images.length > 0 ? (
              <div className='grid grid-cols-3 gap-2'>
                {currentProduct.images.map((imageUrl: string, index: number) => {
                  const isReference = isImageReference(imageUrl);
                  return (
                    <div
                      key={index}
                      className='relative group aspect-square rounded overflow-hidden border border-[#9EA581] bg-gray-100 cursor-pointer'
                      onClick={() => handleToggleProductReference(imageUrl)}
                    >
                      <img
                        src={imageUrl}
                        alt={`Product image ${index + 1}`}
                        className='w-full h-full object-cover'
                        draggable
                        onDragStart={(e) => handleDragStart(e, imageUrl)}
                        onDragEnd={handleDragEnd}
                      />

                      {/* Reference Star */}
                      <div className='absolute top-1 left-1'>
                        <Star
                          className={`w-4 h-4 ${
                            isReference ? 'text-yellow-500 fill-yellow-500' : 'text-white opacity-60 hover:opacity-100'
                          } transition-all`}
                        />
                      </div>

                      {/* Drag Overlay */}
                      {draggedImage === imageUrl && (
                        <div className='absolute inset-0 bg-[#9EA581] bg-opacity-20 flex items-center justify-center'>
                          <span className='text-white text-xs font-medium'>Dragging...</span>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className='text-center py-6 text-gray-500'>
                <Package className='w-8 h-8 mx-auto mb-2 text-gray-300' />
                <p className='text-xs'>No product images</p>
                <p className='text-xs text-gray-400'>Select a product first</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className='p-2 border-t border-[#E8E4D4] bg-[#F9F7ED]'>
        <p className='text-xs text-[#566B46]'>💡 Drag images onto canvas</p>
      </div>
    </div>
  );
};

export default ReferenceDropdown;
