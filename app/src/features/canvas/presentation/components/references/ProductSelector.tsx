/**
 * 📦 Product Selector Component - MODULAR VERSION
 * @description Shows current product and allows switching between products
 * @responsibility Display current product and provide product selection UI
 * @ai_context Modular replacement for old canvas product selector
 */

import React, { useState, useRef, useEffect } from 'react';
import { Package, ChevronDown, Search, Image, X } from 'lucide-react';
import { useQuery, useAction } from 'wasp/client/operations';
import { getProducts, createCanvasReference } from 'wasp/client/operations';
import { useAuth } from 'wasp/client/auth';
import { useOrganizationState } from '../../../../../organization/store';

/**
 * 📦 Product Interface (matching actual Product entity)
 */
interface Product {
  id: number;
  name: string;
  productType: string;
  images: string[];
  brandName?: string | null;
  description: string;
  price: number;
  organizationId: string;
}

/**
 * 📦 ProductSelector Props
 */
interface ProductSelectorProps {
  className?: string;
  onProductChange?: (product: Product | null) => void;
}

/**
 * 📦 ProductSelector Component
 * Shows current product and allows switching between products
 */
const ProductSelector: React.FC<ProductSelectorProps> = ({ className = '', onProductChange }) => {
  const { data: user } = useAuth();
  const { selectedOrganizationId } = useOrganizationState();
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showDropdown, setShowDropdown] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Get current product ID from localStorage
  const selectedProductId = localStorage.getItem('selectedModelId');

  // Fetch all products for selection
  const { data: products = [], isLoading: isLoadingProducts } = useQuery(
    getProducts,
    { organizationId: selectedOrganizationId },
    { enabled: !!selectedOrganizationId }
  );

  // Canvas reference action
  const createCanvasReferenceAction = useAction(createCanvasReference);

  // Find current product from the products list
  const currentProduct = selectedProductId
    ? products.find((p: Product) => p.id.toString() === selectedProductId)
    : null;

  // Filter products based on search
  const filteredProducts = products.filter(
    (product: Product) =>
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.productType?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Set current product when data loads
  useEffect(() => {
    if (currentProduct) {
      setSelectedProduct(currentProduct);
    }
  }, [currentProduct]);

  // Handle product selection
  const handleProductSelect = async (product: Product) => {
    setSelectedProduct(product);
    setShowDropdown(false);
    setSearchQuery('');

    // Store in localStorage (following old pattern)
    localStorage.setItem('selectedModelId', product.id.toString());

    // Auto-add first product image as reference for AI generation
    if (product.images && product.images.length > 0) {
      try {
        await createCanvasReferenceAction({
          whiteboardId: product.id.toString(),
          thumbUrl: product.images[0],
        });
        console.log('[ProductSelector] Auto-added first product image as reference:', product.images[0]);
      } catch (error) {
        console.error('[ProductSelector] Error adding product image as reference:', error);
      }
    }

    // Notify parent component
    onProductChange?.(product);

    console.log('[ProductSelector] Selected product:', product.name);
  };

  // Handle clear product
  const handleClearProduct = () => {
    setSelectedProduct(null);
    localStorage.removeItem('selectedModelId');
    onProductChange?.(null);
    console.log('[ProductSelector] Cleared product selection');
  };

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Current Product Display */}
      {selectedProduct ? (
        <div className='bg-[#F9F7ED] border border-[#E8E4D4] rounded-lg px-3 py-2 shadow-sm'>
          <div className='flex items-center gap-2'>
            {/* Product Image */}
            <div className='w-8 h-8 rounded overflow-hidden bg-gray-100 flex-shrink-0'>
              {selectedProduct.images?.[0] ? (
                <img
                  src={selectedProduct.images[0]}
                  alt={selectedProduct.name}
                  className='w-full h-full object-cover'
                />
              ) : (
                <div className='w-full h-full flex items-center justify-center'>
                  <Package className='w-4 h-4 text-gray-400' />
                </div>
              )}
            </div>

            {/* Product Info */}
            <div className='flex-1 min-w-0'>
              <div className='text-sm font-medium text-[#566B46] truncate'>{selectedProduct.name}</div>
              {selectedProduct.productType && (
                <div className='text-xs text-[#849068] truncate'>{selectedProduct.productType}</div>
              )}
            </div>

            {/* Actions */}
            <div className='flex items-center gap-1'>
              <button
                onClick={() => setShowDropdown(!showDropdown)}
                className='p-1 hover:bg-[#E8E4D4] rounded transition-colors'
                title='Change product'
              >
                <ChevronDown
                  className={`w-4 h-4 text-[#566B46] transition-transform ${showDropdown ? 'rotate-180' : ''}`}
                />
              </button>
              <button
                onClick={handleClearProduct}
                className='p-1 hover:bg-[#E8E4D4] rounded transition-colors'
                title='Clear product'
              >
                <X className='w-4 h-4 text-[#566B46]' />
              </button>
            </div>
          </div>
        </div>
      ) : (
        /* No Product Selected */
        <button
          onClick={() => setShowDropdown(!showDropdown)}
          className='bg-[#F9F7ED] border border-[#E8E4D4] rounded-lg px-3 py-2 shadow-sm hover:shadow-md transition-all duration-200 flex items-center gap-2'
        >
          <Package className='w-4 h-4 text-[#566B46]' />
          <span className='text-sm text-[#566B46]'>Select Product</span>
          <ChevronDown className={`w-4 h-4 text-[#566B46] transition-transform ${showDropdown ? 'rotate-180' : ''}`} />
        </button>
      )}

      {/* Dropdown */}
      {showDropdown && (
        <div className='absolute top-full mt-2 w-80 bg-white border border-[#E8E4D4] rounded-lg shadow-xl z-50 max-h-96 overflow-hidden'>
          {/* Search */}
          <div className='p-3 border-b border-[#E8E4D4]'>
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4' />
              <input
                type='text'
                placeholder='Search products...'
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className='w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#9EA581] focus:border-transparent'
              />
            </div>
          </div>

          {/* Product List */}
          <div className='max-h-64 overflow-y-auto'>
            {isLoadingProducts ? (
              <div className='p-4 text-center text-gray-500'>Loading products...</div>
            ) : filteredProducts.length > 0 ? (
              filteredProducts.map((product: Product) => (
                <div
                  key={product.id}
                  onClick={() => handleProductSelect(product)}
                  className='flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors'
                >
                  {/* Product Image */}
                  <div className='w-10 h-10 rounded overflow-hidden bg-gray-100 flex-shrink-0'>
                    {product.images?.[0] ? (
                      <img src={product.images[0]} alt={product.name} className='w-full h-full object-cover' />
                    ) : (
                      <div className='w-full h-full flex items-center justify-center'>
                        <Package className='w-4 h-4 text-gray-400' />
                      </div>
                    )}
                  </div>

                  {/* Product Info */}
                  <div className='ml-3 flex-1'>
                    <div className='font-medium text-gray-900'>{product.name}</div>
                    <div className='text-sm text-gray-500 flex items-center justify-between'>
                      <span>{product.productType}</span>
                      {product.images && (
                        <span className='text-xs bg-gray-100 px-2 py-1 rounded-full'>
                          {product.images.length} {product.images.length === 1 ? 'image' : 'images'}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className='p-4 text-center text-gray-500'>No products found</div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductSelector;
