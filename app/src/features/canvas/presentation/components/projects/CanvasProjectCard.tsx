/**
 * 🎨 Canvas Project Card Component
 *
 * @description Individual card for displaying canvas project information
 * @responsibility Shows canvas screenshot, name, and actions
 * @dependencies Canvas entities
 * @ai_context Follows the same pattern as BrandKitCard
 */

import React, { useState } from 'react';
import { MoreVertical, Edit, Trash2, ExternalLink, Calendar, Ruler } from 'lucide-react';

/**
 * 🎨 Canvas Project Card Props
 * @ai_context Props for the canvas project card component
 */
interface CanvasProjectCardProps {
  canvas: any; // TODO: Replace with proper Canvas type
  onDelete: (canvasId: string) => void;
}

/**
 * 🎨 Canvas Project Card Component
 *
 * Individual card displaying canvas project with screenshot and actions
 */
export const CanvasProjectCard: React.FC<CanvasProjectCardProps> = ({ canvas, onDelete }) => {
  const [showDropdown, setShowDropdown] = useState(false);

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't navigate if clicking on dropdown or actions
    if ((e.target as HTMLElement).closest('.dropdown-trigger') || (e.target as HTMLElement).closest('.card-actions')) {
      return;
    }

    // Navigate to canvas editor
    window.location.href = `/canvas/${canvas.id}`;
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDropdown(false);
    onDelete(canvas.id);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  };

  return (
    <div
      className='group bg-white dark:bg-gray-800 rounded-2xl border border-[#B5B178]/20 dark:border-gray-600 overflow-hidden hover:shadow-xl hover:shadow-[#676D50]/10 transition-all duration-300 cursor-pointer'
      onClick={handleCardClick}
    >
      {/* Canvas Screenshot */}
      <div className='relative aspect-[4/3] bg-gradient-to-br from-[#676D50]/10 to-[#849068]/10 overflow-hidden'>
        {canvas.thumbnail ? (
          <img
            src={canvas.thumbnail}
            alt={canvas.name}
            className='w-full h-full object-cover group-hover:scale-105 transition-transform duration-300'
          />
        ) : (
          <div className='w-full h-full flex items-center justify-center'>
            <div className='text-center'>
              <span className='text-6xl mb-2 block'>🎨</span>
              <p className='text-[#676D50]/60 dark:text-gray-400 text-sm'>No preview</p>
            </div>
          </div>
        )}

        {/* Overlay with actions */}
        <div className='absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300'>
          <div className='absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300'>
            <div className='relative card-actions'>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowDropdown(!showDropdown);
                }}
                className='dropdown-trigger w-8 h-8 bg-white/90 dark:bg-gray-800/90 rounded-full flex items-center justify-center hover:bg-white dark:hover:bg-gray-800 transition-colors'
              >
                <MoreVertical size={16} className='text-[#676D50] dark:text-gray-400' />
              </button>

              {/* Dropdown Menu */}
              {showDropdown && (
                <div className='absolute top-full right-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-[#B5B178]/20 dark:border-gray-600 py-2 z-10'>
                  <a
                    href={`/canvas/${canvas.id}`}
                    className='flex items-center gap-3 px-4 py-2 text-[#676D50] dark:text-gray-300 hover:bg-[#F6F3E5] dark:hover:bg-gray-700 transition-colors'
                    onClick={(e) => e.stopPropagation()}
                  >
                    <ExternalLink size={16} />
                    <span>Open Canvas</span>
                  </a>
                  <button
                    onClick={handleDelete}
                    className='flex items-center gap-3 px-4 py-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors w-full text-left'
                  >
                    <Trash2 size={16} />
                    <span>Delete</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Canvas Info */}
      <div className='p-6'>
        <div className='mb-4'>
          <h3 className='font-display text-xl font-semibold text-[#676D50] dark:text-white mb-2 line-clamp-1'>
            {canvas.name}
          </h3>
          {canvas.description && (
            <p className='text-[#676D50]/70 dark:text-gray-400 text-sm line-clamp-2'>{canvas.description}</p>
          )}
        </div>

        {/* Canvas Metadata */}
        <div className='space-y-2'>
          <div className='flex items-center gap-2 text-xs text-[#676D50]/60 dark:text-gray-500'>
            <Calendar size={12} />
            <span>Updated {formatDate(canvas.updatedAt)}</span>
          </div>
          <div className='flex items-center gap-2 text-xs text-[#676D50]/60 dark:text-gray-500'>
            <Ruler size={12} />
            <span>Canvas Project</span>
          </div>
        </div>

        {/* Action Button */}
        <div className='mt-4 pt-4 border-t border-[#B5B178]/20 dark:border-gray-600'>
          <button
            onClick={(e) => {
              e.stopPropagation();
              window.location.href = `/canvas/${canvas.id}`;
            }}
            className='w-full px-4 py-2 bg-gradient-to-r from-[#676D50] to-[#849068] hover:from-[#849068] hover:to-[#676D50] text-white rounded-xl transition-all duration-300 font-medium text-sm shadow-sm hover:shadow-md transform hover:scale-[1.02]'
          >
            Open Canvas
          </button>
        </div>
      </div>
    </div>
  );
};

export default CanvasProjectCard;
