/**
 * 🎨 Canvas Project Grid Component
 *
 * @description Displays canvas projects in a responsive grid layout
 * @responsibility Renders canvas project cards with screenshots and actions
 * @dependencies Canvas entities and components
 * @ai_context Follows the same pattern as BrandKitGrid
 */

import React from 'react';
import { CanvasProjectCard } from './CanvasProjectCard';

/**
 * 🎨 Canvas Project Grid Props
 * @ai_context Props for the canvas project grid component
 */
interface CanvasProjectGridProps {
  canvasProjects: any[]; // TODO: Replace with proper Canvas type
  viewMode?: 'grid' | 'list';
  onDelete: (canvasId: string) => void;
}

/**
 * 🎨 Canvas Project Grid Component
 *
 * Renders a responsive grid of canvas project cards with screenshots and actions
 */
export const CanvasProjectGrid: React.FC<CanvasProjectGridProps> = ({
  canvasProjects,
  viewMode = 'grid',
  onDelete,
}) => {
  if (viewMode === 'list') {
    return (
      <div className='space-y-4'>
        {canvasProjects.map((canvas) => (
          <div
            key={canvas.id}
            className='bg-white dark:bg-gray-800 rounded-xl border border-[#B5B178]/20 dark:border-gray-600 p-6 hover:shadow-lg transition-all duration-300'
          >
            <div className='flex items-center gap-6'>
              {/* Canvas Screenshot */}
              <div className='w-24 h-24 flex-shrink-0 bg-gradient-to-br from-[#676D50]/10 to-[#849068]/10 rounded-xl overflow-hidden'>
                {canvas.thumbnail ? (
                  <img src={canvas.thumbnail} alt={canvas.name} className='w-full h-full object-cover' />
                ) : (
                  <div className='w-full h-full flex items-center justify-center'>
                    <span className='text-2xl'>🎨</span>
                  </div>
                )}
              </div>

              {/* Canvas Info */}
              <div className='flex-1 min-w-0'>
                <h3 className='font-display text-xl font-semibold text-[#676D50] dark:text-white mb-2 truncate'>
                  {canvas.name}
                </h3>
                {canvas.description && (
                  <p className='text-[#676D50]/70 dark:text-gray-400 text-sm mb-2 line-clamp-2'>{canvas.description}</p>
                )}
                <div className='flex items-center gap-4 text-xs text-[#676D50]/60 dark:text-gray-500'>
                  <span>Updated {new Date(canvas.updatedAt).toLocaleDateString()}</span>
                  <span>•</span>
                  <span>Canvas Project</span>
                </div>
              </div>

              {/* Actions */}
              <div className='flex items-center gap-2'>
                <a
                  href={`/canvas/${canvas.id}`}
                  className='px-4 py-2 bg-[#676D50] text-white rounded-lg hover:bg-[#849068] transition-colors text-sm font-medium'
                >
                  Open
                </a>
                <button
                  onClick={() => onDelete(canvas.id)}
                  className='px-4 py-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors text-sm font-medium'
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
      {canvasProjects.map((canvas) => (
        <CanvasProjectCard key={canvas.id} canvas={canvas} onDelete={onDelete} />
      ))}
    </div>
  );
};

export default CanvasProjectGrid;
