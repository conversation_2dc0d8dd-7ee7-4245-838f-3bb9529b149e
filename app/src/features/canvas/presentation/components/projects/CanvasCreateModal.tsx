/**
 * 🎨 Canvas Create Modal Component
 *
 * @description Modal for creating new canvas projects
 * @responsibility Handles canvas creation form and validation
 * @dependencies Canvas actions and entities
 * @ai_context Follows the same pattern as BrandKitImportModal
 */

import React, { useState } from 'react';
import { useAction, createCanvas } from 'wasp/client/operations';
import { X, Palette, FileText } from 'lucide-react';
import { useOrganizationState } from '../../../../../organization/store';

/**
 * 🎨 Canvas Create Modal Props
 * @ai_context Props for the canvas create modal component
 */
interface CanvasCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (canvasId: string) => void;
}

/**
 * 🎨 Canvas Create Modal Component
 *
 * Modal for creating new canvas projects with customizable settings
 */
export const CanvasCreateModal: React.FC<CanvasCreateModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const { selectedOrganizationId } = useOrganizationState();
  const [isCreating, setIsCreating] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });

  const createCanvasAction = useAction(createCanvas);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedOrganizationId) {
      return;
    }

    setIsCreating(true);

    try {
      const result = await createCanvasAction({
        name: formData.name,
        description: formData.description || undefined,
        organizationId: selectedOrganizationId,
      });

      onSuccess(result.id);
    } catch (error) {
      console.error('Error creating canvas:', error);
    } finally {
      setIsCreating(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4'>
      <div className='bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto'>
        {/* Header */}
        <div className='flex items-center justify-between p-6 border-b border-[#B5B178]/20 dark:border-gray-600'>
          <div className='flex items-center gap-3'>
            <div className='w-10 h-10 bg-gradient-to-br from-[#676D50] to-[#849068] rounded-xl flex items-center justify-center'>
              <Palette className='w-5 h-5 text-white' />
            </div>
            <div>
              <h2 className='font-display text-2xl font-bold text-[#676D50] dark:text-white'>Create New Canvas</h2>
              <p className='text-[#676D50]/70 dark:text-gray-400 text-sm'>Start a new infinite canvas project</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className='w-8 h-8 flex items-center justify-center rounded-lg hover:bg-[#F6F3E5] dark:hover:bg-gray-700 transition-colors'
          >
            <X size={20} className='text-[#676D50] dark:text-gray-400' />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className='p-6 space-y-6'>
          {/* Canvas Name */}
          <div>
            <label className='flex items-center gap-2 text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>
              <FileText size={16} />
              Canvas Name
            </label>
            <input
              type='text'
              value={formData.name}
              onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
              placeholder='Enter canvas name...'
              className='w-full px-4 py-3 bg-[#F6F3E5] dark:bg-gray-700 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-transparent text-[#1F2419] dark:text-white placeholder-[#676D50]/60 dark:placeholder-gray-400'
              required
            />
          </div>

          {/* Canvas Description */}
          <div>
            <label className='flex items-center gap-2 text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>
              <FileText size={16} />
              Description (Optional)
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
              placeholder='Describe your canvas project...'
              rows={3}
              className='w-full px-4 py-3 bg-[#F6F3E5] dark:bg-gray-700 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-transparent text-[#1F2419] dark:text-white placeholder-[#676D50]/60 dark:placeholder-gray-400 resize-none'
            />
          </div>

          {/* Actions */}
          <div className='flex gap-3 pt-4'>
            <button
              type='button'
              onClick={onClose}
              className='flex-1 px-6 py-3 bg-white dark:bg-gray-700 text-[#676D50] dark:text-gray-300 rounded-xl border border-[#B5B178]/30 dark:border-gray-600 hover:bg-[#F6F3E5] dark:hover:bg-gray-600 transition-colors font-medium'
            >
              Cancel
            </button>
            <button
              type='submit'
              disabled={!formData.name.trim() || isCreating}
              className='flex-1 px-6 py-3 bg-gradient-to-r from-[#676D50] to-[#849068] hover:from-[#849068] hover:to-[#676D50] text-white rounded-xl transition-all duration-300 font-medium shadow-lg hover:shadow-xl transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none'
            >
              {isCreating ? 'Creating...' : 'Create Canvas'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CanvasCreateModal;
