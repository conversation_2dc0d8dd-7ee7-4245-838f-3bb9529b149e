/**
 * 🎨 Shared Canvas Card
 * 
 * @description Card component for displaying shared canvases in projects list
 * @responsibility Shows shared canvas info with access level and actions
 * @dependencies Canvas sharing types, design system components
 * @ai_context Displays shared canvases alongside owned canvases in projects view
 */

import React from 'react';
import { Eye, MessageSquare, Edit3, Users, ExternalLink, Calendar } from 'lucide-react';
import { CanvasSharingPermission } from '../../../domain/types/sharing.types';

interface SharedCanvasCardProps {
  sharedCanvas: {
    id: string;
    canvas: {
      id: string;
      name: string;
      description?: string;
      thumbnail?: string;
      updatedAt: string;
    };
    permission: CanvasSharingPermission;
    sharedBy: {
      username: string;
      email?: string;
    };
    sharedAt: string;
    accessCount: number;
    lastAccessedAt?: string;
  };
  onClick?: () => void;
  className?: string;
}

export const SharedCanvasCard: React.FC<SharedCanvasCardProps> = ({
  sharedCanvas,
  onClick,
  className = ''
}) => {
  const getPermissionIcon = (permission: CanvasSharingPermission) => {
    switch (permission) {
      case CanvasSharingPermission.EDIT:
        return <Edit3 size={14} className="text-green-500" />;
      case CanvasSharingPermission.COMMENT:
        return <MessageSquare size={14} className="text-blue-500" />;
      default:
        return <Eye size={14} className="text-gray-500" />;
    }
  };

  const getPermissionLabel = (permission: CanvasSharingPermission) => {
    switch (permission) {
      case CanvasSharingPermission.EDIT:
        return 'Can edit';
      case CanvasSharingPermission.COMMENT:
        return 'Can comment';
      default:
        return 'Can view';
    }
  };

  const getPermissionColor = (permission: CanvasSharingPermission) => {
    switch (permission) {
      case CanvasSharingPermission.EDIT:
        return 'bg-green-100 text-green-800 border-green-200';
      case CanvasSharingPermission.COMMENT:
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 48) return 'Yesterday';
    return date.toLocaleDateString();
  };

  return (
    <div
      className={`
        group bg-white rounded-xl border border-gray-200 overflow-hidden 
        hover:shadow-lg hover:shadow-olive/10 transition-all duration-300 
        hover:border-olive/30 cursor-pointer relative
        ${className}
      `}
      onClick={onClick}
    >
      {/* Shared indicator */}
      <div className="absolute top-3 left-3 z-10">
        <div className="flex items-center space-x-1 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 text-xs font-medium text-gray-600 border border-gray-200">
          <Users size={12} />
          <span>Shared</span>
        </div>
      </div>

      {/* Permission indicator */}
      <div className="absolute top-3 right-3 z-10">
        <div className={`
          flex items-center space-x-1 rounded-full px-2 py-1 text-xs font-medium border
          ${getPermissionColor(sharedCanvas.permission)}
        `}>
          {getPermissionIcon(sharedCanvas.permission)}
          <span>{getPermissionLabel(sharedCanvas.permission)}</span>
        </div>
      </div>

      {/* Canvas Thumbnail */}
      <div className="aspect-[4/3] bg-gradient-to-br from-olive/10 to-olive/20 overflow-hidden relative">
        {sharedCanvas.canvas.thumbnail ? (
          <img
            src={sharedCanvas.canvas.thumbnail}
            alt={sharedCanvas.canvas.name}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-2 bg-olive/20 rounded-lg flex items-center justify-center">
                <Edit3 size={24} className="text-olive" />
              </div>
              <p className="text-sm text-gray-500">No preview</p>
            </div>
          </div>
        )}
        
        {/* Hover overlay */}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300 flex items-center justify-center">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div className="bg-white/90 backdrop-blur-sm rounded-lg p-2">
              <ExternalLink size={20} className="text-gray-700" />
            </div>
          </div>
        </div>
      </div>

      {/* Canvas Info */}
      <div className="p-4">
        <div className="mb-3">
          <h3 className="font-semibold text-gray-900 group-hover:text-olive transition-colors line-clamp-1">
            {sharedCanvas.canvas.name}
          </h3>
          {sharedCanvas.canvas.description && (
            <p className="text-sm text-gray-600 mt-1 line-clamp-2">
              {sharedCanvas.canvas.description}
            </p>
          )}
        </div>

        {/* Shared by info */}
        <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
          <div className="flex items-center space-x-1">
            <Users size={12} />
            <span>by {sharedCanvas.sharedBy.username}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Calendar size={12} />
            <span>shared {formatDate(sharedCanvas.sharedAt)}</span>
          </div>
        </div>

        {/* Access stats */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div>
            {sharedCanvas.accessCount > 0 ? (
              <span>{sharedCanvas.accessCount} views</span>
            ) : (
              <span>Not accessed yet</span>
            )}
          </div>
          {sharedCanvas.lastAccessedAt && (
            <div className="flex items-center space-x-1">
              <span>Last viewed {formatDate(sharedCanvas.lastAccessedAt)}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

/**
 * 🎨 Shared Canvas Grid
 * 
 * @description Grid layout for displaying multiple shared canvases
 */
interface SharedCanvasGridProps {
  sharedCanvases: SharedCanvasCardProps['sharedCanvas'][];
  onCanvasClick?: (canvasId: string) => void;
  className?: string;
}

export const SharedCanvasGrid: React.FC<SharedCanvasGridProps> = ({
  sharedCanvases,
  onCanvasClick,
  className = ''
}) => {
  if (sharedCanvases.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
          <Users size={24} className="text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No shared canvases</h3>
        <p className="text-gray-600">
          Canvases shared with you will appear here.
        </p>
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 ${className}`}>
      {sharedCanvases.map((sharedCanvas) => (
        <SharedCanvasCard
          key={sharedCanvas.id}
          sharedCanvas={sharedCanvas}
          onClick={() => onCanvasClick?.(sharedCanvas.canvas.id)}
        />
      ))}
    </div>
  );
};

/**
 * 🎨 Shared Canvas Section
 * 
 * @description Section component for projects page with shared canvases
 */
interface SharedCanvasSectionProps {
  sharedCanvases: SharedCanvasCardProps['sharedCanvas'][];
  onCanvasClick?: (canvasId: string) => void;
  isLoading?: boolean;
  className?: string;
}

export const SharedCanvasSection: React.FC<SharedCanvasSectionProps> = ({
  sharedCanvases,
  onCanvasClick,
  isLoading = false,
  className = ''
}) => {
  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center space-x-2">
          <Users size={20} className="text-gray-500" />
          <h2 className="text-xl font-semibold text-gray-900">Shared with me</h2>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-gray-100 rounded-xl aspect-[4/3] animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Users size={20} className="text-gray-500" />
          <h2 className="text-xl font-semibold text-gray-900">Shared with me</h2>
          {sharedCanvases.length > 0 && (
            <span className="bg-gray-100 text-gray-600 text-sm px-2 py-1 rounded-full">
              {sharedCanvases.length}
            </span>
          )}
        </div>
      </div>

      <SharedCanvasGrid
        sharedCanvases={sharedCanvases}
        onCanvasClick={onCanvasClick}
      />
    </div>
  );
};
