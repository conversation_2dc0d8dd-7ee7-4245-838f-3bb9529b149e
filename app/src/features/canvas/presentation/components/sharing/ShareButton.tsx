/**
 * 🎨 Canvas Share Button
 * 
 * @description Button component for triggering canvas sharing modal
 * @responsibility Provides consistent share button across canvas interfaces
 * @dependencies ShareModal, design system components
 * @ai_context Figma-style share button for canvas top-right placement
 */

import React, { useState } from 'react';
import { Share2, Users } from 'lucide-react';
import { Button } from '../../../../../shared/design-system/components/Button';
import { ShareModal } from './ShareModal';
import { type ShareButtonProps, type ShareCanvasResponse } from '../../../domain/types/sharing.types';

export const ShareButton: React.FC<ShareButtonProps> = ({
  canvasId,
  canvasName,
  className = '',
  variant = 'primary',
  size = 'md'
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleShareSuccess = (shareData: ShareCanvasResponse) => {
    // Optional: Handle successful sharing (analytics, notifications, etc.)
    console.log('[ShareButton] Canvas shared successfully:', shareData);
  };

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={() => setIsModalOpen(true)}
        className={`flex items-center space-x-2 ${className}`}
        icon={<Share2 size={16} />}
        iconPosition="left"
      >
        Share
      </Button>

      <ShareModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        canvasId={canvasId}
        canvasName={canvasName}
        onShareSuccess={handleShareSuccess}
      />
    </>
  );
};

/**
 * 🎨 Canvas Share Button with People Count
 * 
 * @description Enhanced share button that shows number of people with access
 */
interface ShareButtonWithCountProps extends ShareButtonProps {
  shareCount?: number;
  showCount?: boolean;
}

export const ShareButtonWithCount: React.FC<ShareButtonWithCountProps> = ({
  canvasId,
  canvasName,
  className = '',
  variant = 'secondary',
  size = 'md',
  shareCount = 0,
  showCount = true
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleShareSuccess = (shareData: ShareCanvasResponse) => {
    console.log('[ShareButtonWithCount] Canvas shared successfully:', shareData);
  };

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={() => setIsModalOpen(true)}
        className={`flex items-center space-x-2 ${className}`}
      >
        <div className="flex items-center space-x-2">
          {shareCount > 0 ? (
            <Users size={16} className="text-current" />
          ) : (
            <Share2 size={16} className="text-current" />
          )}
          <span>
            {shareCount > 0 ? (
              showCount ? `Shared (${shareCount})` : 'Shared'
            ) : (
              'Share'
            )}
          </span>
        </div>
      </Button>

      <ShareModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        canvasId={canvasId}
        canvasName={canvasName}
        onShareSuccess={handleShareSuccess}
      />
    </>
  );
};

/**
 * 🎨 Compact Share Button
 * 
 * @description Minimal share button for tight spaces
 */
export const CompactShareButton: React.FC<ShareButtonProps> = ({
  canvasId,
  canvasName,
  className = ''
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        className={`p-2 hover:bg-gray-100 rounded-lg transition-colors ${className}`}
        title="Share canvas"
      >
        <Share2 size={18} className="text-gray-600" />
      </button>

      <ShareModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        canvasId={canvasId}
        canvasName={canvasName}
      />
    </>
  );
};

/**
 * 🎨 Share Button for Canvas Header
 * 
 * @description Styled specifically for canvas page headers
 */
export const CanvasHeaderShareButton: React.FC<ShareButtonProps> = ({
  canvasId,
  canvasName,
  className = ''
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        className={`
          flex items-center space-x-2 px-4 py-2
          bg-white/90 text-[#566B46] border border-[#9EA581]/30 rounded-lg
          hover:bg-white/95 hover:border-[#9EA581]/50 transition-all duration-200
          shadow-sm hover:shadow-md backdrop-blur-sm
          ${className}
        `}
      >
        <Share2 size={16} />
        <span className="font-medium">Share</span>
      </button>

      <ShareModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        canvasId={canvasId}
        canvasName={canvasName}
      />
    </>
  );
};
