/**
 * 🎨 Canvas Share Modal - Figma Style
 * 
 * @description Figma-style modal for sharing canvases with email invitations and public links
 * @responsibility Handles canvas sharing UI with permissions, expiration, and settings
 * @dependencies Canvas sharing operations, design system components
 * @ai_context Implements Figma-style sharing interface with email invites and public links
 */

import React, { useState, useEffect } from 'react';
import { X, Share2, Mail, Link2, Copy, Check, Users, Settings, Calendar, Eye, MessageSquare, Edit3 } from 'lucide-react';
import { useAction, useQuery } from 'wasp/client/operations';
import { 
  shareCanvas, 
  createPublicCanvasLink, 
  getCanvasShares,
  updateCanvasShare,
  revokeCanvasShare 
} from 'wasp/client/operations';
import { Button } from '../../../../../shared/design-system/components/Button';
import { Input } from '../../../../../shared/design-system/components/Input';
import { CanvasSharingPermission, type ShareModalProps } from '../../../domain/types/sharing.types';
import toast from 'react-hot-toast';

interface ShareItem {
  id: string;
  email: string | null;
  permission: CanvasSharingPermission;
  isPublic: boolean;
  token: string;
  expiresAt: Date | null;
  accessCount: number;
  sharedAt: Date;
}

export const ShareModal: React.FC<ShareModalProps> = ({
  isOpen,
  onClose,
  canvasId,
  canvasName,
  onShareSuccess
}) => {
  const [email, setEmail] = useState('');
  const [permission, setPermission] = useState<CanvasSharingPermission>(CanvasSharingPermission.VIEW);
  const [isLoading, setIsLoading] = useState(false);
  const [copiedToken, setCopiedToken] = useState<string | null>(null);
  const [publicLink, setPublicLink] = useState<string | null>(null);

  // Actions
  const shareCanvasAction = useAction(shareCanvas);
  const createPublicLinkAction = useAction(createPublicCanvasLink);
  const updateShareAction = useAction(updateCanvasShare);
  const revokeShareAction = useAction(revokeCanvasShare);

  // Get existing shares
  const { data: shares = [], refetch: refetchShares } = useQuery(
    getCanvasShares,
    { canvasId },
    { enabled: isOpen }
  );

  // Check for existing public link
  useEffect(() => {
    const existingPublicLink = shares.find((share: any) => !share.sharedWithEmail);

    if (existingPublicLink) {
      const linkUrl = `${window.location.origin}/canvas/shared/${existingPublicLink.token}`;
      setPublicLink(linkUrl);
    } else {
      setPublicLink(null);
    }
  }, [shares]);

  const handleShareWithEmail = async () => {
    if (!email.trim()) {
      toast.error('Please enter an email address');
      return;
    }

    setIsLoading(true);
    try {
      const result = await shareCanvasAction({
        canvasId,
        sharedWithEmail: email.trim(),
        permission,
      });

      toast.success(`Canvas shared with ${email}`);
      setEmail('');
      onShareSuccess?.(result);
      refetchShares();
    } catch (error: any) {
      console.error('Error sharing canvas:', error);
      toast.error(error.message || 'Failed to share canvas');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreatePublicLink = async () => {
    setIsLoading(true);
    try {
      const result = await createPublicLinkAction({
        canvasId,
        permission: CanvasSharingPermission.VIEW,
      });

      const shareUrl = `${window.location.origin}/canvas/shared/${result.token}`;
      setPublicLink(shareUrl);
      toast.success('Public link created');
      refetchShares();
    } catch (error: any) {
      console.error('Error creating public link:', error);
      toast.error(error.message || 'Failed to create public link');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyLink = async (token: string, isPublic: boolean = false) => {
    const shareUrl = `${window.location.origin}/canvas/shared/${token}`;
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopiedToken(token);
      toast.success(isPublic ? 'Public link copied' : 'Share link copied');
      setTimeout(() => setCopiedToken(null), 2000);
    } catch (error) {
      toast.error('Failed to copy link');
    }
  };

  const handleUpdatePermission = async (shareId: string, newPermission: CanvasSharingPermission) => {
    try {
      await updateShareAction({
        shareId,
        permission: newPermission,
      });
      toast.success('Permission updated');
      refetchShares();
    } catch (error: any) {
      toast.error('Failed to update permission');
    }
  };

  const handleRevokeShare = async (shareId: string) => {
    try {
      await revokeShareAction({ shareId });
      toast.success('Access revoked');

      // Refetch shares to update the list (useEffect will handle clearing public link)
      refetchShares();
    } catch (error: any) {
      console.error('[ShareModal] Error revoking share:', error);
      toast.error('Failed to revoke access');
    }
  };

  const getPermissionIcon = (perm: CanvasSharingPermission) => {
    switch (perm) {
      case CanvasSharingPermission.VIEW:
        return <Eye size={16} className="text-gray-500" />;
      case CanvasSharingPermission.COMMENT:
        return <MessageSquare size={16} className="text-blue-500" />;
      case CanvasSharingPermission.EDIT:
        return <Edit3 size={16} className="text-green-500" />;
      default:
        return <Eye size={16} className="text-gray-500" />;
    }
  };

  const getPermissionLabel = (perm: CanvasSharingPermission) => {
    switch (perm) {
      case CanvasSharingPermission.VIEW:
        return 'Can view';
      case CanvasSharingPermission.COMMENT:
        return 'Can comment';
      case CanvasSharingPermission.EDIT:
        return 'Can edit';
      default:
        return 'Can view';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-olive/10 rounded-lg flex items-center justify-center">
              <Share2 size={18} className="text-olive" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Share Canvas</h2>
              <p className="text-sm text-gray-500">{canvasName}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={20} className="text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6 max-h-[calc(90vh-120px)] overflow-y-auto">
          {/* Share with email */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Mail size={18} className="text-gray-500" />
              <h3 className="font-medium text-gray-900">Invite people</h3>
            </div>
            
            <div className="flex space-x-2">
              <div className="flex-1">
                <Input
                  type="email"
                  placeholder="Enter email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleShareWithEmail()}
                />
              </div>
              <select
                value={permission}
                onChange={(e) => setPermission(e.target.value as CanvasSharingPermission)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-olive focus:border-transparent"
              >
                <option value={CanvasSharingPermission.VIEW}>Can view</option>
                <option value={CanvasSharingPermission.COMMENT}>Can comment</option>
                <option value={CanvasSharingPermission.EDIT}>Can edit</option>
              </select>
            </div>
            
            <Button
              onClick={handleShareWithEmail}
              loading={isLoading}
              disabled={!email.trim()}
              className="w-full"
              variant="primary"
            >
              Send Invitation
            </Button>
          </div>

          {/* Public link */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Link2 size={18} className="text-gray-500" />
                <h3 className="font-medium text-gray-900">Public link</h3>
              </div>
              {!publicLink && (
                <Button
                  onClick={handleCreatePublicLink}
                  loading={isLoading}
                  variant="ghost"
                  size="sm"
                >
                  Create link
                </Button>
              )}
            </div>

            {publicLink && (
              <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                <div className="flex-1 text-sm text-gray-600 truncate">
                  {publicLink}
                </div>
                <button
                  onClick={() => handleCopyLink(publicLink.split('/').pop()!, true)}
                  className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  {copiedToken === publicLink.split('/').pop() ? (
                    <Check size={16} className="text-green-500" />
                  ) : (
                    <Copy size={16} className="text-gray-500" />
                  )}
                </button>
              </div>
            )}
          </div>

          {/* Existing shares */}
          {shares.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Users size={18} className="text-gray-500" />
                <h3 className="font-medium text-gray-900">People with access</h3>
              </div>

              <div className="space-y-2">
                {shares.map((share: any) => (
                  <div key={share.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                        {share.sharedWithEmail ? (
                          <Mail size={14} className="text-gray-500" />
                        ) : (
                          <Link2 size={14} className="text-gray-500" />
                        )}
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {share.sharedWithEmail || 'Public link'}
                        </p>
                        <div className="flex items-center space-x-1 text-xs text-gray-500">
                          {getPermissionIcon(share.permission)}
                          <span>{getPermissionLabel(share.permission)}</span>
                          {share.accessCount > 0 && (
                            <span>• {share.accessCount} views</span>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => handleCopyLink(share.token)}
                        className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
                        title="Copy link"
                      >
                        {copiedToken === share.token ? (
                          <Check size={14} className="text-green-500" />
                        ) : (
                          <Copy size={14} className="text-gray-500" />
                        )}
                      </button>
                      <button
                        onClick={() => handleRevokeShare(share.id)}
                        className="p-2 hover:bg-red-100 rounded-lg transition-colors text-red-500"
                        title="Revoke access"
                      >
                        <X size={14} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
