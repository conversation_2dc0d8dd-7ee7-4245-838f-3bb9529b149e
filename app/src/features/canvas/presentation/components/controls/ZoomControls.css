/* 🔍 Zoom Controls Styling - Modular Canvas Controls */

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(158, 165, 129, 0.2);
}

.zoom-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 18px;
  font-weight: 600;
  color: #566B46;
  transition: all 0.2s ease;
  user-select: none;
}

.zoom-button:hover {
  background: rgba(158, 165, 129, 0.1);
  color: #4A5D23;
  transform: scale(1.05);
}

.zoom-button:active {
  transform: scale(0.95);
  background: rgba(158, 165, 129, 0.2);
}

.zoom-percentage {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 50px;
  height: 32px;
  padding: 0 8px;
  font-size: 12px;
  font-weight: 600;
  color: #566B46;
  background: rgba(158, 165, 129, 0.05);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.zoom-percentage:hover {
  background: rgba(158, 165, 129, 0.1);
  color: #4A5D23;
}

.zoom-percentage:active {
  background: rgba(158, 165, 129, 0.15);
}

/* 🌙 Dark mode support */
.dark .zoom-controls {
  background: rgba(42, 42, 42, 0.95);
  border-color: rgba(158, 165, 129, 0.3);
}

.dark .zoom-button {
  color: #9EA581;
}

.dark .zoom-button:hover {
  background: rgba(158, 165, 129, 0.15);
  color: #B8C99A;
}

.dark .zoom-percentage {
  color: #9EA581;
  background: rgba(158, 165, 129, 0.1);
}

.dark .zoom-percentage:hover {
  background: rgba(158, 165, 129, 0.15);
  color: #B8C99A;
}

/* 📱 Mobile responsive */
@media (max-width: 768px) {
  .zoom-controls {
    padding: 2px;
    gap: 2px;
  }
  
  .zoom-button {
    width: 28px;
    height: 28px;
    font-size: 16px;
  }
  
  .zoom-percentage {
    min-width: 45px;
    height: 28px;
    font-size: 11px;
    padding: 0 6px;
  }
}

/* 🎯 Focus states for accessibility */
.zoom-button:focus,
.zoom-percentage:focus {
  outline: 2px solid rgba(200, 161, 60, 0.5);
  outline-offset: 2px;
}

.zoom-button:focus:not(:focus-visible),
.zoom-percentage:focus:not(:focus-visible) {
  outline: none;
}

/* 🔧 High contrast mode */
@media (prefers-contrast: high) {
  .zoom-controls {
    border-width: 2px;
    border-color: #000000;
  }
  
  .zoom-button,
  .zoom-percentage {
    border: 1px solid #000000;
  }
}

/* 🎨 Animation for smooth interactions */
.zoom-controls {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 🚫 Disable text selection */
.zoom-controls * {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
