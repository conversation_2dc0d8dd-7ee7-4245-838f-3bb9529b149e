import React from 'react';
import { useToolDomainStore } from '../../../domain/state'; // 🧠 Using pure domain state!
import { ToolService } from '../../../domain/services'; // 🧠 Using domain service!
import { useShallow } from 'zustand/react/shallow';
import './ZoomControls.css';

const ZoomControls: React.FC = () => {
  // 🧠 Using pure domain state to get zoom scale with shallow comparison
  const { scale } = useToolDomainStore(
    useShallow((state) => ({
      scale: state.scale,
    }))
  );

  const percentage = Math.round(scale * 100);

  return (
    <div className='zoom-controls'>
      <button className='zoom-button' onClick={() => ToolService.zoom.zoomOut()}>
        −
      </button>
      <div className='zoom-percentage' onClick={() => ToolService.zoom.resetZoom()}>
        {percentage}%
      </div>
      <button className='zoom-button' onClick={() => ToolService.zoom.zoomIn()}>
        +
      </button>
    </div>
  );
};

export default ZoomControls;
