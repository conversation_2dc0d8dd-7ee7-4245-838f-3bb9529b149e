/**
 * 🎛️ Modular Controls Component
 *
 * @description Main controls component that orchestrates all canvas control functionality
 * @responsibility Render canvas controls (zoom, pan, etc.)
 * @ai_context This is the main controls component for our modular canvas architecture
 */

import React from 'react';
import ZoomControls from './ZoomControls'; // 🏗️ Using our own modular copy!

/**
 * 🎛️ Controls Props Interface
 * @ai_context Props for the Controls component
 */
export interface ControlsProps {
  className?: string;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

/**
 * 🎛️ Controls Component - MODULAR ARCHITECTURE
 * @ai_context Main controls component that shows canvas controls
 */
export const Controls: React.FC<ControlsProps> = ({ className, position = 'bottom-right' }) => {
  // Removed spammy controls rendering log

  return (
    <div
      className={`controls-container ${className || ''}`}
      style={{
        position: 'fixed',
        zIndex: 50,
        display: 'flex',
        flexDirection: 'column',
        gap: '8px',
        ...(position === 'top-left' && { top: '16px', left: '16px' }),
        ...(position === 'top-right' && { top: '16px', right: '16px' }),
        ...(position === 'bottom-left' && { bottom: '16px', left: '16px' }),
        ...(position === 'bottom-right' && { bottom: '16px', right: '16px' }),
      }}
    >
      {/* Zoom Controls */}
      <div
        style={{
          background: 'white',
          borderRadius: '8px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e5e7eb',
          padding: '8px',
        }}
      >
        <ZoomControls />
      </div>

      {/* Future controls can be added here */}
      {/* Pan Controls, Grid Controls, etc. */}
    </div>
  );
};

export default Controls;
