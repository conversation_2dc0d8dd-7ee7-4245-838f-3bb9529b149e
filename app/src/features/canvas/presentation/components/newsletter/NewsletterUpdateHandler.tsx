/**
 * 📰 Newsletter Update Handler - MODULAR VERSION
 * @description Handles newsletter generation updates and HTML streaming
 * @responsibility Listen for newsletter updates and update canvas state
 * @ai_context Clean separation of newsletter logic from toolbar
 */

import { useCallback, useEffect } from 'react';
// import { useSocketListener } from 'wasp/client/webSocket'; // REMOVED - migrated to Cloudflare Workers
import { useCanvasToolbarStore } from '../../../domain/state';

/**
 * 📰 Newsletter Update Handler Component
 * @description Invisible component that handles newsletter streaming updates
 */
export const NewsletterUpdateHandler = () => {
  // Get state and actions from modular store
  const { htmlElements, canvasSize, scale, addHtmlElement, setHtmlElements } = useCanvasToolbarStore();

  // 📰 Handle HTML Stream Updates (for newsletters)
  const handleHtmlStreamUpdate = useCallback(
    (data: any) => {
      const { taskId, elementId, html, partialHtml, fragment, content, progress, isComplete } = data;
      console.log(`[NewsletterUpdateHandler] Received HTML stream update for task ${taskId}`, { progress, isComplete });

      // Use any available HTML content property
      const htmlContent = html ?? partialHtml ?? fragment ?? content ?? '';

      if (!htmlContent) {
        console.log(`[NewsletterUpdateHandler] No HTML content in update for task ${taskId}`);
        return;
      }

      // Find existing element with this taskId
      const existingElement = htmlElements.find((el) => el.taskId === taskId);

      if (existingElement) {
        // Update existing element
        console.log(`[NewsletterUpdateHandler] Updating existing HTML element for task ${taskId}`);
        const updatedElements = htmlElements.map((el) => (el.taskId === taskId ? { ...el, html: htmlContent } : el));
        setHtmlElements(updatedElements);
      } else if (elementId) {
        // Create new element if we have an elementId
        console.log(`[NewsletterUpdateHandler] Creating new HTML element for task ${taskId}`);
        const newElement = {
          id: elementId,
          x: canvasSize.width / 4 / scale,
          y: canvasSize.height / 4 / scale,
          width: 400,
          height: 600,
          html: htmlContent,
          taskId,
        };
        setHtmlElements([...htmlElements, newElement]);
      }
    },
    [htmlElements, canvasSize, scale, setHtmlElements]
  );

  // 🎙️ Handle Voice Agent Canvas Updates (for newsletters)
  const handleVoiceAgentCanvasUpdate = useCallback(
    (data: any) => {
      // Check if this is an HTML update
      if ((data.elementType === 'html' || data.type === 'html') && (data.html || data.content) && data.taskId) {
        console.log(
          `[NewsletterUpdateHandler] Received voice agent canvas update with HTML content for task ${data.taskId}`
        );

        // Get the HTML content from either html or content property
        const htmlContent = data.html || data.content;

        console.log(`[NewsletterUpdateHandler] HTML content length: ${htmlContent.length}`);
        console.log(`[NewsletterUpdateHandler] HTML content preview: ${htmlContent.substring(0, 100)}...`);

        // Find existing element with this taskId
        const existingElement = htmlElements.find((el) => el.taskId === data.taskId);

        if (!existingElement) {
          // Create new HTML element
          console.log(`[NewsletterUpdateHandler] Creating new HTML element for task ${data.taskId}`);
          const newElementId = addHtmlElement();
          const newElement = {
            id: newElementId,
            x: canvasSize.width / 4 / scale,
            y: canvasSize.height / 4 / scale,
            width: 400,
            height: 600,
            html: htmlContent,
            taskId: data.taskId,
          };
          setHtmlElements([...htmlElements, newElement]);
        } else {
          // Update existing element
          console.log(`[NewsletterUpdateHandler] Updating existing HTML element for task ${data.taskId}`);
          const updatedElements = htmlElements.map((el) =>
            el.taskId === data.taskId ? { ...el, html: htmlContent } : el
          );
          setHtmlElements(updatedElements);
        }
      }
    },
    [htmlElements, canvasSize, scale, addHtmlElement, setHtmlElements]
  );

  // 📰 Handle Newsletter Generation Status Updates
  const handleNewsletterStatusUpdate = useCallback((data: any) => {
    const { taskId, status, progress, isComplete } = data;
    console.log(`[NewsletterUpdateHandler] Newsletter status update for task ${taskId}:`, {
      status,
      progress,
      isComplete,
    });

    // TODO: Update newsletter card state to show "Generating..." or completion
    // This could update a newsletter generation state store
    if (status === 'generating') {
      console.log(`[NewsletterUpdateHandler] Newsletter generation started for task ${taskId}`);
    } else if (isComplete) {
      console.log(`[NewsletterUpdateHandler] Newsletter generation completed for task ${taskId}`);
    }
  }, []);

  // 📡 Event Listeners for Newsletter Updates (migrated from WASP WebSocket to Cloudflare Workers)
  useEffect(() => {
    const handleHtmlStreamUpdateEvent = (event: CustomEvent) => {
      handleHtmlStreamUpdate(event.detail);
    };

    const handleVoiceAgentCanvasUpdateEvent = (event: CustomEvent) => {
      handleVoiceAgentCanvasUpdate(event.detail);
    };

    const handleNewsletterStatusUpdateEvent = (event: CustomEvent) => {
      handleNewsletterStatusUpdate(event.detail);
    };

    window.addEventListener('html_stream_update', handleHtmlStreamUpdateEvent as EventListener);
    window.addEventListener('voice_agent_canvas_update', handleVoiceAgentCanvasUpdateEvent as EventListener);
    window.addEventListener('newsletter_card_status_update', handleNewsletterStatusUpdateEvent as EventListener);

    return () => {
      window.removeEventListener('html_stream_update', handleHtmlStreamUpdateEvent as EventListener);
      window.removeEventListener('voice_agent_canvas_update', handleVoiceAgentCanvasUpdateEvent as EventListener);
      window.removeEventListener('newsletter_card_status_update', handleNewsletterStatusUpdateEvent as EventListener);
    };
  }, [handleHtmlStreamUpdate, handleVoiceAgentCanvasUpdate, handleNewsletterStatusUpdate]);

  // This component doesn't render anything - it's just for handling updates
  return null;
};

/**
 * 📰 Newsletter Update Hook
 * @description Hook version for components that need newsletter update handling
 */
export const useNewsletterUpdates = () => {
  // Get state from modular store
  const { htmlElements, canvasSize, scale, addHtmlElement, setHtmlElements } = useCanvasToolbarStore();

  // 📰 Handle HTML Stream Updates
  const handleHtmlStreamUpdate = useCallback(
    (data: any) => {
      const { taskId, elementId, html, partialHtml, fragment, content } = data;
      console.log(`[useNewsletterUpdates] HTML stream update for task ${taskId}`);

      const htmlContent = html ?? partialHtml ?? fragment ?? content ?? '';
      if (!htmlContent) return;

      const existingElement = htmlElements.find((el) => el.taskId === taskId);

      if (existingElement) {
        const updatedElements = htmlElements.map((el) => (el.taskId === taskId ? { ...el, html: htmlContent } : el));
        setHtmlElements(updatedElements);
      } else if (elementId) {
        const newElement = {
          id: elementId,
          x: canvasSize.width / 4 / scale,
          y: canvasSize.height / 4 / scale,
          width: 400,
          height: 600,
          html: htmlContent,
          taskId,
        };
        setHtmlElements([...htmlElements, newElement]);
      }
    },
    [htmlElements, canvasSize, scale, setHtmlElements]
  );

  return {
    handleHtmlStreamUpdate,
  };
};
