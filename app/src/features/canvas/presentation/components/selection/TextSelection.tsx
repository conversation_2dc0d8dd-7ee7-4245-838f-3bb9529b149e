/**
 * 📝 Text Selection Component - MODULAR VERSION
 * @description Handles text selection and transformation using Konva.Transformer
 * @responsibility Text selection, resize handles, transformation events
 * @ai_context Modular replacement for BaseScene text selection system
 */

import React, { useEffect, useRef } from 'react';
import Konva from 'konva';
import { Transformer } from 'react-konva';
import { useCanvasToolbarStore, useElementDomainStore } from '../../../domain/state';
import { ElementService } from '../../../domain/services';
import { useShallow } from 'zustand/react/shallow';

/**
 * 📝 TextSelection Component
 * Provides transformation handles for selected text elements only
 */
const TextSelection: React.FC = () => {
  const transformerRef = useRef<Konva.Transformer>(null);

  // 🧠 Get selected text IDs from domain state
  const { selectedIds } = useCanvasToolbarStore(
    useShallow((state) => ({
      selectedIds: state.selectedIds,
    }))
  );

  // 🧠 Get texts from domain state
  const { texts } = useElementDomainStore(
    useShallow((state) => ({
      texts: state.texts,
    }))
  );

  // 🔄 Update transformer when selection changes
  useEffect(() => {
    const transformer = transformerRef.current;
    if (!transformer) return;

    // Find the selected text nodes on the stage
    const stage = transformer.getStage();
    if (!stage) return;

    const selectedNodes: Konva.Node[] = [];

    // Only process text elements (filter out image elements)
    const textIds = selectedIds.filter((id) => {
      // Check if this ID belongs to a text element
      return texts.some((text) => text.id === id);
    });

    textIds.forEach((id) => {
      console.log(`[TextSelection] Looking for text node with ID: ${id}`);

      // Look for Text nodes with the selected ID
      const textNode = stage.findOne(`#${id}`) as Konva.Text;
      console.log(`[TextSelection] Found node:`, textNode, `className: ${textNode?.className}`);

      if (textNode && textNode instanceof Konva.Text) {
        selectedNodes.push(textNode);
        console.log(`[TextSelection] Found selected text node: ${id}`);
      } else {
        console.log(`[TextSelection] Could not find text node with ID: ${id}`);
      }
    });

    // Attach transformer to selected text nodes
    transformer.nodes(selectedNodes);

    console.log(`[TextSelection] Attached transformer to ${selectedNodes.length} text nodes`);
  }, [selectedIds, texts]);

  // 🔄 Handle transformation events
  const handleTransformEnd = () => {
    const transformer = transformerRef.current;
    if (!transformer) return;

    const nodes = transformer.nodes();

    nodes.forEach((node) => {
      if (node.className === 'Text') {
        const textId = node.id();

        // Get the current transform values
        const scaleX = node.scaleX();
        const scaleY = node.scaleY();
        const x = node.x();
        const y = node.y();
        const rotation = node.rotation();

        // For text, we typically want to change font size instead of scaling
        const currentText = texts.find((text) => text.id === textId);
        if (currentText) {
          const newFontSize = Math.max(8, currentText.fontSize * Math.max(scaleX, scaleY)); // Minimum 8px font size
          const newWidth = (currentText.width || 200) * scaleX; // Default width of 200 if undefined

          // Reset scale to 1 and update font size and width
          node.scaleX(1);
          node.scaleY(1);

          // Update domain state with new properties
          ElementService.texts.update(textId, {
            ...currentText,
            x,
            y,
            fontSize: newFontSize,
            width: newWidth,
            rotation: rotation * (180 / Math.PI), // Convert to degrees
          });

          console.log(`[TextSelection] Updated text ${textId}:`, {
            x,
            y,
            fontSize: newFontSize,
            width: newWidth,
            rotation: rotation * (180 / Math.PI),
          });
        }
      }
    });
  };

  return (
    <Transformer
      ref={transformerRef}
      flipEnabled={false}
      borderStrokeWidth={2}
      borderStroke='#3B82F6' // Blue for text elements to distinguish from images
      anchorStroke='#3B82F6'
      anchorFill='#3B82F6'
      anchorStrokeWidth={1}
      anchorSize={12}
      anchorCornerRadius={8}
      onTransformEnd={handleTransformEnd}
      // Text-specific transformer settings
      keepRatio={false} // Allow independent width/height scaling for text
      centeredScaling={false}
      anchorStyleFunc={(anchor) => {
        // Style the transformation handles for text
        anchor.stroke('#3B82F6');
        anchor.fill('#3B82F6');
        anchor.strokeWidth(1);

        if (anchor.hasName('top-center') || anchor.hasName('bottom-center')) {
          anchor.height(6);
          anchor.offsetY(3);
          anchor.width(24);
          anchor.offsetX(12);
        } else if (anchor.hasName('middle-left') || anchor.hasName('middle-right')) {
          anchor.height(24);
          anchor.offsetY(12);
          anchor.width(6);
          anchor.offsetX(3);
        } else if (anchor.hasName('rotater')) {
          anchor.cornerRadius(12);
          anchor.width(24);
          anchor.height(24);
          anchor.offsetX(12);
          anchor.offsetY(12);
        } else {
          anchor.width(12);
          anchor.offsetX(6);
          anchor.height(12);
          anchor.offsetY(6);
        }
      }}
    />
  );
};

export default TextSelection;
