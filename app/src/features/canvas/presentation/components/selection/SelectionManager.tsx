/**
 * 🎯 Selection Manager Component - MODULAR VERSION
 * @description Handles click detection and selection logic for canvas elements
 * @responsibility Element selection, click detection, selection state management
 * @ai_context Modular replacement for BaseScene selection system
 */

import React, { useEffect, useRef } from 'react';
import Konva from 'konva';
import { useElementDomainStore } from '../../../domain/state';
import { ElementService } from '../../../domain/services';
import { useShallow } from 'zustand/react/shallow';

/**
 * 🎯 SelectionManager Props
 */
interface SelectionManagerProps {
  stageRef: React.RefObject<Konva.Stage>;
}

/**
 * 🎯 SelectionManager Component
 * Handles click detection and manages which elements are selected
 */
const SelectionManager: React.FC<SelectionManagerProps> = ({ stageRef: passedStageRef }) => {
  const stageRef = useRef<Konva.Stage | null>(null);

  // 🧠 Get selection state from domain store
  const { selectedIds } = useElementDomainStore(useShallow((state) => ({
    selectedIds: state.selectedIds,
  })));

  // 🔄 Set up stage event listeners - FOLLOWING useEffect RULES
  // ✅ This IS appropriate for useEffect: synchronizing with external system (Konva stage DOM events)
  useEffect(() => {
    const stage = passedStageRef.current;
    if (!stage) {
      // Stage not ready yet, effect will re-run when it is
      return;
    }

    // Removed spammy setup log
    stageRef.current = stage;

    // 🖱️ Handle pointer down (click) events
    const handlePointerDown = ({ target }: Konva.KonvaEventObject<MouseEvent>) => {
      // Ignore transformer anchors
      if (target.hasName('_anchor')) return;

      // Removed spammy click target log

      let nodeToSelect: Konva.Node | null = null;

      // 🖼️ Handle Image clicks (from URLImage components)
      if (target.className === 'Image') {
        const parentGroup = target.getParent();

        if (parentGroup && parentGroup.id()) {
          // Image is inside a Group with ID (URLImage component)
          nodeToSelect = parentGroup;
          console.log(`[SelectionManager] Image in Group ${parentGroup.id()}, selecting Group`);
        } else {
          // Plain Image, not part of a component group
          nodeToSelect = target;
          console.log(`[SelectionManager] Selecting plain Image ${target.id() || '(no id)'}`);
        }
      }
      // 🗂️ Handle Group clicks (URLImage components)
      else if (target.className === 'Group' && target.id()) {
        const group = target as Konva.Group;
        if (group.findOne && group.findOne('Image')) {
          // Clicked directly on a Group that has an ID and contains an Image
          nodeToSelect = target;
          console.log(`[SelectionManager] Clicked Group ${target.id()} with Image, selecting Group`);
        }
      }
      // 📄 Handle Text clicks
      else if (target.className === 'Text' && target.id()) {
        nodeToSelect = target;
        console.log(`[SelectionManager] Selecting Text ${target.id()}`);
      }
      // 🌐 Handle HTML element clicks (if they have groups)
      else if (target.className === 'Group' && target.id()) {
        const group = target as Konva.Group;
        if (group.findOne && !group.findOne('Image')) {
          // Group without image - could be HTML element or other component
          nodeToSelect = target;
          console.log(`[SelectionManager] Selecting Group ${target.id()} (non-image)`);
        }
      }

      // 🎯 Update selection state with toggle logic
      if (nodeToSelect && nodeToSelect.id()) {
        const elementId = nodeToSelect.id();

        // 🔍 DEBUG: Log detailed info about the clicked element
        console.log(`[SelectionManager] 🔍 CLICKED ELEMENT DEBUG:`, {
          elementId,
          nodeType: nodeToSelect.getType(),
          className: nodeToSelect.getClassName(),
          isGenerated: elementId.includes('generated-'),
          isCopyPasted: !elementId.includes('generated-'),
          currentlySelected: selectedIds.includes(elementId),
          selectedIds: selectedIds,
          nodeAttrs: {
            x: nodeToSelect.x(),
            y: nodeToSelect.y(),
            width: nodeToSelect.width(),
            height: nodeToSelect.height(),
          }
        });

        // Check if element is already selected
        if (selectedIds.includes(elementId)) {
          // If already selected, deselect it (toggle off)
          console.log(`[SelectionManager] Deselecting element: ${elementId}`);
          ElementService.selection.clearSelection();
        } else {
          // If not selected, select it
          console.log(`[SelectionManager] Selecting element: ${elementId}`);
          ElementService.selection.select([elementId]);
        }
      } else {
        // 🔍 DEBUG: Log what was clicked when no element found
        console.log(`[SelectionManager] 🔍 BACKGROUND CLICK DEBUG:`, {
          clickedNode: target,
          nodeType: target?.getType(),
          className: target?.getClassName(),
          currentSelection: selectedIds,
          willClearSelection: selectedIds.length > 0
        });

        // Clicked on stage background or unselectable element - clear selection
        if (selectedIds.length > 0) {
          console.log(`[SelectionManager] Clearing selection (clicked background or unselectable element)`);
          ElementService.selection.clearSelection();
        } else {
          // Removed spammy background click log
        }
      }
    };

    // 🖱️ Handle double-click events (for cropping)
    const handleDoubleClick = (e: Konva.KonvaEventObject<MouseEvent>) => {
      // Only trigger on left double-click
      if (e.evt.button !== 0) return;

      const { target } = e;
      let nodeForCrop: Konva.Node | null = null;

      // 🖼️ Find the image to crop
      if (target.className === 'Image') {
        const parentGroup = target.getParent();
        if (parentGroup && parentGroup.id()) {
          nodeForCrop = parentGroup; // URLImage group
        } else {
          nodeForCrop = target; // Plain image
        }
      } else if (target.className === 'Group' && target.id()) {
        const group = target as Konva.Group;
        if (group.findOne && group.findOne('Image')) {
          nodeForCrop = target; // URLImage group
        }
      }

      if (nodeForCrop && nodeForCrop.id()) {
        console.log(`[SelectionManager] Double-click detected on ${nodeForCrop.id()}, triggering crop`);

        // 🎭 Dispatch crop event (will be handled by CropHandler component)
        window.dispatchEvent(
          new CustomEvent('canvas:startCrop', {
            detail: {
              nodeId: nodeForCrop.id(),
              node: nodeForCrop,
            },
          })
        );
      }
    };

    // 🖱️ Handle right-click events (for context menus)
    const handleContextMenu = (e: Konva.KonvaEventObject<MouseEvent>) => {
      e.evt.preventDefault(); // Prevent browser context menu

      const { target } = e;
      const stage = target.getStage();
      if (!stage) return;

      const pointer = stage.getPointerPosition();
      if (!pointer) return;



      // 🎯 Determine what was right-clicked
      let contextType = 'canvas';
      let elementId: string | null = null;

      if (target.className === 'Image') {
        const parentGroup = target.getParent();
        if (parentGroup && parentGroup.id()) {
          contextType = 'image';
          elementId = parentGroup.id();
        }
      } else if (target.className === 'Group' && target.id()) {
        const group = target as Konva.Group;
        if (group.findOne && group.findOne('Image')) {
          contextType = 'image';
          elementId = target.id();
        } else {
          contextType = 'element';
          elementId = target.id();
        }
      } else if (target.className === 'Text' && target.id()) {
        contextType = 'text';
        elementId = target.id();
      }

      // 💬 Dispatch context menu event (will be handled by ContextMenuHandler)
      window.dispatchEvent(
        new CustomEvent('canvas:contextMenu', {
          detail: {
            x: pointer.x,
            y: pointer.y,
            contextType,
            elementId,
            target,
          },
        })
      );
    };

    // 📝 Register event listeners
    stage.on('pointerdown', handlePointerDown);
    stage.on('pointerdblclick', handleDoubleClick);
    stage.on('contextmenu', handleContextMenu);

    // Removed spammy event listener registration log

    // 🔍 Debug: Test if stage is receiving any events (DISABLED - too spammy)
    // const debugHandler = () => console.log('[SelectionManager] DEBUG: Stage received ANY pointer event');
    // stage.on('pointerdown', debugHandler);

    // 🧹 Return cleanup function
    return () => {
      stage.off('pointerdown', handlePointerDown);
      stage.off('pointerdblclick', handleDoubleClick);
      stage.off('contextmenu', handleContextMenu);
      // stage.off('pointerdown', debugHandler);
      // Removed spammy event listener cleanup log
    };
  }, [selectedIds]); // Include selectedIds so event handlers get fresh values

  // This component doesn't render anything - it just manages events
  return null;
};

export default SelectionManager;
