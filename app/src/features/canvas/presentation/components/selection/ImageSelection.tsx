/**
 * 🎯 Image Selection Component - MODULAR VERSION
 * @description Handles image selection and transformation using Konva.Transformer
 * @responsibility Image selection, resize handles, transformation events
 * @ai_context Modular replacement for BaseScene selection system
 */

import React, { useEffect, useRef } from 'react';
import Konva from 'konva';
import { Transformer } from 'react-konva';
import { useCanvasToolbarStore, useElementDomainStore } from '../../../domain/state';
import { ElementService } from '../../../domain/services';
import { useShallow } from 'zustand/react/shallow';

/**
 * 🎯 ImageSelection Component
 * Provides transformation handles for selected images
 */
const ImageSelection: React.FC = () => {
  const transformerRef = useRef<Konva.Transformer>(null);

  // 🧠 Get selected image IDs from domain state
  const { selectedIds } = useCanvasToolbarStore(
    useShallow((state) => ({
      selectedIds: state.selectedIds,
    }))
  );

  // 🧠 Get images from domain state
  const { images } = useElementDomainStore(
    useShallow((state) => ({
      images: state.images,
    }))
  );

  // 🔄 Update transformer when selection changes
  useEffect(() => {
    const transformer = transformerRef.current;
    if (!transformer) return;

    // Find the selected image nodes on the stage
    const stage = transformer.getStage();
    if (!stage) return;

    const selectedNodes: Konva.Node[] = [];

    selectedIds.forEach((id) => {
      console.log(`[ImageSelection] Looking for node with ID: ${id}`);

      // Look for Group nodes with the selected ID (URLImage creates Groups)
      const groupNode = stage.findOne(`#${id}`) as Konva.Group;
      console.log(`[ImageSelection] Found node:`, groupNode, `className: ${groupNode?.className}`);

      if (groupNode && groupNode instanceof Konva.Group) {
        selectedNodes.push(groupNode);
        console.log(`[ImageSelection] Found selected group: ${id}`);
      } else {
        console.log(`[ImageSelection] Could not find group with ID: ${id}`);
      }
    });

    // Attach transformer to selected nodes
    transformer.nodes(selectedNodes);

    // Removed spammy transformer attachment log
  }, [selectedIds]);

  // 🔄 Handle transformation events
  const handleTransformEnd = () => {
    const transformer = transformerRef.current;
    if (!transformer) return;

    const nodes = transformer.nodes();

    nodes.forEach((node) => {
      if (node.className === 'Group') {
        const imageId = node.id();

        // Get the current transform values
        const scaleX = node.scaleX();
        const scaleY = node.scaleY();
        const x = node.x();
        const y = node.y();
        const rotation = node.rotation();

        // Calculate new width and height based on scale
        const currentImage = images.find((img) => img.id === imageId);
        if (currentImage) {
          const newWidth = currentImage.width * scaleX;
          const newHeight = currentImage.height * scaleY;

          // Reset scale to 1 and update width/height
          node.scaleX(1);
          node.scaleY(1);

          // Update domain state with new dimensions
          ElementService.images.update(imageId, {
            ...currentImage,
            x,
            y,
            width: newWidth,
            height: newHeight,
            rotation: rotation * (180 / Math.PI), // Convert to degrees
          });

          console.log(`[ImageSelection] Updated image ${imageId}:`, {
            x,
            y,
            width: newWidth,
            height: newHeight,
            rotation: rotation * (180 / Math.PI),
          });
        }
      }
    });
  };

  return (
    <Transformer
      ref={transformerRef}
      flipEnabled={false}
      borderStrokeWidth={2}
      borderStroke='#9EA581' // Brand green
      anchorStroke='#9EA581'
      anchorFill='#9EA581'
      anchorStrokeWidth={1}
      anchorSize={14}
      anchorCornerRadius={10}
      onTransformEnd={handleTransformEnd}
      anchorStyleFunc={(anchor) => {
        // Style the transformation handles
        anchor.stroke('#9EA581');
        anchor.fill('#9EA581');
        anchor.strokeWidth(1);

        if (anchor.hasName('top-center') || anchor.hasName('bottom-center')) {
          anchor.height(6);
          anchor.offsetY(3);
          anchor.width(26);
          anchor.offsetX(13);
        } else if (anchor.hasName('middle-left') || anchor.hasName('middle-right')) {
          anchor.height(26);
          anchor.offsetY(13);
          anchor.width(6);
          anchor.offsetX(3);
        } else if (anchor.hasName('rotater')) {
          anchor.cornerRadius(15);
          anchor.width(26);
          anchor.height(26);
          anchor.offsetX(13);
          anchor.offsetY(13);
        } else {
          anchor.width(14);
          anchor.offsetX(8);
          anchor.height(14);
          anchor.offsetY(8);
        }
      }}
    />
  );
};

export default ImageSelection;
