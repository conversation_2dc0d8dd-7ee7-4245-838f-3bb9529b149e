/**
 * 🎯 Drag Selection Component - MODULAR VERSION
 * @description Handles drag selection rectangle for multi-element selection
 * @responsibility Manage drag selection rectangle and element selection logic
 * @ai_context Modular replacement for old canvas drag selection functionality
 */

import React, { useEffect, useRef, useCallback } from "react";
import Kon<PERSON> from "konva";
import {
	useToolDomainStore,
	useElementDomainStore,
} from "../../../domain/state";
import { DomainCanvasTool } from "../../../domain/state";
import { useShallow } from "zustand/react/shallow";

/**
 * 🎯 DragSelection Props
 */
interface DragSelectionProps {
	stageRef: React.RefObject<Konva.Stage>;
	onSelectionUpdate?: (selection: {
		visible: boolean;
		x: number;
		y: number;
		width: number;
		height: number;
	}) => void;
	canvasElements?: Record<string, any>; // Canvas elements from collaboration state
	selectedIds?: string[]; // Selected element IDs
	onElementsSelect?: (elementIds: string[]) => void; // Callback to select elements
}

/**
 * 🎯 DragSelection Component
 * Handles drag selection rectangle for multi-element selection
 */
const DragSelection: React.FC<DragSelectionProps> = ({
	stageRef,
	onSelectionUpdate,
	canvasElements = {},
	selectedIds = [],
	onElementsSelect,
}) => {
	const isSelectingRef = useRef(false);
	const selectionStartRef = useRef<{ x: number; y: number } | null>(null);

	// Get domain state
	const {
		activeTool,
		scale,
		position,
		selection,
		setSelection,
		hideSelection,
	} = useToolDomainStore(
		useShallow((state) => ({
			activeTool: state.activeTool,
			scale: state.scale,
			position: state.position,
			selection: state.selection,
			setSelection: state.setSelection,
			hideSelection: state.hideSelection,
		})),
	);

	// Use domain store as fallback for element selection functions
	const { selectElements, clearSelection } = useElementDomainStore(
		useShallow((state) => ({
			selectElements: state.selectElements,
			clearSelection: state.clearSelection,
		})),
	);

	// 🔄 Handle mouse down for drag selection - FOLLOWING useEffect RULES
	// ✅ This IS appropriate for useEffect: synchronizing with external system (Konva events)
	useEffect(() => {
		const stage = stageRef.current;
		if (!stage) return;

		// 🖱️ Handle mouse down - start selection
		const handleMouseDown = (e: Konva.KonvaEventObject<MouseEvent>) => {
			// Only handle selection tool
			if (activeTool !== DomainCanvasTool.SELECT) return;

			// Only respond to left-click (button 0), not right-click (button 2)
			if (e.evt.button !== 0) return;

			// Only start selection when clicking on stage background (not on objects)
			if (e.target !== stage) return;

			// Removed spammy drag selection start log
			isSelectingRef.current = true;

			// Get pointer position relative to stage
			const pointer = stage.getPointerPosition();
			if (!pointer) return;

			// Calculate position in canvas coordinates (accounting for scale and position)
			const canvasX = (pointer.x - position.x) / scale;
			const canvasY = (pointer.y - position.y) / scale;

			// Store selection start point
			selectionStartRef.current = { x: canvasX, y: canvasY };

			// Initialize selection rectangle
			setSelection({
				visible: true,
				x: canvasX,
				y: canvasY,
				width: 0,
				height: 0,
			});
		};

		// 🖱️ Handle mouse move - update selection rectangle
		const handleMouseMove = (e: Konva.KonvaEventObject<MouseEvent>) => {
			// Only update if we're currently selecting
			if (!isSelectingRef.current || !selectionStartRef.current) return;

			const pointer = stage.getPointerPosition();
			if (!pointer) return;

			// Calculate current position in canvas coordinates
			const canvasX = (pointer.x - position.x) / scale;
			const canvasY = (pointer.y - position.y) / scale;

			// Calculate rectangle properties
			const startX = selectionStartRef.current.x;
			const startY = selectionStartRef.current.y;

			const rawWidth = canvasX - startX;
			const rawHeight = canvasY - startY;

			// Update selection rectangle (handle negative dimensions)
			const selectionRect = {
				visible: true,
				x: rawWidth < 0 ? canvasX : startX,
				y: rawHeight < 0 ? canvasY : startY, // Fixed: check rawHeight for Y coordinate
				width: Math.abs(rawWidth),
				height: Math.abs(rawHeight),
			};

			setSelection(selectionRect);

			// Broadcast selection rectangle to other users
			if (onSelectionUpdate) {
				onSelectionUpdate(selectionRect);
			}
		};

		// 🖱️ Handle mouse up - finalize selection
		const handleMouseUp = (e: Konva.KonvaEventObject<MouseEvent>) => {
			// Only process if we're selecting
			if (!isSelectingRef.current || !selectionStartRef.current) return;

			// Calculate final selection rectangle using the same logic as mouse move
			const pointer = stage.getPointerPosition();
			if (!pointer) {
				isSelectingRef.current = false;
				selectionStartRef.current = null;
				hideSelection();
				if (onSelectionUpdate) {
					onSelectionUpdate({
						visible: false,
						x: 0,
						y: 0,
						width: 0,
						height: 0,
					});
				}
				return;
			}

			// Calculate current position in canvas coordinates
			const canvasX = (pointer.x - position.x) / scale;
			const canvasY = (pointer.y - position.y) / scale;

			// Calculate rectangle properties
			const startX = selectionStartRef.current.x;
			const startY = selectionStartRef.current.y;

			const rawWidth = canvasX - startX;
			const rawHeight = canvasY - startY;

			// Calculate final selection rectangle (handle negative dimensions)
			const finalSelection = {
				x: rawWidth < 0 ? canvasX : startX,
				y: rawHeight < 0 ? canvasY : startY,
				width: Math.abs(rawWidth),
				height: Math.abs(rawHeight),
			};

			// Clean up state
			isSelectingRef.current = false;
			selectionStartRef.current = null;

			// Always hide selection rectangle first
			hideSelection();

			// Broadcast that selection is hidden
			if (onSelectionUpdate) {
				onSelectionUpdate({ visible: false, x: 0, y: 0, width: 0, height: 0 });
			}

			// If selection rectangle is too small, treat as click (deselect all)
			if (finalSelection.width < 5 && finalSelection.height < 5) {
				if (onElementsSelect) {
					onElementsSelect([]);
				} else {
					clearSelection();
				}
				return;
			}

			// Find elements that intersect with selection rectangle
			const allSelectedIds: string[] = [];

			// Check all canvas elements
			Object.values(canvasElements).forEach((element: any) => {
				if (!element) return;

				// Create bounding box based on element structure
				const baseWidth =
					element.properties?.size?.width || element.width || 50;
				const baseHeight =
					element.properties?.size?.height || element.height || 50;

				// Account for scaling
				const scaleX = element.scaleX || 1;
				const scaleY = element.scaleY || 1;

				const elementBox = {
					x: element.position?.x || element.x || 0,
					y: element.position?.y || element.y || 0,
					width: baseWidth * scaleX,
					height: baseHeight * scaleY,
				};

				// Check if element intersects with selection rectangle
				// Use a robust intersection check
				const hasIntersection =
					finalSelection.x < elementBox.x + elementBox.width &&
					finalSelection.x + finalSelection.width > elementBox.x &&
					finalSelection.y < elementBox.y + elementBox.height &&
					finalSelection.y + finalSelection.height > elementBox.y;

				if (hasIntersection) {
					allSelectedIds.push(element.id);
				}
			});

			// Check if shift key is pressed for multi-select
			const isMultiSelect = e.evt.shiftKey;

			if (isMultiSelect) {
				// Add to existing selection
				const newSelectedIds = [...selectedIds];
				allSelectedIds.forEach((id) => {
					if (!newSelectedIds.includes(id)) {
						newSelectedIds.push(id);
					}
				});

				// Use callback if provided, otherwise use domain store
				if (onElementsSelect) {
					onElementsSelect(newSelectedIds);
				} else {
					selectElements(newSelectedIds);
				}
			} else {
				// Replace selection
				if (onElementsSelect) {
					onElementsSelect(allSelectedIds);
				} else {
					selectElements(allSelectedIds);
				}
			}
		};

		// 📝 Register event listeners
		stage.on("mousedown", handleMouseDown);
		stage.on("mousemove", handleMouseMove);
		stage.on("mouseup", handleMouseUp);

		// Removed spammy event listener logs

		// 🧹 Cleanup
		return () => {
			stage.off("mousedown", handleMouseDown);
			stage.off("mousemove", handleMouseMove);
			stage.off("mouseup", handleMouseUp);
			// Removed spammy cleanup log
		};
	}, [
		activeTool,
		scale,
		position,
		selection,
		canvasElements,
		selectedIds,
		setSelection,
		hideSelection,
		selectElements,
		clearSelection,
		onElementsSelect,
	]);

	// This component doesn't render anything - it just manages drag selection
	return null;
};

export default DragSelection;
