import React from 'react';
import ToolButton from '../ToolButton';
import { BiPointer } from 'react-icons/bi';

interface SelectToolProps {
  active: boolean;
  onToggle: () => void;
}

/**
 * Select tool for selecting and manipulating objects
 */
const SelectTool: React.FC<SelectToolProps> = ({ active, onToggle }) => {
  return (
    <ToolButton
      label='Select Tool'
      active={active}
      onClick={onToggle}
      className='select-tool'
      icon={<BiPointer size={18} />}
    />
  );
};

export default SelectTool;
