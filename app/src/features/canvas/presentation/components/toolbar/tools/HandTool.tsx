import React from 'react';
import ToolButton from '../ToolButton';
import { FaRegHandPaper } from 'react-icons/fa';

interface HandToolProps {
  active: boolean;
  onToggle: () => void;
}

/**
 * Hand tool for panning the canvas
 */
const HandTool: React.FC<HandToolProps> = ({ active, onToggle }) => {
  return (
    <ToolButton
      label='Hand Tool'
      active={active}
      onClick={onToggle}
      className='hand-tool'
      icon={<FaRegHandPaper size={18} />}
    />
  );
};

export default HandTool;
