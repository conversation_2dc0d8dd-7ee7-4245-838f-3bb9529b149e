/**
 * 🔧 Modular Toolbar Component
 *
 * @description Main toolbar component that orchestrates all toolbar functionality
 * @responsibility Render canvas toolbar and masking toolbar based on state
 * @ai_context This is the main toolbar component for our modular canvas architecture
 */

import React, { useCallback } from 'react';
import CanvasToolbar from './CanvasToolbar'; // 🏗️ Using our own modular copy!
import MaskingToolbar from './MaskingToolbar'; // 🏗️ Using our own modular copy!
// 🧠 MIGRATED: Using domain state and services instead of old canvas store
import { useElementDomainStore, useToolDomainStore } from '../../../domain/state';
import { ToolService } from '../../../domain/services';

/**
 * 🔧 Toolbar Props Interface
 * @ai_context Props for the Toolbar component
 */
export interface ToolbarProps {
  className?: string;
  createElement: (elementData: any) => void;
}

/**
 * 🔧 Toolbar Component - MODULAR ARCHITECTURE
 * @ai_context Main toolbar component that shows appropriate toolbar based on canvas state
 */
export const Toolbar: React.FC<ToolbarProps> = ({ className, createElement }) => {
  // 🧠 Using domain stores instead of old canvas store
  const { images } = useElementDomainStore();
  const { activeTool } = useToolDomainStore();

  // 🚨 TODO: Add mask drawing state to domain
  // For now, use simple state (can be enhanced later)
  const maskDrawing = {
    showToolbar: false,
    targetImageId: null,
    isDrawing: false,
  };

  // Handle mask application
  const handleApplyMask = useCallback(async () => {
    try {
      console.log('[Toolbar] Applying mask for image:', maskDrawing.targetImageId);

      // 🧠 Using domain service instead of direct store mutation
      ToolService.tools.selectTool();
      ToolService.masking.stopMaskDrawing();

      console.log('[Toolbar] Mask applied successfully');
    } catch (error) {
      console.error('[Toolbar] Error applying mask:', error);
      ToolService.masking.cancelMaskDrawing();
    }
  }, [maskDrawing.targetImageId]);

  // Get the target image position for masking toolbar
  const getImagePosition = useCallback(() => {
    if (maskDrawing.targetImageId) {
      const targetImage = images.find((img: any) => img.id === maskDrawing.targetImageId);
      if (targetImage) {
        return {
          x: targetImage.x || 0,
          y: targetImage.y || 0,
          width: targetImage.width || 300,
          height: targetImage.height || 200,
        };
      }
    }

    // Default position if no target image found
    return {
      x: 100,
      y: 100,
      width: 300,
      height: 200,
    };
  }, [maskDrawing.targetImageId, images]);

  return (
    <div className={`toolbar-container ${className || ''}`}>
      {/* Show masking toolbar when in mask drawing mode */}
      {maskDrawing.showToolbar ? (
        <MaskingToolbar onApplyMask={handleApplyMask} imagePosition={getImagePosition()} />
      ) : (
        <CanvasToolbar createElement={createElement} />
      )}
    </div>
  );
};

export default Toolbar;
