import React, { useState, useEffect, useRef, useMemo, memo } from 'react';
import { Paintbrush, Eraser, Circle, Trash2, X, Check } from 'lucide-react';
import { Group } from 'react-konva';
import { Html } from 'react-konva-utils';

interface MaskingToolbarProps {
  onApplyMask: () => void;
  onToggleEraseMode?: (activeTool: 'brush' | 'eraser' | null) => void;
  onClearDrawing?: () => void;
  onBrushSizeChange?: (size: number) => void;
  currentTool?: 'brush' | 'eraser' | null;
  currentBrushSize?: number;
  imagePosition: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

const MaskingToolbar: React.FC<MaskingToolbarProps> = ({
  onApplyMask,
  onToggleEraseMode,
  onClearDrawing,
  onBrushSizeChange,
  currentTool = 'brush',
  currentBrushSize = 10,
  imagePosition,
}) => {
  const [showSizePopover, setShowSizePopover] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  const handleToolChange = (tool: 'brush' | 'eraser') => {
    onToggleEraseMode?.(tool);
  };

  const handleBrushSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const size = parseInt(e.target.value);
    onBrushSizeChange?.(size);
  };

  const handleClearMask = () => {
    onClearDrawing?.();
  };

  const handleCancel = () => {
    onToggleEraseMode?.(null);
  };

  const handleApply = async () => {
    try {
      console.log('[MaskingToolbar] Applying mask');
      onApplyMask();
    } catch (error) {
      console.error('[MaskingToolbar] Error applying mask:', error);
    }
  };

  // Handle click outside to close size popover
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Check if the menu exists and the click was outside it
      if (menuRef.current && event.target) {
        const target = event.target as Element;
        const menuElement = menuRef.current as HTMLElement;

        // Check if the click target is not inside the menu
        if (!menuElement.contains(target)) {
          setShowSizePopover(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Update cursor style based on the current tool and brush size
  useEffect(() => {
    const cursorSize = currentBrushSize;
    const cursorColor = currentTool === 'brush' ? '#FFFFFF' : '#000000'; // Pure white/black for cursor

    // Create a custom cursor with the current brush size
    const canvas = document.createElement('canvas');
    canvas.width = cursorSize + 2; // Add 2px for border
    canvas.height = cursorSize + 2;

    const ctx = canvas.getContext('2d');
    if (ctx) {
      // Draw the circle
      ctx.beginPath();
      ctx.arc(cursorSize / 2 + 1, cursorSize / 2 + 1, cursorSize / 2, 0, Math.PI * 2);
      ctx.fillStyle = cursorColor;
      ctx.fill();

      // Draw a border
      ctx.strokeStyle = 'rgba(0, 0, 0, 0.8)';
      ctx.lineWidth = 1;
      ctx.stroke();

      // Convert to data URL
      const dataURL = canvas.toDataURL();

      // Apply the cursor to the document body
      document.body.style.cursor = `url(${dataURL}) ${cursorSize / 2 + 1} ${cursorSize / 2 + 1}, auto`;
    }

    // Clean up when component unmounts
    return () => {
      document.body.style.cursor = 'default';
    };
  }, [currentTool, currentBrushSize]);

  const menuX = useMemo(() => imagePosition.width / 2 - 55, [imagePosition.width]);
  const menuY = -25;

  return (
    <Group x={menuX} y={menuY}>
      <Html
        divProps={{
          style: {
            position: 'absolute',
            zIndex: 1000,
          },
        }}
      >
        <div
          ref={menuRef}
          className='fixed bg-white shadow-md rounded-md flex items-center p-1 z-50'
          style={{
            left: `${menuX}px`,
            top: `${menuY}px`,
            transform: 'translateX(-50%)', // Center horizontally
          }}
        >
          {/* Tool Toggles */}
          <button
            className={`p-2 hover:bg-gray-100 rounded-md transition-colors ${currentTool === 'brush' ? 'bg-gray-100' : ''}`}
            onClick={() => handleToolChange('brush')}
            title='Brush Tool'
          >
            <Paintbrush size={18} />
          </button>

          <button
            className={`p-2 hover:bg-gray-100 rounded-md transition-colors ${currentTool === 'eraser' ? 'bg-gray-100' : ''}`}
            onClick={() => handleToolChange('eraser')}
            title='Eraser Tool'
          >
            <Eraser size={18} />
          </button>

          {/* Brush Size */}
          <div className='relative'>
            <button
              className='p-2 hover:bg-gray-100 rounded-md transition-colors'
              onClick={() => setShowSizePopover(!showSizePopover)}
              title='Brush Size'
            >
              <Circle size={18} />
            </button>

            {/* Size Popover */}
            {showSizePopover && (
              <div
                className='absolute right-0 mt-1 bg-white shadow-lg rounded-md overflow-hidden p-3 z-50'
                style={{ width: '200px' }}
              >
                <div className='flex items-center justify-between mb-2'>
                  <label className='text-gray-700 text-sm'>Brush Size</label>
                  <span className='text-gray-700 text-sm'>{currentBrushSize}px</span>
                </div>
                <input
                  type='range'
                  min='1'
                  max='50'
                  value={currentBrushSize}
                  onChange={handleBrushSizeChange}
                  className='w-full accent-green-600'
                />
                {/* Invisible overlay to detect clicks outside */}
                <div
                  className='fixed inset-0 z-10'
                  onClick={() => setShowSizePopover(false)}
                  style={{ background: 'transparent' }}
                />
              </div>
            )}
          </div>

          {/* Divider */}
          <div className='h-5 w-px bg-gray-300 mx-1'></div>

          {/* Action Buttons */}
          <button
            className='p-2 hover:bg-gray-100 rounded-md transition-colors'
            onClick={handleClearMask}
            title='Clear Mask'
          >
            <Trash2 size={18} />
          </button>

          <button
            className='p-2 hover:bg-gray-100 rounded-md transition-colors'
            onClick={handleCancel}
            title='Cancel Masking'
          >
            <X size={18} />
          </button>

          <button
            className='p-2 hover:bg-gray-100 rounded-md transition-colors text-green-600'
            onClick={handleApply}
            title='Apply Mask'
          >
            <Check size={18} />
          </button>
        </div>
      </Html>
    </Group>
  );
};

export default memo(MaskingToolbar);
