import React from 'react';
import './ToolButton.css';

interface ToolButtonProps {
  label: string;
  icon?: React.ReactNode;
  active?: boolean;
  onClick: () => void;
  className?: string;
}

/**
 * A button component for canvas tools
 */
const ToolButton: React.FC<ToolButtonProps> = ({ label, icon, active = false, onClick, className = '' }) => {
  return (
    <button
      className={`tool-button ${active ? 'tool-button-active' : ''} ${className}`}
      onClick={onClick}
      title={label}
      aria-label={label}
      aria-pressed={active}
    >
      {icon ? <span className='tool-button-icon'>{icon}</span> : <span className='tool-button-label'>{label}</span>}
    </button>
  );
};

export default ToolButton;
