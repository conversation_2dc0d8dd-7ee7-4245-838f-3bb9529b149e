/* 🎨 Canvas Toolbar Styling - Override Tailwind conflicts */

.canvas-toolbar {
  position: fixed !important;
  bottom: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  align-items: center !important;
  justify-content: center !important;
  background: #F9F7ED !important;
  border: 1px solid #E8E4D4 !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
  backdrop-filter: blur(8px) !important;
  z-index: 1000 !important;
  max-width: 90vw !important;
  width: auto !important;
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
  overflow: visible !important;
  gap: 4px !important;
}

/* Reset any inherited styles */
.canvas-toolbar * {
  box-sizing: border-box !important;
}

/* Tool groups */
.element-group,
.action-group,
.history-group,
.debug-group,
.status-group {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 2px !important;
  height: auto !important;
  width: auto !important;
}

/* Toolbar buttons */
.canvas-toolbar .toolbar-button {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: none !important;
  border: none !important;
  margin: 0 2px !important;
  padding: 6px 10px !important;
  border-radius: 3px !important;
  cursor: pointer !important;
  font-size: 14px !important;
  color: #566B46 !important;
  opacity: 0.7 !important;
  transition: all 0.2s ease !important;
  width: auto !important;
  height: auto !important;
  min-width: 32px !important;
  min-height: 32px !important;
  flex-shrink: 0 !important;
}

.canvas-toolbar .toolbar-button:hover {
  background: rgba(158, 165, 129, 0.1) !important;
  opacity: 1 !important;
}

.canvas-toolbar .toolbar-button:disabled {
  opacity: 0.3 !important;
  cursor: not-allowed !important;
}

.canvas-toolbar .toolbar-button.active {
  background: rgba(200, 161, 60, 0.1) !important;
  color: #C8A13C !important;
  opacity: 1 !important;
}

/* Dividers */
.canvas-toolbar .divider {
  width: 1px !important;
  height: 24px !important;
  background: #E8E4D4 !important;
  margin: 0 5px !important;
  flex-shrink: 0 !important;
}

/* Icons */
.canvas-toolbar .icon {
  font-style: normal !important;
  font-size: 16px !important;
  line-height: 1 !important;
  display: inline-block !important;
  width: auto !important;
  height: auto !important;
}

.canvas-toolbar .icon svg {
  width: 18px !important;
  height: 18px !important;
  fill: currentColor !important;
  stroke: currentColor !important;
  display: block !important;
}

/* Toolbar wrappers */
.canvas-toolbar .toolbar-wrapper {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 2px !important;
  padding: 2px !important;
  border-radius: 3px !important;
  cursor: pointer !important;
  transition: background-color 0.2s ease !important;
  width: auto !important;
  height: auto !important;
  flex-shrink: 0 !important;
}

.canvas-toolbar .toolbar-wrapper.active {
  background: rgba(200, 161, 60, 0.1) !important;
  color: #C8A13C !important;
}

/* Override any Tailwind flex/layout classes */
.canvas-toolbar.flex-col {
  flex-direction: row !important;
}

.canvas-toolbar.h-full,
.canvas-toolbar.h-screen,
.canvas-toolbar.min-h-screen {
  height: auto !important;
  min-height: auto !important;
}

.canvas-toolbar.w-full,
.canvas-toolbar.w-screen {
  width: auto !important;
}

/* 📱 Mobile responsive */
@media (max-width: 768px) {
  .canvas-toolbar {
    bottom: 10px !important;
    padding: 6px 8px !important;
    max-width: 95vw !important;
    flex-wrap: wrap !important;
    gap: 2px !important;
  }
  
  .canvas-toolbar .toolbar-button {
    padding: 4px 6px !important;
    min-width: 28px !important;
    min-height: 28px !important;
    font-size: 12px !important;
  }
  
  .canvas-toolbar .icon {
    font-size: 14px !important;
  }
  
  .canvas-toolbar .divider {
    height: 20px !important;
    margin: 0 3px !important;
  }
}

/* 🌙 Dark mode support */
.dark .canvas-toolbar {
  background: rgba(42, 42, 42, 0.95) !important;
  border-color: rgba(158, 165, 129, 0.3) !important;
}

.dark .canvas-toolbar .toolbar-button {
  color: #9EA581 !important;
}

.dark .canvas-toolbar .toolbar-button:hover {
  background: rgba(158, 165, 129, 0.15) !important;
}

.dark .canvas-toolbar .divider {
  background: rgba(158, 165, 129, 0.3) !important;
}

/* 🎯 Focus states for accessibility */
.canvas-toolbar .toolbar-button:focus {
  outline: 2px solid rgba(200, 161, 60, 0.5) !important;
  outline-offset: 2px !important;
}

.canvas-toolbar .toolbar-button:focus:not(:focus-visible) {
  outline: none !important;
}

/* 🔧 High contrast mode */
@media (prefers-contrast: high) {
  .canvas-toolbar {
    border-width: 2px !important;
    border-color: #000000 !important;
  }
  
  .canvas-toolbar .toolbar-button {
    border: 1px solid #000000 !important;
  }
}

/* 🚫 Prevent any layout interference */
.canvas-toolbar,
.canvas-toolbar * {
  position: relative !important;
}

.canvas-toolbar {
  position: fixed !important;
}

/* Override any inherited Tailwind utilities */
.canvas-toolbar {
  all: unset !important;
  position: fixed !important;
  bottom: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  background: #F9F7ED !important;
  border: 1px solid #E8E4D4 !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
  z-index: 1000 !important;
}
