/**
 * 🔧 Canvas Toolbar Component - SIMPLIFIED MODULAR VERSION
 * @description Simplified toolbar component using modular architecture
 * @responsibility Core toolbar operations without complex canvas store dependencies
 * @ai_context Clean modular version focusing on essential toolbar functionality
 */

import React, { useCallback, useState, useRef } from 'react';
import { HandTool, SelectTool } from './tools';
import './CanvasToolbar.css';
// 🧠 MIGRATED: Using modular state instead of canvas store
import { DomainTextElement, useCanvasToolbarStore } from '../../../domain/state';
// 🧠 MIGRATED: Using modular hooks instead of client hooks
import { useReferenceImages, useVoiceAgentRoom } from '../../hooks';
import { ReferenceDropdown } from '../references';
import { useToolDomainStore, DomainCanvasTool } from '../../../domain/state';
import { ToolService } from '../../../domain/services';

import { useAuth } from 'wasp/client/auth';

/**
 * 🔧 Simplified Canvas Toolbar
 * @description Core toolbar functionality without complex dependencies
 */
const CanvasToolBar = ({ createElement }: { createElement: (elementData: any) => void }) => {
  const { data: user } = useAuth();
  const isAdmin = user?.isAdmin || false;

  // 🖼️ Reference dropdown state
  const [showReferenceDropdown, setShowReferenceDropdown] = useState(false);
  const referenceButtonRef = useRef<HTMLButtonElement>(null);

  // 🧠 MIGRATED: Using modular toolbar store
  const {
    canUndo,
    canRedo,
    addImage,
    addText,
    addHtmlElement,
    deleteSelected,
    handleClear,
    handleUndo,
    handleRedo,
    toggleDebugMode,
  } = useCanvasToolbarStore();

  // 🧠 MIGRATED: Using domain store for tool state and toolbar store for other state
  const activeTool = useToolDomainStore((state) => state.activeTool);
  const selectedIds = useCanvasToolbarStore((state) => state.selectedIds);
  const debug = useCanvasToolbarStore((state) => state.debug);

  // Get the room name from our centralized hook
  const roomName = useVoiceAgentRoom();

  // Get the clearReferenceImages function from the useReferenceImages hook
  const { clearReferenceImages } = useReferenceImages(roomName);

  // 🎨 Handle Add Image
  const handleAddImage = useCallback(() => {
    console.log('[CanvasToolbar] Adding image');
    const newImageId = addImage();
    console.log('[CanvasToolbar] Added image with ID:', newImageId);
  }, [addImage]);

  // ✏️ Handle Add Text
  const handleAddText = useCallback(() => {
    console.log('[CanvasToolbar] Adding text');
    const newTextProperties = addText();

    createElement({
      type: 'text',
      position: { x: 300, y: 100 },
      properties: {
        ...newTextProperties,
      } as DomainTextElement,
      zIndex: 1,
      createdBy: 'current-user',
    });
    console.log('[CanvasToolbar] Added text with ID:', newTextProperties.id);
  }, [addText, createElement]);

  // 🌐 Handle Add HTML Element
  const handleAddHtmlElement = useCallback(() => {
    console.log('[CanvasToolbar] Adding HTML element');
    const newHtmlId = addHtmlElement();
    console.log('[CanvasToolbar] Added HTML element with ID:', newHtmlId);
  }, [addHtmlElement]);

  // 🗑️ Handle Delete Selected
  const handleDeleteSelected = useCallback(() => {
    console.log('[CanvasToolbar] Deleting selected elements:', selectedIds);
    deleteSelected();
  }, [deleteSelected, selectedIds]);

  // 🧹 Handle Clear Canvas
  const handleClearCanvas = useCallback(() => {
    console.log('[CanvasToolbar] Clearing canvas');
    handleClear();
    clearReferenceImages();
  }, [handleClear, clearReferenceImages]);

  // ↩️ Handle Undo
  const handleUndoAction = useCallback(() => {
    console.log('[CanvasToolbar] Undo action');
    handleUndo();
  }, [handleUndo]);

  // ↪️ Handle Redo
  const handleRedoAction = useCallback(() => {
    console.log('[CanvasToolbar] Redo action');
    handleRedo();
  }, [handleRedo]);

  // 🔧 Handle Tool Change
  const handleToolChange = useCallback((tool: 'select' | 'hand' | 'mask') => {
    console.log('[CanvasToolbar] Changing tool to:', tool);

    // Map string tools to domain enum and use domain service
    switch (tool) {
      case 'select':
        ToolService.tools.selectTool();
        break;
      case 'hand':
        ToolService.tools.handTool();
        break;
      case 'mask':
        ToolService.tools.setActive(DomainCanvasTool.MASK);
        break;
    }
  }, []);

  // 🐛 Handle Debug Toggle
  const handleDebugToggle = useCallback(() => {
    console.log('[CanvasToolbar] Toggling debug mode');
    toggleDebugMode();
  }, [toggleDebugMode]);

  // 🖼️ Handle Reference Dropdown
  const handleToggleReferences = useCallback(() => {
    console.log('[CanvasToolbar] Toggling reference dropdown');
    setShowReferenceDropdown(!showReferenceDropdown);
  }, [showReferenceDropdown]);

  return (
    <>
      {/* Reference Dropdown */}
      <ReferenceDropdown
        isOpen={showReferenceDropdown}
        onClose={() => setShowReferenceDropdown(false)}
        buttonRef={referenceButtonRef}
        canvasId={localStorage.getItem('selectedModelId') || undefined}
      />

      <div className='canvas-toolbar'>
        {/* Tool Selection */}
        <div className={`toolbar-wrapper ${activeTool === DomainCanvasTool.SELECT ? 'active' : ''}`}>
          <SelectTool active={activeTool === DomainCanvasTool.SELECT} onToggle={() => handleToolChange('select')} />
        </div>
        <div className={`toolbar-wrapper ${activeTool === DomainCanvasTool.HAND ? 'active' : ''}`}>
          <HandTool active={activeTool === DomainCanvasTool.HAND} onToggle={() => handleToolChange('hand')} />
        </div>

        <div className='divider'></div>

        {/* Text Tool */}
        <button onClick={handleAddText} className='toolbar-button' title='Add Text'>
          <i className='icon'>T</i>
        </button>

        {/* Reference Images */}
        <button
          ref={referenceButtonRef}
          onClick={handleToggleReferences}
          className='toolbar-button'
          title='Reference Images'
        >
          <svg className='icon' viewBox='0 0 24 24' fill='none' stroke='currentColor' strokeWidth='1.5'>
            {/* Main image frame */}
            <rect x='3' y='3' width='18' height='14' rx='2' ry='2' fill='none' stroke='currentColor' />

            {/* Image content - mountain and sun */}
            <circle cx='8.5' cy='8.5' r='1.5' fill='currentColor' opacity='0.6' />
            <path
              d='M21 15l-3.5-3.5L14 15'
              stroke='currentColor'
              fill='none'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
            <path d='M14 15l-2-2-4 4' stroke='currentColor' fill='none' strokeLinecap='round' strokeLinejoin='round' />

            {/* Reference indicator - small overlapping images */}
            <rect x='5' y='18' width='6' height='4' rx='1' ry='1' fill='currentColor' opacity='0.3' stroke='none' />
            <rect x='7' y='19.5' width='6' height='4' rx='1' ry='1' fill='currentColor' opacity='0.5' stroke='none' />
            <rect x='9' y='21' width='6' height='4' rx='1' ry='1' fill='none' stroke='currentColor' strokeWidth='1' />

            {/* Star indicator for "reference" */}
            <path d='M18.5 19l1 2 2-1-2 1-1 2-1-2-2 1 2-1-1-2 1 2z' fill='currentColor' opacity='0.8' stroke='none' />
          </svg>
        </button>

        <div className='divider'></div>

        {/* Delete */}
        <button
          onClick={handleDeleteSelected}
          disabled={selectedIds.length === 0}
          className='toolbar-button'
          title='Delete Selected'
        >
          <i className='icon'>🗑</i>
        </button>

        <div className='divider'></div>

        {/* History */}
        <div className='history-group'>
          <button onClick={handleUndoAction} disabled={!canUndo} className='toolbar-button' title='Undo'>
            <i className='icon'>↩</i>
          </button>
          <button onClick={handleRedoAction} disabled={!canRedo} className='toolbar-button' title='Redo'>
            <i className='icon'>↪</i>
          </button>
        </div>

        {/* Clear Canvas */}
        <button onClick={handleClearCanvas} className='toolbar-button' title='Clear Canvas'>
          <i className='icon'>✕</i>
        </button>

        {/* Debug */}
        {isAdmin && (
          <div className='debug-group'>
            <button
              onClick={handleDebugToggle}
              className={`toolbar-button ${debug ? 'active' : ''}`}
              title='Toggle Debug Mode'
            >
              🐛 Debug
            </button>
          </div>
        )}

        {/* Styles now in CanvasToolbar.css */}
      </div>
    </>
  );
};

export default CanvasToolBar;
