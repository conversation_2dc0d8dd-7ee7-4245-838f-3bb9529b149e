/* 🎨 Tool But<PERSON> Styling - Modular Canvas Toolbar */

.tool-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(158, 165, 129, 0.3);
  border-radius: 6px;
  padding: 8px 10px;
  margin: 0 2px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #566B46;
  font-size: 14px;
  min-width: 40px;
  min-height: 40px;
  backdrop-filter: blur(8px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tool-button:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(158, 165, 129, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.tool-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tool-button-active {
  background: rgba(200, 161, 60, 0.15) !important;
  border-color: rgba(200, 161, 60, 0.4) !important;
  color: #C8A13C !important;
}

.tool-button-active:hover {
  background: rgba(200, 161, 60, 0.2) !important;
  border-color: rgba(200, 161, 60, 0.6) !important;
}

.tool-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.tool-button:disabled:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(158, 165, 129, 0.3);
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Icon styling */
.tool-button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.tool-button-icon svg {
  width: 18px;
  height: 18px;
  fill: currentColor;
  stroke: currentColor;
}

/* Label styling */
.tool-button-label {
  font-weight: 500;
  font-size: 14px;
  line-height: 1;
}

/* Specific tool styling */
.hand-tool {
  /* Hand tool specific styling */
}

.select-tool {
  /* Select tool specific styling */
}

/* 🌙 Dark mode support */
.dark .tool-button {
  background: rgba(42, 42, 42, 0.9);
  border-color: rgba(158, 165, 129, 0.3);
  color: #9EA581;
}

.dark .tool-button:hover {
  background: rgba(42, 42, 42, 0.95);
  border-color: rgba(158, 165, 129, 0.5);
}

.dark .tool-button-active {
  background: rgba(200, 161, 60, 0.2) !important;
  border-color: rgba(200, 161, 60, 0.4) !important;
  color: #C8A13C !important;
}

/* 📱 Mobile responsive */
@media (max-width: 768px) {
  .tool-button {
    min-width: 36px;
    min-height: 36px;
    padding: 6px 8px;
  }
  
  .tool-button-icon svg {
    width: 16px;
    height: 16px;
  }
  
  .tool-button-label {
    font-size: 12px;
  }
}

/* 🎯 Focus states for accessibility */
.tool-button:focus {
  outline: 2px solid rgba(200, 161, 60, 0.5);
  outline-offset: 2px;
}

.tool-button:focus:not(:focus-visible) {
  outline: none;
}

/* 🎨 Animation for state changes */
.tool-button-icon {
  transition: transform 0.2s ease;
}

.tool-button:hover .tool-button-icon {
  transform: scale(1.05);
}

.tool-button-active .tool-button-icon {
  transform: scale(1.1);
}

/* 🔧 High contrast mode */
@media (prefers-contrast: high) {
  .tool-button {
    border-width: 2px;
    border-color: #000000;
  }
  
  .tool-button-active {
    background: #C8A13C !important;
    color: #000000 !important;
  }
}
