import React from 'react';
import Konva from 'konva';
import { Stage, Layer, Rect } from 'react-konva';
import { CanvasElement } from '../../hooks/useCanvasSync';
import CanvasElements from '../elements/CanvasElements';
import { CursorManager } from '../collaboration';
import CollaborativeConceptCards from '../cards/CollaborativeConceptCards';
import { ConceptCardData } from '../../../domain/types';
import { ToolService } from '../../../domain/services';
import KonvaConceptCards from '../cards/KonvaConceptCards';

// Define the collaborative card types locally since they are not exported
interface CollaborativeLoadingCard {
  id: string;
  position: { x: number; y: number };
  userId: string;
  userName: string;
  userColor: string;
  requestText: string;
  createdAt: number;
}

interface CollaborativeConceptCard extends ConceptCardData {
  userId: string;
  userName: string;
  userColor: string;
  createdAt: number;
}

interface CanvasStageProps {
  stageRef: React.RefObject<Konva.Stage>;
  className: string;
  width: number;
  height: number;
  scale: number;
  x: number;
  y: number;
  isDraggable: boolean;
  onMouseDown: (e: Konva.KonvaEventObject<MouseEvent>) => void;
  onMouseMove: (e: Konva.KonvaEventObject<MouseEvent>) => void;
  onMouseUp: (e: Konva.KonvaEventObject<MouseEvent>) => void;
  onClick: (e: Konva.KonvaEventObject<MouseEvent>) => void;
  onWheel: (e: Konva.KonvaEventObject<WheelEvent>) => void;
  onContextMenu: (e: Konva.KonvaEventObject<PointerEvent>) => void;
  canvasState: {
    elements: { [key: string]: CanvasElement };
    selectedIds: string[];
  };
  selection: {
    visible: boolean;
    x: number;
    y: number;
    width: number;
    height: number;
  };
  userSelections: {
    [key: string]: {
      visible: boolean;
      x: number;
      y: number;
      width: number;
      height: number;
      color: string;
    };
  };
  users: any[]; // Consider a more specific type
  currentUserId: string;
  currentUserColor: string;
  collaborativeLoadingCards: Record<string, CollaborativeLoadingCard>;
  collaborativeConceptCards: Record<string, CollaborativeConceptCard>;
  handleElementSelect: (id: string) => void;
  handleElementUpdate: (id: string, changes: Partial<CanvasElement>, throttle?: boolean) => void;
  updateConceptCard: (id: string, updates: any) => void;
  removeConceptCard: (id: string) => void;
  removeLocalConceptCard: (id: string) => void;
}

export const CanvasStage: React.FC<CanvasStageProps> = ({
  stageRef,
  className,
  width,
  height,
  scale,
  x,
  y,
  isDraggable,
  onMouseDown,
  onMouseMove,
  onMouseUp,
  onClick,
  onWheel,
  onContextMenu,
  canvasState,
  selection,
  userSelections,
  users,
  currentUserId,
  currentUserColor,
  collaborativeLoadingCards,
  collaborativeConceptCards,
  handleElementSelect,
  handleElementUpdate,
  updateConceptCard,
  removeConceptCard,
  removeLocalConceptCard,
}) => {
  const handleDragEnd = (e: Konva.KonvaEventObject<DragEvent>) => {
    ToolService.canvas.setPosition(e.target.position());
  };

  return (
    <Stage
      ref={stageRef}
      className={className}
      width={width}
      height={height}
      scaleX={scale}
      scaleY={scale}
      x={x}
      y={y}
      draggable={isDraggable}
      onMouseDown={onMouseDown}
      onMouseMove={onMouseMove}
      onMouseUp={onMouseUp}
      onClick={onClick}
      onWheel={onWheel}
      onContextMenu={onContextMenu}
      onDragEnd={handleDragEnd}
    >
      <Layer>
        {Object.values(canvasState.elements).map((element: CanvasElement) => (
          <CanvasElements
            key={element.id}
            element={element}
            isSelected={canvasState.selectedIds.includes(element.id)}
            onSelect={handleElementSelect}
            onUpdate={handleElementUpdate}
          />
        ))}

        {selection.visible && (
          <Rect
            x={selection.x}
            y={selection.y}
            width={selection.width}
            height={selection.height}
            fill={currentUserColor}
            opacity={0.2}
            stroke={currentUserColor}
            strokeWidth={2 / scale}
            dash={[6 / scale, 4 / scale]}
            perfectDrawEnabled={false}
            listening={false}
          />
        )}

        {Object.entries(userSelections).map(([userId, userSelection]) => {
          if (userId === currentUserId || !userSelection.visible) return null;
          return (
            <Rect
              key={`selection-${userId}`}
              x={userSelection.x}
              y={userSelection.y}
              width={userSelection.width}
              height={userSelection.height}
              fill={userSelection.color}
              opacity={0.2}
              stroke={userSelection.color}
              strokeWidth={2 / scale}
              dash={[6 / scale, 4 / scale]}
              perfectDrawEnabled={false}
              listening={false}
            />
          );
        })}

        <CursorManager activeUsers={users} currentUserId={currentUserId} />

        {/* Local user concept and loading cards */}
        <KonvaConceptCards
          onAnswer={() => {}}
          onGenerate={() => {}}
          onRemove={() => {}}
          getNewCardPosition={() => ({ x: 120, y: 120 })}
          canvasId={undefined}
        />

        <CollaborativeConceptCards
          currentUserId={currentUserId}
          collaborativeLoadingCards={collaborativeLoadingCards}
          collaborativeConceptCards={collaborativeConceptCards}
          onConceptCardUpdate={updateConceptCard}
          onConceptCardRemove={removeConceptCard}
          onLocalConceptCardRemove={removeLocalConceptCard}
        />
      </Layer>
    </Stage>
  );
}; 