/**
 * 🎨 Canvas Presentation Component
 *
 * @description Main canvas component - MIGRATED from /client/canvas/KonvaCanvas.tsx
 * @responsibility Renders the canvas UI with proper modular structure
 * @dependencies Canvas store (will be replaced with domain services)
 * @ai_context This is the actual KonvaCanvas code moved to modular structure
 */

import React, { useRef, useCallback, useEffect, useState } from 'react';
import Konva from 'konva';
import { v4 as uuidv4 } from 'uuid';
import { Stage, Layer, Rect, Line } from 'react-konva';
import './Canvas.css';

// Import modular canvas components
import { ImageElements, TextElements, HtmlElements } from '../elements';
import { Toolbar } from '../toolbar';
import { Controls } from '../controls';
import { SelectionManager, ImageSelection, DragSelection, TextSelection } from '../selection';
import { CropHandler } from '../cropping';
import { SnapGuides, SnapMagnet, DragEventDispatcher, SnapConnections, SnapGroupDragger } from '../snapping';
import { ContextMenuManager, ContextMenuProvider } from '../context-menu';
import { CollaborationBox, CursorManager, OffCanvasIndicators } from '../collaboration';
import CollaborativeChatBubbles from '../chat/CollaborativeChatBubbles';
import { ProductSelector } from '../references';

// Import pure domain services and state
import { ElementService, ToolService } from '../../../domain/services';
import {
  useElementDomainStore,
  useToolDomainStore,
  DomainCanvasTool,
  type DomainImageElement,
} from '../../../domain/state';

// Import remaining utilities (MIGRATED to Cloudflare Workers)
// import { useSocketListener } from 'wasp/client/webSocket'; // REMOVED - now using Cloudflare Workers
import { useAuth } from 'wasp/client/auth';
import { useShallow } from 'zustand/react/shallow';

// Import modular hooks
// import { useAuthenticatedSocket } from '../../hooks';

// Import sidebar state for responsive positioning
import { useSidebarState } from '../../../../../client/hooks/useSidebarState';

// Import modular ChatBubble component
import { ChatBubble } from '../chat';

// Import modular card components
import { ConceptCardCreator, KonvaConceptCards, CollaborativeConceptCards } from '../cards';

// Import newsletter update handler
import { NewsletterUpdateHandler } from '../newsletter';

// Import canvas sharing components
import { CanvasHeaderShareButton } from '../sharing';

// Import canvas hook for getting canvas data
import { useCanvas } from '../../hooks/useCanvas.hook';

// Import scene management system
import { SceneManagerContext } from '../scenes';
import { CanvasElement, useCanvasSync } from '../../hooks/useCanvasSync';
import CanvasElements from '../elements/CanvasElements';
import { calculateScaledDimensions } from '../../utils';
import useEventListener from '../../../../../client/hooks/useEventListener';
import { ImageProperties } from '../elements/images/URLImage';

// Enable all events on Konva, even when dragging a node
Konva.hitOnDragEnabled = true;

/**
 * 🎨 Canvas Props Interface
 * @ai_context Props for the Canvas component
 */
export interface CanvasProps {
  canvasId?: string;
  className?: string;
  children?: React.ReactNode;
  updateCursorPosition?: (point: { x: number; y: number }) => void;
  // Shared canvas props
  sharedCanvasToken?: string;
  sharedCanvasData?: {
    permission: string;
    sharedBy: {
      username: string;
      email?: string;
    };
  };
}

/**
 * 🎨 Canvas Component - MIGRATED FROM KonvaCanvas
 * @ai_context This is the actual KonvaCanvas code moved to modular structure
 */
export const Canvas: React.FC<CanvasProps> = ({
  canvasId,
  className,
  sharedCanvasToken,
  sharedCanvasData
}) => {
  // const { socket: _socket, isConnected: _isConnected } = useAuthenticatedSocket();

  // Get auth data to determine if user is authenticated
  const { data: user } = useAuth();

  // Get canvas data for share button (only for authenticated users, not for shared canvas guests)
  const { canvas: canvasData } = useCanvas(user ? canvasId : undefined);

  // Refs
  const stageRef = useRef<Konva.Stage>(null);
  const {
    isConnected,
    canvasState,
    users,
    userSelections,
    updateCursor,
    createElement,
    updateElement,
    deleteElement,
    setSelectedIds,
    uploadImage,
    updateUserName,
    updateSelection,
    createChatBubble,
    updateChatBubble,
    closeChatBubble,
    chatBubbles,
    createLoadingCard,
    removeLoadingCard,
    updateLoadingCard,
    createConceptCard,
    updateConceptCard,
    removeConceptCard,
    removeLocalConceptCard,
    collaborativeLoadingCards,
    collaborativeConceptCards,
    currentUserName,
    currentUserId,
    currentUserColor,
    isGuest,
  } = useCanvasSync({
    roomId: canvasId || 'testingRoom',
    sharedCanvasToken,
    // Don't provide guestUserInfo - let useCanvasSync handle guest user generation
    guestUserInfo: undefined,
  });
  const containerRef = useRef<HTMLDivElement>(null);

  // Debug: console.log('websocket: ', { canvasState, users });
  // Removed spammy user selections log

  const handleElementUpdate = useCallback(
    (elementId: string, changes: Partial<CanvasElement>, throttle?: boolean) => {
      console.log('[Canvas] Updating element:', elementId, changes);
      updateElement(elementId, changes, throttle);
    },
    [updateElement]
  );

  const handleElementSelect = useCallback(
    (elementId: string) => {
      setSelectedIds([elementId]);
    },
    [setSelectedIds]
  );

  const handleDeleteAction = useCallback(() => {
    canvasState.selectedIds.forEach((id) => {
      deleteElement(id);
    });
  }, [canvasState.selectedIds, deleteElement]);

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (e.key === 'Delete' || e.key === 'Backspace') {
        handleDeleteAction();
      }
    },
    [handleDeleteAction]
  );

  const handleRemoveImage = useCallback(() => {
    handleDeleteAction();
  }, [handleDeleteAction]);

  const handlePaste = useCallback(
    async (e: ClipboardEvent) => {
      if (!e.clipboardData) return;

      const items = Array.from(e.clipboardData.items);
      const imageItem = items.find((item) => item.type.startsWith('image/'));

      if (imageItem) {
        console.log('[Canvas] Image found in clipboard');
        e.preventDefault();

        const file = imageItem.getAsFile();
        if (file) {
          const imageUrl = await uploadImage(file);
          console.log('handlePaste Image uploaded, URL:', imageUrl);

          if (imageUrl) {
            // Create a temporary image to get dimensions from the uploaded R2 URL
            const img = new Image();

            img.onload = () => {
              console.log('Image loaded successfully:', imageUrl, img.naturalWidth, img.naturalHeight);

              // Calculate scaled dimensions (max 400x400)
              const scaledDimensions = calculateScaledDimensions(img.naturalWidth, img.naturalHeight, 400, 400);

              // Create canvas element with the actual R2 URL
              createElement({
                type: 'image',
                position: position || { x: 100, y: 100 },
                properties: {
                  id: `image-${uuidv4()}`,
                  url: imageUrl,
                  originalName: file.name,
                  size: scaledDimensions,
                },
                zIndex: Object.keys(canvasState.elements).length + 1,
                createdBy: 'current-user',
              });
            };

            img.onerror = (error) => {
              console.error('Failed to load uploaded image:', imageUrl, error);
              alert('Image uploaded but failed to display. Please check the console for details.');
            };

            // Load the uploaded image from R2
            console.log('Loading image from URL:', imageUrl);
            img.src = imageUrl;
          } else {
            console.error('No image URL returned from upload');
            alert('Failed to upload image. Please try again.');
          }
        }
      }
    },
    [uploadImage]
  );

  const handleDuplicateImage = useCallback(() => {
    canvasState.selectedIds.forEach((id) => {
      const element = canvasState.elements[id];
      if (element) {
        createElement({
          type: 'image',
          position: { x: element.position.x + 20 || 100, y: element.position.y + 20 || 100 },
          properties: {
            ...element.properties,
            id: `image-${uuidv4()}`,
            isReference: false,
          },
          zIndex: Object.keys(canvasState.elements).length + 1,
          createdBy: 'current-user',
        });
      }
    });
  }, [canvasState.elements, canvasState.selectedIds, createElement]);

  const handleSetImageAsReference = useCallback(() => {
    canvasState.selectedIds.forEach((id) => {
      const element = canvasState.elements[id];
      if (element && element.type === 'image' && !(element.properties as ImageProperties).isReference) {
        updateElement(id, {
          ...element,
          properties: {
            ...element.properties,
            isReference: true,
          },
        });
      }
    });
  }, [canvasState.elements, canvasState.selectedIds, updateElement]);

  const handleBringForward = useCallback(() => {
    canvasState.selectedIds.forEach((id) => {
      const element = canvasState.elements[id];
      if (element && element.type === 'image') {
        updateElement(id, {
          ...element,
          zIndex: element.zIndex + 1,
        });
      }
    });
  }, [canvasState.elements, canvasState.selectedIds, updateElement]);

  const handleSendBackward = useCallback(() => {
    canvasState.selectedIds.forEach((id) => {
      const element = canvasState.elements[id];
      if (element && element.type === 'image') {
        updateElement(id, {
          ...element,
          zIndex: element.zIndex - 1,
        });
      }
    });
  }, [canvasState.elements, canvasState.selectedIds, updateElement]);

  useEventListener('keydown', handleKeyDown, [handleKeyDown]);
  useEventListener('removeImage' as keyof WindowEventMap, handleRemoveImage, [handleRemoveImage]);
  useEventListener('paste', handlePaste, [handlePaste]);
  useEventListener('duplicateImage' as keyof WindowEventMap, handleDuplicateImage, [handleDuplicateImage]);
  useEventListener('setImageAsReference' as keyof WindowEventMap, handleSetImageAsReference, [
    handleSetImageAsReference,
  ]);
  useEventListener('bringForward' as keyof WindowEventMap, handleBringForward, [handleBringForward]);
  useEventListener('sendBackward' as keyof WindowEventMap, handleSendBackward, [handleSendBackward]);

  // Canvas size state
  const [canvasViewportSize, setCanvasViewportSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  });

  // Get canvas state from pure domain stores with shallow comparison
  const { images, selectedIds } = useElementDomainStore(
    useShallow((state) => ({
      images: state.images,
      selectedIds: state.selectedIds,
    }))
  );

  const { activeTool, scale, position, selection, isDragging, maskDrawing } = useToolDomainStore(
    useShallow((state) => ({
      activeTool: state.activeTool,
      scale: state.scale,
      position: state.position,
      selection: state.selection,
      isDragging: state.isDragging,
      maskDrawing: state.maskDrawing,
    }))
  );

  // Handle jumping to user's cursor position
  const handleJumpToUser = useCallback((userId: string, position: { x: number; y: number }) => {
    if (!stageRef.current) return;

    const stage = stageRef.current;
    const stageWidth = stage.width();
    const stageHeight = stage.height();

    // Calculate position to center the user's cursor on screen
    const newPosition = {
      x: (stageWidth / 2) - (position.x * scale),
      y: (stageHeight / 2) - (position.y * scale),
    };

    // Update canvas position using domain service
    ToolService.canvas.setPosition(newPosition);

    // Update Konva stage position
    stage.position(newPosition);
    stage.batchDraw();
  }, [scale]);

  // Store the previous tool when using middle mouse button for panning
  const [previousTool, setPreviousTool] = useState<DomainCanvasTool>(DomainCanvasTool.SELECT);

  // 💬 Chat bubble state (MIGRATED from canvas.tsx)
  const [chatBubble, setChatBubble] = useState({
    isVisible: false,
    position: { x: 0, y: 0 },
    productId: undefined as string | undefined,
  });

  // 👥 Collaboration state
  // const [activeUsers, setActiveUsers] = useState<CollaborationUser[]>([]);

  // 🖱️ Panning state
  const [isPanning, setIsPanning] = useState(false);
  const [isMiddleMousePanning, setIsMiddleMousePanning] = useState(false);

  // Handle saving the mask locally using domain services
  const handleApplyMask = useCallback(
    async (maskDataUrl: string) => {
      try {
        console.log('[Canvas] Saving mask for image:', maskDrawing.targetImageId);

        if (!maskDrawing.targetImageId) {
          console.error('[Canvas] No target image for mask');
          return;
        }

        // Save the mask URL in the domain state
        ToolService.masking.setState({
          // Note: maskImageUrl not in domain state yet, would need to add it
        });

        console.log('[Canvas] Mask saved successfully:', maskDataUrl.substring(0, 50) + '...');

        // Exit mask drawing mode using domain service
        ToolService.tools.selectTool();
        ToolService.masking.stopMaskDrawing();

        // Find the image in the images array
        const targetImage = images.find((img: DomainImageElement) => img.id === maskDrawing.targetImageId);
        if (targetImage) {
          // Trigger the edit button click to show the KonvaSideChat
          const event = new CustomEvent('showSideChat', {
            detail: {
              imageId: targetImage.id,
            },
          });
          window.dispatchEvent(event);

          console.log('[Canvas] Dispatched showSideChat event for image:', targetImage.id);
        }

        console.log('[Canvas] Mask handling completed');
      } catch (error) {
        console.error('[Canvas] Error saving mask:', error);
        ToolService.masking.cancelMaskDrawing();
      }
    },
    [maskDrawing.targetImageId, images]
  );

  // 🎭 Scene Manager COMPLETELY DISABLED - Using fully modular system
  // const [sceneMgr, setSceneMgr] = useState<SceneManager | null>(null);
  const sceneMgr = null; // Fully disabled - using modular components

  // Removed spammy log: BaseScene COMPLETELY DISABLED

  // 💬 Close chat bubble handler
  const handleCloseChatBubble = useCallback(() => {
    console.log('[Canvas] Closing ChatBubble');
    setChatBubble({
      ...chatBubble,
      isVisible: false,
    });
  }, [chatBubble]);

  // Handle window resize to make canvas responsive
  useEffect(() => {
    const handleResize = () => {
      setCanvasViewportSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Prevent browser zoom/scroll conflicts (MIGRATED from KonvaCanvas)
  useEffect(() => {
    // Prevent browser page zoom when Ctrl+Scroll is used on canvas
    const preventPageZoom = (e: WheelEvent) => {
      if (e.ctrlKey) {
        console.log('[Canvas] Preventing browser page zoom (Ctrl+Wheel)');
        e.preventDefault();
      }
    };

    // Prevent Safari gesture zoom
    const preventSafariZoom = (e: Event) => {
      console.log('[Canvas] Preventing Safari gesture zoom');
      e.preventDefault();
    };

    // Add document-level listeners with capture to intercept before React
    document.addEventListener('wheel', preventPageZoom, { passive: false, capture: true });
    document.addEventListener('gesturestart', preventSafariZoom, { passive: false });
    document.addEventListener('gesturechange', preventSafariZoom, { passive: false });

    return () => {
      document.removeEventListener('wheel', preventPageZoom, { capture: true });
      document.removeEventListener('gesturestart', preventSafariZoom);
      document.removeEventListener('gesturechange', preventSafariZoom);
    };
  }, []);

  // 🖱️ Middle mouse button panning (MIGRATED from KonvaCanvas)
  useEffect(() => {
    let panData: {
      active: boolean;
      initialMousePosition: { x: number; y: number };
      initialStagePosition: { x: number; y: number };
    } | null = null;

    const handleMiddleMouseDown = (e: MouseEvent) => {
      if (e.button === 1 && stageRef.current) {
        // Middle mouse button
        console.log('[Canvas] Middle mouse down - starting pan');
        e.preventDefault();

        // Store previous tool and switch to hand
        const currentTool = ToolService.tools.getActive();
        setPreviousTool(currentTool);
        ToolService.tools.setActive(DomainCanvasTool.HAND);
        setIsMiddleMousePanning(true);

        // Change cursor to grabbing
        document.body.style.cursor = 'grabbing';

        // Store pan data
        panData = {
          active: true,
          initialMousePosition: { x: e.clientX, y: e.clientY },
          initialStagePosition: { ...ToolService.canvas.getPosition() },
        };
      }
    };

    const handleDocumentMouseMove = (e: MouseEvent) => {
      if (panData?.active && stageRef.current) {
        e.preventDefault();
        const { initialMousePosition, initialStagePosition } = panData;
        const dx = e.clientX - initialMousePosition.x;
        const dy = e.clientY - initialMousePosition.y;
        const newPosition = {
          x: initialStagePosition.x + dx,
          y: initialStagePosition.y + dy,
        };

        // Update position using domain service
        ToolService.canvas.setPosition(newPosition);

        // Update Konva stage directly for smooth panning
        const stage = stageRef.current;
        stage.position(newPosition);
        stage.batchDraw();
      }
    };

    const handleMiddleMouseUp = (e: MouseEvent) => {
      if (e.button === 1 && panData?.active) {
        // Middle mouse button
        console.log('[Canvas] Middle mouse up - stopping pan');
        e.preventDefault();

        // Reset cursor
        document.body.style.cursor = '';

        // Restore previous tool
        ToolService.tools.setActive(previousTool);
        setIsMiddleMousePanning(false);

        // Clear pan data
        panData = null;
      }
    };

    // Add document-level listeners
    document.addEventListener('mousedown', handleMiddleMouseDown);
    document.addEventListener('mousemove', handleDocumentMouseMove);
    document.addEventListener('mouseup', handleMiddleMouseUp);

    return () => {
      document.removeEventListener('mousedown', handleMiddleMouseDown);
      document.removeEventListener('mousemove', handleDocumentMouseMove);
      document.removeEventListener('mouseup', handleMiddleMouseUp);

      // Reset cursor on cleanup
      document.body.style.cursor = '';
    };
  }, [previousTool]);

  // 💬 Document click handler to close chat bubble (MIGRATED from canvas.tsx)
  useEffect(() => {
    const handleDocumentClick = (e: MouseEvent) => {
      if (chatBubble.isVisible) {
        // Check if the click is outside the chat bubble
        const bubbleElement = document.querySelector('.chat-bubble-container');
        if (bubbleElement && !bubbleElement.contains(e.target as Node)) {
          // Don't close if the click is on the canvas (we handle that separately)
          const canvasElement = document.querySelector('.konvajs-content');
          if (!canvasElement || !canvasElement.contains(e.target as Node)) {
            handleCloseChatBubble();
          }
        }
      }
    };

    document.addEventListener('click', handleDocumentClick);

    return () => {
      document.removeEventListener('click', handleDocumentClick);
    };
  }, [chatBubble.isVisible, handleCloseChatBubble]);

  // 🧲 Listen for snap group dragging events to sync with collaboration
  useEffect(() => {
    const handleSnapGroupDragging = (event: CustomEvent) => {
      const { id, x, y, isDragging } = event.detail;

      if (isDragging && id) {
        console.log(`[Canvas] Snap group member dragging: ${id} to (${x}, ${y})`);

        // Send position update to collaboration system
        updateElement(id, {
          position: { x, y },
          properties: {
            ...canvasState.elements[id]?.properties,
            x,
            y,
          },
        }, true); // Use throttling for smooth updates
      }
    };

    window.addEventListener('canvas:elementDragging', handleSnapGroupDragging as EventListener);

    return () => {
      window.removeEventListener('canvas:elementDragging', handleSnapGroupDragging as EventListener);
    };
  }, [updateElement, canvasState.elements]);

  // 🧭 Listen for jump-to-position events from off-canvas indicators
  useEffect(() => {
    const handleJumpToPosition = (event: CustomEvent) => {
      const { position: newPosition } = event.detail;

      console.log(`[Canvas] Jumping to position:`, newPosition);

      // Update canvas position using domain service
      ToolService.canvas.setPosition(newPosition);

      // Update Konva stage position
      if (stageRef.current) {
        stageRef.current.position(newPosition);
        stageRef.current.batchDraw();
      }
    };

    window.addEventListener('canvas:jumpToPosition', handleJumpToPosition as EventListener);

    return () => {
      window.removeEventListener('canvas:jumpToPosition', handleJumpToPosition as EventListener);
    };
  }, []);

  // 🖼️ Handle placeholder image creation from concept cards
  useEffect(() => {
    const handleCreatePlaceholder = (event: CustomEvent) => {
      const { cardId, position } = event.detail;

      console.log('[Canvas] 🖼️ Creating placeholder image for card:', cardId);

      // 🌐 Create placeholder via collaborative system (works for local user + collaborators)
      if (createElement) {
        const collaborativeElement = {
          type: 'image' as const,
          position: { x: position.x, y: position.y },
          properties: {
            id: `placeholder-${cardId}`,
            url: 'https://oliviatest.xyz/blur.png',
            originalName: 'Placeholder Image',
            size: { width: 512, height: 512 },
          },
          zIndex: 1,
          rotation: 0,
          scaleX: 1,
          scaleY: 1,
          createdBy: currentUserName || 'Unknown User',
        };

        createElement(collaborativeElement);
        console.log('[Canvas] 🌐 Broadcasted placeholder image to collaborators:', cardId);
      } else {
        console.warn('[Canvas] ⚠️ Collaboration createElement not available for placeholder broadcast');
      }
    };

    window.addEventListener('createPlaceholderImage', handleCreatePlaceholder as EventListener);

    return () => {
      window.removeEventListener('createPlaceholderImage', handleCreatePlaceholder as EventListener);
    };
  }, [createElement, currentUserName]);

  // 🔌 Canvas element add event handler (migrated from WASP WebSocket to Cloudflare Workers)
  useEffect(() => {
    const handleCanvasElementAdd = (event: CustomEvent) => {
      const data = event.detail;
      console.log('[Canvas] 📥 canvas_element_add event');

      try {
        // Extract image data from the event
        const content = data.content || data;
        const imageUrl = content.imageUrls?.[0] || content.imageUrl || content.src;
        const taskId = content.taskId || data.taskId;
        const placeholderId = content.placeholderId || data.placeholderId;

        if (imageUrl && taskId) {
          console.log('[Canvas] Adding generated image to canvas:', { imageUrl, taskId, placeholderId });

          // Check if image already exists (from streaming)
          const existingImages = images;
          const existingImage = existingImages.find((img) => img.taskId === taskId || img.id === `generated-${taskId}`);

          if (existingImage) {
            console.log('[Canvas] Image already exists from streaming, updating with final URL');
            // Update existing image with final URL
            ElementService.images.update(existingImage.id, {
              url: imageUrl,
              opacity: 1.0, // Full opacity for final image
            });
          } else {
            console.log('[Canvas] Creating new image (no streaming version found)');

            // FAIL FAST: Require size information from task result - no fallbacks to prevent chaos
            const result = content.result;
            if (!result || !result.size) {
              const error = `[Canvas] ❌ FAIL FAST: size is required in task result for image ${taskId}. No fallbacks allowed.`;
              console.error(error);
              throw new Error(error);
            }

            const sizeString = result.size;
            const [actualWidth, actualHeight] = sizeString.split('x').map(Number);

            if (!actualWidth || !actualHeight || isNaN(actualWidth) || isNaN(actualHeight)) {
              const error = `[Canvas] ❌ FAIL FAST: invalid size format "${sizeString}" for image ${taskId}. Expected format: "1024x1024".`;
              console.error(error);
              throw new Error(error);
            }

            // Display at 50% size for better canvas layout (keeps full resolution)
            const displayWidth = actualWidth * 0.5;
            const displayHeight = actualHeight * 0.5;

            console.log('[Canvas] Using image dimensions:', {
              actualSize: `${actualWidth}x${actualHeight}`,
              displaySize: `${displayWidth}x${displayHeight}`,
              scale: '50%'
            });

            // Create new image element using domain service
            const newImage: DomainImageElement = {
              id: `generated-${taskId}`,
              url: imageUrl,
              x: content.x || 200,
              y: content.y || 200,
              width: displayWidth,
              height: displayHeight,
              rotation: 0,
              taskId: taskId,
            };

            // Add to domain state
            ElementService.images.add(newImage);
          }

          console.log('[Canvas] Successfully processed generated image');
        }
      } catch (error) {
        console.error('[Canvas] Error processing canvas_element_add:', error);
      }
    };

    window.addEventListener('canvas_element_add', handleCanvasElementAdd as EventListener);

    return () => {
      window.removeEventListener('canvas_element_add', handleCanvasElementAdd as EventListener);
    };
  }, [images]);

  // 🔌 Canvas element update event handler (migrated from WASP WebSocket to Cloudflare Workers)
  useEffect(() => {
    const handleCanvasElementUpdate = (event: CustomEvent) => {
      const data = event.detail;
      console.log('[Canvas] 📝 canvas_element_update event');

      try {
        // Handle image updates
        if (data.content && data.content.imageUrls && data.content.imageUrls.length > 0) {
          const imageUrl = data.content.imageUrls[0];
          const taskId = data.content.taskId || data.taskId;
          const elementId = data.elementId || data.id;

          console.log('[Canvas] Updating image element:', { imageUrl, taskId, elementId });

          // Find and update existing image
          const existingImages = images;
          const imageToUpdate = existingImages.find(
            (img) => img.taskId === taskId || img.id === elementId || img.id === `generated-${taskId}`
          );

          if (imageToUpdate) {
            // Update the image URL using domain service
            ElementService.images.update(imageToUpdate.id, { url: imageUrl });
            console.log('[Canvas] Successfully updated image in modular canvas');
          } else {
            console.log('[Canvas] Image not found for update, creating new one');

            // FAIL FAST: Require size information from task result - no fallbacks to prevent chaos
            const result = data.content.result;
            if (!result || !result.size) {
              const error = `[Canvas] ❌ FAIL FAST: size is required in task result for image update ${taskId}. No fallbacks allowed.`;
              console.error(error);
              throw new Error(error);
            }

            const sizeString = result.size;
            const [actualWidth, actualHeight] = sizeString.split('x').map(Number);

            if (!actualWidth || !actualHeight || isNaN(actualWidth) || isNaN(actualHeight)) {
              const error = `[Canvas] ❌ FAIL FAST: invalid size format "${sizeString}" for image update ${taskId}. Expected format: "1024x1024".`;
              console.error(error);
              throw new Error(error);
            }

            // Display at 50% size for better canvas layout (keeps full resolution)
            const displayWidth = actualWidth * 0.5;
            const displayHeight = actualHeight * 0.5;

            console.log('[Canvas] Using image dimensions for update:', {
              actualSize: `${actualWidth}x${actualHeight}`,
              displaySize: `${displayWidth}x${displayHeight}`,
              scale: '50%'
            });

            // Create new image if not found
            const newImage: DomainImageElement = {
              id: `generated-${taskId}`,
              url: imageUrl,
              x: data.content.x || 200,
              y: data.content.y || 200,
              width: displayWidth,
              height: displayHeight,
              rotation: 0,
              taskId: taskId,
            };
            ElementService.images.add(newImage);
          }
        }
      } catch (error) {
        console.error('[Canvas] Error processing canvas_element_update:', error);
      }
    };

    window.addEventListener('canvas_element_update', handleCanvasElementUpdate as EventListener);

    return () => {
      window.removeEventListener('canvas_element_update', handleCanvasElementUpdate as EventListener);
    };
  }, [images]);

  // 🔌 Task update event handler for streaming partial images (migrated from WASP WebSocket to Cloudflare Workers)
  useEffect(() => {
    const handleTaskUpdate = (event: CustomEvent) => {
      const data = event.detail;
      console.log('[Canvas] 🔍 DEBUG - Full task_update event structure:', JSON.stringify(data, null, 2));
      console.log('[Canvas] 🔍 DEBUG - data.data:', data.data);
      console.log('[Canvas] 🔍 DEBUG - data.result:', data.result);

      try {
        const { taskId, status, progress, result, data: streamingData } = data;

        // 🔍 DEBUG: Log all task_update events to see what we're receiving
        console.log('[Canvas] 🔍 DEBUG task_update event:', {
          taskId,
          status,
          progress,
          hasResult: !!result,
          hasStreamingData: !!streamingData,
          resultType: result?.type,
          streamingDataType: streamingData?.type
        });

        // 🔍 Check both data.result and data.data for streaming images (different WebSocket message formats)
        const imageData = result || streamingData;

        // Skip task status updates - placeholders are now created via collaborative system only

        // Handle streaming partial images
        if (
          imageData &&
          imageData.type &&
          (imageData.type === 'initial_partial_image' || imageData.type === 'update_partial_image') &&
          imageData.imageBase64
        ) {
          console.log('[Canvas] 🎬 Received streaming partial image:', {
            taskId,
            progress,
            type: imageData.type,
            partialIndex: imageData.partialIndex
          });

          // Convert base64 to data URL for display
          const imageDataUrl = `data:image/jpeg;base64,${imageData.imageBase64}`;

          // 🌐 Find collaborative placeholder to replace with streaming image
          const collaborativePlaceholder = Object.values(canvasState.elements).find((element: any) =>
            element.type === 'image' &&
            (element.properties?.url === 'https://oliviatest.xyz/blur.png' ||
             element.properties?.url?.startsWith('data:image/jpeg;base64,')) &&
            element.properties?.id?.includes('placeholder')
          );

          if (collaborativePlaceholder && updateElement) {
            // Update collaborative placeholder with streaming data
            updateElement(collaborativePlaceholder.id, {
              properties: {
                ...collaborativePlaceholder.properties,
                url: imageDataUrl,
              }
            });
            console.log('[Canvas] 🌐 Updated collaborative placeholder with partial data:', collaborativePlaceholder.id);
          } else {
            // Fallback: Check local images for any existing streaming image
            const existingImages = images;
            const imageToUpdate = existingImages.find((img) =>
              img.taskId === taskId || img.id === `generated-${taskId}`
            );

            if (imageToUpdate) {
              ElementService.images.update(imageToUpdate.id, {
                url: imageDataUrl,
                opacity: 0.8,
              });
              console.log('[Canvas] ✅ Updated local streaming image with partial data:', imageToUpdate.id);
            } else {
              // Create new streaming image with proper dimensions (50% display size)
              const newImage: DomainImageElement = {
                id: `generated-${taskId}`,
                url: imageDataUrl,
                x: 200,
                y: 200,
                width: 512, // Display at 50% size (1024 * 0.5)
                height: 512, // Display at 50% size (1024 * 0.5)
                rotation: 0,
                taskId: taskId,
                opacity: 0.8, // Slightly transparent to indicate it's streaming
              };
              ElementService.images.add(newImage);
              console.log('[Canvas] ✅ Created new streaming image with 512x512 display size (1024x1024 resolution)');
            }
          }
        }

        // Handle final image
        else if (imageData && imageData.type === 'final_image' && imageData.imageUrl) {
          console.log('[Canvas] 🎯 Received final image:', { taskId, imageUrl: imageData.imageUrl });

          // 🌐 Find collaborative placeholder to update with final image
          const collaborativePlaceholder = Object.values(canvasState.elements).find((element: any) =>
            element.type === 'image' &&
            (element.properties?.url === 'https://oliviatest.xyz/blur.png' ||
             element.properties?.url?.startsWith('data:image/jpeg;base64,')) &&
            element.properties?.id?.includes('placeholder')
          );

          if (collaborativePlaceholder && updateElement) {
            // Update collaborative placeholder with final image
            updateElement(collaborativePlaceholder.id, {
              properties: {
                ...collaborativePlaceholder.properties,
                url: imageData.imageUrl,
              }
            });
            console.log('[Canvas] 🌐 Updated collaborative placeholder with final URL:', collaborativePlaceholder.id);
          } else {
            // Fallback: Update local streaming image if it exists
            const existingImages = images;
            const imageToUpdate = existingImages.find((img) =>
              img.taskId === taskId || img.id === `generated-${taskId}`
            );

            if (imageToUpdate) {
              ElementService.images.update(imageToUpdate.id, {
                url: imageData.imageUrl,
                opacity: 1.0, // Full opacity for final image
              });
              console.log('[Canvas] ✅ Updated local streaming image with final URL:', imageToUpdate.id);
            }
          }
        }
      } catch (error) {
        console.error('[Canvas] Error processing task_update:', error);
      }
    };

    window.addEventListener('task_update', handleTaskUpdate as EventListener);

    return () => {
      window.removeEventListener('task_update', handleTaskUpdate as EventListener);
    };
  }, [images, canvasState.elements, updateElement]);

  // Determine if stage should be draggable (MIGRATED from canvas.tsx)
  const isDraggable = activeTool === DomainCanvasTool.HAND || (selectedIds.length === 0 && !selection.visible);

  // Get container class name with cursor management (MIGRATED from canvas.tsx)
  const getContainerClassName = () => {
    const classes = ['canvas-container'];

    // Handle hand tool cursor
    if (activeTool === DomainCanvasTool.HAND || isMiddleMousePanning) {
      classes.push('hand-tool');
      if (isPanning || isMiddleMousePanning) {
        classes.push('panning'); // grabbing cursor
      } else {
        classes.push('hand-cursor'); // grab cursor
      }
    } else {
      if (selection.visible) {
        classes.push('selecting');
      } else if (isDragging) {
        classes.push('dragging');
      } else if (selectedIds.length === 0) {
        classes.push('default');
      }
    }

    return classes.join(' ');
  };

  // Basic event handlers with panning support (MIGRATED from canvas.tsx)
  const handleMouseDown = useCallback(
    (e: any) => {
      // Removed spammy mouse down log

      // Handle hand tool panning
      if (activeTool === DomainCanvasTool.HAND) {
        // Removed spammy pan start log
        setIsPanning(true);

        // Store initial position for panning
        const stage = e.target.getStage();
        if (stage) {
          const pointer = stage.getPointerPosition();
          if (pointer) {
            (window as any).__handPanning = {
              active: true,
              initialMousePosition: pointer,
              initialStagePosition: { ...ToolService.canvas.getPosition() },
            };
          }
        }
      }
    },
    [activeTool]
  );

  const handleStageMouseMove = useCallback(
    (e: Konva.KonvaEventObject<MouseEvent>) => {
      const stage = e.target.getStage();
      // Handle hand tool panning
      if (stage) {
        const pointer = stage.getPointerPosition();

        if (pointer) {
          // Convert stage coordinates to canvas coordinates (same as selection rectangle)
          const canvasX = (pointer.x - position.x) / scale;
          const canvasY = (pointer.y - position.y) / scale;
          updateCursor({ x: canvasX, y: canvasY });
        }

        if (activeTool === DomainCanvasTool.HAND && isPanning && (window as any).__handPanning?.active) {
          if (pointer) {
            const { initialMousePosition, initialStagePosition } = (window as any).__handPanning;
            const dx = pointer.x - initialMousePosition.x;
            const dy = pointer.y - initialMousePosition.y;
            const newPosition = {
              x: initialStagePosition.x + dx,
              y: initialStagePosition.y + dy,
            };

            // Update position using domain service
            ToolService.canvas.setPosition(newPosition);

            // Update Konva stage directly for smooth panning
            stage.position(newPosition);
            stage.batchDraw();
          }
        }
      }
    },
    [activeTool, isPanning, position, scale, updateCursor]
  );

  const handleMouseUp = useCallback(
    (e: any) => {
      // Removed spammy mouse up log

      // Handle hand tool panning
      if (activeTool === DomainCanvasTool.HAND && isPanning) {
        // Removed spammy pan stop log
        setIsPanning(false);
        (window as any).__handPanning = null;
      }
    },
    [activeTool, isPanning]
  );

  const handleClick = useCallback(
    (e: Konva.KonvaEventObject<MouseEvent>) => {
      // Removed spammy click log

      // 🎭 Let BaseScene handle image/group clicks - only handle stage background clicks
      const clickedOnStage = e.target === e.target.getStage();

      if (clickedOnStage) {
        setSelectedIds([]);
        // Removed spammy stage background click log

        // 💬 Close chat bubble if clicking on stage background
        if (chatBubble.isVisible) {
          handleCloseChatBubble();
        }

        // 💬 Notify collaborative chat bubbles of background click
        window.dispatchEvent(new CustomEvent('canvas:backgroundClick'));
      } else {
        // Removed spammy element click log
        // Don't interfere with BaseScene selection logic
      }
    },
    [activeTool, chatBubble.isVisible, handleCloseChatBubble, setSelectedIds]
  );

  // 💬 Right-click context menu handler (MIGRATED from canvas.tsx)
  const handleContextMenu = useCallback((e: any) => {
    // Prevent default browser context menu
    e.evt.preventDefault();

    // If the target is a Konva.Image, let individual elements handle it
    if (e.target.className === 'Image') {
      console.log('[Canvas] Context menu on Image, letting element handle it');
      return;
    }

    // If the target is part of an HTMLElement, let HTMLElement handle it
    if (e.target.findAncestor && e.target.findAncestor('.html-element-konva-group')) {
      console.log('[Canvas] Context menu on HTMLElement, letting element handle it');
      return;
    }

    // Get the pointer position relative to the stage
    const stage = e.target.getStage();
    if (!stage) return;

    const pointer = stage.getPointerPosition();
    if (!pointer) return;

    // Convert stage coordinates to screen coordinates
    const stageContainer = stage.container();
    const containerRect = stageContainer.getBoundingClientRect();

    const screenX = containerRect.left + pointer.x;
    const screenY = containerRect.top + pointer.y;

    console.log('[Canvas] Showing ChatBubble at:', { screenX, screenY });

    // Show chat bubble at pointer position
    setChatBubble({
      isVisible: true,
      position: { x: screenX, y: screenY },
      productId: 'canvas-test', // TODO: Get actual product ID
    });

    // Calculate canvas coordinates for collaboration
    const canvasX = (pointer.x - position.x) / scale;
    const canvasY = (pointer.y - position.y) / scale;

    // Also trigger collaborative chat bubble
    window.dispatchEvent(
      new CustomEvent('canvas:rightClick', {
        detail: {
          position: { x: screenX, y: screenY }, // Screen position for local bubble
          canvasPosition: { x: canvasX, y: canvasY }, // Canvas position for collaboration
        },
      })
    );
  }, []);

  // ️ Wheel handler for scroll/pan and zoom (MIGRATED from KonvaCanvas)
  const handleWheel = useCallback((e: any) => {
    e.evt.preventDefault();
    e.evt.stopPropagation();

    const stage = e.target.getStage();
    if (!stage) return;

    const pointer = stage.getPointerPosition();
    if (!pointer) return;

    // 🔧 FIX: Read current values from domain store instead of closure
    const currentState = useToolDomainStore.getState();
    const oldScale = currentState.scale;
    const currentPosition = currentState.position;

    const mousePointTo = {
      x: (pointer.x - currentPosition.x) / oldScale,
      y: (pointer.y - currentPosition.y) / oldScale,
    };

    let actionTaken = false;

    // 🔍 ZOOM with Ctrl+Scroll (like Figma/design tools)
    if (e.evt.ctrlKey) {
      // Removed spammy wheel zoom log

      const zoomSensitivity = 0.0025;
      let scrollDelta = e.evt.deltaY;

      // Invert for natural pinch-zoom feel
      if (e.evt.ctrlKey) {
        scrollDelta *= -1;
      }

      // Apply damping for smooth zoom
      const dampingFactor = 0.75;
      const dampedScrollDelta = scrollDelta * dampingFactor;

      let newScale = oldScale + dampedScrollDelta * zoomSensitivity;
      newScale = Math.max(0.1, Math.min(newScale, 10.0));

      if (Math.abs(oldScale - newScale) > 0.00001) {
        const newPos = {
          x: pointer.x - mousePointTo.x * newScale,
          y: pointer.y - mousePointTo.y * newScale,
        };

        // Update domain state
        ToolService.zoom.setScale(newScale);
        ToolService.canvas.setPosition(newPos);
        actionTaken = true;
      }
    }
    // 🖱️ PAN with Regular Scroll (like scrollbars)
    else {
      // Removed spammy wheel pan log

      const panSensitivity = 1.0;
      const dx = e.evt.deltaX * panSensitivity;
      const dy = e.evt.deltaY * panSensitivity;

      if (Math.abs(dx) > 0.1 || Math.abs(dy) > 0.1) {
        const newPos = {
          x: currentPosition.x - dx,
          y: currentPosition.y - dy,
        };

        // Update domain state
        ToolService.canvas.setPosition(newPos);
        actionTaken = true;
      }
    }

    if (actionTaken) {
      stage.batchDraw();
    }
  }, []); // 🔧 FIX: No dependencies needed since we read current values from domain state

  return (
    <ContextMenuProvider>
      <SceneManagerContext.Provider value={sceneMgr}>
        <div
          className={`canvas-container ${className || ''}`}
          ref={containerRef}
          style={{
            width: '100vw',
            height: '100vh',
            overflow: 'hidden',
            position: 'relative',
          }}
        >
          {/* FEATURE 5: Modular Toolbar */}
          <Toolbar createElement={createElement} />

          {/* FEATURE 1: Basic Konva Stage (MIGRATED) */}
          <Stage
            ref={stageRef}
            className={getContainerClassName()}
            width={canvasViewportSize.width}
            height={canvasViewportSize.height}
            scaleX={scale}
            scaleY={scale}
            x={position.x}
            y={position.y}
            draggable={isDraggable}
            onMouseDown={handleMouseDown}
            onMouseMove={handleStageMouseMove}
            onMouseUp={handleMouseUp}
            onClick={handleClick}
            onWheel={handleWheel}
            onContextMenu={handleContextMenu}
          >
            <Layer>
              {Object.values(canvasState.elements).map((element: CanvasElement) => (
                <CanvasElements
                  key={element.id}
                  element={element}
                  isSelected={canvasState.selectedIds.includes(element.id)}
                  onSelect={handleElementSelect}
                  onUpdate={handleElementUpdate}
                />
              ))}

              {/* 🖼️ FEATURE 2: New Domain Image Elements (MODULAR COMPONENT) */}
              <ImageElements />

              {/* 🎯 FEATURE 16: Drag Selection Rectangle - Current User */}
              {selection.visible && (
                <Rect
                  x={selection.x}
                  y={selection.y}
                  width={selection.width}
                  height={selection.height}
                  fill={currentUserColor}
                  opacity={0.2} // 20% opacity
                  stroke={currentUserColor} // Current user's color
                  strokeWidth={2 / scale} // Slightly thicker stroke for better visibility
                  dash={[6 / scale, 4 / scale]} // Adjust dash based on scale
                  perfectDrawEnabled={false} // Disable perfect drawing for performance
                  listening={false} // Disable events for the selection rectangle
                />
              )}

              {/* 🎯 FEATURE 16: Other Users' Selection Rectangles */}
              {Object.entries(userSelections).map(([userId, userSelection]) => {
                // Don't show current user's selection (already shown above)
                if (userId === currentUserId) return null;

                // Only show visible selections
                if (!userSelection.visible) return null;

                return (
                  <Rect
                    key={`selection-${userId}`}
                    x={userSelection.x}
                    y={userSelection.y}
                    width={userSelection.width}
                    height={userSelection.height}
                    fill={userSelection.color}
                    opacity={0.2} // 20% opacity
                    stroke={userSelection.color} // User's color
                    strokeWidth={2 / scale} // Consistent stroke width
                    dash={[6 / scale, 4 / scale]} // Consistent dash pattern
                    perfectDrawEnabled={false} // Disable perfect drawing for performance
                    listening={false} // Disable events for the selection rectangle
                  />
                );
              })}

              {/* 👥 FEATURE 15: Real-time Collaboration Cursors */}
              <CursorManager activeUsers={users} currentUserId={currentUserId} />

              {/* 🎨 FEATURE 8: Modular Concept Cards */}
              <KonvaConceptCards
                onAnswer={(id: string, questionIndex: number, answer: string) => {
                  console.log('[Canvas] Concept card answer:', { id, questionIndex, answer });
                }}
                onGenerate={(id: string) => {
                  console.log('[Canvas] Concept card generate:', id);
                }}
                onRemove={(id: string) => {
                  console.log('[Canvas] Concept card remove:', id);
                }}
                getNewCardPosition={() => ({ x: 100, y: 100 })}
                onLoadingCardCreate={createLoadingCard}
                onLoadingCardRemove={removeLoadingCard}
                onLoadingCardUpdate={updateLoadingCard}
                onConceptCardCreate={createConceptCard}
                onConceptCardUpdate={updateConceptCard}
                onConceptCardRemove={removeConceptCard}
              />

              {/* 🎨 FEATURE 8b: Collaborative Concept Cards */}
              <CollaborativeConceptCards
                currentUserId={currentUserId}
                collaborativeLoadingCards={collaborativeLoadingCards}
                collaborativeConceptCards={collaborativeConceptCards}
                onConceptCardUpdate={updateConceptCard}
                onConceptCardRemove={removeConceptCard}
                onLocalConceptCardRemove={removeLocalConceptCard}
              />
            </Layer>
          </Stage>

          {/* FEATURE 6: Modular Controls */}
          <Controls position='bottom-right' />

          {/* 💬 FEATURE 7: ChatBubble for Right-Click Context Menu */}
          <ChatBubble
            isVisible={chatBubble.isVisible}
            position={chatBubble.position}
            onClose={handleCloseChatBubble}
            productId={chatBubble.productId}
          />

          {/* 💬 FEATURE 7b: Collaborative Chat Bubbles */}
          <CollaborativeChatBubbles
            currentUserId={currentUserId}
            currentUserName={currentUserName}
            currentUserColor={currentUserColor}
            onBubbleCreate={createChatBubble}
            onBubbleUpdate={updateChatBubble}
            onBubbleClose={closeChatBubble}
            collaborativeBubbles={chatBubbles}
            canvasPosition={position}
            canvasScale={scale}
          />

          {/* 🎨 FEATURE 9: Concept Card Creator (Event Handler) */}
          <ConceptCardCreator canvasId={canvasId} />

          {/* 📰 FEATURE 10: Newsletter Update Handler (Invisible) */}
          <NewsletterUpdateHandler />

          {/* 🎯 FEATURE 11: Modular Selection Manager (Invisible) */}
          <SelectionManager stageRef={stageRef} />

          {/* 🎯 FEATURE 16: Modular Drag Selection (Invisible) */}
          <DragSelection
            stageRef={stageRef}
            onSelectionUpdate={updateSelection}
            canvasElements={canvasState.elements}
            selectedIds={canvasState.selectedIds}
            onElementsSelect={setSelectedIds}
          />

          {/* ✂️ FEATURE 12: Modular Crop Handler (Invisible) */}
          <CropHandler stageRef={stageRef} updateElement={updateElement} canvasState={canvasState} />

          {/* 📐 FEATURE 13: Modular Snapping System (Invisible) */}
          <DragEventDispatcher stageRef={stageRef} />
          <SnapGuides stageRef={stageRef} />
          <SnapMagnet stageRef={stageRef} />
          <SnapConnections stageRef={stageRef} />
          <SnapGroupDragger stageRef={stageRef} />

          {/* 🖱️ FEATURE 14: Modular Context Menu System (Invisible) */}
          <ContextMenuManager stageRef={stageRef} />

          {/* 👥 FEATURE 15: Modular Collaboration System */}
          {/* <CollaborationManager canvasId={canvasId || 'test-canvas'} onUsersChange={setActiveUsers} /> */}

          {/* 👥 Collaboration Box - Top Left UI */}
          <div
            className='fixed top-4 left-20 z-50'
            style={{
              position: 'fixed',
              top: '20px',
              left: '80px',
              zIndex: 50,
            }}
          >
            <CollaborationBox
              activeUsers={users}
              currentUserName={currentUserName}
              currentUserId={currentUserId}
              onNameChange={updateUserName}
              onJumpToUser={handleJumpToUser}
              isGuest={isGuest}
            />
          </div>

          {/* 🧭 Off-Canvas User Indicators */}
          <OffCanvasIndicators
            users={users}
            currentUserId={currentUserId}
            stageWidth={stageRef.current?.width() || window.innerWidth}
            stageHeight={stageRef.current?.height() || window.innerHeight}
            stagePosition={position}
            stageScale={scale}
          />

          {/* 📦 Product Selector - Next to Collaboration Box */}
          <div
            className='fixed top-4 left-60 z-50'
            style={{
              position: 'fixed',
              top: '20px',
              left: '240px',
              zIndex: 50,
            }}
          >
            <ProductSelector
              onProductChange={(product) => {
                console.log('[Canvas] Product changed:', product?.name);
              }}
            />
          </div>

          {/* 🎨 Share Button - Top Right */}
          <div
            className='fixed top-4 right-4 z-50'
            style={{
              position: 'fixed',
              top: '20px',
              right: '20px',
              zIndex: 50,
            }}
          >
            <CanvasHeaderShareButton
              canvasId={canvasId || 'default-canvas'}
              canvasName={canvasData?.name || 'Canvas Project'}
            />
          </div>
        </div>
      </SceneManagerContext.Provider>
    </ContextMenuProvider>
  );
};

// {/* FEATURE 2: Image Elements (MODULAR COMPONENT) */}
// <ImageElements />

// {/* FEATURE 3: Text Elements (MODULAR COMPONENT) */}
// <TextElements />

// {/* FEATURE 4: HTML Elements (MODULAR COMPONENT) */}
// <HtmlElements />

// {/* 🎯 FEATURE 5: Image Selection & Transformation (MODULAR COMPONENT) */}
// <ImageSelection />

// {/* 📝 FEATURE 5: Text Selection & Transformation (MODULAR COMPONENT) */}
// <TextSelection />

// {/* 🎨 FEATURE 8: Modular Concept Cards */}
// <KonvaConceptCards
//   onAnswer={(id: string, questionIndex: number, answer: string) => {
//     console.log('[Canvas] Concept card answer:', { id, questionIndex, answer });
//   }}
//   onGenerate={(id: string) => {
//     console.log('[Canvas] Concept card generate:', id);
//   }}
//   onRemove={(id: string) => {
//     console.log('[Canvas] Concept card remove:', id);
//   }}
//   getNewCardPosition={() => ({ x: 100, y: 100 })}
// />
