/* 🎨 Canvas Styling - Modular Canvas Component */

/* Canvas Container with Dotted Background */
.canvas-container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
  background-color: #F8F4DF; /* Canvas cream color from design system */
  background-image: radial-gradient(circle, #E8E4CF 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

/* Konva Stage Styling */
.canvas-container .konvajs-content {
  background: transparent !important;
}

/* 🖱️ Cursor Management */
.canvas-container.hand-tool .konvajs-content {
  cursor: grab !important;
}

.canvas-container.hand-tool.panning .konvajs-content {
  cursor: grabbing !important;
}

.canvas-container.selecting .konvajs-content {
  cursor: crosshair !important;
}

.canvas-container.dragging .konvajs-content {
  cursor: move !important;
}

.canvas-container.default .konvajs-content {
  cursor: default !important;
}

/* 🎯 Selection Tool Styling */
.canvas-container.hand-cursor .konvajs-content {
  cursor: grab !important;
}

/* 📱 Mobile Responsive */
@media (max-width: 768px) {
  .canvas-container {
    background-size: 15px 15px;
  }
}

/* 🌙 Dark Mode Support */
.dark .canvas-container {
  background-color: #2A2A2A;
  background-image: radial-gradient(circle, #404040 1px, transparent 1px);
}

/* 🎨 Canvas Overlay Elements */
.canvas-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
}

.canvas-overlay > * {
  pointer-events: auto;
}

/* 🔧 Toolbar Positioning */
.canvas-toolbar {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 50;
}

/* 🎮 Controls Positioning */
.canvas-controls {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 50;
}

/* 👥 Collaboration UI */
.canvas-collaboration {
  position: fixed;
  top: 20px;
  left: 80px;
  z-index: 50;
}

/* 📦 Product Selector */
.canvas-product-selector {
  position: fixed;
  top: 20px;
  left: 240px;
  z-index: 50;
}

/* 🎨 Canvas Animation Support */
.canvas-container.animating {
  transition: all 0.3s ease;
}

/* 📏 Grid Lines (Optional) */
.canvas-container.show-grid {
  background-image: 
    radial-gradient(circle, #E8E4CF 1px, transparent 1px),
    linear-gradient(to right, rgba(232, 228, 207, 0.3) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(232, 228, 207, 0.3) 1px, transparent 1px);
  background-size: 
    20px 20px,
    20px 20px,
    20px 20px;
}

/* 🎯 Focus States */
.canvas-container:focus-within {
  outline: none;
}

/* 🚫 Disable Text Selection */
.canvas-container {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 📱 Touch Device Support */
.canvas-container {
  touch-action: none;
}

/* 🎨 Canvas Loading State */
.canvas-container.loading {
  background-color: #F0F0F0;
  background-image: none;
}

.canvas-container.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 4px solid #E8E4CF;
  border-top: 4px solid #9EA581;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 🎨 Canvas Error State */
.canvas-container.error {
  background-color: #FFF5F5;
  background-image: radial-gradient(circle, #FED7D7 1px, transparent 1px);
}

/* 🎯 High Contrast Mode */
@media (prefers-contrast: high) {
  .canvas-container {
    background-color: #FFFFFF;
    background-image: radial-gradient(circle, #000000 2px, transparent 2px);
  }
}

/* 🌈 Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .canvas-container.animating {
    transition: none;
  }
  
  .canvas-container.loading::after {
    animation: none;
  }
}
