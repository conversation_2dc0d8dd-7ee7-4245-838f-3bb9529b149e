/**
 * 🎨 Canvas Components - MODULAR EXPORTS
 * @description Main exports for all modular canvas components
 * @ai_context Modular canvas component exports
 */

// Main Canvas Component
export { Canvas, type CanvasProps } from './canvas/Canvas';

// Element Components
export { ImageElements, TextElements, HtmlElements } from './elements';
export { default as URLImage } from './elements/images/URLImage';
export { default as TextElement } from './elements/text/TextElement';
export { default as HTMLElement } from './elements/html/HTMLElement';

// UI Components
export { Toolbar } from './toolbar';
export { Controls } from './controls';
export { default as ZoomControls } from './controls/ZoomControls';

// Chat Components
export { ChatBubble, type ChatBubbleProps } from './chat';

// Hooks
export { useAuthenticatedSocket } from '../hooks';

// Card Components
export {
  ConceptCardCreator,
  KonvaConceptCards,
  KonvaLoadingConceptCard,
  KonvaGreenConceptCard,
  NewsletterApprovalCard,
  KonvaHtmlDisplayCard,
} from './cards';

// Scene Components (MIGRATED from client/canvas/konvaScenes)
export {
  SceneManager,
  BaseScene,
  CropScene,
  KonvaImage,
  ScaleTransformer,
  SceneManagerContext,
  useSceneManager,
} from './scenes';

// Newsletter Components
export { NewsletterUpdateHandler } from './newsletter';

// TODO: Add more component exports as we build them
// export { ContextMenu } from './menus';
// export { SidePanel } from './panels';
