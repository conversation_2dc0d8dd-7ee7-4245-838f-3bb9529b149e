/**
 * 🎭 Scene Context - MODULAR VERSION
 * @description React context for scene manager access
 * @responsibility Scene manager context provider and hook
 * @ai_context Context for accessing scene manager throughout component tree
 */

import React, { createContext, useContext } from 'react';
import SceneManager from './SceneManager';

export const SceneManagerContext = createContext<SceneManager | null>(null);

export const useSceneManager = () => useContext(SceneManagerContext);
