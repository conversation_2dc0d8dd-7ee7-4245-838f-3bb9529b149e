/**
 * 🎭 Scale Transformer - MODULAR VERSION
 * @description Enhanced Konva Transformer with scaling capabilities
 * @responsibility Element transformation, scaling, rotation controls
 * @ai_context Transformer component for scene-based element manipulation
 */

import Konva from 'konva';

/**
 * Custom styled Konva.Transformer used for scaling images while keeping
 * anchors easy to grab.
 */
export default class ScaleTransformer extends Konva.Transformer {
  constructor(config: Konva.TransformerConfig = {}) {
    super({
      ...config,
      flipEnabled: false,
      borderStrokeWidth: 2,
      borderStroke: '#9EA581',
      anchorStroke: '#9EA581',
      anchorFill: '#9EA581',
      anchorStrokeWidth: 1,
      anchorSize: 14,
      anchorCornerRadius: 10,
      anchorStyleFunc: (anchor) => {
        // Explicitly set olive green colors for all anchors
        anchor.stroke('#9EA581');
        anchor.fill('#9EA581');
        anchor.strokeWidth(1);

        if (anchor.hasName('top-center') || anchor.hasName('bottom-center')) {
          anchor.height(6);
          anchor.offsetY(3);
          anchor.width(26);
          anchor.offsetX(13);
        } else if (anchor.hasName('middle-left') || anchor.hasName('middle-right')) {
          anchor.height(26);
          anchor.offsetY(13);
          anchor.width(6);
          anchor.offsetX(3);
        } else if (anchor.hasName('rotater')) {
          anchor.cornerRadius(15);
          anchor.width(26);
          anchor.height(26);
          anchor.offsetX(13);
          anchor.offsetY(13);
        } else {
          anchor.width(14);
          anchor.offsetX(8);
          anchor.height(14);
          anchor.offsetY(8);
        }
      },
    });
  }
}
