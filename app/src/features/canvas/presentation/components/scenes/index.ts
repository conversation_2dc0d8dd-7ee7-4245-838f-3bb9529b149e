/**
 * 🎭 Canvas Scenes - MODULAR EXPORTS
 * @description Scene management system for advanced canvas operations
 * @responsibility Scene switching, cropping, transformations, snapping
 * @ai_context Modular scene system for complex canvas interactions
 */

// Core Scene Management
export { default as SceneManager } from './SceneManager';
export { default as BaseScene } from './BaseScene';
export { default as CropScene } from './CropScene';

// Scene Components
export { default as KonvaImage } from './KonvaImage';
export { default as ScaleTransformer } from './ScaleTransformer';

// Scene Context
export { SceneManagerContext, useSceneManager } from './SceneContext';

// Types (will be added as we modularize)
// export type { SceneData, SnapConnection, Edge } from './types';
