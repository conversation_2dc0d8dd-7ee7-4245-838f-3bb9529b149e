/**
 * 🎭 Base Scene - MODULAR VERSION
 * @description Main canvas scene with selection, snapping, and transformation
 * @responsibility Element selection, drag snapping, transformation handling
 * @ai_context Core scene for canvas interactions, cropping triggers, snap guides
 */

import Konva from 'konva';
import ScaleTransformer from './ScaleTransformer';
import SceneManager from './SceneManager';
import KonvaImage from './KonvaImage';
// 🧠 MIGRATED: Import Edge type from modular types
import { Edge } from '../../../domain/types';

const SNAP_THRESHOLD = 8;
const SNAP_LINE_COLOR = 'rgba(255, 0, 102, 0.8)';
const SNAP_LINE_WIDTH = 1;

// Sticky snapping constants
const STICKY_THRESHOLD_OUTER = 5; // px – start showing magnetic guides
const STICKY_THRESHOLD_INNER = 1; // px – actually lock / create connection (auto-sticks)

// A locked snap between two nodes (nodeId is the draggable that got locked)
interface SnapConnection {
  nodeId: string;
  targetNodeId: string;
  nodeEdge: Edge;
  targetEdge: Edge;
}

export default class BaseScene extends Konva.Group {
  public static sceneId = 'BaseScene';

  private _stage: Konva.Stage;
  private _sceneManager: SceneManager;
  private _scaleTransformer: ScaleTransformer;
  private _highlight: Konva.Rect;
  private _selectedNode: Konva.Node | null = null;
  private _boundDragHandler: (() => void) | null = null;

  private _snapLineLayer: Konva.Layer | null = null;
  private _snapDragStartHandler: (() => void) | null = null;
  private _snapDragMoveHandler: ((evt: Konva.KonvaEventObject<DragEvent>) => void) | null = null;
  private _snapDragEndHandler: (() => void) | null = null;

  // Track currently locked snap connections
  private _activeSnapConnections: SnapConnection[] = [];

  // --- Group Dragging State ---
  private _currentSnapDragGroup: {
    node: KonvaImage | Konva.Group;
    initialAbsPos: { x: number; y: number }; // Still useful for some contexts or if primary changes
    initialOffsetFromPrimary?: { dx: number; dy: number }; // Offset from the initially clicked primary node
  }[] = [];
  private _primaryDraggedNode: (KonvaImage | Konva.Group) | null = null; // Keep track of the node that initiated the current drag

  constructor(stage: Konva.Stage, sceneManager: SceneManager) {
    super({
      id: 'BaseScene',
    });
    // console.log('[BaseScene constructor] BaseScene instance created and ID set to BaseScene.');
    this._stage = stage;
    this._sceneManager = sceneManager;
    this._scaleTransformer = new ScaleTransformer();
    this.add(this._scaleTransformer);

    this._highlight = new Konva.Rect({
      stroke: '#9EA581',
      dash: [4, 4],
      visible: false,
      listening: false,
    });
    this.add(this._highlight);

    this._handlePointerDown = this._handlePointerDown.bind(this);
    this._handleDoubleClick = this._handleDoubleClick.bind(this);
    this._handleTransformStart = this._handleTransformStart.bind(this);

    // Bind unsnap custom event (can be triggered from React context-menu later)
    this._handleUnsnapRequest = this._handleUnsnapRequest.bind(this);
    window.addEventListener('canvas:unsnapImageEdge', this._handleUnsnapRequest as EventListener);
    // Listen for when an image has been cropped to readjust snaps
    this._handleImageCroppedAftermath = this._handleImageCroppedAftermath.bind(this);
    window.addEventListener('canvas:imageCropped', this._handleImageCroppedAftermath as EventListener);

    // Listen for URLImage selection events
    this._handleURLImageSelection = this._handleURLImageSelection.bind(this);
    window.addEventListener('canvas:urlImageSelected', this._handleURLImageSelection as EventListener);

    this._handleContextMenu = this._handleContextMenu.bind(this);
  }

  /** Tear-down */
  public destroy(): this {
    window.removeEventListener('canvas:unsnapImageEdge', this._handleUnsnapRequest as EventListener);
    window.removeEventListener('canvas:imageCropped', this._handleImageCroppedAftermath as EventListener);
    return super.destroy();
  }

  // ---------------------------------------------------------------------------
  // Snap-connection helpers
  // ---------------------------------------------------------------------------

  private _getConnectionsForNode(nodeId: string) {
    return this._activeSnapConnections.filter((c) => c.nodeId === nodeId);
  }

  private _removeConnections(nodeId: string, edge?: Edge) {
    if (edge) {
      // Remove connections where the given node participates on the specified edge, regardless of source/target side
      this._activeSnapConnections = this._activeSnapConnections.filter(
        (c) => !((c.nodeId === nodeId && c.nodeEdge === edge) || (c.targetNodeId === nodeId && c.targetEdge === edge))
      );
    } else {
      // No edge specified: remove all connections where nodeId is either source or target
      this._activeSnapConnections = this._activeSnapConnections.filter(
        (c) => c.nodeId !== nodeId && c.targetNodeId !== nodeId
      );
    }
  }

  private _addConnection(conn: SnapConnection) {
    // Remove any existing connection on the same node+edge then push
    this._removeConnections(conn.nodeId, conn.nodeEdge);
    this._activeSnapConnections.push(conn);
  }

  private _handleUnsnapRequest(e: Event) {
    const detail = (e as CustomEvent<{ nodeId: string; edge?: Edge }>).detail || ({} as any);
    const { nodeId, edge } = detail;
    if (!nodeId) return;
    this._removeConnections(nodeId, edge as Edge | undefined);

    if (edge) {
      const node = this.findOne(`#${nodeId}`) as KonvaImage | null;
      if (node) {
        const nudge = 5; // Increased nudge amount
        switch (edge) {
          case 'left':
            node.x(node.x() + nudge); // Corrected direction: move right
            break;
          case 'right':
            node.x(node.x() - nudge); // Corrected direction: move left
            break;
          case 'top':
            node.y(node.y() + nudge); // Corrected direction: move down
            break;
          case 'bottom':
            node.y(node.y() - nudge); // Corrected direction: move up
            break;
          // Optional: Add cases for hCenter/vCenter if nudging is desired for them
          // case 'hCenter': // e.g., node.x(node.x() + (node.width()/2 < stage.width()/2 ? nudge : -nudge));
          //   break;
          // case 'vCenter': // e.g., node.y(node.y() + (node.height()/2 < stage.height()/2 ? nudge : -nudge));
          //   break;
        }
        // console.log(
        //   `[BaseScene _handleUnsnapRequest] Unsnapped ${nodeId} from edge ${edge}, nudged to x:${node.x()}, y:${node.y()}`
        // );
        node.getLayer()?.batchDraw();
      }
    }
  }

  private _registerEvents() {
    // 🎯 TEMPORARILY DISABLED - Let modular SelectionManager handle these events
    // this._stage.on('pointerdown', this._handlePointerDown);
    // this._stage.on('pointerdblclick', this._handleDoubleClick);
    this._scaleTransformer.on('transformstart', this._handleTransformStart);
    // this._stage.on('contextmenu', this._handleContextMenu);
    console.log('[BaseScene] Event handlers DISABLED - using modular SelectionManager');
  }

  private _unbindEvents() {
    // 🎯 TEMPORARILY DISABLED - Modular SelectionManager handles these events
    // this._stage.off('pointerdown', this._handlePointerDown);
    // this._stage.off('pointerdblclick', this._handleDoubleClick);
    this._scaleTransformer.off('transformstart', this._handleTransformStart);
    // this._stage.off('contextmenu', this._handleContextMenu);
  }

  private _handlePointerDown({ target }: Konva.KonvaEventObject<PointerEvent>) {
    if (target.hasName('_anchor')) return; // Ignore transformer anchors
    console.log(`[BaseScene _handlePointerDown] Click target: className=${target.className}, id=${target.id()}`);

    let nodeToSelect: Konva.Node | null = null;

    if (target.className === 'Image') {
      const parentGroup = target.getParent();
      // More detailed logging for the parent
      if (parentGroup) {
        // console.log(`[BaseScene _handlePointerDown] Image's Parent Details: className=${parentGroup.className}, id=${parentGroup.id()}, name=${parentGroup.name()}, attrs.id=${parentGroup.getAttr('id')}`);
        // Try to access custom attributes if you set them, e.g., parentGroup.attrs.myCustomId
      } else {
        // console.log(`[BaseScene _handlePointerDown] Image target has no parent group.`);
      }

      // console.log(`[BaseScene _handlePointerDown] Target is Image. Parent: className=${parentGroup?.className}, id=${parentGroup?.id()}`);
      // If image is inside a Group with an ID, it's part of a URLImage or similar component
      // Relaxing className check as it was sometimes undefined, relying on parentGroup existence and its ID.
      if (parentGroup && parentGroup.id()) {
        nodeToSelect = parentGroup;
        console.log(`[BaseScene _handlePointerDown] Image is in Group ${parentGroup.id()}, selecting Group.`);
      } else {
        // Plain KonvaImage, not part of a component group
        nodeToSelect = target;
        // console.log(`[BaseScene _handlePointerDown] Selecting plain Image ${target.id() || '(no id)'}.`);
      }
    } else if (target.className === 'Group' && target.id() && (target as Konva.Group).findOne('Image')) {
      // Clicked directly on a Group that has an ID and contains an Image (likely a URLImage component)
      nodeToSelect = target;
      console.log(`[BaseScene _handlePointerDown] Clicked Group ${target.id()} with Image, selecting Group.`);
    } else if (target.className === 'Group' && !target.id()) {
      // Clicked on a generic group without an ID - could be a BaseScene internal group or user-created.
      // For now, allow selecting these generic groups if they don't have special handling elsewhere.
      // If it contains an image, maybe it's a custom user group.
      // This part might need refinement based on how generic groups are used.
      // const hasImage = (target as Konva.Group).findOne('Image');
      // if (hasImage) nodeToSelect = target;
      // console.log(`[BaseScene _handlePointerDown] Clicked generic Group (no ID), current behavior is to clear selection.`);
      // Current behavior for generic groups or stage clicks is to clear selection (handled by the else block later)
    }

    if (nodeToSelect) {
      if (this.getSelection() !== nodeToSelect) {
        this.setSelection([nodeToSelect]);
      }
      // If already selected, do nothing (prevents re-running setSelection)
    } else {
      // If no specific node identified for selection (e.g., click on stage or an unhandled group type)
      if (this.getSelection() !== null) {
        // console.log('[BaseScene _handlePointerDown] Clicked unhandled target or stage, clearing selection.');
        this.setSelection([]);
      }
    }
  }

  private _handleDoubleClick(e: Konva.KonvaEventObject<PointerEvent>) {
    // Only trigger crop on double LEFT click
    if (e.evt.button !== 0) {
      // console.log('[BaseScene _handleDoubleClick] Ignoring non-left button double click, button:', e.evt.button);
      return;
    }

    const currentSelection = this.getSelection(); // This is now reliably the Group for URLImage or a KonvaImage

    if (!currentSelection) {
      // console.log('[BaseScene _handleDoubleClick] No selection, cannot crop.');
      return;
    }

    // Determine the node to actually pass to CropScene
    // If currentSelection is a Group (URLImage), we pass the Group.
    // If it's a KonvaImage, we pass that.
    let nodeForCropScene: Konva.Image | Konva.Group | null = null;

    if (currentSelection instanceof Konva.Group && currentSelection.findOne('Image')) {
      // It's a URLImage group, pass the group itself
      nodeForCropScene = currentSelection;
      // console.log(`[BaseScene _handleDoubleClick] Identified Group ${currentSelection.id()} for cropping.`);
    } else if (currentSelection instanceof KonvaImage) {
      // It's a plain KonvaImage
      nodeForCropScene = currentSelection;
      // console.log(`[BaseScene _handleDoubleClick] Identified KonvaImage ${currentSelection.id()} for cropping.`);
    } else {
      // console.log('[BaseScene _handleDoubleClick] Selected node is not a valid type for cropping:', currentSelection.className);
      return;
    }

    // Ensure the double click actually happened on the selected element or its children
    let liturgicalTarget: Konva.Node | null = e.target as Konva.Node;
    let hitSelectionContainer = false;
    while (liturgicalTarget) {
      if (liturgicalTarget === nodeForCropScene) {
        hitSelectionContainer = true;
        break;
      }
      liturgicalTarget = liturgicalTarget.getParent();
    }
    if (!hitSelectionContainer) {
      // console.log('[BaseScene _handleDoubleClick] Double click target was not the selected item or its child. Ignoring.');
      return;
    }

    if (nodeForCropScene && nodeForCropScene.id()) {
      // Check ID here before passing
      // console.log(`[BaseScene _handleDoubleClick] Passing node with ID ${nodeForCropScene.id()} and className ${nodeForCropScene.className} to CropScene.`);
      import('./CropScene').then((mod) => {
        const CropScene = mod.default;
        this._sceneManager.goto(CropScene, nodeForCropScene); // Pass the Group or KonvaImage
      });
    } else {
      // console.error('[BaseScene _handleDoubleClick] Node identified for crop scene is invalid or has no ID.', nodeForCropScene);
    }
  }

  private _handleTransformStart() {
    const selection = this.getSelection();
    if (!selection) return;
    const activeAnchor = this._scaleTransformer.getActiveAnchor();
    // (selection as any).handleTransformStart(activeAnchor); // Original line causing error - REMOVE/COMMENT
    console.log(`[BaseScene] TransformStart on node: ${selection.id()}, anchor: ${activeAnchor}`);

    const transformHandler = () => {
      // (selection as any).handleTransform(activeAnchor); // Original line - REMOVE/COMMENT
      // The transformer itself is updating the node's scale/position during transform
      // We might want to dispatch canvas:elementResizing here if continuous updates are needed
      if (selection && selection.id()) {
        // Optional: Dispatch a continuous resizing event if needed by other parts of UI
        // window.dispatchEvent(
        //   new CustomEvent('canvas:elementResizing', {
        //     detail: {
        //       id: selection.id(),
        //       x: selection.x(),
        //       y: selection.y(),
        //       width: selection.width() * selection.scaleX(), // current visual width
        //       height: selection.height() * selection.scaleY(), // current visual height
        //       rotation: selection.rotation(),
        //       isResizing: true,
        //     },
        //   })
        // );
      }
    };

    const transformEndHandler = () => {
      this._scaleTransformer.off('transform', transformHandler);
      this._scaleTransformer.off('transformend', transformEndHandler);
      // (selection as any).handleTransformEnd(activeAnchor); // Original line - REMOVE/COMMENT
      this._updateHighlight();

      const node = this.getSelection(); // Get the selection again, it should be the same node
      if (node && node.id()) {
        // After transform, Konva Transformer updates the node's scale and position.
        // The visual size is node.width() * node.scaleX().
        // We want to apply this scale to the width/height attributes and reset scale to 1.
        const newWidth = node.width() * node.scaleX();
        const newHeight = node.height() * node.scaleY();

        // Important: Update attributes and then reset scale
        node.width(newWidth);
        node.height(newHeight);
        node.scaleX(1);
        node.scaleY(1);

        console.log(
          `[BaseScene] TransformEnd on node: ${node.id()}, final w: ${node.width()}, h: ${node.height()}, x: ${node.x()}, y: ${node.y()}`
        );

        window.dispatchEvent(
          new CustomEvent('canvas:elementResized', {
            detail: {
              id: node.id(),
              x: node.x(),
              y: node.y(),
              width: node.width(), // Already updated to final visual size
              height: node.height(), // Already updated to final visual size
              rotation: node.rotation(),
              isResizing: false, // Transform has ended
            },
          })
        );
      }
    };
    this._scaleTransformer.on('transform', transformHandler);
    this._scaleTransformer.on('transformend', transformEndHandler);
  }

  private _handleURLImageSelection(event: CustomEvent) {
    const { imageId, isSelected } = event.detail;
    // console.log(`[BaseScene _handleURLImageSelection] URLImage ${imageId} selection changed: ${isSelected}`);

    if (isSelected) {
      // Find the Group node by ID
      const stage = this.getStage();
      if (stage) {
        // console.log(`[BaseScene _handleURLImageSelection] Searching stage for Group with ID: ${imageId}`);
        const groupNode = stage.findOne(`#${imageId}`) as Konva.Group | null;
        // console.log(`[BaseScene _handleURLImageSelection] Found group node:`, groupNode);
        if (groupNode && groupNode instanceof Konva.Group) {
          // Check if it contains an Image
          const hasImage = groupNode.findOne('Image');
          // console.log(`[BaseScene _handleURLImageSelection] Group contains Image:`, !!hasImage);
          if (hasImage) {
            // console.log(`[BaseScene _handleURLImageSelection] Setting selection to Group ${imageId}`);
            this.setSelection([groupNode]);
          }
        } else {
          // console.log(`[BaseScene _handleURLImageSelection] Could not find Group with ID ${imageId}`);
        }
      }
    } else {
      // We intentionally no-longer clear the selection immediately when we
      // receive an "isSelected=false" event from a URLImage.  These events
      // can arrive while the user is still dragging, which removed the
      // dragBoundFunc and breaks sticky-snapping (guides appear but the
      // element never locks).  Pointer interactions outside the image (or
      // the built-in BaseScene pointer handlers) will take care of clearing
      // selection when the user genuinely clicks away.
      // console.log(
      //   `[BaseScene _handleURLImageSelection] Ignoring isSelected=false for ${imageId} in order to preserve snapping behaviour.`
      // );
    }
  }

  public show(): this {
    super.show();
    this._registerEvents();
    this._updateHighlight();
    if (!this._snapLineLayer) {
      this._snapLineLayer = new Konva.Layer({ listening: false, name: 'SnapLineLayer' });
      this.getStage()?.add(this._snapLineLayer);
    }
    this._snapLineLayer.moveToTop();
    return this;
  }

  public hide(): this {
    super.hide();
    this._unbindEvents();
    this._clearSnapLines();
    this._snapLineLayer?.destroy();
    this._snapLineLayer = null;
    return this;
  }

  public getSelection(): Konva.Node | null {
    return this._selectedNode;
  }

  public setSelection(targetNodes: Konva.Node[] | null) {
    // console.log(`[BaseScene setSelection] Called with:`, targetNodes);

    // Clean up previous selection - handle both KonvaImage and Group
    if (this._selectedNode instanceof KonvaImage || this._selectedNode instanceof Konva.Group) {
      // console.log(`[BaseScene setSelection] Cleaning up previous selection:`, this._selectedNode.id());
      this._selectedNode.dragBoundFunc(null);
      if (this._snapDragStartHandler) this._selectedNode.off('dragstart', this._snapDragStartHandler);
      if (this._snapDragMoveHandler) this._selectedNode.off('dragmove', this._snapDragMoveHandler);
      if (this._snapDragEndHandler) this._selectedNode.off('dragend', this._snapDragEndHandler);
      if (this._boundDragHandler) {
        this._selectedNode.off('dragmove', this._boundDragHandler);
        this._selectedNode.off('dragend', this._boundDragHandler);
      }
    }

    this._scaleTransformer.nodes(targetNodes ?? []);
    const currentSelection = targetNodes && targetNodes.length > 0 ? targetNodes[0] : null;
    this._selectedNode = currentSelection;
    // console.log(`[BaseScene setSelection] New selection:`, currentSelection?.id(), currentSelection?.className);

    // 🧠 MIGRATED: Sync selection back to modular canvas store to keep UI in sync
    if (currentSelection && currentSelection.id()) {
      // Import setSelectedIds dynamically to avoid circular imports
      import('../../../domain/state').then(({ useCanvasToolbarStore }) => {
        // console.log(`[BaseScene setSelection] Syncing selection to modular canvas store: ${currentSelection.id()}`);
        useCanvasToolbarStore.getState().setSelectedIds([currentSelection.id()]);
      });
    } else {
      // Clear selection in modular canvas store
      import('../../../domain/state').then(({ useCanvasToolbarStore }) => {
        // console.log(`[BaseScene setSelection] Clearing selection in modular canvas store`);
        useCanvasToolbarStore.getState().setSelectedIds([]);
      });
    }

    // Set up drag handlers for both KonvaImage and Group (URLImage components)
    if (
      currentSelection instanceof KonvaImage ||
      (currentSelection instanceof Konva.Group && currentSelection.findOne('Image'))
    ) {
      // console.log(`[BaseScene setSelection] Setting up drag handlers for:`, currentSelection.id());
      this._snapDragStartHandler = () => {
        // console.log(`[BaseScene _snapDragStartHandler] Drag started for:`, currentSelection.id());
        this._clearSnapLines();
        if (currentSelection instanceof KonvaImage) {
          this._primaryDraggedNode = currentSelection; // Set the primary dragged node
          this._identifyAndStoreSnapGroup(currentSelection);
        } else if (currentSelection instanceof Konva.Group) {
          // For Groups (URLImage), we treat the Group as the draggable unit
          // console.log(`[BaseScene _snapDragStartHandler] Setting up Group drag for:`, currentSelection.id());
          this._primaryDraggedNode = currentSelection as any; // Cast to work with existing system
          this._identifyAndStoreSnapGroup(currentSelection as any);
        }
      };
      this._snapDragMoveHandler = (evt: Konva.KonvaEventObject<DragEvent>) => {
        const primaryNodeFromEvent = evt.target as KonvaImage | Konva.Group;
        // console.log(`[BaseScene _snapDragMoveHandler] Drag move for:`, primaryNodeFromEvent.id());
        this._handleDragMoveWithSnapping(primaryNodeFromEvent); // Draws visual guides

        // Get the node being dragged
        const node = evt.target as KonvaImage | Konva.Group;

        // Similar to URLImage's onDragMove, store position information
        // This mimics the functionality in URLImage.tsx
        node.setAttr('startX', node.x());
        node.setAttr('startY', node.y());

        // Also store stage info like in URLImage
        const stage = node.getStage();
        if (stage) {
          node.setAttr('stageStartScale', stage.scaleX());
          node.setAttr('stageStartX', stage.x());
          node.setAttr('stageStartY', stage.y());
        }

        // Dispatch a custom event for dragging
        // This event will be caught by the image-elements.tsx component
        window.dispatchEvent(
          new CustomEvent('canvas:elementDragging', {
            detail: {
              id: node.id(),
              x: node.x(),
              y: node.y(),
              isDragging: true,
            },
          })
        );

        if (this._currentSnapDragGroup.length > 1 && this._primaryDraggedNode) {
          const primaryNodeCurrentPos = this._primaryDraggedNode.absolutePosition(); // Use the stored primary node

          this._currentSnapDragGroup.forEach((member) => {
            if (member.node !== this._primaryDraggedNode) {
              if (member.initialOffsetFromPrimary) {
                const newSlaveX = primaryNodeCurrentPos.x + member.initialOffsetFromPrimary.dx;
                const newSlaveY = primaryNodeCurrentPos.y + member.initialOffsetFromPrimary.dy;
                member.node.absolutePosition({ x: newSlaveX, y: newSlaveY });

                // Also dispatch dragging events for group members
                window.dispatchEvent(
                  new CustomEvent('canvas:elementDragging', {
                    detail: {
                      id: member.node.id(),
                      x: member.node.x(),
                      y: member.node.y(),
                      isDragging: true,
                    },
                  })
                );
              }
            }
          });
          this._primaryDraggedNode.getLayer()?.batchDraw();
        }
      };
      this._snapDragEndHandler = () => {
        this._clearSnapLines();

        const primaryNode = this._primaryDraggedNode;

        if (!primaryNode) {
          // console.log('[BaseScene._snapDragEndHandler] No primary node, clearing group and returning.');
          this._currentSnapDragGroup = [];
          this._primaryDraggedNode = null;
          return;
        }

        // Dispatch a custom event to signal the end of dragging for the primary node
        window.dispatchEvent(
          new CustomEvent('canvas:elementDraggingEnd', {
            detail: {
              id: primaryNode.id(),
              x: primaryNode.x(),
              y: primaryNode.y(),
              isDragging: false,
            },
          })
        );

        // Also dispatch dragging end events for all group members
        if (this._currentSnapDragGroup.length > 1) {
          this._currentSnapDragGroup.forEach((member) => {
            if (member.node !== primaryNode) {
              window.dispatchEvent(
                new CustomEvent('canvas:elementDraggingEnd', {
                  detail: {
                    id: member.node.id(),
                    x: member.node.x(),
                    y: member.node.y(),
                    isDragging: false,
                  },
                })
              );
            }
          });
        }

        const primaryFinalPos = primaryNode.absolutePosition();
        // console.log(
        //   `[BaseScene._snapDragEndHandler] START. Primary ${primaryNode.id()}: absPos=`,
        //   primaryFinalPos,
        //   `x/y=(${primaryNode.x()}, ${primaryNode.y()})`
        // );

        if (this._currentSnapDragGroup.length > 0) {
          // console.log('[BaseScene._snapDragEndHandler] Group members and their initial offsets:');
          // this._currentSnapDragGroup.forEach((m) =>
          // console.log(
          //   `  - ${m.node.id()}: initialOffset=`,
          //   m.initialOffsetFromPrimary,
          //   `current absPos=`,
          //   m.node.absolutePosition()
          // )
          // );

          // 1. Remove all existing snap connections for ALL members of the drag group.
          // console.log('[BaseScene._snapDragEndHandler] Removing old connections for all group members...');
          this._currentSnapDragGroup.forEach((member) => {
            this._removeConnections(member.node.id());
          });
          // console.log('[BaseScene._snapDragEndHandler] All old connections for group members removed.');

          // 2. Force all slave nodes to their correct final positions relative to the primary.
          // console.log('[BaseScene._snapDragEndHandler] Re-applying slave positions relative to primary...');
          this._currentSnapDragGroup.forEach((member) => {
            if (member.node !== primaryNode && member.initialOffsetFromPrimary && primaryFinalPos) {
              const expectedSlaveX = primaryFinalPos.x + member.initialOffsetFromPrimary.dx;
              const expectedSlaveY = primaryFinalPos.y + member.initialOffsetFromPrimary.dy;
              const currentSlavePos = member.node.absolutePosition();

              if (
                Math.abs(currentSlavePos.x - expectedSlaveX) > 0.1 ||
                Math.abs(currentSlavePos.y - expectedSlaveY) > 0.1
              ) {
                // console.warn(
                //   `[BaseScene._snapDragEndHandler] SLAVE ${member.node.id()} DRIFTED. Expected {x:${expectedSlaveX.toFixed(2)},y:${expectedSlaveY.toFixed(2)}}, Got {x:${currentSlavePos.x.toFixed(2)},y:${currentSlavePos.y.toFixed(2)}}. Re-setting.`
                // );
                member.node.absolutePosition({ x: expectedSlaveX, y: expectedSlaveY });
              } else {
                // console.log(
                //   `[BaseScene._snapDragEndHandler] SLAVE ${member.node.id()} position confirmed at {x:${expectedSlaveX.toFixed(2)},y:${expectedSlaveY.toFixed(2)}}.`
                // );
              }
            }
          });
          // Batch draw layer of primary node (assuming all group members are on the same layer)
          primaryNode.getLayer()?.batchDraw();
          // console.log('[BaseScene._snapDragEndHandler] All slave positions re-applied and layer batch drawn.');

          // 3. Call _finalizeSnapConnections ONLY for the primary node.
          // console.log(`[BaseScene._snapDragEndHandler] Finalizing connections for PRIMARY node: ${primaryNode.id()}`);
          this._finalizeSnapConnections(primaryNode);
          // console.log(`[BaseScene._snapDragEndHandler] Connections finalized for primary node.`);

          // 4. Dispatch events for all affected nodes.
          // console.log('[BaseScene._snapDragEndHandler] Positions BEFORE dispatch (abs and local):');
          this._currentSnapDragGroup
            .sort((a, b) => (a.node === primaryNode ? -1 : b.node === primaryNode ? 1 : 0))
            .forEach((member) => {
              let finalLocalX: number;
              let finalLocalY: number;
              let finalAbsX: number; // For logging
              let finalAbsY: number; // For logging

              if (member.node === primaryNode) {
                const absPos = primaryNode.absolutePosition();
                finalAbsX = absPos.x;
                finalAbsY = absPos.y;
                const parent = primaryNode.getParent();
                finalLocalX = finalAbsX;
                finalLocalY = finalAbsY;
                if (parent && parent !== primaryNode.getStage()) {
                  const parentAbs = parent.absolutePosition();
                  const parentScaleX = parent.getAbsoluteScale().x;
                  const parentScaleY = parent.getAbsoluteScale().y;
                  finalLocalX = (finalAbsX - parentAbs.x) / parentScaleX;
                  finalLocalY = (finalAbsY - parentAbs.y) / parentScaleY;
                }
              } else {
                // This is a SLAVE node
                if (member.initialOffsetFromPrimary && primaryFinalPos) {
                  finalAbsX = primaryFinalPos.x + member.initialOffsetFromPrimary.dx;
                  finalAbsY = primaryFinalPos.y + member.initialOffsetFromPrimary.dy;

                  const parent = member.node.getParent();
                  finalLocalX = finalAbsX;
                  finalLocalY = finalAbsY;
                  if (parent && parent !== member.node.getStage()) {
                    const parentAbs = parent.absolutePosition();
                    const parentScaleX = parent.getAbsoluteScale().x;
                    const parentScaleY = parent.getAbsoluteScale().y;
                    finalLocalX = (finalAbsX - parentAbs.x) / parentScaleX;
                    finalLocalY = (finalAbsY - parentAbs.y) / parentScaleY;
                  }
                  // console.log(
                  //   `[BaseScene._snapDragEndHandler] Slave ${member.node.id()} expected final abs {x:${finalAbsX.toFixed(2)}, y:${finalAbsY.toFixed(2)}}, calc local {x:${finalLocalX.toFixed(2)}, y:${finalLocalY.toFixed(2)}}`
                  // );
                } else {
                  // console.warn(
                  //   `[BaseScene._snapDragEndHandler] Slave ${member.node.id()} missing offset or primaryFinalPos! Using current x/y for dispatch.`
                  // );
                  finalLocalX = member.node.x();
                  finalLocalY = member.node.y();
                  const absPos = member.node.absolutePosition(); // Get current if fallback
                  finalAbsX = absPos.x;
                  finalAbsY = absPos.y;
                }
              }

              // Update node's x/y if they are not in sync with the calculated authoritative local position
              if (Math.abs(member.node.x() - finalLocalX) > 0.01) {
                // console.log(
                //   `[BaseScene._snapDragEndHandler] Correcting ${member.node.id()} local X from ${member.node.x().toFixed(2)} to ${finalLocalX.toFixed(2)}`
                // );
                member.node.x(finalLocalX);
              }
              if (Math.abs(member.node.y() - finalLocalY) > 0.01) {
                // console.log(
                //   `[BaseScene._snapDragEndHandler] Correcting ${member.node.id()} local Y from ${member.node.y().toFixed(2)} to ${finalLocalY.toFixed(2)}`
                // );
                member.node.y(finalLocalY);
              }

              // console.log(
              //   `[BaseScene._snapDragEndHandler] Dispatching elementAttrsChanged for ${member.node.id()}: x=${member.node.x().toFixed(2)}, y=${member.node.y().toFixed(2)} (Abs: x=${finalAbsX.toFixed(2)}, y=${finalAbsY.toFixed(2)})`
              // );
              window.dispatchEvent(
                new CustomEvent('canvas:elementAttrsChanged', {
                  detail: {
                    id: member.node.id(),
                    x: member.node.x(),
                    y: member.node.y(),
                    width: member.node.width(),
                    height: member.node.height(),
                    // BaseScene Log: From _snapDragEndHandler - group member update.
                    // Source x/y based on member.node.x/y() after offset calcs.
                    // Calculated finalLocalX: ${finalLocalX}, finalLocalY: ${finalLocalY}
                    // Calculated finalAbsX: ${finalAbsX}, finalAbsY: ${finalAbsY}
                  },
                })
              );
            });
        } else {
          // Case for a single node drag (not part of a pre-existing group from dragstart)
          // console.log(
          //   `[BaseScene._snapDragEndHandler] Single node drag for ${primaryNode.id()}, removing its connections and finalizing.`
          // );
          this._removeConnections(primaryNode.id());
          this._finalizeSnapConnections(primaryNode);
          // console.log(
          //   `[BaseScene._snapDragEndHandler] Dispatching elementAttrsChanged for single node ${primaryNode.id()}: x=${primaryNode.x().toFixed(2)}, y=${primaryNode.y().toFixed(2)}`
          // );
          window.dispatchEvent(
            new CustomEvent('canvas:elementAttrsChanged', {
              detail: {
                id: primaryNode.id(),
                x: primaryNode.x(),
                y: primaryNode.y(),
                width: primaryNode.width(),
                height: primaryNode.height(),
                // BaseScene Log: From _snapDragEndHandler - single node update.
                // Source x/y based on primaryNode.x/y() after _finalizeSnapConnections.
              },
            })
          );
        }

        // console.log('[BaseScene._snapDragEndHandler] Positions right BEFORE clearing _currentSnapDragGroup:');
        // this._currentSnapDragGroup.forEach((member) => {
        //   const absPos = member.node.absolutePosition();
        // console.log(
        //   `  - ${member.node.id()}: absPos={x:${absPos.x.toFixed(2)},y:${absPos.y.toFixed(2)}}, localPos={x:${member.node.x().toFixed(2)},y:${member.node.y().toFixed(2)}}`
        // );
        // });

        this._currentSnapDragGroup = [];
        this._primaryDraggedNode = null;
        // console.log('[BaseScene._snapDragEndHandler] END. Group cleared.');
      };

      // Set up drag bound function for sticky snapping
      currentSelection.dragBoundFunc(this._handleDragBound.bind(this));

      currentSelection.on('dragstart', this._snapDragStartHandler);
      currentSelection.on('dragmove', this._snapDragMoveHandler);
      currentSelection.on('dragend', this._snapDragEndHandler);

      this._boundDragHandler = () => this._updateHighlight();
      currentSelection.on('dragmove', this._boundDragHandler);
      currentSelection.on('dragend', this._boundDragHandler);
    } else {
      this._snapDragStartHandler = null;
      this._snapDragMoveHandler = null;
      this._snapDragEndHandler = null;
      this._boundDragHandler = null;
    }
    this._updateHighlight();
  }

  private _updateHighlight() {
    const sel = this.getSelection() as Konva.Node | null;
    if (!sel || !(sel instanceof KonvaImage)) {
      this._highlight.visible(false);
    } else {
      const konvaImageSel = sel as KonvaImage;
      this._highlight.setAttrs({
        x: konvaImageSel.x(),
        y: konvaImageSel.y(),
        width: konvaImageSel.width(),
        height: konvaImageSel.height(),
        visible: true,
        rotation: konvaImageSel.rotation(),
        offsetX: konvaImageSel.offsetX(),
        offsetY: konvaImageSel.offsetY(),
        scaleX: konvaImageSel.scaleX(),
        scaleY: konvaImageSel.scaleY(),
      });
      this._highlight.moveToTop();
      this._scaleTransformer.moveToTop();
    }
    this.getLayer()?.batchDraw();
  }

  private _clearSnapLines() {
    if (this._snapLineLayer) {
      this._snapLineLayer.destroyChildren();
      this._snapLineLayer.batchDraw();
    }
  }

  private _drawSnapLine(points: number[]) {
    if (!this._snapLineLayer) return;
    const line = new Konva.Line({
      points: points,
      stroke: SNAP_LINE_COLOR,
      strokeWidth: SNAP_LINE_WIDTH / (this.getStage()?.scaleX() || 1),
      listening: false,
      dash: [4, 2],
    });
    this._snapLineLayer.add(line);
    this._snapLineLayer.batchDraw();
  }

  private _getVisualBounds(node: Konva.Node) {
    // Use getClientRect to honour rotation / transforms
    const rect = node.getClientRect({ skipShadow: true, skipStroke: true });
    return {
      left: rect.x,
      right: rect.x + rect.width,
      top: rect.y,
      bottom: rect.y + rect.height,
      hCenter: rect.x + rect.width / 2,
      vCenter: rect.y + rect.height / 2,
      width: rect.width,
      height: rect.height,
    };
  }

  private _handleDragMoveWithSnapping(draggedNode: KonvaImage | Konva.Group) {
    // console.log(`[BaseScene _handleDragMoveWithSnapping] Called for node:`, draggedNode.id());
    this._clearSnapLines(); // Clear previous lines first

    const stage = this.getStage();
    if (!stage) {
      // console.log(`[BaseScene _handleDragMoveWithSnapping] No stage found`);
      return;
    }

    // Look for both KonvaImage instances and Groups that contain Images (URLImage components)
    // Search the entire stage, not just BaseScene children, since URLImage Groups are in the main layer
    const allNodes = stage.find('Image, Group') as (Konva.Image | Konva.Group)[];
    // console.log(`[BaseScene _handleDragMoveWithSnapping] Found ${allNodes.length} total nodes on stage`);

    const otherImages = allNodes.filter((node) => {
      if (node === draggedNode || !node.isVisible()) return false;

      if (node instanceof KonvaImage) {
        return true;
      }
      // Check if it's a Group containing an Image (URLImage component)
      if (node instanceof Konva.Group) {
        const hasImage = node.findOne('Image');
        return !!hasImage;
      }
      return false;
    }) as (KonvaImage | Konva.Group)[];

    // console.log(`[BaseScene _handleDragMoveWithSnapping] Found ${otherImages.length} other images/groups to snap to:`, otherImages.map(n => n.id()));

    const stageBounds = (s: Konva.Stage) => ({
      left: 0,
      right: s.width() / (s.scaleX() || 1),
      top: 0,
      bottom: s.height() / (s.scaleY() || 1),
      hCenter: s.width() / (s.scaleX() || 1) / 2,
      vCenter: s.height() / (s.scaleY() || 1) / 2,
      width: s.width() / (s.scaleX() || 1),
      height: s.height() / (s.scaleY() || 1),
    });

    const stageStatic = stageBounds(stage);

    const virtualStageNode: any = {
      id: () => '__stage__',
      getClientRect: () => stageStatic,
      width: () => stageStatic.width,
      height: () => stageStatic.height,
      absolutePosition: () => ({ x: stageStatic.left, y: stageStatic.top }),
    };
    const snapCandidates = [...otherImages, virtualStageNode];
    if (!snapCandidates.length) return;

    const draggedBounds = this._getVisualBounds(draggedNode);

    const scale = stage.scaleX() || 1;
    const outerTolerance = STICKY_THRESHOLD_OUTER / scale;

    for (const staticNode of snapCandidates) {
      const staticBounds =
        staticNode === virtualStageNode ? stageStatic : this._getVisualBounds(staticNode as KonvaImage | Konva.Group);

      // ----- Horizontal snapping (X-axis) -----
      // const xLocked = false; // not used in this function
      // const xSnapped = false; // not used in this function
      const hTargets = [
        {
          nodeEdge: 'left',
          targetEdge: 'left',
          draggedVal: draggedBounds.left,
          staticVal: staticBounds.left,
          newPos: staticBounds.left,
        },
        {
          nodeEdge: 'left',
          targetEdge: 'hCenter',
          draggedVal: draggedBounds.left,
          staticVal: staticBounds.hCenter,
          newPos: staticBounds.hCenter,
        },
        {
          nodeEdge: 'left',
          targetEdge: 'right',
          draggedVal: draggedBounds.left,
          staticVal: staticBounds.right,
          newPos: staticBounds.right,
        },

        {
          nodeEdge: 'hCenter',
          targetEdge: 'left',
          draggedVal: draggedBounds.hCenter,
          staticVal: staticBounds.left,
          newPos: staticBounds.left - draggedBounds.width / 2,
        },
        {
          nodeEdge: 'hCenter',
          targetEdge: 'hCenter',
          draggedVal: draggedBounds.hCenter,
          staticVal: staticBounds.hCenter,
          newPos: staticBounds.hCenter - draggedBounds.width / 2,
        },
        {
          nodeEdge: 'hCenter',
          targetEdge: 'right',
          draggedVal: draggedBounds.hCenter,
          staticVal: staticBounds.right,
          newPos: staticBounds.right - draggedBounds.width / 2,
        },

        {
          nodeEdge: 'right',
          targetEdge: 'left',
          draggedVal: draggedBounds.right,
          staticVal: staticBounds.left,
          newPos: staticBounds.left - draggedBounds.width,
        },
        {
          nodeEdge: 'right',
          targetEdge: 'hCenter',
          draggedVal: draggedBounds.right,
          staticVal: staticBounds.hCenter,
          newPos: staticBounds.hCenter - draggedBounds.width,
        },
        {
          nodeEdge: 'right',
          targetEdge: 'right',
          draggedVal: draggedBounds.right,
          staticVal: staticBounds.right,
          newPos: staticBounds.right - draggedBounds.width,
        },
      ];

      for (const snap of hTargets) {
        const diff = Math.abs(snap.draggedVal - snap.staticVal);
        if (diff < outerTolerance) {
          const lineY1 = Math.min(draggedBounds.top, staticBounds.top) - SNAP_THRESHOLD * 2;
          const lineY2 = Math.max(draggedBounds.bottom, staticBounds.bottom) + SNAP_THRESHOLD * 2;
          this._drawSnapLine([snap.staticVal, lineY1, snap.staticVal, lineY2]);
          break; // Only draw one guide line per axis per target
        }
      }

      // ----- Vertical snapping (Y-axis) -----
      // const yLocked = false; // not used in this function
      // const ySnapped = false; // not used in this function
      const vTargets = [
        {
          nodeEdge: 'top',
          targetEdge: 'top',
          draggedVal: draggedBounds.top,
          staticVal: staticBounds.top,
          newPos: staticBounds.top,
        },
        {
          nodeEdge: 'top',
          targetEdge: 'vCenter',
          draggedVal: draggedBounds.top,
          staticVal: staticBounds.vCenter,
          newPos: staticBounds.vCenter,
        },
        {
          nodeEdge: 'top',
          targetEdge: 'bottom',
          draggedVal: draggedBounds.top,
          staticVal: staticBounds.bottom,
          newPos: staticBounds.bottom,
        },

        {
          nodeEdge: 'vCenter',
          targetEdge: 'top',
          draggedVal: draggedBounds.vCenter,
          staticVal: staticBounds.top,
          newPos: staticBounds.top - draggedBounds.height / 2,
        },
        {
          nodeEdge: 'vCenter',
          targetEdge: 'vCenter',
          draggedVal: draggedBounds.vCenter,
          staticVal: staticBounds.vCenter,
          newPos: staticBounds.vCenter - draggedBounds.height / 2,
        },
        {
          nodeEdge: 'vCenter',
          targetEdge: 'bottom',
          draggedVal: draggedBounds.vCenter,
          staticVal: staticBounds.bottom,
          newPos: staticBounds.bottom - draggedBounds.height / 2,
        },

        {
          nodeEdge: 'bottom',
          targetEdge: 'top',
          draggedVal: draggedBounds.bottom,
          staticVal: staticBounds.top,
          newPos: staticBounds.top - draggedBounds.height,
        },
        {
          nodeEdge: 'bottom',
          targetEdge: 'vCenter',
          draggedVal: draggedBounds.bottom,
          staticVal: staticBounds.vCenter,
          newPos: staticBounds.vCenter - draggedBounds.height,
        },
        {
          nodeEdge: 'bottom',
          targetEdge: 'bottom',
          draggedVal: draggedBounds.bottom,
          staticVal: staticBounds.bottom,
          newPos: staticBounds.bottom - draggedBounds.height,
        },
      ];

      for (const snap of vTargets) {
        const diff = Math.abs(snap.draggedVal - snap.staticVal);
        if (diff < outerTolerance) {
          const lineX1 = Math.min(draggedBounds.left, staticBounds.left) - SNAP_THRESHOLD * 2;
          const lineX2 = Math.max(draggedBounds.right, staticBounds.right) + SNAP_THRESHOLD * 2;
          this._drawSnapLine([lineX1, snap.staticVal, lineX2, snap.staticVal]);
          break; // Only draw one guide line per axis per target
        }
      }
    }
  }

  private _handleDragBound(pos: { x: number; y: number }): { x: number; y: number } {
    const draggedNode = this.getSelection() as KonvaImage | Konva.Group | null;
    if (!draggedNode || !(draggedNode instanceof KonvaImage || draggedNode instanceof Konva.Group)) {
      return pos; // Should not happen if selection is managed correctly
    }

    const stage = this.getStage();
    if (!stage) return pos;

    let newAbsX = pos.x;
    let newAbsY = pos.y;

    // Calculate draggedBounds based on the PROPOSED position for accurate diffing
    const tempClientRect = draggedNode.getClientRect({ skipShadow: true, skipStroke: true });
    const draggedBounds = {
      left: pos.x,
      right: pos.x + tempClientRect.width,
      top: pos.y,
      bottom: pos.y + tempClientRect.height,
      hCenter: pos.x + tempClientRect.width / 2,
      vCenter: pos.y + tempClientRect.height / 2,
      width: tempClientRect.width,
      height: tempClientRect.height,
    };

    const scale = stage.scaleX() || 1;
    const innerTolerance = STICKY_THRESHOLD_INNER / scale;

    let lockedConns = this._getConnectionsForNode(draggedNode.id());

    // If dragging a group, temporarily ignore connections *within* that group for the primary node's bounds calculation
    // This allows the group to be dragged away from an external snap, or for the primary node to break an internal snap if dragged hard enough (future)
    // For now, it just allows the group to move as one unit if the primary is part of _currentSnapDragGroup.
    if (this._currentSnapDragGroup.length > 1) {
      const groupMemberIds = new Set(this._currentSnapDragGroup.map((member) => member.node.id()));
      lockedConns = lockedConns.filter(
        (conn) => !groupMemberIds.has(conn.nodeId) || !groupMemberIds.has(conn.targetNodeId)
      );
    }

    const xLocked = lockedConns.some((c) => ['left', 'right', 'hCenter'].includes(c.nodeEdge));
    const yLocked = lockedConns.some((c) => ['top', 'bottom', 'vCenter'].includes(c.nodeEdge));

    const stageBoundsHelper = (s: Konva.Stage) => ({
      left: 0,
      right: s.width() / (s.scaleX() || 1),
      top: 0,
      bottom: s.height() / (s.scaleY() || 1),
      hCenter: s.width() / (s.scaleX() || 1) / 2,
      vCenter: s.height() / (s.scaleY() || 1) / 2,
      width: s.width() / (s.scaleX() || 1),
      height: s.height() / (s.scaleY() || 1),
    });
    const stageStatic = stageBoundsHelper(stage);
    const virtualStageNode: any = {
      id: () => '__stage__',
      getClientRect: () => stageStatic,
      width: () => stageStatic.width,
      height: () => stageStatic.height,
      absolutePosition: () => ({ x: stageStatic.left, y: stageStatic.top }),
    };

    // Search the entire stage for other images/groups, not just BaseScene children
    const allNodes = stage.find('Image, Group') as (Konva.Image | Konva.Group)[];
    const otherImages = allNodes.filter((node) => {
      if (node === draggedNode || !node.isVisible()) return false;

      if (node instanceof KonvaImage) {
        return true;
      }
      // Check if it's a Group containing an Image (URLImage component)
      if (node instanceof Konva.Group) {
        const hasImage = node.findOne('Image');
        return !!hasImage;
      }
      return false;
    }) as (KonvaImage | Konva.Group)[];
    const snapCandidates = [...otherImages, virtualStageNode];

    if (xLocked) {
      const conn = lockedConns.find((c) => ['left', 'right', 'hCenter'].includes(c.nodeEdge))!;
      const targetNode = conn.targetNodeId === '__stage__' ? virtualStageNode : this.findOne(`#${conn.targetNodeId}`);
      const targetBounds =
        targetNode === virtualStageNode ? stageStatic : this._getVisualBounds(targetNode as KonvaImage | Konva.Group);

      switch (conn.nodeEdge) {
        case 'left':
          newAbsX =
            conn.targetEdge === 'left'
              ? targetBounds.left
              : conn.targetEdge === 'hCenter'
                ? targetBounds.hCenter
                : targetBounds.right;
          break;
        case 'hCenter':
          newAbsX =
            (conn.targetEdge === 'left'
              ? targetBounds.left
              : conn.targetEdge === 'hCenter'
                ? targetBounds.hCenter
                : targetBounds.right) -
            draggedBounds.width / 2;
          break;
        case 'right':
          newAbsX =
            (conn.targetEdge === 'left'
              ? targetBounds.left
              : conn.targetEdge === 'hCenter'
                ? targetBounds.hCenter
                : targetBounds.right) - draggedBounds.width;
          break;
      }
    } else {
      // Not X-locked, check for new X snaps
      // Define the type for an individual snap target object based on its structure
      type SnapTarget = {
        nodeEdge: Edge;
        targetEdge: Edge;
        draggedVal: number;
        staticVal: number;
        newPos: number;
      };

      let bestHSnap: (SnapTarget & { targetNodeId: string }) | null = null;
      let minHDiff = Infinity;

      for (const staticNode of snapCandidates) {
        const staticBounds =
          staticNode === virtualStageNode ? stageStatic : this._getVisualBounds(staticNode as KonvaImage | Konva.Group);
        const hTargets = [
          {
            nodeEdge: 'left',
            targetEdge: 'left',
            draggedVal: draggedBounds.left,
            staticVal: staticBounds.left,
            newPos: staticBounds.left,
          },
          {
            nodeEdge: 'left',
            targetEdge: 'hCenter',
            draggedVal: draggedBounds.left,
            staticVal: staticBounds.hCenter,
            newPos: staticBounds.hCenter,
          },
          {
            nodeEdge: 'left',
            targetEdge: 'right',
            draggedVal: draggedBounds.left,
            staticVal: staticBounds.right,
            newPos: staticBounds.right,
          },
          {
            nodeEdge: 'hCenter',
            targetEdge: 'left',
            draggedVal: draggedBounds.hCenter,
            staticVal: staticBounds.left,
            newPos: staticBounds.left - draggedBounds.width / 2,
          },
          {
            nodeEdge: 'hCenter',
            targetEdge: 'hCenter',
            draggedVal: draggedBounds.hCenter,
            staticVal: staticBounds.hCenter,
            newPos: staticBounds.hCenter - draggedBounds.width / 2,
          },
          {
            nodeEdge: 'hCenter',
            targetEdge: 'right',
            draggedVal: draggedBounds.hCenter,
            staticVal: staticBounds.right,
            newPos: staticBounds.right - draggedBounds.width / 2,
          },
          {
            nodeEdge: 'right',
            targetEdge: 'left',
            draggedVal: draggedBounds.right,
            staticVal: staticBounds.left,
            newPos: staticBounds.left - draggedBounds.width,
          },
          {
            nodeEdge: 'right',
            targetEdge: 'hCenter',
            draggedVal: draggedBounds.right,
            staticVal: staticBounds.hCenter,
            newPos: staticBounds.hCenter - draggedBounds.width,
          },
          {
            nodeEdge: 'right',
            targetEdge: 'right',
            draggedVal: draggedBounds.right,
            staticVal: staticBounds.right,
            newPos: staticBounds.right - draggedBounds.width,
          },
        ] as const; // Add 'as const' for better type inference on nodeEdge/targetEdge strings

        for (const snap of hTargets) {
          const diff = Math.abs(snap.draggedVal - snap.staticVal);
          if (diff < innerTolerance) {
            if (diff < minHDiff) {
              minHDiff = diff;
              bestHSnap = { ...snap, targetNodeId: (staticNode as any).id() };
            }
          }
        }
      }
      if (bestHSnap) {
        newAbsX = bestHSnap.newPos;
        // DO NOT CALL _addConnection here anymore for new snaps.
        // Connection is finalized on dragend.
        // if (bestHSnap.targetNodeId !== '__stage__') {
        //     this._addConnection({ nodeId: draggedNode.id(), targetNodeId: bestHSnap.targetNodeId, nodeEdge: bestHSnap.nodeEdge as Edge, targetEdge: bestHSnap.targetEdge as Edge });
        // }
      }
    }

    if (yLocked) {
      const conn = lockedConns.find((c) => ['top', 'bottom', 'vCenter'].includes(c.nodeEdge))!;
      const targetNode = conn.targetNodeId === '__stage__' ? virtualStageNode : this.findOne(`#${conn.targetNodeId}`);
      const targetBounds =
        targetNode === virtualStageNode ? stageStatic : this._getVisualBounds(targetNode as KonvaImage | Konva.Group);

      switch (conn.nodeEdge) {
        case 'top':
          newAbsY =
            conn.targetEdge === 'top'
              ? targetBounds.top
              : conn.targetEdge === 'vCenter'
                ? targetBounds.vCenter
                : targetBounds.bottom;
          break;
        case 'vCenter':
          newAbsY =
            (conn.targetEdge === 'top'
              ? targetBounds.top
              : conn.targetEdge === 'vCenter'
                ? targetBounds.vCenter
                : targetBounds.bottom) -
            draggedBounds.height / 2;
          break;
        case 'bottom':
          newAbsY =
            (conn.targetEdge === 'top'
              ? targetBounds.top
              : conn.targetEdge === 'vCenter'
                ? targetBounds.vCenter
                : targetBounds.bottom) - draggedBounds.height;
          break;
      }
    } else {
      // Not Y-locked, check for new Y snaps
      // Re-use SnapTarget type defined above
      type SnapTarget = {
        nodeEdge: Edge;
        targetEdge: Edge;
        draggedVal: number;
        staticVal: number;
        newPos: number;
      };
      let bestVSnap: (SnapTarget & { targetNodeId: string }) | null = null;
      let minVDiff = Infinity;

      for (const staticNode of snapCandidates) {
        const staticBounds =
          staticNode === virtualStageNode ? stageStatic : this._getVisualBounds(staticNode as KonvaImage | Konva.Group);
        const vTargets = [
          {
            nodeEdge: 'top',
            targetEdge: 'top',
            draggedVal: draggedBounds.top,
            staticVal: staticBounds.top,
            newPos: staticBounds.top,
          },
          {
            nodeEdge: 'top',
            targetEdge: 'vCenter',
            draggedVal: draggedBounds.top,
            staticVal: staticBounds.vCenter,
            newPos: staticBounds.vCenter,
          },
          {
            nodeEdge: 'top',
            targetEdge: 'bottom',
            draggedVal: draggedBounds.top,
            staticVal: staticBounds.bottom,
            newPos: staticBounds.bottom,
          },
          {
            nodeEdge: 'vCenter',
            targetEdge: 'top',
            draggedVal: draggedBounds.vCenter,
            staticVal: staticBounds.top,
            newPos: staticBounds.top - draggedBounds.height / 2,
          },
          {
            nodeEdge: 'vCenter',
            targetEdge: 'vCenter',
            draggedVal: draggedBounds.vCenter,
            staticVal: staticBounds.vCenter,
            newPos: staticBounds.vCenter - draggedBounds.height / 2,
          },
          {
            nodeEdge: 'vCenter',
            targetEdge: 'bottom',
            draggedVal: draggedBounds.vCenter,
            staticVal: staticBounds.bottom,
            newPos: staticBounds.bottom - draggedBounds.height / 2,
          },
          {
            nodeEdge: 'bottom',
            targetEdge: 'top',
            draggedVal: draggedBounds.bottom,
            staticVal: staticBounds.top,
            newPos: staticBounds.top - draggedBounds.height,
          },
          {
            nodeEdge: 'bottom',
            targetEdge: 'vCenter',
            draggedVal: draggedBounds.bottom,
            staticVal: staticBounds.vCenter,
            newPos: staticBounds.vCenter - draggedBounds.height,
          },
          {
            nodeEdge: 'bottom',
            targetEdge: 'bottom',
            draggedVal: draggedBounds.bottom,
            staticVal: staticBounds.bottom,
            newPos: staticBounds.bottom - draggedBounds.height,
          },
        ] as const; // Add 'as const' for better type inference

        for (const snap of vTargets) {
          const diff = Math.abs(snap.draggedVal - snap.staticVal);
          if (diff < innerTolerance) {
            if (diff < minVDiff) {
              minVDiff = diff;
              bestVSnap = { ...snap, targetNodeId: (staticNode as any).id() };
            }
          }
        }
      }
      if (bestVSnap) {
        newAbsY = bestVSnap.newPos;
        // DO NOT CALL _addConnection here anymore for new snaps.
        // if (bestVSnap.targetNodeId !== '__stage__') {
        //     this._addConnection({ nodeId: draggedNode.id(), targetNodeId: bestVSnap.targetNodeId, nodeEdge: bestVSnap.nodeEdge as Edge, targetEdge: bestVSnap.targetEdge as Edge });
        // }
      }
    }
    return { x: newAbsX, y: newAbsY };
  }

  private _finalizeSnapConnections(draggedNode: KonvaImage | Konva.Group) {
    // console.log(`[BaseScene._finalizeSnapConnections] Processing Node: ${draggedNode.id()} (className: ${draggedNode.className})`);
    const stage = this.getStage();
    if (!stage) return;

    const draggedBounds = this._getVisualBounds(draggedNode);
    const scale = stage.scaleX() || 1;
    const innerTolerance = STICKY_THRESHOLD_INNER / scale;

    const allPotentialTargets = stage.find('Image, Group') as (Konva.Image | Konva.Group)[];
    const otherImageElements = allPotentialTargets.filter((node) => {
      if (node === draggedNode || !node.isVisible() || !node.id()) return false; // <<<< Ensure static node has an ID
      return node instanceof KonvaImage || (node instanceof Konva.Group && !!node.findOne('Image'));
    });

    for (const staticNode of otherImageElements) {
      // No need to check staticNode.id() here again as it's filtered above
      const staticNodeId = staticNode.id()!;
      const staticBounds = this._getVisualBounds(staticNode);
      // console.log(`[BaseScene._finalizeSnapConnections]   Checking against staticNode: ${staticNodeId} (className: ${staticNode.className})`);

      let bestHSnap: { connection: SnapConnection; diff: number } | null = null;
      let bestVSnap: { connection: SnapConnection; diff: number } | null = null;

      // Evaluate Horizontal Snaps
      const hTargets = [
        { nodeEdge: 'left', targetEdge: 'left', draggedVal: draggedBounds.left, staticVal: staticBounds.left },
        { nodeEdge: 'left', targetEdge: 'hCenter', draggedVal: draggedBounds.left, staticVal: staticBounds.hCenter },
        { nodeEdge: 'left', targetEdge: 'right', draggedVal: draggedBounds.left, staticVal: staticBounds.right },
        { nodeEdge: 'hCenter', targetEdge: 'left', draggedVal: draggedBounds.hCenter, staticVal: staticBounds.left },
        {
          nodeEdge: 'hCenter',
          targetEdge: 'hCenter',
          draggedVal: draggedBounds.hCenter,
          staticVal: staticBounds.hCenter,
        },
        { nodeEdge: 'hCenter', targetEdge: 'right', draggedVal: draggedBounds.hCenter, staticVal: staticBounds.right },
        { nodeEdge: 'right', targetEdge: 'left', draggedVal: draggedBounds.right, staticVal: staticBounds.left },
        { nodeEdge: 'right', targetEdge: 'hCenter', draggedVal: draggedBounds.right, staticVal: staticBounds.hCenter },
        { nodeEdge: 'right', targetEdge: 'right', draggedVal: draggedBounds.right, staticVal: staticBounds.right },
      ] as const;

      for (const snap of hTargets) {
        const diff = Math.abs(snap.draggedVal - snap.staticVal);
        if (diff <= innerTolerance) {
          // console.log(
          //   `[BaseScene._finalizeSnapConnections]     Found potential H-snap: ${draggedNode.id()}:${snap.nodeEdge} to ${staticNodeId}:${snap.targetEdge}, diff: ${diff.toFixed(2)}`
          // );
          if (!bestHSnap || diff < bestHSnap.diff) {
            bestHSnap = {
              connection: {
                nodeId: draggedNode.id(),
                targetNodeId: staticNodeId,
                nodeEdge: snap.nodeEdge,
                targetEdge: snap.targetEdge,
              },
              diff: diff,
            };
          }
        }
      }
      if (bestHSnap) {
        // console.log(
        //   `[BaseScene._finalizeSnapConnections]     Best H-snap candidate: ${bestHSnap.connection.nodeEdge} to ${bestHSnap.connection.targetEdge} on ${staticNodeId}, diff: ${bestHSnap.diff.toFixed(2)}`
        // );
      }

      // Evaluate Vertical Snaps
      const vTargets = [
        { nodeEdge: 'top', targetEdge: 'top', draggedVal: draggedBounds.top, staticVal: staticBounds.top },
        { nodeEdge: 'top', targetEdge: 'vCenter', draggedVal: draggedBounds.top, staticVal: staticBounds.vCenter },
        { nodeEdge: 'top', targetEdge: 'bottom', draggedVal: draggedBounds.top, staticVal: staticBounds.bottom },
        { nodeEdge: 'vCenter', targetEdge: 'top', draggedVal: draggedBounds.vCenter, staticVal: staticBounds.top },
        {
          nodeEdge: 'vCenter',
          targetEdge: 'vCenter',
          draggedVal: draggedBounds.vCenter,
          staticVal: staticBounds.vCenter,
        },
        {
          nodeEdge: 'vCenter',
          targetEdge: 'bottom',
          draggedVal: draggedBounds.vCenter,
          staticVal: staticBounds.bottom,
        },
        { nodeEdge: 'bottom', targetEdge: 'top', draggedVal: draggedBounds.bottom, staticVal: staticBounds.top },
        {
          nodeEdge: 'bottom',
          targetEdge: 'vCenter',
          draggedVal: draggedBounds.bottom,
          staticVal: staticBounds.vCenter,
        },
        { nodeEdge: 'bottom', targetEdge: 'bottom', draggedVal: draggedBounds.bottom, staticVal: staticBounds.bottom },
      ] as const;

      for (const snap of vTargets) {
        const diff = Math.abs(snap.draggedVal - snap.staticVal);
        if (diff <= innerTolerance) {
          // console.log(
          //   `[BaseScene._finalizeSnapConnections]     Found potential V-snap: ${draggedNode.id()}:${snap.nodeEdge} to ${staticNodeId}:${snap.targetEdge}, diff: ${diff.toFixed(2)}`
          // );
          if (!bestVSnap || diff < bestVSnap.diff) {
            bestVSnap = {
              connection: {
                nodeId: draggedNode.id(),
                targetNodeId: staticNodeId,
                nodeEdge: snap.nodeEdge,
                targetEdge: snap.targetEdge,
              },
              diff: diff,
            };
          }
        }
      }
      if (bestVSnap) {
        // console.log(
        //   `[BaseScene._finalizeSnapConnections]     Best V-snap candidate: ${bestVSnap.connection.nodeEdge} to ${bestVSnap.connection.targetEdge} on ${staticNodeId}, diff: ${bestVSnap.diff.toFixed(2)}`
        // );
      }

      // Decision Logic
      if (bestHSnap && bestVSnap) {
        if (bestHSnap.diff < bestVSnap.diff) {
          this._addConnection(bestHSnap.connection);
          // console.log(
          //   `[BaseScene._finalizeSnapConnections]   DECISION for ${staticNodeId}: Added H-snap (H diff ${bestHSnap.diff.toFixed(2)} < V diff ${bestVSnap.diff.toFixed(2)})`,
          //   bestHSnap.connection
          // );
        } else if (bestVSnap.diff < bestHSnap.diff) {
          // V is strictly better
          this._addConnection(bestVSnap.connection);
          // console.log(
          //   `[BaseScene._finalizeSnapConnections]   DECISION for ${staticNodeId}: Added V-snap (V diff ${bestVSnap.diff.toFixed(2)} < H diff ${bestHSnap.diff.toFixed(2)})`,
          //   bestVSnap.connection
          // );
        } else {
          // Diffs are equal
          // In case of equal diffs (e.g., both 0.00 for a perfect corner), prioritize Vertical connection.
          this._addConnection(bestVSnap.connection);
          // console.log(
          //   `[BaseScene._finalizeSnapConnections]   DECISION for ${staticNodeId}: Diffs equal (H ${bestHSnap.diff.toFixed(2)}, V ${bestVSnap.diff.toFixed(2)}). Prioritizing V-snap.`,
          //   bestVSnap.connection
          // );
        }
      } else if (bestHSnap) {
        this._addConnection(bestHSnap.connection);
        // console.log(
        //   `[BaseScene._finalizeSnapConnections]   DECISION for ${staticNodeId}: Added H-snap (Only H found, diff ${bestHSnap.diff.toFixed(2)})`,
        //   bestHSnap.connection
        // );
      } else if (bestVSnap) {
        this._addConnection(bestVSnap.connection);
        // console.log(
        //   `[BaseScene._finalizeSnapConnections]   DECISION for ${staticNodeId}: Added V-snap (Only V found, diff ${bestVSnap.diff.toFixed(2)})`,
        //   bestVSnap.connection
        // );
      } else {
        // console.log(`[BaseScene._finalizeSnapConnections]   DECISION for ${staticNodeId}: No qualifying snaps found.`);
      }
    }
    // Note: The dispatchEvent logic for elementAttrsChanged was moved to _snapDragEndHandler in a previous step
    // to ensure it happens once after all group processing.
  }

  // --- Group Dragging Helper ---
  private _findAllConnectedNodes(startNodeId: string, connections: SnapConnection[]): string[] {
    const visited = new Set<string>();
    const queue: string[] = [startNodeId];
    const groupMembers: string[] = [];

    while (queue.length > 0) {
      const currentId = queue.shift()!;
      if (visited.has(currentId)) {
        continue;
      }
      visited.add(currentId);
      // Only add actual KonvaImage node IDs, not the virtual stage
      if (currentId !== '__stage__') {
        groupMembers.push(currentId);
      }

      connections.forEach((conn) => {
        if (conn.nodeId === currentId && conn.targetNodeId !== '__stage__' && !visited.has(conn.targetNodeId)) {
          queue.push(conn.targetNodeId);
        } else if (conn.targetNodeId === currentId && conn.nodeId !== '__stage__' && !visited.has(conn.nodeId)) {
          // Ensure we add the other side of the connection if it's not the currentId itself
          // and it's not the __stage__ and not visited
          queue.push(conn.nodeId);
        }
      });
    }
    // console.log(`[_findAllConnectedNodes] For startNode ${startNodeId}, found members:`, groupMembers);
    return groupMembers;
  }

  private _identifyAndStoreSnapGroup(draggedNode: KonvaImage | Konva.Group) {
    this._currentSnapDragGroup = [];
    const primaryInitialPos = draggedNode.absolutePosition(); // This IS the primary for this drag call
    this._primaryDraggedNode = draggedNode as any; // ensure it's set here too

    // console.log(`[BaseScene._identifyAndStoreSnapGroup] Primary dragged node: ${draggedNode.id()}`, primaryInitialPos);

    const allMemberIds = this._findAllConnectedNodes(draggedNode.id(), this._activeSnapConnections);

    // console.log(`[BaseScene._identifyAndStoreSnapGroup] All connected member IDs:`, allMemberIds);
    const stage = this.getStage(); // Get stage here
    if (!stage) {
      // console.error("[BaseScene._identifyAndStoreSnapGroup] Stage not found!");
      return;
    }

    allMemberIds.forEach((id) => {
      const node = stage.findOne(`#${id}`) as KonvaImage | Konva.Group | null; // Use stage.findOne
      if (node) {
        const initialAbsPos = node.absolutePosition();
        let initialOffsetFromPrimary: { dx: number; dy: number } | undefined = undefined;
        if (node !== draggedNode) {
          initialOffsetFromPrimary = {
            dx: initialAbsPos.x - primaryInitialPos.x,
            dy: initialAbsPos.y - primaryInitialPos.y,
          };
        }
        this._currentSnapDragGroup.push({ node: node as any, initialAbsPos, initialOffsetFromPrimary });
      }
    });

    // Ensure the dragged node itself is in the group if it had no other connections
    // (e.g. _findAllConnectedNodes only returned itself)
    if (!this._currentSnapDragGroup.find((m) => m.node.id() === draggedNode.id()) && draggedNode) {
      this._currentSnapDragGroup.push({ node: draggedNode as any, initialAbsPos: primaryInitialPos });
    }
    // console.log(`[BaseScene._identifyAndStoreSnapGroup] Final _currentSnapDragGroup:`, this._currentSnapDragGroup.map(m => ({id: m.node.id(), pos: m.initialAbsPos, offset: m.initialOffsetFromPrimary })));
    // console.log(
    //   `[BaseScene._identifyAndStoreSnapGroup] END. Primary: ${this._primaryDraggedNode?.id()}. Group size: ${this._currentSnapDragGroup.length}. Members:`,
    //   this._currentSnapDragGroup.map((m) => m.node.id())
    // );
  }

  private _handleImageCroppedAftermath(event: CustomEvent) {
    const { id: croppedNodeId } = event.detail;
    const stage = this.getStage();
    if (!stage) {
      // console.warn('[BaseScene._handleImageCroppedAftermath] Stage not found for cropped node search:', croppedNodeId);
      return;
    }
    // Cast to Konva.Node initially, then we can check instanceof KonvaImage or Konva.Group if needed for specific logic
    const croppedNode = stage.findOne(`#${croppedNodeId}`) as Konva.Node | null;

    if (!croppedNode || !(croppedNode instanceof KonvaImage || croppedNode instanceof Konva.Group)) {
      // console.warn('[BaseScene._handleImageCroppedAftermath] Cropped node not found in BaseScene or not a Group/KonvaImage:', croppedNodeId);
      return;
    }
    const castedCroppedNode = croppedNode as KonvaImage | Konva.Group; // Now we have the correct union type

    // console.log('[BaseScene._handleImageCroppedAftermath] Event received for node:', croppedNodeId, event.detail);

    const originalConnections: Array<{
      partnerNode: Konva.Image | Konva.Group;
      connection: SnapConnection;
      amISource: boolean;
    }> = [];
    [...this._activeSnapConnections].forEach((conn) => {
      if (conn.nodeId === croppedNodeId) {
        const partner = stage.findOne(`#${conn.targetNodeId}`) as Konva.Node | null;
        if (
          partner &&
          partner.id() &&
          (partner instanceof KonvaImage ||
            (partner instanceof Konva.Group && (partner as Konva.Group).findOne('Image')))
        ) {
          originalConnections.push({
            partnerNode: partner as Konva.Image | Konva.Group,
            connection: { ...conn },
            amISource: true,
          });
        }
      } else if (conn.targetNodeId === croppedNodeId) {
        const partner = stage.findOne(`#${conn.nodeId}`) as Konva.Node | null;
        if (
          partner &&
          partner.id() &&
          (partner instanceof KonvaImage ||
            (partner instanceof Konva.Group && (partner as Konva.Group).findOne('Image')))
        ) {
          originalConnections.push({
            partnerNode: partner as Konva.Image | Konva.Group,
            connection: { ...conn },
            amISource: false,
          });
        }
      }
    });

    if (originalConnections.length === 0) {
      // console.log(
      //   '[BaseScene._handleImageCroppedAftermath] Cropped node had no active connections. Nothing to readjust.'
      // );
      this._finalizeSnapConnections(castedCroppedNode); // Finalize for the cropped node itself
      castedCroppedNode.getLayer()?.batchDraw();
      return;
    }

    // console.log(
    //   '[BaseScene._handleImageCroppedAftermath] Original connections for cropped node:',
    //   originalConnections.map((oc) => ({ ...oc.connection }))
    // );

    this._removeConnections(croppedNodeId);
    // console.log('[BaseScene._handleImageCroppedAftermath] Removed all connections for cropped node:', croppedNodeId);

    let movedX = false;
    let movedY = false;

    for (const origConn of originalConnections) {
      const { partnerNode, connection, amISource } = origConn;

      const myOldEdge = amISource ? connection.nodeEdge : connection.targetEdge;
      const theirEdge = amISource ? connection.targetEdge : connection.nodeEdge;

      const croppedNodeBounds = this._getVisualBounds(castedCroppedNode);
      const partnerBounds = this._getVisualBounds(partnerNode); // partnerNode is already Konva.Image | Konva.Group

      let dx = 0;
      let dy = 0;

      if (myOldEdge === 'left' && theirEdge === 'right') dx = partnerBounds.right - croppedNodeBounds.left;
      else if (myOldEdge === 'right' && theirEdge === 'left') dx = partnerBounds.left - croppedNodeBounds.right;
      else if (myOldEdge === 'left' && theirEdge === 'left') dx = partnerBounds.left - croppedNodeBounds.left;
      else if (myOldEdge === 'right' && theirEdge === 'right') dx = partnerBounds.right - croppedNodeBounds.right;

      if (myOldEdge === 'top' && theirEdge === 'bottom') dy = partnerBounds.bottom - croppedNodeBounds.top;
      else if (myOldEdge === 'bottom' && theirEdge === 'top') dy = partnerBounds.top - croppedNodeBounds.bottom;
      else if (myOldEdge === 'top' && theirEdge === 'top') dy = partnerBounds.top - croppedNodeBounds.top;
      else if (myOldEdge === 'bottom' && theirEdge === 'bottom') dy = partnerBounds.bottom - croppedNodeBounds.bottom;

      if (dx !== 0 || dy !== 0) {
        // console.log(
        //   `[BaseScene._handleImageCroppedAftermath] Repositioning ${croppedNodeId} by dx:${dx}, dy:${dy} to re-align with ${partnerNode.id()} (orig edges ${myOldEdge}->${theirEdge})`
        // );
        castedCroppedNode.move({ x: dx, y: dy });
        castedCroppedNode.getLayer()?.batchDraw();
        if (dx !== 0) movedX = true;
        if (dy !== 0) movedY = true;

        const newConnection: SnapConnection = {
          nodeId: amISource ? castedCroppedNode.id()! : partnerNode.id()!,
          targetNodeId: amISource ? partnerNode.id()! : castedCroppedNode.id()!,
          nodeEdge: amISource ? myOldEdge : theirEdge,
          targetEdge: amISource ? theirEdge : myOldEdge,
        };
        this._addConnection(newConnection);

        croppedNodeBounds.left += dx;
        croppedNodeBounds.right += dx;
        croppedNodeBounds.hCenter += dx;
        croppedNodeBounds.top += dy;
        croppedNodeBounds.bottom += dy;
        croppedNodeBounds.vCenter += dy;
      }
    }

    // console.log(
    //   `[BaseScene._handleImageCroppedAftermath] Finalizing connections for cropped node ${croppedNodeId} (movedX: ${movedX}, movedY: ${movedY}).`
    // );
    this._finalizeSnapConnections(castedCroppedNode);

    const uniquePartners = new Set<Konva.Image | Konva.Group>();
    originalConnections.forEach((oc) => uniquePartners.add(oc.partnerNode));

    uniquePartners.forEach((partner) => {
      if (partner.id() !== croppedNodeId) {
        // console.log(
        //   `[BaseScene._handleImageCroppedAftermath] Finalizing connections for original partner ${partner.id()}`
        // );
        // Ensure partner is of the correct type before passing to _finalizeSnapConnections
        if (partner instanceof KonvaImage || (partner instanceof Konva.Group && partner.findOne('Image'))) {
          this._finalizeSnapConnections(partner as KonvaImage | Konva.Group);
        }
      }
    });

    castedCroppedNode.getLayer()?.batchDraw();
  }

  private _handleContextMenu(e: Konva.KonvaEventObject<MouseEvent>) {
    e.evt.preventDefault(); // Prevent default browser context menu

    const rawTargetNode = e.target;
    let nodeForContext: Konva.Node | null = null;

    // console.log(`[BaseScene _handleContextMenu] Raw click target: className=${rawTargetNode.className}, id=${rawTargetNode.id()}`);

    // Determine the actual node we want to get context for
    if (rawTargetNode.className === 'Image') {
      const parentGroup = rawTargetNode.getParent();
      if (parentGroup && parentGroup.id()) {
        // If image has a parent with an ID, assume it's a component group like URLImage
        nodeForContext = parentGroup;
        // console.log(`[BaseScene _handleContextMenu] Target is Image within Group ${parentGroup.id()}. Using Group for context menu.`);
      } else {
        nodeForContext = rawTargetNode; // Plain KonvaImage
        // console.log(`[BaseScene _handleContextMenu] Target is plain Image ${rawTargetNode.id() || '(no id)'}. Using Image for context menu.`);
      }
    } else if (
      rawTargetNode.className === 'Group' &&
      rawTargetNode.id() &&
      (rawTargetNode as Konva.Group).findOne('Image')
    ) {
      nodeForContext = rawTargetNode; // Clicked directly on a Group with an ID and an Image inside (likely URLImage)
      // console.log(`[BaseScene _handleContextMenu] Target is Group ${rawTargetNode.id()} with an Image. Using Group for context menu.`);
    } else if (rawTargetNode instanceof KonvaImage) {
      nodeForContext = rawTargetNode; // Direct click on a KonvaImage not fitting above (e.g. no parent or parent has no ID)
      // console.log(`[BaseScene _handleContextMenu] Target is KonvaImage instance ${rawTargetNode.id()}. Using Image for context menu.`);
    } else {
      // console.log(`[BaseScene _handleContextMenu] Click on unhandled target ${rawTargetNode.className}. Hiding menu.`);
      window.dispatchEvent(new CustomEvent('canvas:hideImageContextMenu'));
      return;
    }

    if (!nodeForContext || !nodeForContext.id()) {
      // console.log('[BaseScene _handleContextMenu] No identifiable node or node ID for context menu. Hiding menu.');
      window.dispatchEvent(new CustomEvent('canvas:hideImageContextMenu'));
      return;
    }

    // Check if the context menu was triggered on or within an HTMLElement's Konva group (which handles its own context menu)
    if (rawTargetNode.findAncestor('.html-element-konva-group')) {
      // console.log('[BaseScene] Context menu on HTMLElement, allowing HTMLElement to handle it.');
      // Do not stop propagation or dispatch hide, let HTMLElement manage.
      return;
    }

    e.evt.stopPropagation(); // Stop propagation if we are handling it here

    const stage = this.getStage();
    const pointerPosition = stage?.getPointerPosition();

    if (!pointerPosition) {
      // console.error('[BaseScene _handleContextMenu] No pointer position.');
      return;
    }

    // Determine connected edges using the ID of nodeForContext
    const activeNodeId = nodeForContext.id()!;
    const imageConnectedEdges = new Set<Edge>();
    this._activeSnapConnections.forEach((conn) => {
      if (conn.nodeId === activeNodeId) {
        imageConnectedEdges.add(conn.nodeEdge);
      }
      if (conn.targetNodeId === activeNodeId) {
        imageConnectedEdges.add(conn.targetEdge);
      }
    });
    // console.log(`[BaseScene _handleContextMenu] For node ${activeNodeId} (className: ${nodeForContext.className}), found connectedEdges:`, Array.from(imageConnectedEdges));

    // Prepare details for the context menu event, using properties of nodeForContext
    // If nodeForContext is a Group (URLImage), we might need to get display details (src, width, height) from its inner Image.
    let displaySrc = '';
    const displayWidth = nodeForContext.width();
    const displayHeight = nodeForContext.height();
    const isReference = (nodeForContext as any).isReference || false; // Default for Group or KonvaImage

    if (nodeForContext instanceof Konva.Image) {
      const htmlImg = nodeForContext.image() as HTMLImageElement | null;
      displaySrc = htmlImg?.src || '';
      // isReference is already correctly on KonvaImage if set
    } else if (nodeForContext instanceof Konva.Group) {
      const innerImage = nodeForContext.findOne('Image') as Konva.Image | undefined;
      if (innerImage) {
        const htmlImg = innerImage.image() as HTMLImageElement | null;
        displaySrc = htmlImg?.src || '';
        // For groups, width/height might be on the group, or you might prefer the inner image's for display
        // displayWidth = innerImage.width();
        // displayHeight = innerImage.height();
        // isReference for URLImage groups is stored on the group itself, not the inner image.
      }
    }

    const imageDetails = {
      id: activeNodeId,
      src: displaySrc,
      width: displayWidth,
      height: displayHeight,
      konvaX: nodeForContext.x(),
      konvaY: nodeForContext.y(),
      pointerPosition: { x: pointerPosition.x, y: pointerPosition.y },
      connectedEdges: Array.from(imageConnectedEdges),
      isReference: isReference,
    };

    // console.log('[BaseScene] Dispatching canvas:showImageContextMenu', imageDetails);
    window.dispatchEvent(
      new CustomEvent('canvas:showImageContextMenu', {
        detail: imageDetails,
      })
    );
  }

  public async exportConnectedGroupAsImage(nodeId: string): Promise<string> {
    // console.log(`[BaseScene exportConnectedGroupAsImage] Received request for nodeId: ${nodeId}`);
    const stage = this.getStage();
    if (!stage) {
      // console.error('[BaseScene exportConnectedGroupAsImage] Stage not found.');
      return '';
    }

    const groupMemberIds = this._findAllConnectedNodes(nodeId, this._activeSnapConnections);

    const MAX_EXPORT_DIMENSION = 16384;
    const DESIRED_PIXEL_RATIO = 7;

    if (!groupMemberIds || groupMemberIds.length === 0) {
      // console.warn('[BaseScene exportConnectedGroupAsImage] No group members found (initial list), checking single node.');
      const singleNode = stage.findOne(`#${nodeId}`) as Konva.Node | null; // Search stage
      if (
        singleNode &&
        (singleNode instanceof KonvaImage || (singleNode instanceof Konva.Group && singleNode.findOne('Image')))
      ) {
        // console.log('[BaseScene exportConnectedGroupAsImage] Exporting single unconnected node as image.');
        // ... (rest of single node export logic - ensure it handles Konva.Group correctly if it's a URLImage)
        // For brevity, I'm assuming the single node export logic is okay or will be reviewed separately.
        // The main fix is for multiple group members.
        // For now, let's ensure it can find the singleNode from stage:
        const mainStageScale = stage.scaleX() || 1;
        const clientRect = singleNode.getClientRect({ skipTransform: false });
        const unscaledNodeWidth = clientRect.width / mainStageScale;
        const unscaledNodeHeight = clientRect.height / mainStageScale;
        if (unscaledNodeWidth <= 0 || unscaledNodeHeight <= 0) return ''; // Simplified return
        // ... (actual single node export call - this might need to be a separate helper if complex)
        // This part needs to be robust for both KonvaImage and Konva.Group (URLImage)
        // For now, just returning a placeholder to focus on the group part
        return await this._exportNodesToDataURL(
          [singleNode as Konva.Image | Konva.Group],
          stage,
          MAX_EXPORT_DIMENSION,
          DESIRED_PIXEL_RATIO
        );
      }
      // console.warn('[BaseScene exportConnectedGroupAsImage] No node found for the given ID for single export.');
      return '';
    }

    const groupNodes = groupMemberIds
      .map((id) => stage.findOne(`#${id}`) as Konva.Image | Konva.Group | null) // Search stage
      .filter((node): node is Konva.Image | Konva.Group => {
        if (!node) return false;
        // Ensure it's a KonvaImage or a Group that contains an Image (like URLImage)
        return node instanceof KonvaImage || (node instanceof Konva.Group && !!node.findOne('Image'));
      });

    if (groupNodes.length === 0) {
      // console.warn('[BaseScene exportConnectedGroupAsImage] No valid KonvaImage or URLImage Groups found for the group IDs after filtering.');
      return '';
    }

    // console.log(
    //   '[BaseScene exportConnectedGroupAsImage] Group members to export:',
    //   groupNodes.map((n) => ({id: n.id(), className: n.className}))
    // );

    return await this._exportNodesToDataURL(groupNodes, stage, MAX_EXPORT_DIMENSION, DESIRED_PIXEL_RATIO);
  }

  // Helper function for the actual export logic, accepting an array of nodes
  private async _exportNodesToDataURL(
    nodes: (Konva.Image | Konva.Group)[],
    mainStage: Konva.Stage,
    maxExportDimension: number,
    desiredPixelRatio: number
  ): Promise<string> {
    if (nodes.length === 0) return '';

    const mainStageScale = mainStage.scaleX() || 1;

    let minX = Infinity,
      minY = Infinity,
      maxX = -Infinity,
      maxY = -Infinity;
    nodes.forEach((node) => {
      const clientRect = node.getClientRect({ skipTransform: false });
      minX = Math.min(minX, clientRect.x);
      minY = Math.min(minY, clientRect.y);
      maxX = Math.max(maxX, clientRect.x + clientRect.width);
      maxY = Math.max(maxY, clientRect.y + clientRect.height);
    });

    if (minX === Infinity) {
      // console.error('[_exportNodesToDataURL] Could not determine group bounds.');
      return '';
    }

    const scaledGroupWidth = maxX - minX;
    const scaledGroupHeight = maxY - minY;
    if (scaledGroupWidth <= 0 || scaledGroupHeight <= 0) {
      // console.warn('[_exportNodesToDataURL] Group has zero or negative dimensions (scaled).');
      return '';
    }

    const unscaledTargetWidth = scaledGroupWidth / mainStageScale;
    const unscaledTargetHeight = scaledGroupHeight / mainStageScale;

    if (unscaledTargetWidth <= 0 || unscaledTargetHeight <= 0) {
      // console.warn('[_exportNodesToDataURL] Group has zero or negative dimensions (unscaled).');
      return '';
    }

    let exportPixelRatio = desiredPixelRatio;
    const finalTempStageWidth = unscaledTargetWidth;
    const finalTempStageHeight = unscaledTargetHeight;

    if (
      unscaledTargetWidth * exportPixelRatio > maxExportDimension ||
      unscaledTargetHeight * exportPixelRatio > maxExportDimension
    ) {
      const maxRatioW = unscaledTargetWidth > 0 ? maxExportDimension / unscaledTargetWidth : desiredPixelRatio;
      const maxRatioH = unscaledTargetHeight > 0 ? maxExportDimension / unscaledTargetHeight : desiredPixelRatio;
      exportPixelRatio = Math.floor(Math.min(maxRatioW, maxRatioH, desiredPixelRatio));
      exportPixelRatio = Math.max(1, exportPixelRatio);
    }

    const tempStageContainer = document.createElement('div');
    tempStageContainer.style.position = 'absolute';
    tempStageContainer.style.top = '-9999px';
    tempStageContainer.style.left = '-9999px';
    document.body.appendChild(tempStageContainer);

    const tempStage = new Konva.Stage({
      container: tempStageContainer,
      width: finalTempStageWidth,
      height: finalTempStageHeight,
    });
    const tempLayer = new Konva.Layer();
    tempStage.add(tempLayer);

    for (const node of nodes) {
      const clone = node.clone(); // Clone can be Konva.Image or Konva.Group

      const nodeAbsPos = node.absolutePosition();
      const cloneX = (nodeAbsPos.x - minX) / mainStageScale;
      const cloneY = (nodeAbsPos.y - minY) / mainStageScale;
      clone.position({ x: cloneX, y: cloneY });

      // If it's a group (like URLImage), its children (the actual Image) are already part of the clone.
      // If it's a KonvaImage, properties like crop are on the clone itself.
      // Critical properties like width, height, crop, scale are handled by Konva's clone() for both Image and Group.
      tempLayer.add(clone);
    }
    tempLayer.draw();

    let dataURL = '';
    try {
      dataURL = await tempStage.toDataURL({
        mimeType: 'image/png',
        quality: 1,
        pixelRatio: exportPixelRatio,
      });
    } catch (error) {
      // console.error('[_exportNodesToDataURL] Error during tempStage.toDataURL():', error);
    } finally {
      tempStage.destroy();
      if (tempStageContainer.parentNode) {
        tempStageContainer.parentNode.removeChild(tempStageContainer);
      }
    }
    return dataURL;
  }
}
