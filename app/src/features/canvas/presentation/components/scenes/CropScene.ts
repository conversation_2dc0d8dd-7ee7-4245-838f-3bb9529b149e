/**
 * 🎭 Crop Scene - MODULAR VERSION
 * @description Advanced image cropping scene with visual feedback
 * @responsibility Image cropping, crop transformations, visual clipping
 * @ai_context Scene for cropping images with real-time preview and constraints
 */

import Konva from 'konva';
import SceneManager from './SceneManager';
import BaseScene from './BaseScene';
import ScaleTransformer from './ScaleTransformer';

export default class CropScene extends Konva.Group {
  public static sceneId = 'CropScene';

  private _cropTransformer: Konva.Transformer;
  private _scaleTransformer: ScaleTransformer;
  private _mask: Konva.Rect;
  private _originImage!: Konva.Image;
  private _cropGroup!: Konva.Group;
  private _clipGroup!: Konva.Group;
  private _clipImage!: Konva.Image;
  private _clipRect!: Konva.Rect;
  private _stage: Konva.Stage;
  private _sceneManager: SceneManager;
  private _enterHandler?: (e: KeyboardEvent) => void;
  private _stageScale = { x: 1, y: 1 };

  constructor(stage: Konva.Stage, sceneManager: SceneManager) {
    super();
    this._stage = stage;
    this._sceneManager = sceneManager;

    this._cropTransformer = new Konva.Transformer({
      flipEnabled: false,
      keepRatio: false,
      rotateEnabled: false,
      enabledAnchors: ['top-left', 'top-right', 'bottom-left', 'bottom-right'],
      borderStroke: '#9EA581',
      borderStrokeWidth: 2,
      anchorStroke: '#9EA581',
      anchorFill: '#9EA581',
      anchorStrokeWidth: 1,
    });
    this._scaleTransformer = new ScaleTransformer({
      flipEnabled: false,
      rotateEnabled: false,
      enabledAnchors: ['top-left', 'top-right', 'bottom-left', 'bottom-right'],
    });

    this.add(this._cropTransformer, this._scaleTransformer);

    this._mask = new Konva.Rect({
      width: this._stage.width(),
      height: this._stage.height(),
      fill: 'rgba(0,0,0,0.5)',
    });
    this._mask.addName('mask');
    this.add(this._mask);

    this._handlePointerDown = this._handlePointerDown.bind(this);
    this._handleCrop = this._handleCrop.bind(this);
    this._handleScale = this._handleScale.bind(this);
    this._handleDrag = this._handleDrag.bind(this);
  }

  // ---------------------------------------------------------------------------
  // Event bindings
  // ---------------------------------------------------------------------------

  private _registerEvents() {
    this._stage.on('pointerdown', this._handlePointerDown);
    this._cropTransformer.on('dragmove', (e) => {
      e.evt.stopPropagation();
      this._handleDrag();
    });
    this._cropTransformer.on('transform', (e) => {
      e.evt.stopPropagation();
      this._handleCrop();
    });
    if (this._originImage) {
      this._originImage.on('transform', this._handleScale);
      this._originImage.on('dragstart dragmove dragend', (e) => {
        console.log(`[CropScene DEBUG] _originImage event: ${e.type} at`, e.target.absolutePosition());
      });
    }
    this._cropGroup.on('dragstart dragmove dragend', (e) => {
      console.log(`[CropScene DEBUG] _cropGroup event: ${e.type} at`, e.target.absolutePosition());
    });
  }

  private _unbindEvents() {
    this._stage.off('pointerdown', this._handlePointerDown);
    this._cropTransformer.off('dragmove');
    this._cropTransformer.off('transform');
    if (this._originImage) {
      this._originImage.off('transform', this._handleScale);
      this._originImage.off('dragstart dragmove dragend');
    }
    this._cropGroup.off('dragstart dragmove dragend');
  }

  // ---------------------------------------------------------------------------
  // Public hooks for SceneManager
  // ---------------------------------------------------------------------------

  public show(nodeForCrop?: Konva.Image | Konva.Group): this {
    this.visible(true);

    if (!nodeForCrop) {
      console.error('[CropScene.show] No node provided for cropping.');
      this._sceneManager.goto(BaseScene as any); // Go back if no node
      return this;
    }
    if (!nodeForCrop.id()) {
      console.error('[CropScene.show] Provided node for cropping has no ID.', nodeForCrop);
      this._sceneManager.goto(BaseScene as any);
      return this;
    }

    let baseImageNode: Konva.Image; // The actual <Image> element with pixel data
    const framingNode: Konva.Image | Konva.Group = nodeForCrop; // The node whose x/y/rotation/width/height define the visual frame

    if (nodeForCrop instanceof Konva.Group) {
      // It's a URLImage Group. Find the inner Konva.Image for pixel data.
      const innerImage = nodeForCrop.findOne('Image') as Konva.Image | undefined;
      if (!innerImage || !innerImage.image()) {
        console.error(
          `[CropScene.show] URLImage Group ${nodeForCrop.id()} does not contain a valid inner Image element or its .image() is null.`
        );
        this._sceneManager.goto(BaseScene as any);
        return this;
      }
      baseImageNode = innerImage;
      // framingNode is already nodeForCrop (the Group)
      console.log(
        `[CropScene.show] Initializing for URLImage Group: ${framingNode.id()}, using inner Image for pixel data.`
      );
    } else if (nodeForCrop instanceof Konva.Image) {
      // It's a plain KonvaImage.
      if (!nodeForCrop.image()) {
        console.error(`[CropScene.show] KonvaImage ${nodeForCrop.id()} .image() is null.`);
        this._sceneManager.goto(BaseScene as any);
        return this;
      }
      baseImageNode = nodeForCrop;
      // framingNode is also nodeForCrop
      console.log(`[CropScene.show] Initializing for KonvaImage: ${framingNode.id()}`);
    } else {
      console.error('[CropScene.show] Unexpected node type provided:', nodeForCrop);
      this._sceneManager.goto(BaseScene as any);
      return this;
    }

    const htmlImageElement = baseImageNode.image() as HTMLImageElement;
    if (
      !htmlImageElement ||
      typeof htmlImageElement.naturalWidth === 'undefined' ||
      htmlImageElement.naturalWidth === 0
    ) {
      console.error(
        `[CropScene.show] HTML Image Element for node ${baseImageNode.id() || framingNode.id()} is invalid or has no natural dimensions.`
      );
      this._sceneManager.goto(BaseScene as any);
      return this;
    }

    const selImgAbsPos = framingNode.absolutePosition();
    const selImgPos = framingNode.position(); // Local position relative to its parent
    const stagePos = this._stage.position();
    const stageScale = this._stage.scale();

    console.log('[CropScene.show] Selected Node for Crop Frame Start:', {
      id: framingNode.id(),
      className: framingNode.className,
      frameAbsPos: selImgAbsPos,
      frameLocalPos: selImgPos,
      frameWidth: framingNode.width(), // Visual width of the frame
      frameHeight: framingNode.height(),
      frameRotation: framingNode.rotation(),
      baseImageNodeId: baseImageNode.id(), // ID of the node holding the actual image bitmap
      baseImageCrop: baseImageNode.crop(),
      baseImageWidth: baseImageNode.width(), // This might be different from framingNode.width()
      baseImageHeight: baseImageNode.height(),
      baseImageCropWidth: baseImageNode.cropWidth(),
      baseImageCropHeight: baseImageNode.cropHeight(),
      htmlImageNaturalWidth: htmlImageElement.naturalWidth,
      htmlImageNaturalHeight: htmlImageElement.naturalHeight,
      stagePos,
      stageScale,
    });

    // Use crop information from the baseImageNode (which holds the true crop attributes)
    const currentCrop = baseImageNode.crop();
    if (
      !currentCrop ||
      currentCrop.width === undefined ||
      currentCrop.height === undefined ||
      currentCrop.width === 0 ||
      currentCrop.height === 0
    ) {
      console.error(
        `[CropScene.show] Invalid or zero-dimension crop data on baseImageNode ${baseImageNode.id()}:`,
        currentCrop
      );
      // Default to full image if crop is invalid - this might happen if KonvaImage wasn't initialized with a crop
      baseImageNode.crop({
        x: 0,
        y: 0,
        width: htmlImageElement.naturalWidth,
        height: htmlImageElement.naturalHeight,
      });
      console.warn('[CropScene.show] Applied default full crop to baseImageNode.');
    }

    // Store the current *visual* dimensions (after any scale transforms) so we
    // precisely match what the user sees.  If the element was resized via the
    // Konva Transformer it may have non-1 scaleX/scaleY that are not reflected
    // in the raw width()/height() properties.
    const currentVisualWidth = framingNode.width() * (framingNode.scaleX() || 1);
    const currentVisualHeight = framingNode.height() * (framingNode.scaleY() || 1);

    // Reset any scale on the framing node so we deal only in absolute pixel
    // dimensions from here on.  This prevents double-scaling when we later set
    // new width/height after cropping.  We snapshot the original scale so we
    // can restore it on exit if needed (not required right now, as BaseScene
    // selection typically removes the transformer and normalises scale).
    const originalScale = { x: framingNode.scaleX(), y: framingNode.scaleY() };
    if (originalScale.x !== 1 || originalScale.y !== 1) {
      framingNode.scale({ x: 1, y: 1 });
    }

    // ────────────────────────────────────────────────────────────────────────
    // Keep the image at the EXACT same on-screen size the user already sees
    // ----------------------------------------------------------------------
    // Ratio = how much the *visual* frame (what the user sees) is scaled
    // compared to the underlying crop/data width.  Using this ratio directly
    // ensures the origin image keeps the same apparent size even after we
    // switch into crop mode, regardless of canvas zoom or previous resizing.
    // ----------------------------------------------------------------------
    const ratio = currentVisualWidth / baseImageNode.cropWidth();

    if (!isFinite(ratio) || ratio <= 0) {
      console.error('[CropScene.show] Calculated ratio is invalid (Infinity, NaN, or <= 0).', {
        framingWidth: currentVisualWidth,
        cropWidth: baseImageNode.cropWidth(),
      });
      this._sceneManager.goto(BaseScene as any);
      return this;
    }

    // Preserve stage zoom so we can restore it later if needed
    this._stageScale = { x: stageScale.x || 1, y: stageScale.y || 1 };
    // Disable stage dragging while cropping
    this._stage.draggable(false);

    const scaleToUse = ratio;

    // originImage shows the full, uncropped bitmap at this scale so that the
    // currently-visible crop area lines up 1-to-1 with what the user just saw.
    const originImageWidth = scaleToUse * htmlImageElement.naturalWidth;
    const originImageHeight = scaleToUse * htmlImageElement.naturalHeight;

    console.log('[CropScene.show] Scale calculation:', {
      currentVisualWidth,
      baseImageCropWidth: baseImageNode.cropWidth(),
      scaleToUse,
      htmlImageNaturalWidth: htmlImageElement.naturalWidth,
      htmlImageNaturalHeight: htmlImageElement.naturalHeight,
      calculatedOriginImageWidth: originImageWidth,
      calculatedOriginImageHeight: originImageHeight,
    });
    if (
      !isFinite(originImageWidth) ||
      !isFinite(originImageHeight) ||
      originImageWidth <= 0 ||
      originImageHeight <= 0
    ) {
      console.error('[CropScene.show] Calculated originImage dimensions are invalid:', {
        originImageWidth,
        originImageHeight,
        ratio,
      });
      this._sceneManager.goto(BaseScene as any);
      return this;
    }

    // Position the origin image so that the current crop area aligns with the clip rect
    // The crop area in the origin image should appear at (0, 0) relative to the crop group
    const originImageX = -baseImageNode.cropX() * scaleToUse;
    const originImageY = -baseImageNode.cropY() * scaleToUse;

    console.log('[CropScene.show] Origin image positioning:', {
      originImageX,
      originImageY,
      originImageWidth,
      originImageHeight,
      cropX: baseImageNode.cropX(),
      cropY: baseImageNode.cropY(),
      scaleToUse,
    });

    this._originImage = new Konva.Image({
      image: htmlImageElement, // Use the actual HTML element
      x: originImageX,
      y: originImageY,
      width: originImageWidth,
      height: originImageHeight,
      draggable: false,
      name: 'cropSceneOriginImage',
    });

    this._cropGroup = new Konva.Group({
      rotation: framingNode.rotation(),
      draggable: false,
      name: 'cropSceneCropGroup',
    });
    this.add(this._cropGroup);
    this._cropGroup.absolutePosition(selImgAbsPos);
    console.log('[CropScene.show] _cropGroup positioned at:', this._cropGroup.absolutePosition());

    // _clipRect is the user-manipulable rectangle.
    // Its initial x/y are based on baseImageNode's crop, scaled by the ratio, relative to _cropGroup's origin.
    const initialClipRectX = baseImageNode.cropX() * scaleToUse;
    const initialClipRectY = baseImageNode.cropY() * scaleToUse;
    // Its width/height should match the current visual dimensions exactly (preserve user's scaling)
    const visualCropWidth = currentVisualWidth;
    const visualCropHeight = currentVisualHeight;

    this._clipRect = new Konva.Rect({
      x: initialClipRectX,
      y: initialClipRectY,
      width: visualCropWidth,
      height: visualCropHeight,
      draggable: true,
      name: 'clipRect',
    });

    this._clipGroup = new Konva.Group({
      x: 0,
      y: 0,
      name: 'clipGroup',
    });
    this._clipImage = this._originImage.clone({
      x: originImageX,
      y: originImageY,
      rotation: 0,
      name: 'clipImageInClipGroup',
    });
    this._clipGroup.add(this._clipImage);

    this._cropGroup.add(this._originImage, this._clipGroup, this._clipRect);
    framingNode.hide();

    console.log(
      '[CropScene.show] _clipRect initial relative x/y:',
      { x: this._clipRect.x(), y: this._clipRect.y() },
      'w/h:',
      { w: this._clipRect.width(), h: this._clipRect.height() }
    );
    console.log('[CropScene.show] _clipRect initial visual pos (abs):', this._clipRect.absolutePosition());

    this._handleCrop();
    this._cropTransformer.nodes([this._clipRect]);
    this._scaleTransformer.nodes([]);

    // Ensure visual order: mask < origin < clip stuff < transformer handles
    this._mask.zIndex(2);
    this._originImage?.zIndex(3);
    this._cropGroup?.zIndex(4);
    this._scaleTransformer.zIndex(5);
    this._cropTransformer.zIndex(6);
    // Extra safety: move transformer to very top every time in case of later adds
    this._cropTransformer.moveToTop();

    this._registerEvents();
    this._registerPointerCancel();
    this._bindStageViewportHandlers();

    // register Enter key to finish crop
    this._enterHandler = (e: KeyboardEvent) => {
      if (e.key === 'Enter') {
        this._handleCropEnd();
        this._sceneManager.goto(BaseScene as any);
      }
    };
    window.addEventListener('keydown', this._enterHandler);

    return this;
  }

  public hide(): this {
    this._unbindEvents();
    this._unregisterPointerCancel();
    this.visible(false);

    // Cleanup.
    this._originImage?.destroy();
    this._cropGroup?.destroy();

    // Restore original image visibility
    const base = this._sceneManager.getScene(BaseScene) as BaseScene;
    const sel = base.getSelection() as unknown as Konva.Image;
    sel?.show();

    // cleanup key handler
    if (this._enterHandler) window.removeEventListener('keydown', this._enterHandler);

    this._unbindStageViewportHandlers();

    // Re-enable stage dragging when leaving crop mode
    this._stage.draggable(true);

    return this;
  }

  // ---------------------------------------------------------------------------
  // Internal handlers
  // ---------------------------------------------------------------------------

  private _handlePointerDown(e: Konva.KonvaEventObject<PointerEvent>) {
    if (e.target.hasName('mask')) {
      this._handleCropEnd();
      this._sceneManager.goto(BaseScene as any);
    }
  }

  private _handleScale() {
    // Re-compute clipping while user scales origin image.
    this._syncClipAfterTransform();
  }

  private _handleDrag() {
    this._syncClipAfterTransform();
  }

  private _handleCrop() {
    if (!this._clipRect || !this._originImage) return;

    console.log('[CropScene] _handleCrop fired');

    let x = this._clipRect.x();
    let y = this._clipRect.y();
    let width = this._clipRect.width() * this._clipRect.scaleX();
    let height = this._clipRect.height() * this._clipRect.scaleY();

    // Clamp within origin image bounds
    if (x < 0) {
      width += x;
      x = 0;
    }
    if (x + width > this._originImage.width()) {
      width = this._originImage.width() - x;
    }
    if (y < 0) {
      height += y;
      y = 0;
    }
    if (y + height > this._originImage.height()) {
      height = this._originImage.height() - y;
    }

    // Apply updated rect and reset scale to 1
    this._clipRect.setAttrs({ x, y, width, height, scaleX: 1, scaleY: 1 });

    // update group clipping
    this._clipGroup.clip({ x, y, width, height });
  }

  private _syncClipAfterTransform() {
    if (!this._clipRect || !this._originImage) return;

    let x = this._clipRect.x();
    let y = this._clipRect.y();
    let width = this._clipRect.width();
    let height = this._clipRect.height();

    const originWidth = this._originImage.width();
    const originHeight = this._originImage.height();

    x = Math.max(0, Math.min(x, originWidth - width));
    y = Math.max(0, Math.min(y, originHeight - height));
    if (x + width > originWidth) width = originWidth - x;
    if (y + height > originHeight) height = originHeight - y;

    this._clipRect.setAttrs({ x, y, width, height, scaleX: 1, scaleY: 1 });
    this._clipGroup.clip({ x, y, width, height });
  }

  private _handleCropEnd() {
    const base = this._sceneManager.getScene(BaseScene) as BaseScene;
    // const selectedImage = base.getSelection() as Konva.Image; // Old way
    // The framingNode from show() is the one whose attrs we need to update.
    // We need to retrieve it again or store it on `this` if it's complex to re-fetch.
    // For now, let's assume `this._framingNodePersistent` was set in show(). This needs proper implementation.

    let framingNodePersistent: Konva.Image | Konva.Group | null = null;
    // Attempt to re-fetch based on ID stored in _originImage if we built it that way, or from selection.
    // This part is a bit tricky as framingNode isn't a class member currently.
    // For now, we rely on what BaseScene thinks is selected, which should be the framingNode.
    const currentSelectionInBase = base.getSelection();
    if (
      currentSelectionInBase &&
      (currentSelectionInBase instanceof Konva.Image || currentSelectionInBase instanceof Konva.Group)
    ) {
      framingNodePersistent = currentSelectionInBase;
    } else {
      console.error(
        '[CropScene._handleCropEnd] Could not reliably get the original framing node from BaseScene. Aborting crop finalization.'
      );
      return;
    }

    let baseImageNodeToUpdate: Konva.Image;
    if (framingNodePersistent instanceof Konva.Group) {
      const inner = framingNodePersistent.findOne('Image') as Konva.Image | undefined;
      if (!inner) {
        console.error('[CropScene._handleCropEnd] Framing node Group has no inner image to update crop for.');
        return;
      }
      baseImageNodeToUpdate = inner;
    } else {
      baseImageNodeToUpdate = framingNodePersistent as Konva.Image;
    }

    const imgHtmlElement = baseImageNodeToUpdate.image() as HTMLImageElement;
    if (!imgHtmlElement) {
      console.error('[CropScene._handleCropEnd] HTML Image element not found on node to update.');
      return;
    }

    const ratio = this._originImage.width() / imgHtmlElement.naturalWidth; // Corrected ratio based on naturalWidth
    if (!isFinite(ratio) || ratio === 0) {
      console.error('[CropScene._handleCropEnd] Invalid ratio for final crop calculation.');
      return;
    }

    const cropX = this._clipRect.x() / ratio;
    const cropY = this._clipRect.y() / ratio;
    // Visual width/height of the crop rect becomes the new visual width/height of the framingNode
    const newVisualWidth = this._clipRect.width();
    const newVisualHeight = this._clipRect.height();

    const cropWidth = newVisualWidth / ratio;
    const cropHeight = newVisualHeight / ratio;

    console.log('[CropScene._handleCropEnd] Finalizing crop with values:', {
      newVisualWidth,
      newVisualHeight,
      cropX,
      cropY,
      cropWidth,
      cropHeight,
      clipRectX: this._clipRect.x(),
      clipRectY: this._clipRect.y(),
      clipRectW: this._clipRect.width(),
      clipRectH: this._clipRect.height(),
      ratio,
      originImageW: this._originImage.width(),
      originImageH: this._originImage.height(),
      htmlNaturalW: imgHtmlElement.naturalWidth,
      htmlNaturalH: imgHtmlElement.naturalHeight,
    });

    // Update attributes of the framing node (Group or KonvaImage)
    framingNodePersistent.setAttrs({
      width: newVisualWidth,
      height: newVisualHeight,
      // x and y for framingNode are set by absolutePosition below
    });

    // Update crop attributes on the base image node (the one that holds the actual image data and crop)
    baseImageNodeToUpdate.setAttrs({
      cropX,
      cropY,
      cropWidth,
      cropHeight,
      // width/height of baseImageNode might not need to change if it represents the full image dimensions
      // or if its width/height are derived from crop. This depends on KonvaImage internal logic.
      // For URLImage, the inner baseImageNode.width/height should ideally be consistent with its HTML source or be managed by URLImage.tsx.
      // For now, only updating crop. Width/height of baseImageNode are tricky if they are not just naturalWidth/Height.
    });

    const clipRectAbsPos = this._clipRect.absolutePosition();
    framingNodePersistent.absolutePosition(clipRectAbsPos);

    console.log(
      '[CropScene._handleCropEnd] Updated framingNode',
      framingNodePersistent.id(),
      'absPos:',
      framingNodePersistent.absolutePosition(),
      'size:',
      framingNodePersistent.size()
    );
    console.log(
      '[CropScene._handleCropEnd] Updated baseImageNode',
      baseImageNodeToUpdate.id(),
      'crop:',
      baseImageNodeToUpdate.crop()
    );

    window.dispatchEvent(
      new CustomEvent('canvas:imageCropped', {
        detail: {
          id: framingNodePersistent.id(), // ID of the framing node (Group or KonvaImage)
          x: framingNodePersistent.x(),
          y: framingNodePersistent.y(),
          width: newVisualWidth,
          height: newVisualHeight,
          // Crop details are from the baseImageNode that actually holds the crop info
          cropX: baseImageNodeToUpdate.cropX(),
          cropY: baseImageNodeToUpdate.cropY(),
          cropWidth: baseImageNodeToUpdate.cropWidth(),
          cropHeight: baseImageNodeToUpdate.cropHeight(),
        },
      })
    );
  }

  // Handle touch-interrupts (iOS home gesture etc.)
  private _registerPointerCancel() {
    this._stage.on('pointercancel', () => {
      console.log('[CropScene] pointercancel – aborting crop');
      this._sceneManager.goto(BaseScene as any);
    });
  }

  private _unregisterPointerCancel() {
    this._stage.off('pointercancel');
  }

  // ────────────────────────────────────────────────────────────────────────────
  // Viewport-scale helpers so the grey mask always covers visible canvas
  // ────────────────────────────────────────────────────────────────────────────

  private _bindStageViewportHandlers() {
    const syncMask = () => {
      const scaleX = this._stage.scaleX() || 1;
      const scaleY = this._stage.scaleY() || 1;
      const newW = this._stage.width() / scaleX;
      const newH = this._stage.height() / scaleY;

      // The stage may be panned (x/y != 0).  Convert viewport origin back into
      // scene coordinates so the mask always fills what the user sees.
      const newX = -this._stage.x() / scaleX;
      const newY = -this._stage.y() / scaleY;

      console.log('[CropScene] syncMask', {
        scaleX,
        scaleY,
        stagePos: { x: this._stage.x(), y: this._stage.y() },
        newW,
        newH,
        newX,
        newY,
      });

      this._mask.setAttrs({ x: newX, y: newY, width: newW, height: newH });
    };
    syncMask();
    this._stage.on('scaleXChange scaleYChange resize dragmove xChange yChange positionChange', syncMask);
    // store for unbind
    (this as any)._syncMask = syncMask;
  }

  private _unbindStageViewportHandlers() {
    const fn = (this as any)._syncMask;
    if (fn) {
      this._stage.off('scaleXChange', fn);
      this._stage.off('scaleYChange', fn);
      this._stage.off('resize', fn);
      this._stage.off('dragmove', fn);
      this._stage.off('xChange', fn);
      this._stage.off('yChange', fn);
      this._stage.off('positionChange', fn);
    }
  }
}
