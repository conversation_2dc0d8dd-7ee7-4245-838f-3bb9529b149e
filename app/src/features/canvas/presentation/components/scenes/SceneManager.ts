/**
 * 🎭 Scene Manager - MODULAR VERSION
 * @description Simple scene manager that can switch between different Konva.Group-based scenes
 * @responsibility Scene switching, lifecycle management, lazy instantiation
 * @ai_context Modular scene management for canvas operations like cropping
 *
 * A scene is any class that extends Konva.Group and exposes:
 *   static sceneId: string;
 *   show(): void;
 *   hide(): void;
 */

import Konva from 'konva';
export default class SceneManager extends Konva.Group {
  private _stage: Konva.Stage;
  private _currentScene: (Konva.Group & { show: (data?: any) => void; hide: () => void }) | null = null;

  constructor(stage: Konva.Stage) {
    super();
    this._stage = stage;
  }

  /**
   * Returns an existing instance of a scene that matches SceneCtor.
   */
  public getScene<T extends Konva.Group>(SceneCtor: new (stage: Konva.Stage, mgr: SceneManager) => T): T {
    let scene = this.children.find((c) => (c as any).constructor.sceneId === (SceneCtor as any).sceneId) as
      | T
      | undefined;
    if (!scene) {
      // Lazy instantiate to avoid race condition where consumer asks before goto()
      scene = new SceneCtor(this._stage, this);
      this.add(scene);
    }
    return scene;
  }

  /** Check if a scene instance already exists. */
  public hasScene(SceneCtor: any): boolean {
    const ctor: any = SceneCtor;
    return this.children.findIndex((c) => (c as any).constructor.sceneId === ctor.sceneId) !== -1;
  }

  /**
   * Switch to a given scene class.
   */
  public goto<T extends Konva.Group>(SceneCtor: new (stage: Konva.Stage, mgr: SceneManager) => T, data?: any): void {
    const ctor: any = SceneCtor;
    if (this._currentScene && (this._currentScene.constructor as any).sceneId === ctor.sceneId) {
      return; // Already in that scene.
    }

    let sceneInstance: T;
    if (this.hasScene(SceneCtor)) {
      sceneInstance = this.getScene(SceneCtor);
    } else {
      sceneInstance = new SceneCtor(this._stage, this);
      this.add(sceneInstance);
    }

    if (this._currentScene) {
      (this._currentScene as any).hide();
    }
    this._currentScene = sceneInstance as any;
    (sceneInstance as any).show(data);
    console.log(`[SceneManager] Switched to scene: ${ctor.sceneId}`, data ? 'with data' : '');
  }
}
