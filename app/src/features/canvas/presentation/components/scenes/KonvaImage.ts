/**
 * 🎭 Konva Image - MODULAR VERSION
 * @description Enhanced Konva Image class with scene management features
 * @responsibility Image node with scene-aware capabilities
 * @ai_context Extended image class for scene-based canvas operations
 */

import Konva from 'konva';

/**
 * KonvaImage wraps Konva.Image and tracks the last size/crop so that cropping
 * logic can keep the visible portion aligned while the user drags anchors.
 */
export default class KonvaImage extends Konva.Image {
  private _lastSize?: { width: number; height: number };
  private _lastCrop?: { x: number; y: number; width: number; height: number };

  constructor(config: Konva.ImageConfig & { image: HTMLImageElement }) {
    super(config);
    this.draggable(true);

    // Initialise crop to full image if not provided.
    this.crop({
      x: 0,
      y: 0,
      width: config.image.width,
      height: config.image.height,
    });
    this._lastCrop = this.crop();
  }

  /** Called before a transform starts so we remember starting values. */
  public handleTransformStart(): void {
    this._lastSize = this.size();
    this._lastCrop = this.crop();
  }

  /** Called repeatedly while transforming so we keep the crop in bounds. */
  public handleTransform(activeAnchor: string): void {
    // Commit the new width/height into attrs and reset scale.
    this.setAttrs({
      scaleX: 1,
      scaleY: 1,
      width: this.width() * this.scaleX(),
      height: this.height() * this.scaleY(),
    });

    if (!this._lastSize || !this._lastCrop) return;

    this._applyCropKeepingRatio(this.size(), this._lastSize, this._lastCrop, activeAnchor);
  }

  /** Called when transform ends – clear temp state. */
  public handleTransformEnd(): void {
    this._lastSize = undefined;
    this._lastCrop = undefined;
  }

  // ---------------------------------------------------------------------------
  // Internal helpers
  // ---------------------------------------------------------------------------

  private _applyCropKeepingRatio(
    curSize: { width: number; height: number },
    lastSize: { width: number; height: number },
    lastCrop: { x: number; y: number; width: number; height: number },
    anchor: string
  ): void {
    let ratio: number | undefined;
    let newCropWidth = lastCrop.width;
    let newCropHeight = lastCrop.height;
    const imgEl = this.image() as HTMLImageElement;

    if (anchor === 'middle-left' || anchor === 'middle-right') {
      if (curSize.width < lastSize.width) {
        ratio = curSize.width / lastSize.width;
        newCropWidth = lastCrop.width * ratio;
        this.cropWidth(newCropWidth);
        if (anchor === 'middle-left') {
          this.cropX(lastCrop.x + lastCrop.width - newCropWidth);
        }
      } else {
        ratio = lastCrop.height / lastSize.height;
        newCropWidth = curSize.width * ratio;
        if (newCropWidth > imgEl.width - lastCrop.x) {
          ratio = (imgEl.width - lastCrop.x) / curSize.width;
          newCropHeight = curSize.height * ratio;
          this.cropHeight(newCropHeight);
        } else {
          this.cropWidth(newCropWidth);
        }
      }
    } else if (anchor === 'top-center' || anchor === 'bottom-center') {
      if (curSize.height < lastSize.height) {
        ratio = curSize.height / lastSize.height;
        newCropHeight = lastCrop.height * ratio;
        this.cropHeight(newCropHeight);
        if (anchor === 'top-center') {
          this.cropY(lastCrop.y + lastCrop.height - newCropHeight);
        }
      } else {
        ratio = lastCrop.width / lastSize.width;
        newCropHeight = curSize.height * ratio;
        if (newCropHeight > imgEl.height - lastCrop.y) {
          ratio = (imgEl.height - lastCrop.y) / curSize.height;
          newCropWidth = curSize.width * ratio;
          this.cropWidth(newCropWidth);
        } else {
          this.cropHeight(newCropHeight);
        }
      }
    }
  }
}
