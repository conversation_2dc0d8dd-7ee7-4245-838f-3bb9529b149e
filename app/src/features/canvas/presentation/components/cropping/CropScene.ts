/**
 * ✂️ Crop Scene - MODULAR VERSION
 * @description Advanced image cropping scene with visual feedback
 * @responsibility Image cropping, crop transformations, visual clipping
 * @ai_context Modular crop scene without SceneManager dependency
 */

import Konva from 'konva';

export default class CropScene extends Konva.Group {
  private _cropTransformer: Konva.Transformer;
  private _mask: Konva.Rect;
  private _originImage!: Konva.Image;
  private _cropGroup!: Konva.Group;
  private _clipGroup!: Konva.Group;
  private _clipImage!: Konva.Image;
  private _clipRect!: Konva.Rect;
  private _stage: Konva.Stage;
  private _enterHandler?: (e: KeyboardEvent) => void;
  private _originalFramingNode?: Konva.Image | Konva.Group;

  constructor(stage: Konva.Stage) {
    super();
    this._stage = stage;

    // 🎨 Create crop transformer with brand colors
    this._cropTransformer = new Konva.Transformer({
      flipEnabled: false,
      keepRatio: false,
      rotateEnabled: false,
      enabledAnchors: ['top-left', 'top-right', 'bottom-left', 'bottom-right'],
      borderStroke: '#9EA581',
      borderStrokeWidth: 2,
      anchorStroke: '#9EA581',
      anchorFill: '#9EA581',
      anchorStrokeWidth: 1,
    });

    this.add(this._cropTransformer);

    // 🎭 Create semi-transparent mask (covers entire viewport)
    this._mask = new Konva.Rect({
      x: -this._stage.x() / this._stage.scaleX(),
      y: -this._stage.y() / this._stage.scaleY(),
      width: this._stage.width() / this._stage.scaleX(),
      height: this._stage.height() / this._stage.scaleY(),
      fill: 'rgba(0,0,0,0.5)',
      listening: true, // Allow clicking to exit crop mode
    });
    this._mask.addName('mask');
    this.add(this._mask);

    // 🔗 Bind event handlers
    this._handlePointerDown = this._handlePointerDown.bind(this);
    this._handleCrop = this._handleCrop.bind(this);
    this._handleDrag = this._handleDrag.bind(this);
  }

  // 📝 Register event listeners
  private _registerEvents() {
    this._stage.on('pointerdown', this._handlePointerDown);
    this._cropTransformer.on('dragmove', (e) => {
      e.evt.stopPropagation();
      this._handleDrag();
    });
    this._cropTransformer.on('transform', (e) => {
      e.evt.stopPropagation();
      this._handleCrop();
    });
  }

  // 🧹 Cleanup event listeners
  private _unbindEvents() {
    this._stage.off('pointerdown', this._handlePointerDown);
    this._cropTransformer.off('dragmove');
    this._cropTransformer.off('transform');
  }

  // 🎭 Start crop scene with the selected node
  public startCrop(nodeForCrop: Konva.Image | Konva.Group): this {
    this.visible(true);
    this._originalFramingNode = nodeForCrop;

    // 🎯 Position crop scene at stage origin to ensure mask covers entire stage
    this.position({ x: 0, y: 0 });
    this.scale({ x: 1, y: 1 });

    if (!nodeForCrop || !nodeForCrop.id()) {
      console.error('[CropScene] Invalid node for cropping:', nodeForCrop);
      this._exitCrop();
      return this;
    }

    // 🖼️ Determine base image and framing node
    let baseImageNode: Konva.Image;
    const framingNode: Konva.Image | Konva.Group = nodeForCrop;

    if (nodeForCrop instanceof Konva.Group) {
      // URLImage group - find inner image
      const innerImage = nodeForCrop.findOne('Image') as Konva.Image | undefined;
      if (!innerImage || !innerImage.image()) {
        console.error('[CropScene] URLImage group has no valid inner image:', nodeForCrop.id());
        this._exitCrop();
        return this;
      }
      baseImageNode = innerImage;
      console.log(`[CropScene] Cropping URLImage group: ${framingNode.id()}`);
    } else if (nodeForCrop instanceof Konva.Image) {
      // Plain image
      if (!nodeForCrop.image()) {
        console.error('[CropScene] Image has no image data:', nodeForCrop.id());
        this._exitCrop();
        return this;
      }
      baseImageNode = nodeForCrop;
      console.log(`[CropScene] Cropping plain image: ${framingNode.id()}`);
    } else {
      console.error('[CropScene] Unexpected node type:', nodeForCrop);
      this._exitCrop();
      return this;
    }

    const htmlImageElement = baseImageNode.image() as HTMLImageElement;
    if (!htmlImageElement || !htmlImageElement.naturalWidth) {
      console.error('[CropScene] Invalid HTML image element');
      this._exitCrop();
      return this;
    }

    // 📐 Calculate dimensions and positioning
    const selImgAbsPos = framingNode.absolutePosition();
    const currentVisualWidth = framingNode.width() * (framingNode.scaleX() || 1);
    const currentVisualHeight = framingNode.height() * (framingNode.scaleY() || 1);

    // 🔄 Reset scale to work with absolute dimensions
    framingNode.scale({ x: 1, y: 1 });

    // 📏 Calculate scale ratio with fallback
    let cropWidth = baseImageNode.cropWidth();
    let cropHeight = baseImageNode.cropHeight();

    // 🔧 Fallback: if crop dimensions are not set, use image natural dimensions
    if (!cropWidth || !cropHeight || cropWidth <= 0 || cropHeight <= 0) {
      console.log('[CropScene] No crop dimensions set, using natural image dimensions');
      cropWidth = htmlImageElement.naturalWidth;
      cropHeight = htmlImageElement.naturalHeight;

      // Set initial crop to full image
      baseImageNode.crop({
        x: 0,
        y: 0,
        width: cropWidth,
        height: cropHeight,
      });
    }

    const ratio = currentVisualWidth / cropWidth;
    if (!isFinite(ratio) || ratio <= 0) {
      console.error('[CropScene] Invalid scale ratio:', ratio, {
        currentVisualWidth,
        cropWidth,
        cropHeight,
        naturalWidth: htmlImageElement.naturalWidth,
        naturalHeight: htmlImageElement.naturalHeight,
      });
      this._exitCrop();
      return this;
    }

    console.log('[CropScene] Scale ratio calculated:', ratio);

    // 🎭 Disable stage dragging during crop
    this._stage.draggable(false);

    // 🖼️ Create origin image (full uncropped image)
    const originImageWidth = ratio * htmlImageElement.naturalWidth;
    const originImageHeight = ratio * htmlImageElement.naturalHeight;
    const originImageX = -(baseImageNode.cropX() || 0) * ratio;
    const originImageY = -(baseImageNode.cropY() || 0) * ratio;

    this._originImage = new Konva.Image({
      image: htmlImageElement,
      x: originImageX,
      y: originImageY,
      width: originImageWidth,
      height: originImageHeight,
      draggable: false,
      name: 'cropSceneOriginImage',
    });

    // 🗂️ Create crop group
    this._cropGroup = new Konva.Group({
      rotation: framingNode.rotation(),
      draggable: false,
      name: 'cropSceneCropGroup',
    });
    this.add(this._cropGroup);
    this._cropGroup.absolutePosition(selImgAbsPos);

    // ✂️ Create clip rectangle (user-manipulable crop area)
    const initialClipRectX = (baseImageNode.cropX() || 0) * ratio;
    const initialClipRectY = (baseImageNode.cropY() || 0) * ratio;

    this._clipRect = new Konva.Rect({
      x: initialClipRectX,
      y: initialClipRectY,
      width: currentVisualWidth,
      height: currentVisualHeight,
      draggable: true,
      name: 'clipRect',
    });

    // 🖼️ Create clipped image preview
    this._clipGroup = new Konva.Group({
      x: 0,
      y: 0,
      name: 'clipGroup',
    });
    this._clipImage = this._originImage.clone({
      x: originImageX,
      y: originImageY,
      rotation: 0,
      name: 'clipImageInClipGroup',
    });
    this._clipGroup.add(this._clipImage);

    // 🏗️ Assemble the crop scene
    this._cropGroup.add(this._originImage, this._clipGroup, this._clipRect);
    framingNode.hide();

    // 🎯 Set up transformer
    this._handleCrop();
    this._cropTransformer.nodes([this._clipRect]);

    // 📚 Set z-index order (use moveToTop instead of specific indices)
    this._mask.moveToTop();
    this._originImage?.moveToTop();
    this._cropGroup?.moveToTop();
    this._cropTransformer.moveToTop();

    // 📝 Register events
    this._registerEvents();

    // ⌨️ Register Enter key to finish crop
    this._enterHandler = (e: KeyboardEvent) => {
      if (e.key === 'Enter') {
        this._handleCropEnd();
      }
    };
    window.addEventListener('keydown', this._enterHandler);

    console.log(`[CropScene] Crop mode started for: ${nodeForCrop.id()}`);
    return this;
  }

  // 🚪 Hide crop scene
  public hide(): this {
    this._unbindEvents();
    this.visible(false);

    // 🧹 Cleanup
    this._originImage?.destroy();
    this._cropGroup?.destroy();

    // 👁️ Restore original image visibility
    this._originalFramingNode?.show();

    // ⌨️ Cleanup key handler
    if (this._enterHandler) {
      window.removeEventListener('keydown', this._enterHandler);
    }

    // 🎭 Re-enable stage dragging
    this._stage.draggable(true);

    console.log('[CropScene] Crop mode hidden');
    return this;
  }

  // 🖱️ Handle pointer down events
  private _handlePointerDown(e: Konva.KonvaEventObject<PointerEvent>) {
    if (e.target.hasName('mask')) {
      this._handleCropEnd();
    }
  }

  // 🔄 Handle drag events
  private _handleDrag() {
    this._syncClipAfterTransform();
  }

  // ✂️ Handle crop transformation
  private _handleCrop() {
    if (!this._clipRect || !this._originImage) return;

    let x = this._clipRect.x();
    let y = this._clipRect.y();
    let width = this._clipRect.width() * this._clipRect.scaleX();
    let height = this._clipRect.height() * this._clipRect.scaleY();

    // 📏 Clamp within origin image bounds
    if (x < 0) {
      width += x;
      x = 0;
    }
    if (x + width > this._originImage.width()) {
      width = this._originImage.width() - x;
    }
    if (y < 0) {
      height += y;
      y = 0;
    }
    if (y + height > this._originImage.height()) {
      height = this._originImage.height() - y;
    }

    // 🔄 Apply updated rect and reset scale
    this._clipRect.setAttrs({ x, y, width, height, scaleX: 1, scaleY: 1 });

    // 🖼️ Update group clipping
    this._clipGroup.clip({ x, y, width, height });
  }

  // 🔄 Sync clip after transform
  private _syncClipAfterTransform() {
    if (!this._clipRect || !this._originImage) return;

    let x = this._clipRect.x();
    let y = this._clipRect.y();
    let width = this._clipRect.width();
    let height = this._clipRect.height();

    const originWidth = this._originImage.width();
    const originHeight = this._originImage.height();

    // 📏 Constrain to bounds
    x = Math.max(0, Math.min(x, originWidth - width));
    y = Math.max(0, Math.min(y, originHeight - height));
    if (x + width > originWidth) width = originWidth - x;
    if (y + height > originHeight) height = originHeight - y;

    this._clipRect.setAttrs({ x, y, width, height, scaleX: 1, scaleY: 1 });
    this._clipGroup.clip({ x, y, width, height });
  }

  // ✅ Handle crop completion
  private _handleCropEnd() {
    if (!this._originalFramingNode || !this._clipRect || !this._originImage) {
      console.error('[CropScene] Missing required nodes for crop completion');
      this._exitCrop();
      return;
    }

    const framingNode = this._originalFramingNode;

    // 🖼️ Get base image node for crop data
    let baseImageNode: Konva.Image;
    if (framingNode instanceof Konva.Group) {
      const inner = framingNode.findOne('Image') as Konva.Image | undefined;
      if (!inner) {
        console.error('[CropScene] No inner image found for crop completion');
        this._exitCrop();
        return;
      }
      baseImageNode = inner;
    } else {
      baseImageNode = framingNode as Konva.Image;
    }

    const htmlImageElement = baseImageNode.image() as HTMLImageElement;
    if (!htmlImageElement) {
      console.error('[CropScene] No HTML image element for crop completion');
      this._exitCrop();
      return;
    }

    // 📐 Calculate final crop values
    const ratio = this._originImage.width() / htmlImageElement.naturalWidth;
    if (!isFinite(ratio) || ratio === 0) {
      console.error('[CropScene] Invalid ratio for crop completion');
      this._exitCrop();
      return;
    }

    const cropX = this._clipRect.x() / ratio;
    const cropY = this._clipRect.y() / ratio;
    // 🎯 KEY FIX: Visual width/height of the crop rect becomes the new visual width/height of the framingNode
    const newVisualWidth = this._clipRect.width();
    const newVisualHeight = this._clipRect.height();
    // 🎯 Crop dimensions in image coordinates
    const cropWidth = newVisualWidth / ratio;
    const cropHeight = newVisualHeight / ratio;

    console.log('[CropScene] Finalizing crop:', {
      cropX,
      cropY,
      cropWidth,
      cropHeight,
      newVisualWidth,
      newVisualHeight,
    });

    // 📐 Update framing node dimensions (Group or KonvaImage)
    framingNode.setAttrs({
      width: newVisualWidth,
      height: newVisualHeight,
      // x and y for framingNode are set by absolutePosition below
    });

    // 🔄 Update crop attributes on the base image node (the one that holds the actual image data and crop)
    baseImageNode.setAttrs({
      cropX,
      cropY,
      cropWidth,
      cropHeight,
      // 🎯 KEY FIX: For URLImage groups, update the inner image dimensions to match the new visual size
      width: newVisualWidth,
      height: newVisualHeight,
    });

    // 📍 Set position using clip rect absolute position (KEY FIX!)
    const clipRectAbsPos = this._clipRect.absolutePosition();
    framingNode.absolutePosition(clipRectAbsPos);

    console.log(
      '[CropScene] Updated framingNode',
      framingNode.id(),
      'absPos:',
      framingNode.absolutePosition(),
      'size:',
      framingNode.size()
    );
    console.log('[CropScene] Updated baseImageNode', baseImageNode.id(), 'crop:', baseImageNode.crop());

    // 🎉 Dispatch crop completion event
    window.dispatchEvent(
      new CustomEvent('canvas:imageCropped', {
        detail: {
          id: framingNode.id(),
          x: framingNode.x(),
          y: framingNode.y(),
          width: newVisualWidth,
          height: newVisualHeight,
          cropX: baseImageNode.cropX(),
          cropY: baseImageNode.cropY(),
          cropWidth: baseImageNode.cropWidth(),
          cropHeight: baseImageNode.cropHeight(),
        },
      })
    );

    console.log(`[CropScene] Crop completed for: ${framingNode.id()}`);
    this._exitCrop();
  }

  // 🚪 Exit crop mode
  private _exitCrop() {
    this.hide();
    this.destroy();
  }
}
