/**
 * ✂️ Crop Handler Component - MODULAR VERSION
 * @description Handles crop initiation events and manages crop mode
 * @responsibility Crop event detection, crop mode management, crop completion
 * @ai_context Modular replacement for BaseScene double-click crop detection
 */

import React, { useEffect, useRef } from 'react';
import Konva from 'konva';
import { useCanvasToolbarStore } from '../../../domain/state';
import { ElementService } from '../../../domain/services';
import { useShallow } from 'zustand/react/shallow';
import { CanvasElement } from '../../hooks/useCanvasSync';

/**
 * ✂️ CropHandler Props
 */
interface CropHandlerProps {
  stageRef: React.RefObject<Konva.Stage>;
  updateElement: (elementId: string, changes: Partial<CanvasElement>, throttle?: boolean) => void;
  canvasState: any; // Canvas state to get current element properties
}

/**
 * ✂️ CropHandler Component
 * Handles crop initiation and manages crop mode state
 */
const CropHandler: React.FC<CropHandlerProps> = ({ stageRef, updateElement, canvasState }) => {
  const cropSceneRef = useRef<any>(null);

  // 🧠 Get crop mode state from domain store
  const { isCropMode, setIsCropMode } = useCanvasToolbarStore(
    useShallow((state) => ({
      isCropMode: state.isCropMode,
      setIsCropMode: state.setIsCropMode,
    }))
  );

  // 🔄 Listen for crop events - FOLLOWING useEffect RULES
  // ✅ This IS appropriate for useEffect: synchronizing with external system (custom events)
  useEffect(() => {
    // 🎭 Handle crop start events (dispatched by SelectionManager)
    const handleStartCrop = (event: CustomEvent) => {
      const { nodeId, node } = event.detail;
      console.log(`[CropHandler] Starting crop for node: ${nodeId}`);

      if (!node || !node.id()) {
        console.error('[CropHandler] Invalid node for cropping:', node);
        return;
      }

      // 🎯 Validate that the node is croppable (has an image)
      let nodeForCrop: Konva.Image | Konva.Group | null = null;

      if (node instanceof Konva.Group && node.findOne('Image')) {
        // URLImage group - pass the group
        nodeForCrop = node;
        console.log(`[CropHandler] Cropping URLImage group: ${nodeId}`);
      } else if (node instanceof Konva.Image) {
        // Plain image - pass the image
        nodeForCrop = node;
        console.log(`[CropHandler] Cropping plain image: ${nodeId}`);
      } else {
        console.error('[CropHandler] Node is not croppable (no image found):', node);
        return;
      }

      // 🎭 Start crop mode
      startCropMode(nodeForCrop);
    };

    // 🎭 Handle crop completion events
    const handleImageCropped = (event: CustomEvent) => {
      const { id, x, y, width, height, cropX, cropY, cropWidth, cropHeight } = event.detail;
      console.log(`[CropHandler] Crop completed for ${id}:`, {
        position: { x, y },
        size: { width, height },
        crop: { cropX, cropY, cropWidth, cropHeight },
      });

      // 🔄 Update domain state with new dimensions (KEY FIX!)
      // This ensures the modular element state reflects the crop changes
      console.log(`[CropHandler] Updating domain state for ${id} with:`, {
        x,
        y,
        width,
        height,
        cropX,
        cropY,
        cropWidth,
        cropHeight,
      });

      ElementService.images.update(id, {
        x,
        y,
        width,
        height,
        cropX,
        cropY,
        cropWidth,
        cropHeight,
      });

      // 🔄 ALSO update canvas state so URLImage gets the new dimensions
      console.log(`[CropHandler] Updating canvas state for ${id} with new properties`);
      console.log(`[CropHandler] Canvas state elements:`, Object.keys(canvasState.elements));
      console.log(`[CropHandler] Looking for element:`, id);
      console.log(`[CropHandler] Full canvas state:`, canvasState);

      // Get current element to preserve existing properties
      const currentElement = canvasState.elements[id];
      if (currentElement) {
        console.log(`[CropHandler] Found element in canvas state:`, currentElement);

        // 1. Update Cloudflare Workers WebSocket (real-time collaboration)
        updateElement(id, {
          position: { x, y },
          properties: {
            ...currentElement.properties,
            size: {
              width,
              height,
            },
            cropX,
            cropY,
            cropWidth,
            cropHeight,
          },
        });

        // 2. ✅ UNIFIED SYSTEM: Cloudflare Workers handles persistence automatically
        console.log(`[CropHandler] Crop update sent to Cloudflare Workers via updateElement`);
        // Note: No additional persistence needed - updateElement handles everything!
      } else {
        console.warn(`[CropHandler] Element ${id} not found in canvas state`);
        console.warn(`[CropHandler] Available elements:`, Object.keys(canvasState.elements));
      }

      // 🔄 Exit crop mode
      exitCropMode();
    };

    // 📝 Register event listeners
    window.addEventListener('canvas:startCrop', handleStartCrop as EventListener);
    window.addEventListener('canvas:imageCropped', handleImageCropped as EventListener);

    // Removed spammy setup log

    // 🧹 Cleanup
    return () => {
      window.removeEventListener('canvas:startCrop', handleStartCrop as EventListener);
      window.removeEventListener('canvas:imageCropped', handleImageCropped as EventListener);
      // Removed spammy cleanup log
    };
  }, []);

  // 🎭 Start crop mode with the selected node
  const startCropMode = async (nodeForCrop: Konva.Image | Konva.Group) => {
    const stage = stageRef.current;
    if (!stage) {
      console.error('[CropHandler] Stage not available for cropping');
      return;
    }

    try {
      console.log('[CropHandler] Entering crop mode');

      // 🎯 Update domain state to crop mode
      setIsCropMode(true);

      // 🎭 Dynamically import and initialize CropScene
      const { default: CropScene } = await import('./CropScene');

      // 🏗️ Create crop scene instance
      if (!cropSceneRef.current) {
        cropSceneRef.current = new CropScene(stage);

        // 📝 Add crop scene to the main layer (not directly to stage)
        const mainLayer = stage.getLayers()[0]; // Get the first layer
        if (mainLayer) {
          mainLayer.add(cropSceneRef.current);
          console.log('[CropHandler] Added crop scene to main layer');
        } else {
          console.error('[CropHandler] No main layer found to add crop scene');
          throw new Error('No main layer available for crop scene');
        }
      }

      // 🎯 Start crop scene with the node
      cropSceneRef.current.startCrop(nodeForCrop);

      console.log(`[CropHandler] Crop mode started for node: ${nodeForCrop.id()}`);
    } catch (error) {
      console.error('[CropHandler] Failed to start crop mode:', error);
      setIsCropMode(false);
    }
  };

  // 🔄 Exit crop mode
  const exitCropMode = () => {
    console.log('[CropHandler] Exiting crop mode');

    // 🎭 Hide and cleanup crop scene
    if (cropSceneRef.current) {
      cropSceneRef.current.hide();
      cropSceneRef.current.destroy();
      cropSceneRef.current = null;
    }

    // 🎯 Update domain state to normal mode
    setIsCropMode(false);

    console.log('[CropHandler] Crop mode exited');
  };

  // 🔄 Handle escape key to exit crop mode
  useEffect(() => {
    if (!isCropMode) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        console.log('[CropHandler] Escape pressed, exiting crop mode');
        exitCropMode();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isCropMode]);

  // This component doesn't render anything - it just manages crop events
  return null;
};

export default CropHandler;
