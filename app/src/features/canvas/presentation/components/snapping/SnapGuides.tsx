/**
 * 📐 Snap Guides Component - MODULAR VERSION
 * @description Handles visual snap guide rendering and snap calculations
 * @responsibility Visual snap lines, snap distance calculations, magnetic snapping
 * @ai_context Modular replacement for BaseScene snapping system
 */

import React, { useEffect, useRef } from 'react';
import Konva from 'konva';
import { Edge } from '../../../domain/types';

// Snapping constants (from old BaseScene)
const SNAP_THRESHOLD = 8;
const SNAP_LINE_COLOR = 'rgba(255, 0, 102, 0.8)';
const SNAP_LINE_WIDTH = 1;
const STICKY_THRESHOLD_OUTER = 5; // px – start showing magnetic guides
const STICKY_THRESHOLD_INNER = 1; // px – actually lock / create connection (auto-sticks)

/**
 * 📐 Snap Connection Interface
 */
interface SnapConnection {
  nodeId: string;
  targetNodeId: string;
  nodeEdge: Edge;
  targetEdge: Edge;
}

/**
 * 📐 Visual Bounds Interface
 */
interface VisualBounds {
  left: number;
  right: number;
  top: number;
  bottom: number;
  hCenter: number;
  vCenter: number;
  width: number;
  height: number;
}

/**
 * 📐 Snap Target Interface
 */
interface SnapTarget {
  nodeEdge: Edge;
  targetEdge: Edge;
  draggedVal: number;
  staticVal: number;
  newPos: number;
}

/**
 * 📐 SnapGuides Props
 */
interface SnapGuidesProps {
  stageRef: React.RefObject<Konva.Stage>;
}

/**
 * 📐 SnapGuides Component
 * Handles visual snap guides and snap calculations
 */
const SnapGuides: React.FC<SnapGuidesProps> = ({ stageRef }) => {
  const snapLineLayerRef = useRef<Konva.Layer | null>(null);
  const activeSnapConnectionsRef = useRef<SnapConnection[]>([]);
  const currentDraggedNodeRef = useRef<Konva.Node | null>(null);

  // 🔄 Set up snap line layer - FOLLOWING useEffect RULES
  // ✅ This IS appropriate for useEffect: synchronizing with external system (Konva layer)
  useEffect(() => {
    const stage = stageRef.current;
    if (!stage) return;

    // 🎨 Create snap line layer
    if (!snapLineLayerRef.current) {
      snapLineLayerRef.current = new Konva.Layer({
        listening: false,
        name: 'SnapLineLayer',
      });
      stage.add(snapLineLayerRef.current);
      snapLineLayerRef.current.moveToTop();
      // Removed spammy setup log
    }

    // 🧹 Cleanup
    return () => {
      if (snapLineLayerRef.current) {
        snapLineLayerRef.current.destroy();
        snapLineLayerRef.current = null;
        // Removed spammy cleanup log
      }
    };
  }, []);

  // 🔄 Listen for drag events - FOLLOWING useEffect RULES
  // ✅ This IS appropriate for useEffect: synchronizing with external system (custom events)
  useEffect(() => {
    // 🎭 Handle drag start events
    const handleDragStart = (event: CustomEvent) => {
      const { nodeId, node } = event.detail;
      console.log(`[SnapGuides] Drag started for node: ${nodeId}`);

      currentDraggedNodeRef.current = node;
      clearSnapLines();
    };

    // 🎭 Handle drag move events (show snap guides)
    const handleDragMove = (event: CustomEvent) => {
      const { nodeId, node } = event.detail;
      console.log(`[SnapGuides] Drag move event received for: ${nodeId}`);

      if (currentDraggedNodeRef.current === node) {
        console.log(`[SnapGuides] Processing snap guides for: ${nodeId}`);
        handleDragMoveWithSnapping(node);
      } else {
        console.log(`[SnapGuides] Ignoring drag move for: ${nodeId} (not current dragged node)`);
      }
    };

    // 🎭 Handle drag end events
    const handleDragEnd = (event: CustomEvent) => {
      const { nodeId } = event.detail;
      console.log(`[SnapGuides] Drag ended for node: ${nodeId}`);

      currentDraggedNodeRef.current = null;
      clearSnapLines();
    };

    // 📝 Register event listeners
    window.addEventListener('canvas:dragStart', handleDragStart as EventListener);
    window.addEventListener('canvas:dragMove', handleDragMove as EventListener);
    window.addEventListener('canvas:dragEnd', handleDragEnd as EventListener);

    // Removed spammy setup log

    // 🧹 Cleanup
    return () => {
      window.removeEventListener('canvas:dragStart', handleDragStart as EventListener);
      window.removeEventListener('canvas:dragMove', handleDragMove as EventListener);
      window.removeEventListener('canvas:dragEnd', handleDragEnd as EventListener);
      // Removed spammy cleanup log
    };
  }, []);

  // 🧹 Clear all snap lines
  const clearSnapLines = () => {
    if (snapLineLayerRef.current) {
      snapLineLayerRef.current.destroyChildren();
      snapLineLayerRef.current.batchDraw();
    }
  };

  // 🎨 Draw a snap line
  const drawSnapLine = (points: number[]) => {
    if (!snapLineLayerRef.current) return;

    const stage = stageRef.current;
    const scale = stage?.scaleX() || 1;

    const line = new Konva.Line({
      points: points,
      stroke: SNAP_LINE_COLOR,
      strokeWidth: SNAP_LINE_WIDTH / scale,
      listening: false,
      dash: [4, 2],
    });

    snapLineLayerRef.current.add(line);
    snapLineLayerRef.current.batchDraw();
  };

  // 📐 Get visual bounds of a node (handles rotation/transforms)
  const getVisualBounds = (node: Konva.Node): VisualBounds => {
    const rect = node.getClientRect({ skipShadow: true, skipStroke: true });
    return {
      left: rect.x,
      right: rect.x + rect.width,
      top: rect.y,
      bottom: rect.y + rect.height,
      hCenter: rect.x + rect.width / 2,
      vCenter: rect.y + rect.height / 2,
      width: rect.width,
      height: rect.height,
    };
  };

  // 🎯 Handle drag move with snapping logic
  const handleDragMoveWithSnapping = (draggedNode: Konva.Node) => {
    clearSnapLines(); // Clear previous lines first

    const stage = stageRef.current;
    if (!stage) {
      console.log('[SnapGuides] No stage available');
      return;
    }

    // 🔍 Find all other images/groups to snap to
    const allNodes: Konva.Node[] = [];

    // 🎯 Find all nodes recursively (not using CSS selectors)
    const findAllNodes = (container: Konva.Container) => {
      container.getChildren().forEach((child) => {
        if (child !== draggedNode && child.id() && child.visible()) {
          // Check if it's an image or a group with an image
          if (child instanceof Konva.Image || (child instanceof Konva.Group && child.findOne('Image'))) {
            allNodes.push(child);
          }
        }

        // Recursively search in containers
        if (child instanceof Konva.Container) {
          findAllNodes(child);
        }
      });
    };

    // Search through all layers
    stage.getLayers().forEach((layer) => {
      findAllNodes(layer);
    });

    console.log(
      `[SnapGuides] Found ${allNodes.length} other nodes to snap to:`,
      allNodes.map((n) => n.id())
    );

    if (!allNodes.length) {
      console.log('[SnapGuides] No other nodes found for snapping');
      return;
    }

    const draggedBounds = getVisualBounds(draggedNode);
    const scale = stage.scaleX() || 1;
    const outerTolerance = STICKY_THRESHOLD_OUTER / scale;

    console.log(`[SnapGuides] Dragged bounds:`, draggedBounds);
    console.log(`[SnapGuides] Outer tolerance: ${outerTolerance}`);

    // 🎯 Check snapping against each other node
    for (const staticNode of allNodes) {
      const staticBounds = getVisualBounds(staticNode);

      // 📏 Horizontal snap targets (vertical lines)
      const hTargets: SnapTarget[] = [
        {
          nodeEdge: 'left',
          targetEdge: 'left',
          draggedVal: draggedBounds.left,
          staticVal: staticBounds.left,
          newPos: staticBounds.left,
        },
        {
          nodeEdge: 'left',
          targetEdge: 'right',
          draggedVal: draggedBounds.left,
          staticVal: staticBounds.right,
          newPos: staticBounds.right,
        },
        {
          nodeEdge: 'left',
          targetEdge: 'hCenter',
          draggedVal: draggedBounds.left,
          staticVal: staticBounds.hCenter,
          newPos: staticBounds.hCenter,
        },
        {
          nodeEdge: 'right',
          targetEdge: 'left',
          draggedVal: draggedBounds.right,
          staticVal: staticBounds.left,
          newPos: staticBounds.left - draggedBounds.width,
        },
        {
          nodeEdge: 'right',
          targetEdge: 'right',
          draggedVal: draggedBounds.right,
          staticVal: staticBounds.right,
          newPos: staticBounds.right - draggedBounds.width,
        },
        {
          nodeEdge: 'hCenter',
          targetEdge: 'hCenter',
          draggedVal: draggedBounds.hCenter,
          staticVal: staticBounds.hCenter,
          newPos: staticBounds.hCenter - draggedBounds.width / 2,
        },
      ];

      // 🎨 Draw horizontal snap guides
      for (const snap of hTargets) {
        const diff = Math.abs(snap.draggedVal - snap.staticVal);
        console.log(
          `[SnapGuides] H-Snap check: ${snap.nodeEdge}->${snap.targetEdge}, diff=${diff}, tolerance=${outerTolerance}`
        );
        if (diff <= outerTolerance) {
          const lineY1 = Math.min(draggedBounds.top, staticBounds.top) - SNAP_THRESHOLD * 2;
          const lineY2 = Math.max(draggedBounds.bottom, staticBounds.bottom) + SNAP_THRESHOLD * 2;
          console.log(`[SnapGuides] Drawing H-snap line: [${snap.staticVal}, ${lineY1}, ${snap.staticVal}, ${lineY2}]`);
          drawSnapLine([snap.staticVal, lineY1, snap.staticVal, lineY2]);
          break; // Only draw one guide line per axis per target
        }
      }

      // 📏 Vertical snap targets (horizontal lines)
      const vTargets: SnapTarget[] = [
        {
          nodeEdge: 'top',
          targetEdge: 'top',
          draggedVal: draggedBounds.top,
          staticVal: staticBounds.top,
          newPos: staticBounds.top,
        },
        {
          nodeEdge: 'top',
          targetEdge: 'bottom',
          draggedVal: draggedBounds.top,
          staticVal: staticBounds.bottom,
          newPos: staticBounds.bottom,
        },
        {
          nodeEdge: 'top',
          targetEdge: 'vCenter',
          draggedVal: draggedBounds.top,
          staticVal: staticBounds.vCenter,
          newPos: staticBounds.vCenter,
        },
        {
          nodeEdge: 'bottom',
          targetEdge: 'top',
          draggedVal: draggedBounds.bottom,
          staticVal: staticBounds.top,
          newPos: staticBounds.top - draggedBounds.height,
        },
        {
          nodeEdge: 'bottom',
          targetEdge: 'bottom',
          draggedVal: draggedBounds.bottom,
          staticVal: staticBounds.bottom,
          newPos: staticBounds.bottom - draggedBounds.height,
        },
        {
          nodeEdge: 'vCenter',
          targetEdge: 'vCenter',
          draggedVal: draggedBounds.vCenter,
          staticVal: staticBounds.vCenter,
          newPos: staticBounds.vCenter - draggedBounds.height / 2,
        },
      ];

      // 🎨 Draw vertical snap guides
      for (const snap of vTargets) {
        const diff = Math.abs(snap.draggedVal - snap.staticVal);
        if (diff <= outerTolerance) {
          const lineX1 = Math.min(draggedBounds.left, staticBounds.left) - SNAP_THRESHOLD * 2;
          const lineX2 = Math.max(draggedBounds.right, staticBounds.right) + SNAP_THRESHOLD * 2;
          drawSnapLine([lineX1, snap.staticVal, lineX2, snap.staticVal]);
          break; // Only draw one guide line per axis per target
        }
      }
    }
  };

  // This component doesn't render anything - it just manages snap guides
  return null;
};

export default SnapGuides;
