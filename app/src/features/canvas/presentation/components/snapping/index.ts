/**
 * 📐 Snapping Components - MODULAR EXPORTS
 * @description Central export point for all snapping-related components
 * @ai_context Modular snapping system for canvas elements
 */

export { default as SnapGuides } from './SnapGuides';
export { default as SnapMagnet } from './SnapMagnet';
export { default as DragEventDispatcher } from './DragEventDispatcher';
export { default as SnapConnections } from './SnapConnections';
export { default as SnapGroupDragger } from './SnapGroupDragger';
export type { SnapConnection } from './SnapConnections';
