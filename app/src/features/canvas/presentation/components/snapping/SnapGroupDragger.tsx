/**
 * 👥 Snap Group Dragger Component - MODULAR VERSION
 * @description Handles group dragging of connected images
 * @responsibility Move connected images together as a group
 * @ai_context Modular replacement for BaseScene _currentSnapDragGroup system
 */

import React, { useEffect, useRef } from 'react';
import Konva from 'konva';
import { SnapConnection } from './SnapConnections';

/**
 * 👥 Snap Group Member Interface
 */
interface SnapGroupMember {
  node: Konva.Node;
  initialAbsPos: { x: number; y: number };
  initialOffsetFromPrimary?: { dx: number; dy: number };
}

/**
 * 👥 SnapGroupDragger Props
 */
interface SnapGroupDraggerProps {
  stageRef: React.RefObject<Konva.Stage>;
}

/**
 * 👥 SnapGroupDragger Component
 * Handles group dragging of connected images
 */
const SnapGroupDragger: React.FC<SnapGroupDraggerProps> = ({ stageRef }) => {
  const currentSnapDragGroupRef = useRef<SnapGroupMember[]>([]);
  const primaryDraggedNodeRef = useRef<Konva.Node | null>(null);

  // 🔄 Listen for drag events - FOLLOWING useEffect RULES
  // ✅ This IS appropriate for useEffect: synchronizing with external system (custom events)
  useEffect(() => {
    // 🎭 Handle drag start events
    const handleDragStart = (event: CustomEvent) => {
      const { nodeId, node } = event.detail;
      console.log(`[SnapGroupDragger] Setting up group dragging for: ${nodeId}`);

      identifyAndStoreSnapGroup(node);
    };

    // 🎭 Handle drag move events (move connected group)
    const handleDragMove = (event: CustomEvent) => {
      const { nodeId, node } = event.detail;

      if (primaryDraggedNodeRef.current === node && currentSnapDragGroupRef.current.length > 1) {
        moveConnectedGroup();
      }
    };

    // 🎭 Handle drag end events
    const handleDragEnd = (event: CustomEvent) => {
      const { nodeId } = event.detail;
      console.log(`[SnapGroupDragger] Cleaning up group dragging for: ${nodeId}`);

      // Clear group dragging state
      currentSnapDragGroupRef.current = [];
      primaryDraggedNodeRef.current = null;
    };

    // 📝 Register event listeners
    window.addEventListener('canvas:dragStart', handleDragStart as EventListener);
    window.addEventListener('canvas:dragMove', handleDragMove as EventListener);
    window.addEventListener('canvas:dragEnd', handleDragEnd as EventListener);

    // Removed spammy setup log

    // 🧹 Cleanup
    return () => {
      window.removeEventListener('canvas:dragStart', handleDragStart as EventListener);
      window.removeEventListener('canvas:dragMove', handleDragMove as EventListener);
      window.removeEventListener('canvas:dragEnd', handleDragEnd as EventListener);
      // Removed spammy cleanup log
    };
  }, []);

  // 🔍 Find all connected nodes using graph traversal
  const findAllConnectedNodes = (startNodeId: string, connections: SnapConnection[]): string[] => {
    const visited = new Set<string>();
    const queue: string[] = [startNodeId];
    const groupMembers: string[] = [];

    while (queue.length > 0) {
      const currentId = queue.shift()!;
      if (visited.has(currentId)) {
        continue;
      }
      visited.add(currentId);

      // Only add actual node IDs, not virtual stage
      if (currentId !== '__stage__') {
        groupMembers.push(currentId);
      }

      connections.forEach((conn) => {
        if (conn.nodeId === currentId && conn.targetNodeId !== '__stage__' && !visited.has(conn.targetNodeId)) {
          queue.push(conn.targetNodeId);
        } else if (conn.targetNodeId === currentId && conn.nodeId !== '__stage__' && !visited.has(conn.nodeId)) {
          queue.push(conn.nodeId);
        }
      });
    }

    console.log(`[SnapGroupDragger] Found connected nodes for ${startNodeId}:`, groupMembers);
    return groupMembers;
  };

  // 🎯 Identify and store the snap group for dragging
  const identifyAndStoreSnapGroup = (draggedNode: Konva.Node) => {
    currentSnapDragGroupRef.current = [];
    const primaryInitialPos = draggedNode.absolutePosition();
    primaryDraggedNodeRef.current = draggedNode;

    console.log(`[SnapGroupDragger] Primary dragged node: ${draggedNode.id()}`, primaryInitialPos);

    // 🔗 Get all connections from the global snap connections
    const allConnections = (window as any).snapConnections?.getAllConnections() || [];
    const allMemberIds = findAllConnectedNodes(draggedNode.id(), allConnections);

    console.log(`[SnapGroupDragger] All connected member IDs:`, allMemberIds);

    const stage = stageRef.current;
    if (!stage) {
      console.error('[SnapGroupDragger] Stage not found!');
      return;
    }

    // 🔍 Find all connected nodes and store their initial positions
    allMemberIds.forEach((memberId) => {
      const memberNode = stage.findOne(`#${memberId}`);
      if (memberNode && memberNode.id()) {
        const memberInitialPos = memberNode.absolutePosition();

        // Calculate offset from primary node
        const offsetFromPrimary = {
          dx: memberInitialPos.x - primaryInitialPos.x,
          dy: memberInitialPos.y - primaryInitialPos.y,
        };

        currentSnapDragGroupRef.current.push({
          node: memberNode,
          initialAbsPos: memberInitialPos,
          initialOffsetFromPrimary: offsetFromPrimary,
        });

        console.log(`[SnapGroupDragger] Added group member: ${memberId}, offset:`, offsetFromPrimary);
      }
    });

    console.log(`[SnapGroupDragger] Group size: ${currentSnapDragGroupRef.current.length}`);
  };

  // 🚚 Move all connected group members together
  const moveConnectedGroup = () => {
    if (!primaryDraggedNodeRef.current || currentSnapDragGroupRef.current.length <= 1) {
      return;
    }

    const primaryNodeCurrentPos = primaryDraggedNodeRef.current.absolutePosition();

    currentSnapDragGroupRef.current.forEach((member) => {
      if (member.node !== primaryDraggedNodeRef.current) {
        if (member.initialOffsetFromPrimary) {
          const newSlaveX = primaryNodeCurrentPos.x + member.initialOffsetFromPrimary.dx;
          const newSlaveY = primaryNodeCurrentPos.y + member.initialOffsetFromPrimary.dy;

          // Move the connected node
          member.node.absolutePosition({ x: newSlaveX, y: newSlaveY });

          // 📡 Dispatch dragging events for group members (like old BaseScene)
          window.dispatchEvent(
            new CustomEvent('canvas:elementDragging', {
              detail: {
                id: member.node.id(),
                x: member.node.x(),
                y: member.node.y(),
                isDragging: true,
              },
            })
          );

          console.log(`[SnapGroupDragger] Moved connected node ${member.node.id()} to (${newSlaveX}, ${newSlaveY})`);
        }
      }
    });
  };

  // This component doesn't render anything - it just manages group dragging
  return null;
};

export default SnapGroupDragger;
