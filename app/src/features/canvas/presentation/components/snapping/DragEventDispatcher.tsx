/**
 * 🎯 Drag Event Dispatcher - MODULAR VERSION
 * @description Dispatches drag events for snapping system integration
 * @responsibility Detect drag operations and emit events for snap components
 * @ai_context Modular replacement for BaseScene drag event handling
 */

import React, { useEffect, useRef } from 'react';
import Konva from 'konva';

/**
 * 🎯 DragEventDispatcher Props
 */
interface DragEventDispatcherProps {
  stageRef: React.RefObject<Konva.Stage>;
}

/**
 * 🎯 DragEventDispatcher Component
 * Detects drag operations and dispatches events for snapping system
 */
const DragEventDispatcher: React.FC<DragEventDispatcherProps> = ({ stageRef }) => {
  const activeDragNodesRef = useRef<Set<string>>(new Set());

  // 🔄 Listen for existing URLImage drag events - FOLLOWING useEffect RULES
  // ✅ This IS appropriate for useEffect: synchronizing with external system (custom events)
  useEffect(() => {
    const stage = stageRef.current;
    if (!stage) return;

    // 🎭 Handle URLImage drag start (from Konva dragstart)
    const handleKonvaDragStart = (e: Konva.KonvaEventObject<DragEvent>) => {
      const node = e.target;
      const nodeId = node.id();

      // 🔍 Only handle draggable images and groups
      if (!nodeId || !node.draggable()) return;

      // 🖼️ Check if it's an image or a group with an image
      const isImage = node instanceof Konva.Image;
      const isImageGroup = node instanceof Konva.Group && node.findOne('Image');

      if (!isImage && !isImageGroup) return;

      console.log(`[DragEventDispatcher] Konva drag start detected: ${nodeId}`);

      // 📝 Track active drag
      activeDragNodesRef.current.add(nodeId);

      // 📡 Dispatch drag start event for snapping system
      window.dispatchEvent(
        new CustomEvent('canvas:dragStart', {
          detail: {
            nodeId,
            node,
            type: isImage ? 'image' : 'group',
          },
        })
      );
    };

    // 🎭 Handle URLImage drag move (from Konva dragmove)
    const handleKonvaDragMove = (e: Konva.KonvaEventObject<DragEvent>) => {
      const node = e.target;
      const nodeId = node.id();

      // 🔍 Only handle nodes that are actively being dragged
      if (!nodeId || !activeDragNodesRef.current.has(nodeId)) return;

      // 📡 Dispatch drag move event for snapping system
      console.log(`[DragEventDispatcher] Konva drag move detected: ${nodeId} at (${node.x()}, ${node.y()})`);
      window.dispatchEvent(
        new CustomEvent('canvas:dragMove', {
          detail: {
            nodeId,
            node,
            position: { x: node.x(), y: node.y() },
          },
        })
      );
    };

    // 🎭 Handle URLImage drag end (from Konva dragend)
    const handleKonvaDragEnd = (e: Konva.KonvaEventObject<DragEvent>) => {
      const node = e.target;
      const nodeId = node.id();

      // 🔍 Only handle nodes that were actively being dragged
      if (!nodeId || !activeDragNodesRef.current.has(nodeId)) return;

      console.log(`[DragEventDispatcher] Konva drag end detected: ${nodeId}`);

      // 📝 Remove from active drag tracking
      activeDragNodesRef.current.delete(nodeId);

      // 📡 Dispatch drag end event for snapping system
      window.dispatchEvent(
        new CustomEvent('canvas:dragEnd', {
          detail: {
            nodeId,
            node,
            finalPosition: { x: node.x(), y: node.y() },
          },
        })
      );
    };

    // 📝 Register Konva drag event listeners on stage
    stage.on('dragstart', handleKonvaDragStart);
    stage.on('dragmove', handleKonvaDragMove);
    stage.on('dragend', handleKonvaDragEnd);

    // Removed spammy setup log

    // 🧹 Cleanup
    return () => {
      stage.off('dragstart', handleKonvaDragStart);
      stage.off('dragmove', handleKonvaDragMove);
      stage.off('dragend', handleKonvaDragEnd);

      // Clear active drag tracking
      activeDragNodesRef.current.clear();

      // Removed spammy cleanup log
    };
  }, []);

  // Note: URLImage components are already draggable, no need to enable dragging

  // This component doesn't render anything - it just manages drag event dispatching
  return null;
};

export default DragEventDispatcher;
