/**
 * 🧲 Snap Magnet Component - MODULAR VERSION
 * @description Handles magnetic snapping during drag operations
 * @responsibility Magnetic position snapping, dragBoundFunc implementation
 * @ai_context Modular replacement for BaseScene magnetic snapping system
 */

import React, { useEffect, useRef } from 'react';
import Konva from 'konva';
import { Edge } from '../../../domain/types';
import { useCanvasToolbarStore } from '../../../domain/state';

// Snapping constants (from old BaseScene)
const STICKY_THRESHOLD_INNER = 1; // px – actually lock / create connection (auto-sticks)

/**
 * 🧲 Visual Bounds Interface
 */
interface VisualBounds {
  left: number;
  right: number;
  top: number;
  bottom: number;
  hCenter: number;
  vCenter: number;
  width: number;
  height: number;
}

/**
 * 🧲 Snap Target Interface
 */
interface SnapTarget {
  nodeEdge: Edge;
  targetEdge: Edge;
  draggedVal: number;
  staticVal: number;
  newPos: number;
}

/**
 * 🧲 SnapMagnet Props
 */
interface SnapMagnetProps {
  stageRef: React.RefObject<Konva.Stage>;
}

/**
 * 🧲 SnapMagnet Component
 * Handles magnetic position snapping during drag operations
 */
const SnapMagnet: React.FC<SnapMagnetProps> = ({ stageRef }) => {
  const currentDraggedNodeRef = useRef<Konva.Node | null>(null);
  const originalDragBoundFuncRef = useRef<((pos: Konva.Vector2d) => Konva.Vector2d) | null>(null);

  // 🎯 Get selected IDs from domain state
  const selectedIds = useCanvasToolbarStore((state) => state.selectedIds);

  // 🔄 Listen for drag events - FOLLOWING useEffect RULES
  // ✅ This IS appropriate for useEffect: synchronizing with external system (custom events)
  useEffect(() => {
    // 🎭 Handle drag start events
    const handleDragStart = (event: CustomEvent) => {
      const { nodeId, node } = event.detail;

      // 🎯 CRITICAL FIX: Only apply magnetic snapping to SELECTED nodes (like old BaseScene)
      if (!selectedIds.includes(nodeId)) {
        console.log(`[SnapMagnet] Skipping magnetic snapping for unselected node: ${nodeId}`);
        return;
      }

      console.log(`[SnapMagnet] Setting up magnetic snapping for SELECTED node: ${nodeId}`);

      currentDraggedNodeRef.current = node;

      // 🧲 Set up magnetic snapping dragBoundFunc
      if (node && typeof node.dragBoundFunc === 'function') {
        // Store original dragBoundFunc if it exists
        originalDragBoundFuncRef.current = node.dragBoundFunc();
      }

      // 🎯 Apply magnetic snapping dragBoundFunc
      node.dragBoundFunc((pos: Konva.Vector2d) => {
        return handleMagneticSnapping(node, pos);
      });
    };

    // 🎭 Handle drag end events
    const handleDragEnd = (event: CustomEvent) => {
      const { nodeId, node } = event.detail;

      // 🎯 Only clean up if this was a selected node that we set up
      if (currentDraggedNodeRef.current !== node) {
        return;
      }

      console.log(`[SnapMagnet] Removing magnetic snapping for SELECTED node: ${nodeId}`);

      // 🔄 Restore original dragBoundFunc or remove it
      if (originalDragBoundFuncRef.current) {
        node.dragBoundFunc(originalDragBoundFuncRef.current);
      } else {
        node.dragBoundFunc(null);
      }

      currentDraggedNodeRef.current = null;
      originalDragBoundFuncRef.current = null;
    };

    // 📝 Register event listeners
    window.addEventListener('canvas:dragStart', handleDragStart as EventListener);
    window.addEventListener('canvas:dragEnd', handleDragEnd as EventListener);

    // Removed spammy setup log

    // 🧹 Cleanup
    return () => {
      window.removeEventListener('canvas:dragStart', handleDragStart as EventListener);
      window.removeEventListener('canvas:dragEnd', handleDragEnd as EventListener);
      // Removed spammy cleanup log
    };
  }, []);

  // 📐 Get visual bounds of a node (handles rotation/transforms)
  const getVisualBounds = (node: Konva.Node): VisualBounds => {
    const rect = node.getClientRect({ skipShadow: true, skipStroke: true });
    return {
      left: rect.x,
      right: rect.x + rect.width,
      top: rect.y,
      bottom: rect.y + rect.height,
      hCenter: rect.x + rect.width / 2,
      vCenter: rect.y + rect.height / 2,
      width: rect.width,
      height: rect.height,
    };
  };

  // 🧲 Handle magnetic snapping during drag
  const handleMagneticSnapping = (draggedNode: Konva.Node, pos: Konva.Vector2d): Konva.Vector2d => {
    const stage = stageRef.current;
    if (!stage) return pos;

    // 🔍 Find all other images/groups to snap to
    const allNodes: Konva.Node[] = [];

    // 🎯 Find all nodes recursively (not using CSS selectors)
    const findAllNodes = (container: Konva.Container) => {
      container.getChildren().forEach((child) => {
        if (child !== draggedNode && child.id() && child.visible()) {
          // Check if it's an image or a group with an image
          if (child instanceof Konva.Image || (child instanceof Konva.Group && child.findOne('Image'))) {
            allNodes.push(child);
          }
        }

        // Recursively search in containers
        if (child instanceof Konva.Container) {
          findAllNodes(child);
        }
      });
    };

    // Search through all layers
    stage.getLayers().forEach((layer) => {
      findAllNodes(layer);
    });

    if (!allNodes.length) return pos;

    // 🎯 Calculate bounds based on PROPOSED position (like old BaseScene)
    const tempClientRect = draggedNode.getClientRect({ skipShadow: true, skipStroke: true });
    const draggedBounds = {
      left: pos.x,
      right: pos.x + tempClientRect.width,
      top: pos.y,
      bottom: pos.y + tempClientRect.height,
      hCenter: pos.x + tempClientRect.width / 2,
      vCenter: pos.y + tempClientRect.height / 2,
      width: tempClientRect.width,
      height: tempClientRect.height,
    };

    const scale = stage.scaleX() || 1;
    const innerTolerance = STICKY_THRESHOLD_INNER / scale;

    let snappedX = pos.x;
    let snappedY = pos.y;
    let hasXSnap = false;
    let hasYSnap = false;

    // 🎯 Check snapping against each other node
    for (const staticNode of allNodes) {
      if (hasXSnap && hasYSnap) break; // Both axes snapped, no need to continue

      const staticBounds = getVisualBounds(staticNode);

      // 📏 Horizontal snap targets (X-axis snapping)
      if (!hasXSnap) {
        const hTargets: SnapTarget[] = [
          {
            nodeEdge: 'left',
            targetEdge: 'left',
            draggedVal: draggedBounds.left,
            staticVal: staticBounds.left,
            newPos: staticBounds.left,
          },
          {
            nodeEdge: 'left',
            targetEdge: 'right',
            draggedVal: draggedBounds.left,
            staticVal: staticBounds.right,
            newPos: staticBounds.right,
          },
          {
            nodeEdge: 'right',
            targetEdge: 'left',
            draggedVal: draggedBounds.right,
            staticVal: staticBounds.left,
            newPos: staticBounds.left - draggedBounds.width,
          },
          {
            nodeEdge: 'right',
            targetEdge: 'right',
            draggedVal: draggedBounds.right,
            staticVal: staticBounds.right,
            newPos: staticBounds.right - draggedBounds.width,
          },
          {
            nodeEdge: 'hCenter',
            targetEdge: 'hCenter',
            draggedVal: draggedBounds.hCenter,
            staticVal: staticBounds.hCenter,
            newPos: staticBounds.hCenter - draggedBounds.width / 2,
          },
        ];

        // 🧲 Apply horizontal magnetic snapping
        for (const snap of hTargets) {
          const diff = Math.abs(snap.draggedVal - snap.staticVal);
          if (diff <= innerTolerance) {
            snappedX = snap.newPos;
            hasXSnap = true;
            console.log(`[SnapMagnet] X-axis snapped: ${snap.nodeEdge} -> ${snap.targetEdge}, newX=${snap.newPos}`);
            break;
          }
        }
      }

      // 📏 Vertical snap targets (Y-axis snapping)
      if (!hasYSnap) {
        const vTargets: SnapTarget[] = [
          {
            nodeEdge: 'top',
            targetEdge: 'top',
            draggedVal: draggedBounds.top,
            staticVal: staticBounds.top,
            newPos: staticBounds.top,
          },
          {
            nodeEdge: 'top',
            targetEdge: 'bottom',
            draggedVal: draggedBounds.top,
            staticVal: staticBounds.bottom,
            newPos: staticBounds.bottom,
          },
          {
            nodeEdge: 'bottom',
            targetEdge: 'top',
            draggedVal: draggedBounds.bottom,
            staticVal: staticBounds.top,
            newPos: staticBounds.top - draggedBounds.height,
          },
          {
            nodeEdge: 'bottom',
            targetEdge: 'bottom',
            draggedVal: draggedBounds.bottom,
            staticVal: staticBounds.bottom,
            newPos: staticBounds.bottom - draggedBounds.height,
          },
          {
            nodeEdge: 'vCenter',
            targetEdge: 'vCenter',
            draggedVal: draggedBounds.vCenter,
            staticVal: staticBounds.vCenter,
            newPos: staticBounds.vCenter - draggedBounds.height / 2,
          },
        ];

        // 🧲 Apply vertical magnetic snapping
        for (const snap of vTargets) {
          const diff = Math.abs(snap.draggedVal - snap.staticVal);
          if (diff <= innerTolerance) {
            snappedY = snap.newPos;
            hasYSnap = true;
            console.log(`[SnapMagnet] Y-axis snapped: ${snap.nodeEdge} -> ${snap.targetEdge}, newY=${snap.newPos}`);
            break;
          }
        }
      }
    }

    // 🎯 Return snapped position
    const finalPos = { x: snappedX, y: snappedY };

    // 📝 Log snapping if it occurred
    if (hasXSnap || hasYSnap) {
      console.log(
        `[SnapMagnet] Snapped ${draggedNode.id()}: X=${hasXSnap ? 'snapped' : 'free'}, Y=${hasYSnap ? 'snapped' : 'free'}`
      );
    }

    return finalPos;
  };

  // This component doesn't render anything - it just manages magnetic snapping
  return null;
};

export default SnapMagnet;
