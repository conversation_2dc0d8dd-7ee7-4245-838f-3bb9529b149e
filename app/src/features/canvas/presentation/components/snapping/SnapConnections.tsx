/**
 * 🔗 Snap Connections Component - MODULAR VERSION
 * @description Manages persistent snap connections between images
 * @responsibility Connection creation, removal, and persistence
 * @ai_context Modular replacement for BaseScene _activeSnapConnections system
 */

import React, { useEffect, useRef } from 'react';
import Konva from 'konva';
import { Edge } from '../../../domain/types';

// Snapping constants (from old BaseScene)
const STICKY_THRESHOLD_INNER = 1; // px – actually lock / create connection (auto-sticks)

/**
 * 🔗 Snap Connection Interface
 */
export interface SnapConnection {
  nodeId: string;
  targetNodeId: string;
  nodeEdge: Edge;
  targetEdge: Edge;
}

/**
 * 🔗 SnapConnections Props
 */
interface SnapConnectionsProps {
  stageRef: React.RefObject<Konva.Stage>;
}

/**
 * 🔗 SnapConnections Component
 * Manages persistent snap connections between images
 */
const SnapConnections: React.FC<SnapConnectionsProps> = ({ stageRef }) => {
  const activeConnectionsRef = useRef<SnapConnection[]>([]);

  // 🔄 Listen for drag end events to finalize connections - FOLLOWING useEffect RULES
  // ✅ This IS appropriate for useEffect: synchronizing with external system (custom events)
  useEffect(() => {
    // 🎭 Handle drag end events to finalize snap connections
    const handleDragEnd = (event: CustomEvent) => {
      const { nodeId, node } = event.detail;
      console.log(`[SnapConnections] Finalizing connections for: ${nodeId}`);

      finalizeSnapConnections(node);
    };

    // 📝 Register event listeners
    window.addEventListener('canvas:dragEnd', handleDragEnd as EventListener);

    // Removed spammy setup log

    // 🧹 Cleanup
    return () => {
      window.removeEventListener('canvas:dragEnd', handleDragEnd as EventListener);
      // Removed spammy cleanup log
    };
  }, []);

  // 🔗 Get connections for a specific node
  const getConnectionsForNode = (nodeId: string): SnapConnection[] => {
    return activeConnectionsRef.current.filter((c) => c.nodeId === nodeId);
  };

  // 🗑️ Remove connections for a node
  const removeConnections = (nodeId: string, edge?: Edge) => {
    if (edge) {
      // Remove connections where the given node participates on the specified edge
      activeConnectionsRef.current = activeConnectionsRef.current.filter(
        (c) => !((c.nodeId === nodeId && c.nodeEdge === edge) || (c.targetNodeId === nodeId && c.targetEdge === edge))
      );
    } else {
      // Remove all connections where nodeId is either source or target
      activeConnectionsRef.current = activeConnectionsRef.current.filter(
        (c) => c.nodeId !== nodeId && c.targetNodeId !== nodeId
      );
    }
  };

  // ➕ Add a new connection
  const addConnection = (conn: SnapConnection) => {
    // Remove any existing connection on the same node+edge then push
    removeConnections(conn.nodeId, conn.nodeEdge);
    activeConnectionsRef.current.push(conn);
    console.log(
      `[SnapConnections] Added connection: ${conn.nodeId}(${conn.nodeEdge}) -> ${conn.targetNodeId}(${conn.targetEdge})`
    );

    // 📡 Dispatch connection added event
    window.dispatchEvent(
      new CustomEvent('canvas:connectionAdded', {
        detail: { connection: conn, allConnections: [...activeConnectionsRef.current] },
      })
    );
  };

  // 📐 Get visual bounds of a node
  const getVisualBounds = (node: Konva.Node) => {
    const rect = node.getClientRect({ skipShadow: true, skipStroke: true });
    return {
      left: rect.x,
      right: rect.x + rect.width,
      top: rect.y,
      bottom: rect.y + rect.height,
      hCenter: rect.x + rect.width / 2,
      vCenter: rect.y + rect.height / 2,
      width: rect.width,
      height: rect.height,
    };
  };

  // 🎯 Finalize snap connections after drag ends
  const finalizeSnapConnections = (draggedNode: Konva.Node) => {
    const stage = stageRef.current;
    if (!stage) return;

    // 🔍 Find all other images/groups to potentially connect to
    const allNodes: Konva.Node[] = [];

    const findAllNodes = (container: Konva.Container) => {
      container.getChildren().forEach((child) => {
        if (child !== draggedNode && child.id() && child.visible()) {
          if (child instanceof Konva.Image || (child instanceof Konva.Group && child.findOne('Image'))) {
            allNodes.push(child);
          }
        }

        if (child instanceof Konva.Container) {
          findAllNodes(child);
        }
      });
    };

    stage.getLayers().forEach((layer) => {
      findAllNodes(layer);
    });

    if (!allNodes.length) return;

    const draggedBounds = getVisualBounds(draggedNode);
    const scale = stage.scaleX() || 1;
    const innerTolerance = STICKY_THRESHOLD_INNER / scale;

    console.log(`[SnapConnections] Checking ${allNodes.length} nodes for potential connections`);

    // 🎯 Check each other node for potential connections
    for (const staticNode of allNodes) {
      const staticBounds = getVisualBounds(staticNode);
      let bestHSnap: { connection: SnapConnection; diff: number } | null = null;
      let bestVSnap: { connection: SnapConnection; diff: number } | null = null;

      // 📏 Horizontal snap targets
      const hTargets = [
        {
          nodeEdge: 'left' as Edge,
          targetEdge: 'left' as Edge,
          draggedVal: draggedBounds.left,
          staticVal: staticBounds.left,
        },
        {
          nodeEdge: 'left' as Edge,
          targetEdge: 'right' as Edge,
          draggedVal: draggedBounds.left,
          staticVal: staticBounds.right,
        },
        {
          nodeEdge: 'right' as Edge,
          targetEdge: 'left' as Edge,
          draggedVal: draggedBounds.right,
          staticVal: staticBounds.left,
        },
        {
          nodeEdge: 'right' as Edge,
          targetEdge: 'right' as Edge,
          draggedVal: draggedBounds.right,
          staticVal: staticBounds.right,
        },
        {
          nodeEdge: 'hCenter' as Edge,
          targetEdge: 'hCenter' as Edge,
          draggedVal: draggedBounds.hCenter,
          staticVal: staticBounds.hCenter,
        },
      ];

      // 📏 Vertical snap targets
      const vTargets = [
        {
          nodeEdge: 'top' as Edge,
          targetEdge: 'top' as Edge,
          draggedVal: draggedBounds.top,
          staticVal: staticBounds.top,
        },
        {
          nodeEdge: 'top' as Edge,
          targetEdge: 'bottom' as Edge,
          draggedVal: draggedBounds.top,
          staticVal: staticBounds.bottom,
        },
        {
          nodeEdge: 'bottom' as Edge,
          targetEdge: 'top' as Edge,
          draggedVal: draggedBounds.bottom,
          staticVal: staticBounds.top,
        },
        {
          nodeEdge: 'bottom' as Edge,
          targetEdge: 'bottom' as Edge,
          draggedVal: draggedBounds.bottom,
          staticVal: staticBounds.bottom,
        },
        {
          nodeEdge: 'vCenter' as Edge,
          targetEdge: 'vCenter' as Edge,
          draggedVal: draggedBounds.vCenter,
          staticVal: staticBounds.vCenter,
        },
      ];

      // 🔍 Find best horizontal snap
      for (const snap of hTargets) {
        const diff = Math.abs(snap.draggedVal - snap.staticVal);
        if (diff <= innerTolerance) {
          if (!bestHSnap || diff < bestHSnap.diff) {
            bestHSnap = {
              connection: {
                nodeId: draggedNode.id(),
                targetNodeId: staticNode.id(),
                nodeEdge: snap.nodeEdge,
                targetEdge: snap.targetEdge,
              },
              diff,
            };
          }
        }
      }

      // 🔍 Find best vertical snap
      for (const snap of vTargets) {
        const diff = Math.abs(snap.draggedVal - snap.staticVal);
        if (diff <= innerTolerance) {
          if (!bestVSnap || diff < bestVSnap.diff) {
            bestVSnap = {
              connection: {
                nodeId: draggedNode.id(),
                targetNodeId: staticNode.id(),
                nodeEdge: snap.nodeEdge,
                targetEdge: snap.targetEdge,
              },
              diff,
            };
          }
        }
      }

      // 🎯 Add the best connection (prioritize vertical if equal)
      if (bestHSnap && bestVSnap) {
        if (bestVSnap.diff <= bestHSnap.diff) {
          addConnection(bestVSnap.connection);
        } else {
          addConnection(bestHSnap.connection);
        }
      } else if (bestHSnap) {
        addConnection(bestHSnap.connection);
      } else if (bestVSnap) {
        addConnection(bestVSnap.connection);
      }
    }
  };

  // 🔄 Expose connection management functions globally
  useEffect(() => {
    // Make connection functions available globally for other components
    (window as any).snapConnections = {
      getConnectionsForNode,
      removeConnections,
      addConnection,
      getAllConnections: () => [...activeConnectionsRef.current],
    };

    return () => {
      delete (window as any).snapConnections;
    };
  }, []);

  // This component doesn't render anything - it just manages connections
  return null;
};

export default SnapConnections;
