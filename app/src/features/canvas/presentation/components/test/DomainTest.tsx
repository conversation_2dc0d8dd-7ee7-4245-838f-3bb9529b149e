/**
 * 🧪 Domain Test Component
 *
 * @description Simple test to check if our domain stores are working
 * @ai_context Minimal test to isolate domain state issues
 */

import React from 'react';
import { useElementDomainStore, useToolDomainStore } from '../../../domain/state';
import { useShallow } from 'zustand/react/shallow';

export const DomainTest: React.FC = () => {
  console.log('[DomainTest] Component rendering...');

  try {
    // Test element domain store with shallow comparison
    const elementState = useElementDomainStore(
      useShallow((state) => ({
        images: state.images,
        texts: state.texts,
        htmlElements: state.htmlElements,
        selectedIds: state.selectedIds,
      }))
    );

    console.log('[DomainTest] Element state:', elementState);

    // Test tool domain store with shallow comparison
    const toolState = useToolDomainStore(
      useShallow((state) => ({
        activeTool: state.activeTool,
        scale: state.scale,
        position: state.position,
      }))
    );

    console.log('[DomainTest] Tool state:', toolState);

    return (
      <div style={{ padding: '20px', fontFamily: 'Inter' }}>
        <h1>🧪 Domain State Test</h1>

        <div style={{ marginBottom: '20px' }}>
          <h2>Element State:</h2>
          <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
            {JSON.stringify(elementState, null, 2)}
          </pre>
        </div>

        <div style={{ marginBottom: '20px' }}>
          <h2>Tool State:</h2>
          <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
            {JSON.stringify(toolState, null, 2)}
          </pre>
        </div>

        <div>
          <p>✅ If you can see this, domain stores are working!</p>
        </div>
      </div>
    );
  } catch (error) {
    console.error('[DomainTest] Error:', error);
    return (
      <div style={{ padding: '20px', color: 'red' }}>
        <h1>❌ Domain Test Failed</h1>
        <p>Error: {error instanceof Error ? error.message : 'Unknown error'}</p>
      </div>
    );
  }
};

export default DomainTest;
