/**
 * 👥 Collaboration Box - MODULAR VERSION
 * @description UI component showing active collaborators
 * @responsibility Display active users with avatars and dropdown
 * @ai_context Modular replacement for old CollaborationBox component
 */

import React, { useState, useRef, useEffect } from 'react';
import { useAuth } from 'wasp/client/auth';
import { ChevronDown, Users, Edit2, Check, X } from 'lucide-react';
import { CollaborationUser, removeEmailDomain } from '../../hooks/useCanvasSync';

/**
 * 👥 Avatar Component
 */
const Avatar: React.FC<{ className?: string; children: React.ReactNode }> = ({ className, children }) => (
  <div className={`rounded-full overflow-hidden ${className}`}>{children}</div>
);

/**
 * 👥 Avatar Fallback Component
 */
const AvatarFallback: React.FC<{ className?: string; children: React.ReactNode }> = ({ className, children }) => (
  <div className={`flex items-center justify-center w-full h-full ${className}`}>{children}</div>
);

/**
 * 👥 Collaboration Box Props
 */
interface CollaborationBoxProps {
  activeUsers: CollaborationUser[];
  className?: string;
  currentUserName?: string;
  currentUserId?: string;
  onNameChange?: (newName: string) => void;
  onJumpToUser?: (userId: string, position: { x: number; y: number }) => void;
  isGuest?: boolean;
}

/**
 * 👥 Collaboration Box Component
 * UI component showing active collaborators
 */
const CollaborationBox: React.FC<CollaborationBoxProps> = ({
  activeUsers,
  className,
  currentUserName,
  currentUserId,
  onNameChange,
  onJumpToUser,
  isGuest = false
}) => {
  const { data: user } = useAuth();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isEditingName, setIsEditingName] = useState(false);
  const [editedName, setEditedName] = useState(currentUserName || '');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLButtonElement>(null);
  const nameInputRef = useRef<HTMLInputElement>(null);

  // 🔄 Close dropdown when clicking outside - FOLLOWING useEffect RULES
  // ✅ This IS appropriate for useEffect: synchronizing with external system (DOM events)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isDropdownOpen]);

  // 🔍 Filter out current user from active users
  const otherUsers = activeUsers.filter((activeUser) => {
    if (user) {
      // For authenticated users, compare by user ID
      return Number(activeUser.user?.id) !== Number(user.id);
    } else if (isGuest && currentUserId) {
      // For guest users, filter out by matching the current user ID
      return activeUser.user?.id !== currentUserId;
    }
    return true;
  });

  // 👀 Show max 3 avatars in the button
  const maxVisibleAvatars = 3;
  const visibleUsers = otherUsers.slice(0, maxVisibleAvatars);
  const hiddenUsersCount = Math.max(0, otherUsers.length - maxVisibleAvatars);

  // 🎨 Get user initials
  const getUserInitials = (username: string): string => {
    return (
      username
        ?.split(' ')
        .map((n) => n[0])
        .join('')
        .toUpperCase() || '?'
    );
  };

  // 📝 Handle name editing
  const handleStartEdit = () => {
    setEditedName(currentUserName || '');
    setIsEditingName(true);
    setTimeout(() => nameInputRef.current?.focus(), 0);
  };

  const handleSaveName = () => {
    const trimmedName = editedName.trim();
    if (trimmedName && trimmedName !== currentUserName && onNameChange) {
      onNameChange(trimmedName);
    }
    setIsEditingName(false);
  };

  const handleCancelEdit = () => {
    setEditedName(currentUserName || '');
    setIsEditingName(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveName();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  // Update edited name when currentUserName changes
  useEffect(() => {
    setEditedName(currentUserName || '');
  }, [currentUserName]);

  // Handle jumping to user's cursor position
  const handleJumpToUser = (activeUser: CollaborationUser) => {
    if (activeUser.cursor && onJumpToUser) {
      onJumpToUser(activeUser.user.id, activeUser.cursor);
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Main collaboration button */}
      <button
        ref={triggerRef}
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className='flex items-center gap-2 bg-[#F9F7ED] border border-[#E8E4D4] rounded-lg px-3 py-2 shadow-sm hover:shadow-md transition-all duration-200'
        style={{ boxShadow: '0 2px 8px rgba(0,0,0,0.08)' }}
        aria-label='Show active collaborators'
      >
        {/* Avatar stack */}
        <div className='flex -space-x-2'>
          {otherUsers.length > 0 ? (
            <>
              {visibleUsers.map((activeUser, idx) => (
                <Avatar key={`${activeUser.user?.id}-${idx}`} className='h-6 w-6 border-2 border-white ring-0'>
                  <AvatarFallback className='text-xs bg-[#566B46] text-white'>
                    {getUserInitials(activeUser.user?.name)}
                  </AvatarFallback>
                </Avatar>
              ))}
              {hiddenUsersCount > 0 && (
                <div className='h-6 w-6 border-2 border-white rounded-full bg-[#566B46] text-white flex items-center justify-center text-xs font-medium'>
                  +{hiddenUsersCount}
                </div>
              )}
            </>
          ) : (
            <Users className='h-4 w-4 text-[#566B46]' />
          )}
        </div>

        {/* User count and status */}
        <div className='flex items-center gap-1'>
          <span className='text-sm font-medium text-[#566B46]'>
            {otherUsers.length > 0 ? `${otherUsers.length}` : 'Solo'}
          </span>
          {otherUsers.length > 0 && <div className='w-2 h-2 bg-green-500 rounded-full'></div>}
        </div>

        {/* Dropdown arrow */}
        <ChevronDown
          className={`h-3 w-3 text-[#566B46] transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`}
        />
      </button>

      {/* Dropdown menu */}
      {isDropdownOpen && (
        <div
          ref={dropdownRef}
          className='absolute top-full left-0 mt-2 w-64 bg-white border border-[#E8E4D4] rounded-lg shadow-lg z-50'
          style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}
        >
          <div className='p-3'>
            <div className='flex items-center gap-2 mb-3'>
              <Users className='h-4 w-4 text-[#566B46]' />
              <span className='text-sm font-semibold text-[#566B46]'>Active Collaborators ({otherUsers.length})</span>
            </div>

            <div className='space-y-2'>
              {/* Current User Section (for guests) */}
              {isGuest && currentUserName && (
                <>
                  <div className='flex items-center gap-3 p-2 rounded-md bg-[#566B46]/10 border border-[#566B46]/20'>
                    <Avatar className='h-8 w-8 ring-0'>
                      <AvatarFallback className='text-sm bg-[#566B46] text-white'>
                        {getUserInitials(currentUserName)}
                      </AvatarFallback>
                    </Avatar>
                    <div className='flex-1'>
                      {isEditingName ? (
                        <div className='flex items-center gap-2'>
                          <input
                            ref={nameInputRef}
                            type='text'
                            value={editedName}
                            onChange={(e) => setEditedName(e.target.value)}
                            onKeyDown={handleKeyDown}
                            className='text-sm font-medium text-[#566B46] bg-white border border-[#566B46]/30 rounded px-2 py-1 flex-1 focus:outline-none focus:ring-2 focus:ring-[#566B46]/50'
                            placeholder='Enter your name'
                            maxLength={50}
                          />
                          <button
                            onClick={handleSaveName}
                            className='p-1 text-green-600 hover:bg-green-100 rounded transition-colors'
                            title='Save name'
                          >
                            <Check className='w-3 h-3' />
                          </button>
                          <button
                            onClick={handleCancelEdit}
                            className='p-1 text-red-600 hover:bg-red-100 rounded transition-colors'
                            title='Cancel'
                          >
                            <X className='w-3 h-3' />
                          </button>
                        </div>
                      ) : (
                        <div className='flex items-center gap-2'>
                          <div>
                            <div className='text-sm font-medium text-[#566B46]'>
                              {removeEmailDomain(currentUserName)} (You)
                            </div>
                            <div className='text-xs text-[#566B46]/70'>Active now</div>
                          </div>
                          <button
                            onClick={handleStartEdit}
                            className='p-1 text-[#566B46]/60 hover:text-[#566B46] hover:bg-[#566B46]/10 rounded transition-colors'
                            title='Edit your name'
                          >
                            <Edit2 className='w-3 h-3' />
                          </button>
                        </div>
                      )}
                    </div>
                    <div className='w-2 h-2 bg-blue-500 rounded-full'></div>
                  </div>
                  {otherUsers.length > 0 && <div className='border-t border-[#566B46]/20 my-2'></div>}
                </>
              )}

              {/* Other Users */}
              {otherUsers.length > 0 ? (
                otherUsers.map((activeUser) => (
                  <div
                    key={activeUser.user?.id}
                    className='flex items-center gap-3 p-2 rounded-md hover:bg-[#566B46]/10 transition-colors cursor-pointer'
                    onClick={() => handleJumpToUser(activeUser)}
                    title={`Jump to ${activeUser.user?.name}'s cursor`}
                  >
                    <Avatar className='h-8 w-8 ring-0'>
                      <AvatarFallback className='text-sm bg-[#566B46] text-white'>
                        {getUserInitials(activeUser.user?.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className='flex-1'>
                      <div className='text-sm font-medium text-[#566B46]'>
                        {removeEmailDomain(activeUser.user?.name) || 'Unknown User'}
                      </div>
                      <div className='text-xs text-[#566B46]/70'>Active now</div>
                    </div>
                    <div className='w-2 h-2 bg-green-500 rounded-full'></div>
                  </div>
                ))
              ) : !isGuest ? (
                <div className='text-center py-4'>
                  <Users className='h-8 w-8 text-[#566B46]/30 mx-auto mb-2' />
                  <div className='text-sm text-[#566B46]/70'>No other collaborators</div>
                  <div className='text-xs text-[#566B46]/50 mt-1'>Share this canvas to collaborate</div>
                </div>
              ) : null}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CollaborationBox;
