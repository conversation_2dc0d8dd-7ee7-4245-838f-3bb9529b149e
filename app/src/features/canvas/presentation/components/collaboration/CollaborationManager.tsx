/**
 * 👥 Collaboration Manager - UNIFIED CLOUDFLARE WORKERS VERSION
 * @description Manages real-time collaboration features via Cloudflare Workers
 * @responsibility User presence, cursor tracking - unified with canvas sync
 * @ai_context Uses Cloudflare Workers WebSocket instead of WASP for collaboration
 */

import React, { useEffect, useLayoutEffect, useRef, useState } from "react";
import { useAuth } from "wasp/client/auth";
import { useCanvasSync } from "../../hooks/useCanvasSync";

/**
 * 👥 User Info Interface
 */
export interface CollaborationUser {
	userId: number;
	username: string;
	color?: string;
}

/**
 * 👥 Collaboration Manager Props
 */
interface CollaborationManagerProps {
	canvasId: string;
	onUsersChange?: (users: CollaborationUser[]) => void;
	onCursorUpdate?: (data: any) => void;
}

/**
 * 👥 Collaboration Manager Component
 * Manages real-time collaboration features
 */
const CollaborationManager: React.FC<CollaborationManagerProps> = ({
	canvasId,
	onUsersChange,
	onCursorUpdate,
}) => {
	const { data: user } = useAuth();

	// Use the unified Cloudflare Workers system - this handles all collaboration automatically
	const { users, updateCursor, isConnected } = useCanvasSync({
		roomId: canvasId || "default-room",
	});

	// Convert Cloudflare Workers users to the format expected by components
	const activeUsers = users.map((u) => ({
		userId: parseInt(u.user.id),
		username: u.user.name,
		color: u.user.color,
	}));

	// 🔄 Notify parent of user changes - users come from Cloudflare Workers now
	useEffect(() => {
		onUsersChange?.(activeUsers);
	}, [activeUsers, onUsersChange]);

	// 🖱️ Forward cursor updates to parent component (if needed)
	// Note: Cursor tracking is now handled automatically by useCanvasSync
	const handleCursorUpdate = (data: any) => {
		onCursorUpdate?.(data);
	};

	// 📡 Send cursor position updates - now uses Cloudflare Workers
	const sendCursorPosition = (
		position: { x: number; y: number },
		isVisible: boolean = true,
	) => {
		if (isConnected) {
			updateCursor(position);
		}
	};

	// 📡 Send user selection updates - handled by useCanvasSync
	const sendUserSelection = (selectedIds: string[]) => {
		// Selection updates are now handled automatically by useCanvasSync
		console.log(
			"[CollaborationManager] Selection update (handled by useCanvasSync):",
			selectedIds,
		);
	};

	// 📡 Send element updates - handled by useCanvasSync
	const sendElementUpdate = (
		element: any,
		type: string,
		isDragging: boolean = false,
	) => {
		// Element updates are now handled automatically by useCanvasSync
		console.log(
			"[CollaborationManager] Element update (handled by useCanvasSync):",
			element.id,
		);
	};

	// 🚨 Expose collaboration functions globally (for backward compatibility)
	// Note: Most functionality is now handled automatically by useCanvasSync
	useLayoutEffect(() => {
		(window as any).collaboration = {
			sendCursorPosition,
			sendUserSelection,
			sendElementUpdate,
			activeUsers,
			currentUser: user,
			// Expose the unified system functions
			isConnected,
			users,
		};

		return () => {
			delete (window as any).collaboration;
		};
	}, [
		sendCursorPosition,
		sendUserSelection,
		sendElementUpdate,
		activeUsers,
		user,
		isConnected,
		users,
	]);

	// This component doesn't render anything - it just manages collaboration
	return null;
};

export default CollaborationManager;
