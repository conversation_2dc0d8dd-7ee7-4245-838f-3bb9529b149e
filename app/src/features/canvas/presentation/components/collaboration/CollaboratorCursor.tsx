/**
 * 🖱️ Collaborator Cursor - MODULAR VERSION
 * @description Individual cursor component for collaborators
 * @responsibility Display and animate individual user cursors
 * @ai_context Modular replacement for old CollaboratorCursor component
 */

import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { Group, Path, Text, Rect, Line } from 'react-konva';
import Konva from 'konva';

/**
 * 🖱️ Cursor Ref Interface
 */
export interface CursorRef {
  updatePosition: (position: { x: number; y: number }) => void;
}

/**
 * 🖱️ Collaborator Cursor Props
 */
interface CollaboratorCursorProps {
  username: string;
  color: string;
  initialPosition: { x: number; y: number };
  isVisible?: boolean;
}

/**
 * 🖱️ Collaborator Cursor Component
 * Individual cursor component for collaborators
 */
const CollaboratorCursor = forwardRef<CursorRef, CollaboratorCursorProps>(
  ({ username, color, initialPosition, isVisible = true }, ref) => {
    const groupRef = useRef<Konva.Group>(null);
    const tweenRef = useRef<Konva.Tween | null>(null);

    // 🔄 Expose update position function
    useImperativeHandle(ref, () => ({
      updatePosition: (position: { x: number; y: number }) => {
        const group = groupRef.current;
        if (!group) return;

        // Cancel any existing tween
        if (tweenRef.current) {
          tweenRef.current.destroy();
        }

        // Create smooth animation to new position
        tweenRef.current = new Konva.Tween({
          node: group,
          duration: 0.1, // Fast, smooth animation
          x: position.x,
          y: position.y,
          easing: Konva.Easings.EaseOut,
        });

        tweenRef.current.play();
      },
    }));

    // 🚫 Don't render if not visible
    if (!isVisible) {
      return null;
    }

    // 🎨 Cursor design - Using your exact Mac cursor SVG
    const cursorSize = 30;
    const labelPadding = 6;
    const labelHeight = 22;
    const scale = 1.05; // 25% smaller than 1.4 (1.4 * 0.75 = 1.05)

    return (
      <Group
        ref={groupRef}
        x={initialPosition.x}
        y={initialPosition.y}
        listening={false} // Cursors shouldn't interfere with interactions
      >
        {/* Mac cursor - clean colored version */}
        <Group scaleX={scale} scaleY={scale}>
          {/* Colored cursor body */}
          <Path
            data="M9.2,7.3 L9.2,18.5 L12.2,15.6 L12.6,15.5 L17.4,15.5 Z"
            fill={color}
          />

          {/* Cursor line/stem */}
          <Path
            data="M12.5,13.6 L14.5,13.6 L14.5,21.6 L12.5,21.6 Z"
            fill={color}
            rotation={-22.5}
            offsetX={13.5}
            offsetY={17.6}
            x={13.5}
            y={17.6}
          />
        </Group>

        {/* Username label */}
        <Group x={cursorSize + 4} y={-2}>
          {/* Label background */}
          <Rect
            x={0}
            y={0}
            width={Math.max(50, username.length * 7 + labelPadding * 2)}
            height={labelHeight}
            fill={color}
            cornerRadius={4}
            opacity={0.9}
            shadowColor='rgba(0,0,0,0.2)'
            shadowBlur={2}
            shadowOffset={{ x: 1, y: 1 }}
          />

          {/* Username text */}
          <Text
            x={labelPadding}
            y={labelPadding}
            text={username}
            fontSize={11}
            fontFamily='Inter, Arial, sans-serif'
            fill='white'
            fontStyle='500'
            width={Math.max(50, username.length * 7)}
            height={labelHeight - labelPadding * 2}
            align='center'
            verticalAlign='middle'
          />
        </Group>
      </Group>
    );
  }
);

CollaboratorCursor.displayName = 'CollaboratorCursor';

export default CollaboratorCursor;
