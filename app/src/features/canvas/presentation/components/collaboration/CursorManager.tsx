/**
 * 🖱️ Cursor Manager - MODULAR VERSION
 * @description Manages real-time cursor tracking for collaborators
 * @responsibility Display and track other users' cursors on canvas
 * @ai_context Modular replacement for old CursorManager component
 */

import React, { useRef, useCallback } from 'react';
import { Group } from 'react-konva';
import { useAuth } from 'wasp/client/auth';
import CollaboratorCursor, { CursorRef } from './CollaboratorCursor';
import { CollaborationUser } from '../../hooks/useCanvasSync';

/**
 * 🖱️ Cursor Manager Props
 */
interface CursorManagerProps {
  activeUsers: CollaborationUser[];
  currentUserId?: string; // Add current user ID for guest filtering
}

/**
 * 🖱️ Cursor Manager Component
 * Manages real-time cursor tracking for collaborators
 */
const CursorManager: React.FC<CursorManagerProps> = ({ activeUsers, currentUserId }) => {
  const { data: user } = useAuth();
  const actualCurrentUserId = user?.id?.toString() || currentUserId; // Use auth user ID or guest user ID
  const cursorRefs = useRef<Record<string, CursorRef | null>>({});

  // 🔄 Get cursor ref callback
  const getCursorRefCallback = useCallback(
    (userId: string) => (ref: CursorRef | null) => {
      if (ref) {
        cursorRefs.current[userId] = ref;
      } else {
        delete cursorRefs.current[userId];
      }
    },
    []
  );

  return (
    <Group>
      {activeUsers.map((cUser) => {
        // Don't show current user's cursor (works for both auth and guest users)
        if (actualCurrentUserId && cUser.user.id === actualCurrentUserId) {
          return null;
        }

        // Don't show cursors without position data
        if (!cUser.cursor) {
          return null;
        }

        return (
          <CollaboratorCursor
            key={cUser.user.id}
            username={cUser.user.name}
            color={cUser.user.color}
            initialPosition={cUser.cursor || { x: 0, y: 0 }}
            isVisible
            ref={getCursorRefCallback(cUser.user.id)}
          />
        );
      })}
    </Group>
  );
};

export default CursorManager;
