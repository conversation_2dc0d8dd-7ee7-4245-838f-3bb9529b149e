/**
 * 🧭 Off-Canvas User Indicators Component
 * @description Shows arrows pointing to users who are outside the current viewport
 * @responsibility Visual indicators for off-screen collaborators
 * @ai_context Part of the modular collaboration system
 */

import React, { useMemo } from 'react';
import { CollaborationUser } from '../../hooks/useCanvasSync';

/**
 * 🧭 Off-Canvas Indicators Props
 */
interface OffCanvasIndicatorsProps {
  users: CollaborationUser[];
  currentUserId?: string;
  stageWidth: number;
  stageHeight: number;
  stagePosition: { x: number; y: number };
  stageScale: number;
}

/**
 * 🧭 Arrow Position Type
 */
type ArrowPosition = 'top-left' | 'top' | 'top-right' | 'right' | 'bottom-right' | 'bottom' | 'bottom-left' | 'left';

/**
 * 🧭 Off-Canvas User Interface
 */
interface OffCanvasUser {
  user: CollaborationUser;
  position: ArrowPosition;
  screenX: number;
  screenY: number;
}

/**
 * 🧭 OffCanvasIndicators Component
 */
const OffCanvasIndicators: React.FC<OffCanvasIndicatorsProps> = ({
  users,
  currentUserId,
  stageWidth,
  stageHeight,
  stagePosition,
  stageScale,
}) => {
  // 🔍 Calculate which users are off-canvas and their arrow positions
  const offCanvasUsers = useMemo(() => {
    const results: OffCanvasUser[] = [];

    users.forEach((user) => {
      // Skip current user
      if (user.user.id === currentUserId) return;
      
      // Skip users without cursor position
      if (!user.cursor) return;

      // Convert canvas coordinates to screen coordinates
      const screenX = (user.cursor.x * stageScale) + stagePosition.x;
      const screenY = (user.cursor.y * stageScale) + stagePosition.y;

      // Check if user is off-canvas
      const isOffLeft = screenX < 0;
      const isOffRight = screenX > stageWidth;
      const isOffTop = screenY < 0;
      const isOffBottom = screenY > stageHeight;

      // Only show indicator if user is actually off-canvas
      if (!isOffLeft && !isOffRight && !isOffTop && !isOffBottom) return;

      // Determine arrow position based on where user is relative to viewport
      let position: ArrowPosition;
      
      if (isOffLeft && isOffTop) {
        position = 'top-left';
      } else if (isOffRight && isOffTop) {
        position = 'top-right';
      } else if (isOffLeft && isOffBottom) {
        position = 'bottom-left';
      } else if (isOffRight && isOffBottom) {
        position = 'bottom-right';
      } else if (isOffLeft) {
        position = 'left';
      } else if (isOffRight) {
        position = 'right';
      } else if (isOffTop) {
        position = 'top';
      } else {
        position = 'bottom';
      }

      results.push({
        user,
        position,
        screenX,
        screenY,
      });
    });

    return results;
  }, [users, currentUserId, stageWidth, stageHeight, stagePosition, stageScale]);

  // 🎨 Get arrow style based on position
  const getArrowStyle = (offCanvasUser: OffCanvasUser) => {
    const { position, user } = offCanvasUser;
    const color = user.user.color;
    const size = 16; // Smaller arrows
    const margin = 12; // Slightly smaller margin too

    const baseStyle: React.CSSProperties = {
      position: 'absolute',
      width: `${size}px`,
      height: `${size}px`,
      backgroundColor: color,
      clipPath: 'polygon(0% 50%, 100% 0%, 100% 100%)', // Triangle pointing right
      cursor: 'pointer',
      zIndex: 1000,
      transition: 'all 0.2s ease',
      filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))',
    };

    // Position and rotation based on arrow direction
    switch (position) {
      case 'right':
        return {
          ...baseStyle,
          right: `${margin}px`,
          top: '50%',
          transform: 'translateY(-50%)',
        };
      case 'left':
        return {
          ...baseStyle,
          left: `${margin}px`,
          top: '50%',
          transform: 'translateY(-50%) rotate(180deg)',
        };
      case 'top':
        return {
          ...baseStyle,
          top: `${margin}px`,
          left: '50%',
          transform: 'translateX(-50%) rotate(-90deg)',
        };
      case 'bottom':
        return {
          ...baseStyle,
          bottom: `${margin}px`,
          left: '50%',
          transform: 'translateX(-50%) rotate(90deg)',
        };
      case 'top-right':
        return {
          ...baseStyle,
          top: `${margin}px`,
          right: `${margin}px`,
          transform: 'rotate(-45deg)',
        };
      case 'top-left':
        return {
          ...baseStyle,
          top: `${margin}px`,
          left: `${margin}px`,
          transform: 'rotate(-135deg)',
        };
      case 'bottom-right':
        return {
          ...baseStyle,
          bottom: `${margin}px`,
          right: `${margin}px`,
          transform: 'rotate(45deg)',
        };
      case 'bottom-left':
        return {
          ...baseStyle,
          bottom: `${margin}px`,
          left: `${margin}px`,
          transform: 'rotate(135deg)',
        };
      default:
        return baseStyle;
    }
  };

  // 🎯 Handle arrow click - center viewport on user's cursor
  const handleArrowClick = (offCanvasUser: OffCanvasUser) => {
    if (!offCanvasUser.user.cursor) return;

    // Calculate position to center the user's cursor on screen
    const newPosition = {
      x: (stageWidth / 2) - (offCanvasUser.user.cursor.x * stageScale),
      y: (stageHeight / 2) - (offCanvasUser.user.cursor.y * stageScale),
    };

    // Dispatch event to update canvas position
    window.dispatchEvent(
      new CustomEvent('canvas:jumpToPosition', {
        detail: { position: newPosition },
      })
    );
  };

  // Don't render if no off-canvas users
  if (offCanvasUsers.length === 0) return null;

  return (
    <div className="off-canvas-indicators">
      {offCanvasUsers.map((offCanvasUser) => (
        <div
          key={offCanvasUser.user.user.id}
          style={getArrowStyle(offCanvasUser)}
          onClick={() => handleArrowClick(offCanvasUser)}
          title={`Jump to ${offCanvasUser.user.user.name}'s cursor`}
          className="off-canvas-arrow hover:scale-110 hover:brightness-110"
        />
      ))}
    </div>
  );
};

export default OffCanvasIndicators;
