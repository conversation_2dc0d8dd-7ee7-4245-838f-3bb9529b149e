/**
 * 🖱️ Context Menu Provider - MODULAR VERSION
 * @description Manages context menu state and coordinates menu display
 * @responsibility Context menu state management and event coordination
 * @ai_context Modular replacement for KonvaCanvasWithModal context menu logic
 */

import React, { useEffect, useState } from 'react';
import { Edge } from '../../../domain/types';
import { useElementDomainStore } from '../../../domain/state';
import { useAction } from 'wasp/client/operations';
import { createCanvasReference, deleteCanvasReference } from 'wasp/client/operations';
import ImageContextMenu from './ImageContextMenu';

/**
 * 🖱️ Context Menu Data Interface
 */
interface ContextMenuData {
  id: string;
  src: string;
  width: number;
  height: number;
  isReference?: boolean;
  connectedEdges?: Edge[];
}

/**
 * 🖱️ ContextMenuProvider Props
 */
interface ContextMenuProviderProps {
  children?: React.ReactNode;
}

/**
 * 🖱️ ContextMenuProvider Component
 * Manages context menu state and coordinates menu display
 */
const ContextMenuProvider: React.FC<ContextMenuProviderProps> = ({ children }) => {
  const [contextMenuVisible, setContextMenuVisible] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
  const [contextMenuData, setContextMenuData] = useState<ContextMenuData | null>(null);

  // Get domain state and actions
  const images = useElementDomainStore((state) => state.images);
  const updateImage = useElementDomainStore((state) => state.updateImage);
  const removeImage = useElementDomainStore((state) => state.removeImage);

  // Canvas reference actions
  const createCanvasReferenceAction = useAction(createCanvasReference);
  const deleteCanvasReferenceAction = useAction(deleteCanvasReference);

  // 🔄 Listen for context menu events - FOLLOWING useEffect RULES
  // ✅ This IS appropriate for useEffect: synchronizing with external system (custom events)
  useEffect(() => {
    // 📋 Handle show context menu events
    const handleShowMenu = (event: Event) => {
      const customEvent = event as CustomEvent;
      const { id, src, width, height, pointerPosition, connectedEdges } = customEvent.detail;

      console.log('[ContextMenuProvider] Show menu event received:', { id, src, width, height });

      // Find image in store to get reference status
      const imageFromStore = images.find((img) => img.id === id);

      setContextMenuPosition(pointerPosition);
      setContextMenuData({
        id,
        src,
        width,
        height,
        isReference: imageFromStore?.isReference || false,
        connectedEdges: connectedEdges || [],
      });
      setContextMenuVisible(true);
    };

    // 🙈 Handle hide context menu events
    const handleHideMenu = () => {

      setContextMenuVisible(false);
    };

    // 👥 Handle set group as reference events
    const handleSetGroupAsReference = (event: Event) => {
      const customEvent = event as CustomEvent;
      const { imageId } = customEvent.detail;

      console.log('[ContextMenuProvider] Set group as reference event received:', imageId);

      // Get all connected images from snap connections
      const allConnections = (window as any).snapConnections?.getAllConnections() || [];
      const connectedImageIds = new Set<string>();

      // Find all images connected to this one
      const findConnectedImages = (startId: string, visited = new Set<string>()) => {
        if (visited.has(startId)) return;
        visited.add(startId);
        connectedImageIds.add(startId);

        allConnections.forEach((conn: any) => {
          if (conn.nodeId === startId && !visited.has(conn.targetNodeId)) {
            findConnectedImages(conn.targetNodeId, visited);
          } else if (conn.targetNodeId === startId && !visited.has(conn.nodeId)) {
            findConnectedImages(conn.nodeId, visited);
          }
        });
      };

      findConnectedImages(imageId);

      // Set all connected images as references
      connectedImageIds.forEach((id) => {
        const image = images.find((img) => img.id === id);
        if (image) {
          updateImage(id, { ...image, isReference: true });
        }
      });

      console.log(`[ContextMenuProvider] Set ${connectedImageIds.size} connected images as references`);
    };

    // 📝 Register event listeners
    window.addEventListener('canvas:showImageContextMenu', handleShowMenu);
    window.addEventListener('canvas:hideImageContextMenu', handleHideMenu);
    window.addEventListener('canvas:setGroupAsReference', handleSetGroupAsReference);

    // 🖱️ General click listener to hide menu when clicking outside
    const handleDocumentClick = (event: MouseEvent) => {
      if (contextMenuVisible) {
        const menuEl = document.querySelector('.image-context-menu');
        if (menuEl && menuEl.contains(event.target as Node)) {
          return; // Click is inside context menu; ignore
        }
        setContextMenuVisible(false);
      }
    };

    document.addEventListener('click', handleDocumentClick);

    // Removed spammy setup log

    // 🧹 Cleanup
    return () => {
      window.removeEventListener('canvas:showImageContextMenu', handleShowMenu);
      window.removeEventListener('canvas:hideImageContextMenu', handleHideMenu);
      window.removeEventListener('canvas:setGroupAsReference', handleSetGroupAsReference);
      document.removeEventListener('click', handleDocumentClick);
      // Removed spammy cleanup log
    };
  }, [contextMenuVisible, images, updateImage]);

  // ⭐ Handle set as reference
  const handleSetAsReference = async (imageId: string) => {
    console.log('[ContextMenuProvider] Set as reference:', imageId);

    const image = images.find((img) => img.id === imageId);
    if (image) {
      // Toggle reference status in domain state
      const newReferenceStatus = !image.isReference;
      updateImage(imageId, { ...image, isReference: newReferenceStatus });

      // Also add/remove from canvas references for AI generation
      const selectedProductId = localStorage.getItem('selectedModelId') || '';

      if (newReferenceStatus) {
        // Add to canvas references
        try {
          await createCanvasReferenceAction({
            whiteboardId: selectedProductId,
            thumbUrl: image.url,
          });
          console.log(`[ContextMenuProvider] Added image ${imageId} to canvas references`);
        } catch (error) {
          console.error(`[ContextMenuProvider] Error adding image to canvas references:`, error);
        }
      } else {
        // Remove from canvas references - we'd need to find the reference by URL first
        // For now, just log that we'd remove it
        console.log(`[ContextMenuProvider] Would remove image ${imageId} from canvas references`);
        // TODO: Implement removal by finding the reference with matching thumbUrl
      }

      console.log(`[ContextMenuProvider] Image ${imageId} reference status: ${newReferenceStatus}`);
    }
  };

  // 🗑️ Handle remove image
  const handleRemoveImage = (imageId: string) => {
    console.log('[ContextMenuProvider] Remove image:', imageId);

    // Remove from snap connections first
    if ((window as any).snapConnections) {
      (window as any).snapConnections.removeConnections(imageId);
    }

    // Remove from domain state
    removeImage(imageId);

    console.log(`[ContextMenuProvider] Image ${imageId} removed`);
  };

  return (
    <>
      {children}

      {/* 🖱️ Render context menu */}
      {contextMenuVisible && contextMenuData && (
        <ImageContextMenu
          isVisible={contextMenuVisible}
          position={contextMenuPosition}
          imageId={contextMenuData.id}
          imageSrc={contextMenuData.src}
          imageWidth={contextMenuData.width}
          imageHeight={contextMenuData.height}
          isReference={contextMenuData.isReference}
          connectedEdges={contextMenuData.connectedEdges}
          onSetAsReference={handleSetAsReference}
          onRemoveImage={handleRemoveImage}
          onClose={() => setContextMenuVisible(false)}
        />
      )}
    </>
  );
};

export default ContextMenuProvider;
