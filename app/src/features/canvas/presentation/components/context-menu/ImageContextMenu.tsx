/**
 * 🖱️ Image Context Menu - MODULAR VERSION
 * @description Right-click context menu for images
 * @responsibility Display context menu options for image actions
 * @ai_context Migrated from old ImageContextMenu.tsx with modular architecture
 */

import React, { useLayoutEffect, useRef, useState } from 'react';
import { Star, Trash, Link2Off, ChevronRight, Download, Users } from 'lucide-react';
import { useCanvasToolbarStore } from '../../../domain/state';
import { Edge } from '../../../domain/types';

/**
 * 🖱️ ImageContextMenu Props
 */
interface ImageContextMenuProps {
  isVisible: boolean;
  position: { x: number; y: number };
  imageId: string;
  imageSrc: string;
  onSetAsReference: (imageId: string) => void;
  onRemoveImage: (imageId: string) => void;
  imageWidth: number;
  imageHeight: number;
  onClose: () => void;
  isReference?: boolean;
  connectedEdges?: Edge[];
}

/**
 * 🖱️ ImageContextMenu Component
 * Right-click context menu for images
 */
const ImageContextMenu: React.FC<ImageContextMenuProps> = ({
  isVisible,
  position,
  imageId,
  imageSrc,
  onSetAsReference,
  onRemoveImage,
  imageWidth,
  imageHeight,
  onClose,
  isReference = false,
  connectedEdges = [],
}) => {
  const menuRef = useRef<HTMLDivElement>(null);
  const [showSeparateOptions, setShowSeparateOptions] = useState(false);

  // Get the active tool from the domain state
  const activeTool = useCanvasToolbarStore((state) => state.activeTool);

  // 📐 Position the menu to avoid going off-screen
  useLayoutEffect(() => {
    if (!isVisible || !menuRef.current) return;

    const menu = menuRef.current;
    const rect = menu.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let left = position.x;
    let top = position.y;

    // Adjust horizontal position if menu would go off-screen
    if (left + rect.width > viewportWidth) {
      left = viewportWidth - rect.width - 10;
    }

    // Adjust vertical position if menu would go off-screen
    if (top + rect.height > viewportHeight) {
      top = viewportHeight - rect.height - 10;
    }

    // Ensure menu doesn't go off the left or top edge
    left = Math.max(10, left);
    top = Math.max(10, top);

    // Apply the calculated position
    menu.style.left = `${left}px`;
    menu.style.top = `${top}px`;
  }, [isVisible, position.x, position.y]);

  // 🔗 Handle separating connected edges
  const handleSeparateClick = (edgeToUnsnap: Edge) => {
    console.log(`[ImageContextMenu] Separating edge: ${edgeToUnsnap} for image: ${imageId}`);

    // Use the global snap connections to remove the connection
    if ((window as any).snapConnections) {
      (window as any).snapConnections.removeConnections(imageId, edgeToUnsnap);
    }

    // Also dispatch the old event for compatibility
    window.dispatchEvent(
      new CustomEvent('canvas:unsnapImageEdge', {
        detail: { nodeId: imageId, edge: edgeToUnsnap },
      })
    );

    setShowSeparateOptions(false);
    onClose();
  };

  // 📤 Handle export group as image
  const handleExportGroup = () => {
    console.log('[ImageContextMenu] Export Group as Image clicked for imageId:', imageId);
    window.dispatchEvent(
      new CustomEvent('canvas:exportGroupAsImage', {
        detail: { imageId: imageId },
      })
    );
    onClose();
  };

  // ⭐ Handle set group as reference
  const handleSetGroupAsReference = () => {
    console.log('[ImageContextMenu] Set Group as Reference clicked for imageId:', imageId);
    window.dispatchEvent(
      new CustomEvent('canvas:setGroupAsReference', {
        detail: { imageId: imageId },
      })
    );
    onClose();
  };

  // 🔄 Toggle separate options submenu
  const toggleSeparateOptions = (e: React.MouseEvent) => {
    e.stopPropagation();
    (e.nativeEvent as any).stopImmediatePropagation?.();
    console.log('[ImageContextMenu] Toggle Separate clicked');
    setShowSeparateOptions((prev) => !prev);
  };

  // 🚫 Don't render the menu when in mask drawing mode
  if (!isVisible || activeTool === 'mask') return null;

  const hasConnections = connectedEdges && connectedEdges.length > 0;

  // 🔤 Capitalize helper
  const capitalize = (s: string) => s.charAt(0).toUpperCase() + s.slice(1);

  return (
    <div
      ref={menuRef}
      className='image-context-menu fixed bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700'
      style={{
        left: position.x + 'px',
        top: position.y + 'px',
        minWidth: '180px',
        zIndex: 1010,
      }}
    >
      <ul className='py-1'>
        {/* ⭐ Set as Reference */}
        <li>
          <button
            className='w-full px-3 py-2 text-left flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-sm dark:text-gray-200'
            onClick={(e) => {
              e.stopPropagation();
              onSetAsReference(imageId);
              onClose();
            }}
          >
            <Star
              size={16}
              className={
                isReference
                  ? 'text-yellow-500 dark:text-yellow-400 fill-yellow-500 dark:fill-yellow-400'
                  : 'dark:text-gray-300'
              }
            />
            {isReference ? 'Remove Reference' : 'Set as Reference'}
          </button>
        </li>

        {/* 🔗 Separate (if has connections) */}
        {hasConnections && (
          <li className='relative'>
            <button
              className='w-full px-3 py-2 text-left flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-sm dark:text-gray-200'
              onClick={toggleSeparateOptions}
            >
              <Link2Off size={16} className='dark:text-gray-300' />
              Separate
              <ChevronRight size={14} className='ml-auto dark:text-gray-400' />
            </button>

            {/* 📋 Separate submenu */}
            {showSeparateOptions && (
              <div className='absolute left-full top-0 ml-1 bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 min-w-[120px] z-10'>
                <ul className='py-1'>
                  {connectedEdges.map((edge) => (
                    <li key={edge}>
                      <button
                        className='w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-sm dark:text-gray-200'
                        onClick={() => handleSeparateClick(edge)}
                      >
                        {capitalize(edge)}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </li>
        )}

        {/* 👥 Set Group as Reference (if has connections) */}
        {hasConnections && (
          <li>
            <button
              className='w-full px-3 py-2 text-left flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-sm dark:text-gray-200'
              onClick={handleSetGroupAsReference}
            >
              <Users size={16} className='dark:text-gray-300' />
              Set Group as Reference
            </button>
          </li>
        )}

        {/* 📤 Export Group as Image (if has connections) */}
        {hasConnections && (
          <li>
            <button
              className='w-full px-3 py-2 text-left flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-sm dark:text-gray-200'
              onClick={handleExportGroup}
            >
              <Download size={16} className='dark:text-gray-300' />
              Export Group as Image
            </button>
          </li>
        )}

        {/* 🗑️ Remove Image */}
        <li className='border-t border-gray-200 dark:border-gray-600 mt-1 pt-1'>
          <button
            className='w-full px-3 py-2 text-left flex items-center gap-2 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors text-sm text-red-600 dark:text-red-400'
            onClick={(e) => {
              e.stopPropagation();
              onRemoveImage(imageId);
              onClose();
            }}
          >
            <Trash size={16} />
            Remove Image
          </button>
        </li>
      </ul>
    </div>
  );
};

export default ImageContextMenu;
