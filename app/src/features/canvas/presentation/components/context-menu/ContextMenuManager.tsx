/**
 * 🖱️ Context Menu Manager - MODULAR VERSION
 * @description Handles right-click detection and context menu events
 * @responsibility Detect right-clicks on images and dispatch context menu events
 * @ai_context Modular replacement for BaseScene context menu handling
 */

import React, { useEffect, useRef } from "react";
import Konva from "konva";
import { useCanvasToolbarStore } from "../../../domain/state";
import { Edge } from "../../../domain/types";

/**
 * 🖱️ ContextMenuManager Props
 */
interface ContextMenuManagerProps {
	stageRef: React.RefObject<Konva.Stage>;
}

/**
 * 🖱️ ContextMenuManager Component
 * Handles right-click detection and context menu events
 */
const ContextMenuManager: React.FC<ContextMenuManagerProps> = ({
	stageRef,
}) => {
	const activeTool = useCanvasToolbarStore((state) => state.activeTool);

	// 🔄 Set up context menu detection - FOLLOWING useEffect RULES
	// ✅ This IS appropriate for useEffect: synchronizing with external system (Konva events)
	useEffect(() => {
		const stage = stageRef.current;
		if (!stage) return;

		// 🖱️ Handle right-click context menu events
		const handleContextMenu = (e: Konva.KonvaEventObject<MouseEvent>) => {
			e.evt.preventDefault(); // Prevent default browser context menu

			const rawTargetNode = e.target;
			let nodeForContext: Konva.Node | null = null;

			// 🎯 Determine the context node (similar to old BaseScene logic)
			if (rawTargetNode.className === "Image") {
				// Direct click on Image - find parent Group (URLImage)
				const parentGroup = rawTargetNode.getParent();
				if (parentGroup && parentGroup.id()) {
					nodeForContext = parentGroup;
					console.log(
						`[ContextMenuManager] Image click, using parent Group: ${parentGroup.id()}`,
					);
				}
			} else if (rawTargetNode.className === "Group" && rawTargetNode.id()) {
				// Direct click on Group
				nodeForContext = rawTargetNode;
				console.log(`[ContextMenuManager] Group click: ${rawTargetNode.id()}`);
			} else {
				// Click on unhandled target - hide menu

				window.dispatchEvent(new CustomEvent("canvas:hideImageContextMenu"));
				return;
			}

			if (!nodeForContext || !nodeForContext.id()) {
				console.log(
					"[ContextMenuManager] No identifiable node for context menu. Hiding menu.",
				);
				window.dispatchEvent(new CustomEvent("canvas:hideImageContextMenu"));
				return;
			}

			// 🚫 Don't show context menu in mask mode
			if (activeTool === "mask") {
				console.log("[ContextMenuManager] In mask mode, hiding context menu");
				window.dispatchEvent(new CustomEvent("canvas:hideImageContextMenu"));
				return;
			}

			// 🔍 Check for HTMLElement groups (let them handle their own context menu)
			if (
				rawTargetNode.findAncestor &&
				rawTargetNode.findAncestor(".html-element-konva-group")
			) {
				console.log(
					"[ContextMenuManager] Context menu on HTMLElement, letting HTMLElement handle it.",
				);
				return;
			}

			e.evt.stopPropagation(); // Stop propagation since we're handling it

			const pointerPosition = stage.getPointerPosition();
			if (!pointerPosition) {
				console.error("[ContextMenuManager] No pointer position.");
				return;
			}

			// 🔗 Get connected edges from snap connections
			const activeNodeId = nodeForContext.id();
			const imageConnectedEdges = new Set<Edge>();

			// Get connections from global snap connections
			const allConnections =
				(window as any).snapConnections?.getAllConnections() || [];
			allConnections.forEach((conn: any) => {
				if (conn.nodeId === activeNodeId) {
					imageConnectedEdges.add(conn.nodeEdge);
				}
				if (conn.targetNodeId === activeNodeId) {
					imageConnectedEdges.add(conn.targetEdge);
				}
			});

			// 🎨 Get display details from the node
			let displaySrc = "";
			let displayWidth = nodeForContext.width();
			let displayHeight = nodeForContext.height();
			const isReference = (nodeForContext as any).isReference || false;

			// If it's a Group (URLImage), get details from inner Image
			if (nodeForContext.className === "Group") {
				const group = nodeForContext as Konva.Group;
				const innerImage = group.findOne("Image") as Konva.Image;
				if (innerImage && innerImage.image()) {
					const imageElement = innerImage.image() as HTMLImageElement;
					displaySrc = imageElement.src || "";
					// Use group dimensions for display
					displayWidth = nodeForContext.width();
					displayHeight = nodeForContext.height();
				}
			} else if (nodeForContext.className === "Image") {
				const konvaImage = nodeForContext as Konva.Image;
				if (konvaImage.image()) {
					const imageElement = konvaImage.image() as HTMLImageElement;
					displaySrc = imageElement.src || "";
				}
				displayWidth = konvaImage.width();
				displayHeight = konvaImage.height();
			}

			// 📋 Prepare context menu details
			const imageDetails = {
				id: activeNodeId,
				src: displaySrc,
				width: displayWidth,
				height: displayHeight,
				konvaX: nodeForContext.x(),
				konvaY: nodeForContext.y(),
				pointerPosition: { x: pointerPosition.x, y: pointerPosition.y },
				connectedEdges: Array.from(imageConnectedEdges),
				isReference: isReference,
			};

			console.log(
				"[ContextMenuManager] Dispatching canvas:showImageContextMenu",
				imageDetails,
			);

			// 📡 Dispatch context menu event
			window.dispatchEvent(
				new CustomEvent("canvas:showImageContextMenu", {
					detail: imageDetails,
				}),
			);
		};

		// 📝 Register context menu listener on stage
		stage.on("contextmenu", handleContextMenu);

		// Removed spammy setup log

		// 🧹 Cleanup
		return () => {
			stage.off("contextmenu", handleContextMenu);
			// Removed spammy cleanup log
		};
	}, [activeTool]);

	// This component doesn't render anything - it just manages context menu detection
	return null;
};

export default ContextMenuManager;
