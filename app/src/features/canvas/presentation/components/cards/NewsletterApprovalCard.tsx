import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Group, Rect, Text } from 'react-konva';
import { Html } from 'react-konva-utils';
import {
  NewsletterApprovalCardDataType,
  NewsletterOutline,
  NewsletterOutlineSection,
  // 🧠 MIGRATED: Using modular types instead of client types
} from '../../../domain/types';
import Konva from 'konva'; // Import Konva namespace for type usage
import { submitNewsletterOutlineFeedback, updateNewsletterOutlineDirectly } from 'wasp/client/operations'; // Import the new Wasp actions

// Tech Meets Nature Theme Colors (approximations)
const THEME = {
  cardBackground: '#F5F3ED', // Soft, off-white beige
  primaryText: '#4A5D23', // Dark, desaturated olive green
  headingText: '#3B4A1E', // Slightly darker olive for headings
  accentOchre: '#C0A062', // Muted ochre/gold
  accentGreen: '#5E6C3B', // Medium olive green for secondary actions/borders
  buttonTextLight: '#F5F3ED', // For text on dark buttons (matches card background for harmony)
  buttonTextDark: '#3B4A1E', // For text on light buttons
  inputBorder: '#A0A889', // Muted green for input borders
  inputBackground: '#FFFFFF', // White, or a very light tint of cardBackground
  subtleBorder: '#D8D5CC', // Lighter border for dividers
  dragHandleBackground: '#E4E0D7', // Slightly darker beige for drag handle
  statusFailed: '#A75A5A', // Muted red for failure status
  statusCompleted: '#5E6C3B', // Accent green for success status (matches button)
  buttonSuggestChangesBg: '#6A7A4B', // Darker olive for suggest changes button
  buttonCancelBg: '#B0B89F', // Muted, lighter green-gray for cancel
};

interface NewsletterApprovalCardProps {
  cardData: NewsletterApprovalCardDataType;
  onApprove?: (params: { taskId: string; outline: NewsletterOutline }) => void;
  onSubmitFeedback?: (params: { taskId: string; feedback: string }) => void;
}

const CARD_WIDTH_DEFAULT = 420;
const CARD_HEIGHT_MIN = 200;
const CARD_MAX_VISIBLE_HEIGHT = 450;
const PADDING = 15;
const DRAG_HANDLE_HEIGHT = 20; // Height for the new draggable top bar
const HEADER_AREA_HEIGHT = 0; // For the title "Newsletter Outline for Approval" - REMOVED
const BUTTON_AREA_HEIGHT = 40;
const FEEDBACK_TEXTAREA_HEIGHT = 70;
const SECTION_SPACING = 10;
const LINE_HEIGHT = 1.5;
const FONT_SIZE_DESC = 12;
const FONT_SIZE_HEADING = 14;
const FONT_SIZE_DETAIL = 11;
const DETAIL_LINE_HEIGHT = 1.3;
const FONT_SIZE_STATUS = 13;

const NewsletterApprovalCard: React.FC<NewsletterApprovalCardProps> = ({ cardData, onApprove, onSubmitFeedback }) => {
  const { id, taskId, threadId, outline, outlineText, position, status, gmailMockUrl } = cardData;

  const [showFeedbackInput, setShowFeedbackInput] = useState(false);
  const [cardWidth, setCardWidth] = useState(CARD_WIDTH_DEFAULT);
  const [feedbackText, setFeedbackText] = useState('');
  const [currentPosition, setCurrentPosition] = useState({ x: position.x - 10, y: position.y });
  const [editableOutline, setEditableOutline] = useState<NewsletterOutline | undefined>(outline);
  const [manualHeight, setManualHeight] = useState<number | null>(null);
  // 🔧 LOCAL STATE: Track if user clicked approve (for immediate UI feedback)
  const [userClickedApprove, setUserClickedApprove] = useState(false);

  // ✅ RESET STATE DURING RENDERING: Reset userClickedApprove when task completes/fails
  const [prevStatus, setPrevStatus] = useState(status);
  if (status !== prevStatus) {
    setPrevStatus(status);
    if (status === 'completed' || status === 'failed') {
      setUserClickedApprove(false);
    }
  }

  // ✅ CALCULATE DURING RENDERING: Determine if we're generating
  const isGenerating =
    status === 'generating_html' || (userClickedApprove && status !== 'completed' && status !== 'failed');

  const groupRef = useRef<Konva.Group>(null);
  const scrollableContentRef = useRef<HTMLDivElement>(null); // UNCOMMENTED: This ref IS used
  const processingTransformEndRef = useRef(false); // Re-entrancy guard
  // Refs for manual drag calculation of the group position based on header drag - NO LONGER NEEDED for this new approach
  // const dragStartPointerPos = useRef<{x: number, y: number} | null>(null);
  // const initialGroupPosForDrag = useRef<{x: number, y: number} | null>(null);

  useEffect(() => {
    // Initialize editableOutline when the cardData.outline changes or on initial load
    setEditableOutline(cardData.outline);
  }, [cardData.outline]);

  // Calculate actualDisplayHeight using useMemo
  const actualDisplayHeight = useMemo(() => {
    let calculatedContentRequiredHeight: number;
    console.log('[NAC heightEffect START]', { manualHeightIn: manualHeight, cardWidthIn: cardWidth, statusIn: status });

    if (editableOutline && editableOutline.title && editableOutline.sections) {
      let estimatedContentHeight = PADDING; // Top padding for the title within its area
      const titleLines = Math.ceil((editableOutline.title?.length || 0) / (cardWidth / 8));
      estimatedContentHeight += titleLines * (FONT_SIZE_HEADING + 4) * LINE_HEIGHT;
      estimatedContentHeight += PADDING; // Space after title

      editableOutline.sections.forEach((section) => {
        estimatedContentHeight +=
          (section.heading.length / (cardWidth / 7)) * FONT_SIZE_HEADING * LINE_HEIGHT +
          (section.copyIdea.length / (cardWidth / 6)) * FONT_SIZE_DETAIL * DETAIL_LINE_HEIGHT +
          PADDING / 2 +
          (section.visualDesignIdea.length / (cardWidth / 6)) * FONT_SIZE_DETAIL * DETAIL_LINE_HEIGHT +
          PADDING / 2 +
          (section.description.length / (cardWidth / 6.5)) * FONT_SIZE_DESC * LINE_HEIGHT +
          SECTION_SPACING;
      });
      calculatedContentRequiredHeight = DRAG_HANDLE_HEIGHT + estimatedContentHeight + BUTTON_AREA_HEIGHT + PADDING;

      if (status === 'completed' || status === 'failed') {
        // If completed or failed, we might not show the feedback input, or direct edit controls, so don't add its height for these.
      } else if (showFeedbackInput) {
        calculatedContentRequiredHeight += FEEDBACK_TEXTAREA_HEIGHT + PADDING;
      }
      // Add a bit more space if we are in editing mode for the "Save Direct Edits" button
      if (status !== 'completed' && status !== 'failed' && !showFeedbackInput) {
        calculatedContentRequiredHeight += BUTTON_AREA_HEIGHT + PADDING / 2; // Space for the new save button row
      }
    } else {
      // Outline not ready, or card is in a basic loading state.
      // Default to CARD_HEIGHT_MIN for the content-based required height in this scenario.
      calculatedContentRequiredHeight = CARD_HEIGHT_MIN;
    }

    // If manualHeight is set by the user, it takes precedence.
    // Otherwise, fall back to the height required by the content.
    const targetHeight = manualHeight !== null ? manualHeight : calculatedContentRequiredHeight;
    console.log('[NAC heightEffect CALCS]', {
      calculatedContentRequiredHeight,
      manualHeightInEffect: manualHeight,
      chosenTargetHeight: targetHeight,
    });

    // Apply overall card constraints (min/max possible card height)
    const newActual = Math.max(CARD_HEIGHT_MIN, targetHeight);
    console.log('[NAC heightEffect SET (useMemo)]', {
      newActualFromCalcs: newActual,
      finalActualDisplayHeight: newActual,
    });
    return newActual;
  }, [editableOutline, showFeedbackInput, status, cardWidth, manualHeight]);

  // Mark this group so the canvas can detect it for resizing
  useEffect(() => {
    if (groupRef.current) {
      groupRef.current.setAttr('isNewsletterCard', true);
    }
  }, []);

  const handleApproveClick = (e: React.MouseEvent) => {
    e.stopPropagation();

    if (editableOutline && onApprove) {
      // ✅ IMMEDIATE UI UPDATE: Set user clicked approve for immediate UI feedback
      console.log('[NewsletterApprovalCard] User clicked approve - setting state for immediate UI feedback');
      setUserClickedApprove(true);

      onApprove({ taskId, outline: editableOutline });
    } else {
      console.error(
        '[NewsletterApprovalCard] Cannot approve: editableOutline data is missing or onApprove prop not provided.'
      );
    }
  };
  const handleSuggestChangesClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowFeedbackInput(true);
  };
  const handleSubmitFeedbackClick = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!feedbackText.trim()) return;
    // Call the Wasp action directly, no longer relying on onSubmitFeedback prop from parent for this specific action.
    // The onSubmitFeedback prop (if provided) could be for other notifications.
    try {
      await submitNewsletterOutlineFeedback({ taskId, feedback: feedbackText });
      setFeedbackText('');
      setShowFeedbackInput(false);
      console.log(`[NewsletterApprovalCard] Feedback submitted for task ${taskId}`);
      // If an onSubmitFeedback prop IS provided from parent, call it for notification purposes.
      if (onSubmitFeedback) {
        onSubmitFeedback({ taskId, feedback: feedbackText });
      }
    } catch (error) {
      console.error(`[NewsletterApprovalCard] Error submitting feedback for task ${taskId}:`, error);
      alert(`Error submitting feedback: ${(error as Error).message}`);
    }
  };

  const handleSaveDirectEditsClick = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (editableOutline && taskId) {
      console.log(
        '[NewsletterApprovalCard] Attempting to save directly edited outline:',
        JSON.stringify(editableOutline, null, 2)
      );
      try {
        await updateNewsletterOutlineDirectly({ taskId, editedOutline: editableOutline });
        console.log('[NewsletterApprovalCard] Successfully saved directly edited outline for task:', taskId);
        alert('Outline changes saved successfully!');
        // Optionally, you might want to refresh or indicate saved status visually
      } catch (error) {
        console.error(`[NewsletterApprovalCard] Error saving directly edited outline for task ${taskId}:`, error);
        alert(`Error saving outline changes: ${(error as Error).message}`);
      }
    } else {
      console.error('[NewsletterApprovalCard] No editable outline data or taskId to save.');
      alert('Cannot save: Outline data or task ID is missing.');
    }
  };

  const handleOutlineInputChange = (field: keyof NewsletterOutline, value: string) => {
    setEditableOutline((prev) => (prev ? { ...prev, [field]: value } : undefined));
  };

  const handleSectionInputChange = (sectionIndex: number, field: keyof NewsletterOutlineSection, value: string) => {
    setEditableOutline((prev) => {
      if (!prev) return undefined;
      const newSections = [...prev.sections];
      newSections[sectionIndex] = { ...newSections[sectionIndex], [field]: value };
      return { ...prev, sections: newSections };
    });
  };

  const handleDragBarDragStart = (e: Konva.KonvaEventObject<DragEvent>) => {
    // This function will now be for the GROUP
    const stage = e.target.getStage();
    if (stage && groupRef.current) {
      // dragStartPointerPos.current = stage.getPointerPosition(); // Not needed for group's internal drag
      // initialGroupPosForDrag.current = groupRef.current.position(); // Not needed
      groupRef.current.moveToTop();
      if (stage.container()) stage.container().style.cursor = 'grabbing';
    }
  };

  // const handleDragBarDragMove = (e: Konva.KonvaEventObject<DragEvent>) => { // No longer needed if group handles its own move
  //   if (dragStartPointerPos.current && initialGroupPosForDrag.current && groupRef.current) {
  //     const stage = e.target.getStage();
  //     const currentPointerPos = stage?.getPointerPosition();
  //     if (!currentPointerPos || !dragStartPointerPos.current || !initialGroupPosForDrag.current) return;
  //     const dx = currentPointerPos.x - dragStartPointerPos.current.x;
  //     const dy = currentPointerPos.y - dragStartPointerPos.current.y;
  //     setCurrentPosition({
  //       x: initialGroupPosForDrag.current.x + dx,
  //       y: initialGroupPosForDrag.current.y + dy,
  //     });
  //   }
  // };

  const handleDragBarDragEnd = (e: Konva.KonvaEventObject<DragEvent>) => {
    // This function will now be for the GROUP
    // dragStartPointerPos.current = null; // Not needed
    // initialGroupPosForDrag.current = null; // Not needed
    const stage = e.target.getStage();
    if (stage && stage.container()) stage.container().style.cursor = 'default';
    // Update state with the final position from the group itself
    setCurrentPosition({
      x: e.target.x(),
      y: e.target.y(),
    });
  };

  const handleScrollableWheel = (e: React.WheelEvent<HTMLDivElement>) => {
    e.stopPropagation();
  };
  const handleScrollableMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
  };

  const portalDivStyle: React.CSSProperties = {
    width: `${cardWidth}px`,
    height: `${actualDisplayHeight}px`,
    display: 'flex',
    flexDirection: 'column',
    pointerEvents: 'none',
    backgroundColor: THEME.cardBackground, // Apply theme background to the HTML portal wrapper
    // borderRadius: '8px', // Match Konva Rect rounding if needed, but might be clipped by Konva Rect
  };

  const scrollableContentStyle: React.CSSProperties = {
    // Height calculation needs to account for DRAG_HANDLE_HEIGHT and HEADER_AREA_HEIGHT
    height: `${actualDisplayHeight - DRAG_HANDLE_HEIGHT - BUTTON_AREA_HEIGHT - PADDING}px`, // REMOVED HEADER_AREA_HEIGHT
    overflowY: 'auto',
    overflowX: 'hidden',
    padding: PADDING,
    // Content starts below the Konva Header Area, which is below the Drag Handle
    paddingTop: PADDING / 2,
    boxSizing: 'border-box',
    outline: 'none',
    pointerEvents: 'auto',
    userSelect: 'text',
    WebkitUserSelect: 'text',
  };

  const controlsContainerStyle: React.CSSProperties = {
    padding: `0 ${PADDING}px ${PADDING}px ${PADDING}px`,
    width: `100%`,
    boxSizing: 'border-box',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'stretch',
    pointerEvents: 'auto', // <<<< Controls ARE interactive
    marginTop: 'auto',
    // backgroundColor: THEME.cardBackground, // Ensure controls area matches card bg
  };

  const buttonStyle: React.CSSProperties = {
    padding: '10px 15px', // Increased padding
    borderRadius: '6px', // Slightly more pronounced rounding
    border: 'none',
    cursor: 'pointer',
    fontSize: '13px',
    fontWeight: '500', // Medium weight for button text
    transition: 'background-color 0.2s ease, box-shadow 0.2s ease', // Smooth transitions
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)', // Subtle shadow for depth
    marginBottom: showFeedbackInput ? `${PADDING}px` : '0px',
    width: '100%',
  };

  const dualButtonContainerStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%',
  };

  const dualButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    width: `calc(50% - ${PADDING / 2}px)`,
    marginBottom: '0px',
  };

  const textareaStyle: React.CSSProperties = {
    // For feedback input
    width: '100%',
    height: `${FEEDBACK_TEXTAREA_HEIGHT}px`,
    padding: '10px', // Increased padding
    borderRadius: '6px',
    border: `1px solid ${THEME.inputBorder}`,
    backgroundColor: THEME.inputBackground,
    color: THEME.primaryText,
    fontSize: '13px',
    marginBottom: `${PADDING}px`,
    boxSizing: 'border-box',
    resize: 'none',
    fontFamily: 'sans-serif', // Explicitly set a common sans-serif
  };

  const inputFieldStyle: React.CSSProperties = {
    // For editable title and headings
    width: '100%',
    padding: '8px 10px', // Adjusted padding
    borderRadius: '6px',
    border: `1px solid ${THEME.inputBorder}`,
    backgroundColor: THEME.inputBackground,
    color: THEME.primaryText,
    fontSize: 'inherit',
    marginBottom: `${PADDING / 2}px`,
    boxSizing: 'border-box',
    fontFamily: 'sans-serif',
  };

  const sectionTextareaStyle: React.CSSProperties = {
    // For editable copy idea, visual idea, description
    ...inputFieldStyle,
    minHeight: '60px', // Slightly taller
    resize: 'vertical',
    padding: '10px',
  };

  const statusTextStyle: React.CSSProperties = {
    fontSize: `${FONT_SIZE_STATUS}px`,
    color: status === 'failed' ? THEME.statusFailed : THEME.statusCompleted,
    textAlign: 'center',
    width: '100%',
    padding: '5px 0',
    marginBottom: `${PADDING}px`,
    fontWeight: '500',
  };

  const handleViewNewsletterClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (gmailMockUrl) {
      window.open(gmailMockUrl, '_blank');
    }
  };

  const handleTransformEnd = (e: Konva.KonvaEventObject<Event>) => {
    if (processingTransformEndRef.current) {
      return; // Ignore re-entrant call
    }
    processingTransformEndRef.current = true;

    const node = groupRef.current;
    if (!node) return;

    const scaleX = node.scaleX();
    const scaleY = node.scaleY();

    const newWidth = cardWidth * scaleX;
    const newHeight = actualDisplayHeight * scaleY;

    // Apply min/max constraints
    const clampedWidth = Math.max(250, newWidth);
    const clampedHeight = Math.max(CARD_HEIGHT_MIN, newHeight);

    setCardWidth(clampedWidth);
    // Store user preferred height; effect will reconcile with content requirements
    setManualHeight(clampedHeight);
    console.log('[NAC transformEnd]', {
      scaleY,
      currentActualHeight: actualDisplayHeight,
      newRawHeight: newHeight,
      clampedHeightForManual: clampedHeight,
    });

    // Reset scale so future transforms start from 1
    node.scale({ x: 1, y: 1 });

    setTimeout(() => {
      processingTransformEndRef.current = false;
    }, 0);
  };

  if (!editableOutline || !editableOutline.title || !editableOutline.sections) {
    return (
      <Group ref={groupRef} x={currentPosition.x} y={currentPosition.y}>
        <Rect width={cardWidth} height={actualDisplayHeight} fill={THEME.cardBackground} cornerRadius={8} />
        <Text
          text={outlineText || 'Loading outline or error...'}
          x={PADDING}
          y={PADDING}
          fill={THEME.primaryText}
          width={cardWidth - PADDING * 2}
        />
      </Group>
    );
  }

  return (
    <Group
      ref={groupRef}
      x={currentPosition.x}
      y={currentPosition.y}
      draggable // Make the entire group draggable
      onDragStart={handleDragBarDragStart} // Reuse for cursor and moveToTop
      onDragEnd={handleDragBarDragEnd} // Reuse for cursor and final position update
      onTransformEnd={handleTransformEnd}
      // onDragMove can be added here if live state update during drag is needed for other reasons
    >
      <Rect
        width={cardWidth}
        height={actualDisplayHeight}
        fill={THEME.cardBackground}
        cornerRadius={8}
        shadowColor='#000000'
        shadowBlur={15}
        shadowOpacity={0.15}
        shadowOffsetX={3}
        shadowOffsetY={3}
      />

      <Rect // This is now just a visual handle, not initiating drag itself for the group
        width={cardWidth}
        height={DRAG_HANDLE_HEIGHT}
        fill={THEME.dragHandleBackground}
        cornerRadius={[8, 8, 0, 0]}
        // draggable // Remove draggable from here
        // onDragStart={handleDragBarDragStart} // Remove
        // onDragMove={handleDragBarDragMove}   // Remove
        // onDragEnd={handleDragBarDragEnd}     // Remove
      />

      <Html
        divProps={{
          style: {
            ...portalDivStyle, // Apply base portal styles
            top: `${DRAG_HANDLE_HEIGHT}px`, // Offset the HTML portal itself down by the drag handle height
            height: `${actualDisplayHeight - DRAG_HANDLE_HEIGHT}px`, // Adjust portal height
            width: `${cardWidth}px`,
          },
        }}
      >
        <div
          ref={scrollableContentRef}
          style={{
            ...scrollableContentStyle,
            // scrollable area starts from top of its parent (the offset portal div)
            // and its height needs to accommodate HEADER_AREA_HEIGHT now contained within portal
            height: `${actualDisplayHeight - DRAG_HANDLE_HEIGHT - BUTTON_AREA_HEIGHT - PADDING}px`, // REMOVED HEADER_AREA_HEIGHT
            paddingTop: PADDING, // Normal padding for content within scrollable area
          }}
          onWheel={handleScrollableWheel}
          onMouseDown={handleScrollableMouseDown}
          tabIndex={0}
        >
          <div
            style={{
              color: THEME.headingText,
              fontSize: FONT_SIZE_HEADING + 2,
              fontWeight: 'bold',
              lineHeight: `${LINE_HEIGHT}em`,
              marginBottom: PADDING,
            }}
          >
            <input
              type='text'
              value={editableOutline.title}
              onChange={(e) => handleOutlineInputChange('title', e.target.value)}
              style={{
                ...inputFieldStyle,
                fontSize: `${FONT_SIZE_HEADING + 2}px`,
                fontWeight: 'bold',
                backgroundColor: 'transparent',
                border: 'none',
                borderBottom: `1px solid ${THEME.subtleBorder}`,
                borderRadius: '0px',
                color: THEME.headingText,
              }}
              onClick={(e) => e.stopPropagation()}
              onMouseDown={(e) => e.stopPropagation()}
            />
          </div>

          {editableOutline.sections.map((section: NewsletterOutlineSection, index: number) => (
            <div
              key={`sec-html-${index}`}
              style={{
                marginBottom: SECTION_SPACING,
                borderTop: `1px solid ${THEME.subtleBorder}`,
                paddingTop: SECTION_SPACING,
              }}
            >
              <input
                type='text'
                value={section.heading}
                onChange={(e) => handleSectionInputChange(index, 'heading', e.target.value)}
                placeholder='Section Heading'
                style={{
                  ...inputFieldStyle,
                  fontSize: `${FONT_SIZE_HEADING}px`,
                  fontWeight: 'bold',
                  marginBottom: `${PADDING / 2}px`,
                  backgroundColor: 'transparent',
                  border: 'none',
                  color: THEME.headingText,
                }}
                onClick={(e) => e.stopPropagation()}
                onMouseDown={(e) => e.stopPropagation()}
              />

              <div
                style={{
                  color: THEME.primaryText,
                  fontSize: FONT_SIZE_DETAIL,
                  lineHeight: `${DETAIL_LINE_HEIGHT}em`,
                  marginBottom: `${PADDING / 2}px`,
                }}
              >
                <strong style={{ display: 'block', marginBottom: '4px', color: THEME.accentGreen }}>Copy Idea:</strong>
                <textarea
                  value={section.copyIdea || ''}
                  onChange={(e) => handleSectionInputChange(index, 'copyIdea', e.target.value)}
                  placeholder='Detailed textual content idea...'
                  style={{ ...sectionTextareaStyle, color: THEME.primaryText }}
                  onClick={(e) => e.stopPropagation()}
                  onMouseDown={(e) => e.stopPropagation()}
                />
              </div>
              <div
                style={{
                  color: THEME.primaryText,
                  fontSize: FONT_SIZE_DETAIL,
                  lineHeight: `${DETAIL_LINE_HEIGHT}em`,
                  marginBottom: `${PADDING / 2}px`,
                }}
              >
                <strong style={{ display: 'block', marginBottom: '4px', color: THEME.accentGreen }}>
                  Visual Idea:
                </strong>
                <textarea
                  value={section.visualDesignIdea || ''}
                  onChange={(e) => handleSectionInputChange(index, 'visualDesignIdea', e.target.value)}
                  placeholder='Suggestion for visual design/layout...'
                  style={{ ...sectionTextareaStyle, color: THEME.primaryText }}
                  onClick={(e) => e.stopPropagation()}
                  onMouseDown={(e) => e.stopPropagation()}
                />
              </div>
              <div
                style={{
                  color: THEME.primaryText,
                  fontSize: FONT_SIZE_DESC,
                  lineHeight: `${LINE_HEIGHT}em`,
                  paddingBottom: '2px',
                }}
              >
                <strong style={{ display: 'block', marginBottom: '4px', color: THEME.accentGreen }}>Summary:</strong>
                <textarea
                  value={section.description || ''}
                  onChange={(e) => handleSectionInputChange(index, 'description', e.target.value)}
                  placeholder='Brief summary of the section...'
                  style={{ ...sectionTextareaStyle, color: THEME.primaryText }}
                  onClick={(e) => e.stopPropagation()}
                  onMouseDown={(e) => e.stopPropagation()}
                />
              </div>
            </div>
          ))}
        </div>

        <div style={controlsContainerStyle}>
          {status === 'completed' ? (
            <>
              <div style={statusTextStyle}>Newsletter Ready!</div>
              <button
                style={{ ...buttonStyle, backgroundColor: THEME.accentOchre, color: THEME.buttonTextDark }}
                onClick={handleViewNewsletterClick}
              >
                View Newsletter
              </button>
            </>
          ) : status === 'failed' ? (
            <>
              <div style={statusTextStyle}>Failed to generate newsletter.</div>
              {/* Optionally, add a retry button here if implemented */}
            </>
          ) : showFeedbackInput ? (
            <>
              <textarea
                style={textareaStyle}
                value={feedbackText}
                onChange={(e) => setFeedbackText(e.target.value)}
                placeholder='Enter your feedback here...'
                onClick={(e) => e.stopPropagation()} // Allow text selection
                onMouseDown={(e) => e.stopPropagation()} // Prevent drag
              />
              <div style={dualButtonContainerStyle}>
                <button
                  style={{ ...dualButtonStyle, backgroundColor: THEME.accentOchre, color: THEME.buttonTextDark }}
                  onClick={handleSubmitFeedbackClick}
                >
                  Submit Feedback
                </button>
                <button
                  style={{ ...dualButtonStyle, backgroundColor: THEME.buttonCancelBg, color: THEME.buttonTextDark }}
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowFeedbackInput(false);
                  }}
                >
                  Cancel
                </button>
              </div>
            </>
          ) : status === 'generating_html' || isGenerating ? (
            <div style={{ textAlign: 'center', padding: '20px 0', color: THEME.primaryText }}>
              <div style={{ fontSize: '14px', fontWeight: '500' }}>Generating your newsletter...</div>
              {/* Basic spinner */}
              <div
                style={{
                  marginTop: '15px',
                  width: '30px',
                  height: '30px',
                  border: `4px solid ${THEME.accentGreen}`,
                  borderTop: `4px solid ${THEME.cardBackground}`,
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite',
                  display: 'inline-block',
                }}
              ></div>
              <style>
                {`
                  @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                  }
                `}
              </style>
            </div>
          ) : (
            <>
              <button
                style={{
                  ...buttonStyle,
                  backgroundColor: THEME.accentOchre,
                  color: THEME.buttonTextDark,
                  marginBottom: PADDING,
                }}
                onClick={handleApproveClick}
              >
                Approve Outline & Generate HTML
              </button>
              <div style={dualButtonContainerStyle}>
                <button
                  style={{
                    ...dualButtonStyle,
                    backgroundColor: THEME.buttonSuggestChangesBg,
                    color: THEME.buttonTextLight,
                  }}
                  onClick={handleSuggestChangesClick}
                >
                  Suggest Changes
                </button>
                <button
                  style={{ ...dualButtonStyle, backgroundColor: THEME.accentGreen, color: THEME.buttonTextLight }}
                  onClick={handleSaveDirectEditsClick}
                >
                  Save Direct Edits
                </button>
              </div>
            </>
          )}
        </div>
      </Html>
    </Group>
  );
};

export default NewsletterApprovalCard;
