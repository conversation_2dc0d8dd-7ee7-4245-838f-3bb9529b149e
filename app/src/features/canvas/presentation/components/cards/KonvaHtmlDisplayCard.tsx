import React, { useState, useRef, useEffect } from 'react';
import { Group, Rect, Text } from 'react-konva';
import { Html } from 'react-konva-utils';
import Konva from 'konva';
// 🧠 MIGRATED: Using modular types instead of client types
import { HtmlDisplayCardDataType } from '../../../domain/types';

// Theme colors adapted from NewsletterApprovalCard.tsx
const THEME = {
  cardBackground: '#F5F3ED', // Soft, off-white beige
  primaryText: '#4A5D23', // Dark, desaturated olive green
  headingText: '#3B4A1E', // Slightly darker olive for headings
  dragHandleBackground: '#E4E0D7', // Slightly darker beige for drag handle
  // Add other theme colors if needed for title text, etc.
};

interface KonvaHtmlDisplayCardProps {
  cardData: HtmlDisplayCardDataType;
  // Add any interaction props if needed later, e.g., onClose
}

export const DEFAULT_CARD_WIDTH = 600;
export const DEFAULT_CARD_HEIGHT = 700;
const PADDING = 15;
const DRAG_HANDLE_HEIGHT = 30;
const TITLE_AREA_HEIGHT = 20; // Space for the title text below drag handle

const KonvaHtmlDisplayCard: React.FC<KonvaHtmlDisplayCardProps> = ({ cardData }) => {
  const { id, taskId, title, iframeUrl, position, inlinedMobileHtmlUrl, inlinedDesktopHtmlUrl } = cardData;
  const [cardWidth, setCardWidth] = useState(cardData.width || DEFAULT_CARD_WIDTH);
  const [cardHeight, setCardHeight] = useState(cardData.height || DEFAULT_CARD_HEIGHT);
  const [exportButtonText, setExportButtonText] = useState('Export HTML');

  const [currentPosition, setCurrentPosition] = useState(position);
  const groupRef = useRef<Konva.Group>(null);
  const processingTransformEndRef = useRef(false); // Re-entrancy guard for transform

  // Drag handling logic (similar to NewsletterApprovalCard)
  const dragStartPointerPos = useRef<{ x: number; y: number } | null>(null);
  const initialGroupPosForDrag = useRef<{ x: number; y: number } | null>(null);

  useEffect(() => {
    setCurrentPosition(position); // Update position if cardData.position changes
  }, [position]);

  // Mark this group so the canvas can detect it for resizing
  useEffect(() => {
    if (groupRef.current) {
      groupRef.current.setAttr('isHtmlDisplayCard', true);
    }
  }, []);

  const handleDragBarDragStart = (e: Konva.KonvaEventObject<DragEvent>) => {
    const stage = e.target.getStage();
    if (stage && groupRef.current) {
      dragStartPointerPos.current = stage.getPointerPosition();
      initialGroupPosForDrag.current = groupRef.current.position();
      groupRef.current.moveToTop();
      if (stage.container()) stage.container().style.cursor = 'grabbing';
    }
  };

  const handleDragBarDragMove = (e: Konva.KonvaEventObject<DragEvent>) => {
    if (dragStartPointerPos.current && initialGroupPosForDrag.current && groupRef.current) {
      const stage = e.target.getStage();
      const currentPointerPos = stage?.getPointerPosition();
      if (!currentPointerPos || !dragStartPointerPos.current || !initialGroupPosForDrag.current) return;
      const dx = currentPointerPos.x - dragStartPointerPos.current.x;
      const dy = currentPointerPos.y - dragStartPointerPos.current.y;
      setCurrentPosition({
        x: initialGroupPosForDrag.current.x + dx,
        y: initialGroupPosForDrag.current.y + dy,
      });
    }
  };

  const handleDragBarDragEnd = (e: Konva.KonvaEventObject<DragEvent>) => {
    dragStartPointerPos.current = null;
    initialGroupPosForDrag.current = null;
    const stage = e.target.getStage();
    if (stage && stage.container()) stage.container().style.cursor = 'default';
    // Here you might want to dispatch an event or call a prop to save the new position
    // For example: onPositionChange?.(id, currentPosition);
  };

  // Calculate iframe dimensions
  const iframeContainerHeight = cardHeight - DRAG_HANDLE_HEIGHT - TITLE_AREA_HEIGHT - PADDING;
  const iframeHeight = iframeContainerHeight - PADDING; // Subtract padding for the title area
  const iframeWidth = cardWidth - PADDING * 2;

  const portalDivStyle: React.CSSProperties = {
    width: `${cardWidth}px`,
    height: `${iframeContainerHeight}px`, // This is the height of the div that contains the title and iframe
    display: 'flex',
    flexDirection: 'column',
    pointerEvents: 'none', // Portal div itself doesn't take mouse events for dragging Konva items
  };

  const titleStyle: React.CSSProperties = {
    padding: `0 ${PADDING}px ${PADDING / 2}px ${PADDING}px`,
    color: THEME.headingText, // Use theme color for title
    fontSize: '14px',
    fontWeight: 'bold',
    boxSizing: 'border-box',
    pointerEvents: 'none', // Title text not interactive for canvas dragging
  };

  const iframeContainerStyle: React.CSSProperties = {
    width: '100%',
    height: `${iframeHeight}px`, // Actual height for the iframe wrapper
    paddingLeft: `${PADDING}px`,
    paddingRight: `${PADDING}px`,
    paddingBottom: `${PADDING}px`,
    boxSizing: 'border-box',
    pointerEvents: 'auto', // Enable pointer events for iframe content interaction
  };

  const iframeStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    border: 'none',
  };

  // Stop propagation for events inside the HTML portal to allow iframe interaction
  const handleHtmlEvents = (e: React.SyntheticEvent) => {
    e.stopPropagation();
  };

  const handleTransformEnd = (e: Konva.KonvaEventObject<Event>) => {
    if (processingTransformEndRef.current) {
      return; // Ignore re-entrant call
    }
    processingTransformEndRef.current = true;

    const node = groupRef.current;
    if (!node) {
      processingTransformEndRef.current = false;
      return;
    }

    const scaleX = node.scaleX();
    const scaleY = node.scaleY();

    // Update dimensions based on current state and scale
    const newWidth = cardWidth * scaleX;
    const newHeight = cardHeight * scaleY;

    // Apply any local constraints (min/max width/height for this specific element)
    // Example: const MIN_WIDTH = 300, const MIN_HEIGHT = 250;
    const clampedWidth = Math.max(300, newWidth);
    const clampedHeight = Math.max(250, newHeight);

    setCardWidth(clampedWidth);
    setCardHeight(clampedHeight);

    // CRITICAL: Reset the node's scale to 1
    node.scale({ x: 1, y: 1 });

    setTimeout(() => {
      processingTransformEndRef.current = false;
    }, 0);
  };

  const handleExportHtml = async () => {
    if (!inlinedMobileHtmlUrl) {
      console.error('No inlined mobile HTML URL available for export.');
      setExportButtonText('Error!');
      setTimeout(() => setExportButtonText('Export HTML'), 2000);
      return;
    }

    try {
      setExportButtonText('Fetching...');
      const response = await fetch(inlinedMobileHtmlUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch HTML: ${response.status} ${response.statusText}`);
      }
      const htmlString = await response.text();
      await navigator.clipboard.writeText(htmlString);
      setExportButtonText('Copied!');
    } catch (error) {
      console.error('Failed to export HTML:', error);
      setExportButtonText('Failed!');
    }

    setTimeout(() => setExportButtonText('Export HTML'), 2000); // Reset button text after 2s
  };

  return (
    <Group
      ref={groupRef}
      x={currentPosition.x}
      y={currentPosition.y}
      id={id}
      draggable
      onDragStart={handleDragBarDragStart}
      onDragEnd={handleDragBarDragEnd}
      onTransformEnd={handleTransformEnd}
    >
      <Rect
        width={cardWidth}
        height={cardHeight}
        fill={THEME.cardBackground} // Use theme background
        cornerRadius={8}
        shadowColor='black'
        shadowBlur={10}
        shadowOpacity={0.3}
        shadowOffsetX={5}
        shadowOffsetY={5}
      />

      {/* Draggable Top Bar */}
      <Rect
        width={cardWidth}
        height={DRAG_HANDLE_HEIGHT}
        fill={THEME.dragHandleBackground} // Use theme drag handle background
        cornerRadius={[8, 8, 0, 0]}
      />
      <Text
        text='Drag Handle'
        x={PADDING}
        y={DRAG_HANDLE_HEIGHT / 2 - 6} // Vertically center
        fill={THEME.primaryText} // Use theme text color for drag handle text
        fontSize={12}
        listening={false} // Not interactive itself
      />

      {/* Export HTML Button */}
      <Text
        text={exportButtonText}
        x={cardWidth - PADDING - 80} // Position to the right, adjust 80 as needed for text width
        y={DRAG_HANDLE_HEIGHT / 2 - 7} // Align with drag handle text
        fill={THEME.primaryText}
        fontSize={12}
        padding={5}
        onTap={handleExportHtml}
        onClick={handleExportHtml} // For non-touch
        onMouseEnter={(e) => {
          const stage = e.target.getStage();
          if (stage) stage.container().style.cursor = 'pointer';
        }}
        onMouseLeave={(e) => {
          const stage = e.target.getStage();
          if (stage) stage.container().style.cursor = 'default';
        }}
        // listening={true} // Ensure button is interactive
      />

      <Html
        divProps={{
          style: {
            ...portalDivStyle,
            top: `${DRAG_HANDLE_HEIGHT}px`, // Position below drag handle
          },
          onWheel: handleHtmlEvents,
          onMouseDown: handleHtmlEvents,
          onTouchStart: handleHtmlEvents, // for touch devices
        }}
      >
        <div style={titleStyle}>{title}</div>
        <div style={iframeContainerStyle}>
          {iframeUrl ? (
            <iframe title={title} src={iframeUrl} style={iframeStyle} />
          ) : (
            <div style={{ color: '#FFB3B3', padding: '20px' }}>Error: iframe URL not provided.</div>
          )}
        </div>
      </Html>
    </Group>
  );
};

export default KonvaHtmlDisplayCard;
