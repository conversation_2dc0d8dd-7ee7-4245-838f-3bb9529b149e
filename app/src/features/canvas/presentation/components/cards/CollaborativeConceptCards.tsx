/**
 * 🎨 Collaborative Concept Cards Component
 * @description Renders concept cards and loading cards from other users
 * @responsibility Real-time collaborative concept card visualization
 * @ai_context Part of the modular collaboration system for concept cards
 */

import React from 'react';
import { Group, Rect, Text } from 'react-konva';
import KonvaGreenConceptCard from './KonvaGreenConceptCard';
import { ConceptCardData } from '../../../domain/types';

/**
 * 🎨 Collaborative Loading Card Interface
 */
interface CollaborativeLoadingCard {
  id: string;
  position: { x: number; y: number };
  userId: string;
  userName: string;
  userColor: string;
  requestText: string;
  createdAt: number;
}

/**
 * 🎨 Collaborative Concept Card Interface
 */
interface CollaborativeConceptCard extends ConceptCardData {
  userId: string;
  userName: string;
  userColor: string;
  createdAt: number;
}

/**
 * 🎨 Collaborative Concept Cards Props
 */
interface CollaborativeConceptCardsProps {
  currentUserId?: string;
  collaborativeLoadingCards?: Record<string, CollaborativeLoadingCard>;
  collaborativeConceptCards?: Record<string, CollaborativeConceptCard>;
  onConceptCardUpdate?: (cardId: string, updates: any) => void;
  onConceptCardRemove?: (cardId: string) => void;
  onLocalConceptCardRemove?: (cardId: string) => void;
}

/**
 * 🎨 Collaborative Loading Card Component
 */
const CollaborativeLoadingCardComponent: React.FC<{
  card: CollaborativeLoadingCard;
}> = ({ card }) => {
  const cardWidth = 300;
  const cardHeight = 200;

  return (
    <Group x={card.position.x} y={card.position.y}>
      {/* Card Background */}
      <Rect
        width={cardWidth}
        height={cardHeight}
        fill="#2D2F33"
        stroke={card.userColor}
        strokeWidth={2}
        cornerRadius={12}
        opacity={0.9}
      />

      {/* User Indicator */}
      <Rect
        x={8}
        y={8}
        width={cardWidth - 16}
        height={24}
        fill={card.userColor}
        cornerRadius={4}
      />
      <Text
        x={16}
        y={14}
        text={`${card.userName} is creating...`}
        fontSize={12}
        fontFamily="Inter"
        fill="white"
        fontStyle="bold"
      />

      {/* Loading Text */}
      <Text
        x={16}
        y={50}
        text={card.requestText}
        fontSize={14}
        fontFamily="Inter"
        fill="white"
        width={cardWidth - 32}
        wrap="word"
      />

      {/* Loading Animation Dots */}
      <Text
        x={16}
        y={cardHeight - 40}
        text="●●●"
        fontSize={16}
        fontFamily="Inter"
        fill={card.userColor}
        opacity={0.7}
      />
    </Group>
  );
};

/**
 * 🎨 Collaborative Concept Card Component
 * Uses the real KonvaGreenConceptCard but in read-only mode
 */
const CollaborativeConceptCardComponent: React.FC<{
  card: CollaborativeConceptCard;
  onPositionUpdate?: (cardId: string, position: { x: number; y: number }) => void;
  onRemove?: (cardId: string) => void;
  onStatusUpdate?: (cardId: string, status: string) => void;
  onGenerate?: (cardId: string, numImages?: number) => void;
}> = ({ card, onPositionUpdate, onRemove, onStatusUpdate, onGenerate }) => {
  return (
    <Group>
      {/* User indicator overlay */}
      <Group x={card.position.x} y={card.position.y - 30}>
        <Rect
          width={200}
          height={24}
          fill={card.userColor}
          cornerRadius={12}
          opacity={0.9}
        />
        <Text
          x={12}
          y={6}
          text={`${card.userName}'s concept`}
          fontSize={12}
          fontFamily="Inter"
          fill="white"
          fontStyle="bold"
        />
      </Group>

      {/* Actual concept card with enabled interactions for collaboration */}
      <KonvaGreenConceptCard
        card={card}
        onAnswer={() => {}} // Disabled for collaborators
        onGenerate={onGenerate || (() => {})} // Enable generate for collaboration
        onRemove={onRemove || (() => {})} // Enable removal for collaboration
        isSelected={false}
        onSelect={() => {}} // Disabled for collaborators
        onPositionUpdate={onPositionUpdate} // Enable position updates for collaboration
        onStatusUpdate={onStatusUpdate || (() => {})} // Enable status updates for collaboration
      />

      {/* Overlay to prevent interactions */}
      <Rect
        x={card.position.x}
        y={card.position.y}
        width={350} // Approximate card width
        height={800} // Approximate card height
        fill="transparent"
        listening={false} // This makes it non-interactive
        opacity={0}
      />
    </Group>
  );
};

/**
 * 🎨 Collaborative Concept Cards Component
 */
const CollaborativeConceptCards: React.FC<CollaborativeConceptCardsProps> = ({
  currentUserId,
  collaborativeLoadingCards = {},
  collaborativeConceptCards = {},
  onConceptCardUpdate,
  onConceptCardRemove,
  onLocalConceptCardRemove,
}) => {
  // Debug logging
  React.useEffect(() => {
    console.log('[CollaborativeConceptCards] Loading cards:', collaborativeLoadingCards);
    console.log('[CollaborativeConceptCards] Concept cards:', collaborativeConceptCards);
    console.log('[CollaborativeConceptCards] Current user ID:', currentUserId);

    // Debug each concept card
    Object.values(collaborativeConceptCards).forEach(card => {
      console.log('[CollaborativeConceptCards] Card details:', {
        id: card.id,
        userId: card.userId,
        userName: card.userName,
        userColor: card.userColor,
        position: card.position,
        concept: card.concept,
        prompt: card.prompt
      });
    });
  }, [collaborativeLoadingCards, collaborativeConceptCards, currentUserId]);

  return (
    <Group>
      {/* Render collaborative loading cards */}
      {Object.values(collaborativeLoadingCards).map((card) => {
        // Don't show current user's cards (they see their own local cards)
        if (card.userId === currentUserId) return null;

        return (
          <CollaborativeLoadingCardComponent
            key={card.id}
            card={card}
          />
        );
      })}

      {/* Render collaborative concept cards */}
      {Object.values(collaborativeConceptCards).map((card) => {
        // Don't show current user's cards (they see their own local cards)
        if (card.userId === currentUserId) return null;

        return (
          <CollaborativeConceptCardComponent
            key={card.id}
            card={card}
            onPositionUpdate={(cardId, position) => {
              // Broadcast position update to collaborators
              if (onConceptCardUpdate) {
                onConceptCardUpdate(cardId, { position });
              }
            }}
            onStatusUpdate={(cardId, status) => {
              // Broadcast status update to collaborators
              if (onConceptCardUpdate) {
                onConceptCardUpdate(cardId, { status });
              }
            }}
            onGenerate={(cardId, numImages) => {
              // When collaborative user clicks generate, just broadcast the status change
              // Don't actually generate images (that would be confusing)
              if (onConceptCardUpdate) {
                onConceptCardUpdate(cardId, { status: 'generating' });
              }
            }}
            onRemove={(cardId) => {
              // Remove from local collaborative state first
              if (onLocalConceptCardRemove) {
                onLocalConceptCardRemove(cardId);
              }

              // Then broadcast concept card removal to collaborators
              if (onConceptCardRemove) {
                onConceptCardRemove(cardId);
              }
            }}
          />
        );
      })}
    </Group>
  );
};

export default CollaborativeConceptCards;
