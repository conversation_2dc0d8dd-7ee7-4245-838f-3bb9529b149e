import React, { useRef, useEffect, useState } from 'react';
import { Group, Rect, Text, Circle } from 'react-konva';
import Konva from 'konva';

interface KonvaLoadingConceptCardProps {
  position: { x: number; y: number };
  requestText?: string; // Make requestText optional
  id: string; // Add ID property to identify the card
}

const KonvaLoadingConceptCard: React.FC<KonvaLoadingConceptCardProps> = ({
  position,
  requestText = 'Creating your concept...', // Default text
  id,
}) => {
  // Reference to the group for dragging
  const groupRef = useRef<Konva.Group>(null);

  // State to track if the card is being dragged
  const [isDragging, setIsDragging] = useState(false);
  // Card dimensions
  const width = 272;
  const height = 400;
  const padding = 24;
  const borderRadius = 32;
  const borderWidth = 2.5;

  // Animation refs
  const circleRef = useRef<Konva.Group>(null);
  const animationRef = useRef<Konva.Animation | null>(null);

  // Colors
  const bgColor = '#2A4222'; // Dark green background
  const borderColor = '#D4AF37'; // Gold border
  const textColor = 'white'; // White text
  const loadingColor = '#D4AF37'; // Gold for loading spinner

  // Start the loading animation
  useEffect(() => {
    if (circleRef.current) {
      // Create the animation
      const circle = circleRef.current;

      animationRef.current = new Konva.Animation((frame) => {
        if (!frame) return;

        // Rotate the circle
        const angleDiff = (frame.timeDiff * 90) / 1000; // 90 degrees per second
        circle.rotate(angleDiff);

        return true;
      }, circle.getLayer());

      // Start the animation
      animationRef.current.start();
    }

    // Clean up
    return () => {
      if (animationRef.current) {
        animationRef.current.stop();
      }
    };
  }, []);

  // Using the default requestText value directly

  // Handle drag start
  const handleDragStart = () => {
    setIsDragging(true);
    console.log(`[KonvaLoadingConceptCard] Started dragging`);
  };

  // Handle drag end to update the position
  const handleDragEnd = () => {
    if (!groupRef.current) return;

    // Get the new position
    const newPosition = groupRef.current.position();
    console.log(`[KonvaLoadingConceptCard] Dragged to position: x=${newPosition.x}, y=${newPosition.y}`);

    // Reset the dragging state
    setIsDragging(false);

    // Dispatch a custom event to update the loading card position in the global state
    const updatePositionEvent = new CustomEvent('updateLoadingCardPosition', {
      detail: {
        id: id, // Use the ID passed as a prop
        position: newPosition,
      },
    });
    window.dispatchEvent(updatePositionEvent);
  };

  return (
    <Group
      ref={groupRef}
      x={position.x}
      y={position.y}
      width={width}
      height={height}
      draggable // Make the card draggable
      onDragStart={handleDragStart} // Set dragging state on drag start
      onDragEnd={handleDragEnd} // Update position on drag end
      onMouseEnter={(e) => {
        const stage = e.target.getStage();
        if (stage) {
          stage.container().style.cursor = 'move'; // Change cursor to indicate draggable
        }
      }}
      onMouseLeave={(e) => {
        const stage = e.target.getStage();
        if (stage) {
          stage.container().style.cursor = 'default'; // Reset cursor
        }
      }}
    >
      {/* Card background with gold border */}
      <Rect
        width={width}
        height={height}
        fill={bgColor}
        cornerRadius={borderRadius}
        stroke={isDragging ? '#FFFFFF' : borderColor} // Change border color when dragging
        strokeWidth={isDragging ? borderWidth * 1.5 : borderWidth} // Make border thicker when dragging
        shadowColor={isDragging ? 'black' : 'transparent'} // Add shadow when dragging
        shadowBlur={isDragging ? 10 : 0}
        shadowOpacity={isDragging ? 0.3 : 0}
        shadowOffset={{ x: isDragging ? 5 : 0, y: isDragging ? 5 : 0 }}
      />

      {/* Loading title */}
      <Text
        x={padding}
        y={padding + 8}
        text='CREATING CONCEPT'
        fontSize={16}
        fontFamily="'Helvetica', sans-serif"
        fill={loadingColor}
        fontStyle='bold'
        width={width - padding * 2}
      />

      {/* Request text */}
      <Text
        x={padding}
        y={padding + 60}
        width={width - padding * 2}
        text={`"${requestText}"`}
        fontSize={14}
        fontFamily="'Georgia', serif"
        fill={textColor}
        fontStyle='italic'
        lineHeight={1.3}
        wrap='word'
      />

      {/* Loading spinner */}
      <Group x={width / 2} y={height / 2}>
        {/* Outer circle */}
        <Circle radius={40} stroke={loadingColor} strokeWidth={2} opacity={0.3} />

        {/* Spinning arc */}
        <Group ref={circleRef}>
          <Circle radius={40} stroke={loadingColor} strokeWidth={4} dash={[60, 180]} />
        </Group>

        {/* Loading text */}
        <Text
          y={60}
          text='Generating...'
          fontSize={14}
          fontFamily="'Helvetica', sans-serif"
          fill={textColor}
          align='center'
          width={100}
          offsetX={50}
        />
      </Group>
    </Group>
  );
};

export default KonvaLoadingConceptCard;
