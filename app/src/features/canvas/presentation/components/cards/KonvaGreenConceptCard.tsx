import React, { useState, useRef, useEffect } from 'react';
import { Group, Rect, Text, Line, Image } from 'react-konva';
import Konva from 'konva';
// 🧠 MIGRATED: Using modular types instead of client types
import { ConceptCardData } from '../../../domain/types';

// Cache for storing loaded images to prevent them from being garbage collected
const imageCache = new Map<string, HTMLImageElement>();

interface KonvaGreenConceptCardProps {
  card: ConceptCardData;
  onAnswer: (id: string, questionIndex: number, answer: string) => void;
  onGenerate: (id: string, numImages?: number) => void;
  onRemove: (id: string) => void;
  isSelected: boolean;
  onSelect: () => void;
  onPositionUpdate?: (id: string, position: { x: number; y: number }) => void;
  onStatusUpdate?: (id: string, status: string) => void;
}

const KonvaGreenConceptCard: React.FC<KonvaGreenConceptCardProps> = ({
  card,
  onAnswer,
  onGenerate,
  onRemove,
  isSelected,
  onSelect,
  onPositionUpdate,
  onStatusUpdate,
}) => {
  const [inputText, setInputText] = useState('');
  const [isGenerating, setIsGenerating] = useState(card.status === 'generating');
  const [isUpdatingPreview, setIsUpdatingPreview] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [showPrompt, setShowPrompt] = useState(false);
  const [numImages, setNumImages] = useState(card.numImages || 1); // State for number of images to generate
  const [streamingImageUrl, setStreamingImageUrl] = useState<string | null>(null);
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  const groupRef = useRef<Konva.Group>(null);
  const imageRef = useRef<Konva.Image>(null);
  const questionTextRef = useRef<Konva.Text>(null);

  // Card dimensions with resizing support - 25% wider
  const [width, setWidth] = useState(card.width || 340); // 25% wider than 272 (272 * 1.25 = 340)
  const [minHeight, setMinHeight] = useState(400); // Minimum height
  const padding = 24; // 80% of 30
  const borderRadius = 32; // 80% of 40
  const borderWidth = 2.5; // 80% of 3, rounded
  const [isResizing, setIsResizing] = useState(false);

  // Calculate dynamic spacing
  const titleY = padding + 8;
  const promptY = padding + 60; // Increased from 40 to give more space below the concept title

  // Use state to track element heights for dynamic positioning
  const [promptHeight, setPromptHeight] = useState(60); // Default height

  // Get suggestion chips from the card or use empty array
  const suggestionChips = card.suggestionChips || [];

  // Debug log for suggestion chips
  useEffect(() => {
    console.log(
      `[KonvaGreenConceptCard] Card ${card.id} has ${suggestionChips.length} suggestion chips:`,
      suggestionChips
    );
  }, [card.id, suggestionChips]);

  // Calculate positions based on content heights
  // Simplified layout - no more question carousel
  const inputY = promptY + (card.previewImageUrl ? 240 + 40 : promptHeight + 40); // Account for larger image height (240px) + 40px spacing
  const chipsY = inputY + 80 + 20; // Position chips below input field
  const chipsHeight = suggestionChips.length > 0 ? Math.ceil(suggestionChips.length / 2) * 36 + 10 : 0; // Height based on number of chips (2 per row) with increased row height
  const updatePreviewY = chipsY + chipsHeight + 20; // Position update preview button below chips
  const imageCountY = updatePreviewY + 40 + 20; // Position image count selector below update preview button
  const buttonY = imageCountY + 60 + 20; // Position generate button below image count selector (60px height for selector + 20px spacing)

  // Calculate total height based on content
  const height = Math.max(minHeight, buttonY + 60); // Ensure minimum height with padding at bottom

  // Debug log positions when they change
  useEffect(() => {
    console.log(
      `[KonvaConceptCard] Layout positions - inputY: ${inputY}, chipsY: ${chipsY}, chipsHeight: ${chipsHeight}, updatePreviewY: ${updatePreviewY}, imageCountY: ${imageCountY}, buttonY: ${buttonY}`
    );
  }, [inputY, chipsY, chipsHeight, updatePreviewY, imageCountY, buttonY]);

  // Store the image object in a ref to prevent it from being garbage collected
  const imageObjRef = useRef<HTMLImageElement | null>(null);

  // Load the preview image - more robust implementation
  useEffect(() => {
    // Skip if no preview URL is available
    if (!card.previewImageUrl) {
      setImageLoaded(false);
      return;
    }

    console.log(`[KonvaConceptCard] Loading image for card ${card.id}: ${card.previewImageUrl}`);

    // Check if the image is already in the cache
    let image: HTMLImageElement;
    if (imageCache.has(card.previewImageUrl)) {
      // Use the cached image
      image = imageCache.get(card.previewImageUrl)!;
      imageObjRef.current = image;

      // If the image is already loaded, update the state immediately
      if (image.complete && image.naturalWidth > 0) {
        console.log(`[KonvaConceptCard] Using cached image for card ${card.id}`);
        setImageLoaded(true);

        // Still need to update the image reference in case the component just mounted
        requestAnimationFrame(() => {
          if (imageRef.current) {
            imageRef.current.image(image);
            imageRef.current.getLayer()?.batchDraw();
          }
        });
      }
    } else {
      // Create a new image object
      image = new window.Image();
      image.crossOrigin = 'Anonymous'; // Add cross-origin attribute to prevent CORS issues
      imageObjRef.current = image;

      // Add to cache
      imageCache.set(card.previewImageUrl, image);
    }

    // Set up event handlers before setting src
    image.onload = () => {
      console.log(`[KonvaConceptCard] Image loaded for card ${card.id}`);
      setImageLoaded(true);

      // Use requestAnimationFrame to ensure the component is mounted
      requestAnimationFrame(() => {
        if (imageRef.current) {
          // Set the image object
          imageRef.current.image(image);

          // Calculate aspect ratio to prevent smooshing
          const aspectRatio = image.width / image.height;
          const containerWidth = width - padding * 2 - borderWidth * 2;
          const containerHeight = 240; // 2x bigger (was 120)

          // Adjust dimensions to maintain aspect ratio
          let imgWidth = containerWidth;
          let imgHeight = containerWidth / aspectRatio;

          // If calculated height is too tall, constrain by height instead
          if (imgHeight > containerHeight) {
            imgHeight = containerHeight;
            imgWidth = containerHeight * aspectRatio;
          }

          // Center the image horizontally if it's narrower than container
          let imgX = padding;
          if (imgWidth < containerWidth) {
            imgX = padding + (containerWidth - imgWidth) / 2;
          }

          // Update image dimensions
          imageRef.current.width(imgWidth);
          imageRef.current.height(imgHeight);
          imageRef.current.x(imgX);

          // Force a redraw of the layer
          const layer = imageRef.current.getLayer();
          if (layer) {
            layer.batchDraw();
          }
        }
      });
    };

    // Handle image loading errors
    image.onerror = () => {
      console.error(`[KonvaConceptCard] Failed to load image for card ${card.id}: ${card.previewImageUrl}`);
      setImageLoaded(false);
    };

    // Set the source last to trigger loading
    image.src = card.previewImageUrl;

    // Cleanup function
    return () => {
      // Remove event handlers to prevent memory leaks
      image.onload = null;
      image.onerror = null;
    };
  }, [card.id, card.previewImageUrl, width, padding, borderWidth]);

  // Update heights when component mounts and when content changes
  useEffect(() => {
    // Use setTimeout to ensure the text elements have rendered
    const timer = setTimeout(() => {
      const promptElement = groupRef.current?.findOne('.prompt-text');

      if (promptElement) {
        setPromptHeight(promptElement.height());
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [card.prompt]);

  // Sync internal state with card prop changes (for collaborative updates)
  useEffect(() => {
    setIsGenerating(card.status === 'generating');
  }, [card.status]);

  // Listen for numImages updates from collaboration
  useEffect(() => {
    const handleNumImagesUpdate = (event: CustomEvent<{ cardId: string; numImages: number }>) => {
      const { cardId, numImages: newNumImages } = event.detail;
      if (cardId === card.id) {
        setNumImages(newNumImages);
      }
    };

    const handleInputTextUpdate = (event: CustomEvent<{ cardId: string; inputText: string }>) => {
      const { cardId, inputText: newInputText } = event.detail;
      if (cardId === card.id) {
        setInputText(newInputText);
      }
    };

    window.addEventListener('updateCardNumImages', handleNumImagesUpdate as EventListener);
    window.addEventListener('updateCardInputText', handleInputTextUpdate as EventListener);

    return () => {
      window.removeEventListener('updateCardNumImages', handleNumImagesUpdate as EventListener);
      window.removeEventListener('updateCardInputText', handleInputTextUpdate as EventListener);
    };
  }, [card.id]);

  // 🎬 Listen for task processing start and auto-remove concept card
  useEffect(() => {
    const handleTaskUpdate = (event: CustomEvent) => {
      const data = event.detail;

      // Check if this task update is for our concept card's generation
      if (data.taskId && isGenerating) {
        const { status, result, data: streamingData } = data;
        const imageData = result || streamingData;

        // Remove concept card when task starts processing (placeholder appears)
        if (status === 'processing' && !imageData) {
          console.log(`[KonvaGreenConceptCard] 🔄 Task started processing for card ${card.id}, removing concept card (placeholder will appear)`);
          onRemove(card.id);
        }
        // Fallback: Remove on first partial image if we missed the processing status
        else if (imageData && imageData.type === 'initial_partial_image' && imageData.imageBase64) {
          console.log(`[KonvaGreenConceptCard] 🎬 First partial image received for card ${card.id}, removing concept card`);
          onRemove(card.id);
        }
      }
    };

    window.addEventListener('task_update', handleTaskUpdate as EventListener);

    return () => {
      window.removeEventListener('task_update', handleTaskUpdate as EventListener);
    };
  }, [card.id, isGenerating, onRemove]);

  // Colors - based on the image
  const bgColor = '#2A4222'; // Dark green background
  const borderColor = '#D4AF37'; // Gold border
  const textColor = 'white'; // White text
  const conceptNumberColor = '#D4AF37'; // Gold for "CONCEPT 0"
  const inputBgColor = '#2A4222'; // Same as background
  const inputBorderColor = '#D4AF37'; // Gold border for input
  const placeholderColor = '#A0A0A0'; // Light gray for placeholder text

  // Handle drag end
  const handleDragEnd = () => {
    if (!groupRef.current) return;

    // Update the card position in the state
    const newPosition = groupRef.current.position();
    card.position = newPosition;

    // Broadcast position update to collaborators
    if (onPositionUpdate) {
      onPositionUpdate(card.id, newPosition);
    }
  };

  // Handle resize start
  const handleResizeStart = (e: any) => {
    e.cancelBubble = true; // Prevent bubbling to parent elements
    setIsResizing(true);

    // Add mouse move and mouse up event listeners
    window.addEventListener('mousemove', handleResizeMove);
    window.addEventListener('mouseup', handleResizeEnd);
  };

  // Handle resize move
  const handleResizeMove = (_e: MouseEvent) => {
    if (!isResizing || !groupRef.current) return;

    // Get the stage and its container
    const stage = groupRef.current.getStage();
    if (!stage) return;

    // Get the pointer position relative to the stage
    const pointerPos = stage.getPointerPosition();
    if (!pointerPos) return;

    // Get the card position in the stage
    const cardPos = groupRef.current.position();

    // Calculate the new width and height
    const newWidth = Math.max(340, pointerPos.x - cardPos.x); // Minimum width of 340 (25% wider)
    const newHeight = Math.max(400, pointerPos.y - cardPos.y); // Minimum height of 400

    console.log(`Resizing card to ${newWidth}x${newHeight}`);

    // Update the width state
    setWidth(newWidth);

    // Update the card width property
    card.width = newWidth;

    // Update all elements that depend on width
    const cardBg = groupRef.current.findOne('Rect') as Konva.Rect;
    if (cardBg) {
      cardBg.width(newWidth);
    }

    // Update all text elements
    const textElements = groupRef.current.find('Text');
    textElements.forEach((text) => {
      // Don't resize the text in the generate button
      if (text.parent?.className !== 'Group') {
        text.width(newWidth - padding * 2);
      }
    });

    // Update the generate button position and width
    const generateButton = groupRef.current.findOne('Group') as Konva.Group;
    if (generateButton && generateButton !== groupRef.current) {
      const buttonRect = generateButton.findOne('Rect') as Konva.Rect;
      if (buttonRect) {
        buttonRect.width(newWidth - padding * 2);
      }

      const buttonText = generateButton.findOne('Text') as Konva.Text;
      if (buttonText) {
        buttonText.width(newWidth - padding * 2);
      }
    }

    // Update the resize handle position
    const resizeHandle = groupRef.current.findOne('Group[name="resize-handle-group"]') as Konva.Group;
    if (resizeHandle) {
      resizeHandle.x(newWidth - 30);
      resizeHandle.y(height - 30);
    }

    // Force a redraw of the layer
    groupRef.current.getLayer()?.batchDraw();
  };

  // Handle resize end
  const handleResizeEnd = () => {
    setIsResizing(false);

    // Remove event listeners
    window.removeEventListener('mousemove', handleResizeMove);
    window.removeEventListener('mouseup', handleResizeEnd);

    // Save the new dimensions to the card
    if (groupRef.current) {
      const newWidth = groupRef.current.width();
      card.width = newWidth;
      console.log(`Resize complete. New width: ${newWidth}`);

      // Force a final redraw
      groupRef.current.getLayer()?.batchDraw();
    }
  };

  // State to track if the input is being edited
  const [isEditing, setIsEditing] = useState(false);

  // Handle input click to start editing
  const handleInputClick = () => {
    if (isEditing) return; // Already editing

    setIsEditing(true);

    // Create a textarea for editing directly on the canvas
    const textarea = document.createElement('textarea');
    textarea.value = inputText || '';
    textarea.style.position = 'absolute';

    // Position the textarea over the input field on the card
    const stage = groupRef.current?.getStage();
    if (stage) {
      // Get the input field node - try multiple selector approaches
      let inputRect: Konva.Node | null = null;

      if (!groupRef.current) return;

      // Try to find by class name
      inputRect = groupRef.current.findOne('.input-field') || null;

      // If that fails, try by name attribute
      if (!inputRect && groupRef.current) {
        inputRect = groupRef.current.findOne('Rect[name="input-field"]') || null;
      }

      // If we still can't find it, try the text field (which might have been clicked)
      // but we'll still use the input rect for positioning
      if (!inputRect && groupRef.current) {
        const textField =
          groupRef.current.findOne('.input-text') || groupRef.current.findOne('Text[name="input-text"]') || null;

        if (textField && groupRef.current) {
          // If text was clicked, find the associated input rect
          const shapes = groupRef.current.find('Rect');
          for (const shape of shapes) {
            if (shape.y() === inputY) {
              // Use dynamic inputY instead of hardcoded value
              inputRect = shape;
              break;
            }
          }
        }
      }

      // Last resort - find the rect at the expected position
      if (!inputRect && groupRef.current) {
        const shapes = groupRef.current.find('Rect');
        for (const shape of shapes) {
          if (shape.y() === inputY) {
            // Use dynamic inputY instead of hardcoded value
            inputRect = shape;
            break;
          }
        }
      }

      if (!inputRect) {
        console.error('Could not find input field');
        setIsEditing(false);
        return;
      }

      // Get the client rect of the input field - this accounts for all transformations
      const inputClientRect = (inputRect as Konva.Shape).getClientRect();
      const stageBox = stage.container().getBoundingClientRect();

      // Calculate the absolute screen position
      const screenX = stageBox.left + inputClientRect.x;
      const screenY = stageBox.top + inputClientRect.y;

      // Position and style the textarea to match the input field
      textarea.style.position = 'absolute';
      textarea.style.left = `${screenX}px`;
      textarea.style.top = `${screenY}px`;
      textarea.style.width = `${inputClientRect.width}px`;
      textarea.style.height = `${inputClientRect.height}px`;
      textarea.style.padding = `${8 * stage.scaleX()}px`; // 80% of 10
      textarea.style.fontSize = `${14 * stage.scaleX()}px`; // 80% of 18, rounded down
      textarea.style.backgroundColor = inputBgColor;
      textarea.style.color = textColor;
      textarea.style.border = `2px solid ${inputBorderColor}`; // 80% of 2.5px
      textarea.style.borderRadius = `${16 * stage.scaleX()}px`; // 80% of 20
      textarea.style.outline = 'none';
      textarea.style.resize = 'none';
      textarea.style.fontFamily = "'Helvetica', sans-serif";
      textarea.style.transform = 'translate(0, 0)'; // Reset any transforms

      document.body.appendChild(textarea);
      textarea.focus();

      // Function to update textarea position on zoom/pan
      const updateTextareaPosition = () => {
        if (!stage || !inputRect) return;

        // Get the updated client rect - this accounts for all transformations
        const inputClientRect = (inputRect as Konva.Shape).getClientRect();
        const stageBox = stage.container().getBoundingClientRect();

        // Calculate the absolute screen position
        const screenX = stageBox.left + inputClientRect.x;
        const screenY = stageBox.top + inputClientRect.y;

        // Update the textarea position and size
        textarea.style.left = `${screenX}px`;
        textarea.style.top = `${screenY}px`;
        textarea.style.width = `${inputClientRect.width}px`;
        textarea.style.height = `${inputClientRect.height}px`;
        textarea.style.padding = `${8 * stage.scaleX()}px`; // 80% of 10
        textarea.style.fontSize = `${14 * stage.scaleX()}px`; // 80% of 18, rounded down
        textarea.style.borderRadius = `${16 * stage.scaleX()}px`; // 80% of 20
      };

      // Listen for all relevant stage and window events
      stage.on('dragmove dragend scale zoom wheel', updateTextareaPosition);
      window.addEventListener('resize', updateTextareaPosition);
      window.addEventListener('scroll', updateTextareaPosition);

      // Update position immediately after a short delay to ensure proper positioning
      setTimeout(updateTextareaPosition, 50);

      // Function to remove the textarea and clean up all event listeners
      const removeTextarea = () => {
        document.body.removeChild(textarea);
        setIsEditing(false);
        window.removeEventListener('click', handleOutsideClick);
        window.removeEventListener('resize', updateTextareaPosition);
        window.removeEventListener('scroll', updateTextareaPosition);
        stage.off('dragmove dragend scale zoom wheel', updateTextareaPosition);
      };

      // Handle Enter key to update preview
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();

          // Update the input text
          setInputText(textarea.value);

          // Broadcast input text change to collaborators
          if (onStatusUpdate && textarea.value !== inputText) {
            onStatusUpdate(card.id, `inputText:${textarea.value}`);
          }

          // Call the onAnswer prop to update the preview
          if (textarea.value.trim()) {
            setIsUpdatingPreview(true);
            onAnswer(card.id, 0, textarea.value);
            console.log(`[KonvaGreenConceptCard] Updating preview with: ${textarea.value}`);
            // Reset after a short delay
            setTimeout(() => setIsUpdatingPreview(false), 2000);
          }

          removeTextarea();
        } else if (e.key === 'Escape') {
          // Cancel editing
          removeTextarea();
        }
      };

      // Handle clicks outside the textarea
      const handleOutsideClick = (e: MouseEvent) => {
        if (e.target !== textarea) {
          // Update the input text
          setInputText(textarea.value);

          // Broadcast input text change to collaborators
          if (onStatusUpdate && textarea.value !== inputText) {
            onStatusUpdate(card.id, `inputText:${textarea.value}`);
          }

          // Only call onAnswer if there's actual content
          if (textarea.value.trim()) {
            // We don't automatically update the preview when clicking outside
            // Just store the input for later use
            console.log(`[KonvaGreenConceptCard] Stored input: ${textarea.value}`);
          }

          removeTextarea();
        }
      };

      textarea.addEventListener('keydown', handleKeyDown);

      // Add a small delay to avoid immediate triggering
      setTimeout(() => {
        window.addEventListener('click', handleOutsideClick);
      }, 100);
    }
  };

  // No longer need question navigation functions

  // Handle suggestion chip click
  const handleSuggestionClick = (suggestion: string) => {
    // Directly trigger an update with the suggestion
    setIsUpdatingPreview(true);
    onAnswer(card.id, 0, suggestion);
    console.log(`[KonvaGreenConceptCard] Updating preview with suggestion: ${suggestion}`);

    // Reset after a short delay
    setTimeout(() => setIsUpdatingPreview(false), 2000);
  };

  // Handle image count increment/decrement
  const handleImageCountChange = (delta: number) => {
    const newCount = Math.max(1, Math.min(5, numImages + delta)); // Limit between 1 and 5
    setNumImages(newCount);

    // Broadcast image count change to collaborators
    if (onStatusUpdate) {
      onStatusUpdate(card.id, `numImages:${newCount}`);
    }
  };

  // Handle generate button click
  const handleGenerate = () => {
    if (isGenerating) return; // Prevent multiple clicks

    setIsGenerating(true);

    // Broadcast generating state to collaborators
    if (onStatusUpdate) {
      onStatusUpdate(card.id, 'generating');
    }

    // 🖼️ Create placeholder image immediately
    console.log(`[KonvaGreenConceptCard] Creating placeholder image for card ${card.id}`);
    window.dispatchEvent(new CustomEvent('createPlaceholderImage', {
      detail: {
        cardId: card.id,
        position: { x: 200, y: 200 } // Default position
      }
    }));

    // Remove concept card immediately after creating placeholder
    console.log(`[KonvaGreenConceptCard] Removing concept card ${card.id} (placeholder created)`);
    onRemove(card.id);

    // Pass the number of images to the onGenerate callback
    console.log(`[KonvaGreenConceptCard] Generating ${numImages} image(s) for card ${card.id}`);
    onGenerate(card.id, numImages);
  };

  return (
    <Group
      ref={groupRef}
      x={card.position.x}
      y={card.position.y}
      width={width}
      height={height}
      draggable
      onDragEnd={handleDragEnd}
      onClick={(e) => {
        // Prevent event bubbling
        e.cancelBubble = true;
        // Call the onSelect callback
        onSelect();
        // Force a redraw to ensure all elements are visible
        const layer = groupRef.current?.getLayer();
        if (layer) {
          layer.batchDraw();
        }
      }}
    >
      {/* Card background with gold border */}
      <Rect
        width={width}
        height={height}
        fill={bgColor}
        cornerRadius={borderRadius}
        stroke={isResizing ? textColor : borderColor}
        strokeWidth={isResizing ? borderWidth * 1.5 : borderWidth}
      />

      {/* Concept label with title */}
      <Text
        x={padding}
        y={padding + 8}
        text={`${card.concept}`}
        fontSize={16} // 80% of 20
        fontFamily="'Helvetica', sans-serif"
        fill={conceptNumberColor}
        fontStyle='bold'
        width={width - padding * 2}
        wrap='word'
        name='concept-title'
      />

      {/* Preview image or prompt display */}
      {card.previewImageUrl ? (
        <Group
          onClick={onSelect} // Make sure clicks on the group also select the card
        >
          {/* Background for the image area */}
          <Rect
            x={padding}
            y={promptY}
            width={width - padding * 2 - borderWidth * 2}
            height={240} // 2x bigger (was 120)
            fill='#f5f5f5'
            cornerRadius={8}
            stroke={borderColor}
            strokeWidth={1}
          />

          {/* The actual image */}
          <Image
            ref={imageRef}
            x={padding}
            y={promptY}
            width={width - padding * 2 - borderWidth * 2}
            height={240} // 2x bigger (was 120)
            opacity={imageLoaded ? 1 : 0}
            cornerRadius={8}
            image={imageObjRef.current || new window.Image()} // Use the stored image object
            listening={false} // Prevent the image from capturing mouse events
          />

          {/* Loading indicator */}
          {!imageLoaded && (
            <Group>
              <Text
                x={padding}
                y={promptY + 100} // Centered in the 240px height (was promptY + 40)
                width={width - padding * 2}
                height={40}
                text='Loading preview...'
                fontSize={16} // Slightly larger font
                fill='#666'
                align='center'
                verticalAlign='middle'
              />
              <Rect
                x={padding + (width - padding * 2) / 4}
                y={promptY + 140} // Adjusted for new height (was promptY + 80)
                width={(width - padding * 2) / 2}
                height={6} // Slightly thicker (was 4)
                fill='#D4AF37'
                cornerRadius={3} // Slightly rounder (was 2)
              />
            </Group>
          )}
          {/* Info icon to view the full prompt */}
          <Group
            x={width - padding - borderWidth - 32} // Slightly larger and more to the right
            y={promptY + 8} // Slightly lower
            onMouseEnter={() => {
              setShowPrompt(true);
            }}
            onMouseLeave={() => {
              // Add a small delay before hiding the prompt to prevent accidental hiding
              setTimeout(() => {
                setShowPrompt(false);
              }, 300);
            }}
          >
            <Rect
              width={28}
              height={28}
              cornerRadius={14}
              fill='#D4AF37' // Gold background to match card theme
              opacity={0.9}
              stroke='#2A4222' // Dark green border
              strokeWidth={1}
            />
            <Text
              text='i'
              fontSize={16}
              fontStyle='bold'
              fill='#2A4222' // Dark green text
              align='center'
              verticalAlign='middle'
              width={28}
              height={28}
            />
          </Group>
          {/* Tooltip to show the full prompt */}
          {showPrompt && (
            <Group
              x={padding}
              y={promptY + 120} // Centered in the 240px height (was promptY + 60)
              listening={false} // Prevent the tooltip from capturing mouse events
            >
              <Rect
                width={width - padding * 2}
                cornerRadius={8}
                fill='#f0f0f0'
                opacity={0.95}
                height={Math.min(250, Math.max(150, (card.prompt?.length || 0) / 2))}
                shadowColor='black'
                shadowBlur={10}
                shadowOpacity={0.4}
                shadowOffset={{ x: 2, y: 2 }}
                stroke='#D4AF37' // Gold border to match card theme
                strokeWidth={1}
              />
              <Text
                x={8}
                y={8}
                text={card.prompt || ''}
                padding={12}
                fontSize={13}
                fontStyle='italic'
                fill='#333'
                width={width - padding * 2 - 32}
                wrap='word'
                align='left'
                lineHeight={1.4}
              />
            </Group>
          )}
        </Group>
      ) : (
        <Text
          x={padding}
          y={promptY}
          width={width - padding * 2}
          text={card.prompt?.substring(0, 100) + '...' || 'Generating prompt...'}
          fontSize={18}
          fontFamily="'Georgia', serif"
          fill={textColor}
          lineHeight={1.3}
          wrap='word'
          name='prompt-text'
          className='prompt-text'
        />
      )}

      {/* No more questions carousel */}

      {/* Input field (simulated) */}
      <Rect
        x={padding}
        y={inputY}
        width={width - padding * 2}
        height={80} // 80% of 100
        fill={inputBgColor}
        stroke={isEditing ? textColor : inputBorderColor}
        strokeWidth={isEditing ? 2.5 : 2} // 80% of original stroke width
        cornerRadius={16} // 80% of 20
        onClick={handleInputClick}
        name='input-field'
        className='input-field'
      />

      <Text
        x={padding + 12} // 80% of (padding + 15)
        y={inputY + 16} // Centered in input field
        text={inputText || 'Refine this idea...'}
        fontSize={14} // 80% of 18, rounded down
        fontFamily="'Helvetica', sans-serif"
        fill={inputText ? textColor : placeholderColor}
        width={width - padding * 2 - 24} // 80% of (width - padding * 2 - 30)
        onClick={handleInputClick}
        name='input-text'
        className='input-text'
      />

      {/* Suggestion Chips */}
      {suggestionChips && suggestionChips.length > 0 ? (
        <Group x={padding} y={chipsY} width={width - padding * 2} name='suggestion-chips-group'>
          {/* Debug text to show chips are being rendered */}
          <Text
            x={0}
            y={-20}
            text={`${suggestionChips.length} suggestions`}
            fontSize={12}
            fontFamily="'Helvetica', sans-serif"
            fill='#333333'
          />

          {suggestionChips.map((chip, index) => (
            <Group
              key={index}
              x={(index % 2) * ((width - padding * 2) / 2 - 5) + (index % 2 === 1 ? 10 : 0)}
              y={Math.floor(index / 2) * 36} // Increased vertical spacing between rows
              onClick={(e) => {
                e.cancelBubble = true;
                handleSuggestionClick(chip);
                console.log(`[KonvaGreenConceptCard] Clicked suggestion chip: ${chip}`);
              }}
              onMouseEnter={(e) => {
                const stage = e.target.getStage();
                if (stage) {
                  stage.container().style.cursor = 'pointer';
                }
              }}
              onMouseLeave={(e) => {
                const stage = e.target.getStage();
                if (stage) {
                  stage.container().style.cursor = 'default';
                }
              }}
            >
              {/* Use a fixed width for all chips for consistency */}
              <Rect
                width={(width - padding * 2) / 2 - 10} // Fixed width for all chips
                height={30} // Increased height for better padding
                fill='#4A6A3D' // Darker green
                cornerRadius={15} // Increased corner radius for rounded look
                opacity={0.9}
                shadowColor='black'
                shadowBlur={3}
                shadowOpacity={0.3}
                shadowOffset={{ x: 1, y: 1 }}
              />
              <Text
                x={16} // Increased horizontal padding
                y={9} // Centered vertically in the 30px height
                text={chip}
                fontSize={12}
                fontFamily="'Helvetica', sans-serif"
                fill='#FFFFFF'
                width={(width - padding * 2) / 2 - 42} // Account for padding on both sides
                align='center'
                ellipsis={true}
              />
            </Group>
          ))}
        </Group>
      ) : (
        // Debug text to show when no chips are available
        <Text
          x={padding}
          y={chipsY}
          text='No suggestion chips available'
          fontSize={12}
          fontFamily="'Helvetica', sans-serif"
          fill='#999999'
        />
      )}

      {/* Close button (X) */}
      <Group
        x={width - 32} // 80% of (width - 40)
        y={padding + 8} // 80% of (padding + 10)
        onClick={(e) => {
          e.cancelBubble = true;
          onRemove(card.id);
        }}
      >
        <Text
          text='×'
          fontSize={22} // 80% of 28, rounded
          fontFamily="'Helvetica', sans-serif"
          fill={borderColor}
          fontStyle='bold'
        />
      </Group>

      {/* Update Preview button */}
      <Group
        x={padding}
        y={updatePreviewY} // Dynamic position based on content
        onClick={(e) => {
          e.cancelBubble = true;
          // Handle update preview
          if (inputText.trim()) {
            setIsUpdatingPreview(true);
            // Call the onAnswer prop to update the preview
            onAnswer(card.id, 0, inputText);
            console.log(`[KonvaGreenConceptCard] Updating preview with: ${inputText}`);
            // Reset after a short delay
            setTimeout(() => setIsUpdatingPreview(false), 2000);
          }
        }}
      >
        <Rect
          width={width - padding * 2}
          height={40}
          fill='#8A9A8B' // Muted green for update button
          cornerRadius={16}
        />
        <Text
          x={0}
          y={12}
          text={isUpdatingPreview ? 'Updating...' : '✨ Update Preview'}
          fontSize={14}
          fontFamily="'Helvetica', sans-serif"
          fill='#FFFFFF' // White text
          align='center'
          width={width - padding * 2}
        />
      </Group>

      {/* Image Count Selector */}
      <Group x={padding} y={imageCountY} name='image-count-selector'>
        {/* Background container */}
        <Rect
          width={width - padding * 2}
          height={60}
          fill='#2A4222' // Same as card background
          stroke='#D4AF37' // Gold border
          strokeWidth={2}
          cornerRadius={16}
        />

        {/* Label */}
        <Text
          x={16}
          y={12}
          text='Number of Images'
          fontSize={12}
          fontFamily="'Helvetica', sans-serif"
          fill='#D4AF37' // Gold text
          fontStyle='bold'
        />

        {/* Count display and controls container */}
        <Group
          x={width - padding * 2 - 120} // Position on the right side
          y={25}
        >
          {/* Minus button */}
          <Group
            onClick={(e) => {
              e.cancelBubble = true;
              handleImageCountChange(-1);
            }}
            onMouseEnter={(e) => {
              const stage = e.target.getStage();
              if (stage) {
                stage.container().style.cursor = 'pointer';
              }
            }}
            onMouseLeave={(e) => {
              const stage = e.target.getStage();
              if (stage) {
                stage.container().style.cursor = 'default';
              }
            }}
          >
            <Rect
              width={30}
              height={30}
              fill={numImages > 1 ? '#D4AF37' : '#666666'} // Gold if enabled, gray if disabled
              cornerRadius={15}
              opacity={numImages > 1 ? 1 : 0.5}
            />
            <Text
              text='−'
              fontSize={18}
              fontFamily="'Helvetica', sans-serif"
              fill={numImages > 1 ? '#2A4222' : '#999999'} // Dark green if enabled, gray if disabled
              align='center'
              verticalAlign='middle'
              width={30}
              height={30}
              fontStyle='bold'
            />
          </Group>

          {/* Count display */}
          <Group x={40}>
            <Rect
              width={40}
              height={30}
              fill='#1A3418' // Darker green
              stroke='#D4AF37'
              strokeWidth={1}
              cornerRadius={8}
            />
            <Text
              text={numImages.toString()}
              fontSize={16}
              fontFamily="'Helvetica', sans-serif"
              fill='#D4AF37' // Gold text
              align='center'
              verticalAlign='middle'
              width={40}
              height={30}
              fontStyle='bold'
            />
          </Group>

          {/* Plus button */}
          <Group
            x={90}
            onClick={(e) => {
              e.cancelBubble = true;
              handleImageCountChange(1);
            }}
            onMouseEnter={(e) => {
              const stage = e.target.getStage();
              if (stage) {
                stage.container().style.cursor = 'pointer';
              }
            }}
            onMouseLeave={(e) => {
              const stage = e.target.getStage();
              if (stage) {
                stage.container().style.cursor = 'default';
              }
            }}
          >
            <Rect
              width={30}
              height={30}
              fill={numImages < 5 ? '#D4AF37' : '#666666'} // Gold if enabled, gray if disabled
              cornerRadius={15}
              opacity={numImages < 5 ? 1 : 0.5}
            />
            <Text
              text='+'
              fontSize={18}
              fontFamily="'Helvetica', sans-serif"
              fill={numImages < 5 ? '#2A4222' : '#999999'} // Dark green if enabled, gray if disabled
              align='center'
              verticalAlign='middle'
              width={30}
              height={30}
              fontStyle='bold'
            />
          </Group>
        </Group>

        {/* Helper text */}
        <Text
          x={16}
          y={40}
          text='Generate 1-5 images at once'
          fontSize={10}
          fontFamily="'Helvetica', sans-serif"
          fill='#A0A0A0' // Light gray
          fontStyle='italic'
        />
      </Group>

      {/* Generate Final button */}
      {card.generatedContent ? (
        <Group
          x={padding}
          y={buttonY} // Dynamic position based on content
        >
          <Rect
            width={width - padding * 2}
            height={40} // Slightly taller
            fill='#4CAF50' // Green for success
            cornerRadius={16}
          />
          <Text
            x={0}
            y={12}
            text='Generated ✓'
            fontSize={14}
            fontFamily="'Helvetica', sans-serif"
            fill='#FFFFFF' // White text
            align='center'
            width={width - padding * 2}
          />
        </Group>
      ) : (
        <Group
          x={padding}
          y={buttonY} // Dynamic position based on content
          onClick={(e) => {
            e.cancelBubble = true;
            handleGenerate();
          }}
        >
          <Rect
            width={width - padding * 2}
            height={40} // Slightly taller
            fill='#D4AF37' // Gold button
            cornerRadius={16}
          />
          <Text
            x={0}
            y={12}
            text={isGenerating ? 'Generating...' : '✅ Create Final Image'}
            fontSize={14}
            fontFamily="'Helvetica', sans-serif"
            fill='#2A4222' // Dark green text
            align='center'
            width={width - padding * 2}
          />
        </Group>
      )}

      {/* Resize handle */}
      <Group
        x={width - 30}
        y={height - 30}
        onMouseDown={handleResizeStart}
        onTouchStart={handleResizeStart}
        onMouseEnter={(e) => {
          const stage = e.target.getStage();
          if (stage) {
            stage.container().style.cursor = 'nwse-resize';
          }
        }}
        onMouseLeave={(e) => {
          const stage = e.target.getStage();
          if (stage) {
            stage.container().style.cursor = 'default';
          }
        }}
        name='resize-handle-group'
      >
        <Rect
          width={30}
          height={30}
          fill='transparent'
          stroke={isResizing ? textColor : 'transparent'}
          strokeWidth={1}
          cornerRadius={5}
          name='resize-handle'
        />
        <Line points={[8, 22, 22, 8]} stroke={borderColor} strokeWidth={2.5} />
        <Line points={[14, 22, 22, 14]} stroke={borderColor} strokeWidth={2.5} />
        <Line points={[20, 22, 22, 20]} stroke={borderColor} strokeWidth={2.5} />
      </Group>
    </Group>
  );
};

export default KonvaGreenConceptCard;
