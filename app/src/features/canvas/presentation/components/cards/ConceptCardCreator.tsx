/**
 * 🎨 ConceptCardCreator Component - MODULAR VERSION
 * @description Handles createConceptCardRequest events and creates concept cards
 * @responsibility Listens for DOM events and calls Wasp operations
 * @dependencies Wasp operations, auth, reference images
 * @ai_context This is the ConceptCardCreator code moved to modular structure
 */

import React, { useEffect } from "react";
import { createConceptCard, getModelProduct } from "wasp/client/operations";
import { useAuth } from "wasp/client/auth";
import { v4 as uuid } from "uuid";

// TODO: Migrate these dependencies to modular structure
// 🧠 MIGRATED: Using modular hooks instead of client hooks
import { useReferenceImages } from "../../hooks";
// 🧠 MIGRATED: Using modular hooks instead of client hooks
import { useVoiceAgentRoom } from "../../hooks";

/**
 * 🎨 ConceptCardCreator Props Interface
 */
interface ConceptCardCreatorProps {
	userId?: string;
	canvasId?: string;
}

/**
 * 🎨 Card Request Detail Interface
 */
interface CardRequestDetail {
	id: string;
	prompt: string;
	position: { x: number; y: number };
	productId?: string;
	aspectRatio?: "1024x1024" | "1536x1024" | "1024x1536";
}

/**
 * 🎨 Create Concept Card Request Event Interface
 */
interface CreateConceptCardRequestEvent extends CustomEvent {
	detail: string | CardRequestDetail;
}

/**
 * 🎨 Global Window Event Map Extension
 */
declare global {
	interface WindowEventMap {
		createConceptCardRequest: CreateConceptCardRequestEvent;
		showLoadingConceptCard: CustomEvent;
		removeLoadingConceptCard: CustomEvent;
	}
}

/**
 * 🎨 ConceptCardCreator Component - MODULAR VERSION
 * @ai_context This handles the createConceptCardRequest events from ChatBubble
 */
export const ConceptCardCreator: React.FC<ConceptCardCreatorProps> = ({
	userId,
	canvasId,
}) => {
	const { data: user } = useAuth();
	const roomName = useVoiceAgentRoom();
	const { getReferenceImages } = useReferenceImages(roomName);

	// Get the effective user ID - use prop, then auth user, then fallback
	const effectiveUserId =
		typeof userId === "number"
			? userId
			: typeof userId === "string"
				? parseInt(userId, 10)
				: typeof user?.id === "number"
					? user.id
					: typeof user?.id === "string"
						? parseInt(user.id, 10)
						: 1;

	// Removed spammy user ID resolution logs

	// Listen for createConceptCardRequest events
	useEffect(() => {
		const handleCreateConceptCardRequest = async (
			event: CreateConceptCardRequestEvent,
		) => {
			const detail = event.detail;

			try {
				// Handle both string and object details
				let request: string;
				let position = { x: 100, y: 100 }; // Default position
				let cardId = `loading-${uuid()}`; // Default ID
				let productId: string | undefined;
				let aspectRatio: "1024x1024" | "1536x1024" | "1024x1536" | undefined;

				if (typeof detail === "string") {
					request = detail;
				} else {
					request = detail.prompt;
					position = detail.position;
					cardId = detail.id;
					productId = detail.productId;
					aspectRatio = detail.aspectRatio;
				}

				console.log(`[ConceptCardCreator] 🎨 Creating concept card:`, {
					request,
					position,
					cardId,
					productId,
					aspectRatio,
				});

				// Create a temporary loading card ID
				const tempId = cardId || `loading-${uuid()}`;

				// Show loading card first
				window.dispatchEvent(
					new CustomEvent("showLoadingConceptCard", {
						detail: {
							id: tempId,
							position,
							requestText: "Creating your concept...",
						},
					}),
				);

				// Get reference images
				const referenceImages = getReferenceImages();
				console.log(
					`[ConceptCardCreator] Passing ${referenceImages.length} reference images for concept card creation`,
				);

				// Call the Wasp action to create a concept card
				console.log(`[ConceptCardCreator] 🚀 CALLING CREATE CONCEPT CARD:`);
				console.log(`[ConceptCardCreator] - Request: ${request}`);
				console.log(`[ConceptCardCreator] - User ID: ${effectiveUserId}`);
				console.log(`[ConceptCardCreator] - Product ID: ${productId}`);
				console.log(`[ConceptCardCreator] - Canvas ID: ${canvasId}`);

				await createConceptCard({
					request,
					userId: effectiveUserId,
					loadingCardId: tempId, // Pass the loading card ID to replace it later
					productId, // Include the product ID if available
					referenceImages, // Pass all reference images with product reference if available
					aspectRatio, // Pass the aspectRatio if available
					canvasId, // Pass the canvas ID for proper room targeting
				});
			} catch (error) {
				console.error(
					"[ConceptCardCreator] Error creating concept card:",
					error,
				);
			}
		};

		// Add event listener
		window.addEventListener(
			"createConceptCardRequest",
			handleCreateConceptCardRequest,
		);

		// Clean up
		return () => {
			window.removeEventListener(
				"createConceptCardRequest",
				handleCreateConceptCardRequest,
			);
		};
	}, [effectiveUserId, getReferenceImages, canvasId]);

	return null; // This component doesn't render anything
};

export default ConceptCardCreator;
