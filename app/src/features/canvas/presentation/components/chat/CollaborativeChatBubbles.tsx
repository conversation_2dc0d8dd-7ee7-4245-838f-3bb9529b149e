/**
 * 💬 Collaborative Chat Bubbles Component
 * @description Manages collaborative chat bubbles that are visible to all users
 * @responsibility Real-time chat bubble creation, updates, and live typing
 * @ai_context Part of the modular collaboration system
 */

import React, { useState, useCallback } from 'react';
import { ChatBubble } from './ChatBubble';

/**
 * 💬 Collaborative Chat Bubble Interface
 */
interface CollaborativeChatBubble {
  id: string;
  position: { x: number; y: number };
  userId: string;
  userName: string;
  userColor: string;
  message: string;
  isVisible: boolean;
  createdAt: number;
}

/**
 * 💬 Collaborative Chat Bubbles Props
 */
interface CollaborativeChatBubblesProps {
  currentUserId?: string;
  currentUserName?: string;
  currentUserColor?: string;
  onBubbleCreate?: (bubble: { id: string; position: { x: number; y: number }; userId: string; message: string }) => void;
  onBubbleUpdate?: (bubbleId: string, message: string) => void;
  onBubbleClose?: (bubbleId: string) => void;
  // Received bubbles from other users
  collaborativeBubbles?: Record<string, CollaborativeChatBubble>;
  // Canvas viewport information for positioning
  canvasPosition?: { x: number; y: number };
  canvasScale?: number;
}

/**
 * 💬 Collaborative Chat Bubbles Component
 */
const CollaborativeChatBubbles: React.FC<CollaborativeChatBubblesProps> = ({
  currentUserId,
  currentUserName,
  currentUserColor,
  onBubbleCreate,
  onBubbleUpdate,
  onBubbleClose,
  collaborativeBubbles = {},
  canvasPosition = { x: 0, y: 0 },
  canvasScale = 1,
}) => {
  const [localChatBubble, setLocalChatBubble] = useState<{
    isVisible: boolean;
    position: { x: number; y: number };
    id: string;
  } | null>(null);

  // 🤝 Handle bubble close (declare first to avoid circular dependency)
  const handleBubbleClose = useCallback((bubbleId: string) => {


    // Close local bubble if it matches
    if (localChatBubble?.id === bubbleId) {
      setLocalChatBubble(null);
    }

    onBubbleClose?.(bubbleId);
  }, [localChatBubble?.id, onBubbleClose]);

  // 🎯 Handle right-click to create chat bubble
  const handleCanvasRightClick = useCallback((event: CustomEvent) => {
    const { position, canvasPosition } = event.detail;

    if (!currentUserId) return;

    const bubbleId = `chat-${currentUserId}-${Date.now()}`;



    // If there's an existing bubble, close it first (but don't wait)
    if (localChatBubble) {
      onBubbleClose?.(localChatBubble.id);
    }

    // Create new bubble immediately
    setLocalChatBubble({
      isVisible: true,
      position, // Screen position for local bubble
      id: bubbleId,
    });

    // The ChatBubble component will handle broadcasting when it becomes visible
  }, [currentUserId, localChatBubble, onBubbleClose]);

  // 🤝 Handle bubble creation (broadcast to other users)
  const handleBubbleCreate = useCallback((bubble: { id: string; position: { x: number; y: number }; userId: string; message: string }) => {
    // Convert screen position to canvas position for collaboration
    const canvasX = (bubble.position.x - canvasPosition.x) / canvasScale;
    const canvasY = (bubble.position.y - canvasPosition.y) / canvasScale;

    const collaborativeBubble = {
      ...bubble,
      position: { x: canvasX, y: canvasY }, // Send canvas coordinates
    };


    onBubbleCreate?.(collaborativeBubble);
  }, [onBubbleCreate, canvasPosition, canvasScale]);

  // 🤝 Handle bubble update (live typing)
  const handleBubbleUpdate = useCallback((bubbleId: string, message: string) => {

    onBubbleUpdate?.(bubbleId, message);
  }, [onBubbleUpdate]);

  // 🎯 Close local bubble
  const handleLocalBubbleClose = useCallback(() => {
    if (localChatBubble) {
      handleBubbleClose(localChatBubble.id);
    }
  }, [localChatBubble, handleBubbleClose]);

  // 🔄 Listen for canvas events
  React.useEffect(() => {
    window.addEventListener('canvas:rightClick', handleCanvasRightClick as EventListener);

    return () => {
      window.removeEventListener('canvas:rightClick', handleCanvasRightClick as EventListener);
    };
  }, [handleCanvasRightClick]);

  // 🔄 Listen for canvas background clicks to close bubbles
  React.useEffect(() => {
    const handleCanvasBackgroundClick = () => {
      if (localChatBubble) {
        console.log('[CollaborativeChatBubbles] Background click, closing local bubble');
        handleBubbleClose(localChatBubble.id);
      }
    };

    window.addEventListener('canvas:backgroundClick', handleCanvasBackgroundClick);

    return () => {
      window.removeEventListener('canvas:backgroundClick', handleCanvasBackgroundClick);
    };
  }, [localChatBubble, handleBubbleClose]);

  return (
    <>
      {/* 💬 Local user's chat bubble */}
      {localChatBubble && (
        <ChatBubble
          isVisible={localChatBubble.isVisible}
          position={localChatBubble.position}
          onClose={handleLocalBubbleClose}
          chatBubbleId={localChatBubble.id}
          currentUserId={currentUserId}
          currentUserColor={currentUserColor}
          onBubbleCreate={handleBubbleCreate}
          onBubbleUpdate={handleBubbleUpdate}
          onBubbleClose={handleBubbleClose}
        />
      )}

      {/* 💬 Other users' chat bubbles */}
      {Object.values(collaborativeBubbles).map((bubble) => {
        // Don't show current user's bubble (already shown above)
        if (bubble.userId === currentUserId) return null;

        // Only show visible bubbles
        if (!bubble.isVisible) return null;

        // Convert canvas coordinates to screen coordinates
        const screenX = (bubble.position.x * canvasScale) + canvasPosition.x;
        const screenY = (bubble.position.y * canvasScale) + canvasPosition.y;

        return (
          <div
            key={bubble.id}
            className="fixed z-40 pointer-events-none"
            style={{
              left: screenX + 'px',
              top: screenY + 'px',
              transform: 'translate(-50%, -50%)',
            }}
          >
            {/* 👤 User indicator */}
            <div
              className="mb-2 text-xs font-medium px-2 py-1 rounded-full"
              style={{
                backgroundColor: bubble.userColor,
                color: 'white',
              }}
            >
              {bubble.userName} is typing...
            </div>
            
            {/* 💬 Chat bubble preview */}
            <div
              className="
                flex items-center
                w-[250px] h-11
                bg-[#2D2F33]
                rounded-full
                shadow-lg
                overflow-hidden
                relative
                opacity-80
              "
              style={{
                border: `2px solid ${bubble.userColor}`,
              }}
            >
              {/* Drag handle (3-dot grabber) */}
              <div className="flex flex-col items-center justify-center h-full px-3">
                <div className="flex flex-col gap-1">
                  <div className="w-1 h-1 rounded-full bg-gray-400"></div>
                  <div className="w-1 h-1 rounded-full bg-gray-400"></div>
                  <div className="w-1 h-1 rounded-full bg-gray-400"></div>
                </div>
              </div>
              
              {/* Message preview */}
              <div className="flex-1 h-full px-3 py-2 text-white text-sm truncate">
                {bubble.message || 'Ask Olivia...'}
              </div>

              {/* Send button */}
              <div className="flex-none p-0.5 mr-4">
                <img
                  src="https://oliviatest.xyz/Aliviasendbutton.png"
                  alt="Send"
                  width="36"
                  height="36"
                  className="opacity-50"
                />
              </div>
            </div>
          </div>
        );
      })}
    </>
  );
};

export default CollaborativeChatBubbles;
