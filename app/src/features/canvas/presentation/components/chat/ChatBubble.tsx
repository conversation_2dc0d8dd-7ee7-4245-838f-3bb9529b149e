/**
 * 💬 ChatBubble Component - MODULAR VERSION
 * @description Modular ChatBubble for AI-powered content generation
 * @responsibility Handles user input and AI request submission
 * @dependencies Pure domain services (TODO: migrate from old dependencies)
 * @ai_context This is the ChatBubble code moved to modular structure
 */

import React, { useState, useRef, useEffect, useLayoutEffect } from "react";
import { useAuth } from "wasp/client/auth";
import {
	analyzeMultiCardRequest,
	submitAgentJob,
} from "wasp/client/operations";

// 🧠 MIGRATED: Using internal theme management instead of external context
// Simple theme detection - can be enhanced later with domain state
const useInternalTheme = () => {
	const [isDarkMode, setIsDarkMode] = useState(() => {
		// Check system preference or localStorage
		if (typeof window !== "undefined") {
			const stored = localStorage.getItem("theme");
			if (stored) return stored === "dark";
			return window.matchMedia("(prefers-color-scheme: dark)").matches;
		}
		return false;
	});

	return { isDarkMode };
};
// 🧠 MIGRATED: Using modular hooks instead of client hooks
import { useReferenceImages } from "../../hooks";
// 🧠 MIGRATED: Using modular hooks instead of client hooks
import { useVoiceAgentRoom } from "../../hooks";

/**
 * 💬 ChatBubble Props Interface
 * @ai_context Props for the modular ChatBubble component
 */
export interface ChatBubbleProps {
	isVisible: boolean;
	position: { x: number; y: number };
	onClose: () => void;
	productId?: string;
	// Collaboration props
	chatBubbleId?: string;
	currentUserId?: string;
	currentUserColor?: string;
	onBubbleCreate?: (bubble: {
		id: string;
		position: { x: number; y: number };
		userId: string;
		message: string;
	}) => void;
	onBubbleUpdate?: (bubbleId: string, message: string) => void;
	onBubbleClose?: (bubbleId: string) => void;
}

/**
 * 💬 ChatBubble Component - MODULAR VERSION
 * @ai_context This is the actual ChatBubble code moved to modular structure
 */
export const ChatBubble: React.FC<ChatBubbleProps> = ({
	isVisible,
	position,
	onClose,
	productId,
	chatBubbleId,
	currentUserId,
	currentUserColor,
	onBubbleCreate,
	onBubbleUpdate,
	onBubbleClose,
}) => {
	const { isDarkMode } = useInternalTheme();
	const { data: user } = useAuth();
	const [message, setMessage] = useState("");
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isDragging, setIsDragging] = useState(false);
	const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
	const [bubblePosition, setBubblePosition] = useState({
		x: position.x,
		y: position.y,
	});

	const bubbleRef = useRef<HTMLDivElement>(null);
	const inputRef = useRef<HTMLInputElement>(null);
	const buttonRef = useRef<HTMLButtonElement>(null);

	const roomName = useVoiceAgentRoom();
	const { getReferenceImages } = useReferenceImages(roomName);

	// Guard flag to ensure we only dispatch one HTML request per submission
	const htmlRequestSentRef = useRef(false);

	// Handle button click animation
	const handleButtonMouseDown = () => {
		// We'll add the spin class on mouse up
	};

	const handleButtonMouseUp = () => {
		if (buttonRef.current) {
			const img = buttonRef.current.querySelector(".send-button-img");
			if (img) {
				// Add the spin animation class
				img.classList.add("spin-animation");

				// Remove the class after animation completes
				setTimeout(() => {
					img.classList.remove("spin-animation");
				}, 500); // Match the animation duration
			}
		}
	};

	// 🖱️ Drag functionality
	const handleDragStart = (e: React.MouseEvent | React.TouchEvent) => {
		setIsDragging(true);

		// Calculate the offset from the mouse/touch position to the bubble position
		let clientX: number, clientY: number;

		if ("touches" in e) {
			// Touch event
			clientX = e.touches[0].clientX;
			clientY = e.touches[0].clientY;
		} else {
			// Mouse event
			clientX = e.clientX;
			clientY = e.clientY;
		}

		setDragOffset({
			x: clientX - bubblePosition.x,
			y: clientY - bubblePosition.y,
		});

		// Prevent default behavior
		e.preventDefault();
	};

	const handleDragMove = (e: MouseEvent | TouchEvent) => {
		if (!isDragging) return;

		let clientX: number, clientY: number;

		if ("touches" in e) {
			// Touch event
			clientX = e.touches[0].clientX;
			clientY = e.touches[0].clientY;
		} else {
			// Mouse event
			clientX = e.clientX;
			clientY = e.clientY;
		}

		// Update bubble position
		setBubblePosition({
			x: clientX - dragOffset.x,
			y: clientY - dragOffset.y,
		});
	};

	const handleDragEnd = () => {
		setIsDragging(false);
	};

	// Add and remove event listeners for drag
	useEffect(() => {
		if (isDragging) {
			window.addEventListener("mousemove", handleDragMove);
			window.addEventListener("touchmove", handleDragMove);
			window.addEventListener("mouseup", handleDragEnd);
			window.addEventListener("touchend", handleDragEnd);
		} else {
			window.removeEventListener("mousemove", handleDragMove);
			window.removeEventListener("touchmove", handleDragMove);
			window.removeEventListener("mouseup", handleDragEnd);
			window.removeEventListener("touchend", handleDragEnd);
		}

		return () => {
			window.removeEventListener("mousemove", handleDragMove);
			window.removeEventListener("touchmove", handleDragMove);
			window.removeEventListener("mouseup", handleDragEnd);
			window.removeEventListener("touchend", handleDragEnd);
		};
	}, [isDragging, dragOffset]);

	useEffect(() => {
		if (isVisible && inputRef.current) {
			setTimeout(() => inputRef.current!.focus(), 100);
		}
	}, [isVisible]);

	// Update bubble position when position prop changes
	useEffect(() => {
		setBubblePosition({ x: position.x, y: position.y });
	}, [position.x, position.y]);

	// 🤝 Collaboration: Broadcast bubble creation when bubble ID changes
	useEffect(() => {
		if (isVisible && onBubbleCreate && chatBubbleId && currentUserId) {
			onBubbleCreate({
				id: chatBubbleId,
				position: bubblePosition,
				userId: currentUserId,
				message: message,
			});
		}
	}, [chatBubbleId, isVisible]); // Run when bubble ID or visibility changes

	// 🤝 Collaboration: Broadcast live typing updates
	useEffect(() => {
		if (isVisible && onBubbleUpdate && chatBubbleId && message) {
			// Throttle typing updates to avoid spam
			const timeoutId = setTimeout(() => {
				onBubbleUpdate(chatBubbleId, message);
			}, 300); // 300ms delay

			return () => clearTimeout(timeoutId);
		}
	}, [message, chatBubbleId, onBubbleUpdate, isVisible]);

	// 🤝 Collaboration: Handle bubble close
	const handleClose = () => {
		if (onBubbleClose && chatBubbleId) {
			onBubbleClose(chatBubbleId);
		}
		onClose();
	};

	// Adjust bubble position if it goes off screen
	useLayoutEffect(() => {
		if (isVisible && bubbleRef.current) {
			const bubble = bubbleRef.current;
			const rect = bubble.getBoundingClientRect();
			let left = bubblePosition.x,
				top = bubblePosition.y;
			const rightSpace = window.innerWidth - left;
			const bottomSpace = window.innerHeight - top;

			// Only adjust if needed
			if (rightSpace < rect.width || bottomSpace < rect.height) {
				if (rightSpace < rect.width)
					left = Math.max(10, bubblePosition.x - rect.width);
				if (bottomSpace < rect.height)
					top = Math.max(10, bubblePosition.y - rect.height);
				setBubblePosition({ x: left, y: top });
			}
		}
	}, [isVisible, bubblePosition.x, bubblePosition.y]);

	// 📤 Handle form submission (FULL AI LOGIC - MIGRATED from old ChatBubble)
	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!message.trim() || isSubmitting) return;

		setIsSubmitting(true);
		const basePosition = { x: 100, y: 100 };
		const loadingCardId = `request-${Date.now()}-0`;

		// Show loading card
		window.dispatchEvent(
			new CustomEvent("showLoadingConceptCard", {
				detail: {
					id: loadingCardId,
					position: basePosition,
					requestText: "Olivia is thinking...",
				},
			}),
		);
		await new Promise((resolve) => setTimeout(resolve, 10));

		try {
			const currentUserId =
				typeof user?.id === "number"
					? user.id
					: typeof user?.id === "string"
						? parseInt(user.id, 10)
						: parseInt(localStorage.getItem("userId") || "1", 10);
			const storedProductId = localStorage.getItem("selectedModelId");
			const currentProductId = productId || storedProductId || undefined;
			const referenceImages = getReferenceImages();

			console.log(`[ChatBubble] Product context:`, {
				productIdProp: productId,
				storedProductId: storedProductId,
				currentProductId: currentProductId,
			});

			// 🧠 Analyze the request to determine content type
			const analysis = await analyzeMultiCardRequest({
				request: message,
				productId: currentProductId,
				referenceImages,
			});

			console.log(
				"[ChatBubble] Analysis result:",
				JSON.stringify(analysis, null, 2),
			);

			// Process based on analysis result
			const processRequestBasedOnAnalysis = (
				analysisResult: any,
				retrievedBasePosition: { x: number; y: number },
				userIdToUse: number,
			) => {
				// For non-newsletter types, remove the generic loading card
				if (analysisResult.contentType !== "newsletter") {
					window.dispatchEvent(
						new CustomEvent("removeLoadingConceptCard", {
							detail: { id: loadingCardId },
						}),
					);
				}

				// 📰 NEWSLETTER GENERATION
				if (analysisResult.contentType === "newsletter") {
					console.log(
						"[ChatBubble] 📰 Detected newsletter request. Submitting AgentTask for outlining.",
					);

					const agentMetadata: Record<string, any> = {
						clientContext: { host: window.location.origin, roomName },
						initialX: retrievedBasePosition.x,
						initialY: retrievedBasePosition.y,
						client_loadingCardId: loadingCardId,
					};

					if (analysisResult.metadataForTask) {
						agentMetadata.productId = analysisResult.metadataForTask.productId;
						agentMetadata.organizationId =
							analysisResult.metadataForTask.organizationId;
						agentMetadata.brandName = analysisResult.metadataForTask.brandName;
						agentMetadata.modelId = analysisResult.metadataForTask.modelId;
					} else {
						console.warn(
							"[ChatBubble] metadataForTask missing for newsletter. Using fallback.",
						);
						if (currentProductId) agentMetadata.modelId = currentProductId;
					}

					submitAgentJob({
						request: message,
						userId: userIdToUse,
						requestType: "generate_newsletter",
						metadata: agentMetadata,
						initialStatus: "pending",
						placeholderId: loadingCardId,
					})
						.then((result: any) => {
							if (result?.taskId) {
								console.log(
									"[ChatBubble] 📰 Newsletter generation started:",
									result.taskId,
								);
							} else {
								console.error(
									"[ChatBubble] Newsletter generation failed:",
									result,
								);
								window.dispatchEvent(
									new CustomEvent("removeLoadingConceptCard", {
										detail: { id: loadingCardId },
									}),
								);
							}
						})
						.catch((err: any) => {
							console.error("[ChatBubble] Newsletter generation error:", err);
							window.dispatchEvent(
								new CustomEvent("removeLoadingConceptCard", {
									detail: { id: loadingCardId },
								}),
							);
						});
				}
				// 🌐 WEBSITE/HTML GENERATION
				else if (analysisResult.contentType === "html") {
					if (htmlRequestSentRef.current) {
						console.log("[ChatBubble] HTML request already sent, skipping.");
						setIsSubmitting(false);
						return;
					}
					htmlRequestSentRef.current = true;

					console.log(
						"[ChatBubble] 🌐 Detected website/HTML request. Submitting AgentTask for HTML generation.",
					);

					const agentMetadata: Record<string, any> = {
						clientContext: { host: window.location.origin, roomName },
						initialX: retrievedBasePosition.x,
						initialY: retrievedBasePosition.y,
						client_loadingCardId: loadingCardId,
						referenceImages: referenceImages,
					};

					if (analysisResult.metadataForTask) {
						agentMetadata.productId = analysisResult.metadataForTask.productId;
						agentMetadata.organizationId =
							analysisResult.metadataForTask.organizationId;
						agentMetadata.brandName = analysisResult.metadataForTask.brandName;
						agentMetadata.modelId = analysisResult.metadataForTask.modelId;
					} else {
						console.warn(
							"[ChatBubble] metadataForTask missing for HTML. Using fallback.",
						);
						if (currentProductId) agentMetadata.modelId = currentProductId;
					}

					submitAgentJob({
						request: message,
						userId: userIdToUse,
						requestType: "generate_html",
						metadata: agentMetadata,
						initialStatus: "pending",
						placeholderId: loadingCardId,
					})
						.then((result: any) => {
							if (result?.taskId) {
								console.log(
									"[ChatBubble] 🌐 Website generation started:",
									result.taskId,
								);
							} else {
								console.error(
									"[ChatBubble] Website generation failed:",
									result,
								);
							}
						})
						.catch((err: any) => {
							console.error("[ChatBubble] Website generation error:", err);
						})
						.finally(() => {
							setTimeout(() => {
								htmlRequestSentRef.current = false;
							}, 1000);
						});
				}
				// 🎨 IMAGE/AD GENERATION (Default)
				else {
					console.log(
						"[ChatBubble] 🎨 Generating images/ads based on analysis:",
						analysisResult,
					);

					const offset = { x: 50, y: 50 };
					for (let i = 0; i < analysisResult.count; i++) {
						let cardPrompt = analysisResult.base_prompt;
						if (
							analysisResult.variations &&
							analysisResult.variations.length > i
						) {
							cardPrompt += ` ${analysisResult.variations[i]}`;
						}

						const cardPosition = {
							x: retrievedBasePosition.x + i * offset.x,
							y: retrievedBasePosition.y + i * offset.y,
						};
						const cardId = `request-${Date.now()}-${i}`;

						window.dispatchEvent(
							new CustomEvent("createConceptCardRequest", {
								detail: {
									id: cardId,
									prompt: cardPrompt,
									position: cardPosition,
									productId: currentProductId,
									referenceImages,
									aspectRatio: analysisResult.aspectRatio,
								},
							}),
						);
					}
				}
			};

			// Get loading card position and process
			window.dispatchEvent(
				new CustomEvent("getLoadingCardPosition", {
					detail: {
						id: loadingCardId,
						callback: (pos: { x: number; y: number } | null) => {
							const retrievedPos = pos || basePosition;
							processRequestBasedOnAnalysis(
								analysis,
								retrievedPos,
								currentUserId,
							);
						},
					},
				}),
			);

			// Clear message and close
			setMessage("");
			handleClose();
		} catch (err: any) {
			console.error("[ChatBubble] Error processing request:", err);
			window.dispatchEvent(
				new CustomEvent("removeLoadingConceptCard", {
					detail: { id: loadingCardId },
				}),
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleKeyPress = (e: React.KeyboardEvent) => {
		if (e.key === "Enter" && !e.shiftKey) handleSubmit(e);
		if (e.key === "Escape") handleClose();
	};

	if (!isVisible) return null;

	return (
		<>
			<style>
				{`
          /* Prevent text selection on the entire chat bubble */
          .chat-bubble-container,
          .chat-bubble-container * {
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
          }

          /* But allow text selection in the input field */
          .chat-bubble-container input {
            user-select: text;
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            cursor: text;
          }

          /* Override global button styles */
          form button[type="submit"] {
            background-color: transparent !important;
            width: auto !important;
            padding: 0 !important;
            border-radius: 0 !important;
            border: none !important;
          }
          form button[type="submit"]:hover {
            background-color: transparent !important;
          }

          /* Send button animations */
          @keyframes spin {
            0% { transform: scale(1); }
            100% { transform: scale(1) rotate(360deg); }
          }

          .send-button-img {
            transition: transform 0.1s ease;
          }

          form button[type="submit"]:active .send-button-img {
            transform: scale(0.85);
          }

          /* Add a class for the spin animation that we'll add via JavaScript */
          .spin-animation {
            animation: spin 0.5s ease;
          }

          .send-button-img {
            position: relative;
            left: -15px; /* Move the image to the left, but not as much */
          }
        `}
			</style>
			<div
				ref={bubbleRef}
				className="fixed z-50 chat-bubble-container"
				style={{
					left: bubblePosition.x + "px",
					top: bubblePosition.y + "px",
					transform: "translate(-50%, -50%)",
				}}
			>
				<form onSubmit={handleSubmit} className="w-[250px]">
					<div
						className="
            flex items-center
            w-full h-11
            bg-[#2D2F33]
            rounded-full
            shadow-lg
            overflow-hidden
            relative
          "
						style={{
							border: currentUserColor
								? `2px solid ${currentUserColor}`
								: "2px solid #9EA581",
						}}
					>
						{/* Drag handle (3-dot grabber) */}
						<div
							className="
              flex flex-col items-center justify-center
              h-full px-3 cursor-grab active:cursor-grabbing
            "
							onMouseDown={handleDragStart}
							onTouchStart={handleDragStart}
						>
							<div className="flex flex-col gap-1">
								<div className="w-1 h-1 rounded-full bg-gray-400"></div>
								<div className="w-1 h-1 rounded-full bg-gray-400"></div>
								<div className="w-1 h-1 rounded-full bg-gray-400"></div>
							</div>
						</div>

						{/* Input field */}
						<input
							ref={inputRef}
							type="text"
							value={message}
							onChange={(e) => setMessage(e.target.value)}
							onKeyDown={handleKeyPress}
							placeholder="Ask Olivia..."
							disabled={isSubmitting}
							className="
              flex-1 h-full px-3 py-2
              bg-transparent text-white placeholder-gray-400
              border-none outline-none
              text-sm
            "
						/>

						{/* Send button */}
						<button
							ref={buttonRef}
							type="submit"
							disabled={isSubmitting || !message.trim()}
							onMouseDown={handleButtonMouseDown}
							onMouseUp={handleButtonMouseUp}
							onTouchStart={handleButtonMouseDown}
							onTouchEnd={handleButtonMouseUp}
							aria-label="Send"
							className="
              flex-none
              p-0.5
              appearance-none
              bg-transparent !important
              border-none
              w-auto !important
              rounded-none !important
              shadow-none
              mr-4
            "
						>
							<img
								src="https://oliviatest.xyz/Aliviasendbutton.png"
								alt="Send"
								width="36"
								height="36"
								className="send-button-img"
							/>
						</button>
					</div>
				</form>
			</div>
		</>
	);
};

export default ChatBubble;
