/**
 * 🎨 Shared Canvas Redirect Utilities
 * 
 * @description Utilities for handling redirects after login for shared canvas access
 * @responsibility Manages post-login redirects for shared canvas links
 * @dependencies Local storage, navigation utilities
 * @ai_context Handles seamless access to shared canvases after authentication
 */

/**
 * Store a shared canvas redirect for after login
 */
export const storeSharedCanvasRedirect = (token: string) => {
  const redirectData = {
    type: 'shared-canvas',
    token,
    timestamp: Date.now(),
  };
  
  localStorage.setItem('redirectAfterLogin', JSON.stringify(redirectData));
};

/**
 * Get and clear stored redirect data
 */
export const getAndClearRedirect = (): { type: string; token?: string; path?: string } | null => {
  try {
    const stored = localStorage.getItem('redirectAfterLogin');
    if (!stored) return null;

    localStorage.removeItem('redirectAfterLogin');

    // Handle legacy string format (direct path)
    if (stored.startsWith('/')) {
      return { type: 'path', path: stored };
    }

    // Handle new object format
    const parsed = JSON.parse(stored);
    return parsed;
  } catch (error) {
    console.error('Error parsing redirect data:', error);
    localStorage.removeItem('redirectAfterLogin');
    return null;
  }
};

/**
 * Handle post-login redirect
 */
export const handlePostLoginRedirect = (navigate: (path: string) => void): boolean => {
  const redirectData = getAndClearRedirect();

  if (!redirectData) return false;

  // Handle shared canvas redirects
  if (redirectData.type === 'shared-canvas' && redirectData.token) {
    navigate(`/canvas/shared/${redirectData.token}`);
    return true;
  }

  // Handle path redirects
  if (redirectData.type === 'path' && redirectData.path) {
    navigate(redirectData.path);
    return true;
  }

  return false;
};

/**
 * Check if current URL is a shared canvas link
 */
export const isSharedCanvasUrl = (pathname: string): { isShared: boolean; token?: string } => {
  const match = pathname.match(/^\/canvas\/shared\/(.+)$/);
  return {
    isShared: !!match,
    token: match?.[1],
  };
};

/**
 * Create a shared canvas URL
 */
export const createSharedCanvasUrl = (token: string, baseUrl?: string): string => {
  const base = baseUrl || window.location.origin;
  return `${base}/canvas/shared/${token}`;
};

/**
 * Extract token from shared canvas URL
 */
export const extractTokenFromUrl = (url: string): string | null => {
  try {
    const urlObj = new URL(url);
    const match = urlObj.pathname.match(/^\/canvas\/shared\/(.+)$/);
    return match?.[1] || null;
  } catch {
    return null;
  }
};

/**
 * Validate shared canvas token format
 */
export const isValidShareToken = (token: string): boolean => {
  // Basic validation - tokens should be non-empty strings
  // Add more specific validation based on your token format
  return typeof token === 'string' && token.length > 0 && !token.includes('/');
};
