// Image cache utility for efficiently loading and caching images
class ImageCache {
  private cache = new Map<string, HTMLImageElement>();
  private loadingPromises = new Map<string, Promise<HTMLImageElement>>();

  async loadImage(url: string): Promise<HTMLImageElement> {
    // Return cached image if available
    if (this.cache.has(url)) {
      return this.cache.get(url)!;
    }

    // Return existing loading promise if image is being loaded
    if (this.loadingPromises.has(url)) {
      return this.loadingPromises.get(url)!;
    }

    // Create new loading promise
    const loadingPromise = new Promise<HTMLImageElement>((resolve, reject) => {
      const img = new Image();

      img.onload = () => {
        this.cache.set(url, img);
        this.loadingPromises.delete(url);
        resolve(img);
      };

      img.onerror = (error) => {
        this.loadingPromises.delete(url);
        reject(new Error(`Failed to load image: ${url}`));
      };

      // Only set crossOrigin for external URLs, not for same-origin or blob URLs
      if (url.startsWith('http') && !url.startsWith(window.location.origin) && !url.startsWith('blob:')) {
        img.crossOrigin = 'anonymous';
      }

      img.src = url;
    });

    this.loadingPromises.set(url, loadingPromise);
    return loadingPromise;
  }

  getImage(url: string): HTMLImageElement | null {
    return this.cache.get(url) || null;
  }

  preloadImage(url: string): Promise<HTMLImageElement> {
    return this.loadImage(url);
  }

  clearCache(): void {
    this.cache.clear();
    this.loadingPromises.clear();
  }

  removeFromCache(url: string): void {
    this.cache.delete(url);
    this.loadingPromises.delete(url);
  }

  getCacheSize(): number {
    return this.cache.size;
  }

  getCachedUrls(): string[] {
    return Array.from(this.cache.keys());
  }
}

// Create a global image cache instance
export const imageCache = new ImageCache();

// Utility function to get image dimensions
export function getImageDimensions(image: HTMLImageElement): {
  width: number;
  height: number;
} {
  return {
    width: image.naturalWidth || image.width,
    height: image.naturalHeight || image.height,
  };
}

// Utility function to calculate scaled dimensions while maintaining aspect ratio
export function calculateScaledDimensions(
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number
): { width: number; height: number } {
  const aspectRatio = originalWidth / originalHeight;

  let newWidth = originalWidth;
  let newHeight = originalHeight;

  // Scale down if too large
  if (originalWidth > maxWidth) {
    newWidth = maxWidth;
    newHeight = newWidth / aspectRatio;
  }

  if (newHeight > maxHeight) {
    newHeight = maxHeight;
    newWidth = newHeight * aspectRatio;
  }

  return {
    width: Math.round(newWidth),
    height: Math.round(newHeight),
  };
}
