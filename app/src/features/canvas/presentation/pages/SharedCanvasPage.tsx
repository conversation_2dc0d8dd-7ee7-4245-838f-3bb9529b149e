/**
 * 🎨 Shared Canvas Page
 * 
 * @description Page for accessing shared canvases via token
 * @responsibility Handles shared canvas access and display
 * @dependencies Canvas sharing queries, Canvas component
 * @ai_context Provides public access to shared canvases
 */

import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from 'wasp/client/auth';
import { useAction } from 'wasp/client/operations';
import { getSharedCanvasByToken, acceptCanvasShare } from 'wasp/client/operations';
import { Canvas } from '../components/canvas/Canvas';
import { UserPlus, LogIn, Eye, MessageSquare, Edit3 } from 'lucide-react';
import { storeSharedCanvasRedirect } from '../utils/shared-canvas-redirect';
// Simple loading spinner component
const LoadingSpinner = ({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  return (
    <div className={`animate-spin rounded-full border-2 border-gray-300 border-t-olive ${sizeClasses[size]}`} />
  );
};

interface SharedCanvasPageProps {}

const SharedCanvasPage: React.FC<SharedCanvasPageProps> = () => {
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  const { data: user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sharedCanvas, setSharedCanvas] = useState<any>(null);
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [hasAccepted, setHasAccepted] = useState(false);

  // Actions
  const acceptShareAction = useAction(acceptCanvasShare);

  useEffect(() => {
    if (!token) {
      setError('Invalid share link');
      setLoading(false);
      return;
    }

    const loadSharedCanvas = async () => {
      try {
        setLoading(true);
        const result = await getSharedCanvasByToken({ token });
        setSharedCanvas(result);

        // Show login prompt if user is not authenticated and this is an email invitation
        if (!user && result.sharedCanvas.sharedWithEmail) {
          setShowLoginPrompt(true);
        }
      } catch (err: any) {
        console.error('Error loading shared canvas:', err);
        if (err.message?.includes('not found')) {
          setError('This shared canvas could not be found.');
        } else if (err.message?.includes('expired')) {
          setError('This share link has expired.');
        } else {
          setError('Failed to load shared canvas. Please check the link and try again.');
        }
      } finally {
        setLoading(false);
      }
    };

    loadSharedCanvas();
  }, [token]); // Removed 'user' to prevent infinite loop

  // Separate effect for handling user authentication changes
  useEffect(() => {
    if (!sharedCanvas || !user) return;

    // Auto-accept email invitations when user logs in
    const handleUserLogin = async () => {
      if (sharedCanvas.sharedCanvas.sharedWithEmail === user.email && !sharedCanvas.sharedCanvas.sharedWithUserId && !hasAccepted) {
        try {
          await acceptShareAction({ token });
          setHasAccepted(true);
          setShowLoginPrompt(false);
        } catch (acceptError) {
          console.warn('Could not auto-accept share:', acceptError);
        }
      }
    };

    handleUserLogin();
  }, [user, sharedCanvas, hasAccepted, token, acceptShareAction]);

  const handleAcceptShare = async () => {
    if (!user) {
      setShowLoginPrompt(true);
      return;
    }

    try {
      await acceptShareAction({ token });
      setHasAccepted(true);
      setShowLoginPrompt(false);
    } catch (error: any) {
      console.error('Error accepting share:', error);
    }
  };

  const handleLogin = () => {
    // Store the shared canvas token for redirect after login
    if (token) {
      storeSharedCanvasRedirect(token);
    }
    navigate('/login');
  };

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case 'edit':
        return <Edit3 size={20} className="text-green-500" />;
      case 'comment':
        return <MessageSquare size={20} className="text-blue-500" />;
      default:
        return <Eye size={20} className="text-gray-500" />;
    }
  };

  const getPermissionText = (permission: string) => {
    switch (permission) {
      case 'edit':
        return 'You can edit this canvas';
      case 'comment':
        return 'You can view and comment on this canvas';
      default:
        return 'You can view this canvas';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-canvas">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Loading shared canvas...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-canvas">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h1 className="text-xl font-semibold text-gray-900 mb-2">Unable to Access Canvas</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => navigate('/')}
            className="bg-olive text-white px-6 py-2 rounded-lg hover:bg-olive/90 transition-colors"
          >
            Go to Home
          </button>
        </div>
      </div>
    );
  }

  if (!sharedCanvas) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-canvas">
        <div className="text-center">
          <p className="text-gray-600">No canvas data available.</p>
        </div>
      </div>
    );
  }

  // Show login prompt for email invitations
  if (showLoginPrompt && !user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-canvas">
        <div className="max-w-md mx-auto p-6 bg-white rounded-xl shadow-lg border border-gray-200">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-olive/10 rounded-full flex items-center justify-center">
              <UserPlus size={24} className="text-olive" />
            </div>
            <h1 className="text-xl font-semibold text-gray-900 mb-2">
              You've been invited to collaborate!
            </h1>
            <p className="text-gray-600 mb-6">
              {sharedCanvas?.sharedCanvas?.sharedBy?.username || 'Someone'} has shared
              "{sharedCanvas?.canvas?.name || 'a canvas'}" with you.
            </p>

            <div className="flex items-center justify-center space-x-2 mb-6 p-3 bg-gray-50 rounded-lg">
              {getPermissionIcon(sharedCanvas?.accessLevel || 'view')}
              <span className="text-sm text-gray-700">
                {getPermissionText(sharedCanvas?.accessLevel || 'view')}
              </span>
            </div>

            <div className="space-y-3">
              <button
                onClick={handleLogin}
                className="w-full flex items-center justify-center space-x-2 bg-olive text-white px-6 py-3 rounded-lg hover:bg-olive/90 transition-colors"
              >
                <LogIn size={18} />
                <span>Sign in to access canvas</span>
              </button>

              <p className="text-xs text-gray-500">
                Don't have an account? You'll be able to create one after clicking above.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-canvas">
      {/* Canvas content */}
      <div className="relative">

        <Canvas
          canvasId={sharedCanvas.sharedCanvas.canvasId}
          className="shared-canvas"
          sharedCanvasToken={token}
          sharedCanvasData={{
            permission: sharedCanvas.permission,
            sharedBy: {
              username: sharedCanvas.sharedBy?.username || 'Unknown User',
              email: sharedCanvas.sharedBy?.email,
            },
          }}
        />

        {/* Overlay for view-only access */}
        {sharedCanvas.accessLevel === 'view' && (
          <div className="absolute inset-0 pointer-events-none bg-transparent">
            {/* This overlay can be used to restrict interactions for view-only users */}
          </div>
        )}
      </div>
    </div>
  );
};

export default SharedCanvasPage;
