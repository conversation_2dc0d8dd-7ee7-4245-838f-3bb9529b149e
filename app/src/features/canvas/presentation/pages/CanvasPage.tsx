/**
 * 🎨 Canvas Page Component
 *
 * @description Main page component using existing canvas infrastructure
 * @responsibility Provides the complete canvas editing interface using proven components
 * @dependencies Existing canvas components
 * @ai_context This wraps existing canvas functionality in modular structure
 */

import React from 'react';
import { useParams } from 'react-router-dom';
import { Canvas } from '../components/canvas/Canvas';
import { ActiveSessionChecker } from '../../../onboarding/infrastructure/hooks/useActiveSessionRedirect';

/**
 * 🎨 Canvas Page Props
 * @ai_context Props for the canvas page component
 */
export interface CanvasPageProps {
  canvasId?: string;
  className?: string;
}

/**
 * 🎨 Canvas Page Component
 * @ai_context Complete canvas editing interface using existing components
 */
export default function CanvasPage({ canvasId, className }: CanvasPageProps) {
  const { id } = useParams<{ id: string }>();

  // Use canvasId from props or URL params
  const activeCanvasId = canvasId || id;

  return (
    <ActiveSessionChecker>
      <div className={`canvas-page ${className || ''} w-full h-full`}>
        <Canvas canvasId={activeCanvasId} className='w-full h-full' />
      </div>
    </ActiveSessionChecker>
  );
}
