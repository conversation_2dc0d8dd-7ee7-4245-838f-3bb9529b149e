/**
 * 🎨 Share Modal Demo Page
 * 
 * @description Demo page for testing the canvas sharing modal
 * @responsibility Provides testing interface for share modal components
 * @dependencies Canvas sharing components
 * @ai_context Demo page for testing and showcasing share modal functionality
 */

import React from 'react';
import { 
  ShareButton, 
  ShareButtonWithCount, 
  CompactShareButton, 
  CanvasHeaderShareButton 
} from '../components/sharing';

const ShareModalDemo: React.FC = () => {
  const demoCanvasId = 'demo-canvas-123';
  const demoCanvasName = 'My Awesome Canvas Design';

  return (
    <div className="min-h-screen bg-canvas p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Canvas Share Modal Demo</h1>
          <p className="text-gray-600">Test the different share button variants and modal functionality</p>
        </div>

        {/* Demo Sections */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Primary Share Button */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Primary Share Button</h3>
            <p className="text-gray-600 mb-4">Main share button with primary styling</p>
            <ShareButton
              canvasId={demoCanvasId}
              canvasName={demoCanvasName}
              variant="primary"
              size="md"
            />
          </div>

          {/* Secondary Share Button */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Secondary Share Button</h3>
            <p className="text-gray-600 mb-4">Secondary variant for less prominent placement</p>
            <ShareButton
              canvasId={demoCanvasId}
              canvasName={demoCanvasName}
              variant="secondary"
              size="md"
            />
          </div>

          {/* Share Button with Count */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Share Button with Count</h3>
            <p className="text-gray-600 mb-4">Shows number of people with access</p>
            <div className="space-y-3">
              <ShareButtonWithCount
                canvasId={demoCanvasId}
                canvasName={demoCanvasName}
                shareCount={0}
                showCount={true}
              />
              <ShareButtonWithCount
                canvasId={demoCanvasId}
                canvasName={demoCanvasName}
                shareCount={5}
                showCount={true}
              />
            </div>
          </div>

          {/* Compact Share Button */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Compact Share Button</h3>
            <p className="text-gray-600 mb-4">Minimal button for tight spaces</p>
            <CompactShareButton
              canvasId={demoCanvasId}
              canvasName={demoCanvasName}
            />
          </div>

          {/* Canvas Header Share Button */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 md:col-span-2">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Canvas Header Share Button</h3>
            <p className="text-gray-600 mb-4">Styled specifically for canvas page headers</p>
            <CanvasHeaderShareButton
              canvasId={demoCanvasId}
              canvasName={demoCanvasName}
            />
          </div>
        </div>

        {/* Mock Canvas Header */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{demoCanvasName}</h2>
              <p className="text-sm text-gray-500">Last edited 2 hours ago</p>
            </div>
            <div className="flex items-center space-x-3">
              <CompactShareButton
                canvasId={demoCanvasId}
                canvasName={demoCanvasName}
              />
              <CanvasHeaderShareButton
                canvasId={demoCanvasId}
                canvasName={demoCanvasName}
              />
            </div>
          </div>
          <div className="p-8 bg-gray-50 text-center">
            <p className="text-gray-500">Canvas content would go here...</p>
            <p className="text-sm text-gray-400 mt-2">This is a demo of how share buttons would appear in a real canvas interface</p>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">How to Test</h3>
          <ul className="text-blue-800 space-y-1 text-sm">
            <li>• Click any share button to open the modal</li>
            <li>• Try entering an email address and selecting permissions</li>
            <li>• Create a public link and copy it</li>
            <li>• Test the responsive design on different screen sizes</li>
            <li>• Check that the modal closes properly with the X button or backdrop click</li>
          </ul>
        </div>

        {/* Feature Status */}
        <div className="bg-green-50 rounded-xl p-6 border border-green-200">
          <h3 className="text-lg font-semibold text-green-900 mb-2">✅ Implemented Features</h3>
          <ul className="text-green-800 space-y-1 text-sm">
            <li>• Figma-style share modal design</li>
            <li>• Email invitation with permission selection</li>
            <li>• Public link creation and copying</li>
            <li>• Multiple share button variants</li>
            <li>• Existing shares management</li>
            <li>• Permission updates and access revocation</li>
            <li>• Toast notifications for user feedback</li>
            <li>• Responsive design for mobile and desktop</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ShareModalDemo;
