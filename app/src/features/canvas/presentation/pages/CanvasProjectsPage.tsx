/**
 * 🎨 Canvas Projects Page
 *
 * @description Main page for viewing and managing canvas projects
 * @responsibility Displays all user canvas projects in a grid layout
 * @dependencies Canvas queries and components
 * @ai_context Follows the same pattern as BrandKitsPage
 */

import React, { useState } from "react";
import {
	useQuery,
	useAction,
	getCanvases,
	deleteCanvas,
	getSharedCanvases,
} from "wasp/client/operations";
import { Plus, Search, Filter, Grid, List, Users } from "lucide-react";
import { toast } from "react-hot-toast";
import { CanvasProjectGrid } from "../components/projects/CanvasProjectGrid";
import { CanvasCreateModal } from "../components/projects/CanvasCreateModal";
import { SharedCanvasSection } from "../components/sharing/SharedCanvasCard";
import { useOrganizationState } from "../../../../organization/store";
import { Button } from "../../../../shared";
import { ActiveSessionChecker } from "../../../onboarding/infrastructure/hooks/useActiveSessionRedirect";

/**
 * 🎨 Canvas Projects Page Component
 * @ai_context Main page for canvas project management
 */
const CanvasProjectsPage: React.FC = () => {
	const { selectedOrganizationId } = useOrganizationState();
	const [searchTerm, setSearchTerm] = useState("");
	const [showCreateModal, setShowCreateModal] = useState(false);
	const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
	const [activeTab, setActiveTab] = useState<"owned" | "shared" | "all">("all");

	// Fetch canvas projects
	const {
		data: canvasProjects,
		isLoading,
		error,
		refetch,
	} = useQuery(
		getCanvases,
		{ organizationId: selectedOrganizationId },
		{ enabled: !!selectedOrganizationId },
	);

	// Fetch shared canvases
	const {
		data: sharedCanvases,
		isLoading: sharedCanvasesLoading,
		error: sharedCanvasesError,
	} = useQuery(getSharedCanvases, {}, { enabled: !!selectedOrganizationId });

	// Delete canvas action
	const deleteCanvasAction = useAction(deleteCanvas);

	// Transform shared canvases to match expected interface
	const transformedSharedCanvases = (sharedCanvases || []).map(
		(sharedCanvas: any) => ({
			id: sharedCanvas.id,
			canvas: {
				id: sharedCanvas.canvasId || sharedCanvas.canvas?.id,
				name: sharedCanvas.canvas?.name || "Untitled Canvas",
				description: sharedCanvas.canvas?.description,
				thumbnail: sharedCanvas.canvas?.thumbnail,
				updatedAt: sharedCanvas.canvas?.updatedAt || sharedCanvas.sharedAt,
			},
			permission: sharedCanvas.permission,
			sharedBy: {
				username: sharedCanvas.sharedBy?.username || "Unknown User",
				email: sharedCanvas.sharedBy?.email,
			},
			sharedAt: sharedCanvas.sharedAt,
			accessCount: sharedCanvas.accessCount || 0,
			lastAccessedAt: sharedCanvas.lastAccessedAt,
		}),
	);

	// Filter canvas projects based on search
	const filteredCanvasProjects =
		canvasProjects?.filter(
			(canvas) =>
				canvas.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				canvas.description?.toLowerCase().includes(searchTerm.toLowerCase()),
		) || [];

	// Filter shared canvases based on search term
	const filteredSharedCanvases = transformedSharedCanvases.filter(
		(sharedCanvas) =>
			sharedCanvas.canvas.name
				.toLowerCase()
				.includes(searchTerm.toLowerCase()) ||
			(sharedCanvas.canvas.description &&
				sharedCanvas.canvas.description
					.toLowerCase()
					.includes(searchTerm.toLowerCase())),
	);

	// Get total counts for tabs
	const ownedCount = filteredCanvasProjects.length;
	const sharedCount = filteredSharedCanvases.length;
	const totalCount = ownedCount + sharedCount;

	// Handle canvas deletion
	const handleDelete = async (canvasId: string) => {
		if (
			!window.confirm(
				"Are you sure you want to delete this canvas project? This action cannot be undone.",
			)
		) {
			return;
		}

		try {
			await deleteCanvasAction({ id: canvasId });
			await refetch();
			toast.success("Canvas project deleted successfully");
		} catch (error) {
			console.error("Error deleting canvas project:", error);
			toast.error("Failed to delete canvas project");
		}
	};

	// Render content based on active tab
	const renderContent = () => {
		const isLoadingAny = isLoading || sharedCanvasesLoading;

		if (isLoadingAny) {
			return (
				<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
					{[...Array(8)].map((_, i) => (
						<div
							key={i}
							className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-[#B5B178]/30 dark:border-gray-600 animate-pulse"
						>
							<div className="aspect-[4/3] bg-gray-200 dark:bg-gray-700 rounded-lg mb-3"></div>
							<div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
							<div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
						</div>
					))}
				</div>
			);
		}

		// Show empty state if no projects found
		const hasOwnedProjects = filteredCanvasProjects.length > 0;
		const hasSharedProjects = filteredSharedCanvases.length > 0;
		const hasAnyProjects = hasOwnedProjects || hasSharedProjects;

		if (!hasAnyProjects) {
			return (
				<div className="text-center py-16">
					<div className="w-32 h-32 mx-auto mb-6 bg-gradient-to-br from-[#676D50]/10 to-[#849068]/10 rounded-3xl flex items-center justify-center">
						{activeTab === "shared" ? (
							<Users size={48} className="text-[#676D50]/60" />
						) : (
							<Plus size={48} className="text-[#676D50]/60" />
						)}
					</div>
					<h3 className="font-display text-2xl font-semibold text-[#676D50] dark:text-white mb-3">
						{searchTerm
							? `No ${activeTab === "all" ? "" : activeTab} canvas projects found`
							: activeTab === "shared"
								? "No shared canvases yet"
								: "Start your creative journey"}
					</h3>
					<p className="text-[#676D50]/70 dark:text-gray-300 text-lg mb-8 max-w-md mx-auto">
						{searchTerm
							? "Try adjusting your search terms to find what you're looking for"
							: activeTab === "shared"
								? "Canvases shared with you will appear here"
								: "Create your first canvas project and bring your ideas to life"}
					</p>
					{!searchTerm && activeTab !== "shared" && (
						<button
							onClick={() => setShowCreateModal(true)}
							className="px-8 py-4 bg-gradient-to-r from-[#676D50] to-[#849068] hover:from-[#849068] hover:to-[#676D50] text-[#F8F4DF] rounded-xl transition-all duration-300 font-medium shadow-lg hover:shadow-xl transform hover:scale-105"
						>
							Create Your First Canvas Project
						</button>
					)}
				</div>
			);
		}

		// Render content based on active tab
		return (
			<div className="space-y-8">
				{/* Owned Projects */}
				{(activeTab === "all" || activeTab === "owned") && hasOwnedProjects && (
					<div>
						{activeTab === "all" && (
							<div className="flex items-center space-x-2 mb-6">
								<h2 className="text-xl font-semibold text-[#676D50] dark:text-white">
									My Projects
								</h2>
								<span className="bg-[#676D50]/10 text-[#676D50] text-sm px-2 py-1 rounded-full">
									{filteredCanvasProjects.length}
								</span>
							</div>
						)}
						<CanvasProjectGrid
							canvasProjects={filteredCanvasProjects}
							viewMode={viewMode}
							onDelete={handleDelete}
						/>
					</div>
				)}

				{/* Shared Projects */}
				{(activeTab === "all" || activeTab === "shared") &&
					hasSharedProjects && (
						<div>
							{activeTab === "all" && (
								<div className="flex items-center space-x-2 mb-6">
									<Users size={20} className="text-[#676D50] dark:text-white" />
									<h2 className="text-xl font-semibold text-[#676D50] dark:text-white">
										Shared with me
									</h2>
									<span className="bg-[#676D50]/10 text-[#676D50] text-sm px-2 py-1 rounded-full">
										{filteredSharedCanvases.length}
									</span>
								</div>
							)}
							<SharedCanvasSection
								sharedCanvases={filteredSharedCanvases}
								onCanvasClick={(canvasId) =>
									(window.location.href = `/canvas/${canvasId}`)
								}
								isLoading={false}
								className="bg-transparent p-0"
							/>
						</div>
					)}
			</div>
		);
	};

	if (!selectedOrganizationId) {
		return (
			<div className="min-h-screen bg-[#F0EFE9] dark:bg-black flex items-center justify-center">
				<div className="text-center">
					<h2 className="text-2xl font-bold text-[#1F2419] dark:text-white mb-4">
						No Organization Found
					</h2>
					<p className="text-[#676D50] dark:text-gray-400">
						Please join an organization to access canvas projects.
					</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="min-h-screen bg-[#F0EFE9] dark:bg-black flex items-center justify-center">
				<div className="text-center">
					<h2 className="text-2xl font-bold text-red-600 mb-4">
						Error Loading Canvas Projects
					</h2>
					<p className="text-[#676D50] dark:text-gray-400 mb-4">
						{error.message || "An unexpected error occurred"}
					</p>
					<Button onClick={() => refetch()} variant="primary">
						Try Again
					</Button>
				</div>
			</div>
		);
	}

	return (
		<ActiveSessionChecker>
			<div className="min-h-screen bg-[#F0EFE9] dark:bg-black text-[#1F2419] dark:text-white font-sans p-8">
				<div className="max-w-7xl mx-auto">
					{/* Header */}
					<div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
						<div>
							<h1 className="font-display text-4xl font-bold text-[#676D50] dark:text-white mb-2">
								Canvas Projects
							</h1>
							<p className="text-[#676D50]/70 dark:text-gray-400 text-lg">
								Create and manage your design projects
							</p>
						</div>
						<div className="flex flex-col sm:flex-row w-full sm:w-auto space-y-2 sm:space-y-0 sm:space-x-4 mt-4 sm:mt-0">
							<Button
								onClick={() => setShowCreateModal(true)}
								variant="primary"
								size="lg"
								icon={<Plus size={20} />}
								iconPosition="left"
								className="w-full sm:w-auto"
							>
								New Canvas Project
							</Button>
						</div>
					</div>

					{/* Tabs */}
					<div className="flex flex-col sm:flex-row gap-4 mb-8">
						<div className="flex bg-white dark:bg-gray-800 rounded-xl border border-[#B5B178]/30 dark:border-gray-600 p-1">
							<button
								onClick={() => setActiveTab("all")}
								className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
									activeTab === "all"
										? "bg-[#676D50] text-white"
										: "text-[#676D50] dark:text-gray-300 hover:bg-[#676D50]/10"
								}`}
							>
								All ({totalCount})
							</button>
							<button
								onClick={() => setActiveTab("owned")}
								className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
									activeTab === "owned"
										? "bg-[#676D50] text-white"
										: "text-[#676D50] dark:text-gray-300 hover:bg-[#676D50]/10"
								}`}
							>
								My Projects ({ownedCount})
							</button>
							<button
								onClick={() => setActiveTab("shared")}
								className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-1 ${
									activeTab === "shared"
										? "bg-[#676D50] text-white"
										: "text-[#676D50] dark:text-gray-300 hover:bg-[#676D50]/10"
								}`}
							>
								<Users size={16} />
								<span>Shared ({sharedCount})</span>
							</button>
						</div>
					</div>

					{/* Search and Filters */}
					<div className="flex flex-col sm:flex-row gap-4 mb-8">
						<div className="relative flex-1">
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#676D50]/60 dark:text-gray-400 w-5 h-5" />
							<input
								type="text"
								placeholder={`Search ${activeTab === "all" ? "all" : activeTab} canvas projects...`}
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className="w-full pl-10 pr-4 py-3 bg-white dark:bg-gray-800 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-transparent text-[#1F2419] dark:text-white placeholder-[#676D50]/60 dark:placeholder-gray-400"
							/>
						</div>
						<div className="flex gap-2">
							<button
								onClick={() => setViewMode("grid")}
								className={`p-3 rounded-xl transition-colors ${
									viewMode === "grid"
										? "bg-[#676D50] text-white"
										: "bg-white dark:bg-gray-800 text-[#676D50] dark:text-gray-400 border border-[#B5B178]/30 dark:border-gray-600"
								}`}
							>
								<Grid size={20} />
							</button>
							<button
								onClick={() => setViewMode("list")}
								className={`p-3 rounded-xl transition-colors ${
									viewMode === "list"
										? "bg-[#676D50] text-white"
										: "bg-white dark:bg-gray-800 text-[#676D50] dark:text-gray-400 border border-[#B5B178]/30 dark:border-gray-600"
								}`}
							>
								<List size={20} />
							</button>
						</div>
					</div>

					{/* Content based on active tab */}
					{renderContent()}
				</div>

				{/* Create Modal */}
				{showCreateModal && (
					<CanvasCreateModal
						isOpen={showCreateModal}
						onClose={() => setShowCreateModal(false)}
						onSuccess={(canvasId) => {
							setShowCreateModal(false);
							refetch();
							toast.success("Canvas project created successfully!");
						}}
					/>
				)}
			</div>
		</ActiveSessionChecker>
	);
};

export default CanvasProjectsPage;
