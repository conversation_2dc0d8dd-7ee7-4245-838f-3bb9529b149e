# Canvas Collaboration System

## Overview

The Canvas Collaboration System enables real-time collaborative editing of canvases using WebSockets and Cloudflare Workers with Durable Objects. Multiple users can simultaneously edit the same canvas, see each other's authentic Mac-style cursors, share canvases with guest users, and navigate to collaborators' positions with click-to-jump functionality.

## Architecture

### High-Level Flow
```
Client (React) ↔ Cloudflare Worker ↔ Durable Object ↔ R2 Storage
     ↕                    ↕                ↕              ↕
WebSocket Hook    WebSocket Upgrade    Room State    Image Storage
```

### Key Components

1. **Client-Side Collaboration** (`useCanvasSync` hook)
2. **WebSocket Infrastructure** (Cloudflare Workers)
3. **Real-Time State Management** (Durable Objects)
4. **Canvas Sharing System** (Public/Private shares)

## Client-Side Implementation

### Core Hook: `useCanvasSync`

**Location:** `src/features/canvas/presentation/hooks/useCanvasSync.ts`

The main collaboration hook that manages:
- WebSocket connections
- Real-time canvas state synchronization
- User presence and cursors
- Optimistic updates
- Guest user management

**Key Features:**
- Stable guest user ID generation (prevents infinite reconnections)
- Sequential guest numbering (Guest 1, Guest 2, etc.)
- Editable guest names with sessionStorage persistence
- Throttled element updates for smooth dragging
- Automatic reconnection with exponential backoff
- Separate drag vs. final update messages
- Real-time name updates via WebSocket

### Canvas Integration

**Location:** `src/features/canvas/presentation/components/canvas/Canvas.tsx`

The Canvas component integrates collaboration through:
- `useCanvasSync` hook for real-time features
- Conditional canvas data loading (skips auth for guests)
- Event handlers for collaborative actions

## WebSocket Infrastructure

### Cloudflare Worker

**Location:** `/canvas-server/src/index.ts`

**Key Features:**
- Custom fetch handler that bypasses Hono for WebSocket upgrades
- Direct WebSocket forwarding to Durable Objects
- CORS handling for development/production environments

**Configuration:** `/canvas-server/wrangler.toml`
- Development: `CORS_ORIGIN = "http://localhost:4010"`
- Production: `CORS_ORIGIN = "*"`

### Durable Object: CanvasRoom

**Location:** `/canvas-server/src/durable-objects/CanvasRoom.ts`

**Responsibilities:**
- WebSocket connection management
- Canvas state persistence
- User presence tracking
- Sequential guest user numbering
- Real-time message broadcasting
- Automatic cleanup of inactive users
- Guest user immediate cleanup on disconnect

**State Management:**
- `canvasState`: Canvas elements, selection, viewport
- `userPresence`: Active users and their cursors
- `sessions`: WebSocket connections

## Message Types

### Client → Server
- `state_request`: Request initial canvas state
- `cursor_move`: Update user cursor position
- `element_create`: Create new canvas element
- `element_update`: Update existing element (final)
- `element_drag_update`: Update during dragging (throttled)
- `element_delete`: Delete canvas element
- `selection_update`: Update selected elements
- `viewport_update`: Update canvas viewport
- `user_name_update`: Update user's display name
- `drag_selection_update`: Real-time drag selection rectangles
- `chat_bubble_create`: Create collaborative chat bubble
- `chat_bubble_update`: Update chat bubble message (live typing)
- `chat_bubble_close`: Close collaborative chat bubble
- `loading_card_create`: Create loading concept card
- `loading_card_update`: Update loading card (position, etc.)
- `loading_card_remove`: Remove loading concept card
- `concept_card_create`: Create full concept card
- `concept_card_update`: Update concept card (position, status, etc.)
- `concept_card_remove`: Delete concept card

### Server → Client
- `state_response`: Initial canvas state and users
- `user_join`: New user joined the room
- `user_leave`: User left the room
- `cursor_move`: User cursor movement
- `element_create`: Element created by another user
- `element_update`: Element updated by another user
- `element_delete`: Element deleted by another user
- `selection_update`: Selection changed by another user
- `user_name_update`: User changed their display name
- `drag_selection_update`: Real-time drag selection rectangles
- `chat_bubble_create`: Collaborative chat bubble created
- `chat_bubble_update`: Live typing in chat bubbles
- `chat_bubble_close`: Chat bubble closed by user
- `loading_card_create`: Loading concept card created by another user
- `loading_card_update`: Loading card updated by another user
- `loading_card_remove`: Loading card removed by another user
- `concept_card_create`: Concept card created by another user
- `concept_card_update`: Concept card updated by another user
- `concept_card_remove`: Concept card deleted by another user

## Canvas Sharing System

### Share Creation

**Location:** `src/features/canvas/infrastructure/wasp/actions/canvas-sharing.actions.ts`

**Process:**
1. User clicks share button
2. `shareCanvas` action creates share record
3. Generates unique token and share URL
4. Optionally sends email invitation

### Share Access

**Location:** `src/features/canvas/presentation/pages/SharedCanvasPage.tsx`

**Process:**
1. Guest accesses share URL with token
2. `getSharedCanvasByToken` validates token and permissions
3. Canvas loads with correct room ID for collaboration
4. Guest gets stable user ID for WebSocket connection

### Guest User Handling

**Key Innovation:** Stable guest user ID generation prevents infinite WebSocket reconnections:

```typescript
// Generate stable guest user ID once and persist it
const stableGuestId = useRef<string | null>(null);
if (!stableGuestId.current) {
  stableGuestId.current = `shared_user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}
```

**Sequential Guest Numbering:**
- Server assigns sequential numbers: Guest 1, Guest 2, Guest 3, etc.
- Numbers are reused when users leave (Guest 1 leaves → next user becomes Guest 1)
- Only increments when multiple guests are active simultaneously
- Guest users are immediately removed from presence on disconnect

**Guest Name Management:**
- Names persist in sessionStorage (cleared when browser closes)
- Inline editing with save/cancel buttons
- Real-time name updates broadcast to all users
- Server-assigned names override client defaults

## Collaboration UI Components

### CollaborationBox Component

**Location:** `src/features/canvas/presentation/components/collaboration/CollaborationBox.tsx`

**Features:**
- Active collaborators dropdown with user avatars
- Current user section for guests with editable names
- Click-to-jump functionality to navigate to other users' cursors
- Proper user filtering (excludes current user from "other users" list)
- Visual hover feedback and tooltips
- Real-time user count and status indicators

**User Interaction:**
- Click on any collaborator to center canvas on their cursor position
- Edit guest names with inline editing UI (pencil icon → input field)
- Hover effects and tooltips for better UX

### CursorManager Component

**Location:** `src/features/canvas/presentation/components/collaboration/CursorManager.tsx`

**Features:**
- Renders authentic Mac-style cursors for all collaborators
- Proper filtering to exclude current user's cursor (prevents ghost cursors)
- Smooth cursor animations with Konva tweening
- User color theming for cursor identification

### CollaboratorCursor Component

**Location:** `src/features/canvas/presentation/components/collaboration/CollaboratorCursor.tsx`

**Features:**
- Authentic Mac cursor design using custom SVG from `/public/cursor/cursor.svg`
- User color theming (cursor body matches user's assigned color)
- Username labels with rounded backgrounds
- Smooth position animations
- Proper scaling (1.05x scale for optimal visibility)

**Cursor Design:**
- Based on authentic macOS cursor SVG
- White outline paths for visibility
- Colored main body and diagonal stem
- Clean typography with Inter font
- Subtle shadows for depth

### Click-to-Jump Navigation

**Feature:** Users can click on any collaborator in the dropdown to instantly navigate to their cursor position.

**Implementation:**
```typescript
const handleJumpToUser = useCallback((userId: string, position: { x: number; y: number }) => {
  if (!stageRef.current) return;

  const stage = stageRef.current;
  const stageWidth = stage.width();
  const stageHeight = stage.height();

  // Calculate position to center the user's cursor on screen
  const newPosition = {
    x: (stageWidth / 2) - (position.x * scale),
    y: (stageHeight / 2) - (position.y * scale),
  };

  // Update canvas position using domain service
  ToolService.canvas.setPosition(newPosition);

  // Update Konva stage position
  stage.position(newPosition);
  stage.batchDraw();
}, [scale]);
```

**User Experience:**
- Clickable user items in collaborators dropdown
- Hover effects with cursor pointer
- Tooltip: "Jump to [username]'s cursor"
- Instant smooth navigation to user's location
- Accounts for current zoom level and viewport

## Advanced Collaboration Features

### Snap Group Collaboration

**Feature:** When users snap/connect images together, all connected images move as a group across all canvases in real-time.

**Implementation:**
- `SnapGroupDragger` dispatches `canvas:elementDragging` events for connected images
- Canvas listens for these events and broadcasts position updates via `updateElement()`
- Other users see entire snap groups moving together, not just the primary dragged image

**Location:** `src/features/canvas/presentation/components/snapping/SnapGroupDragger.tsx`

### Off-Canvas User Indicators

**Feature:** Colored arrows appear at screen edges pointing toward collaborators who are outside the current viewport.

**Location:** `src/features/canvas/presentation/components/collaboration/OffCanvasIndicators.tsx`

**Key Features:**
- **Smart Positioning**: Arrows appear in 8 directions (top, top-right, right, bottom-right, bottom, bottom-left, left, top-left)
- **User Color Matching**: Each arrow uses the collaborator's assigned color
- **Click-to-Jump**: Click any arrow to center the canvas on that user's cursor
- **Real-time Updates**: Arrows appear/disappear as users move in/out of viewport
- **Viewport Awareness**: Converts canvas coordinates to screen coordinates using zoom/pan

**Arrow Design:**
- Triangle shape using CSS `clip-path` for authentic arrow appearance
- 16px size with 12px margin from screen edges
- User's color theming with drop shadow for visibility
- Hover effects (scale + brightness) for interactivity

### Collaborative Chat Bubbles

**Feature:** Right-click on canvas creates chat bubbles visible to all users with live typing functionality.

**Location:** `src/features/canvas/presentation/components/chat/CollaborativeChatBubbles.tsx`

**Key Features:**
- **Real-time Creation**: Right-click broadcasts chat bubble to all users
- **Live Typing**: Other users see typing updates in real-time
- **Canvas Coordinate System**: Bubbles position correctly with canvas pan/zoom
- **User Color Theming**: Bubble borders match user's assigned color
- **Smart Replacement**: New bubbles replace old ones instead of accumulating
- **Background Click Closing**: Click canvas background to close bubbles

**Message Flow:**
1. **Right-click** → Creates local ChatBubble + broadcasts `chat_bubble_create`
2. **Live typing** → Broadcasts `chat_bubble_update` with 300ms throttling
3. **Submit/Close** → Broadcasts `chat_bubble_close`
4. **Background click** → Closes all collaborative bubbles

**Coordinate Handling:**
- **Local bubbles**: Use screen coordinates for positioning
- **Collaborative bubbles**: Store canvas coordinates, convert to screen coordinates per user's viewport
- **Viewport sync**: Bubbles move correctly when users pan/zoom

### Real-time Drag Selection Rectangles

**Feature:** Users can see each other's drag selection rectangles in real-time with user-specific colors.

**Implementation:**
- `DragSelection` component broadcasts selection updates via `updateSelection()`
- Other users see colored selection rectangles during dragging
- Each user's selection uses their assigned color for identification
- 20% opacity fill with 2px stroke for optimal visibility

**Visual Design:**
- User-colored selection rectangles with dashed borders
- Proper scaling based on canvas zoom level
- Performance optimized with `perfectDrawEnabled={false}`
- Non-interactive overlays that don't interfere with canvas interaction

### Collaborative Concept Cards

**Feature:** Real-time collaborative concept card creation, editing, and generation with full synchronization across all canvases.

**Location:** `src/features/canvas/presentation/components/cards/`

**Key Features:**
- **Real-time Creation**: Concept cards appear instantly on all collaborator canvases
- **Position Synchronization**: Dragging concept cards updates positions for all users
- **Loading Card Sync**: "Creating your concept..." loading cards sync across canvases
- **Generate Button State**: "Generate Final Image" shows "Generating..." on all canvases
- **Deletion Sync**: Deleting concept cards removes them from all collaborator canvases
- **User Attribution**: Each collaborative concept card shows creator's name and color
- **Bidirectional Sync**: Works perfectly from both local and shared canvases

**Components:**
1. **`KonvaConceptCards.tsx`** - Main concept card system with collaboration hooks
2. **`CollaborativeConceptCards.tsx`** - Renders concept cards from other users
3. **`KonvaGreenConceptCard.tsx`** - Individual concept card with collaboration support
4. **`KonvaLoadingConceptCard.tsx`** - Loading cards with position sync

**Message Flow:**
1. **Loading Card Creation**: `loading_card_create` → Shows loading card on all canvases
2. **Loading Card Position**: `loading_card_update` → Syncs loading card dragging
3. **Concept Card Creation**: `concept_card_create` → Full concept card appears everywhere
4. **Position Updates**: `concept_card_update` → Syncs concept card dragging
5. **Status Updates**: `concept_card_update` → Syncs "Generating..." button states
6. **Card Removal**: `concept_card_remove` → Deletes cards from all canvases
7. **Loading Card Cleanup**: `loading_card_remove` → Removes loading cards when concept is ready

**User Experience:**
- **Seamless Creation**: Right-click → type request → loading card appears on all canvases
- **Real-time Movement**: Drag any concept card → moves on all collaborator canvases
- **Visual Attribution**: Each card shows "[Username]'s concept" with user color theming
- **Generate State Sync**: Click "Generate Final Image" → shows "Generating..." everywhere
- **Clean Deletion**: Click X → card disappears from all canvases instantly
- **Loading Transitions**: Loading cards automatically become full concept cards for everyone

**Technical Implementation:**
- **Smart State Management**: Separates local vs collaborative concept cards
- **Position Broadcasting**: Real-time position updates via WebSocket
- **User Info Embedding**: Includes user name/color in messages (no lookup required)
- **Dual Removal Handling**: Local removal + broadcast for proper cleanup
- **Event-Driven Updates**: DOM events for local card updates from collaborative messages

## File Structure

### Frontend Files
```
src/features/canvas/
├── presentation/
│   ├── hooks/
│   │   └── useCanvasSync.ts              # Main collaboration hook
│   ├── components/canvas/
│   │   └── Canvas.tsx                    # Canvas component with collaboration
│   ├── components/collaboration/
│   │   ├── CollaborationBox.tsx          # Active users dropdown
│   │   ├── CursorManager.tsx             # Real-time cursor rendering
│   │   ├── CollaboratorCursor.tsx        # Individual cursor component
│   │   └── OffCanvasIndicators.tsx       # Off-screen user arrows
│   ├── components/chat/
│   │   ├── ChatBubble.tsx                # Individual chat bubble
│   │   └── CollaborativeChatBubbles.tsx  # Multi-user chat system
│   ├── components/cards/
│   │   ├── KonvaConceptCards.tsx         # Main concept card system
│   │   ├── CollaborativeConceptCards.tsx # Collaborative concept cards
│   │   ├── KonvaGreenConceptCard.tsx     # Individual concept card
│   │   └── KonvaLoadingConceptCard.tsx   # Loading concept cards
│   ├── components/snapping/
│   │   └── SnapGroupDragger.tsx          # Collaborative snap groups
│   ├── pages/
│   │   └── SharedCanvasPage.tsx          # Shared canvas access page
│   └── components/sharing/
│       └── CanvasHeaderShareButton.tsx   # Share button component
├── infrastructure/wasp/
│   ├── actions/
│   │   └── canvas-sharing.actions.ts     # Share creation/management
│   └── queries/
│       └── canvas-sharing.queries.ts     # Share access queries
├── domain/
│   ├── entities/
│   │   └── shared-canvas.entity.ts       # Share data model
│   ├── services/
│   │   └── canvas-sharing.service.ts     # Share business logic
│   └── repositories/
│       └── canvas-sharing.repository.ts  # Share data access
└── docs/
    └── COLLABORATION.md                  # This documentation
```

### Backend Files (Cloudflare Workers)
```
canvas-server/
├── src/
│   ├── index.ts                         # Main worker with WebSocket handling
│   ├── durable-objects/
│   │   └── CanvasRoom.ts                # Durable Object for room state
│   └── types/
│       └── websocket.types.ts           # WebSocket message types
├── wrangler.toml                        # Cloudflare Worker configuration
└── package.json                         # Dependencies
```

### Database Schema
```
my-saas/app/schema.prisma
├── SharedCanvas model                   # Share records
└── Canvas model                         # Canvas data
```

## Configuration

### Environment Variables

**Development:**
- `WS_BASE_URL`: `https://canvas-workers.j-5bb.workers.dev`
- `CORS_ORIGIN`: `http://localhost:4010`

**Production:**
- `WS_BASE_URL`: Production Cloudflare Worker URL
- `CORS_ORIGIN`: `*` or specific domain

### WebSocket Connection

**URL Format:**
```
wss://canvas-workers.j-5bb.workers.dev/ws/{roomId}?userId={userId}&userName={userName}&userColor={color}&sharedToken={token}
```

**Room ID Logic:**
- Regular canvas: Uses canvas ID (e.g., `cmc3rvn6c0001101f21ivnr7m`)
- Shared canvas: Uses shared canvas's canvas ID (same room as original)

## Performance Optimizations

### Throttled Updates
- **Drag updates**: Sent every 50ms during dragging
- **Final updates**: Sent immediately on drag end
- **Cursor updates**: Real-time, no throttling

### Optimistic Updates
- UI updates immediately for responsiveness
- Server sync happens asynchronously
- Conflicts resolved by server state

### Connection Management
- Automatic reconnection with exponential backoff
- Stable user IDs prevent unnecessary reconnections
- Cleanup of inactive users after 5 minutes

## Troubleshooting

### Common Issues

1. **Infinite Reconnections**
   - Cause: Unstable user ID generation
   - Solution: Ensure stable guest ID in `useCanvasSync`

2. **401 Auth Errors for Guests**
   - Cause: Canvas component trying to load auth-required data
   - Solution: Conditional canvas data loading based on user auth

3. **WebSocket Cancellations**
   - Cause: Routing conflicts between Hono and WebSocket handling
   - Solution: Custom fetch handler that bypasses Hono for WebSocket upgrades

4. **Duplicate Guest Users in UI**
   - Cause: Current user not properly filtered from collaborators list
   - Solution: Filter by user ID instead of name for both auth and guest users

5. **Ghost Cursors (Current User's Cursor Visible)**
   - Cause: CursorManager not filtering out current user's cursor
   - Solution: Pass currentUserId to CursorManager for proper filtering

6. **Guest Numbers Not Reusing Available Slots**
   - Cause: Server incrementing guest numbers without checking for gaps
   - Solution: Find lowest available guest number instead of always incrementing

7. **Stale Guest Names Across Browser Sessions**
   - Cause: Using localStorage which persists across browser sessions
   - Solution: Use sessionStorage for guest data (clears when browser closes)

8. **Snap Groups Not Moving Together for Collaborators**
   - Cause: SnapGroupDragger not broadcasting connected image movements
   - Solution: Listen for `canvas:elementDragging` events and call `updateElement()` for each connected image

9. **Off-Canvas Arrows Not Appearing**
   - Cause: Incorrect coordinate conversion between canvas and screen space
   - Solution: Ensure proper viewport transformation: `screenX = (canvasX * scale) + position.x`

10. **Chat Bubbles Not Visible to Collaborators**
    - Cause: Using screen coordinates instead of canvas coordinates for collaboration
    - Solution: Store canvas coordinates, convert to screen coordinates per user's viewport

11. **Chat Bubbles Not Disappearing on New Right-Click**
    - Cause: `onBubbleCreate` only firing on visibility change, not bubble ID change
    - Solution: Trigger `onBubbleCreate` when `chatBubbleId` changes, not just `isVisible`

12. **Drag Selection Rectangles Not Showing for Other Users**
    - Cause: Selection updates not being broadcast or rendered
    - Solution: Ensure `updateSelection()` is called and `userSelections` state is rendered in Canvas

13. **Concept Cards Not Appearing on Collaborator Canvases**
    - Cause: Concept card creation not being broadcast or collaborative cards not rendering
    - Solution: Ensure `onConceptCardCreate` is called and `CollaborativeConceptCards` component is rendered

14. **Concept Card Position Changes Not Syncing**
    - Cause: Position updates not being broadcast or collaborative position updates not handled
    - Solution: Verify `onPositionUpdate` callback is wired up and `concept_card_update` messages are handled

15. **Loading Cards Not Disappearing When Concept Cards Are Ready**
    - Cause: Loading card removal not being broadcast when concept card is created
    - Solution: Ensure `onLoadingCardRemove` is called when concept card replaces loading card

16. **"Generate Final Image" Button Not Showing "Generating..." on All Canvases**
    - Cause: Status updates not being broadcast or collaborative status updates not handled
    - Solution: Verify `onStatusUpdate` callback broadcasts status and `updateConceptCardStatus` events are handled

17. **Concept Cards Showing "Unknown User" Instead of Creator Name**
    - Cause: User lookup failing due to empty users array or user info not included in messages
    - Solution: Include user name and color directly in concept card creation messages instead of relying on lookup

18. **Deleting Concept Cards Only Works One Direction**
    - Cause: Collaborative concept card removal not removing from local state or vice versa
    - Solution: Implement dual removal handling - local removal + broadcast for proper cleanup

19. **Loading Cards Not Syncing Position When Dragged**
    - Cause: Loading card position updates not being broadcast
    - Solution: Ensure `onLoadingCardUpdate` is called when loading cards are dragged

### Debug Tools

1. **Browser Console**: WebSocket connection logs
2. **Cloudflare Logs**: `npx wrangler tail` for server-side debugging
3. **Network Tab**: WebSocket message inspection

## Security Considerations

### Share Permissions
- `VIEW`: Read-only access
- `EDIT`: Full editing capabilities
- `COMMENT`: View + commenting (future)

### Token Security
- Unique random tokens for each share
- Optional expiration dates
- Revocable shares
- Email-based access control

### Guest User Isolation
- Guest users can't access auth-required features
- Limited to shared canvas collaboration
- No persistent data access

## Complete File Listing

### Core Collaboration Files

#### Frontend (React/TypeScript)
1. **`src/features/canvas/presentation/hooks/useCanvasSync.ts`**
   - Main collaboration hook
   - WebSocket connection management
   - Real-time state synchronization
   - Guest user ID generation

2. **`src/features/canvas/presentation/components/canvas/Canvas.tsx`**
   - Canvas component with collaboration integration
   - Conditional auth handling for guests
   - Event handlers for collaborative actions

3. **`src/features/canvas/presentation/pages/SharedCanvasPage.tsx`**
   - Shared canvas access page
   - Token validation and canvas loading
   - Guest user interface

4. **`src/features/canvas/presentation/components/sharing/CanvasHeaderShareButton.tsx`**
   - Share button component
   - Share modal and link generation

#### Backend Infrastructure (WASP)
5. **`src/features/canvas/infrastructure/wasp/actions/canvas-sharing.actions.ts`**
   - Share creation and management actions
   - Email invitation system
   - Share revocation

6. **`src/features/canvas/infrastructure/wasp/queries/canvas-sharing.queries.ts`**
   - Share access queries
   - Token validation
   - Guest canvas access

#### Domain Layer
7. **`src/features/canvas/domain/entities/shared-canvas.entity.ts`**
   - Share data model and validation
   - Permission types and settings
   - Entity conversion methods

8. **`src/features/canvas/domain/services/canvas-sharing.service.ts`**
   - Share business logic
   - Access control and validation
   - URL generation

9. **`src/features/canvas/domain/repositories/canvas-sharing.repository.ts`**
   - Share data access layer
   - Database operations
   - Relationship management

#### WebSocket Infrastructure (Cloudflare Workers)
10. **`canvas-server/src/index.ts`**
    - Main Cloudflare Worker
    - Custom WebSocket handling
    - Request routing and CORS

11. **`canvas-server/src/durable-objects/CanvasRoom.ts`**
    - Durable Object for room state
    - WebSocket connection management
    - Real-time message broadcasting
    - State persistence

12. **`canvas-server/src/types/websocket.types.ts`**
    - WebSocket message type definitions
    - Canvas state interfaces
    - User presence types

#### Configuration Files
13. **`canvas-server/wrangler.toml`**
    - Cloudflare Worker configuration
    - Environment settings
    - Durable Object bindings

14. **`canvas-server/package.json`**
    - Worker dependencies
    - Build scripts

15. **`my-saas/app/schema.prisma`**
    - Database schema
    - SharedCanvas model
    - Canvas relationships

#### WASP Configuration
16. **`my-saas/app/main.wasp`**
    - Canvas sharing actions and queries
    - Entity definitions
    - Route configurations

### Supporting Files

#### Types and Interfaces
17. **`src/features/canvas/types/collaboration.types.ts`**
    - Collaboration-specific types
    - User presence interfaces
    - WebSocket message types

#### Utilities
18. **`src/features/canvas/presentation/utils/shared-canvas-redirect.ts`**
    - Redirect handling for shared canvases
    - URL parameter management

#### Hooks
19. **`src/features/canvas/presentation/hooks/useCanvas.hook.ts`**
    - Canvas data loading hook
    - Conditional auth handling

### Dependencies

#### Frontend Dependencies (package.json)
- `react-use-websocket`: WebSocket connection management
- `konva` & `react-konva`: Canvas rendering
- `@tanstack/react-query`: Data fetching and caching
- `wasp`: Full-stack framework

#### Worker Dependencies (canvas-server/package.json)
- `hono`: Web framework for Workers
- `@cloudflare/workers-types`: TypeScript types

## Future Enhancements

### Planned Features
- **Concept Card Image Streaming**: Real-time streaming of partial images during generation visible to all collaborators
- **Enhanced Chat Bubbles**: Persistent chat history and threaded conversations
- **Voice/Video Chat**: Integrated communication for collaborators
- **Follow Mode**: Automatically follow another user's viewport
- **Collaborative Annotations**: Sticky notes and markup tools
- **Version History**: Track and restore canvas versions
- **Enhanced Presence Indicators**: Show user avatars with profile pictures
- **Conflict Resolution**: Handle simultaneous edits gracefully
- **Offline Support**: Queue changes when disconnected
- **Multi-Selection Collaboration**: See other users' multi-element selections
- **Collaborative Layers**: Shared layer management and visibility controls
- **Concept Card Collaboration Enhancements**:
  - Real-time suggestion chip interactions
  - Collaborative concept card editing
  - Shared concept card templates
  - Multi-user concept card generation queues

### Scalability Improvements
- **Room Sharding**: Distribute large rooms across multiple Durable Objects
- **Message Compression**: Reduce WebSocket bandwidth usage
- **State Snapshots**: Periodic state persistence for faster loading
