/**
 * 🎨 Canvas Feature Exports
 *
 * @description Central export point for the canvas feature
 * @responsibility Provides clean imports for canvas functionality
 * @dependencies All canvas modules
 * @ai_context This is the main entry point for the canvas feature
 *
 * @example
 * ```typescript
 * import { Canvas, CanvasService, saveCanvas } from '@features/canvas';
 * import { CanvasToolType, ImageElement } from '@features/canvas';
 * ```
 */

// Domain Layer Exports
export * from './domain/entities/canvas.entity';
export * from './domain/services/canvas.service';
export * from './domain/repositories/canvas.repository';

// Infrastructure Layer Exports (Type definitions only)
export type {
  CreateCanvasInput,
  UpdateCanvasInput,
  DeleteCanvasInput,
  CreateCanvasReferenceInput,
} from './infrastructure/wasp/actions/save-canvas';

export type {
  GetCanvasInput,
  GetCanvasesInput,
  GetCanvasElementsInput,
  GetCanvasReferencesInput,
} from './infrastructure/wasp/queries/get-canvas';

// Presentation Layer Exports
export { default as CanvasPage } from './presentation/pages/CanvasPage';
export { default as CanvasProjectsPage } from './presentation/pages/CanvasProjectsPage';
export { Canvas } from './presentation/components/canvas/Canvas';
export { CanvasProjectGrid } from './presentation/components/projects/CanvasProjectGrid';
export { CanvasProjectCard } from './presentation/components/projects/CanvasProjectCard';
export { CanvasCreateModal } from './presentation/components/projects/CanvasCreateModal';
export { useCanvas } from './presentation/hooks/useCanvas.hook';

// Re-export commonly used items for convenience
export { Canvas as CanvasEntity, CanvasStatus } from './domain/entities/canvas.entity';
export { CanvasService } from './domain/services/canvas.service';

// Type exports
export type {
  ImageElement,
  TextElement,
  HtmlElement,
  CanvasToolType,
  CanvasState,
  SelectionState,
} from './domain/entities/canvas.entity';
