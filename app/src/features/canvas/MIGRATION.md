# 🔄 Canvas Migration Plan

## 🎯 Goal: Move Existing Canvas to Modular Structure

Instead of recreating everything from scratch, we're **rearranging existing working code** into our modular architecture.

## ✅ Phase 1: Basic Integration (DONE)

- ✅ Created modular structure
- ✅ Set up test routes (`/canvas/test`, `/canvas/compare`)
- ✅ Fixed client-side import issues
- ✅ **STARTED PROPER MIGRATION**: Moved KonvaCanvas imports to modular Canvas component
- ✅ **FOUNDATION READY**: Canvas component now has all the imports and structure from KonvaCanvas

## 🔄 Phase 2: Component Migration (NEXT)

### **Move Canvas Components to Presentation Layer**

#### **From: `/client/canvas/`**
#### **To: `/features/canvas/presentation/components/`**

```
client/canvas/                          → features/canvas/presentation/components/
├── KonvaCanvas.tsx                     → canvas/KonvaCanvas.tsx
├── image-elements.tsx                  → elements/ImageElements.tsx
├── text-elements.tsx                   → elements/TextElements.tsx
├── html-elements.tsx                   → elements/HtmlElements.tsx
├── canvas-toolbar.tsx                  → toolbar/CanvasToolbar.tsx
├── ZoomControls.tsx                    → controls/ZoomControls.tsx
├── collaboration/                      → collaboration/
│   ├── CollaboratorCursors.tsx        → CollaboratorCursors.tsx
│   └── UserSelections.tsx             → UserSelections.tsx
└── modals/                            → modals/
    ├── ImageHistoryModal.tsx          → ImageHistoryModal.tsx
    └── CanvasSettingsModal.tsx        → CanvasSettingsModal.tsx
```

### **Move Canvas Store to Domain Layer**

#### **From: `/client/canvas/canvas-store.tsx`**
#### **To: `/features/canvas/domain/services/canvas-state.service.ts`**

- Extract business logic from Zustand store
- Move to domain service following SOLID principles
- Keep UI state in presentation layer

### **Move Canvas Hooks to Presentation Layer**

#### **From: `/client/canvas/hooks/`**
#### **To: `/features/canvas/presentation/hooks/`**

```
client/canvas/hooks/                    → features/canvas/presentation/hooks/
├── useCanvasImages.ts                  → useCanvasImages.hook.ts
├── useCanvasHistory.ts                 → useCanvasHistory.hook.ts
├── useCanvasTools.ts                   → useCanvasTools.hook.ts
└── useCollaboration.ts                 → useCollaboration.hook.ts
```

## 🔄 Phase 3: Business Logic Extraction

### **Extract to Domain Services**

1. **Canvas Operations** → `domain/services/canvas-operations.service.ts`
2. **Image Processing** → `domain/services/image-processing.service.ts`
3. **Collaboration Logic** → `domain/services/collaboration.service.ts`
4. **History Management** → `domain/services/history.service.ts`
5. **Tool Management** → `domain/services/tool-management.service.ts`

### **Create Domain Entities**

1. **Canvas Elements** → `domain/entities/canvas-elements.entity.ts`
2. **Collaboration State** → `domain/entities/collaboration.entity.ts`
3. **Tool State** → `domain/entities/tool-state.entity.ts`

## 🔄 Phase 4: Infrastructure Integration

### **Move WASP Operations**

- Keep existing WASP actions/queries
- Create clean interfaces in infrastructure layer
- Update imports to use modular structure

### **External Service Integration**

1. **WebSocket** → `infrastructure/websocket/canvas-websocket.ts`
2. **AI Services** → `infrastructure/ai/canvas-ai.service.ts`
3. **Storage** → `infrastructure/storage/canvas-storage.service.ts`

## 🧪 Testing Strategy

### **Incremental Migration**

1. **Test each component** as it's moved
2. **Keep old routes working** during migration
3. **Use comparison page** to verify functionality
4. **Gradual replacement** of old imports

### **Validation Points**

- ✅ Canvas rendering works
- ✅ Tool switching works
- ✅ Element manipulation works
- ✅ Collaboration works
- ✅ Persistence works
- ✅ Performance is maintained

## 📋 Migration Checklist

### **Phase 2: Component Migration**
- [ ] Move KonvaCanvas to modular structure
- [ ] Move element components (Image, Text, HTML)
- [ ] Move toolbar and controls
- [ ] Move collaboration components
- [ ] Move modal components
- [ ] Update all imports

### **Phase 3: Business Logic**
- [ ] Extract canvas operations to domain services
- [ ] Extract image processing logic
- [ ] Extract collaboration logic
- [ ] Extract history management
- [ ] Create domain entities
- [ ] Update presentation layer to use domain services

### **Phase 4: Infrastructure**
- [ ] Clean up WASP operation interfaces
- [ ] Move external service integrations
- [ ] Update WebSocket handling
- [ ] Test end-to-end functionality

### **Phase 5: Cleanup**
- [ ] Remove old canvas directory
- [ ] Update all imports throughout codebase
- [ ] Update documentation
- [ ] Performance testing

## 🎯 Success Criteria

1. **✅ All existing functionality preserved**
2. **✅ Modular architecture implemented**
3. **✅ Business logic in domain services**
4. **✅ Clean separation of concerns**
5. **✅ Improved maintainability**
6. **✅ Better testability**

## 🚀 Current Status

**Phase 1: COMPLETE** ✅
- Basic modular structure created
- Existing KonvaCanvas wrapped in modular component
- Test routes working
- Ready for component migration

**Next Step: Start Phase 2** 🔄
- Begin moving individual components
- Test each component as it's moved
- Maintain functionality throughout migration
