/**
 * 🗄️ Canvas Repository Implementation
 *
 * @description Server-side implementation of canvas repository
 * @responsibility Handles canvas data persistence (server-side only)
 * @dependencies Canvas entity, repository interface
 * @ai_context This implements the repository pattern for canvas data access
 *
 * Note: This file should only be used on the server side.
 * Client-side canvas operations should use WASP client operations.
 */

import { Canvas, CanvasStatus } from '../../domain/entities/canvas.entity';
import type {
  ICanvasRepository,
  CanvasListOptions,
  CanvasSearchOptions,
  CanvasStatistics,
} from '../../domain/repositories/canvas.repository';

/**
 * 🗄️ Canvas Repository Implementation
 * @ai_context Prisma-based repository implementation
 */
export class CanvasRepositoryImpl implements ICanvasRepository {
  constructor(private entities: any) {}

  /**
   * 💾 Save a canvas (create or update)
   */
  async save(canvas: Canvas): Promise<void> {
    const canvasData = canvas.toState();

    try {
      await this.entities.Canvas.upsert({
        where: { id: canvas.id },
        update: {
          name: canvas.name,
          status: canvas.status,
          canvasSize: canvasData.canvasSize,
          activeTool: canvasData.activeTool,
          selectedIds: canvasData.selectedIds,
          scale: canvasData.scale,
          position: canvasData.position,
          selection: canvasData.selection,
          isCollaborative: canvasData.isCollaborative,
          collaborators: canvasData.collaborators,
          updatedAt: new Date(),
        },
        create: {
          id: canvas.id,
          name: canvas.name,
          userId: canvas.userId,
          organizationId: canvas.organizationId,
          status: canvas.status,
          canvasSize: canvasData.canvasSize,
          activeTool: canvasData.activeTool,
          selectedIds: canvasData.selectedIds,
          scale: canvasData.scale,
          position: canvasData.position,
          selection: canvasData.selection,
          isCollaborative: canvasData.isCollaborative,
          collaborators: canvasData.collaborators,
          createdAt: canvas.createdAt,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      console.error('Error saving canvas:', error);
      throw new Error('Failed to save canvas');
    }
  }

  /**
   * 🔍 Find canvas by ID
   */
  async findById(id: string): Promise<Canvas | null> {
    try {
      const canvasData = await this.entities.Canvas.findUnique({
        where: { id },
      });

      if (!canvasData) {
        return null;
      }

      return this.mapToCanvas(canvasData);
    } catch (error) {
      console.error('Error finding canvas by ID:', error);
      throw new Error('Failed to find canvas');
    }
  }

  /**
   * 🔍 Find canvas by name and organization
   */
  async findByNameAndOrganization(name: string, organizationId: string): Promise<Canvas | null> {
    try {
      const canvasData = await this.entities.Canvas.findFirst({
        where: {
          name,
          organizationId,
          deletedAt: null,
        },
      });

      if (!canvasData) {
        return null;
      }

      return this.mapToCanvas(canvasData);
    } catch (error) {
      console.error('Error finding canvas by name and organization:', error);
      throw new Error('Failed to find canvas');
    }
  }

  /**
   * 📋 List canvases with filtering and pagination
   */
  async list(options: Partial<CanvasListOptions> = {}): Promise<Canvas[]> {
    try {
      const where: any = {};

      if (options.userId) where.userId = options.userId;
      if (options.organizationId) where.organizationId = options.organizationId;
      if (options.status) where.status = options.status;
      if (options.isCollaborative !== undefined) where.isCollaborative = options.isCollaborative;
      if (!options.includeDeleted) where.deletedAt = null;

      const canvases = await this.entities.Canvas.findMany({
        where,
        take: options.limit || 50,
        skip: options.offset || 0,
        orderBy: {
          [options.sortBy || 'updatedAt']: options.sortOrder || 'desc',
        },
      });

      return canvases.map((canvas: any) => this.mapToCanvas(canvas));
    } catch (error) {
      console.error('Error listing canvases:', error);
      throw new Error('Failed to list canvases');
    }
  }

  /**
   * 🔍 Find canvases by user
   */
  async findByUser(
    userId: number,
    organizationId: string,
    options: Partial<CanvasListOptions> = {}
  ): Promise<Canvas[]> {
    return this.list({
      ...options,
      userId,
      organizationId,
    });
  }

  /**
   * 🔍 Find collaborative canvases
   */
  async findCollaborativeCanvases(
    userId: number,
    organizationId: string,
    options: Partial<CanvasListOptions> = {}
  ): Promise<Canvas[]> {
    try {
      const where: any = {
        organizationId,
        isCollaborative: true,
        OR: [{ userId }, { collaborators: { has: userId.toString() } }],
      };

      if (!options.includeDeleted) where.deletedAt = null;

      const canvases = await this.entities.Canvas.findMany({
        where,
        take: options.limit || 50,
        skip: options.offset || 0,
        orderBy: {
          [options.sortBy || 'updatedAt']: options.sortOrder || 'desc',
        },
      });

      return canvases.map((canvas: any) => this.mapToCanvas(canvas));
    } catch (error) {
      console.error('Error finding collaborative canvases:', error);
      throw new Error('Failed to find collaborative canvases');
    }
  }

  /**
   * 🔍 Search canvases
   */
  async search(query: string, organizationId: string, options: Partial<CanvasSearchOptions> = {}): Promise<Canvas[]> {
    try {
      const where: any = {
        organizationId,
        name: {
          contains: query,
          mode: 'insensitive',
        },
      };

      if (options.userId) where.userId = options.userId;
      if (options.status) where.status = options.status;
      if (options.isCollaborative !== undefined) where.isCollaborative = options.isCollaborative;
      if (!options.includeDeleted) where.deletedAt = null;

      const canvases = await this.entities.Canvas.findMany({
        where,
        take: options.limit || 50,
        skip: options.offset || 0,
        orderBy: {
          updatedAt: 'desc',
        },
      });

      return canvases.map((canvas: any) => this.mapToCanvas(canvas));
    } catch (error) {
      console.error('Error searching canvases:', error);
      throw new Error('Failed to search canvases');
    }
  }

  /**
   * 🗑️ Delete a canvas (soft delete)
   */
  async delete(id: string): Promise<void> {
    try {
      await this.entities.Canvas.update({
        where: { id },
        data: {
          deletedAt: new Date(),
          status: CanvasStatus.DELETED,
        },
      });
    } catch (error) {
      console.error('Error deleting canvas:', error);
      throw new Error('Failed to delete canvas');
    }
  }

  /**
   * 🗑️ Permanently delete a canvas
   */
  async permanentlyDelete(id: string): Promise<void> {
    try {
      await this.entities.Canvas.delete({
        where: { id },
      });
    } catch (error) {
      console.error('Error permanently deleting canvas:', error);
      throw new Error('Failed to permanently delete canvas');
    }
  }

  /**
   * 🔄 Restore a soft-deleted canvas
   */
  async restore(id: string): Promise<void> {
    try {
      await this.entities.Canvas.update({
        where: { id },
        data: {
          deletedAt: null,
          status: CanvasStatus.ACTIVE,
        },
      });
    } catch (error) {
      console.error('Error restoring canvas:', error);
      throw new Error('Failed to restore canvas');
    }
  }

  /**
   * ✅ Check if canvas exists
   */
  async exists(id: string): Promise<boolean> {
    try {
      const count = await this.entities.Canvas.count({
        where: { id },
      });
      return count > 0;
    } catch (error) {
      console.error('Error checking canvas existence:', error);
      return false;
    }
  }

  /**
   * 🔢 Count canvases
   */
  async count(options: Partial<CanvasListOptions> = {}): Promise<number> {
    try {
      const where: any = {};

      if (options.userId) where.userId = options.userId;
      if (options.organizationId) where.organizationId = options.organizationId;
      if (options.status) where.status = options.status;
      if (options.isCollaborative !== undefined) where.isCollaborative = options.isCollaborative;
      if (!options.includeDeleted) where.deletedAt = null;

      return await this.entities.Canvas.count({ where });
    } catch (error) {
      console.error('Error counting canvases:', error);
      throw new Error('Failed to count canvases');
    }
  }

  // Placeholder implementations for remaining methods
  async getStatistics(organizationId: string): Promise<CanvasStatistics> {
    // TODO: Implement statistics calculation
    return {
      totalCanvases: 0,
      activeCanvases: 0,
      draftCanvases: 0,
      archivedCanvases: 0,
      collaborativeCanvases: 0,
      totalElements: 0,
      averageElementsPerCanvas: 0,
      mostUsedTools: [],
      createdThisMonth: 0,
      createdThisWeek: 0,
    };
  }

  async addCollaborator(canvasId: string, userId: number): Promise<void> {
    // TODO: Implement collaborator management
  }

  async removeCollaborator(canvasId: string, userId: number): Promise<void> {
    // TODO: Implement collaborator management
  }

  async getCollaborators(canvasId: string): Promise<number[]> {
    // TODO: Implement collaborator retrieval
    return [];
  }

  async recordActivity(canvasId: string, userId: number, activity: string): Promise<void> {
    // TODO: Implement activity recording
  }

  async getActivityHistory(
    canvasId: string,
    limit?: number
  ): Promise<Array<{ userId: number; activity: string; timestamp: Date }>> {
    // TODO: Implement activity history
    return [];
  }

  async duplicate(canvasId: string, newName: string, userId: number): Promise<Canvas> {
    // TODO: Implement canvas duplication
    throw new Error('Canvas duplication not implemented');
  }

  async export(canvasId: string, format: 'json' | 'pdf' | 'png'): Promise<Buffer | string> {
    // TODO: Implement canvas export
    throw new Error('Canvas export not implemented');
  }

  async import(data: any, userId: number, organizationId: string): Promise<Canvas> {
    // TODO: Implement canvas import
    throw new Error('Canvas import not implemented');
  }

  async archive(canvasId: string): Promise<void> {
    // TODO: Implement canvas archiving
  }

  async unarchive(canvasId: string): Promise<void> {
    // TODO: Implement canvas unarchiving
  }

  async getTags(organizationId: string): Promise<string[]> {
    // TODO: Implement tag management
    return [];
  }

  async addTag(canvasId: string, tag: string): Promise<void> {
    // TODO: Implement tag management
  }

  async removeTag(canvasId: string, tag: string): Promise<void> {
    // TODO: Implement tag management
  }

  async findByTag(tag: string, organizationId: string, options?: Partial<CanvasListOptions>): Promise<Canvas[]> {
    // TODO: Implement tag-based search
    return [];
  }

  /**
   * 🔄 Map database entity to domain model
   */
  private mapToCanvas(data: any): Canvas {
    return Canvas.create({
      id: data.id,
      name: data.name,
      userId: data.userId,
      organizationId: data.organizationId,
      canvasSize: data.canvasSize || { width: 1920, height: 1080 },
      isCollaborative: data.isCollaborative || false,
    });
  }
}
