/**
 * 🎨 Canvas Sharing Actions
 * 
 * @description WASP actions for canvas sharing functionality
 * @responsibility Handles canvas sharing operations via WASP framework
 * @dependencies Canvas sharing domain layer, WASP operations
 * @ai_context Follows existing WASP action patterns for canvas operations
 */

import { HttpError } from 'wasp/server';
import { prisma } from 'wasp/server';
import { emailSender } from 'wasp/server/email';
import { authenticateUser } from '../../../../../server/helpers';
// Remove typed imports - use standard WASP pattern

// Import domain layer
import {
  CanvasSharingService,
  PrismaCanvasSharingRepository,
  CanvasSharingPermission,
  type ShareCanvasRequest,
  type ShareCanvasResponse,
  type CreatePublicLinkRequest,
  type CreatePublicLinkResponse
} from '../../../domain';

// Import email template
import { inviteToSharedCanvasTemplate } from '../../../../../server/template/emails/invite-to-shared-canvas';

// Initialize service with repository
const canvasSharingRepository = new PrismaCanvasSharingRepository(prisma);
const canvasSharingService = new CanvasSharingService(canvasSharingRepository);

/**
 * 📤 Share Canvas Action
 * @description Share a canvas with an email address
 */
export const shareCanvas = async (args: ShareCanvasRequest, context: any) => {
  try {
    const currentUser = authenticateUser(context);

    // Validate input
    if (!args.canvasId || !args.sharedWithEmail) {
      throw new HttpError(400, 'Canvas ID and email are required');
    }

    // Check if user owns the canvas or has permission to share
    const canvas = await prisma.canvas.findUnique({
      where: { id: args.canvasId },
      include: {
        user: true,
        organization: {
          include: {
            memberships: {
              where: { userId: currentUser.id },
            },
          },
        },
      },
    });

    if (!canvas) {
      throw new HttpError(404, 'Canvas not found');
    }

    // Check permissions
    const isOwner = canvas.userId === currentUser.id;
    const isOrgAdmin = canvas.organization?.memberships.some(
      (m) => m.role === 'ADMIN' || m.role === 'OWNER'
    );

    if (!isOwner && !isOrgAdmin) {
      throw new HttpError(403, 'You do not have permission to share this canvas');
    }

    // Share the canvas using domain service
    const shareResult = await canvasSharingService.shareCanvas({
      canvasId: args.canvasId,
      sharedByUserId: parseInt(currentUser.id, 10),
      sharedWithEmail: args.sharedWithEmail,
      permission: args.permission || CanvasSharingPermission.VIEW,
      expiresAt: args.expiresAt,
      settings: args.settings,
    });

    // Send email notification
    try {
      const emailTemplate = inviteToSharedCanvasTemplate({
        inviterName: currentUser.username || 'Someone',
        canvasName: canvas.name,
        inviteeEmail: args.sharedWithEmail,
        inviteLink: shareResult.shareUrl,
        permission: args.permission || 'view',
        canvasThumbnail: canvas.thumbnail || undefined,
      });

      await emailSender.send(emailTemplate);
    } catch (emailError) {
      console.error('[ShareCanvas] Failed to send email notification:', emailError);
      // Don't fail the entire operation if email fails
    }

    return shareResult;
  } catch (error) {
    console.error('[ShareCanvas] Error sharing canvas:', error);
    throw error instanceof HttpError ? error : new HttpError(500, 'Failed to share canvas');
  }
};

/**
 * 🔗 Create Public Canvas Link Action
 * @description Create a public share link for a canvas
 */
export const createPublicCanvasLink = async (args: CreatePublicLinkRequest, context: any) => {
  try {
    const currentUser = authenticateUser(context);

    // Validate input
    if (!args.canvasId) {
      throw new HttpError(400, 'Canvas ID is required');
    }

    // Check if user owns the canvas or has permission to share
    const canvas = await prisma.canvas.findUnique({
      where: { id: args.canvasId },
      include: {
        user: true,
        organization: {
          include: {
            memberships: {
              where: { userId: currentUser.id },
            },
          },
        },
      },
    });

    if (!canvas) {
      throw new HttpError(404, 'Canvas not found');
    }

    // Check permissions
    const isOwner = canvas.userId === currentUser.id;
    const isOrgAdmin = canvas.organization?.memberships.some(
      (m) => m.role === 'ADMIN' || m.role === 'OWNER'
    );

    if (!isOwner && !isOrgAdmin) {
      throw new HttpError(403, 'You do not have permission to share this canvas');
    }

    // Create public link using domain service
    const linkResult = await canvasSharingService.createPublicLink({
      canvasId: args.canvasId,
      sharedByUserId: parseInt(currentUser.id, 10),
      permission: args.permission || CanvasSharingPermission.VIEW,
      expiresAt: args.expiresAt,
      settings: args.settings,
    });

    return linkResult;
  } catch (error) {
    console.error('[CreatePublicCanvasLink] Error creating public link:', error);
    throw error instanceof HttpError ? error : new HttpError(500, 'Failed to create public link');
  }
};

/**
 * 📋 Get Canvas Shares Query
 * @description Get all shares for a canvas
 */
export const getCanvasShares = async (args: { canvasId: string }, context: any) => {
  try {
    const currentUser = authenticateUser(context);

    // Validate input
    if (!args.canvasId) {
      throw new HttpError(400, 'Canvas ID is required');
    }

    // Check if user owns the canvas or has permission to view shares
    const canvas = await prisma.canvas.findUnique({
      where: { id: args.canvasId },
      include: {
        user: true,
        organization: {
          include: {
            memberships: {
              where: { userId: currentUser.id },
            },
          },
        },
      },
    });

    if (!canvas) {
      throw new HttpError(404, 'Canvas not found');
    }

    // Check permissions
    const isOwner = canvas.userId === currentUser.id;
    const isOrgAdmin = canvas.organization?.memberships.some(
      (m) => m.role === 'ADMIN' || m.role === 'OWNER'
    );

    if (!isOwner && !isOrgAdmin) {
      throw new HttpError(403, 'You do not have permission to view shares for this canvas');
    }

    // Get shares using domain service
    const shares = await canvasSharingService.getCanvasShares(args.canvasId);

    return shares.map(share => share.toPlainObject());
  } catch (error) {
    console.error('[GetCanvasShares] Error getting canvas shares:', error);
    throw error instanceof HttpError ? error : new HttpError(500, 'Failed to get canvas shares');
  }
};

/**
 * ✏️ Update Canvas Share Action
 * @description Update permissions for a canvas share
 */
export const updateCanvasShare = async (args: {
  shareId: string;
  permission?: CanvasSharingPermission;
  expiresAt?: Date;
  isActive?: boolean;
}, context: any) => {
  try {
    const currentUser = authenticateUser(context);

    // Validate input
    if (!args.shareId) {
      throw new HttpError(400, 'Share ID is required');
    }

    // Get the share and check permissions
    const share = await prisma.sharedCanvas.findUnique({
      where: { id: args.shareId },
      include: {
        canvas: {
          include: {
            user: true,
            organization: {
              include: {
                memberships: {
                  where: { userId: currentUser.id },
                },
              },
            },
          },
        },
      },
    });

    if (!share) {
      throw new HttpError(404, 'Share not found');
    }

    // Check permissions
    const isOwner = share.canvas.userId === currentUser.id;
    const isOrgAdmin = share.canvas.organization?.memberships.some(
      (m) => m.role === 'ADMIN' || m.role === 'OWNER'
    );

    if (!isOwner && !isOrgAdmin) {
      throw new HttpError(403, 'You do not have permission to update this share');
    }

    // Update share using domain service
    const updatedShare = await canvasSharingService.updateSharePermissions(args.shareId, {
      permission: args.permission,
      expiresAt: args.expiresAt,
      isActive: args.isActive,
    });

    return updatedShare.toPlainObject();
  } catch (error) {
    console.error('[UpdateCanvasShare] Error updating canvas share:', error);
    throw error instanceof HttpError ? error : new HttpError(500, 'Failed to update canvas share');
  }
};

/**
 * 🗑️ Revoke Canvas Share Action
 * @description Revoke a canvas share
 */
export const revokeCanvasShare = async (args: { shareId: string }, context: any) => {
  try {
    const currentUser = authenticateUser(context);

    // Validate input
    if (!args.shareId) {
      throw new HttpError(400, 'Share ID is required');
    }

    // Get the share and check permissions
    const share = await prisma.sharedCanvas.findUnique({
      where: { id: args.shareId },
      include: {
        canvas: {
          include: {
            user: true,
            organization: {
              include: {
                memberships: {
                  where: { userId: currentUser.id },
                },
              },
            },
          },
        },
      },
    });

    if (!share) {
      throw new HttpError(404, 'Share not found');
    }

    // Check permissions
    const isOwner = share.canvas.userId === currentUser.id;
    const isOrgAdmin = share.canvas.organization?.memberships.some(
      (m) => m.role === 'ADMIN' || m.role === 'OWNER'
    );

    if (!isOwner && !isOrgAdmin) {
      throw new HttpError(403, 'You do not have permission to revoke this share');
    }

    // Revoke share using domain service
    await canvasSharingService.revokeShare(args.shareId);

    return { success: true };
  } catch (error) {
    console.error('[RevokeCanvasShare] Error revoking canvas share:', error);
    throw error instanceof HttpError ? error : new HttpError(500, 'Failed to revoke canvas share');
  }
};
