/**
 * 💾 Canvas Actions - Modular Infrastructure Layer
 *
 * @description Provides interface to canvas actions through WASP client operations
 * @responsibility Clean interface to WASP canvas operations for modular architecture
 * @dependencies WASP client operations
 * @ai_context This bridges WASP client operations with new modular architecture
 */

// Note: These will be imported from 'wasp/client/operations' in the presentation layer
// This file serves as documentation and type definitions for the infrastructure layer

/**
 * 🏗️ Canvas Creation Action
 * @description Creates a new canvas
 */
export interface CreateCanvasInput {
  name: string;
  description?: string;
  organizationId?: string;
}

/**
 * 🔄 Canvas Update Action
 * @description Updates an existing canvas
 */
export interface UpdateCanvasInput {
  id: string;
  name?: string;
  description?: string;
  data?: any;
}

/**
 * 🗑️ Canvas Delete Action
 * @description Deletes a canvas
 */
export interface DeleteCanvasInput {
  id: string;
}

/**
 * 📋 Canvas Reference Actions
 * @description Canvas reference management
 */
export interface CreateCanvasReferenceInput {
  canvasId: string;
  referenceData: any;
}

// These actions should be imported from 'wasp/client/operations' in components:
// import { createCanvas, updateCanvas, deleteCanvas } from 'wasp/client/operations';
