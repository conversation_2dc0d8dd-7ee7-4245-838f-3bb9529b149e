/**
 * 🔍 Canvas Queries - Modular Infrastructure Layer
 *
 * @description Provides interface to canvas queries through WASP client operations
 * @responsibility Clean interface to WASP canvas queries for modular architecture
 * @dependencies WASP client operations
 * @ai_context This bridges WASP client operations with new modular architecture
 */

// Note: These will be imported from 'wasp/client/operations' in the presentation layer
// This file serves as documentation and type definitions for the infrastructure layer

/**
 * 🔍 Get Canvas Query
 * @description Retrieves a single canvas by ID
 */
export interface GetCanvasInput {
  id: string;
}

/**
 * 📋 Get Canvases Query
 * @description Retrieves multiple canvases
 */
export interface GetCanvasesInput {
  organizationId?: string;
  limit?: number;
  offset?: number;
}

/**
 * 🎨 Get Canvas Elements Query
 * @description Retrieves canvas elements
 */
export interface GetCanvasElementsInput {
  canvasId: string;
}

/**
 * 📎 Get Canvas References Query
 * @description Retrieves canvas references
 */
export interface GetCanvasReferencesInput {
  canvasId: string;
}

// These queries should be imported from 'wasp/client/operations' in components:
// import { getCanvas, getCanvases, getCanvasElements } from 'wasp/client/operations';
