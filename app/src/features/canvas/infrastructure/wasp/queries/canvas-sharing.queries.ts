/**
 * 🎨 Canvas Sharing Queries
 * 
 * @description WASP queries for canvas sharing functionality
 * @responsibility Handles canvas sharing read operations via WASP framework
 * @dependencies Canvas sharing domain layer, WASP operations
 * @ai_context Follows existing WASP query patterns for canvas operations
 */

import { HttpError } from 'wasp/server';
import { prisma } from 'wasp/server';
import { authenticateUser } from '../../../../../server/helpers';
// Remove typed imports - use standard WASP pattern

// Import domain layer
import {
  CanvasSharingService,
  PrismaCanvasSharingRepository,
  type AccessSharedCanvasResponse,
  type ValidateTokenResponse,
  type CanvasSharingStats
} from '../../../domain';

// Initialize service with repository
const canvasSharingRepository = new PrismaCanvasSharingRepository(prisma);
const canvasSharingService = new CanvasSharingService(canvasSharingRepository);

/**
 * 📋 Get Shared Canvases Query
 * @description Get all canvases shared with the current user
 */
export const getSharedCanvases = async (args: {}, context: any) => {
  try {
    const currentUser = authenticateUser(context);

    if (!currentUser.email) {
      throw new HttpError(400, 'User email is required to get shared canvases');
    }

    // Get shared canvases using domain service
    const sharedCanvases = await canvasSharingService.getSharedCanvases(
      currentUser.email,
      (typeof currentUser.id === 'number' ? currentUser.id : parseInt(currentUser.id, 10))
    );

    return sharedCanvases.map(share => share.toPlainObject());
  } catch (error) {
    console.error('[GetSharedCanvases] Error getting shared canvases:', error);
    throw error instanceof HttpError ? error : new HttpError(500, 'Failed to get shared canvases');
  }
};

/**
 * 🔗 Get Shared Canvas by Token Query
 * @description Access a shared canvas using a share token
 */
export const getSharedCanvasByToken = async (args: { token: string }, context: any) => {
  try {
    // Note: This query doesn't require authentication as it's for accessing shared links
    // But we can get the user if they're logged in for better tracking
    let accessorUserId: number | undefined;
    try {
      const currentUser = authenticateUser(context);
      accessorUserId = (typeof currentUser.id === 'number' ? currentUser.id : parseInt(currentUser.id, 10));
    } catch {
      // User not authenticated, that's okay for public shares
    }

    // Validate input
    if (!args.token) {
      throw new HttpError(400, 'Share token is required');
    }

    // Access shared canvas using domain service
    const accessResult = await canvasSharingService.accessSharedCanvas(args.token, accessorUserId);

    return {
      sharedCanvas: accessResult.sharedCanvas.toPlainObject(),
      canvas: accessResult.canvas,
      hasAccess: accessResult.hasAccess,
      accessLevel: accessResult.accessLevel,
    };
  } catch (error) {
    console.error('[GetSharedCanvasByToken] Error accessing shared canvas:', error);

    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        throw new HttpError(404, 'Shared canvas not found');
      }
      if (error.message.includes('expired') || error.message.includes('no longer active')) {
        throw new HttpError(410, 'This share link has expired or is no longer active');
      }
    }

    throw error instanceof HttpError ? error : new HttpError(500, 'Failed to access shared canvas');
  }
};

/**
 * ✅ Validate Canvas Share Token Query
 * @description Validate a share token without accessing the canvas
 */
export const validateCanvasShareToken = async (args: { token: string }, context: any) => {
  try {
    // Validate input
    if (!args.token) {
      throw new HttpError(400, 'Share token is required');
    }

    // Validate token using domain service
    const validationResult = await canvasSharingService.validateShareToken(args.token);

    return {
      isValid: validationResult.isValid,
      shareEntity: validationResult.shareEntity?.toPlainObject(),
      error: validationResult.error,
    };
  } catch (error) {
    console.error('[ValidateCanvasShareToken] Error validating token:', error);
    throw error instanceof HttpError ? error : new HttpError(500, 'Failed to validate share token');
  }
};

/**
 * 📊 Get Canvas Sharing Stats Query
 * @description Get sharing statistics for a canvas
 */
export const getCanvasSharingStats = async (args: { canvasId: string }, context: any) => {
  try {
    const currentUser = authenticateUser(context);

    // Validate input
    if (!args.canvasId) {
      throw new HttpError(400, 'Canvas ID is required');
    }

    // Check if user owns the canvas or has permission to view stats
    const canvas = await prisma.canvas.findUnique({
      where: { id: args.canvasId },
      include: {
        user: true,
        organization: {
          include: {
            memberships: {
              where: { userId: String(currentUser.id) },
            },
          },
        },
      },
    });

    if (!canvas) {
      throw new HttpError(404, 'Canvas not found');
    }

    // Check permissions
    const isOwner = String(canvas.userId) === String(currentUser.id);
    const isOrgAdmin = canvas.organization?.memberships.some(
      (m) => m.role === 'ADMIN' || m.role === 'OWNER'
    );

    if (!isOwner && !isOrgAdmin) {
      throw new HttpError(403, 'You do not have permission to view sharing stats for this canvas');
    }

    // Get stats using domain service
    const stats = await canvasSharingService.getCanvasSharingStats(args.canvasId);

    return stats;
  } catch (error) {
    console.error('[GetCanvasSharingStats] Error getting canvas sharing stats:', error);
    throw error instanceof HttpError ? error : new HttpError(500, 'Failed to get canvas sharing stats');
  }
};

/**
 * 🔍 Check Canvas Access Query
 * @description Check if current user can access a specific canvas
 */
export const checkCanvasAccess: any = async (args: { canvasId: string }, context: any) => {
  try {
    const currentUser = authenticateUser(context);

    // Validate input
    if (!args.canvasId) {
      throw new HttpError(400, 'Canvas ID is required');
    }

    // Check access using domain service
    const accessResult = await canvasSharingService.canUserAccessCanvas(
      args.canvasId,
      (typeof currentUser.id === 'number' ? currentUser.id : parseInt(currentUser.id, 10))
    );

    return accessResult;
  } catch (error) {
    console.error('[CheckCanvasAccess] Error checking canvas access:', error);
    throw error instanceof HttpError ? error : new HttpError(500, 'Failed to check canvas access');
  }
};

/**
 * 🎯 Accept Canvas Share Action (implemented as query for now)
 * @description Accept a canvas share invitation
 */
export const acceptCanvasShare: any = async (args: { token: string }, context: any) => {
  try {
    const currentUser = authenticateUser(context);

    // Validate input
    if (!args.token) {
      throw new HttpError(400, 'Share token is required');
    }

    // Get the shared canvas
    const sharedCanvas = await canvasSharingRepository.findByToken(args.token);

    if (!sharedCanvas) {
      throw new HttpError(404, 'Share not found');
    }

    // Check if share is valid
    if (!sharedCanvas.isValid()) {
      throw new HttpError(410, 'This share link has expired or is no longer active');
    }

    // Update the share to link it to the current user
    if (!sharedCanvas.sharedWithUserId && currentUser.email === sharedCanvas.sharedWithEmail) {
      await prisma.sharedCanvas.update({
        where: { id: sharedCanvas.id },
        data: { sharedWithUserId: currentUser.id },
      });
    }

    // Record access
    await canvasSharingRepository.recordAccess(args.token);

    return {
      success: true,
      canvasId: sharedCanvas.canvasId,
      permission: sharedCanvas.permission,
    };
  } catch (error) {
    console.error('[AcceptCanvasShare] Error accepting canvas share:', error);
    throw error instanceof HttpError ? error : new HttpError(500, 'Failed to accept canvas share');
  }
};
