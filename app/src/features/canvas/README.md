# 🎨 Canvas Feature - Modular Architecture

## 🎯 Overview

The Canvas feature has been successfully migrated to a modular architecture following the same pattern as the brand-kits feature. This migration extracts business logic to the backend, implements SOLID principles, and organizes code in a feature folder structure.

## 📁 Directory Structure

```
features/canvas/
├── 🧠 domain/                          # Business logic layer
│   ├── entities/                       # Domain entities
│   │   ├── canvas.entity.ts            # Core canvas domain model
│   │   ├── image.entity.ts             # Image element entity
│   │   ├── text.entity.ts              # Text element entity
│   │   ├── html-element.entity.ts      # HTML element entity
│   │   ├── tool.entity.ts              # Canvas tool entity
│   │   └── selection.entity.ts         # Selection state entity
│   ├── services/                       # Business services
│   │   ├── canvas.service.ts           # Core canvas operations
│   │   ├── image-processing.service.ts # Image manipulation logic
│   │   ├── collaboration.service.ts    # Real-time collaboration
│   │   ├── history.service.ts          # Undo/redo management
│   │   ├── mask-drawing.service.ts     # Mask drawing operations
│   │   └── canvas-persistence.service.ts # Canvas state persistence
│   └── repositories/                   # Repository interfaces
│       ├── canvas.repository.ts        # Canvas data operations
│       ├── image.repository.ts         # Image storage operations
│       └── collaboration.repository.ts # Collaboration data
├── 🔧 infrastructure/                  # External integrations
│   ├── wasp/                          # WASP framework integration
│   │   ├── actions/                   # Server actions
│   │   │   ├── save-canvas.ts         # Canvas persistence
│   │   │   ├── upload-image.ts        # Image upload
│   │   │   ├── generate-image.ts      # AI image generation
│   │   │   └── process-mask.ts        # Mask processing
│   │   ├── queries/                   # Server queries
│   │   │   ├── get-canvas.ts          # Canvas retrieval
│   │   │   ├── get-images.ts          # Image queries
│   │   │   └── get-collaboration.ts   # Collaboration data
│   │   └── websockets/                # WebSocket handlers
│   │       ├── canvas-sync.ts         # Real-time sync
│   │       └── collaboration.ts       # Collaborative editing
│   ├── external/                      # External services
│   │   ├── r2-storage.ts             # R2 image storage
│   │   ├── ai-services.ts            # AI image generation
│   │   └── image-proxy.ts            # Image proxy service
│   └── database/                      # Database implementations
│       ├── canvas.repository.impl.ts  # Canvas persistence
│       ├── image.repository.impl.ts   # Image storage
│       └── collaboration.repository.impl.ts # Collaboration data
├── 🎨 presentation/                    # UI layer
│   ├── components/                     # React components
│   │   ├── canvas/                    # Core canvas components
│   │   │   ├── Canvas.tsx             # Main canvas component
│   │   │   ├── CanvasToolbar.tsx      # Canvas toolbar
│   │   │   └── ZoomControls.tsx       # Zoom controls
│   │   ├── elements/                  # Canvas elements
│   │   │   ├── ImageElements.tsx      # Image rendering
│   │   │   ├── TextElements.tsx       # Text rendering
│   │   │   ├── HtmlElements.tsx       # HTML rendering
│   │   │   └── ElementTransformer.tsx # Element transformation
│   │   ├── tools/                     # Canvas tools
│   │   │   ├── SelectTool.tsx         # Selection tool
│   │   │   ├── HandTool.tsx           # Pan tool
│   │   │   └── MaskTool.tsx           # Mask drawing tool
│   │   ├── collaboration/             # Collaboration UI
│   │   │   ├── CollaboratorCursors.tsx # User cursors
│   │   │   └── UserSelections.tsx     # User selections
│   │   ├── modals/                    # Modal components
│   │   │   ├── ImageHistoryModal.tsx  # Image history
│   │   │   └── CanvasSettingsModal.tsx # Canvas settings
│   │   └── chat/                      # Chat components
│   │       ├── KonvaSideChat.tsx      # Image chat
│   │       └── HtmlSideChat.tsx       # HTML chat
│   ├── pages/                         # Page components
│   │   └── CanvasPage.tsx             # Main canvas page
│   ├── hooks/                         # React hooks
│   │   ├── useCanvas.hook.ts          # Canvas operations
│   │   ├── useCanvasImages.hook.ts    # Image management
│   │   ├── useCanvasHistory.hook.ts   # History management
│   │   ├── useCanvasTools.hook.ts     # Tool management
│   │   └── useCollaboration.hook.ts   # Collaboration hooks
│   └── stores/                        # Client state
│       ├── canvas.store.ts            # Main canvas store
│       ├── tools.store.ts             # Tools state
│       └── collaboration.store.ts     # Collaboration state
├── 📚 docs/                            # Feature documentation
│   ├── ARCHITECTURE.md                 # Technical architecture
│   ├── API.md                          # API documentation
│   ├── TOOLS.md                        # Canvas tools guide
│   ├── COLLABORATION.md                # Collaboration features
│   └── MIGRATION.md                    # Migration guide
└── index.ts                            # Feature exports
```

## 🎯 Core Capabilities

### 1. **Canvas Management**
- Create, load, save canvas states
- Multi-layer element management
- Infinite canvas with zoom/pan
- Grid and snap functionality

### 2. **Element Types**
- Images with transformation and effects
- Text with rich formatting
- HTML elements with live preview
- Interactive wireframes
- Concept cards and placeholders

### 3. **Drawing Tools**
- Selection and transformation
- Pan and zoom navigation
- Mask drawing for image editing
- Collaborative cursors

### 4. **Image Processing**
- AI-powered image generation
- Image editing with masks
- Image history and versioning
- R2 storage integration

### 5. **Real-time Collaboration**
- Multi-user editing
- Live cursor tracking
- Synchronized selections
- Conflict resolution

### 6. **History Management**
- Undo/redo operations
- State snapshots
- Persistent history
- Optimistic updates

## 🔄 Business Workflows

### Canvas Creation
1. User creates new canvas
2. System initializes with default settings
3. Canvas state is persisted
4. Collaboration session is established

### Image Generation Workflow
1. User requests image generation
2. System processes with AI services
3. Image is uploaded to R2 storage
4. Canvas is updated with new image
5. History snapshot is created

### Collaborative Editing
1. User joins canvas session
2. WebSocket connection established
3. Real-time sync of all changes
4. Conflict resolution for simultaneous edits
5. Persistent state synchronization

## 🤖 AI Integration

### Image Generation
- OpenAI DALL-E integration
- Fal.ai for advanced generation
- Custom LoRA model support
- Batch processing capabilities

### Image Editing
- Mask-based editing
- AI-powered enhancements
- Style transfer operations
- Automatic background removal

## 🔧 Technical Architecture

### State Management
- Zustand for client state
- Domain services for business logic
- Repository pattern for data access
- Event-driven architecture

### Performance Optimization
- Konva.js for canvas rendering
- Virtualization for large canvases
- Debounced updates
- Optimistic UI updates

### Collaboration
- Socket.IO for real-time sync
- Operational transformation
- Conflict resolution algorithms
- Persistent connection management

## 🚀 Getting Started

```typescript
// Import canvas functionality
import { 
  CanvasService, 
  Canvas, 
  ImageElement,
  useCanvas,
  CanvasPage 
} from '@features/canvas';

// Create a new canvas
const canvasService = new CanvasService();
const canvas = await canvasService.createCanvas({
  name: "My Canvas",
  userId: 123,
  organizationId: "org_456"
});

// Use in React components
function MyCanvasPage() {
  const { canvas, addImage, selectTool } = useCanvas();
  
  return <CanvasPage />;
}
```

## 📋 Migration Notes

This feature was migrated from the legacy `/client/canvas/` structure to follow the modular architecture pattern. All business logic has been extracted to domain services, external dependencies moved to infrastructure layer, and UI components organized in the presentation layer.

Key improvements:
- ✅ Separation of concerns
- ✅ Testable business logic
- ✅ Clean dependency management
- ✅ Scalable architecture
- ✅ AI-friendly code organization
