/**
 * 🖼️ Image Types - MODULAR VERSION
 * @description Types for image elements in the modular canvas system
 * @responsibility Define data structures for image handling
 * @ai_context Migrated from client/canvas/types/index.ts to modular structure
 */

export interface ImageObject {
  id: string;
  src: string;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;
  taskId?: string;
  isPending?: boolean;
  progress?: number;
  status?: string;
  isReference?: boolean;
  imageIndex?: number;
  totalImages?: number;
  cropX?: number;
  cropY?: number;
  cropWidth?: number;
  cropHeight?: number;
  isDragging?: boolean;
}

export interface URLImageProps {
  image: ImageObject;
  isSelected: boolean;
  onSelect: () => void;
  onChange: (newAttrs: ImageObject) => void;
}

// HTML Element types
export interface HTMLElementObject {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;
  html?: string;
  htmlUrl?: string;
  htmlStoredInR2?: boolean;
  cssContent?: string;
  isNewsletter?: boolean;
  isDragging?: boolean;
}

export interface HTMLElementProps {
  element: HTMLElementObject;
  isSelected: boolean;
  onSelect: (e?: React.MouseEvent) => void;
  onChange: (newAttrs: HTMLElementObject) => void;
  onDragging?: (isDragging: boolean) => void;
}

// Text Element types
export interface TextElementObject {
  id: string;
  x: number;
  y: number;
  text: string;
  fontSize: number;
  fontFamily: string;
  fill: string;
  width: number;
  rotation?: number;
  isDragging?: boolean;
}

export interface TextElementProps {
  textNode: TextElementObject;
  isSelected: boolean;
  onSelect: (e?: React.MouseEvent) => void;
  onChange: (newAttrs: TextElementObject) => void;
  onDragging?: (newAttrs: TextElementObject) => void;
}

// Interface for pending image props
export interface PendingImageProps {
  image: ImageObject;
  isSelected: boolean;
  onSelect: () => void;
  onChange: (newAttrs: ImageObject) => void;
}

// Canvas tool types
export enum ECanvasTool {
  SELECT = 'select',
  HAND = 'hand',
  MASK = 'mask',
}

export type CanvasToolType = 'select' | 'hand' | 'mask';

// Interface for mask drawing state
export interface MaskDrawingState {
  isDrawing: boolean;
  tool: 'brush' | 'eraser';
  brushSize: number;
  opacity: number;
  points: ({ x: number; y: number } | null)[]; // May contain null separators
  maskImageUrl: string | null;
  targetImageId: string | null;
  targetImageUrl: string | null;
  showToolbar: boolean;
  // Store the image position for reference
  imagePosition?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

// Debug mode settings interface
export interface DebugModeSettings {
  enabled: boolean;
  showBorders: boolean;
  showPositions: boolean;
  showIds: boolean;
  showGroupBounds: boolean;
  showComponentDetails: boolean;
  borderColors: {
    image: string;
    text: string;
    html: string;
    selected: string;
    mask: string;
    maskBrush: string;
    maskEraser: string;
    reference: string;
    group: string;
    component: string;
  };
}

// Custom event detail interface (for toolbar events)
export interface CustomEventDetail {
  imageUrl?: string;
  taskId?: string;
  isPending?: boolean;
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  status?: string;
  progress?: number;
}
