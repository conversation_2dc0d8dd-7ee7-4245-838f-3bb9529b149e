/**
 * 🎨 Canvas Sharing Types
 *
 * @description Type definitions for canvas sharing functionality
 * @responsibility Centralized type exports for canvas sharing
 * @dependencies Canvas sharing entities and services
 * @ai_context Provides type safety for canvas sharing operations
 */

// Import types from entity
import type {
  CanvasSharingSettings,
  SharedCanvas,
} from '../entities/shared-canvas.entity';

import {
  CanvasSharingPermission,
  SharedCanvasEntity,
  CreateSharedCanvasSchema,
  UpdateSharedCanvasSchema,
  CanvasSharingSettingsSchema,
} from '../entities/shared-canvas.entity';

// Re-export entity types
export type {
  CanvasSharingSettings,
  SharedCanvas,
};

export {
  CanvasSharingPermission,
  SharedCanvasEntity,
  CreateSharedCanvasSchema,
  UpdateSharedCanvasSchema,
  CanvasSharingSettingsSchema,
};

// Import repository types
import type {
  CanvasSharingRepository,
} from '../repositories/canvas-sharing.repository';

import {
  PrismaCanvasSharingRepository,
} from '../repositories/canvas-sharing.repository';

// Import service types
import {
  CanvasSharingService,
} from '../services/canvas-sharing.service';

// Re-export repository types
export type { CanvasSharingRepository };
export { PrismaCanvasSharingRepository };

// Re-export service types
export { CanvasSharingService };

// Additional types for API responses (with index signatures for WASP compatibility)
export interface ShareCanvasRequest {
  canvasId: string;
  sharedWithEmail: string;
  permission?: CanvasSharingPermission;
  expiresAt?: Date;
  settings?: Partial<CanvasSharingSettings>;
  [key: string]: any; // Index signature for SuperJSON compatibility
}

export interface ShareCanvasResponse {
  shareId: string;
  token: string;
  shareUrl: string;
  isNewShare: boolean;
  expiresAt: Date | null;
  [key: string]: any; // Index signature for SuperJSON compatibility
}

export interface CreatePublicLinkRequest {
  canvasId: string;
  permission?: CanvasSharingPermission;
  expiresAt?: Date;
  settings?: Partial<CanvasSharingSettings>;
  [key: string]: any; // Index signature for SuperJSON compatibility
}

export interface CreatePublicLinkResponse {
  shareId: string;
  token: string;
  shareUrl: string;
  expiresAt: Date | null;
  [key: string]: any; // Index signature for SuperJSON compatibility
}

export interface AccessSharedCanvasResponse {
  sharedCanvas: SharedCanvas;
  canvas: any;
  hasAccess: boolean;
  accessLevel: CanvasSharingPermission;
  [key: string]: any; // Index signature for SuperJSON compatibility
}

export interface CanvasSharingStats {
  totalShares: number;
  totalAccess: number;
  activeShares: number;
  lastAccessed: Date | null;
  [key: string]: any; // Index signature for SuperJSON compatibility
}

export interface ValidateTokenResponse {
  isValid: boolean;
  shareEntity?: SharedCanvas;
  error?: string;
  [key: string]: any; // Index signature for SuperJSON compatibility
}

export interface UserCanvasAccess {
  canAccess: boolean;
  accessLevel: CanvasSharingPermission | 'owner';
  shareEntity?: SharedCanvas;
}

// Email notification types
export interface CanvasShareEmailData {
  recipientEmail: string;
  shareUrl: string;
  canvas: {
    id: string;
    name: string;
    thumbnail?: string;
  };
  sharedBy: {
    id: number;
    username: string;
    email: string;
  };
  permission: CanvasSharingPermission;
  expiresAt?: Date;
}

// UI component props types
export interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  canvasId: string;
  canvasName: string;
  onShareSuccess?: (shareData: ShareCanvasResponse) => void;
}

export interface ShareButtonProps {
  canvasId: string;
  canvasName: string;
  className?: string;
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
}

export interface SharedCanvasListProps {
  shares: SharedCanvas[];
  onUpdatePermission?: (shareId: string, permission: CanvasSharingPermission) => void;
  onRevokeShare?: (shareId: string) => void;
  onResendInvite?: (shareId: string) => void;
}

export interface CanvasAccessIndicatorProps {
  accessLevel: CanvasSharingPermission | 'owner';
  isShared: boolean;
  shareCount?: number;
}

// Error types
export class CanvasSharingError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 400
  ) {
    super(message);
    this.name = 'CanvasSharingError';
  }
}

export class CanvasNotFoundError extends CanvasSharingError {
  constructor(canvasId: string) {
    super(`Canvas with ID ${canvasId} not found`, 'CANVAS_NOT_FOUND', 404);
  }
}

export class ShareNotFoundError extends CanvasSharingError {
  constructor(token: string) {
    super(`Share with token ${token} not found`, 'SHARE_NOT_FOUND', 404);
  }
}

export class ShareExpiredError extends CanvasSharingError {
  constructor() {
    super('This share link has expired', 'SHARE_EXPIRED', 410);
  }
}

export class ShareAlreadyExistsError extends CanvasSharingError {
  constructor(email: string) {
    super(`Canvas is already shared with ${email}`, 'SHARE_ALREADY_EXISTS', 409);
  }
}

export class InsufficientPermissionsError extends CanvasSharingError {
  constructor(action: string) {
    super(`Insufficient permissions to ${action}`, 'INSUFFICIENT_PERMISSIONS', 403);
  }
}

// Utility types
export type CanvasShareStatus = 'active' | 'expired' | 'revoked';

export interface CanvasShareSummary {
  id: string;
  canvasId: string;
  canvasName: string;
  sharedWithEmail: string;
  permission: CanvasSharingPermission;
  status: CanvasShareStatus;
  sharedAt: Date;
  expiresAt: Date | null;
  accessCount: number;
  lastAccessedAt: Date | null;
}

// Permission level helpers
export const PERMISSION_LEVELS = {
  [CanvasSharingPermission.VIEW]: {
    label: 'Can view',
    description: 'Can view the canvas but cannot make changes',
    icon: 'eye',
  },
  [CanvasSharingPermission.COMMENT]: {
    label: 'Can comment',
    description: 'Can view and add comments to the canvas',
    icon: 'message-circle',
  },
  [CanvasSharingPermission.EDIT]: {
    label: 'Can edit',
    description: 'Can view, comment, and edit the canvas',
    icon: 'edit',
  },
} as const;

// Default settings
export const DEFAULT_SHARE_SETTINGS: CanvasSharingSettings = {
  allowComments: true,
  allowEdit: false,
  requirePassword: false,
  trackViews: true,
  notifyOnAccess: false,
};

// Note: CanvasSharingPermission is already exported above
