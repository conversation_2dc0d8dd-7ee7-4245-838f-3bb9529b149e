/**
 * 🧠 Canvas Domain Types - MAIN EXPORTS
 * @description Central export point for all domain types
 * @ai_context Domain types for the modular canvas system
 */

// Card types (MIGRATED from client/canvas/types)
export * from './concept-card.types';
export * from './newsletter.types';
export * from './html-display.types';

// Image types (MIGRATED from client/canvas/types)
export * from './image.types';

// 🎭 Scene Types (MIGRATED from client/canvas/types)
export type Edge = 'left' | 'right' | 'top' | 'bottom' | 'hCenter' | 'vCenter';

// TODO: Add more domain types as we build them
// export * from './element.types';
// export * from './tool.types';
// export * from './canvas.types';
// export * from './selection.types';
