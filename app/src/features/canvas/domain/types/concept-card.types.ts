/**
 * 🎨 Concept Card Types - MODULAR VERSION
 * @description Types for concept cards in the modular canvas system
 * @responsibility Define data structures for concept cards
 * @ai_context Migrated from client/canvas/types/conceptCard.ts to modular structure
 */

export interface ConceptCardData {
  id: string;
  concept: string;
  prompt?: string;
  questions: string[];
  position: { x: number; y: number };
  width?: number; // Card width
  height?: number; // Card height
  answers?: string[];
  status?: 'questioning' | 'generating' | 'completed';
  generatedContent?: {
    type: 'image' | 'html' | 'text';
    content: string;
    elementId?: string; // Reference to the actual canvas element
  };
  taskId?: string; // Reference to the agent task
  loadingCardId?: string | undefined; // Reference to the loading card that this card replaces
  productId?: string; // Reference to the product for brand information
  previewImageUrl?: string; // URL of the preview image for the concept card
  suggestionChips?: string[]; // Suggestion chips for refining the prompt
  numImages?: number; // Number of images to generate (1-5)
  inputText?: string; // Current input text in the "Refine this idea" field
}

// Event types for concept cards
export interface CreateConceptCardEvent extends CustomEvent {
  detail: ConceptCardData;
}

// Declare global event types
declare global {
  interface WindowEventMap {
    createConceptCard: CreateConceptCardEvent;
  }
}
