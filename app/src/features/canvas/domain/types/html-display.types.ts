/**
 * 🌐 HTML Display Types - MODULAR VERSION
 * @description Types for HTML display cards in the modular canvas system
 * @responsibility Define data structures for HTML content display
 * @ai_context Migrated from client/canvas/types/htmlDisplayCardTypes.ts to modular structure
 */

export interface HtmlDisplayCardDataType {
  id: string; // Unique ID for the Konva element, typically derived from taskId
  taskId: string; // Original AgentTask ID, for reference
  title: string; // e.g., the newsletter title
  iframeUrl: string; // URL for the iframe src, e.g., the gmailMockUrl from R2
  inlinedMobileHtmlUrl?: string; // URL for the inlined mobile HTML (for export)
  inlinedDesktopHtmlUrl?: string; // URL for the inlined desktop HTML (for export)
  position: { x: number; y: number };
  width?: number; // Optional, with a default in the component
  height?: number; // Optional, with a default in the component
}
