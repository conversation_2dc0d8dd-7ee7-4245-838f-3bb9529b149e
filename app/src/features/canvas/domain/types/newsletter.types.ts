/**
 * 📰 Newsletter Types - MODULAR VERSION
 * @description Types for newsletter approval cards in the modular canvas system
 * @responsibility Define data structures for newsletter generation and approval
 * @ai_context Migrated from client/canvas/types/newsletterApprovalTypes.ts to modular structure
 */

// Define the structure of the newsletter outline data
export interface NewsletterOutlineSection {
  heading: string;
  copyIdea: string; // New: Detailed textual content idea
  visualDesignIdea: string; // New: Suggestion for visual design/layout
  description: string; // Kept: Brief summary of the section
}

export interface NewsletterOutline {
  title: string;
  sections: NewsletterOutlineSection[];
}

export interface NewsletterApprovalCardDataType {
  id: string; // Unique ID for the Konva element, typically same as taskId
  taskId: string; // The AgentTask ID from the backend
  threadId: string; // The agent thread ID for resuming with feedback
  outline?: NewsletterOutline; // Parsed structured outline
  outlineText?: string; // Raw outline text (if outline object isn't available yet or for simpler display)
  agentContext?: any; // Optional context from the agent
  initialOptions?: any; // Optional initial options or suggestions
  position: { x: number; y: number };
  status?: 'pending_approval' | 'processing' | 'completed' | 'failed' | 'outline_ready_for_review' | 'generating_html'; // Added generating_html status
  gmailMockUrl?: string; // URL to the generated Gmail mock HTML
  error?: string; // Optional error message for failed status
  // Optionally, add other final URLs if needed by the card
  // rawMobileHtmlUrl?: string;
  // rawDesktopHtmlUrl?: string;
  // inlinedMobileHtmlUrl?: string;
  // inlinedDesktopHtmlUrl?: string;
  // Callbacks are now part of the data type if defined per instance
  onApprove?: (params: { taskId: string; outline: NewsletterOutline }) => void;
  onSubmitFeedback?: (params: { taskId: string; feedback: string }) => void;
}
