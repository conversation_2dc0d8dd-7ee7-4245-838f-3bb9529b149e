/**
 * 🎨 Canvas Toolbar State - MODULAR VERSION
 * @description Modular state management for canvas toolbar operations
 * @responsibility Replace canvas store dependencies with modular state
 * @ai_context Clean state management for toolbar without external dependencies
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import {
  CanvasOperationsService,
  type CanvasState,
  type CanvasImage,
  type CanvasHtmlElement,
  type CanvasTextElement,
} from '../services/canvas-operations.service';
import { DomainTextElement } from './element.state';

/**
 * 🎨 Canvas Toolbar State Interface
 */
interface CanvasToolbarState extends CanvasState {
  // State getters
  canUndo: boolean;
  canRedo: boolean;

  // Operations
  addImage: () => string;
  addText: () => DomainTextElement;
  addHtmlElement: () => string;
  deleteSelected: () => void;
  handleClear: () => void;
  handleUndo: () => void;
  handleRedo: () => void;
  setActiveTool: (tool: 'select' | 'hand' | 'mask') => void;
  setSelectedIds: (ids: string[]) => void;

  // ✂️ Crop mode
  isCropMode: boolean;
  setIsCropMode: (isCropMode: boolean) => void;
  setImages: (images: CanvasImage[]) => void;
  setHtmlElements: (htmlElements: CanvasHtmlElement[]) => void;
  setTextElements: (textElements: CanvasTextElement[]) => void;
  toggleDebugMode: () => void;

  // Canvas properties
  setCanvasSize: (size: { width: number; height: number }) => void;
  setPosition: (position: { x: number; y: number }) => void;
  setScale: (scale: number) => void;
}

/**
 * 🎨 Canvas Toolbar Store
 * @description Modular Zustand store for canvas toolbar operations
 */
export const useCanvasToolbarStore = create<CanvasToolbarState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    images: [],
    htmlElements: [],
    textElements: [],
    selectedIds: [],
    activeTool: 'select',
    isCropMode: false,
    canvasSize: { width: 1920, height: 1080 },
    position: { x: 0, y: 0 },
    scale: 1,
    debug: false,
    history: [],
    historyStep: 0,
    canUndo: false,
    canRedo: false,

    // Operations
    addImage: () => {
      const newId = CanvasOperationsService.addImage();
      return newId;
    },

    addText: () => {
      const newText = CanvasOperationsService.addText();
      return newText;
    },

    addHtmlElement: () => {
      const newId = CanvasOperationsService.addHtmlElement();
      return newId;
    },

    deleteSelected: () => {
      const state = get();
      const updates = CanvasOperationsService.deleteSelected(state, state.selectedIds);
      set(updates);
    },

    handleClear: () => {
      const updates = CanvasOperationsService.clearCanvas();
      set(updates);
    },

    handleUndo: () => {
      const state = get();
      const updates = CanvasOperationsService.handleUndo(state);
      set(updates);
    },

    handleRedo: () => {
      const state = get();
      const updates = CanvasOperationsService.handleRedo(state);
      set(updates);
    },

    setActiveTool: (tool) => {
      const updates = CanvasOperationsService.setActiveTool(tool);
      set(updates);
    },

    setSelectedIds: (selectedIds) => {
      const updates = CanvasOperationsService.setSelectedIds(selectedIds);
      set(updates);
    },

    setIsCropMode: (isCropMode) => {
      set({ isCropMode });
    },

    setImages: (images) => {
      const updates = CanvasOperationsService.setImages(images);
      set(updates);
    },

    setHtmlElements: (htmlElements) => {
      const updates = CanvasOperationsService.setHtmlElements(htmlElements);
      set(updates);
    },

    setTextElements: (textElements) => {
      const updates = CanvasOperationsService.setTextElements(textElements);
      set(updates);
    },

    toggleDebugMode: () => {
      const state = get();
      const updates = CanvasOperationsService.toggleDebugMode(state.debug);
      set(updates);
    },

    // Canvas properties
    setCanvasSize: (canvasSize) => {
      set({ canvasSize });
    },

    setPosition: (position) => {
      set({ position });
    },

    setScale: (scale) => {
      set({ scale });
    },
  }))
);

/**
 * 🎯 Shallow Selector Hook
 * @description Provides shallow selection like the old canvas store
 */
export const useCanvasToolbarShallowSelector = <T>(selector: (state: CanvasToolbarState) => T): T => {
  return useCanvasToolbarStore(selector);
};

/**
 * 📦 Export individual operations for compatibility
 */
export const {
  addImage,
  addText,
  addHtmlElement,
  deleteSelected,
  handleClear,
  handleUndo,
  handleRedo,
  setActiveTool,
  setSelectedIds,
  setImages,
  setHtmlElements,
  setTextElements,
  toggleDebugMode,
} = useCanvasToolbarStore.getState();
