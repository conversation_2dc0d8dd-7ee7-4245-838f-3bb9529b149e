/**
 * 🧠 Domain State Index
 *
 * @description Exports all domain state for clean imports
 * @responsibility Central export point for all canvas domain state
 * @ai_context Clean imports for domain state in our pure domain architecture
 */

// Element Domain State
export {
  useElementDomainStore,
  type ElementDomainState,
  type DomainImageElement,
  type DomainTextElement,
  type DomainHtmlElement,
} from './element.state';

// Tool Domain State
export {
  useToolDomainStore,
  type ToolDomainState,
  DomainCanvasTool,
  type DomainCanvasPosition,
  type DomainCanvasSize,
  type DomainMaskDrawingState,
  type DomainSelectionState,
} from './tool.state';

// Canvas Toolbar State (for toolbar operations)
export {
  useCanvasToolbarStore,
  useCanvasToolbarShallowSelector,
  addImage,
  addText,
  addHtmlElement,
  deleteSelected,
  handleClear,
  handleUndo,
  handleRedo,
  setActiveTool,
  setSelectedIds,
  setImages,
  setHtmlElements,
  setTextElements,
  toggleDebugMode,
} from './canvas-toolbar.state';
