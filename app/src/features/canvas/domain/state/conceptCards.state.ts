import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ConceptCardData } from '../types';

export interface LoadingCard {
  id: string;
  position: { x: number; y: number };
  requestText: string;
}

interface ConceptCardsState {
  loadingCards: LoadingCard[];
  cards: ConceptCardData[];
  addLoadingCard: (card: LoadingCard) => void;
  removeLoadingCard: (id: string) => void;
  updateLoadingCardPosition: (id: string, position: { x: number; y: number }) => void;
  addCard: (card: ConceptCardData) => void;
  updateCard: (id: string, updates: Partial<ConceptCardData>) => void;
  removeCard: (id: string) => void;
}

export const useConceptCardsStore = create<ConceptCardsState>()(
  persist(
    (set) => ({
      loadingCards: [],
      cards: [],
      addLoadingCard: (card) =>
        set((state) => ({ loadingCards: [...state.loadingCards, card] })),
      removeLoadingCard: (id) =>
        set((state) => ({ loadingCards: state.loadingCards.filter((c) => c.id !== id) })),
      updateLoadingCardPosition: (id, position) =>
        set((state) => ({
          loadingCards: state.loadingCards.map((c) => (c.id === id ? { ...c, position } : c)),
        })),
      addCard: (card) =>
        set((state) => ({
          cards: state.cards.some((c) => c.id === card.id) ? state.cards : [...state.cards, card],
        })),
      updateCard: (id, updates) =>
        set((state) => ({
          cards: state.cards.map((c) => (c.id === id ? { ...c, ...updates } : c)),
        })),
      removeCard: (id) =>
        set((state) => ({ cards: state.cards.filter((c) => c.id !== id) })),
    }),
    { name: 'concept-cards-store' }
  )
); 