/**
 * 🔧 Tool Domain State - Pure Domain Layer
 *
 * @description Pure domain state management for canvas tools and interactions
 * @responsibility Manage tool state with clean domain logic
 * @ai_context This replaces canvas-store dependencies with pure domain state
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

/**
 * 🔧 Domain Tool Types
 */
export enum DomainCanvasTool {
  SELECT = 'select',
  HAND = 'hand',
  TEXT = 'text',
  IMAGE = 'image',
  MASK = 'mask',
}

export interface DomainCanvasPosition {
  x: number;
  y: number;
}

export interface DomainCanvasSize {
  width: number;
  height: number;
}

export interface DomainMaskDrawingState {
  isDrawing: boolean;
  showToolbar: boolean;
  targetImageId?: string;
  tool: 'brush' | 'eraser';
  brushSize: number;
  points: any[];
}

export interface DomainSelectionState {
  visible: boolean;
  x: number;
  y: number;
  width: number;
  height: number;
}

/**
 * 🔧 Tool Domain State Interface
 */
export interface ToolDomainState {
  // State
  activeTool: DomainCanvasTool;
  scale: number;
  position: DomainCanvasPosition;
  canvasSize: DomainCanvasSize;
  isDragging: boolean;
  maskDrawing: DomainMaskDrawingState;
  selection: DomainSelectionState;

  // Tool Actions
  setActiveTool: (tool: DomainCanvasTool) => void;
  selectTool: () => void;
  handTool: () => void;
  textTool: () => void;
  imageTool: () => void;
  maskTool: () => void;

  // Zoom Actions
  setScale: (scale: number) => void;
  zoomIn: () => void;
  zoomOut: () => void;
  resetZoom: () => void;
  getZoomPercentage: () => number;

  // Position Actions
  setPosition: (position: DomainCanvasPosition) => void;
  moveCanvas: (deltaX: number, deltaY: number) => void;

  // Canvas Actions
  setCanvasSize: (size: DomainCanvasSize) => void;
  setDragging: (isDragging: boolean) => void;

  // Mask Actions
  startMaskDrawing: (targetImageId: string) => void;
  stopMaskDrawing: () => void;
  setMaskDrawingState: (state: Partial<DomainMaskDrawingState>) => void;

  // Selection Actions
  setSelection: (selection: Partial<DomainSelectionState>) => void;
  showSelection: (x: number, y: number, width: number, height: number) => void;
  hideSelection: () => void;
}

/**
 * 🔧 Tool Domain Store - Pure Domain State
 * @ai_context This is completely independent of canvas-store
 */
export const useToolDomainStore = create<ToolDomainState>()(
  subscribeWithSelector((set, get) => ({
    // Initial State
    activeTool: DomainCanvasTool.SELECT,
    scale: 1,
    position: { x: 0, y: 0 },
    canvasSize: { width: 1200, height: 800 },
    isDragging: false,
    maskDrawing: {
      isDrawing: false,
      showToolbar: false,
      tool: 'brush',
      brushSize: 10,
      points: [],
    },
    selection: {
      visible: false,
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    },

    // Tool Actions
    setActiveTool: (tool) => {
      console.log('[ToolDomain] Setting active tool:', tool);
      set({ activeTool: tool });
    },

    selectTool: () => {
      console.log('[ToolDomain] Switching to select tool');
      set({ activeTool: DomainCanvasTool.SELECT });
    },

    handTool: () => {
      console.log('[ToolDomain] Switching to hand tool');
      set({ activeTool: DomainCanvasTool.HAND });
    },

    textTool: () => {
      console.log('[ToolDomain] Switching to text tool');
      set({ activeTool: DomainCanvasTool.TEXT });
    },

    imageTool: () => {
      console.log('[ToolDomain] Switching to image tool');
      set({ activeTool: DomainCanvasTool.IMAGE });
    },

    maskTool: () => {
      console.log('[ToolDomain] Switching to mask tool');
      set({ activeTool: DomainCanvasTool.MASK });
    },

    // Zoom Actions
    setScale: (scale) => {
      console.log('[ToolDomain] Setting scale:', scale);
      set({ scale: Math.max(0.1, Math.min(5, scale)) }); // Clamp between 0.1 and 5
    },

    zoomIn: () => {
      console.log('[ToolDomain] Zooming in');
      set((state) => ({
        scale: Math.min(5, state.scale * 1.2),
      }));
    },

    zoomOut: () => {
      console.log('[ToolDomain] Zooming out');
      set((state) => ({
        scale: Math.max(0.1, state.scale / 1.2),
      }));
    },

    resetZoom: () => {
      console.log('[ToolDomain] Resetting zoom');
      set({ scale: 1 });
    },

    getZoomPercentage: () => {
      return Math.round(get().scale * 100);
    },

    // Position Actions
    setPosition: (position) => {
      // Removed spammy position log
      set({ position });
    },

    moveCanvas: (deltaX, deltaY) => {
      // Removed spammy move log
      set((state) => ({
        position: {
          x: state.position.x + deltaX,
          y: state.position.y + deltaY,
        },
      }));
    },

    // Canvas Actions
    setCanvasSize: (size) => {
      console.log('[ToolDomain] Setting canvas size:', size);
      set({ canvasSize: size });
    },

    setDragging: (isDragging) => {
      set({ isDragging });
    },

    // Mask Actions
    startMaskDrawing: (targetImageId) => {
      console.log('[ToolDomain] Starting mask drawing for image:', targetImageId);
      set((state) => ({
        maskDrawing: {
          ...state.maskDrawing,
          isDrawing: true,
          showToolbar: true,
          targetImageId,
          points: [], // Clear points when starting new mask
        },
        activeTool: DomainCanvasTool.MASK,
      }));
    },

    stopMaskDrawing: () => {
      console.log('[ToolDomain] Stopping mask drawing');
      set((state) => ({
        maskDrawing: {
          ...state.maskDrawing,
          isDrawing: false,
          showToolbar: false,
          targetImageId: undefined,
        },
        activeTool: DomainCanvasTool.SELECT,
      }));
    },

    setMaskDrawingState: (maskState) => {
      console.log('[ToolDomain] Setting mask drawing state:', maskState);
      set((state) => ({
        maskDrawing: { ...state.maskDrawing, ...maskState },
      }));
    },

    // Selection Actions
    setSelection: (selection) => {
      set((state) => ({
        selection: { ...state.selection, ...selection },
      }));
    },

    showSelection: (x, y, width, height) => {
      console.log('[ToolDomain] Showing selection:', { x, y, width, height });
      set({
        selection: {
          visible: true,
          x,
          y,
          width,
          height,
        },
      });
    },

    hideSelection: () => {
      set({
        selection: {
          visible: false,
          x: 0,
          y: 0,
          width: 0,
          height: 0,
        },
      });
    },
  }))
);
