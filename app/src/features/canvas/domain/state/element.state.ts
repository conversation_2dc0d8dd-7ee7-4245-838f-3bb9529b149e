/**
 * 🎨 Element Domain State - Pure Domain Layer
 *
 * @description Pure domain state management for canvas elements (no UI dependencies)
 * @responsibility Manage element state with clean domain logic
 * @ai_context This replaces canvas-store dependencies with pure domain state
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

/**
 * 🎨 Domain Element Types
 */
export interface DomainImageElement {
  id: string;
  url: string;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;
  scaleX?: number;
  scaleY?: number;
  opacity?: number;
  taskId?: string; // For tracking generated images
  isReference?: boolean; // For reference image status
  // ✂️ Crop properties for image cropping
  cropX?: number;
  cropY?: number;
  cropWidth?: number;
  cropHeight?: number;
}

export interface DomainTextElement {
  id: string;
  text: string;
  x: number;
  y: number;
  width?: number;
  height?: number;
  fontSize: number;
  fontFamily: string;
  fill: string;
  rotation?: number;
  align?: string;
}

export interface DomainHtmlElement {
  id: string;
  html: string;
  x: number;
  y: number;
  width: number;
  height: number;
  backgroundColor?: string;
  rotation?: number;
}

/**
 * 🎨 Element Domain State Interface
 */
export interface ElementDomainState {
  // State
  images: DomainImageElement[];
  texts: DomainTextElement[];
  htmlElements: DomainHtmlElement[];
  selectedIds: string[];

  // Image Actions
  addImage: (image: DomainImageElement) => void;
  updateImage: (id: string, updates: Partial<DomainImageElement>) => void;
  removeImage: (id: string) => void;
  setImages: (images: DomainImageElement[]) => void;

  // Text Actions
  addText: (text: DomainTextElement) => void;
  updateText: (id: string, updates: Partial<DomainTextElement>) => void;
  removeText: (id: string) => void;
  setTexts: (texts: DomainTextElement[]) => void;

  // HTML Actions
  addHtmlElement: (element: DomainHtmlElement) => void;
  updateHtmlElement: (id: string, updates: Partial<DomainHtmlElement>) => void;
  removeHtmlElement: (id: string) => void;
  setHtmlElements: (elements: DomainHtmlElement[]) => void;

  // Selection Actions
  selectElements: (ids: string[]) => void;
  clearSelection: () => void;
  toggleSelection: (id: string) => void;

  // Utility Actions
  getAllElements: () => (DomainImageElement | DomainTextElement | DomainHtmlElement)[];
  getElementById: (id: string) => DomainImageElement | DomainTextElement | DomainHtmlElement | undefined;
  isSelected: (id: string) => boolean;
}

/**
 * 🎨 Element Domain Store - Pure Domain State
 * @ai_context This is completely independent of canvas-store
 */
export const useElementDomainStore = create<ElementDomainState>()(
  subscribeWithSelector((set, get) => ({
    // Initial State
    images: [],
    texts: [],
    htmlElements: [],
    selectedIds: [],

    // Image Actions
    addImage: (image) => {
      console.log('[ElementDomain] Adding image:', image.id);
      set((state) => ({
        images: [...state.images, image],
      }));
    },

    updateImage: (id, updates) => {
      console.log('[ElementDomain] Updating image:', id, updates);
      set((state) => ({
        images: state.images.map((img) => (img.id === id ? { ...img, ...updates } : img)),
      }));
    },

    removeImage: (id) => {
      console.log('[ElementDomain] Removing image:', id);
      set((state) => ({
        images: state.images.filter((img) => img.id !== id),
        selectedIds: state.selectedIds.filter((selectedId) => selectedId !== id),
      }));
    },

    setImages: (images) => {
      console.log('[ElementDomain] Setting images:', images.length);
      set({ images });
    },

    // Text Actions
    addText: (text) => {
      console.log('[ElementDomain] Adding text:', text.id);
      set((state) => ({
        texts: [...state.texts, text],
      }));
    },

    updateText: (id, updates) => {
      console.log('[ElementDomain] Updating text:', id, updates);
      set((state) => ({
        texts: state.texts.map((text) => (text.id === id ? { ...text, ...updates } : text)),
      }));
    },

    removeText: (id) => {
      console.log('[ElementDomain] Removing text:', id);
      set((state) => ({
        texts: state.texts.filter((text) => text.id !== id),
        selectedIds: state.selectedIds.filter((selectedId) => selectedId !== id),
      }));
    },

    setTexts: (texts) => {
      console.log('[ElementDomain] Setting texts:', texts.length);
      set({ texts });
    },

    // HTML Actions
    addHtmlElement: (element) => {
      console.log('[ElementDomain] Adding HTML element:', element.id);
      set((state) => ({
        htmlElements: [...state.htmlElements, element],
      }));
    },

    updateHtmlElement: (id, updates) => {
      console.log('[ElementDomain] Updating HTML element:', id, updates);
      set((state) => ({
        htmlElements: state.htmlElements.map((element) => (element.id === id ? { ...element, ...updates } : element)),
      }));
    },

    removeHtmlElement: (id) => {
      console.log('[ElementDomain] Removing HTML element:', id);
      set((state) => ({
        htmlElements: state.htmlElements.filter((element) => element.id !== id),
        selectedIds: state.selectedIds.filter((selectedId) => selectedId !== id),
      }));
    },

    setHtmlElements: (elements) => {
      console.log('[ElementDomain] Setting HTML elements:', elements.length);
      set({ htmlElements: elements });
    },

    // Selection Actions
    selectElements: (ids) => {
      console.log('[ElementDomain] Selecting elements:', ids);
      set({ selectedIds: ids });
    },

    clearSelection: () => {
      console.log('[ElementDomain] Clearing selection');
      set({ selectedIds: [] });
    },

    toggleSelection: (id) => {
      console.log('[ElementDomain] Toggling selection:', id);
      set((state) => {
        const isSelected = state.selectedIds.includes(id);
        return {
          selectedIds: isSelected
            ? state.selectedIds.filter((selectedId) => selectedId !== id)
            : [...state.selectedIds, id],
        };
      });
    },

    // Utility Actions
    getAllElements: () => {
      const state = get();
      return [...state.images, ...state.texts, ...state.htmlElements];
    },

    getElementById: (id) => {
      const state = get();
      return (
        state.images.find((img) => img.id === id) ||
        state.texts.find((text) => text.id === id) ||
        state.htmlElements.find((element) => element.id === id)
      );
    },

    isSelected: (id) => {
      return get().selectedIds.includes(id);
    },
  }))
);
