/**
 * 🗄️ Canvas Repository Interface
 *
 * @description Contract for canvas data operations
 * @responsibility Defines data access methods for canvas
 * @dependencies Canvas entity, shared repository types
 * @ai_context This interface defines how canvas data is accessed and stored
 *
 * @example
 * ```typescript
 * class CanvasRepositoryImpl implements ICanvasRepository {
 *   async save(canvas: Canvas): Promise<void> {
 *     // Implementation using Prisma
 *   }
 *
 *   async findById(id: string): Promise<Canvas | null> {
 *     // Implementation using Prisma
 *   }
 * }
 * ```
 */

import { Canvas, CanvasStatus } from '../entities/canvas.entity';

/**
 * 🔍 Canvas List Options
 * @ai_context Options for listing canvases with filtering and pagination
 */
export interface CanvasListOptions {
  userId?: number;
  organizationId?: string;
  status?: CanvasStatus;
  isCollaborative?: boolean;
  limit?: number;
  offset?: number;
  sortBy?: 'name' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
  includeDeleted?: boolean;
}

/**
 * 🔍 Canvas Search Options
 * @ai_context Options for searching canvases
 */
export interface CanvasSearchOptions {
  userId?: string;
  organizationId?: string;
  status?: CanvasStatus;
  isCollaborative?: boolean;
  limit?: number;
  offset?: number;
  includeDeleted?: boolean;
}

/**
 * 📊 Canvas Statistics
 * @ai_context Statistical data about canvases
 */
export interface CanvasStatistics {
  totalCanvases: number;
  activeCanvases: number;
  draftCanvases: number;
  archivedCanvases: number;
  collaborativeCanvases: number;
  totalElements: number;
  averageElementsPerCanvas: number;
  mostUsedTools: Array<{
    tool: string;
    usage: number;
  }>;
  createdThisMonth: number;
  createdThisWeek: number;
}

/**
 * 🗄️ Canvas Repository Interface
 * @ai_context Contract for all canvas data operations following Repository pattern
 */
export interface ICanvasRepository {
  /**
   * 💾 Save a canvas (create or update)
   * @param canvas Canvas to save
   * @returns Promise that resolves when saved
   */
  save(canvas: Canvas): Promise<void>;

  /**
   * 🔍 Find canvas by ID
   * @param id Canvas ID
   * @returns Canvas or null if not found
   */
  findById(id: string): Promise<Canvas | null>;

  /**
   * 🔍 Find canvas by name and organization
   * @param name Canvas name
   * @param organizationId Organization ID
   * @returns Canvas or null if not found
   */
  findByNameAndOrganization(name: string, organizationId: string): Promise<Canvas | null>;

  /**
   * 📋 List canvases with filtering and pagination
   * @param options List options
   * @returns Array of canvases
   */
  list(options?: Partial<CanvasListOptions>): Promise<Canvas[]>;

  /**
   * 🔍 Find canvases by user
   * @param userId User ID
   * @param organizationId Organization ID
   * @param options Additional options
   * @returns User's canvases
   */
  findByUser(userId: number, organizationId: string, options?: Partial<CanvasListOptions>): Promise<Canvas[]>;

  /**
   * 🔍 Find collaborative canvases
   * @param userId User ID
   * @param organizationId Organization ID
   * @param options Additional options
   * @returns Collaborative canvases user has access to
   */
  findCollaborativeCanvases(
    userId: number,
    organizationId: string,
    options?: Partial<CanvasListOptions>
  ): Promise<Canvas[]>;

  /**
   * 🔍 Search canvases
   * @param query Search query
   * @param organizationId Organization ID
   * @param options Additional search options
   * @returns Matching canvases
   */
  search(query: string, organizationId: string, options?: Partial<CanvasSearchOptions>): Promise<Canvas[]>;

  /**
   * 🗑️ Delete a canvas (soft delete)
   * @param id Canvas ID
   * @returns Promise that resolves when deleted
   */
  delete(id: string): Promise<void>;

  /**
   * 🗑️ Permanently delete a canvas
   * @param id Canvas ID
   * @returns Promise that resolves when permanently deleted
   */
  permanentlyDelete(id: string): Promise<void>;

  /**
   * 🔄 Restore a soft-deleted canvas
   * @param id Canvas ID
   * @returns Promise that resolves when restored
   */
  restore(id: string): Promise<void>;

  /**
   * ✅ Check if canvas exists
   * @param id Canvas ID
   * @returns True if exists, false otherwise
   */
  exists(id: string): Promise<boolean>;

  /**
   * 🔢 Count canvases
   * @param options Filter options
   * @returns Count of matching canvases
   */
  count(options?: Partial<CanvasListOptions>): Promise<number>;

  /**
   * 📊 Get canvas statistics
   * @param organizationId Organization ID
   * @returns Statistical data
   */
  getStatistics(organizationId: string): Promise<CanvasStatistics>;

  /**
   * 👥 Add collaborator to canvas
   * @param canvasId Canvas ID
   * @param userId User ID to add
   * @returns Promise that resolves when collaborator added
   */
  addCollaborator(canvasId: string, userId: number): Promise<void>;

  /**
   * 👥 Remove collaborator from canvas
   * @param canvasId Canvas ID
   * @param userId User ID to remove
   * @returns Promise that resolves when collaborator removed
   */
  removeCollaborator(canvasId: string, userId: number): Promise<void>;

  /**
   * 👥 Get canvas collaborators
   * @param canvasId Canvas ID
   * @returns Array of collaborator user IDs
   */
  getCollaborators(canvasId: string): Promise<number[]>;

  /**
   * 📈 Update canvas activity
   * @param canvasId Canvas ID
   * @param userId User ID
   * @param activity Activity type
   * @returns Promise that resolves when activity recorded
   */
  recordActivity(canvasId: string, userId: number, activity: string): Promise<void>;

  /**
   * 📈 Get canvas activity history
   * @param canvasId Canvas ID
   * @param limit Number of activities to return
   * @returns Array of activities
   */
  getActivityHistory(
    canvasId: string,
    limit?: number
  ): Promise<
    Array<{
      userId: number;
      activity: string;
      timestamp: Date;
    }>
  >;

  /**
   * 🔄 Duplicate canvas
   * @param canvasId Canvas ID to duplicate
   * @param newName Name for the duplicate
   * @param userId User ID creating the duplicate
   * @returns New canvas
   */
  duplicate(canvasId: string, newName: string, userId: number): Promise<Canvas>;

  /**
   * 📤 Export canvas data
   * @param canvasId Canvas ID
   * @param format Export format
   * @returns Exported data
   */
  export(canvasId: string, format: 'json' | 'pdf' | 'png'): Promise<Buffer | string>;

  /**
   * 📥 Import canvas data
   * @param data Canvas data to import
   * @param userId User ID importing
   * @param organizationId Organization ID
   * @returns Imported canvas
   */
  import(data: any, userId: number, organizationId: string): Promise<Canvas>;

  /**
   * 🔄 Archive canvas
   * @param canvasId Canvas ID
   * @returns Promise that resolves when archived
   */
  archive(canvasId: string): Promise<void>;

  /**
   * 🔄 Unarchive canvas
   * @param canvasId Canvas ID
   * @returns Promise that resolves when unarchived
   */
  unarchive(canvasId: string): Promise<void>;

  /**
   * 🏷️ Get canvas tags
   * @param organizationId Organization ID
   * @returns Array of unique tags used in canvases
   */
  getTags(organizationId: string): Promise<string[]>;

  /**
   * 🏷️ Add tag to canvas
   * @param canvasId Canvas ID
   * @param tag Tag to add
   * @returns Promise that resolves when tag added
   */
  addTag(canvasId: string, tag: string): Promise<void>;

  /**
   * 🏷️ Remove tag from canvas
   * @param canvasId Canvas ID
   * @param tag Tag to remove
   * @returns Promise that resolves when tag removed
   */
  removeTag(canvasId: string, tag: string): Promise<void>;

  /**
   * 🔍 Find canvases by tag
   * @param tag Tag to search for
   * @param organizationId Organization ID
   * @param options Additional options
   * @returns Canvases with the specified tag
   */
  findByTag(tag: string, organizationId: string, options?: Partial<CanvasListOptions>): Promise<Canvas[]>;
}
