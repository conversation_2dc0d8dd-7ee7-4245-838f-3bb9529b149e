import { SharedCanvasEntity, CreateSharedCanvasSchema, UpdateSharedCanvasSchema } from '../entities/shared-canvas.entity';
import { z } from 'zod';

/**
 * 🎨 Canvas Sharing Repository Interface
 * 
 * @description Contract for canvas sharing data operations
 * @responsibility Defines data access methods for canvas sharing
 * @dependencies SharedCanvas entity, shared repository types
 * @ai_context This interface follows the same pattern as asset sharing repository
 */
export interface CanvasSharingRepository {
  /**
   * Create a new canvas share
   */
  create(data: Omit<z.infer<typeof CreateSharedCanvasSchema>, 'sharedWithEmail'> & {
    sharedByUserId: number;
    token: string;
    sharedWithEmail?: string | null;
  }): Promise<SharedCanvasEntity>;

  /**
   * Find a shared canvas by token
   */
  findByToken(token: string): Promise<SharedCanvasEntity | null>;

  /**
   * Find all shares for a specific canvas
   */
  findByCanvasId(canvasId: string): Promise<SharedCanvasEntity[]>;

  /**
   * Find all canvases shared with a specific user (by email)
   */
  findBySharedWithEmail(email: string): Promise<SharedCanvasEntity[]>;

  /**
   * Find all canvases shared by a specific user
   */
  findBySharedByUserId(userId: number): Promise<SharedCanvasEntity[]>;

  /**
   * Find all canvases shared with a specific user (by user ID)
   */
  findBySharedWithUserId(userId: number): Promise<SharedCanvasEntity[]>;

  /**
   * Update a shared canvas
   */
  update(id: string, data: z.infer<typeof UpdateSharedCanvasSchema>): Promise<SharedCanvasEntity>;

  /**
   * Delete a shared canvas
   */
  delete(id: string): Promise<void>;

  /**
   * Record access to a shared canvas
   */
  recordAccess(token: string): Promise<void>;

  /**
   * Check if a canvas is already shared with a specific email
   */
  isCanvasSharedWithEmail(canvasId: string, email: string): Promise<boolean>;

  /**
   * Get sharing statistics for a canvas
   */
  getCanvasSharingStats(canvasId: string): Promise<{
    totalShares: number;
    totalAccess: number;
    activeShares: number;
    lastAccessed: Date | null;
  }>;

  /**
   * Clean up expired shares
   */
  cleanupExpiredShares(): Promise<number>;

  /**
   * Revoke all shares for a canvas
   */
  revokeAllCanvasShares(canvasId: string): Promise<void>;

  /**
   * Find shared canvas with full relations (canvas, users)
   */
  findByTokenWithRelations(token: string): Promise<SharedCanvasEntity | null>;
}

/**
 * 🎨 Canvas Sharing Repository Implementation
 * 
 * @description Prisma implementation of canvas sharing repository
 * @responsibility Handles database operations for canvas sharing
 * @dependencies Prisma client, SharedCanvas entity
 * @ai_context Implements the repository interface using Prisma
 */
export class PrismaCanvasSharingRepository implements CanvasSharingRepository {
  constructor(private prisma: any) {}

  async create(data: Omit<z.infer<typeof CreateSharedCanvasSchema>, 'sharedWithEmail'> & {
    sharedByUserId: number;
    token: string;
    sharedWithEmail?: string | null;
  }): Promise<SharedCanvasEntity> {
    const sharedCanvasData = SharedCanvasEntity.createNew(data);
    
    const createdShare = await this.prisma.sharedCanvas.create({
      data: sharedCanvasData,
      include: {
        canvas: true,
        sharedBy: true,
        sharedWithUser: true,
      },
    });

    return SharedCanvasEntity.fromPrismaSharedCanvas(createdShare);
  }

  async findByToken(token: string): Promise<SharedCanvasEntity | null> {
    const sharedCanvas = await this.prisma.sharedCanvas.findUnique({
      where: { token },
      include: {
        canvas: true,
        sharedBy: true,
        sharedWithUser: true,
      },
    });

    return sharedCanvas ? SharedCanvasEntity.fromPrismaSharedCanvas(sharedCanvas) : null;
  }

  async findByCanvasId(canvasId: string): Promise<SharedCanvasEntity[]> {
    const sharedCanvases = await this.prisma.sharedCanvas.findMany({
      where: {
        canvasId,
        isActive: true, // Only return active shares
      },
      include: {
        canvas: true,
        sharedBy: true,
        sharedWithUser: true,
      },
      orderBy: { sharedAt: 'desc' },
    });

    return sharedCanvases.map((sc: any) => SharedCanvasEntity.fromPrismaSharedCanvas(sc));
  }

  async findBySharedWithEmail(email: string): Promise<SharedCanvasEntity[]> {
    const sharedCanvases = await this.prisma.sharedCanvas.findMany({
      where: { 
        sharedWithEmail: email,
        isActive: true,
      },
      include: {
        canvas: true,
        sharedBy: true,
        sharedWithUser: true,
      },
      orderBy: { sharedAt: 'desc' },
    });

    return sharedCanvases.map((sc: any) => SharedCanvasEntity.fromPrismaSharedCanvas(sc));
  }

  async findBySharedByUserId(userId: number): Promise<SharedCanvasEntity[]> {
    const sharedCanvases = await this.prisma.sharedCanvas.findMany({
      where: { sharedByUserId: userId },
      include: {
        canvas: true,
        sharedBy: true,
        sharedWithUser: true,
      },
      orderBy: { sharedAt: 'desc' },
    });

    return sharedCanvases.map((sc: any) => SharedCanvasEntity.fromPrismaSharedCanvas(sc));
  }

  async findBySharedWithUserId(userId: number): Promise<SharedCanvasEntity[]> {
    const sharedCanvases = await this.prisma.sharedCanvas.findMany({
      where: { 
        sharedWithUserId: userId,
        isActive: true,
      },
      include: {
        canvas: true,
        sharedBy: true,
        sharedWithUser: true,
      },
      orderBy: { sharedAt: 'desc' },
    });

    return sharedCanvases.map((sc: any) => SharedCanvasEntity.fromPrismaSharedCanvas(sc));
  }

  async update(id: string, data: z.infer<typeof UpdateSharedCanvasSchema>): Promise<SharedCanvasEntity> {
    const updatedShare = await this.prisma.sharedCanvas.update({
      where: { id },
      data,
      include: {
        canvas: true,
        sharedBy: true,
        sharedWithUser: true,
      },
    });

    return SharedCanvasEntity.fromPrismaSharedCanvas(updatedShare);
  }

  async delete(id: string): Promise<void> {
    await this.prisma.sharedCanvas.delete({
      where: { id },
    });
  }

  async recordAccess(token: string): Promise<void> {
    await this.prisma.sharedCanvas.update({
      where: { token },
      data: {
        accessCount: { increment: 1 },
        lastAccessedAt: new Date(),
      },
    });
  }

  async isCanvasSharedWithEmail(canvasId: string, email: string): Promise<boolean> {
    const existingShare = await this.prisma.sharedCanvas.findFirst({
      where: {
        canvasId,
        sharedWithEmail: email,
        isActive: true,
      },
    });

    return !!existingShare;
  }

  async getCanvasSharingStats(canvasId: string): Promise<{
    totalShares: number;
    totalAccess: number;
    activeShares: number;
    lastAccessed: Date | null;
  }> {
    const stats = await this.prisma.sharedCanvas.aggregate({
      where: { canvasId },
      _count: { id: true },
      _sum: { accessCount: true },
      _max: { lastAccessedAt: true },
    });

    const activeShares = await this.prisma.sharedCanvas.count({
      where: { 
        canvasId,
        isActive: true,
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } },
        ],
      },
    });

    return {
      totalShares: stats._count.id || 0,
      totalAccess: stats._sum.accessCount || 0,
      activeShares,
      lastAccessed: stats._max.lastAccessedAt,
    };
  }

  async cleanupExpiredShares(): Promise<number> {
    const result = await this.prisma.sharedCanvas.updateMany({
      where: {
        isActive: true,
        expiresAt: { lt: new Date() },
      },
      data: { isActive: false },
    });

    return result.count;
  }

  async revokeAllCanvasShares(canvasId: string): Promise<void> {
    await this.prisma.sharedCanvas.updateMany({
      where: { canvasId },
      data: { isActive: false },
    });
  }

  async findByTokenWithRelations(token: string): Promise<SharedCanvasEntity | null> {
    const sharedCanvas = await this.prisma.sharedCanvas.findUnique({
      where: { token },
      include: {
        canvas: {
          include: {
            user: true,
            organization: true,
          },
        },
        sharedBy: true,
        sharedWithUser: true,
      },
    });

    return sharedCanvas ? SharedCanvasEntity.fromPrismaSharedCanvas(sharedCanvas) : null;
  }
}

export default CanvasSharingRepository;
