import { z } from 'zod';

// Canvas sharing permission levels
export enum CanvasSharingPermission {
  VIEW = 'view',
  COMMENT = 'comment', 
  EDIT = 'edit',
}

// Canvas sharing settings interface
export interface CanvasSharingSettings {
  allowComments: boolean;
  allowEdit: boolean;
  requirePassword: boolean;
  password?: string;
  trackViews: boolean;
  notifyOnAccess: boolean;
}

// Shared canvas interface
export interface SharedCanvas {
  id: string;
  canvasId: string;
  sharedByUserId: number;
  sharedWithEmail: string | null; // null for public links
  sharedWithUserId: number | null;
  permission: CanvasSharingPermission;
  token: string;
  settings: CanvasSharingSettings;
  expiresAt: Date | null;
  isActive: boolean;
  accessCount: number;
  lastAccessedAt: Date | null;
  sharedAt: Date;

  // Related entities (optional for domain entity)
  canvas?: any;
  sharedBy?: any;
  sharedWithUser?: any;

  // Index signature for WASP SuperJSON compatibility
  [key: string]: any;
}

// Validation schemas
export const CanvasSharingSettingsSchema = z.object({
  allowComments: z.boolean().default(true),
  allowEdit: z.boolean().default(false),
  requirePassword: z.boolean().default(false),
  password: z.string().optional(),
  trackViews: z.boolean().default(true),
  notifyOnAccess: z.boolean().default(false),
});

export const CreateSharedCanvasSchema = z.object({
  canvasId: z.string(),
  sharedWithEmail: z.string().email().optional().nullable(),
  permission: z.nativeEnum(CanvasSharingPermission).default(CanvasSharingPermission.VIEW),
  settings: CanvasSharingSettingsSchema.default({}),
  expiresAt: z.date().optional(),
});

export const UpdateSharedCanvasSchema = z.object({
  permission: z.nativeEnum(CanvasSharingPermission).optional(),
  settings: CanvasSharingSettingsSchema.optional(),
  expiresAt: z.date().optional(),
  isActive: z.boolean().optional(),
});

/**
 * 🎨 Shared Canvas Entity
 * 
 * @description Domain entity for canvas sharing with business logic
 * @responsibility Encapsulates canvas sharing rules and validation
 * @dependencies Zod validation schemas
 * @ai_context Follows the same pattern as SharedAssetEntity
 */
export class SharedCanvasEntity {
  constructor(private sharedCanvas: SharedCanvas) {}

  // Getters
  get id(): string {
    return this.sharedCanvas.id;
  }

  get canvasId(): string {
    return this.sharedCanvas.canvasId;
  }

  get sharedByUserId(): number {
    return this.sharedCanvas.sharedByUserId;
  }

  get sharedWithEmail(): string | null {
    return this.sharedCanvas.sharedWithEmail;
  }

  get permission(): CanvasSharingPermission {
    return this.sharedCanvas.permission;
  }

  get token(): string {
    return this.sharedCanvas.token;
  }

  get settings(): CanvasSharingSettings {
    return this.sharedCanvas.settings;
  }

  get expiresAt(): Date | null {
    return this.sharedCanvas.expiresAt;
  }

  get isActive(): boolean {
    return this.sharedCanvas.isActive;
  }

  get accessCount(): number {
    return this.sharedCanvas.accessCount;
  }

  get sharedAt(): Date {
    return this.sharedCanvas.sharedAt;
  }

  get sharedWithUserId(): number | null {
    return this.sharedCanvas.sharedWithUserId;
  }

  get lastAccessedAt(): Date | null {
    return this.sharedCanvas.lastAccessedAt;
  }

  // Business logic methods
  isValid(): boolean {
    if (!this.sharedCanvas.isActive) return false;
    if (this.sharedCanvas.expiresAt && this.sharedCanvas.expiresAt < new Date()) return false;
    return true;
  }

  isExpired(): boolean {
    return this.sharedCanvas.expiresAt !== null && this.sharedCanvas.expiresAt < new Date();
  }

  isPublicLink(): boolean {
    return this.sharedCanvas.sharedWithEmail === null;
  }

  canView(): boolean {
    return this.isValid();
  }

  canComment(): boolean {
    return this.isValid() && 
           (this.sharedCanvas.permission === CanvasSharingPermission.COMMENT || 
            this.sharedCanvas.permission === CanvasSharingPermission.EDIT) &&
           this.sharedCanvas.settings.allowComments;
  }

  canEdit(): boolean {
    return this.isValid() && 
           this.sharedCanvas.permission === CanvasSharingPermission.EDIT &&
           this.sharedCanvas.settings.allowEdit;
  }

  requiresPassword(): boolean {
    return this.sharedCanvas.settings.requirePassword;
  }

  shouldTrackViews(): boolean {
    return this.sharedCanvas.settings.trackViews;
  }

  shouldNotifyOnAccess(): boolean {
    return this.sharedCanvas.settings.notifyOnAccess;
  }

  // Update methods
  recordAccess(): void {
    if (this.shouldTrackViews()) {
      this.sharedCanvas.accessCount += 1;
      this.sharedCanvas.lastAccessedAt = new Date();
    }
  }

  updatePermission(permission: CanvasSharingPermission): void {
    this.sharedCanvas.permission = permission;
  }

  updateSettings(settings: Partial<CanvasSharingSettings>): void {
    this.sharedCanvas.settings = { ...this.sharedCanvas.settings, ...settings };
  }

  deactivate(): void {
    this.sharedCanvas.isActive = false;
  }

  activate(): void {
    this.sharedCanvas.isActive = true;
  }

  // Static factory methods
  static fromPrismaSharedCanvas(prismaSharedCanvas: any): SharedCanvasEntity {
    const sharedCanvas: SharedCanvas = {
      id: prismaSharedCanvas.id,
      canvasId: prismaSharedCanvas.canvasId,
      sharedByUserId: prismaSharedCanvas.sharedByUserId,
      sharedWithEmail: prismaSharedCanvas.sharedWithEmail,
      sharedWithUserId: prismaSharedCanvas.sharedWithUserId,
      permission: prismaSharedCanvas.permission as CanvasSharingPermission,
      token: prismaSharedCanvas.token,
      settings: prismaSharedCanvas.settings as CanvasSharingSettings || SharedCanvasEntity.getDefaultSettings(),
      expiresAt: prismaSharedCanvas.expiresAt,
      isActive: prismaSharedCanvas.isActive,
      accessCount: prismaSharedCanvas.accessCount,
      lastAccessedAt: prismaSharedCanvas.lastAccessedAt,
      sharedAt: prismaSharedCanvas.sharedAt,
      canvas: prismaSharedCanvas.canvas,
      sharedBy: prismaSharedCanvas.sharedBy,
      sharedWithUser: prismaSharedCanvas.sharedWithUser,
    };

    return new SharedCanvasEntity(sharedCanvas);
  }

  static createNew(
    data: z.infer<typeof CreateSharedCanvasSchema> & {
      sharedByUserId: number;
      token: string;
    }
  ): Partial<SharedCanvas> {
    return {
      canvasId: data.canvasId,
      sharedByUserId: data.sharedByUserId,
      sharedWithEmail: data.sharedWithEmail ?? null,
      permission: data.permission,
      token: data.token,
      settings: data.settings,
      expiresAt: data.expiresAt || null,
      isActive: true,
      accessCount: 0,
      lastAccessedAt: null,
      sharedAt: new Date(),
    };
  }

  // Utility methods
  static generateToken(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  static getDefaultSettings(): CanvasSharingSettings {
    return {
      allowComments: true,
      allowEdit: false,
      requirePassword: false,
      trackViews: true,
      notifyOnAccess: false,
    };
  }

  static getDefaultPermission(): CanvasSharingPermission {
    return CanvasSharingPermission.VIEW;
  }

  // Convert to plain object
  toPlainObject(): SharedCanvas {
    return this.sharedCanvas;
  }

  // Validation
  static validateCreateData(data: unknown): z.infer<typeof CreateSharedCanvasSchema> {
    return CreateSharedCanvasSchema.parse(data);
  }

  static validateUpdateData(data: unknown): z.infer<typeof UpdateSharedCanvasSchema> {
    return UpdateSharedCanvasSchema.parse(data);
  }
}

export default SharedCanvasEntity;
