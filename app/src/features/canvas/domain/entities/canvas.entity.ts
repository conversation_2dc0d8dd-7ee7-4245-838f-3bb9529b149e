/**
 * 🎨 Canvas Domain Entity
 *
 * @description Core domain model for canvas management
 * @responsibility Encapsulates canvas business rules and validation
 * @dependencies None (pure domain model)
 * @ai_context This entity represents a complete canvas with elements, tools, and state
 *
 * @example
 * ```typescript
 * const canvas = new Canvas({
 *   name: "My Design Canvas",
 *   userId: 123,
 *   organizationId: "org_456",
 *   elements: []
 * });
 *
 * canvas.addImage(imageElement);
 * canvas.selectTool(CanvasToolType.SELECT);
 * canvas.validateState(); // Returns validation result
 * ```
 */

import { v4 as uuidv4 } from 'uuid';

/**
 * 🎨 Canvas Tool Types
 * @ai_context Available tools for canvas interaction
 */
export enum CanvasToolType {
  SELECT = 'select',
  HAND = 'hand',
  MASK = 'mask',
}

/**
 * 🎨 Canvas Status Enum
 * @ai_context Lifecycle states of a canvas
 */
export enum CanvasStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  DELETED = 'deleted',
}

/**
 * 🎨 Canvas Element Base Interface
 * @ai_context Common properties for all canvas elements
 */
export interface CanvasElementBase {
  id: string;
  type: 'image' | 'text' | 'html';
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;
  isSelected?: boolean;
  isDragging?: boolean;
  taskId?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 🖼️ Image Element Interface
 * @ai_context Image-specific properties
 */
export interface ImageElement extends CanvasElementBase {
  type: 'image';
  src: string;
  originalSrc?: string;
  crop?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  filters?: {
    brightness?: number;
    contrast?: number;
    saturation?: number;
  };
}

/**
 * 📝 Text Element Interface
 * @ai_context Text-specific properties
 */
export interface TextElement extends CanvasElementBase {
  type: 'text';
  text: string;
  fontSize: number;
  fontFamily: string;
  fontWeight?: string;
  fontStyle?: string;
  color: string;
  align?: 'left' | 'center' | 'right';
  verticalAlign?: 'top' | 'middle' | 'bottom';
}

/**
 * 🌐 HTML Element Interface
 * @ai_context HTML content properties
 */
export interface HtmlElement extends CanvasElementBase {
  type: 'html';
  html: string;
  css?: string;
  htmlUrl?: string;
  backgroundColor?: string;
  isInteractive?: boolean;
}

/**
 * 🎯 Selection State Interface
 * @ai_context Canvas selection rectangle
 */
export interface SelectionState {
  visible: boolean;
  x: number;
  y: number;
  width: number;
  height: number;
}

/**
 * 🎨 Canvas State Interface
 * @ai_context Complete canvas state
 */
export interface CanvasState {
  // Canvas metadata
  id: string;
  name: string;
  userId: number;
  organizationId: string;
  status: CanvasStatus;

  // Canvas elements
  images: ImageElement[];
  texts: TextElement[];
  htmlElements: HtmlElement[];

  // Canvas settings
  canvasSize: { width: number; height: number };
  activeTool: CanvasToolType;
  selectedIds: string[];

  // Canvas transform
  scale: number;
  position: { x: number; y: number };

  // Selection state
  selection: SelectionState;

  // Collaboration
  isCollaborative: boolean;
  collaborators: string[];

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
}

/**
 * 🎨 Canvas Domain Entity Class
 * @ai_context Core domain model with business logic and validation
 */
export class Canvas {
  private constructor(
    public readonly id: string,
    public readonly name: string,
    public readonly userId: number,
    public readonly organizationId: string,
    public readonly status: CanvasStatus,
    private _images: ImageElement[],
    private _texts: TextElement[],
    private _htmlElements: HtmlElement[],
    private _canvasSize: { width: number; height: number },
    private _activeTool: CanvasToolType,
    private _selectedIds: string[],
    private _scale: number,
    private _position: { x: number; y: number },
    private _selection: SelectionState,
    private _isCollaborative: boolean,
    private _collaborators: string[],
    public readonly createdAt: Date,
    public readonly updatedAt: Date,
    public readonly deletedAt: Date | null = null
  ) {}

  /**
   * 🏗️ Create a new canvas
   * @param data Canvas creation data
   * @returns New Canvas instance
   */
  static create(data: {
    id?: string;
    name: string;
    userId: number;
    organizationId: string;
    canvasSize?: { width: number; height: number };
    isCollaborative?: boolean;
  }): Canvas {
    // Validate required fields
    if (!data.name.trim()) {
      throw new Error('Canvas name is required');
    }

    if (data.name.length > 100) {
      throw new Error('Canvas name must be 100 characters or less');
    }

    if (data.userId <= 0) {
      throw new Error('Valid user ID is required');
    }

    if (!data.organizationId.trim()) {
      throw new Error('Organization ID is required');
    }

    const now = new Date();
    const defaultCanvasSize = data.canvasSize || { width: 1920, height: 1080 };

    return new Canvas(
      data.id || uuidv4(),
      data.name.trim(),
      data.userId,
      data.organizationId,
      CanvasStatus.DRAFT,
      [], // images
      [], // texts
      [], // htmlElements
      defaultCanvasSize,
      CanvasToolType.SELECT,
      [], // selectedIds
      1, // scale
      { x: 0, y: 0 }, // position
      { visible: false, x: 0, y: 0, width: 0, height: 0 }, // selection
      data.isCollaborative || false,
      [], // collaborators
      now,
      now,
      null // deletedAt
    );
  }

  // Getters for immutable access
  get images(): readonly ImageElement[] {
    return this._images;
  }
  get texts(): readonly TextElement[] {
    return this._texts;
  }
  get htmlElements(): readonly HtmlElement[] {
    return this._htmlElements;
  }
  get canvasSize(): { width: number; height: number } {
    return { ...this._canvasSize };
  }
  get activeTool(): CanvasToolType {
    return this._activeTool;
  }
  get selectedIds(): readonly string[] {
    return this._selectedIds;
  }
  get scale(): number {
    return this._scale;
  }
  get position(): { x: number; y: number } {
    return { ...this._position };
  }
  get selection(): SelectionState {
    return { ...this._selection };
  }
  get isCollaborative(): boolean {
    return this._isCollaborative;
  }
  get collaborators(): readonly string[] {
    return this._collaborators;
  }

  /**
   * 🖼️ Add image to canvas
   * @param image Image element to add
   */
  addImage(image: Omit<ImageElement, 'id' | 'createdAt' | 'updatedAt'>): void {
    const newImage: ImageElement = {
      ...image,
      id: uuidv4(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this._images = [...this._images, newImage];
  }

  /**
   * 📝 Add text to canvas
   * @param text Text element to add
   */
  addText(text: Omit<TextElement, 'id' | 'createdAt' | 'updatedAt'>): void {
    const newText: TextElement = {
      ...text,
      id: uuidv4(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this._texts = [...this._texts, newText];
  }

  /**
   * 🌐 Add HTML element to canvas
   * @param html HTML element to add
   */
  addHtmlElement(html: Omit<HtmlElement, 'id' | 'createdAt' | 'updatedAt'>): void {
    const newHtml: HtmlElement = {
      ...html,
      id: uuidv4(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this._htmlElements = [...this._htmlElements, newHtml];
  }

  /**
   * 🎯 Select tool
   * @param tool Tool to activate
   */
  selectTool(tool: CanvasToolType): void {
    this._activeTool = tool;
  }

  /**
   * 🎯 Select elements
   * @param ids Element IDs to select
   */
  selectElements(ids: string[]): void {
    this._selectedIds = [...ids];
  }

  /**
   * 🔍 Zoom canvas
   * @param scale New scale value
   */
  setScale(scale: number): void {
    if (scale <= 0) {
      throw new Error('Scale must be positive');
    }
    this._scale = Math.max(0.1, Math.min(10, scale));
  }

  /**
   * 📍 Set canvas position
   * @param position New position
   */
  setPosition(position: { x: number; y: number }): void {
    this._position = { ...position };
  }

  /**
   * ✅ Validate canvas state
   * @returns Validation result
   */
  validate(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.name.trim()) {
      errors.push('Canvas name is required');
    }

    if (this.name.length > 100) {
      errors.push('Canvas name must be 100 characters or less');
    }

    if (this.userId <= 0) {
      errors.push('Valid user ID is required');
    }

    if (!this.organizationId.trim()) {
      errors.push('Organization ID is required');
    }

    if (this._scale <= 0) {
      errors.push('Scale must be positive');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 🔄 Convert to state object
   * @returns Canvas state for serialization
   */
  toState(): CanvasState {
    return {
      id: this.id,
      name: this.name,
      userId: this.userId,
      organizationId: this.organizationId,
      status: this.status,
      images: [...this._images],
      texts: [...this._texts],
      htmlElements: [...this._htmlElements],
      canvasSize: { ...this._canvasSize },
      activeTool: this._activeTool,
      selectedIds: [...this._selectedIds],
      scale: this._scale,
      position: { ...this._position },
      selection: { ...this._selection },
      isCollaborative: this._isCollaborative,
      collaborators: [...this._collaborators],
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      deletedAt: this.deletedAt,
    };
  }
}
