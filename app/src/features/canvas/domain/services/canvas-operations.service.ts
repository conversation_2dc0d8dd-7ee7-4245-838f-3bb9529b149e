/**
 * 🎨 Canvas Operations Service - MODULAR VERSION
 * @description Domain service for canvas operations (replaces canvas store functions)
 * @responsibility Handle canvas element operations in a modular way
 * @ai_context Replaces canvas store functions for modular architecture
 */

import { v4 as uuidv4 } from 'uuid';
import { type DomainTextElement } from '../state';

// Types for canvas operations
export interface CanvasImage {
  id: string;
  src: string;
  x: number;
  y: number;
  width: number;
  height: number;
  taskId?: string;
  imageIndex?: number;
  totalImages?: number;
  rotation?: number;
  isDragging?: boolean;
}

export interface CanvasHtmlElement {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  html?: string;
  htmlUrl?: string;
  taskId?: string;
  rotation?: number;
  isDragging?: boolean;
}

export interface CanvasTextElement {
  id: string;
  x: number;
  y: number;
  text: string;
  fontSize: number;
  fontFamily: string;
  fill: string;
  width: number;
  rotation?: number;
  isDragging?: boolean;
}

export interface CanvasState {
  images: CanvasImage[];
  htmlElements: CanvasHtmlElement[];
  textElements: CanvasTextElement[];
  selectedIds: string[];
  activeTool: 'select' | 'hand' | 'mask';
  canvasSize: { width: number; height: number };
  position: { x: number; y: number };
  scale: number;
  debug: boolean;
  history: any[];
  historyStep: number;
}

/**
 * 🎨 Canvas Operations Service
 * @description Provides canvas operations without direct store dependencies
 */
export class CanvasOperationsService {
  /**
   * 🖼️ Add Image Operation
   * @description Creates a new image ID and returns it
   */
  static addImage(): string {
    return `image-${uuidv4()}`;
  }

  /**
   * ✏️ Add Text Operation
   * @description Creates a new text element with default properties
   */
  static addText(): DomainTextElement {
    const defaultTextElement: DomainTextElement = {
      id: `text-${uuidv4()}`,
      text: 'Double click to edit',
      x: 100,
      y: 100,
      fontSize: 18,
      fontFamily: 'Arial',
      fill: '#000000',
      width: 200,
    };
    return defaultTextElement;
  }

  /**
   * 🌐 Add HTML Element Operation
   * @description Creates a new HTML element ID and returns it
   */
  static addHtmlElement(): string {
    return `html-${uuidv4()}`;
  }

  /**
   * 🗑️ Delete Selected Operation
   * @description Returns filtered arrays without selected elements
   */
  static deleteSelected(state: CanvasState, selectedIds: string[]): Partial<CanvasState> {
    return {
      images: state.images.filter((img) => !selectedIds.includes(img.id)),
      htmlElements: state.htmlElements.filter((el) => !selectedIds.includes(el.id)),
      textElements: state.textElements.filter((text) => !selectedIds.includes(text.id)),
      selectedIds: [],
    };
  }

  /**
   * 🧹 Clear Canvas Operation
   * @description Returns empty canvas state
   */
  static clearCanvas(): Partial<CanvasState> {
    return {
      images: [],
      htmlElements: [],
      textElements: [],
      selectedIds: [],
    };
  }

  /**
   * 🔧 Set Active Tool Operation
   * @description Returns new active tool state
   */
  static setActiveTool(tool: 'select' | 'hand' | 'mask'): Partial<CanvasState> {
    return {
      activeTool: tool,
    };
  }

  /**
   * 🎯 Set Selected IDs Operation
   * @description Returns new selected IDs state
   */
  static setSelectedIds(selectedIds: string[]): Partial<CanvasState> {
    return {
      selectedIds,
    };
  }

  /**
   * 🖼️ Set Images Operation
   * @description Returns new images state
   */
  static setImages(images: CanvasImage[]): Partial<CanvasState> {
    return {
      images,
    };
  }

  /**
   * 🌐 Set HTML Elements Operation
   * @description Returns new HTML elements state
   */
  static setHtmlElements(htmlElements: CanvasHtmlElement[]): Partial<CanvasState> {
    return {
      htmlElements,
    };
  }

  /**
   * ✏️ Set Text Elements Operation
   * @description Returns new text elements state
   */
  static setTextElements(textElements: CanvasTextElement[]): Partial<CanvasState> {
    return {
      textElements,
    };
  }

  /**
   * 🐛 Toggle Debug Mode Operation
   * @description Returns toggled debug state
   */
  static toggleDebugMode(currentDebug: boolean): Partial<CanvasState> {
    return {
      debug: !currentDebug,
    };
  }

  /**
   * ↩️ Undo Operation (Stub)
   * @description Placeholder for undo functionality
   */
  static handleUndo(state: CanvasState): Partial<CanvasState> {
    // TODO: Implement proper undo functionality
    console.log('[CanvasOperationsService] Undo operation (stubbed)');
    return {};
  }

  /**
   * ↪️ Redo Operation (Stub)
   * @description Placeholder for redo functionality
   */
  static handleRedo(state: CanvasState): Partial<CanvasState> {
    // TODO: Implement proper redo functionality
    console.log('[CanvasOperationsService] Redo operation (stubbed)');
    return {};
  }

  /**
   * 📏 Calculate Center Position
   * @description Helper to calculate center position for new elements
   */
  static calculateCenterPosition(
    canvasSize: { width: number; height: number },
    position: { x: number; y: number },
    scale: number,
    elementSize: { width: number; height: number }
  ): { x: number; y: number } {
    const centerX = (canvasSize.width / 2 - position.x) / scale;
    const centerY = (canvasSize.height / 2 - position.y) / scale;

    return {
      x: centerX - elementSize.width / 2,
      y: centerY - elementSize.height / 2,
    };
  }
}
