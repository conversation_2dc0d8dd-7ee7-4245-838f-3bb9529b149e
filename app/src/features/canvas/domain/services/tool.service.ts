/**
 * 🔧 Tool Service - Pure Domain Layer
 *
 * @description Handles all tool operations with pure domain logic (no canvas-store dependency)
 * @responsibility Manage canvas tools, zoom, pan, and interactions using pure domain state
 * @ai_context This service uses pure domain state instead of canvas-store
 */

import {
  useToolDomainStore,
  DomainCanvasTool,
  type DomainCanvasPosition,
  type DomainCanvasSize,
  type DomainMaskDrawingState,
} from '../state';

/**
 * 🔧 Tool Service Class - Pure Domain Logic
 * @ai_context Centralized business logic using pure domain state
 */
export class ToolService {
  /**
   * 🎯 Tool Management
   */
  static tools = {
    /**
     * Get the currently active tool
     */
    getActive: (): DomainCanvasTool => {
      return useToolDomainStore.getState().activeTool;
    },

    /**
     * Set the active tool
     */
    setActive: (tool: DomainCanvasTool) => {
      console.log('[ToolService] Setting active tool:', tool);
      useToolDomainStore.getState().setActiveTool(tool);
    },

    /**
     * Switch to select tool
     */
    selectTool: () => {
      console.log('[ToolService] Switching to select tool');
      useToolDomainStore.getState().selectTool();
    },

    /**
     * Switch to hand/pan tool
     */
    handTool: () => {
      console.log('[ToolService] Switching to hand tool');
      useToolDomainStore.getState().handTool();
    },

    /**
     * Switch to text tool
     */
    textTool: () => {
      console.log('[ToolService] Switching to text tool');
      useToolDomainStore.getState().textTool();
    },

    /**
     * Switch to image tool
     */
    imageTool: () => {
      console.log('[ToolService] Switching to image tool');
      useToolDomainStore.getState().imageTool();
    },

    /**
     * Switch to mask tool
     */
    maskTool: () => {
      console.log('[ToolService] Switching to mask tool');
      useToolDomainStore.getState().maskTool();
    },
  };

  /**
   * 🔍 Zoom Operations
   */
  static zoom = {
    /**
     * Get current zoom scale
     */
    getScale: (): number => {
      return useToolDomainStore.getState().scale;
    },

    /**
     * Set zoom scale
     */
    setScale: (scale: number) => {
      console.log('[ToolService] Setting zoom scale:', scale);
      useToolDomainStore.getState().setScale(scale);
    },

    /**
     * Zoom in
     */
    zoomIn: () => {
      console.log('[ToolService] Zooming in');
      useToolDomainStore.getState().zoomIn();
    },

    /**
     * Zoom out
     */
    zoomOut: () => {
      console.log('[ToolService] Zooming out');
      useToolDomainStore.getState().zoomOut();
    },

    /**
     * Reset zoom to 100%
     */
    resetZoom: () => {
      console.log('[ToolService] Resetting zoom');
      useToolDomainStore.getState().resetZoom();
    },

    /**
     * Get zoom percentage
     */
    getPercentage: (): number => {
      return useToolDomainStore.getState().getZoomPercentage();
    },
  };

  /**
   * 🎭 Mask Drawing Operations
   */
  static masking = {
    /**
     * Get mask drawing state
     */
    getState: (): DomainMaskDrawingState => {
      return useToolDomainStore.getState().maskDrawing;
    },

    /**
     * Start mask drawing mode
     */
    startMaskDrawing: (targetImageId: string) => {
      console.log('[ToolService] Starting mask drawing for image:', targetImageId);
      useToolDomainStore.getState().startMaskDrawing(targetImageId);
    },

    /**
     * Stop mask drawing mode
     */
    stopMaskDrawing: () => {
      console.log('[ToolService] Stopping mask drawing');
      useToolDomainStore.getState().stopMaskDrawing();
    },

    /**
     * Cancel mask drawing
     */
    cancelMaskDrawing: () => {
      console.log('[ToolService] Cancelling mask drawing');
      useToolDomainStore.getState().stopMaskDrawing(); // Same as stop for now
    },

    /**
     * Set mask drawing state
     */
    setState: (state: Partial<DomainMaskDrawingState>) => {
      console.log('[ToolService] Setting mask drawing state:', state);
      useToolDomainStore.getState().setMaskDrawingState(state);
    },

    /**
     * Check if currently in mask drawing mode
     */
    isDrawing: (): boolean => {
      return useToolDomainStore.getState().maskDrawing.isDrawing;
    },
  };

  /**
   * 📐 Canvas State Operations
   */
  static canvas = {
    /**
     * Get canvas position
     */
    getPosition: (): DomainCanvasPosition => {
      return useToolDomainStore.getState().position;
    },

    /**
     * Set canvas position
     */
    setPosition: (position: DomainCanvasPosition) => {
      // Removed spammy position log
      useToolDomainStore.getState().setPosition(position);
    },

    /**
     * Move canvas by delta
     */
    moveCanvas: (deltaX: number, deltaY: number) => {
      // Removed spammy move log
      useToolDomainStore.getState().moveCanvas(deltaX, deltaY);
    },

    /**
     * Get canvas size
     */
    getSize: (): DomainCanvasSize => {
      return useToolDomainStore.getState().canvasSize;
    },

    /**
     * Set canvas size
     */
    setSize: (size: DomainCanvasSize) => {
      console.log('[ToolService] Setting canvas size:', size);
      useToolDomainStore.getState().setCanvasSize(size);
    },

    /**
     * Check if canvas is dragging
     */
    isDragging: (): boolean => {
      return useToolDomainStore.getState().isDragging;
    },

    /**
     * Set dragging state
     */
    setDragging: (isDragging: boolean) => {
      useToolDomainStore.getState().setDragging(isDragging);
    },

    /**
     * Get selection state
     */
    getSelection: () => {
      return useToolDomainStore.getState().selection;
    },

    /**
     * Show selection rectangle
     */
    showSelection: (x: number, y: number, width: number, height: number) => {
      console.log('[ToolService] Showing selection:', { x, y, width, height });
      useToolDomainStore.getState().showSelection(x, y, width, height);
    },

    /**
     * Hide selection rectangle
     */
    hideSelection: () => {
      console.log('[ToolService] Hiding selection');
      useToolDomainStore.getState().hideSelection();
    },
  };
}
