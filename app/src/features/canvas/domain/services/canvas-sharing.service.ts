import { CanvasSharingRepository } from '../repositories/canvas-sharing.repository';
import { SharedCanvasEntity, CanvasSharingPermission, CreateSharedCanvasSchema, UpdateSharedCanvasSchema } from '../entities/shared-canvas.entity';
import { z } from 'zod';

/**
 * 🎨 Canvas Sharing Service
 * 
 * @description Business logic for canvas sharing operations
 * @responsibility Handles canvas sharing workflows, validation, and business rules
 * @dependencies Canvas sharing repository, email service
 * @ai_context Follows the same pattern as asset sharing service
 */
export class CanvasSharingService {
  constructor(
    private canvasSharingRepository: CanvasSharingRepository,
    private emailService?: any // Will be injected for sending emails
  ) {}

  /**
   * Share a canvas with an email address
   */
  async shareCanvas(request: {
    canvasId: string;
    sharedByUserId: number;
    sharedWithEmail: string;
    permission?: CanvasSharingPermission;
    expiresAt?: Date;
    settings?: any;
  }): Promise<{
    shareId: string;
    token: string;
    shareUrl: string;
    isNewShare: boolean;
    expiresAt: Date | null;
  }> {
    // Validate input
    const validatedData = SharedCanvasEntity.validateCreateData({
      canvasId: request.canvasId,
      sharedWithEmail: request.sharedWithEmail,
      permission: request.permission || CanvasSharingPermission.VIEW,
      expiresAt: request.expiresAt,
      settings: request.settings || SharedCanvasEntity.getDefaultSettings(),
    });

    // Check if canvas is already shared with this email
    const existingShare = await this.canvasSharingRepository.isCanvasSharedWithEmail(
      request.canvasId,
      request.sharedWithEmail
    );

    if (existingShare) {
      throw new Error('Canvas is already shared with this email address');
    }

    // Generate unique token
    const token = SharedCanvasEntity.generateToken();

    // Create share record
    const shareData = {
      canvasId: validatedData.canvasId,
      permission: validatedData.permission,
      settings: validatedData.settings,
      expiresAt: validatedData.expiresAt,
      sharedByUserId: request.sharedByUserId,
      sharedWithEmail: validatedData.sharedWithEmail,
      token,
    };

    const createdShare = await this.canvasSharingRepository.create(shareData);
    const shareEntity = SharedCanvasEntity.fromPrismaSharedCanvas(createdShare);

    // Generate share URL
    const shareUrl = this.generateShareUrl(token);

    return {
      shareId: shareEntity.id,
      token: shareEntity.token,
      shareUrl,
      isNewShare: true,
      expiresAt: shareEntity.expiresAt,
    };
  }

  /**
   * Create a public share link for a canvas
   */
  async createPublicLink(request: {
    canvasId: string;
    sharedByUserId: number;
    permission?: CanvasSharingPermission;
    expiresAt?: Date;
    settings?: any;
  }): Promise<{
    shareId: string;
    token: string;
    shareUrl: string;
    expiresAt: Date | null;
  }> {
    // Validate input for public link (no email)
    const validatedData = SharedCanvasEntity.validateCreateData({
      canvasId: request.canvasId,
      sharedWithEmail: null, // Public link
      permission: request.permission || CanvasSharingPermission.VIEW,
      expiresAt: request.expiresAt,
      settings: request.settings || SharedCanvasEntity.getDefaultSettings(),
    });

    // Generate unique token
    const token = SharedCanvasEntity.generateToken();

    // Create public share
    const shareData = {
      canvasId: validatedData.canvasId,
      permission: validatedData.permission,
      settings: validatedData.settings,
      expiresAt: validatedData.expiresAt,
      sharedByUserId: request.sharedByUserId,
      sharedWithEmail: validatedData.sharedWithEmail,
      token,
    };

    const createdShare = await this.canvasSharingRepository.create(shareData);
    const shareEntity = SharedCanvasEntity.fromPrismaSharedCanvas(createdShare);

    // Generate share URL
    const shareUrl = this.generateShareUrl(token);

    return {
      shareId: shareEntity.id,
      token: shareEntity.token,
      shareUrl,
      expiresAt: shareEntity.expiresAt,
    };
  }

  /**
   * Access a shared canvas by token
   */
  async accessSharedCanvas(token: string, accessorUserId?: number): Promise<{
    sharedCanvas: SharedCanvasEntity;
    canvas: any;
    hasAccess: boolean;
    accessLevel: CanvasSharingPermission;
  }> {
    const sharedCanvas = await this.canvasSharingRepository.findByTokenWithRelations(token);

    if (!sharedCanvas) {
      throw new Error('Shared canvas not found');
    }

    const shareEntity = SharedCanvasEntity.fromPrismaSharedCanvas(sharedCanvas);

    // Check if share is valid
    if (!shareEntity.isValid()) {
      throw new Error('This share link has expired or is no longer active');
    }

    // Record access
    await this.canvasSharingRepository.recordAccess(token);

    return {
      sharedCanvas: shareEntity,
      canvas: (sharedCanvas as any).canvas, // Canvas data from relations
      hasAccess: true,
      accessLevel: shareEntity.permission,
    };
  }

  /**
   * Get all shares for a canvas
   */
  async getCanvasShares(canvasId: string): Promise<SharedCanvasEntity[]> {
    return await this.canvasSharingRepository.findByCanvasId(canvasId);
  }

  /**
   * Get all canvases shared with a user
   */
  async getSharedCanvases(userEmail: string, userId?: number): Promise<SharedCanvasEntity[]> {
    const emailShares = await this.canvasSharingRepository.findBySharedWithEmail(userEmail);
    
    if (userId) {
      const userShares = await this.canvasSharingRepository.findBySharedWithUserId(userId);
      // Combine and deduplicate
      const allShares = [...emailShares, ...userShares];
      const uniqueShares = allShares.filter((share, index, self) => 
        index === self.findIndex(s => s.id === share.id)
      );
      return uniqueShares;
    }

    return emailShares;
  }

  /**
   * Update share permissions
   */
  async updateSharePermissions(
    shareId: string,
    updates: z.infer<typeof UpdateSharedCanvasSchema>
  ): Promise<SharedCanvasEntity> {
    const validatedUpdates = SharedCanvasEntity.validateUpdateData(updates);
    return await this.canvasSharingRepository.update(shareId, validatedUpdates);
  }

  /**
   * Revoke a specific share
   */
  async revokeShare(shareId: string): Promise<void> {
    await this.canvasSharingRepository.update(shareId, { isActive: false });
  }

  /**
   * Revoke all shares for a canvas
   */
  async revokeAllCanvasShares(canvasId: string): Promise<void> {
    await this.canvasSharingRepository.revokeAllCanvasShares(canvasId);
  }

  /**
   * Get sharing statistics for a canvas
   */
  async getCanvasSharingStats(canvasId: string): Promise<{
    totalShares: number;
    totalAccess: number;
    activeShares: number;
    lastAccessed: Date | null;
  }> {
    return await this.canvasSharingRepository.getCanvasSharingStats(canvasId);
  }

  /**
   * Send share notification email
   */
  async sendShareNotification(
    recipientEmail: string,
    shareUrl: string,
    canvas: any,
    sharedByUserId: number
  ): Promise<void> {
    if (!this.emailService) {
      console.warn('Email service not configured, skipping notification');
      return;
    }

    // This will be implemented when we create the email template
    // await this.emailService.sendCanvasShareInvite({
    //   recipientEmail,
    //   shareUrl,
    //   canvas,
    //   sharedByUserId,
    // });
  }

  /**
   * Clean up expired shares
   */
  async cleanupExpiredShares(): Promise<number> {
    return await this.canvasSharingRepository.cleanupExpiredShares();
  }

  /**
   * Validate share token
   */
  async validateShareToken(token: string): Promise<{
    isValid: boolean;
    shareEntity?: SharedCanvasEntity;
    error?: string;
  }> {
    try {
      const sharedCanvas = await this.canvasSharingRepository.findByToken(token);
      
      if (!sharedCanvas) {
        return { isValid: false, error: 'Share not found' };
      }

      const shareEntity = SharedCanvasEntity.fromPrismaSharedCanvas(sharedCanvas);

      if (!shareEntity.isValid()) {
        return { isValid: false, error: 'Share has expired or is inactive' };
      }

      return { isValid: true, shareEntity };
    } catch (error) {
      return { isValid: false, error: 'Invalid share token' };
    }
  }

  /**
   * Generate share URL
   */
  private generateShareUrl(token: string): string {
    // This should use the actual frontend URL from config
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    return `${baseUrl}/canvas/shared/${token}`;
  }

  /**
   * Check if user can access canvas
   */
  async canUserAccessCanvas(canvasId: string, userId: number): Promise<{
    canAccess: boolean;
    accessLevel: CanvasSharingPermission | 'owner';
    shareEntity?: SharedCanvasEntity;
  }> {
    // First check if user owns the canvas
    // This would need to be implemented with canvas repository
    
    // Check if canvas is shared with user
    const userShares = await this.canvasSharingRepository.findBySharedWithUserId(userId);
    const canvasShare = userShares.find(share => share.canvasId === canvasId);

    if (canvasShare) {
      const shareEntity = SharedCanvasEntity.fromPrismaSharedCanvas(canvasShare);
      if (shareEntity.isValid()) {
        return {
          canAccess: true,
          accessLevel: shareEntity.permission,
          shareEntity,
        };
      }
    }

    return { canAccess: false, accessLevel: CanvasSharingPermission.VIEW };
  }
}

export default CanvasSharingService;
