/**
 * 🎨 Canvas Domain Service
 *
 * @description Core business logic for canvas operations
 * @responsibility Orchestrates canvas creation, updates, and business rules
 * @dependencies Canvas entity, repository interfaces
 * @ai_context This service contains all the business logic for canvas management
 *
 * @example
 * ```typescript
 * const service = new CanvasService(repository);
 *
 * const canvas = await service.createCanvas({
 *   name: "My Design",
 *   userId: 123,
 *   organizationId: "org_456"
 * });
 *
 * await service.addImageToCanvas(canvas.id, {
 *   src: "https://example.com/image.jpg",
 *   x: 100, y: 100, width: 200, height: 150
 * });
 * ```
 */

import {
  Canvas,
  CanvasStatus,
  CanvasToolType,
  ImageElement,
  TextElement,
  HtmlElement,
} from '../entities/canvas.entity';
import type { ICanvasRepository } from '../repositories/canvas.repository';
import { CanvasRepositoryClient } from '../../infrastructure/client/canvas.repository.client';
import { v4 as uuidv4 } from 'uuid';

/**
 * 🏗️ Canvas Creation Input
 * @ai_context Data required to create a new canvas
 */
export interface CreateCanvasInput {
  name: string;
  userId: number;
  organizationId: string;
  canvasSize?: { width: number; height: number };
  isCollaborative?: boolean;
  templateId?: string;
}

/**
 * 🔄 Canvas Update Input
 * @ai_context Data for updating canvas properties
 */
export interface UpdateCanvasInput {
  name?: string;
  canvasSize?: { width: number; height: number };
  isCollaborative?: boolean;
  status?: CanvasStatus;
}

/**
 * 🖼️ Add Image Input
 * @ai_context Data for adding image to canvas
 */
export interface AddImageInput {
  src: string;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;
  taskId?: string;
  crop?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

/**
 * 📝 Add Text Input
 * @ai_context Data for adding text to canvas
 */
export interface AddTextInput {
  text: string;
  x: number;
  y: number;
  width: number;
  height: number;
  fontSize: number;
  fontFamily: string;
  color: string;
  rotation?: number;
  fontWeight?: string;
  fontStyle?: string;
  align?: 'left' | 'center' | 'right';
}

/**
 * 🌐 Add HTML Input
 * @ai_context Data for adding HTML element to canvas
 */
export interface AddHtmlInput {
  html: string;
  x: number;
  y: number;
  width: number;
  height: number;
  css?: string;
  htmlUrl?: string;
  backgroundColor?: string;
  rotation?: number;
  taskId?: string;
}

/**
 * 🎨 Canvas Domain Service
 * @ai_context Core business logic for canvas operations following SOLID principles
 */
export class CanvasService {
  constructor(private readonly repository: ICanvasRepository = new CanvasRepositoryClient()) {}

  /**
   * �️ Create a new canvas
   * @param input Canvas creation data
   * @returns Created canvas
   */
  async createCanvas(input: CreateCanvasInput): Promise<Canvas> {
    // Validate input
    this.validateCreateInput(input);

    // Check for duplicate names within organization
    const existingCanvas = await this.repository.findByNameAndOrganization(input.name, input.organizationId);

    if (existingCanvas) {
      throw new Error(`Canvas with name "${input.name}" already exists in this organization`);
    }

    // Create canvas entity
    const canvas = Canvas.create({
      id: uuidv4(),
      name: input.name,
      userId: input.userId,
      organizationId: input.organizationId,
      canvasSize: input.canvasSize,
      isCollaborative: input.isCollaborative,
    });

    // Validate the created canvas
    const validation = canvas.validate();
    if (!validation.isValid) {
      throw new Error(`Invalid canvas data: ${validation.errors.join(', ')}`);
    }

    // Save to repository
    await this.repository.save(canvas);

    return canvas;
  }

  /**
   * 🔄 Update canvas properties
   * @param canvasId Canvas ID
   * @param input Update data
   * @returns Updated canvas
   */
  async updateCanvas(canvasId: string, input: UpdateCanvasInput): Promise<Canvas> {
    // Find existing canvas
    const canvas = await this.repository.findById(canvasId);
    if (!canvas) {
      throw new Error(`Canvas with ID ${canvasId} not found`);
    }

    // Create updated canvas (immutable update)
    const updatedCanvas = Canvas.create({
      id: canvas.id,
      name: input.name || canvas.name,
      userId: canvas.userId,
      organizationId: canvas.organizationId,
      canvasSize: input.canvasSize || canvas.canvasSize,
      isCollaborative: input.isCollaborative ?? canvas.isCollaborative,
    });

    // Validate updated canvas
    const validation = updatedCanvas.validate();
    if (!validation.isValid) {
      throw new Error(`Invalid canvas update: ${validation.errors.join(', ')}`);
    }

    // Save updated canvas
    await this.repository.save(updatedCanvas);

    return updatedCanvas;
  }

  /**
   * 🖼️ Add image to canvas
   * @param canvasId Canvas ID
   * @param input Image data
   * @returns Updated canvas
   */
  async addImageToCanvas(canvasId: string, input: AddImageInput): Promise<Canvas> {
    // Find existing canvas
    const canvas = await this.repository.findById(canvasId);
    if (!canvas) {
      throw new Error(`Canvas with ID ${canvasId} not found`);
    }

    // Validate image input
    this.validateImageInput(input);

    // Add image to canvas
    canvas.addImage({
      type: 'image',
      src: input.src,
      x: input.x,
      y: input.y,
      width: input.width,
      height: input.height,
      rotation: input.rotation || 0,
      taskId: input.taskId,
      crop: input.crop,
    });

    // Save updated canvas
    await this.repository.save(canvas);

    return canvas;
  }

  /**
   * 📝 Add text to canvas
   * @param canvasId Canvas ID
   * @param input Text data
   * @returns Updated canvas
   */
  async addTextToCanvas(canvasId: string, input: AddTextInput): Promise<Canvas> {
    // Find existing canvas
    const canvas = await this.repository.findById(canvasId);
    if (!canvas) {
      throw new Error(`Canvas with ID ${canvasId} not found`);
    }

    // Validate text input
    this.validateTextInput(input);

    // Add text to canvas
    canvas.addText({
      type: 'text',
      text: input.text,
      x: input.x,
      y: input.y,
      width: input.width,
      height: input.height,
      fontSize: input.fontSize,
      fontFamily: input.fontFamily,
      color: input.color,
      rotation: input.rotation || 0,
      fontWeight: input.fontWeight,
      fontStyle: input.fontStyle,
      align: input.align,
    });

    // Save updated canvas
    await this.repository.save(canvas);

    return canvas;
  }

  /**
   * 🌐 Add HTML element to canvas
   * @param canvasId Canvas ID
   * @param input HTML data
   * @returns Updated canvas
   */
  async addHtmlToCanvas(canvasId: string, input: AddHtmlInput): Promise<Canvas> {
    // Find existing canvas
    const canvas = await this.repository.findById(canvasId);
    if (!canvas) {
      throw new Error(`Canvas with ID ${canvasId} not found`);
    }

    // Validate HTML input
    this.validateHtmlInput(input);

    // Add HTML element to canvas
    canvas.addHtmlElement({
      type: 'html',
      html: input.html,
      x: input.x,
      y: input.y,
      width: input.width,
      height: input.height,
      css: input.css,
      htmlUrl: input.htmlUrl,
      backgroundColor: input.backgroundColor,
      rotation: input.rotation || 0,
      taskId: input.taskId,
    });

    // Save updated canvas
    await this.repository.save(canvas);

    return canvas;
  }

  /**
   * 🎯 Select tool on canvas
   * @param canvasId Canvas ID
   * @param tool Tool to select
   * @returns Updated canvas
   */
  async selectTool(canvasId: string, tool: CanvasToolType): Promise<Canvas> {
    const canvas = await this.repository.findById(canvasId);
    if (!canvas) {
      throw new Error(`Canvas with ID ${canvasId} not found`);
    }

    canvas.selectTool(tool);
    await this.repository.save(canvas);

    return canvas;
  }

  /**
   * 🎯 Select elements on canvas
   * @param canvasId Canvas ID
   * @param elementIds Element IDs to select
   * @returns Updated canvas
   */
  async selectElements(canvasId: string, elementIds: string[]): Promise<Canvas> {
    const canvas = await this.repository.findById(canvasId);
    if (!canvas) {
      throw new Error(`Canvas with ID ${canvasId} not found`);
    }

    canvas.selectElements(elementIds);
    await this.repository.save(canvas);

    return canvas;
  }

  /**
   * 🔍 Set canvas zoom
   * @param canvasId Canvas ID
   * @param scale Scale value
   * @returns Updated canvas
   */
  async setCanvasZoom(canvasId: string, scale: number): Promise<Canvas> {
    const canvas = await this.repository.findById(canvasId);
    if (!canvas) {
      throw new Error(`Canvas with ID ${canvasId} not found`);
    }

    canvas.setScale(scale);
    await this.repository.save(canvas);

    return canvas;
  }

  /**
   * 📍 Set canvas position
   * @param canvasId Canvas ID
   * @param position Position coordinates
   * @returns Updated canvas
   */
  async setCanvasPosition(canvasId: string, position: { x: number; y: number }): Promise<Canvas> {
    const canvas = await this.repository.findById(canvasId);
    if (!canvas) {
      throw new Error(`Canvas with ID ${canvasId} not found`);
    }

    canvas.setPosition(position);
    await this.repository.save(canvas);

    return canvas;
  }

  /**
   * 🗑️ Delete canvas
   * @param canvasId Canvas ID
   */
  async deleteCanvas(canvasId: string): Promise<void> {
    const canvas = await this.repository.findById(canvasId);
    if (!canvas) {
      throw new Error(`Canvas with ID ${canvasId} not found`);
    }

    await this.repository.delete(canvasId);
  }

  /**
   * ✅ Validate canvas creation input
   * @param input Creation input to validate
   */
  private validateCreateInput(input: CreateCanvasInput): void {
    if (!input.name?.trim()) {
      throw new Error('Canvas name is required');
    }

    if (input.name.length > 100) {
      throw new Error('Canvas name must be 100 characters or less');
    }

    if (!input.userId || input.userId <= 0) {
      throw new Error('Valid user ID is required');
    }

    if (!input.organizationId?.trim()) {
      throw new Error('Organization ID is required');
    }
  }

  /**
   * ✅ Validate image input
   * @param input Image input to validate
   */
  private validateImageInput(input: AddImageInput): void {
    if (!input.src?.trim()) {
      throw new Error('Image source is required');
    }

    if (input.width <= 0 || input.height <= 0) {
      throw new Error('Image dimensions must be positive');
    }
  }

  /**
   * ✅ Validate text input
   * @param input Text input to validate
   */
  private validateTextInput(input: AddTextInput): void {
    if (!input.text?.trim()) {
      throw new Error('Text content is required');
    }

    if (input.fontSize <= 0) {
      throw new Error('Font size must be positive');
    }

    if (!input.fontFamily?.trim()) {
      throw new Error('Font family is required');
    }

    if (!input.color?.trim()) {
      throw new Error('Text color is required');
    }
  }

  /**
   * ✅ Validate HTML input
   * @param input HTML input to validate
   */
  private validateHtmlInput(input: AddHtmlInput): void {
    if (!input.html?.trim()) {
      throw new Error('HTML content is required');
    }

    if (input.width <= 0 || input.height <= 0) {
      throw new Error('HTML element dimensions must be positive');
    }
  }
}
