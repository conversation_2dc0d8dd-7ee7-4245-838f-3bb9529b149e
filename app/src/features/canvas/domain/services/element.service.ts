/**
 * 🎨 Element Service - Pure Domain Layer
 *
 * @description Handles all element operations with pure domain logic (no canvas-store dependency)
 * @responsibility Manage canvas elements using pure domain state
 * @ai_context This service uses pure domain state instead of canvas-store
 */

import {
  useElementDomainStore,
  type DomainImageElement,
  type DomainTextElement,
  type DomainHtmlElement,
} from '../state';

/**
 * 🎨 Element Service Class - Pure Domain Logic
 * @ai_context Centralized business logic using pure domain state
 */
export class ElementService {
  /**
   * 🖼️ Image Element Operations
   */
  static images = {
    /**
     * Get all images from domain state
     */
    getAll: (): DomainImageElement[] => {
      return useElementDomainStore.getState().images;
    },

    /**
     * Add a new image to the canvas
     */
    add: (image: DomainImageElement) => {
      console.log('[ElementService] Adding image:', image.id);
      useElementDomainStore.getState().addImage(image);
    },

    /**
     * Update an existing image
     */
    update: (imageId: string, updates: Partial<DomainImageElement>) => {
      console.log('[ElementService] Updating image:', imageId, updates);
      useElementDomainStore.getState().updateImage(imageId, updates);
    },

    /**
     * Remove an image from the canvas
     */
    remove: (imageId: string) => {
      console.log('[ElementService] Removing image:', imageId);
      useElementDomainStore.getState().removeImage(imageId);
    },

    /**
     * Set all images (replace current images)
     */
    setAll: (images: DomainImageElement[]) => {
      console.log('[ElementService] Setting all images:', images.length);
      useElementDomainStore.getState().setImages(images);
    },
  };

  /**
   * 📝 Text Element Operations
   */
  static texts = {
    /**
     * Get all texts from domain state
     */
    getAll: (): DomainTextElement[] => {
      return useElementDomainStore.getState().texts;
    },

    /**
     * Add a new text to the canvas
     */
    add: (text: DomainTextElement) => {
      console.log('[ElementService] Adding text:', text.id);
      useElementDomainStore.getState().addText(text);
    },

    /**
     * Update an existing text
     */
    update: (textId: string, updates: Partial<DomainTextElement>) => {
      console.log('[ElementService] Updating text:', textId, updates);
      useElementDomainStore.getState().updateText(textId, updates);
    },

    /**
     * Remove a text from the canvas
     */
    remove: (textId: string) => {
      console.log('[ElementService] Removing text:', textId);
      useElementDomainStore.getState().removeText(textId);
    },

    /**
     * Set all texts (replace current texts)
     */
    setAll: (texts: DomainTextElement[]) => {
      console.log('[ElementService] Setting all texts:', texts.length);
      useElementDomainStore.getState().setTexts(texts);
    },
  };

  /**
   * 🌐 HTML Element Operations
   */
  static htmlElements = {
    /**
     * Get all HTML elements from domain state
     */
    getAll: (): DomainHtmlElement[] => {
      return useElementDomainStore.getState().htmlElements;
    },

    /**
     * Add a new HTML element to the canvas
     */
    add: (htmlElement: DomainHtmlElement) => {
      console.log('[ElementService] Adding HTML element:', htmlElement.id);
      useElementDomainStore.getState().addHtmlElement(htmlElement);
    },

    /**
     * Update an existing HTML element
     */
    update: (elementId: string, updates: Partial<DomainHtmlElement>) => {
      console.log('[ElementService] Updating HTML element:', elementId, updates);
      useElementDomainStore.getState().updateHtmlElement(elementId, updates);
    },

    /**
     * Remove an HTML element from the canvas
     */
    remove: (elementId: string) => {
      console.log('[ElementService] Removing HTML element:', elementId);
      useElementDomainStore.getState().removeHtmlElement(elementId);
    },

    /**
     * Set all HTML elements (replace current elements)
     */
    setAll: (elements: DomainHtmlElement[]) => {
      console.log('[ElementService] Setting all HTML elements:', elements.length);
      useElementDomainStore.getState().setHtmlElements(elements);
    },
  };

  /**
   * 🎯 Selection Operations
   */
  static selection = {
    /**
     * Get currently selected element IDs
     */
    getSelected: (): string[] => {
      return useElementDomainStore.getState().selectedIds;
    },

    /**
     * Select elements by IDs
     */
    select: (elementIds: string[]) => {
      console.log('[ElementService] Selecting elements:', elementIds);
      useElementDomainStore.getState().selectElements(elementIds);
    },

    /**
     * Clear all selections
     */
    clearSelection: () => {
      console.log('[ElementService] Clearing selection');
      useElementDomainStore.getState().clearSelection();
    },

    /**
     * Toggle selection of an element
     */
    toggle: (elementId: string) => {
      console.log('[ElementService] Toggling selection:', elementId);
      useElementDomainStore.getState().toggleSelection(elementId);
    },

    /**
     * Check if an element is selected
     */
    isSelected: (elementId: string): boolean => {
      return useElementDomainStore.getState().isSelected(elementId);
    },
  };

  /**
   * 🔍 Utility Operations
   */
  static utils = {
    /**
     * Get all elements (images, texts, HTML)
     */
    getAllElements: () => {
      return useElementDomainStore.getState().getAllElements();
    },

    /**
     * Get element by ID
     */
    getElementById: (id: string) => {
      return useElementDomainStore.getState().getElementById(id);
    },

    /**
     * Get element counts
     */
    getCounts: () => {
      const state = useElementDomainStore.getState();
      return {
        images: state.images.length,
        texts: state.texts.length,
        htmlElements: state.htmlElements.length,
        total: state.images.length + state.texts.length + state.htmlElements.length,
      };
    },
  };
}
