/**
 * 🎨 Canvas Domain Layer Exports
 * 
 * @description Central export point for canvas domain layer
 * @responsibility Provides clean imports for canvas domain functionality
 * @dependencies All canvas domain entities, services, repositories, and types
 * @ai_context Follows modular architecture patterns for clean separation
 */

// Entities
export * from './entities/shared-canvas.entity';

// Repositories
export * from './repositories/canvas-sharing.repository';

// Services
export * from './services/canvas-sharing.service';

// Types
export * from './types/sharing.types';

// Re-export commonly used types for convenience
export type {
  SharedCanvas,
  CanvasSharingSettings,
  ShareCanvasRequest,
  ShareCanvasResponse,
  CreatePublicLinkRequest,
  CreatePublicLinkResponse,
  AccessSharedCanvasResponse,
  CanvasSharingStats,
  ValidateTokenResponse,
  UserCanvasAccess,
  CanvasShareEmailData,
  ShareModalProps,
  ShareButtonProps,
  SharedCanvasListProps,
  CanvasAccessIndicatorProps,
  CanvasShareSummary,
} from './types/sharing.types';

// Re-export enums for convenience
export { CanvasSharingPermission } from './entities/shared-canvas.entity';

// Re-export error classes
export {
  CanvasSharingError,
  CanvasNotFoundError,
  ShareNotFoundError,
  ShareExpiredError,
  ShareAlreadyExistsError,
  InsufficientPermissionsError,
} from './types/sharing.types';

// Re-export constants
export {
  PERMISSION_LEVELS,
  DEFAULT_SHARE_SETTINGS,
} from './types/sharing.types';
