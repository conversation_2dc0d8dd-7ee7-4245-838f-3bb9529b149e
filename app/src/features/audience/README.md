# 👥 Audience Feature

## 🎯 Overview

The Audience feature manages customer personas and target audience segments for organizations. It provides AI-powered audience generation, detailed persona management, and comprehensive demographic and psychographic profiling.

## 📁 Directory Structure

```
features/audience/
├── 🧠 domain/                          # Business logic layer
│   ├── entities/                       # Domain entities
│   │   ├── audience.entity.ts          # Core audience domain model
│   │   ├── persona.entity.ts           # Persona management
│   │   ├── demographics.entity.ts      # Demographic data
│   │   └── psychographics.entity.ts    # Psychographic profiling
│   ├── services/                       # Business services
│   │   ├── audience-generator.service.ts # AI-powered audience generation
│   │   ├── persona-analyzer.service.ts  # Persona analysis logic
│   │   ├── avatar-generator.service.ts  # Avatar generation
│   │   └── audience-validator.service.ts # Data validation
│   ├── repositories/                   # Data access interfaces
│   │   └── audience.repository.ts      # Audience data operations
│   ├── events/                         # Domain events
│   │   ├── audience-created.event.ts   # Creation events
│   │   ├── audience-updated.event.ts   # Update events
│   │   └── avatar-generated.event.ts   # Avatar generation events
│   └── types.ts                        # Domain-specific types
├── 🔧 infrastructure/                  # External integrations
│   ├── wasp/                           # WASP operations
│   │   ├── actions/                    # WASP actions
│   │   │   ├── create-audience.action.ts
│   │   │   ├── update-audience.action.ts
│   │   │   ├── delete-audience.action.ts
│   │   │   ├── import-audience.action.ts
│   │   │   └── generate-avatar.action.ts
│   │   ├── queries/                    # WASP queries
│   │   │   ├── get-audiences.query.ts
│   │   │   ├── get-audience-by-id.query.ts
│   │   │   └── get-import-progress.query.ts
│   │   └── apis/                       # API endpoints
│   │       └── audience-webhook.api.ts
│   ├── ai/                             # AI integrations
│   │   ├── prompts/                    # AI prompts
│   │   │   ├── audience-generation.prompt.ts
│   │   │   ├── persona-analysis.prompt.ts
│   │   │   └── avatar-generation.prompt.ts
│   │   └── processors/                 # AI processors
│   │       ├── gemini-audience-analyzer.ts
│   │       └── claude-audience-analyzer.ts
│   ├── external/                       # External services
│   │   ├── websocket-handler.ts        # WebSocket integration
│   │   └── progress-tracker.ts         # Import progress tracking
│   └── database/                       # Database implementations
│       └── audience.repository.impl.ts
├── 🎨 presentation/                    # UI layer
│   ├── components/                     # React components
│   │   ├── audience-form/              # Main form component
│   │   ├── audience-list/              # List view
│   │   ├── audience-selector/          # Selection component
│   │   ├── persona-card/               # Persona display
│   │   ├── avatar-generator/           # Avatar generation UI
│   │   └── import-wizard/              # Import workflow
│   ├── pages/                          # Page components
│   │   ├── audience-list.page.tsx
│   │   └── audience-details.page.tsx
│   ├── hooks/                          # React hooks
│   │   ├── use-audience.hook.ts        # Audience operations
│   │   ├── use-audience-import.hook.ts # Import workflow
│   │   └── use-avatar-generation.hook.ts # Avatar generation
│   └── stores/                         # Client state
│       └── audience.store.ts           # Zustand store
├── 📚 docs/                            # Feature documentation
│   ├── ARCHITECTURE.md                 # Technical architecture
│   ├── API.md                          # API documentation
│   ├── WORKFLOWS.md                    # Business workflows
│   └── MIGRATION.md                    # Migration guide
└── index.ts                            # Feature exports
```

## 🎯 Core Capabilities

### 1. **Audience Management**
- Create, read, update, delete audience personas
- Organize by organizations and users
- Comprehensive demographic and psychographic profiling

### 2. **AI-Powered Generation**
- Automatic audience generation from brand and product data
- AI-powered persona analysis and enhancement
- Avatar generation with customizable options

### 3. **Comprehensive Profiling**
- Demographics (age, gender, location, education, income)
- Professional details (occupation, job title, seniority)
- Psychographics (goals, challenges, motivations, attitudes)
- Behavioral patterns (buying habits, media consumption)
- Lifestyle and interests

### 4. **Import & Export**
- AI-powered audience import from brand and product data
- Real-time progress tracking via WebSocket
- Batch processing capabilities

## 🔄 Business Workflows

### Audience Creation
1. User creates new audience persona
2. System initializes with default structure
3. User can manually fill or import from AI analysis
4. Avatar generation with customizable options
5. Audience is saved and made available

### AI Import Workflow
1. User selects brand kit and product for analysis
2. System analyzes brand and product data
3. AI generates multiple audience personas
4. Real-time progress updates via WebSocket
5. Generated personas are saved with avatars
6. User reviews and refines results

### Avatar Generation Workflow
1. User requests avatar generation for persona
2. System analyzes persona characteristics
3. AI generates realistic avatar based on demographics
4. Avatar is saved and associated with persona
5. Avatar can be used in photo generation workflows

## 🤖 AI Integration

### Models Used
- **Gemini 2.5 Pro**: Primary analysis model for audience generation
- **Claude Sonnet 4**: Fallback for text analysis and reasoning
- **OpenAI DALL-E**: Avatar generation and visual content

### Analysis Categories
- Demographic profiling
- Psychographic analysis
- Behavioral pattern identification
- Professional characteristic extraction
- Lifestyle and interest mapping

## 🔗 Dependencies

### Internal
- `@shared`: Common utilities and types
- `@features/brand-kits`: Brand data integration
- `@features/products`: Product data integration

### External
- Prisma: Database operations
- WASP: Framework operations
- Zod: Runtime validation
- React Hook Form: Form management
- Socket.IO: Real-time communication

## 📊 Performance Considerations

### Optimization Strategies
- Lazy loading of audience data
- Caching of AI-generated content
- Optimistic updates for better UX
- Batch processing for imports

### Monitoring
- Generation success rates
- Processing times
- Error rates by model
- User engagement metrics

## 🧪 Testing Strategy

### Unit Tests
- Domain entity validation
- Service logic testing
- Repository operations
- AI prompt validation

### Integration Tests
- WASP action/query testing
- Database operations
- AI model integration
- WebSocket communication

### E2E Tests
- Complete import workflows
- User interface interactions
- Cross-feature integration

## 🚀 Future Enhancements

### Planned Features
- Advanced audience segmentation
- Behavioral prediction models
- Integration with marketing platforms
- A/B testing capabilities

### Technical Improvements
- Performance optimizations
- Enhanced AI models
- Better error handling
- Improved caching strategies
