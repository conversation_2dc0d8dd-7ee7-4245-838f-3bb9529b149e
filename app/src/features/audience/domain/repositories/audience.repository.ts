import type { Audience, CreateAudienceInput, UpdateAudienceInput, AudienceFormData } from '../types';

/**
 * Repository interface for audience data operations
 * Defines the contract for data access without implementation details
 */
export interface AudienceRepository {
  /**
   * Find an audience by ID
   */
  findById(id: number): Promise<Audience | null>;

  /**
   * Find all audiences for an organization
   */
  findByOrganization(organizationId: string): Promise<Audience[]>;

  /**
   * Find audiences by user ID
   */
  findByUser(userId: number): Promise<Audience[]>;

  /**
   * Create a new audience
   */
  create(data: CreateAudienceInput & { userId: number }): Promise<Audience>;

  /**
   * Update an existing audience
   */
  update(id: number, data: Partial<AudienceFormData>): Promise<Audience>;

  /**
   * Delete an audience
   */
  delete(id: number): Promise<void>;

  /**
   * Check if an audience exists
   */
  exists(id: number): Promise<boolean>;

  /**
   * Count audiences for an organization
   */
  countByOrganization(organizationId: string): Promise<number>;

  /**
   * Find audiences with avatars
   */
  findWithAvatars(organizationId: string): Promise<Audience[]>;

  /**
   * Update avatar URL for an audience
   */
  updateAvatar(id: number, avatarUrl: string): Promise<Audience>;

  /**
   * Bulk create audiences (for import functionality)
   */
  bulkCreate(audiences: Array<Omit<Audience, 'id'>>): Promise<Audience[]>;

  /**
   * Search audiences by name or characteristics
   */
  search(organizationId: string, query: string): Promise<Audience[]>;
}
