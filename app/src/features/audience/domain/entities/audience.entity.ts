import { z } from 'zod';
import type { Audience, AudienceFormData, CreateAudienceInput, AudienceTier } from '../types';

// Validation schemas
export const AudienceSchema = z.object({
  id: z.number(),
  personaName: z.string().nullable().optional(),

  // Demographics
  age: z.string().nullable().optional(),
  gender: z.string().nullable().optional(),
  maritalStatus: z.string().nullable().optional(),
  numberOfChildren: z.string().nullable().optional(),
  location: z.string().nullable().optional(),
  occupation: z.string().nullable().optional(),
  jobTitle: z.string().nullable().optional(),
  annualIncome: z.string().nullable().optional(),
  educationLevel: z.string().nullable().optional(),

  // Goals and Values
  goals: z.string().nullable().optional(),
  values: z.string().nullable().optional(),
  aspirations: z.string().nullable().optional(),
  idealDay: z.string().nullable().optional(),

  // Challenges and Pain Points
  challenges: z.string().nullable().optional(),
  painPoints: z.string().nullable().optional(),
  frustrations: z.string().nullable().optional(),
  fears: z.string().nullable().optional(),

  // Sources of Information
  books: z.string().nullable().optional(),
  magazines: z.string().nullable().optional(),
  blogsWebsites: z.string().nullable().optional(),
  conferences: z.string().nullable().optional(),
  gurus: z.string().nullable().optional(),
  otherSources: z.string().nullable().optional(),

  // Objections and Roles
  possibleObjections: z.string().nullable().optional(),
  roleInPurchaseProcess: z.string().nullable().optional(),
  decisionMakingFactors: z.string().nullable().optional(),
  budgetConcerns: z.string().nullable().optional(),

  // Additional Context
  quote: z.string().nullable().optional(),
  bio: z.string().nullable().optional(),

  // System fields
  avatarUrl: z.string().nullable().optional(),
  tier: z.enum(['primary', 'secondary', 'tertiary']).nullable().optional(),
  userId: z.string(),
  organizationId: z.string(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

export const CreateAudienceSchema = z.object({
  personaName: z.string().min(1, 'Persona name is required'),
  organizationId: z.string().min(1, 'Organization ID is required'),
});

export const UpdateAudienceSchema = z.object({
  id: z.number(),
  personaName: z.string().optional(),

  // Demographics
  age: z.string().optional(),
  gender: z.string().optional(),
  maritalStatus: z.string().optional(),
  numberOfChildren: z.string().optional(),
  location: z.string().optional(),
  occupation: z.string().optional(),
  jobTitle: z.string().optional(),
  annualIncome: z.string().optional(),
  educationLevel: z.string().optional(),

  // Goals and Values
  goals: z.string().optional(),
  values: z.string().optional(),
  aspirations: z.string().optional(),
  idealDay: z.string().optional(),

  // Challenges and Pain Points
  challenges: z.string().optional(),
  painPoints: z.string().optional(),
  frustrations: z.string().optional(),
  fears: z.string().optional(),

  // Sources of Information
  books: z.string().optional(),
  magazines: z.string().optional(),
  blogsWebsites: z.string().optional(),
  conferences: z.string().optional(),
  gurus: z.string().optional(),
  otherSources: z.string().optional(),

  // Objections and Roles
  possibleObjections: z.string().optional(),
  roleInPurchaseProcess: z.string().optional(),
  decisionMakingFactors: z.string().optional(),
  budgetConcerns: z.string().optional(),

  // Additional Context
  quote: z.string().optional(),
  bio: z.string().optional(),

  // System fields
  tier: z.enum(['primary', 'secondary', 'tertiary']).optional(),
});

// Domain entity class
export class AudienceEntity {
  constructor(private data: Audience) {
    this.validate();
  }

  private validate(): void {
    AudienceSchema.parse(this.data);
  }

  // Getters
  get id(): number {
    return this.data.id;
  }

  get personaName(): string | null | undefined {
    return this.data.personaName;
  }

  get organizationId(): string {
    return this.data.organizationId;
  }

  get userId(): string {
    return this.data.userId;
  }

  get avatarUrl(): string | null | undefined {
    return this.data.avatarUrl;
  }

  // Business logic methods
  hasAvatar(): boolean {
    return !!this.data.avatarUrl;
  }

  isComplete(): boolean {
    const requiredFields = ['personaName', 'age', 'gender', 'occupation', 'location', 'goals', 'challenges'];

    return requiredFields.every((field) => {
      const value = (this.data as any)[field];
      return value && value.toString().trim() !== '';
    });
  }

  getCompletionPercentage(): number {
    const allFields = Object.keys(this.data).filter(
      (key) =>
        key !== 'id' && key !== 'userId' && key !== 'organizationId' && key !== 'createdAt' && key !== 'updatedAt'
    );

    const filledFields = allFields.filter((field) => {
      const value = (this.data as any)[field];
      return value && value.toString().trim() !== '';
    });

    return Math.round((filledFields.length / allFields.length) * 100);
  }

  getDemographicSummary(): string {
    const parts: string[] = [];
    const data = this.data as any;

    if (data.age) parts.push(`${data.age} years old`);
    if (data.gender) parts.push(data.gender);
    if (data.occupation) parts.push(data.occupation);
    if (data.location) parts.push(`from ${data.location}`);
    if (data.maritalStatus) parts.push(data.maritalStatus);

    return parts.join(', ');
  }

  getProfessionalSummary(): string {
    const parts: string[] = [];
    const data = this.data as any;

    if (data.jobTitle) parts.push(data.jobTitle);
    if (data.occupation && !data.jobTitle) parts.push(data.occupation);
    if (data.annualIncome) parts.push(`(${data.annualIncome})`);

    return parts.join(' ');
  }

  // Update methods
  updatePersonaName(name: string): AudienceEntity {
    return new AudienceEntity({
      ...this.data,
      personaName: name,
    });
  }

  updateAvatar(avatarUrl: string): AudienceEntity {
    return new AudienceEntity({
      ...this.data,
      avatarUrl,
    });
  }

  updateField(field: string, value: string): AudienceEntity {
    return new AudienceEntity({
      ...this.data,
      [field]: value,
    });
  }

  // Conversion methods
  toPlainObject(): Audience {
    return { ...this.data };
  }

  toFormData(): Record<string, string> {
    const formData: Record<string, string> = {};

    Object.keys(this.data).forEach((key) => {
      if (
        key !== 'id' &&
        key !== 'userId' &&
        key !== 'organizationId' &&
        key !== 'createdAt' &&
        key !== 'updatedAt' &&
        key !== 'avatarUrl' &&
        key !== 'tier'
      ) {
        const value = (this.data as any)[key];
        if (value !== null && value !== undefined) {
          formData[key] = value.toString();
        }
      }
    });

    return formData;
  }

  // Static factory methods
  static create(input: CreateAudienceInput & { userId: string }): AudienceEntity {
    const data: Audience = {
      id: 0, // Will be set by database
      personaName: input.personaName,

      // Demographics
      age: null,
      gender: null,
      maritalStatus: null,
      numberOfChildren: null,
      location: null,
      occupation: null,
      jobTitle: null,
      annualIncome: null,
      educationLevel: null,

      // Goals and Values
      goals: null,
      values: null,
      aspirations: null,
      idealDay: null,

      // Challenges and Pain Points
      challenges: null,
      painPoints: null,
      frustrations: null,
      fears: null,

      // Sources of Information
      books: null,
      magazines: null,
      blogsWebsites: null,
      conferences: null,
      gurus: null,
      otherSources: null,

      // Objections and Roles
      possibleObjections: null,
      roleInPurchaseProcess: null,
      decisionMakingFactors: null,
      budgetConcerns: null,

      // Additional Context
      quote: null,
      bio: null,

      // System fields
      avatarUrl: null,
      tier: undefined,
      userId: input.userId,
      organizationId: input.organizationId,
    };

    return new AudienceEntity(data);
  }

  static fromPlainObject(data: Audience): AudienceEntity {
    return new AudienceEntity(data);
  }
}
