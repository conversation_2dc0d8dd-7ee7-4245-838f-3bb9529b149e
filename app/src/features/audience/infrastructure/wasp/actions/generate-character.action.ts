import { HttpError } from 'wasp/server';
import type { GenerateCharacter } from 'wasp/server/operations';
import { FalAiProvider } from '../../../../../core/ai/providers';
import type { AvatarGenerationOptions } from '../../../domain/types';

type Args = {
  characterId: number;
  options: AvatarGenerationOptions;
};

type GenerateCharacterResult = {
  success: boolean;
  avatarUrl?: string;
  error?: string;
};

export const generateCharacter: GenerateCharacter<Args, GenerateCharacterResult> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'Not authenticated');
  }

  try {
    // Find the audience/character
    const character = await context.entities.AudienceCharacter.findUnique({
      where: { id: args.characterId },
      include: {
        audience: true,
      },
    });

    if (!character) {
      throw new HttpError(404, 'Character not found');
    }

    if (character.userId !== context.user.id) {
      throw new HttpError(403, 'Not authorized');
    }

    // Generate character using Fal.ai
    const result = await FalAiProvider.generateCharacterAvatar(character, args.options);

    if (!result.success || !result.images || result.images.length === 0) {
      throw new Error(result.error || 'Failed to generate character');
    }

    // Get the first generated image URL
    const avatarUrl = result.images[0].url;

    // Update character with new avatar URL
    await context.entities.AudienceCharacter.update({
      where: { id: character.id },
      data: { avatarUrl },
    });

    return {
      success: true,
      avatarUrl,
    };
  } catch (error) {
    console.error('Error generating character:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate character',
    };
  }
};
