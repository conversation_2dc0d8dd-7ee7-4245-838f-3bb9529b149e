import { HttpError } from 'wasp/server';
import type { GenerateCharacters } from 'wasp/server/operations';
import { FalAiProvider } from '../../../../../core/ai/providers';

type Args = {
  audienceId: number;
  count?: number; // Default to 3
};

type GenerateCharactersResult = {
  success: boolean;
  characters?: Array<{
    id: number;
    name: string;
    avatarUrl?: string;
  }>;
  error?: string;
};

export const generateCharacters: GenerateCharacters<Args, GenerateCharactersResult> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'Not authenticated');
  }

  try {
    const count = args.count || 3;

    // Find the audience
    const audience = await context.entities.Audience.findUnique({
      where: { id: args.audienceId },
    });

    if (!audience) {
      throw new HttpError(404, 'Audience not found');
    }

    if (audience.userId !== context.user.id) {
      throw new HttpError(403, 'Not authorized');
    }

    // Generate character names based on audience
    const characterNames = generateCharacterNames(audience, count);

    const createdCharacters: Array<{
      id: number;
      name: string;
      avatarUrl?: string;
    }> = [];

    // Create characters one by one
    for (let i = 0; i < count; i++) {
      const characterName = characterNames[i];

      // Create character record
      const character = await context.entities.AudienceCharacter.create({
        data: {
          name: characterName,
          age: audience.age || '25-35',
          gender: audience.gender || undefined,
          occupation: audience.occupation || undefined,
          location: audience.location || undefined,
          bio: generateCharacterBio(characterName, audience),
          audienceId: audience.id,
          userId: context.user.id,
          organizationId: audience.organizationId,
        },
      });

      // Generate avatar for this character
      try {
        const avatarResult = await FalAiProvider.generateCharacterAvatar(
          {
            ...audience,
            personaName: characterName, // Use character name instead of audience name
          },
          {
            gender: audience.gender || 'any',
            age: audience.age || '25-35',
            style: 'realistic',
            pose: 'standing',
            shot: 'half-body',
          }
        );

        if (avatarResult.success && avatarResult.images && avatarResult.images.length > 0) {
          // Update character with avatar URL
          await context.entities.AudienceCharacter.update({
            where: { id: character.id },
            data: { avatarUrl: avatarResult.images[0].url },
          });

          createdCharacters.push({
            id: character.id,
            name: character.name,
            avatarUrl: avatarResult.images[0].url,
          });
        } else {
          createdCharacters.push({
            id: character.id,
            name: character.name,
          });
        }
      } catch (avatarError) {
        console.error(`Failed to generate avatar for character ${characterName}:`, avatarError);
        createdCharacters.push({
          id: character.id,
          name: character.name,
        });
      }
    }

    return {
      success: true,
      characters: createdCharacters,
    };
  } catch (error) {
    console.error('Error generating characters:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate characters',
    };
  }
};

// Helper function to generate character names using AI
function generateCharacterNames(_audience: any, count: number): string[] {
  // This function is now deprecated - characters should be generated by AI
  // in the audience import process with proper names
  const fallbackNames: string[] = [];
  for (let i = 1; i <= count; i++) {
    fallbackNames.push(`Character ${i}`);
  }
  return fallbackNames;
}

// Helper function to extract a specific age from age range
function extractAgeFromRange(ageRange?: string | null): number | undefined {
  if (!ageRange) return undefined;

  // Extract numbers from age range like "25-35" or "30-45"
  const match = ageRange.match(/(\d+)/);
  if (match) {
    return parseInt(match[1]) + Math.floor(Math.random() * 10); // Add some variation
  }

  return undefined;
}

// Helper function to generate character bio
function generateCharacterBio(characterName: string, audience: any): string | undefined {
  const name = characterName.split(' ').pop() || characterName;
  const occupation = audience.occupation || 'professional';
  const location = audience.location || 'urban area';
  const personaName = audience.personaName || 'audience segment';

  return `${name} is a ${occupation} living in ${location}. They represent the ${personaName} audience segment with similar goals, challenges, and lifestyle patterns.`;
}
