import { HttpError } from 'wasp/server';
import type { GenerateCharacterDescription } from 'wasp/server/operations';
import { GoogleGenAI } from '@google/genai';

type Args = {
  characterId: number;
};

type CharacterDescriptionResult = {
  success: boolean;
  description?: {
    gender: string;
    age: string;
    ethnicity: string;
    hairColor: string;
    eyeColor: string;
    bodyType: string;
    clothing: string;
    personalityTraits: string[];
    occupation: string;
    lifestyle: string;
    visualDescription: string;
  };
  error?: string;
};

// Initialize Vertex AI - same as brand kit approach
const ai = new GoogleGenAI({
  vertexai: true,
  project: process.env.GOOGLE_CLOUD_PROJECT!,
  location: 'global',
});

const characterModel = 'gemini-2.5-pro-preview-06-05';

const characterGenerationConfig = {
  maxOutputTokens: 2048,
  temperature: 0.7,
  topP: 0.8,
  responseMimeType: 'application/json',
};

export const generateCharacterDescription: GenerateCharacterDescription<Args, CharacterDescriptionResult> = async (
  args,
  context
) => {
  if (!context.user) {
    throw new HttpError(401, 'Not authenticated');
  }

  try {
    // Find the character and associated audience
    const character = await context.entities.AudienceCharacter.findUnique({
      where: { id: args.characterId },
      include: {
        audience: true,
      },
    });

    if (!character) {
      throw new HttpError(404, 'Character not found');
    }

    if (character.userId !== context.user.id) {
      throw new HttpError(403, 'Not authorized');
    }

    const audience = character.audience;
    if (!audience) {
      throw new HttpError(404, 'Associated audience not found');
    }

    // Create a detailed prompt for character description generation
    const prompt = `Based on the following audience information, create a detailed character description for "${character.name}":

Audience Information:
- Persona Name: ${audience.personaName}
- Age: ${audience.age}
- Gender: ${audience.gender || 'any'}
- Occupation: ${audience.occupation}
- Location: ${audience.location}
- Income: ${(audience as any).income || 'Not specified'}
- Education: ${(audience as any).education || 'Not specified'}
- Bio: ${audience.bio || 'Not specified'}
- Goals: ${(audience as any).goals || 'Not specified'}
- Challenges: ${(audience as any).challenges || 'Not specified'}
- Values: ${(audience as any).values || 'Not specified'}
- Interests: ${(audience as any).interests || 'Not specified'}
- Lifestyle: ${(audience as any).lifestyle || 'Not specified'}

Generate a realistic character description that would fit this audience segment. The character should feel like a real person who belongs to this demographic.

Return a JSON object with the following structure:
{
  "gender": "male/female/non-binary",
  "age": "specific age or age range like '28' or '25-30'",
  "ethnicity": "caucasian/african-american/hispanic/asian/middle-eastern/mixed",
  "hairColor": "blonde/brown/black/red/gray/white",
  "eyeColor": "brown/blue/green/hazel/gray",
  "bodyType": "slim/average/athletic/curvy/plus-size",
  "clothing": "casual/business/formal/sporty/trendy/bohemian",
  "personalityTraits": ["trait1", "trait2", "trait3"],
  "occupation": "specific job title that fits the audience",
  "lifestyle": "brief description of their lifestyle",
  "visualDescription": "detailed 2-3 sentence description of their physical appearance for image generation"
}

Make sure the character feels authentic and represents the target audience accurately.`;

    // Generate character description using Gemini
    const req = {
      model: characterModel,
      contents: [
        {
          role: 'user',
          parts: [{ text: prompt }],
        },
      ],
      config: characterGenerationConfig,
    };

    const result = await ai.models.generateContent(req as any);

    if (!result?.candidates?.[0]?.content?.parts?.[0]?.text) {
      throw new Error('Invalid response from Gemini');
    }

    const responseText = result.candidates[0].content.parts[0].text;
    let characterDescription;

    try {
      characterDescription = JSON.parse(responseText);
    } catch (parseError) {
      console.error('Failed to parse Gemini response:', responseText);
      throw new Error('Failed to parse character description from AI response');
    }

    // Update character with generated description
    await context.entities.AudienceCharacter.update({
      where: { id: character.id },
      data: {
        gender: characterDescription.gender,
        age: characterDescription.age,
        ethnicity: characterDescription.ethnicity,
        hairColor: characterDescription.hairColor,
        eyeColor: characterDescription.eyeColor,
        bodyType: characterDescription.bodyType,
        clothing: characterDescription.clothing,
        occupation: characterDescription.occupation,
        lifestyle: characterDescription.lifestyle,
        visualDescription: characterDescription.visualDescription,
        personalityTraits: characterDescription.personalityTraits,
      },
    });

    return {
      success: true,
      description: characterDescription,
    };
  } catch (error) {
    console.error('Error generating character description:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate character description',
    };
  }
};
