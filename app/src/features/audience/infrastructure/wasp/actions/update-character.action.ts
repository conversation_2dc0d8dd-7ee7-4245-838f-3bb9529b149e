import { HttpError } from 'wasp/server';
import type { Update<PERSON>haracter } from 'wasp/server/operations';

type Args = {
  id: number;
  name?: string;
  age?: string;
  gender?: string;
  occupation?: string;
  location?: string;
  bio?: string;
  ethnicity?: string;
  hairColor?: string;
  eyeColor?: string;
  bodyType?: string;
  clothing?: string;
  lifestyle?: string;
  visualDescription?: string;
  personalityTraits?: string[];
};

export const updateCharacter: UpdateCharacter<Args, any> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'Not authenticated');
  }

  try {
    const { id, ...updateData } = args;

    // Find the character first to verify ownership
    const character = await context.entities.AudienceCharacter.findUnique({
      where: { id },
    });

    if (!character) {
      throw new HttpError(404, 'Character not found');
    }

    if (character.userId !== context.user.id) {
      throw new HttpError(403, 'Not authorized to update this character');
    }

    // Update the character
    const updated<PERSON>haracter = await context.entities.AudienceCharacter.update({
      where: { id },
      data: updateData,
    });

    return updatedCharacter;
  } catch (error) {
    console.error('Error updating character:', error);
    if (error instanceof HttpError) {
      throw error;
    }
    throw new HttpError(500, 'Failed to update character');
  }
};
