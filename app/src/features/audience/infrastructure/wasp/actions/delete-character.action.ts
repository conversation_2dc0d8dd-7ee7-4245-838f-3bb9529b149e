import { HttpError } from 'wasp/server';
import type { Delete<PERSON><PERSON>cter } from 'wasp/server/operations';

type Args = {
  id: number;
};

export const deleteCharacter: DeleteCharacter<Args, void> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'Not authenticated');
  }

  try {
    // Find the character
    const character = await context.entities.AudienceCharacter.findUnique({
      where: { id: args.id },
    });

    if (!character) {
      throw new HttpError(404, 'Character not found');
    }

    // Check if user has access to this character
    if (character.userId !== context.user.id) {
      throw new HttpError(403, 'Not authorized to delete this character');
    }

    // Delete the character
    await context.entities.AudienceCharacter.delete({
      where: { id: args.id },
    });
  } catch (error) {
    console.error('Error deleting character:', error);
    if (error instanceof HttpError) {
      throw error;
    }
    throw new HttpError(500, 'Failed to delete character');
  }
};
