import { type ImportAudience } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { Audience } from 'wasp/entities';
import { updateProgress, clearProgress } from '../../external/progress-tracker';
import { generativeModel } from '../../ai/processors/gemini-audience-analyzer';
import { type Prisma } from '@prisma/client';
import type { AudienceGenerationInput } from '../../../domain/types';
import { FalAiProvider } from '../../../../../core/ai/providers';
import { GoogleGenAI } from '@google/genai';

type CreateAudienceInput = Omit<Prisma.AudienceCreateInput, 'user'>;

// Initialize Vertex AI for character description generation - same as brand kit
const ai = new GoogleGenAI({
  vertexai: true,
  project: process.env.GOOGLE_CLOUD_PROJECT!,
  location: 'global',
});

const characterModel = 'gemini-2.5-pro-preview-06-05';

const characterGenerationConfig = {
  maxOutputTokens: 2048,
  temperature: 0.7,
  topP: 0.8,
  responseMimeType: 'application/json',
};

// Helper function to generate character description using AI
async function generateCharacterDescription(audience: any, characterName: string) {
  const prompt = `Based on the following audience information, create a detailed character description for "${characterName}":

Audience Information:
- Persona Name: ${audience.personaName}
- Age: ${audience.age}
- Gender: ${audience.gender || 'any'}
- Occupation: ${audience.occupation}
- Location: ${audience.location}
- Income: ${(audience as any).income || 'Not specified'}
- Education: ${(audience as any).education || 'Not specified'}
- Bio: ${audience.bio}
- Goals: ${(audience as any).goals || 'Not specified'}
- Challenges: ${(audience as any).challenges || 'Not specified'}
- Values: ${(audience as any).values || 'Not specified'}
- Interests: ${(audience as any).interests || 'Not specified'}
- Lifestyle: ${(audience as any).lifestyle || 'Not specified'}

Generate a realistic character description that would fit this audience segment. The character should feel like a real person who belongs to this demographic.

IMPORTANT: Consider the character's name when determining ethnicity. For example:
- Names like "Chen", "Wang", "Li" suggest Asian ethnicity
- Names like "Singh", "Patel", "Kumar" suggest South Asian ethnicity
- Names like "Rodriguez", "Garcia", "Martinez" suggest Hispanic ethnicity
- Names like "Johnson", "Smith", "Williams" could be any ethnicity but often Caucasian
- Names like "Washington", "Jackson", "Brown" could suggest African-American ethnicity
- Use the name as a strong indicator but also consider the location and audience demographics

Return a JSON object with the following structure:
{
  "gender": "male/female/non-binary",
  "age": "specific age or age range like '28' or '25-30'",
  "ethnicity": "caucasian/african-american/hispanic/asian/middle-eastern/mixed",
  "hairColor": "blonde/brown/black/red/gray/white",
  "eyeColor": "brown/blue/green/hazel/gray",
  "bodyType": "slim/average/athletic/curvy/plus-size",
  "clothing": "casual/business/formal/sporty/trendy/bohemian",
  "personalityTraits": ["trait1", "trait2", "trait3"],
  "occupation": "specific job title that fits the audience",
  "lifestyle": "brief description of their lifestyle",
  "visualDescription": "detailed 2-3 sentence description of their physical appearance for image generation"
}

Make sure the character feels authentic and represents the target audience accurately.`;

  try {
    const req = {
      model: characterModel,
      contents: [
        {
          role: 'user',
          parts: [{ text: prompt }],
        },
      ],
      config: characterGenerationConfig,
    };

    const result = await ai.models.generateContent(req as any);

    if (!result?.candidates?.[0]?.content?.parts?.[0]?.text) {
      throw new Error('Invalid response from Gemini');
    }

    const responseText = result.candidates[0].content.parts[0].text;
    return JSON.parse(responseText);
  } catch (error) {
    console.error('Error generating character description:', error);
    return null;
  }
}

// Note: These helper functions are deprecated and should not be used.
// All character generation should be done by AI to ensure diversity and realism.

export const importAudience: ImportAudience<AudienceGenerationInput, Audience[]> = async (args, context) => {
  const { taskId, brandKitId, productId } = args;
  console.log(`Starting audience import for taskId: ${taskId}, brandKitId: ${brandKitId}, productId: ${productId}`);

  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  if (!process.env.GOOGLE_CLOUD_PROJECT) {
    throw new HttpError(500, 'Google Cloud Project not configured');
  }

  const userId = context.user.id;

  try {
    updateProgress(taskId, 0, 'Starting analysis...');

    // Get brand kit and product details
    const brandKit = brandKitId
      ? await context.entities.BrandKit.findUnique({
          where: { id: brandKitId },
        })
      : null;

    const product = await context.entities.Product.findUnique({
      where: { id: productId },
    });

    console.log('[ImportAudience] Found brand kit:', brandKit?.id || 'none (async mode)');
    console.log('[ImportAudience] Found product:', product?.id);

    if (brandKitId && !brandKit) {
      throw new HttpError(404, `Brand kit not found with ID: ${brandKitId}`);
    }

    if (!product) {
      throw new HttpError(404, `Product not found with ID: ${productId}`);
    }

    // Prepare prompt for Gemini
    console.log('Preparing Gemini prompt...');
    const brandName = brandKit?.name || product.manufacturer || 'the brand';
    const prompt = `Analyze ${brandName}'s product ${product.name} and create 3 distinct audience personas (primary, secondary, tertiary) based on this information:

        Product Information:
        - Name: ${product.name}
        - Description: ${product.description}
        - Target Audience: ${product.targetAudience}
        - Unique Selling Proposition: ${product.usp}
        - Price: ${product.price} ${product.currency}
        - Features: ${product.features.join(', ')}

        Brand Information:
        - Brand Name: ${brandName}
        - Brand Personality: ${brandKit?.brandPersonalityText || 'Not specified'}
        - Target Emotions: ${brandKit?.targetEmotionsText || 'Not specified'}
        - Brand Values: ${brandKit?.brandValuesText || 'Not specified'}
        - Primary Audience: ${brandKit?.primaryAudienceText || 'Not specified'}
        - Primary Audience Goals: ${brandKit?.primaryAudienceGoalsText || 'Not specified'}
        - Primary Audience Challenges: ${brandKit?.primaryAudienceChallengesText || 'Not specified'}

        Create 3 distinct audience segments (primary, secondary, tertiary) that would be interested in this product. Each should represent a different demographic/psychographic segment of your target market:

        Guidelines for each audience segment:
        - personaName: Audience segment name (e.g., "Health-Conscious Professionals", "Busy Working Parents", "Budget-Conscious Students") - NOT individual names
        - age: Age range for this segment (e.g., "25-35", "35-50")
        - gender: Primary gender distribution for this segment or "Mixed"
        - maritalStatus: Typical marital status for this segment
        - numberOfChildren: Typical family size for this segment
        - location: Geographic regions where this segment is concentrated
        - occupation: Primary industries/occupations for this segment
        - jobTitle: Common job categories in this segment
        - annualIncome: Income range for this segment
        - educationLevel: Typical education level for this segment

        Goals & Values (segment-wide characteristics):
        - goals: 2-3 key life and professional goals common to this segment
        - values: Core values that drive this segment's decisions
        - aspirations: What this segment aspires to achieve
        - idealDay: Description of this segment's ideal lifestyle

        Challenges & Pain Points (segment-wide issues):
        - challenges: Primary life challenges this segment faces
        - painPoints: Specific pain points your product addresses for this segment
        - frustrations: Common frustrations in this segment's daily life
        - fears: Key fears or concerns this segment has

        Information Sources (where this segment gets info):
        - books: Types of books this segment reads
        - magazines: Publications this segment subscribes to
        - blogsWebsites: Websites and blogs this segment follows
        - conferences: Events this segment attends
        - gurus: Thought leaders this segment follows
        - otherSources: Other information sources for this segment

        Purchase Behavior (how this segment buys):
        - possibleObjections: Why this segment might hesitate to buy
        - roleInPurchaseProcess: This segment's role in buying decisions
        - decisionMakingFactors: What influences this segment's decisions
        - budgetConcerns: Budget-related concerns for this segment

        - bio: Rich description of this audience segment's characteristics, lifestyle, and behavior patterns
        - quote: A quote that represents this segment's collective mindset/attitude

        - characters: Generate 3 diverse, realistic individual characters that represent this audience segment. Each character should be a unique person with:
          - name: Realistic full name that reflects diverse ethnicities and backgrounds (e.g., "Sarah Mitchell", "Mike Rodriguez", "Hannah Chen", "Aisha Patel", "Marcus Washington", "Elena Kowalski")
          - age: Specific age within the segment's age range
          - gender: Specific gender (male/female/non-binary)
          - occupation: Specific job title within the segment's occupation category
          - location: Specific city/region within the segment's geographic area
          - bio: Rich personal bio describing this individual's background, personality, lifestyle, and what makes them unique within this audience segment

        Return the response as a JSON object with a 'personas' array containing 3 objects, each representing a distinct audience segment with 3 individual characters each.
        `;

    updateProgress(taskId, 20, 'Generating personas...');
    console.log('Calling Gemini API...');
    // Generate personas using Gemini
    const aiResult = await generativeModel.generateContent({
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
    });

    updateProgress(taskId, 40, 'Processing AI response...');
    if (!aiResult.response?.candidates?.[0]?.content?.parts?.[0]?.text) {
      console.error('Invalid Gemini response structure:', aiResult);
      throw new Error('Invalid response from Gemini');
    }

    const rawResponse = aiResult.response.candidates[0].content.parts[0].text;
    console.log('Raw Gemini response:', rawResponse);

    // Parse response
    let personas;
    try {
      const parsedResponse = JSON.parse(rawResponse);
      console.log('Parsed response:', parsedResponse);

      if (!parsedResponse.personas || !Array.isArray(parsedResponse.personas)) {
        console.error('Invalid response format - missing personas array:', parsedResponse);
        throw new Error('Invalid response format - missing personas array');
      }

      personas = parsedResponse.personas;

      if (personas.length !== 3) {
        console.error(`Expected 3 personas, got ${personas.length}`);
        throw new Error(`Expected 3 personas, got ${personas.length}`);
      }
    } catch (error: unknown) {
      console.error('JSON parsing error:', error);
      console.error('Raw response that failed parsing:', rawResponse);
      const errorMessage = error instanceof Error ? error.message : 'Unknown JSON parsing error';
      throw new Error(`Failed to parse Gemini response: ${errorMessage}`);
    }

    updateProgress(taskId, 50, 'Creating audience profiles...');
    console.log('Creating audience profiles in database...');
    // Create audiences and generate characters with AI descriptions and avatars
    const createdAudiences = await Promise.all(
      personas.map(async (personaData: CreateAudienceInput, index: number) => {
        const tiers = ['primary', 'secondary', 'tertiary'];
        const tier = tiers[index];
        updateProgress(taskId, 50 + index * 16, `Creating ${tier} audience...`);
        console.log(`Creating ${tier} audience:`, personaData);

        // Create the audience - field names now match exactly
        const audienceData = {
          // Demographics - direct mapping since field names match
          personaName: (personaData as any).personaName,
          age: (personaData as any).age,
          gender: (personaData as any).gender,
          maritalStatus: (personaData as any).maritalStatus,
          numberOfChildren: (personaData as any).numberOfChildren,
          location: (personaData as any).location,
          occupation: (personaData as any).occupation,
          jobTitle: (personaData as any).jobTitle,
          annualIncome: (personaData as any).annualIncome,
          educationLevel: (personaData as any).educationLevel,

          // Goals and Values - direct mapping
          goals: (personaData as any).goals,
          values: (personaData as any).values,
          aspirations: (personaData as any).aspirations,
          idealDay: (personaData as any).idealDay,

          // Challenges and Pain Points - direct mapping
          challenges: (personaData as any).challenges,
          painPoints: (personaData as any).painPoints,
          frustrations: (personaData as any).frustrations,
          fears: (personaData as any).fears,

          // Sources of Information - direct mapping
          books: (personaData as any).books,
          magazines: (personaData as any).magazines,
          blogsWebsites: (personaData as any).blogsWebsites,
          conferences: (personaData as any).conferences,
          gurus: (personaData as any).gurus,
          otherSources: (personaData as any).otherSources,

          // Objections and Roles - direct mapping
          possibleObjections: (personaData as any).possibleObjections,
          roleInPurchaseProcess: (personaData as any).roleInPurchaseProcess,
          decisionMakingFactors: (personaData as any).decisionMakingFactors,
          budgetConcerns: (personaData as any).budgetConcerns,

          // Context - direct mapping
          quote: (personaData as any).quote,
          bio: (personaData as any).bio,

          // Set tier based on index
          tier: tier,

          // Relationships
          user: { connect: { id: userId } },
          organization: { connect: { id: args.organizationId } },
        };

        const newAudience = await context.entities.Audience.create({
          data: audienceData,
        });
        console.log(`Created audience ${index + 1} with ID:`, newAudience.id);

        // Use AI-generated characters if available, otherwise generate generic ones
        const characters = (personaData as any).characters || [];
        updateProgress(taskId, 60 + index * 10, `Creating characters for ${tier} audience...`);

        if (Array.isArray(characters) && characters.length > 0) {
          // Use the specific characters generated by AI
          for (let charIndex = 0; charIndex < characters.length; charIndex++) {
            const characterData = characters[charIndex];
            try {
              updateProgress(taskId, 62 + index * 10 + charIndex * 2, `Creating character ${characterData.name}...`);

              // Create character record with AI-generated data
              const character = await context.entities.AudienceCharacter.create({
                data: {
                  name: characterData.name,
                  age: characterData.age || newAudience.age || '25-35',
                  gender: characterData.gender || newAudience.gender || undefined,
                  occupation: characterData.occupation || newAudience.occupation || undefined,
                  location: characterData.location || newAudience.location || undefined,
                  bio:
                    characterData.bio ||
                    `${characterData.name} represents the ${newAudience.personaName} audience segment.`,
                  audienceId: newAudience.id,
                  userId: context.user!.id,
                  organizationId: newAudience.organizationId,
                },
              });

              updateProgress(
                taskId,
                63 + index * 10 + charIndex * 2,
                `Generating AI description for ${characterData.name}...`
              );

              // Generate AI character description for appearance details
              const aiDescription = await generateCharacterDescription(newAudience, characterData.name);

              if (aiDescription) {
                // Update character with AI-generated appearance description
                await context.entities.AudienceCharacter.update({
                  where: { id: character.id },
                  data: {
                    gender: aiDescription.gender || character.gender,
                    age: aiDescription.age || character.age,
                    ethnicity: aiDescription.ethnicity,
                    hairColor: aiDescription.hairColor,
                    eyeColor: aiDescription.eyeColor,
                    bodyType: aiDescription.bodyType,
                    clothing: aiDescription.clothing,
                    occupation: aiDescription.occupation || character.occupation,
                    lifestyle: aiDescription.lifestyle,
                    visualDescription: aiDescription.visualDescription,
                    personalityTraits: aiDescription.personalityTraits || [],
                  },
                });

                updateProgress(
                  taskId,
                  64 + index * 10 + charIndex * 2,
                  `Generating avatar for ${characterData.name}...`
                );

                // Generate AI avatar using the description
                try {
                  const avatarResult = await FalAiProvider.generateCharacterAvatar(
                    {
                      ...newAudience,
                      ...aiDescription,
                      personaName: characterData.name,
                    },
                    {
                      gender: aiDescription.gender || 'any',
                      age: aiDescription.age || '25-35',
                      ethnicity: aiDescription.ethnicity || 'any',
                      hairColor: aiDescription.hairColor || 'any',
                      eyeColor: aiDescription.eyeColor || 'any',
                      bodyType: aiDescription.bodyType || 'average',
                      clothing: aiDescription.clothing || 'casual',
                      style: 'realistic',
                      pose: 'standing',
                      shot: 'half-body',
                    }
                  );

                  if (avatarResult.success && avatarResult.images && avatarResult.images.length > 0) {
                    // Update character with avatar URL
                    await context.entities.AudienceCharacter.update({
                      where: { id: character.id },
                      data: { avatarUrl: avatarResult.images[0].url },
                    });
                    console.log(`Generated avatar for character ${characterData.name}`);
                  } else {
                    console.warn(`Failed to generate avatar for character ${characterData.name}:`, avatarResult.error);
                  }
                } catch (avatarError) {
                  console.error(`Avatar generation failed for character ${characterData.name}:`, avatarError);
                }
              }

              console.log(`Successfully created character ${characterData.name} with AI description and avatar`);
            } catch (characterError) {
              console.error(`Failed to create character ${characterData.name}:`, characterError);
            }
          }
        } else {
          // This should never happen since AI should always provide characters
          console.error(`No characters provided for ${tier} audience - this is unexpected!`);
          throw new Error(`AI failed to generate characters for ${tier} audience`);
        }

        return newAudience;
      })
    );

    updateProgress(taskId, 98, 'Finalizing...');

    // Final progress update
    updateProgress(taskId, 100, 'Complete!');
    console.log('Import completed successfully');

    // Clean up progress tracking
    clearProgress(taskId);

    return createdAudiences;
  } catch (error: unknown) {
    console.error('Import failed:', error);
    // Clean up progress tracking on error
    clearProgress(taskId);
    throw new HttpError(500, error instanceof Error ? error.message : 'Failed to import audiences');
  }
};
