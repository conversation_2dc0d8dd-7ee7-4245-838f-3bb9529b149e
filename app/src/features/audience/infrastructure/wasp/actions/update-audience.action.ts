import { type UpdateAudience } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { Audience } from 'wasp/entities';
import type { UpdateAudienceInput } from '../../../domain/types';

export const updateAudience: UpdateAudience<UpdateAudienceInput, Audience> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const { id, ...data } = args;

  try {
    const updatedAudience = await context.entities.Audience.update({
      where: { id },
      data,
    });
    return updatedAudience;
  } catch (error: unknown) {
    console.error('Failed to update audience:', error);
    throw new HttpError(500, 'Failed to update audience');
  }
};
