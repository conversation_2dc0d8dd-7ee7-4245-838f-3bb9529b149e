import { type DeleteAudience } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { Audience } from 'wasp/entities';

export const deleteAudience: DeleteAudience<{ id: number }, Audience> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const { id } = args;

  try {
    // First verify the audience belongs to the user and get associated projects
    const audience = await context.entities.Audience.findFirst({
      where: {
        id,
        userId: context.user.id,
      },
      include: {
        projects: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!audience) {
      throw new HttpError(404, 'Audience not found or access denied');
    }

    // Check if audience is being used by any projects
    if (audience.projects.length > 0) {
      const projectNames = audience.projects.map((p) => p.name).join(', ');
      throw new HttpError(
        400,
        `Cannot delete audience that is being used by the following projects: ${projectNames}. Please update or remove these projects first.`
      );
    }

    const deletedAudience = await context.entities.Audience.delete({
      where: { id },
    });

    return deletedAudience;
  } catch (error: unknown) {
    console.error('Failed to delete audience:', error);
    if (error instanceof HttpError) {
      throw error;
    }
    throw new HttpError(500, 'Failed to delete audience');
  }
};
