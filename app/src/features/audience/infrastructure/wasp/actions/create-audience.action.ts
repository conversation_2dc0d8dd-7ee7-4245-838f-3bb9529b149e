import { type CreateAudience } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { Audience } from 'wasp/entities';
import { authenticateUser } from '../../../../../server/helpers';
import type { CreateAudienceInput } from '../../../domain/types';

type Input = CreateAudienceInput;

export const createAudience: CreateAudience<Input, Audience> = async ({ organizationId, personaName }, context) => {
  const currentUser = authenticateUser(context);

  try {
    const newAudience = await context.entities.Audience.create({
      data: {
        personaName,
        user: { connect: { id: currentUser.id } },
        organization: { connect: { id: organizationId } },
      },
    });
    return newAudience;
  } catch (error: unknown) {
    console.error('Failed to create audience:', error);
    throw new HttpError(500, 'Failed to create audience');
  }
};
