import { HttpError } from 'wasp/server';
import type { GetCharacters } from 'wasp/server/operations';

type Args = {
  organizationId?: string;
  audienceId?: number;
};

export const getCharacters: GetCharacters<Args, any[]> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'Not authenticated');
  }

  const whereClause: any = {
    userId: context.user.id,
  };

  // Filter by organization if provided
  if (args.organizationId) {
    whereClause.organizationId = args.organizationId;
  }

  // Filter by specific audience if provided
  if (args.audienceId) {
    whereClause.audienceId = args.audienceId;
  }

  try {
    const characters = await context.entities.AudienceCharacter.findMany({
      where: whereClause,
      include: {
        audience: {
          select: {
            id: true,
            personaName: true,
            tier: true,
            occupation: true,
            location: true,
          },
        },
      },
      orderBy: [
        { audience: { tier: 'asc' } }, // Order by audience tier first
        { createdAt: 'asc' }, // Then by creation date
      ],
    });

    return characters;
  } catch (error) {
    console.error('Error fetching characters:', error);
    throw new HttpError(500, 'Failed to fetch characters');
  }
};
