import { type GetAudiences } from 'wasp/server/operations';
import { Audience } from 'wasp/entities';
import { authenticateUser } from '../../../../../server/helpers';

export const getAudiences: GetAudiences<{ organizationId: string }, Audience[]> = async (
  { organizationId },
  context
) => {
  authenticateUser(context);

  return context.entities.Audience.findMany({
    where: { organizationId },
  });
};
