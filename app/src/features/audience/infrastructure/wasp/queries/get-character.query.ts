import { HttpError } from 'wasp/server';
import type { <PERSON>Character } from 'wasp/server/operations';

type Args = {
  id: number;
};

export const getCharacter: GetCharacter<Args, any> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'Not authenticated');
  }

  try {
    const character = await context.entities.AudienceCharacter.findUnique({
      where: { id: args.id },
      include: {
        audience: {
          select: {
            id: true,
            personaName: true,
            tier: true,
            occupation: true,
            location: true,
          },
        },
      },
    });

    if (!character) {
      throw new HttpError(404, 'Character not found');
    }

    // Check if user has access to this character
    if (character.userId !== context.user.id) {
      throw new HttpError(403, 'Not authorized to access this character');
    }

    return character;
  } catch (error) {
    console.error('Error fetching character:', error);
    if (error instanceof HttpError) {
      throw error;
    }
    throw new HttpError(500, 'Failed to fetch character');
  }
};
