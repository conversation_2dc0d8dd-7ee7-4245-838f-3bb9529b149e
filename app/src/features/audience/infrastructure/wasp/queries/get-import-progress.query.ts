import { type GetAudienceImportProgress } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { getImportProgress } from '../../external/progress-tracker';
import type { ImportProgress } from '../../../domain/types';

type GetAudienceImportProgressInput = {
  taskId: string;
};

export const getAudienceImportProgress: GetAudienceImportProgress<
  GetAudienceImportProgressInput,
  ImportProgress | null
> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'Not authorized');
  }

  const { taskId } = args;
  const progress = getImportProgress(taskId);

  if (!progress) {
    return null;
  }

  return {
    progress: progress.progress,
    message: progress.message,
    taskId,
  };
};
