// Progress tracking for audience import operations
export type ImportProgress = {
  taskId: string;
  progress: number;
  message: string;
};

const progressMap = new Map<string, ImportProgress>();

export const getImportProgress = (taskId: string): ImportProgress | undefined => {
  return progressMap.get(taskId);
};

export const updateProgress = (taskId: string, progress: number, message: string): void => {
  console.log(`Progress update - ${progress}%: ${message}`);
  progressMap.set(taskId, { taskId, progress, message });
};

export const clearProgress = (taskId: string): void => {
  progressMap.delete(taskId);
};
