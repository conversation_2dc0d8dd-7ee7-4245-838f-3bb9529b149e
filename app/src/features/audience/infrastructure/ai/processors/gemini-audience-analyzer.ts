import { GoogleGenAI } from '@google/genai';

// Initialize Vertex AI with the new SDK
const ai = new GoogleGenAI({
  vertexai: true,
  project: process.env.GOOGLE_CLOUD_PROJECT!,
  location: 'global',
});

const model = 'gemini-2.5-pro-preview-06-05';

// Set up generation config with structured output
const generationConfig = {
  maxOutputTokens: 8192,
  temperature: 0.7,
  topP: 0.8,
  responseMimeType: 'application/json',
  responseSchema: {
    type: 'OBJECT' as const,
    properties: {
      personas: {
        type: 'ARRAY' as const,
        items: {
          type: 'OBJECT' as const,
          properties: {
            // Demographics - exact field names from Audience schema
            personaName: { type: 'STRING' as const },
            age: { type: 'STRING' as const },
            gender: { type: 'STRING' as const },
            maritalStatus: { type: 'STRING' as const },
            numberOfChildren: { type: 'STRING' as const },
            location: { type: 'STRING' as const },
            occupation: { type: 'STRING' as const },
            jobTitle: { type: 'STRING' as const },
            annualIncome: { type: 'STRING' as const },
            educationLevel: { type: 'STRING' as const },

            // Goals and Values
            goals: { type: 'STRING' as const },
            values: { type: 'STRING' as const },
            aspirations: { type: 'STRING' as const },
            idealDay: { type: 'STRING' as const },

            // Challenges and Pain Points
            challenges: { type: 'STRING' as const },
            painPoints: { type: 'STRING' as const },
            frustrations: { type: 'STRING' as const },
            fears: { type: 'STRING' as const },

            // Sources of Information
            books: { type: 'STRING' as const },
            magazines: { type: 'STRING' as const },
            blogsWebsites: { type: 'STRING' as const },
            conferences: { type: 'STRING' as const },
            gurus: { type: 'STRING' as const },
            otherSources: { type: 'STRING' as const },

            // Objections and Roles
            possibleObjections: { type: 'STRING' as const },
            roleInPurchaseProcess: { type: 'STRING' as const },
            decisionMakingFactors: { type: 'STRING' as const },
            budgetConcerns: { type: 'STRING' as const },

            // Context
            quote: { type: 'STRING' as const },
            bio: { type: 'STRING' as const },

            // Characters representing this audience
            characters: {
              type: 'ARRAY' as const,
              items: {
                type: 'OBJECT' as const,
                properties: {
                  name: { type: 'STRING' as const },
                  age: { type: 'STRING' as const },
                  gender: { type: 'STRING' as const },
                  occupation: { type: 'STRING' as const },
                  location: { type: 'STRING' as const },
                  bio: { type: 'STRING' as const },
                },
                required: ['name', 'bio'],
              },
            },
          },
          required: [
            // Core required fields
            'personaName',
            'age',
            'gender',
            'location',
            'occupation',
            'jobTitle',
            'annualIncome',
            'educationLevel',
            'goals',
            'values',
            'challenges',
            'painPoints',
            'quote',
            'bio',
            'characters',
          ],
        },
      },
    },
    required: ['personas'],
  },
};

// Export function to generate content with structured output
export const generativeModel = {
  async generateContent(request: { contents: Array<{ role: string; parts: Array<{ text: string }> }> }) {
    const req = {
      model: model,
      contents: request.contents,
      config: generationConfig,
    };

    const response = await ai.models.generateContent(req as any);

    // Extract the JSON text from the response
    const jsonText = response.candidates?.[0]?.content?.parts?.[0]?.text;

    return {
      response: {
        candidates: [
          {
            content: {
              parts: [
                {
                  text: jsonText,
                },
              ],
            },
          },
        ],
      },
    };
  },
};
