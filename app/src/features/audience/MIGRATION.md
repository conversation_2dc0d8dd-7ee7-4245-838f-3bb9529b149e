# 🔄 Audience Feature Migration Guide

## 📋 Overview

This document outlines the migration of the audience functionality from the legacy client/server structure to the new modular architecture following the same pattern as brand-kits and products features.

## 🗂️ Migration Summary

**From Legacy Structure:**
- `/src/client/audience/pages/AudiencePage.tsx` → `/src/features/audience/presentation/pages/AudiencePage.tsx`
- `/src/client/audience/pages/AudienceDetailsPage.tsx` → `/src/features/audience/presentation/pages/AudienceDetailsPage.tsx`
- `/src/client/audience/components/AudienceSelector.tsx` → `/src/features/audience/presentation/components/audience-selector/AudienceSelector.tsx`
- `/src/client/audience/types.ts` → `/src/features/audience/domain/types.ts` (enhanced)
- `/src/server/actions/audience/*` → `/src/features/audience/infrastructure/wasp/actions/`
- `/src/server/queries/audience/*` → `/src/features/audience/infrastructure/wasp/queries/`
- Audience utilities from `/src/server/actions/audience/utils/` → `/src/features/audience/infrastructure/`

### New Modular Structure

```
features/audience/
├── 🧠 domain/                          # Business logic layer
│   ├── entities/
│   │   └── audience.entity.ts          # Domain entity with validation
│   ├── repositories/
│   │   └── audience.repository.ts      # Data access interface
│   └── types.ts                        # Comprehensive type definitions
├── 🔧 infrastructure/                  # External integrations
│   ├── wasp/
│   │   ├── actions/                    # WASP server actions
│   │   │   ├── create-audience.action.ts
│   │   │   ├── update-audience.action.ts
│   │   │   ├── delete-audience.action.ts
│   │   │   ├── import-audience.action.ts
│   │   │   └── generate-avatar.action.ts
│   │   └── queries/                    # WASP server queries
│   │       ├── get-audiences.query.ts
│   │       ├── get-audience-by-id.query.ts
│   │       └── get-import-progress.query.ts
│   ├── ai/
│   │   └── processors/
│   │       └── gemini-audience-analyzer.ts
│   └── external/
│       └── progress-tracker.ts         # Import progress tracking
├── 🎨 presentation/                    # UI layer
│   ├── components/
│   │   └── audience-selector/
│   │       ├── AudienceSelector.tsx
│   │       └── index.ts
│   └── pages/
│       ├── AudiencePage.tsx
│       ├── AudienceDetailsPage.tsx
│       └── index.ts
└── index.ts                            # Feature exports
```

## 🔧 Technical Changes

### 1. **WASP Configuration Updates**

**main.wasp** - Updated import paths:
```wasp
# Pages
page AudiencePage {
  authRequired: true,
  component: import AudiencePage from "@src/features/audience/presentation/pages/AudiencePage"
}

page AudienceDetailsPage {
  component: import AudienceDetailsPage from "@src/features/audience/presentation/pages/AudienceDetailsPage"
}

# Actions
action createAudience {
  fn: import { createAudience } from "@src/features/audience/infrastructure/wasp/actions/create-audience.action",
  entities: [Audience]
}

action updateAudience {
  fn: import { updateAudience } from "@src/features/audience/infrastructure/wasp/actions/update-audience.action",
  entities: [Audience]
}

action deleteAudience {
  fn: import { deleteAudience } from "@src/features/audience/infrastructure/wasp/actions/delete-audience.action",
  entities: [Audience]
}

action importAudience {
  fn: import { importAudience } from "@src/features/audience/infrastructure/wasp/actions/import-audience.action",
  entities: [Audience, BrandKit, Product]
}

action generateAvatar {
  fn: import { generateAvatar } from "@src/features/audience/infrastructure/wasp/actions/generate-avatar.action",
  entities: [Audience]
}

# Queries
query getAudiences {
  fn: import { getAudiences } from "@src/features/audience/infrastructure/wasp/queries/get-audiences.query",
  entities: [Audience]
}

query getAudience {
  fn: import { getAudience } from "@src/features/audience/infrastructure/wasp/queries/get-audience-by-id.query",
  entities: [Audience]
}

query getAudienceImportProgress {
  fn: import { getAudienceImportProgress } from "@src/features/audience/infrastructure/wasp/queries/get-import-progress.query",
  entities: [Audience]
}
```

### 2. **Enhanced Type System**

**New comprehensive types** in `/src/features/audience/domain/types.ts`:
- `Audience` - Core entity interface
- `AudienceFormData` - Form handling
- `CreateAudienceInput` / `UpdateAudienceInput` - Action inputs
- `AvatarGenerationOptions` / `AvatarGenerationResult` - Avatar generation
- `ImportProgress` / `AudienceGenerationInput` - Import workflow
- `AudienceSelectorProps` - Component props
- Domain events and repository interfaces

### 3. **Domain Entity**

**New domain entity** with business logic:
- Validation using Zod schemas
- Business methods (completion percentage, summaries)
- Immutable updates
- Factory methods for creation

### 4. **Legacy Cleanup Complete**

**All legacy files removed** - `/src/client/audience/` directory has been completely removed:
- ✅ Legacy types moved to modular structure
- ✅ Legacy components removed (using modular versions)
- ✅ All imports updated to use modular structure
- ✅ Backward compatibility types preserved in domain layer

## 🚀 Migration Benefits

### 1. **Improved Organization**
- Clear separation of concerns (domain, infrastructure, presentation)
- Consistent with other modularized features
- Better discoverability and maintainability

### 2. **Enhanced Type Safety**
- Comprehensive type definitions
- Domain entity validation
- Proper input/output typing for all operations

### 3. **Better Testing**
- Isolated business logic in domain layer
- Mockable infrastructure dependencies
- Clear component boundaries

### 4. **AI-Friendly Structure**
- Comprehensive documentation
- Clear architectural patterns
- Consistent naming conventions

## 🔄 Import Path Updates

### For New Code (Recommended)
```typescript
// Use modular imports
import { AudiencePage, AudienceSelector } from '@features/audience/presentation';
import type { Audience, AudienceFormData } from '@features/audience/domain/types';
import { createAudience, getAudiences } from '@features/audience/infrastructure';
```

### Legacy Compatibility (Removed)
```typescript
// ❌ Legacy imports no longer available - use modular imports instead
// import { AudiencePage, AudienceSelector } from '@src/client/audience';
// import type { Audience, AudienceFormData } from '@src/client/audience/types';

// ✅ Use modular imports instead
import { AudiencePage, AudienceSelector } from '@src/features/audience/presentation';
import type { Audience, AudienceFormData } from '@src/features/audience/domain/types';
```

## 🧪 Testing Strategy

### Unit Tests
- Domain entity validation and business logic
- Service layer operations
- Component rendering and interactions

### Integration Tests
- WASP action/query operations
- Database interactions
- AI service integration

### E2E Tests
- Complete audience creation workflow
- Import functionality with progress tracking
- Avatar generation process

## 📝 Migration Complete ✅

1. ✅ **Full Migration**: All code now uses modular structure
2. ✅ **Legacy Cleanup**: Legacy client/audience structure completely removed
3. ✅ **Enhanced Features**: Ready for new capabilities using modular architecture
4. ✅ **Documentation**: Migration documentation updated and comprehensive

## 🎯 **Current Status**
- **All imports**: Using modular structure (`@src/features/audience/`)
- **WASP configuration**: Points to modular structure
- **Legacy files**: Completely removed
- **Backward compatibility**: Types preserved in domain layer

## 🔗 Related Features

This migration follows the same pattern as:
- **Brand Kits**: `/src/features/brand-kits/`
- **Products**: `/src/features/products/`
- **Canvas**: `/src/features/canvas/` (in progress)

The consistent modular architecture makes it easier to understand and maintain the codebase across all features.
