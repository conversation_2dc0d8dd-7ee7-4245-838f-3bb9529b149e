import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from 'wasp/client/operations';
import { getAudience, generate<PERSON>haracter, generateCharacterDescription, getCharacter } from 'wasp/client/operations';
import { Loader2, User, ArrowLeft, RefreshCw, Palette, Eye, Users, Shirt, Save } from 'lucide-react';
import toast from 'react-hot-toast';
import type { AvatarGenerationOptions } from '../../domain/types';

const CharacterEditorPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const {
    data: character,
    isLoading,
    error,
    refetch,
  } = useQuery(getCharacter, { id: Number(id) }) as { data: any; isLoading: boolean; error: any; refetch: () => void };
  const [isGeneratingAvatar, setIsGeneratingAvatar] = useState(false);
  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Character customization options
  const [characterOptions, setCharacterOptions] = useState<AvatarGenerationOptions>({
    gender: 'any',
    age: '25-35',
    ethnicity: 'any',
    hairColor: 'any',
    eyeColor: 'any',
    bodyType: 'average',
    clothing: 'casual',
    style: 'realistic',
    pose: 'standing',
    shot: 'full-body',
  });

  // Load saved character options from character data when component mounts
  useEffect(() => {
    if (character) {
      setCharacterOptions((prev) => ({
        ...prev,
        gender: character.gender || prev.gender,
        age: character.age || prev.age,
        ethnicity: character.ethnicity || prev.ethnicity,
        hairColor: character.hairColor || prev.hairColor,
        eyeColor: character.eyeColor || prev.eyeColor,
        bodyType: character.bodyType || prev.bodyType,
        clothing: character.clothing || prev.clothing,
      }));
    }
  }, [character]);

  const saveCharacterOptions = async () => {
    if (!character) return;

    setIsSaving(true);
    try {
      // TODO: Replace with updateCharacter action when available after rebuild
      // For now, we'll simulate saving by just showing success
      console.log('Would save character options:', {
        id: character.id,
        gender: characterOptions.gender !== 'any' ? characterOptions.gender : undefined,
        age: characterOptions.age,
        ethnicity: characterOptions.ethnicity !== 'any' ? characterOptions.ethnicity : undefined,
        hairColor: characterOptions.hairColor !== 'any' ? characterOptions.hairColor : undefined,
        eyeColor: characterOptions.eyeColor !== 'any' ? characterOptions.eyeColor : undefined,
        bodyType: characterOptions.bodyType !== 'average' ? characterOptions.bodyType : undefined,
        clothing: characterOptions.clothing !== 'casual' ? characterOptions.clothing : undefined,
      });

      toast.success('Character settings saved (placeholder)');
    } catch (error) {
      console.error('Error saving character options:', error);
      toast.error('Failed to save character settings');
    } finally {
      setIsSaving(false);
    }
  };

  const handleGenerateDescription = async () => {
    if (!character) return;

    setIsGeneratingDescription(true);
    try {
      const result = await generateCharacterDescription({
        characterId: character.id,
      });

      if (result.success && result.description) {
        const desc = result.description;
        // Update character options with AI-generated description
        setCharacterOptions((prev) => ({
          ...prev,
          gender: desc.gender || prev.gender,
          age: desc.age || prev.age,
          ethnicity: desc.ethnicity || prev.ethnicity,
          hairColor: desc.hairColor || prev.hairColor,
          eyeColor: desc.eyeColor || prev.eyeColor,
          bodyType: desc.bodyType || prev.bodyType,
          clothing: desc.clothing || prev.clothing,
        }));

        toast.success('Character description generated successfully');
        // Refetch to get updated character data
        refetch();
      } else {
        throw new Error(result.error || 'Failed to generate character description');
      }
    } catch (error) {
      console.error('Error generating character description:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to generate character description');
    } finally {
      setIsGeneratingDescription(false);
    }
  };

  const handleGenerateAvatar = async () => {
    if (!character) return;

    setIsGeneratingAvatar(true);
    try {
      // First save the character options
      await saveCharacterOptions();

      // Then generate the character
      const result = await generateCharacter({
        characterId: character.id,
        options: characterOptions,
      });

      if (result.success) {
        toast.success('Character generated successfully');
        // Refetch data instead of full page reload
        refetch();
      } else {
        throw new Error(result.error || 'Failed to generate character');
      }
    } catch (error) {
      console.error('Error generating character:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to generate character');
    } finally {
      setIsGeneratingAvatar(false);
    }
  };

  const updateOption = (key: keyof AvatarGenerationOptions, value: string) => {
    setCharacterOptions((prev) => ({ ...prev, [key]: value }));
  };

  if (isLoading) {
    return (
      <div className='min-h-screen bg-[#F8F4DF] dark:bg-gray-900 flex items-center justify-center'>
        <Loader2 className='w-8 h-8 animate-spin text-[#676D50] dark:text-gray-400' />
      </div>
    );
  }

  if (error) {
    return (
      <div className='min-h-screen bg-[#F8F4DF] dark:bg-gray-900 flex items-center justify-center'>
        <div className='text-red-600 dark:text-red-400'>Error: {error.message}</div>
      </div>
    );
  }

  if (!character) {
    return (
      <div className='min-h-screen bg-[#F8F4DF] dark:bg-gray-900 flex items-center justify-center'>
        <div className='text-[#676D50] dark:text-gray-400'>Character not found</div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-[#F0EFE9] dark:bg-black text-[#1F2419] dark:text-white font-sans'>
      <div className='max-w-7xl mx-auto p-8'>
        {/* Header */}
        <div className='flex items-center justify-between mb-8 pb-6 border-b border-[#B5B178]/20 dark:border-gray-700'>
          <div className='flex items-center gap-4'>
            <button
              onClick={() => window.history.back()}
              className='p-3 hover:bg-[#F6F3E5] dark:hover:bg-gray-800 rounded-xl transition-colors'
            >
              <ArrowLeft size={20} className='text-[#676D50] dark:text-gray-400' />
            </button>
            <div>
              <h1 className='font-display text-4xl font-bold text-[#676D50] dark:text-white'>Character Editor</h1>
              <p className='text-[#676D50]/70 mt-1'>
                Customize the visual appearance for {character.name || 'this character'}
              </p>
            </div>
          </div>
          <div className='flex items-center gap-4'>
            <button
              onClick={() => (window.location.href = '/canvas')}
              className='px-6 py-3 text-[#676D50] hover:text-[#1F2419] border border-[#B5B178]/30 hover:border-[#676D50] rounded-xl transition-colors font-medium'
            >
              Use in Canvas
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
          {/* Left Column - Character Preview */}
          <div className='space-y-6'>
            {/* Character Preview */}
            <div className='bg-white dark:bg-gray-800 rounded-2xl border border-[#B5B178]/20 dark:border-gray-700 p-6 shadow-lg'>
              <h2 className='font-display text-xl font-semibold text-[#676D50] dark:text-white mb-4 flex items-center gap-2'>
                <Eye size={20} />
                Character Preview
              </h2>

              <div className='aspect-square bg-[#F6F3E5] dark:bg-gray-700 rounded-2xl flex items-center justify-center mb-6'>
                {character.avatarUrl ? (
                  <img
                    src={character.avatarUrl}
                    alt='Character preview'
                    className='w-full h-full object-cover rounded-2xl'
                  />
                ) : (
                  <div className='text-center'>
                    <User size={64} className='text-[#9EA581] mx-auto mb-4' />
                    <p className='text-[#676D50]/60'>No character generated yet</p>
                  </div>
                )}
              </div>

              <div className='space-y-3'>
                <button
                  onClick={handleGenerateDescription}
                  disabled={isGeneratingDescription}
                  className='w-full bg-[#B5B178] hover:bg-[#9EA581] text-[#F8F4DF] px-6 py-3 rounded-xl transition-colors disabled:opacity-50 font-medium shadow-sm flex items-center justify-center gap-2'
                >
                  {isGeneratingDescription ? (
                    <>
                      <Loader2 className='w-5 h-5 animate-spin' />
                      <span>Generating Description...</span>
                    </>
                  ) : (
                    <>
                      <Palette className='w-5 h-5' />
                      <span>AI Generate Appearance</span>
                    </>
                  )}
                </button>

                <button
                  onClick={saveCharacterOptions}
                  disabled={isSaving}
                  className='w-full bg-[#9EA581] hover:bg-[#849068] text-[#F8F4DF] px-6 py-3 rounded-xl transition-colors disabled:opacity-50 font-medium shadow-sm flex items-center justify-center gap-2'
                >
                  {isSaving ? (
                    <>
                      <Loader2 className='w-5 h-5 animate-spin' />
                      <span>Saving...</span>
                    </>
                  ) : (
                    <>
                      <Save className='w-5 h-5' />
                      <span>Save Settings</span>
                    </>
                  )}
                </button>

                <button
                  onClick={handleGenerateAvatar}
                  disabled={isGeneratingAvatar}
                  className='w-full bg-[#676D50] hover:bg-[#849068] text-[#F8F4DF] px-6 py-4 rounded-xl transition-colors disabled:opacity-50 font-medium shadow-sm flex items-center justify-center gap-3'
                >
                  {isGeneratingAvatar ? (
                    <>
                      <Loader2 className='w-6 h-6 animate-spin' />
                      <span>Generating Avatar...</span>
                    </>
                  ) : (
                    <>
                      <RefreshCw className='w-6 h-6' />
                      <span>{character.avatarUrl ? 'Regenerate Avatar' : 'Generate Avatar'}</span>
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* Character Details */}
            <div className='bg-white dark:bg-gray-800 rounded-2xl border border-[#B5B178]/20 dark:border-gray-700 p-6 shadow-lg'>
              <h3 className='font-display text-lg font-semibold text-[#676D50] dark:text-white mb-3 flex items-center gap-2'>
                <Users size={18} />
                Character Details
              </h3>
              <div className='space-y-3 text-sm'>
                <div>
                  <span className='text-[#676D50]/70 font-medium'>Name:</span>
                  <p className='text-[#1F2419] dark:text-white'>{character.name || 'Unnamed'}</p>
                </div>
                <div>
                  <span className='text-[#676D50]/70 font-medium'>Bio:</span>
                  <p className='text-[#1F2419] dark:text-white'>{character.bio || 'No bio available'}</p>
                </div>
                {character.visualDescription && (
                  <div>
                    <span className='text-[#676D50]/70 font-medium'>Visual Description:</span>
                    <p className='text-[#1F2419] dark:text-white'>{character.visualDescription}</p>
                  </div>
                )}
                {character.personalityTraits && character.personalityTraits.length > 0 && (
                  <div>
                    <span className='text-[#676D50]/70 font-medium'>Personality Traits:</span>
                    <div className='flex flex-wrap gap-1 mt-1'>
                      {character.personalityTraits.map((trait: string, index: number) => (
                        <span
                          key={index}
                          className='px-2 py-1 bg-[#F6F3E5] text-[#676D50] rounded-md text-xs font-medium'
                        >
                          {trait}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                {character.lifestyle && (
                  <div>
                    <span className='text-[#676D50]/70 font-medium'>Lifestyle:</span>
                    <p className='text-[#1F2419] dark:text-white'>{character.lifestyle}</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Column - Customization Options */}
          <div className='space-y-6'>
            {/* Basic Appearance */}
            <div className='bg-white dark:bg-gray-800 rounded-2xl border border-[#B5B178]/20 dark:border-gray-700 p-6 shadow-lg'>
              <h2 className='font-display text-xl font-semibold text-[#676D50] dark:text-white mb-4 flex items-center gap-2'>
                <Palette size={20} />
                Basic Appearance
              </h2>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                {/* Gender */}
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Gender</label>
                  <select
                    value={characterOptions.gender || 'any'}
                    onChange={(e) => updateOption('gender', e.target.value)}
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                  >
                    <option value='any'>Any</option>
                    <option value='male'>Male</option>
                    <option value='female'>Female</option>
                    <option value='non-binary'>Non-binary</option>
                  </select>
                </div>

                {/* Age Range */}
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Age Range</label>
                  <select
                    value={characterOptions.age || '25-35'}
                    onChange={(e) => updateOption('age', e.target.value)}
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                  >
                    <option value='18-25'>18-25</option>
                    <option value='25-35'>25-35</option>
                    <option value='35-45'>35-45</option>
                    <option value='45-55'>45-55</option>
                    <option value='55+'>55+</option>
                  </select>
                </div>

                {/* Ethnicity */}
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Ethnicity</label>
                  <select
                    value={characterOptions.ethnicity || 'any'}
                    onChange={(e) => updateOption('ethnicity', e.target.value)}
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                  >
                    <option value='any'>Any</option>
                    <option value='caucasian'>Caucasian</option>
                    <option value='african-american'>African American</option>
                    <option value='hispanic'>Hispanic</option>
                    <option value='asian'>Asian</option>
                    <option value='middle-eastern'>Middle Eastern</option>
                    <option value='mixed'>Mixed</option>
                  </select>
                </div>

                {/* Body Type */}
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Body Type</label>
                  <select
                    value={characterOptions.bodyType || 'average'}
                    onChange={(e) => updateOption('bodyType', e.target.value)}
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                  >
                    <option value='slim'>Slim</option>
                    <option value='average'>Average</option>
                    <option value='athletic'>Athletic</option>
                    <option value='curvy'>Curvy</option>
                    <option value='plus-size'>Plus Size</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Physical Features */}
            <div className='bg-white dark:bg-gray-800 rounded-2xl border border-[#B5B178]/20 dark:border-gray-700 p-6 shadow-lg'>
              <h2 className='font-display text-xl font-semibold text-[#676D50] dark:text-white mb-4 flex items-center gap-2'>
                <Eye size={20} />
                Physical Features
              </h2>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                {/* Hair Color */}
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Hair Color</label>
                  <select
                    value={characterOptions.hairColor || 'any'}
                    onChange={(e) => updateOption('hairColor', e.target.value)}
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                  >
                    <option value='any'>Any</option>
                    <option value='blonde'>Blonde</option>
                    <option value='brown'>Brown</option>
                    <option value='black'>Black</option>
                    <option value='red'>Red</option>
                    <option value='gray'>Gray</option>
                    <option value='white'>White</option>
                  </select>
                </div>

                {/* Eye Color */}
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Eye Color</label>
                  <select
                    value={characterOptions.eyeColor || 'any'}
                    onChange={(e) => updateOption('eyeColor', e.target.value)}
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                  >
                    <option value='any'>Any</option>
                    <option value='brown'>Brown</option>
                    <option value='blue'>Blue</option>
                    <option value='green'>Green</option>
                    <option value='hazel'>Hazel</option>
                    <option value='gray'>Gray</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Style & Clothing */}
            <div className='bg-white dark:bg-gray-900 rounded-2xl border border-[#B5B178]/20 dark:border-gray-600 p-6 shadow-lg'>
              <h2 className='font-display text-xl font-semibold text-[#676D50] dark:text-white mb-4 flex items-center gap-2'>
                <Shirt size={20} />
                Style & Clothing
              </h2>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                {/* Clothing Style */}
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>
                    Clothing Style
                  </label>
                  <select
                    value={characterOptions.clothing || 'casual'}
                    onChange={(e) => updateOption('clothing', e.target.value)}
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                  >
                    <option value='casual'>Casual</option>
                    <option value='business'>Business</option>
                    <option value='formal'>Formal</option>
                    <option value='sporty'>Sporty</option>
                    <option value='trendy'>Trendy</option>
                    <option value='bohemian'>Bohemian</option>
                  </select>
                </div>

                {/* Pose */}
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Pose</label>
                  <select
                    value={characterOptions.pose || 'standing'}
                    onChange={(e) => updateOption('pose', e.target.value)}
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                  >
                    <option value='standing'>Standing</option>
                    <option value='sitting'>Sitting</option>
                    <option value='walking'>Walking</option>
                    <option value='portrait'>Portrait</option>
                    <option value='action'>Action</option>
                  </select>
                </div>

                {/* Shot Type */}
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Shot Type</label>
                  <select
                    value={characterOptions.shot || 'full-body'}
                    onChange={(e) => updateOption('shot', e.target.value)}
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                  >
                    <option value='headshot'>Headshot</option>
                    <option value='half-body'>Half Body</option>
                    <option value='full-body'>Full Body</option>
                  </select>
                </div>

                {/* Style */}
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Art Style</label>
                  <select
                    value={characterOptions.style || 'realistic'}
                    onChange={(e) => updateOption('style', e.target.value)}
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                  >
                    <option value='realistic'>Realistic</option>
                    <option value='artistic'>Artistic</option>
                    <option value='cartoon'>Cartoon</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CharacterEditorPage;
