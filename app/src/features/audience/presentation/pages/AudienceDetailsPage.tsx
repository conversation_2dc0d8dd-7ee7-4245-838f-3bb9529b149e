import React from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from 'wasp/client/operations';
import { getAudience } from 'wasp/client/operations';
import { Loader2, Edit, ArrowLeft } from 'lucide-react';
import { useOnboardingStore } from '../../../../features/onboarding/domain/store';

const AudienceDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const {
    data: audience,
    isLoading,
    error,
  } = useQuery(getAudience, { id: Number(id) }) as { data: any; isLoading: boolean; error: any };
  const { pageIsLoaded } = useOnboardingStore();

  if (isLoading) {
    return (
      <div className='min-h-screen bg-[#F8F4DF] dark:bg-black flex items-center justify-center'>
        <Loader2 className='w-8 h-8 animate-spin text-[#676D50] dark:text-gray-400' />
      </div>
    );
  }

  if (error) {
    return (
      <div className='min-h-screen bg-[#F8F4DF] dark:bg-black flex items-center justify-center'>
        <div className='text-red-600 dark:text-red-400'>Error: {error.message}</div>
      </div>
    );
  }

  if (!audience) {
    return (
      <div className='min-h-screen bg-[#F8F4DF] dark:bg-gray-900 flex items-center justify-center'>
        <div className='text-[#676D50] dark:text-gray-400'>Audience not found</div>
      </div>
    );
  }

  const getInitials = (name: string) => {
    if (!name) return '';
    const names = name.split(' ');
    if (names.length > 1) {
      return `${names[0][0]}${names[names.length - 1][0]}`;
    }
    return name.substring(0, 2);
  };

  return (
    <div
      className='min-h-screen bg-[#F8F4DF] dark:bg-gray-900 text-[#1F2419] dark:text-white font-sans'
      ref={() => {
        pageIsLoaded();
      }}
    >
      <div className='max-w-7xl mx-auto p-8'>
        {/* Header */}
        <div className='flex items-center justify-between mb-8 pb-6 border-b border-[#B5B178]/20 dark:border-gray-700'>
          <div className='flex items-center gap-4'>
            <button
              onClick={() => window.history.back()}
              className='p-3 hover:bg-[#F6F3E5] dark:hover:bg-gray-800 rounded-xl transition-colors'
            >
              <ArrowLeft size={20} className='text-[#676D50] dark:text-gray-400' />
            </button>
            <h1 className='font-display text-4xl font-bold text-[#676D50] dark:text-white'>
              {audience.personaName || 'Unnamed Audience'}
            </h1>
          </div>
          <div className='flex items-center gap-4'>
            <button className='px-6 py-3 text-[#676D50] hover:text-red-600 border border-[#B5B178]/30 hover:border-red-300 rounded-xl transition-colors font-medium'>
              Delete
            </button>
            <button className='px-6 py-3 bg-[#676D50] hover:bg-[#849068] text-[#F8F4DF] rounded-xl transition-colors font-medium shadow-sm'>
              Use on Canvas
            </button>
          </div>
        </div>

        {/* Characters Section */}
        <div className='mb-8'>
          <h2 className='font-display text-2xl font-semibold text-[#676D50] dark:text-white mb-4'>Characters</h2>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {audience.characters?.map((character: any) => (
              <div
                key={character.id}
                className='bg-white dark:bg-gray-900 rounded-2xl border border-[#B5B178]/20 dark:border-gray-600 p-6 shadow-lg flex flex-col items-center text-center'
              >
                <div className='w-24 h-24 rounded-full bg-[#F6F3E5] dark:bg-gray-800 flex items-center justify-center mb-4'>
                  {character.avatarUrl ? (
                    <img
                      src={character.avatarUrl}
                      alt={character.name}
                      className='w-full h-full rounded-full object-cover'
                    />
                  ) : (
                    <span className='text-3xl font-bold text-[#676D50] dark:text-white'>
                      {getInitials(character.name)}
                    </span>
                  )}
                </div>
                <h3 className='font-display font-semibold text-lg text-[#676D50] dark:text-white'>{character.name}</h3>
                <p className='text-sm text-[#676D50]/80 dark:text-gray-400'>{character.occupation}</p>
                <p className='text-xs text-[#676D50]/60 dark:text-gray-500 mt-1'>{character.location}</p>
                <p className='text-sm mt-4 text-left'>{character.bio}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className='grid grid-cols-1 lg:grid-cols-4 gap-8'>
          {/* Left Column - Demographics */}
          <div className='lg:col-span-1 space-y-6'>
            {/* Demographic Information */}
            <div className='bg-white dark:bg-gray-800 rounded-2xl border border-[#B5B178]/20 dark:border-gray-700 p-6 shadow-lg'>
              <h3 className='font-display font-semibold text-[#676D50] dark:text-white mb-4 flex items-center gap-2'>
                <Edit size={16} className='text-[#9EA581]' />
                Audience Demographics
              </h3>
              <div className='space-y-4'>
                <div>
                  <label className='block text-xs font-medium text-[#676D50]/70 dark:text-gray-400 mb-1'>
                    Age Range
                  </label>
                  <input
                    type='text'
                    placeholder='e.g., 25-35'
                    className='w-full px-3 py-2 text-sm border border-[#B5B178]/30 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                    defaultValue={audience.age || ''}
                  />
                </div>
                <div>
                  <label className='block text-xs font-medium text-[#676D50]/70 dark:text-gray-400 mb-1'>Gender</label>
                  <input
                    type='text'
                    placeholder='e.g., Female'
                    className='w-full px-3 py-2 text-sm border border-[#B5B178]/30 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                    defaultValue={audience.gender || ''}
                  />
                </div>
                <div>
                  <label className='block text-xs font-medium text-[#676D50]/70 dark:text-gray-400 mb-1'>
                    Marital Status
                  </label>
                  <input
                    type='text'
                    placeholder='e.g., Married'
                    className='w-full px-3 py-2 text-sm border border-[#B5B178]/30 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                    defaultValue={audience.maritalStatus || ''}
                  />
                </div>
                <div>
                  <label className='block text-xs font-medium text-[#676D50]/70 dark:text-gray-400 mb-1'>
                    # of Children
                  </label>
                  <input
                    type='text'
                    placeholder='e.g., 2'
                    className='w-full px-3 py-2 text-sm border border-[#B5B178]/30 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                    defaultValue={audience.numberOfChildren || ''}
                  />
                </div>
                <div>
                  <label className='block text-xs font-medium text-[#676D50]/70 dark:text-gray-400 mb-1'>
                    Location
                  </label>
                  <input
                    type='text'
                    placeholder='e.g., Brooklyn, NY'
                    className='w-full px-3 py-2 text-sm border border-[#B5B178]/30 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                    defaultValue={audience.location || ''}
                  />
                </div>
                <div>
                  <label className='block text-xs font-medium text-[#676D50]/70 dark:text-gray-400 mb-1'>
                    Occupation
                  </label>
                  <input
                    type='text'
                    placeholder='e.g., Marketing Manager'
                    className='w-full px-3 py-2 text-sm border border-[#B5B178]/30 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                    defaultValue={audience.occupation || ''}
                  />
                </div>
                <div>
                  <label className='block text-xs font-medium text-[#676D50]/70 dark:text-gray-400 mb-1'>
                    Job Title
                  </label>
                  <input
                    type='text'
                    placeholder='e.g., Senior Marketing Manager'
                    className='w-full px-3 py-2 text-sm border border-[#B5B178]/30 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                    defaultValue={audience.jobTitle || ''}
                  />
                </div>
                <div>
                  <label className='block text-xs font-medium text-[#676D50]/70 dark:text-gray-400 mb-1'>
                    Annual Income
                  </label>
                  <input
                    type='text'
                    placeholder='e.g., $75k - $110k'
                    className='w-full px-3 py-2 text-sm border border-[#B5B178]/30 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                    defaultValue={audience.annualIncome || ''}
                  />
                </div>
                <div>
                  <label className='block text-xs font-medium text-[#676D50]/70 dark:text-gray-400 mb-1'>
                    Education Level
                  </label>
                  <input
                    type='text'
                    placeholder="e.g., Bachelor's Degree"
                    className='w-full px-3 py-2 text-sm border border-[#B5B178]/30 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white'
                    defaultValue={audience.educationLevel || ''}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Customer Avatar Sections */}
          <div className='lg:col-span-3 space-y-6'>
            {/* Goals and Values */}
            <div className='bg-white dark:bg-gray-800 rounded-2xl border border-[#B5B178]/20 dark:border-gray-700 p-6 shadow-lg'>
              <div className='flex items-center justify-between mb-4'>
                <h2 className='font-display text-xl font-semibold text-[#676D50] dark:text-white'>Goals and Values</h2>
                <Edit size={16} className='text-[#9EA581] hover:text-[#676D50] cursor-pointer transition-colors' />
              </div>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Goals</label>
                  <textarea
                    rows={4}
                    placeholder='What keeps them up at night? What do they hope to achieve?'
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.goals || ''}
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Values</label>
                  <textarea
                    rows={4}
                    placeholder='Which core values will they resonate with?'
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.values || ''}
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>
                    Aspirations
                  </label>
                  <textarea
                    rows={3}
                    placeholder='Who do they aspire to be? What do they admire?'
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.aspirations || ''}
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Ideal Day</label>
                  <textarea
                    rows={3}
                    placeholder='What would their ideal day look like?'
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.idealDay || ''}
                  />
                </div>
              </div>
            </div>

            {/* Challenges and Pain Points */}
            <div className='bg-white dark:bg-gray-900 rounded-2xl border border-[#B5B178]/20 dark:border-gray-600 p-6 shadow-lg'>
              <div className='flex items-center justify-between mb-4'>
                <h2 className='font-display text-xl font-semibold text-[#676D50] dark:text-white'>
                  Challenges and Pain Points
                </h2>
                <Edit size={16} className='text-[#9EA581] hover:text-[#676D50] cursor-pointer transition-colors' />
              </div>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Challenges</label>
                  <textarea
                    rows={4}
                    placeholder='What really annoys them? What are their biggest challenges?'
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.challenges || ''}
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>
                    Pain Points
                  </label>
                  <textarea
                    rows={4}
                    placeholder='What are the definite deal killers? How do they feel?'
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.painPoints || ''}
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>
                    Frustrations
                  </label>
                  <textarea
                    rows={3}
                    placeholder='What frustrates them on a daily basis?'
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.frustrations || ''}
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Fears</label>
                  <textarea
                    rows={3}
                    placeholder='What are they afraid of? What keeps them worried?'
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.fears || ''}
                  />
                </div>
              </div>
            </div>

            {/* Sources of Information */}
            <div className='bg-white dark:bg-gray-800 rounded-2xl border border-[#B5B178]/20 dark:border-gray-700 p-6 shadow-lg'>
              <div className='flex items-center justify-between mb-4'>
                <h2 className='font-display text-xl font-semibold text-[#676D50] dark:text-white'>
                  Sources of Information
                </h2>
                <Edit size={16} className='text-[#9EA581] hover:text-[#676D50] cursor-pointer transition-colors' />
              </div>
              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Books</label>
                  <textarea
                    rows={3}
                    placeholder="What books do they read that others wouldn't?"
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.books || ''}
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Magazines</label>
                  <textarea
                    rows={3}
                    placeholder='Which magazines do they subscribe to?'
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.magazines || ''}
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>
                    Blogs/Websites
                  </label>
                  <textarea
                    rows={3}
                    placeholder='What websites do they visit regularly?'
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.blogsWebsites || ''}
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>
                    Conferences
                  </label>
                  <textarea
                    rows={3}
                    placeholder='Which conferences do they attend?'
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.conferences || ''}
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Gurus</label>
                  <textarea
                    rows={3}
                    placeholder="Who do they follow that others wouldn't?"
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.gurus || ''}
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>
                    Other Sources
                  </label>
                  <textarea
                    rows={3}
                    placeholder='Podcasts, YouTube channels, etc.'
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.otherSources || ''}
                  />
                </div>
              </div>
            </div>

            {/* Objections and Roles */}
            <div className='bg-white dark:bg-gray-900 rounded-2xl border border-[#B5B178]/20 dark:border-gray-600 p-6 shadow-lg'>
              <div className='flex items-center justify-between mb-4'>
                <h2 className='font-display text-xl font-semibold text-[#676D50] dark:text-white'>
                  Objections and Roles
                </h2>
                <Edit size={16} className='text-[#9EA581] hover:text-[#676D50] cursor-pointer transition-colors' />
              </div>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>
                    Possible Objections
                  </label>
                  <textarea
                    rows={4}
                    placeholder='What concerns do they have? Why would they choose NOT to buy?'
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.possibleObjections || ''}
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>
                    Role in Purchase Process
                  </label>
                  <textarea
                    rows={4}
                    placeholder='Are they the decision maker? Do they need approval?'
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.roleInPurchaseProcess || ''}
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>
                    Decision Making Factors
                  </label>
                  <textarea
                    rows={3}
                    placeholder='What influences their buying decisions?'
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.decisionMakingFactors || ''}
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>
                    Budget Concerns
                  </label>
                  <textarea
                    rows={3}
                    placeholder='Is time, money or other resources an issue?'
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.budgetConcerns || ''}
                  />
                </div>
              </div>
            </div>

            {/* Quote & Bio */}
            <div className='bg-white dark:bg-gray-800 rounded-2xl border border-[#B5B178]/20 dark:border-gray-700 p-6 shadow-lg'>
              <div className='flex items-center justify-between mb-4'>
                <h2 className='font-display text-xl font-semibold text-[#676D50] dark:text-white'>Quote & Bio</h2>
                <Edit size={16} className='text-[#9EA581] hover:text-[#676D50] cursor-pointer transition-colors' />
              </div>
              <div className='grid grid-cols-1 gap-6'>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Quote</label>
                  <textarea
                    rows={2}
                    placeholder='A quote that represents their mindset...'
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.quote || ''}
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>
                    Bio Summary
                  </label>
                  <textarea
                    rows={4}
                    placeholder='A brief summary that brings this avatar to life...'
                    className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-700 text-[#1F2419] dark:text-white placeholder-[#676D50]/50'
                    defaultValue={audience.bio || ''}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudienceDetailsPage;
