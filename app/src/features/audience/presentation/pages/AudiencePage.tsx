import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getAudiences, getCharacters, createAudience, deleteAudience, useQuery } from 'wasp/client/operations';

import { Link } from 'react-router-dom';
import { Plus, User, X, Trash2 } from 'lucide-react';
import toast from 'react-hot-toast';
import type { Audience } from '../../domain/types';

import { useOrganizationState } from '../../../../organization/store';
import { useTourNavigation } from '../../../../features/onboarding/infrastructure/hooks/useTourNavigation';
import { Card, Button, Input, Label, FormGroup } from '../../../../shared';
import { ActiveSessionChecker } from '../../../onboarding/infrastructure/hooks/useActiveSessionRedirect';

const AudiencePage = () => {
  const navigate = useNavigate();
  const { selectedOrganizationId } = useOrganizationState();
  const [showNewAudienceModal, setShowNewAudienceModal] = useState(false);
  const [newAudienceName, setNewAudienceName] = useState('');
  const [selectedTier, setSelectedTier] = useState<'primary' | 'secondary' | 'tertiary'>('primary');
  const [isCreatingAudience, setIsCreatingAudience] = useState(false);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const { handleTourStep } = useTourNavigation();

  // ✅ Good: Use WASP's useQuery instead of useEffect for data fetching
  const { data: audiences = [], refetch: refetchAudiences } = useQuery(
    getAudiences,
    { organizationId: selectedOrganizationId! },
    { enabled: !!selectedOrganizationId }
  ) as { data: Audience[]; refetch: () => void };

  // Fetch characters for the organization
  const { data: characters = [], refetch: refetchCharacters } = useQuery(
    getCharacters,
    { organizationId: selectedOrganizationId! },
    { enabled: !!selectedOrganizationId }
  ) as { data: any[]; refetch: () => void };

  // Helper function to get characters by audience tier
  const getCharactersByTier = (tier: string) => {
    return characters.filter(
      (character) => character.audience?.tier === tier || (!character.audience?.tier && tier === 'primary')
    );
  };

  // Helper function to get audience by tier
  const getAudienceByTier = (tier: string) => {
    return audiences.find((audience) => audience.tier === tier || (!audience.tier && tier === 'primary'));
  };

  const openNewAudienceModal = () => {
    handleTourStep();
    setShowNewAudienceModal(true);
  };
  const closeNewAudienceModal = () => setShowNewAudienceModal(false);

  const handleCreateAudience = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsCreatingAudience(true);
    try {
      const createdAudience = await createAudience({
        personaName: newAudienceName,
        organizationId: selectedOrganizationId!,
      });
      // ✅ Good: Refetch data instead of manually updating state
      refetchAudiences();
      refetchCharacters();
      closeNewAudienceModal();
      setNewAudienceName('');
      handleTourStep();
      navigate(`/audiences/${createdAudience.id}`);
      toast.success('Audience created successfully');
    } catch (error) {
      console.error('Error creating audience:', error);
      toast.error('Failed to create audience');
    } finally {
      setIsCreatingAudience(false);
    }
  };

  const handleDeleteAudience = async (id: number, personaName: string) => {
    if (!confirm(`Are you sure you want to delete "${personaName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deleteAudience({ id });
      // ✅ Good: Refetch data instead of manually updating state
      refetchAudiences();
      refetchCharacters();
      toast.success('Audience deleted successfully');
    } catch (error: any) {
      console.error('Failed to delete audience:', error);
      toast.error(error.message || 'Failed to delete audience', {
        duration: 5000,
        style: {
          maxWidth: '500px',
          padding: '16px',
          lineHeight: '1.5',
        },
      });
    }
  };

  const handleDeleteCharacter = async (id: number, characterName: string) => {
    if (!confirm(`Are you sure you want to delete "${characterName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      // For now, we'll use a placeholder since the action isn't built yet
      // TODO: Replace with actual deleteCharacter action when available
      console.log('Would delete character:', id);
      toast.success('Character deleted successfully');
      refetchCharacters();
    } catch (error: any) {
      console.error('Failed to delete character:', error);
      toast.error(error.message || 'Failed to delete character', {
        duration: 5000,
        style: {
          maxWidth: '500px',
          padding: '16px',
          lineHeight: '1.5',
        },
      });
    }
  };

  const handleDeleteAudienceTier = async (tier: string) => {
    const tierAudiences = audiences.filter(
      (audience) => audience.tier === tier || (!audience.tier && tier === 'primary')
    );
    const tierName = tier.charAt(0).toUpperCase() + tier.slice(1);

    if (tierAudiences.length === 0) {
      toast.error(`No ${tierName} audience to delete`);
      return;
    }

    if (
      !confirm(
        `Are you sure you want to delete the entire ${tierName} audience? This will delete ${tierAudiences.length} audience(s) and all their characters. This action cannot be undone.`
      )
    ) {
      return;
    }

    try {
      // Delete all audiences in this tier
      for (const audience of tierAudiences) {
        await deleteAudience({ id: audience.id });
      }

      refetchAudiences();
      refetchCharacters();
      toast.success(`${tierName} audience deleted successfully`);
    } catch (error: any) {
      console.error(`Failed to delete ${tierName} audience:`, error);
      toast.error(error.message || `Failed to delete ${tierName} audience`, {
        duration: 5000,
        style: {
          maxWidth: '500px',
          padding: '16px',
          lineHeight: '1.5',
        },
      });
    }
  };

  return (
    <ActiveSessionChecker>
      <div className='min-h-screen bg-[#F0EFE9] dark:bg-black text-[#1F2419] dark:text-white font-sans p-8'>
        <div className='max-w-7xl mx-auto'>
          {/* Header */}
          <div className='flex justify-between items-center mb-8'>
            <h1 className='font-display text-4xl font-bold text-[#676D50] dark:text-white'>Audiences</h1>
            <Button
              id='step-1'
              onClick={openNewAudienceModal}
              variant='primary'
              size='lg'
              icon={<Plus size={18} />}
              iconPosition='left'
            >
              Create Audience
            </Button>
          </div>

          {/* Primary Audience Section */}
          <div className='mb-12'>
            <div className='flex items-center gap-3 mb-6'>
              <div className='w-8 h-8 bg-[#676D50] rounded-full flex items-center justify-center'>
                <span className='text-[#F8F4DF] font-bold text-sm'>🎯</span>
              </div>
              <h2 className='font-display text-2xl font-bold text-[#676D50]'>Primary Audience</h2>
              <span className='text-[#676D50]/60 text-sm'>Main target demographic</span>
            </div>

            {/* Primary Audience Card */}
            {getAudienceByTier('primary') ? (
              <Card variant='default' className='mb-4'>
                <Card.Content>
                  <div className='flex items-center justify-between mb-4'>
                    <div>
                      <Card.Title level={3}>
                        {getAudienceByTier('primary')?.personaName || 'Primary Audience'}
                      </Card.Title>
                      <p className='text-[#676D50]/80 text-sm'>
                        {getAudienceByTier('primary')?.bio?.slice(0, 100) || 'Main target demographic for your brand'}
                        ...
                      </p>
                    </div>
                    <div className='flex gap-2'>
                      <Link
                        to={`/audiences/${getAudienceByTier('primary')?.id}`}
                        className='px-4 py-2 text-sm text-[#F8F4DF] bg-[#676D50] hover:bg-[#849068] rounded-lg transition-colors font-medium'
                      >
                        Edit Details
                      </Link>
                      <button
                        onClick={() => handleDeleteAudienceTier('primary')}
                        className='px-4 py-2 text-sm text-white bg-red-500 hover:bg-red-600 rounded-lg transition-colors font-medium'
                        title='Delete entire Primary audience and all characters'
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>

                  {/* Character Avatars */}
                  <div className='grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3'>
                    {getCharactersByTier('primary')
                      .slice(0, 6)
                      .map((character, index) => (
                        <div key={character.id} className='text-center group cursor-pointer relative'>
                          <Link to={`/characters/${character.id}`} className='block'>
                            <div className='w-20 h-20 rounded-full overflow-hidden mb-2 mx-auto border-2 border-[#B5B178]/20 group-hover:border-[#676D50]/50 transition-colors'>
                              {character.avatarUrl ? (
                                <img
                                  src={character.avatarUrl}
                                  alt={character.name}
                                  className='w-full h-full object-cover group-hover:scale-105 transition-transform'
                                />
                              ) : (
                                <div className='w-full h-full bg-[#F6F3E5] dark:bg-gray-700 flex items-center justify-center group-hover:bg-[#676D50]/10 transition-colors'>
                                  <User
                                    size={24}
                                    className='text-[#9EA581] group-hover:text-[#676D50] transition-colors'
                                  />
                                </div>
                              )}
                            </div>
                            <p className='text-xs text-[#676D50] font-medium group-hover:text-[#1F2419] transition-colors'>
                              {character.name || `Character ${index + 1}`}
                            </p>
                          </Link>

                          {/* Delete Button */}
                          <button
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleDeleteCharacter(character.id, character.name || `Character ${index + 1}`);
                            }}
                            className='absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity shadow-sm'
                            title='Delete character'
                          >
                            <X size={12} />
                          </button>
                        </div>
                      ))}

                    {/* Add Character Button */}
                    {getCharactersByTier('primary').length < 6 && (
                      <div onClick={openNewAudienceModal} className='text-center cursor-pointer group'>
                        <div className='w-20 h-20 rounded-full border-2 border-dashed border-[#9EA581]/30 flex items-center justify-center mb-2 mx-auto group-hover:border-[#676D50]/50 transition-colors'>
                          <Plus size={24} className='text-[#9EA581] group-hover:text-[#676D50] transition-colors' />
                        </div>
                        <p className='text-xs text-[#676D50]/60 font-medium'>Add Character</p>
                      </div>
                    )}
                  </div>
                </Card.Content>
              </Card>
            ) : (
              <div
                onClick={openNewAudienceModal}
                className='bg-[#F6F3E5] dark:bg-gray-900 rounded-2xl border-2 border-dashed border-[#9EA581]/30 flex items-center justify-center h-32 transition-all duration-300 hover:border-[#676D50]/50 cursor-pointer'
              >
                <div className='text-center'>
                  <Plus size={32} className='text-[#9EA581] mx-auto mb-2' />
                  <p className='text-[#676D50] font-medium'>Create Primary Audience</p>
                </div>
              </div>
            )}
          </div>

          {/* Secondary Audience Section */}
          <div className='mb-12'>
            <div className='flex items-center gap-3 mb-6'>
              <div className='w-8 h-8 bg-[#849068] rounded-full flex items-center justify-center'>
                <span className='text-[#F8F4DF] font-bold text-sm'>🥈</span>
              </div>
              <h2 className='font-display text-2xl font-bold text-[#676D50]'>Secondary Audience</h2>
              <span className='text-[#676D50]/60 text-sm'>Secondary target demographic</span>
            </div>

            {/* Secondary Audience Card */}
            {audiences.filter((audience) => audience.tier === 'secondary').length > 0 ? (
              <Card variant='default' className='mb-4'>
                <Card.Content>
                  <div className='flex items-center justify-between mb-4'>
                    <div>
                      <Card.Title level={3}>
                        {audiences.filter((audience) => audience.tier === 'secondary')[0]?.personaName ||
                          'Secondary Audience'}
                      </Card.Title>
                      <p className='text-[#676D50]/80 text-sm'>
                        {audiences.filter((audience) => audience.tier === 'secondary')[0]?.bio?.slice(0, 100) ||
                          'Secondary target demographic for your brand'}
                        ...
                      </p>
                    </div>
                    <div className='flex gap-2'>
                      <Link
                        to={`/audiences/${audiences.filter((audience) => audience.tier === 'secondary')[0]?.id}`}
                        className='px-4 py-2 text-sm text-[#F8F4DF] bg-[#676D50] hover:bg-[#849068] rounded-lg transition-colors font-medium'
                      >
                        Edit Details
                      </Link>
                      <button
                        onClick={() => handleDeleteAudienceTier('secondary')}
                        className='px-4 py-2 text-sm text-white bg-red-500 hover:bg-red-600 rounded-lg transition-colors font-medium'
                        title='Delete entire Secondary audience and all characters'
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>

                  {/* Character Avatars */}
                  <div className='grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3'>
                    {getCharactersByTier('secondary')
                      .slice(0, 6)
                      .map((character, index) => (
                        <div key={character.id} className='text-center group cursor-pointer relative'>
                          <Link to={`/characters/${character.id}`} className='block'>
                            <div className='w-20 h-20 rounded-full overflow-hidden mb-2 mx-auto border-2 border-[#B5B178]/20 group-hover:border-[#676D50]/50 transition-colors'>
                              {character.avatarUrl ? (
                                <img
                                  src={character.avatarUrl}
                                  alt={character.name}
                                  className='w-full h-full object-cover group-hover:scale-105 transition-transform'
                                />
                              ) : (
                                <div className='w-full h-full bg-[#F6F3E5] dark:bg-gray-700 flex items-center justify-center group-hover:bg-[#676D50]/10 transition-colors'>
                                  <User
                                    size={24}
                                    className='text-[#9EA581] group-hover:text-[#676D50] transition-colors'
                                  />
                                </div>
                              )}
                            </div>
                            <p className='text-xs text-[#676D50] font-medium group-hover:text-[#1F2419] transition-colors'>
                              {character.name || `Character ${index + 1}`}
                            </p>
                          </Link>

                          {/* Delete Button */}
                          <button
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleDeleteCharacter(character.id, character.name || `Character ${index + 1}`);
                            }}
                            className='absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity shadow-sm'
                            title='Delete character'
                          >
                            <X size={12} />
                          </button>
                        </div>
                      ))}

                    {/* Add Character Button */}
                    {getCharactersByTier('secondary').length < 6 && (
                      <div
                        onClick={() => {
                          setSelectedTier('secondary');
                          openNewAudienceModal();
                        }}
                        className='text-center cursor-pointer group'
                      >
                        <div className='w-20 h-20 rounded-full border-2 border-dashed border-[#9EA581]/30 flex items-center justify-center mb-2 mx-auto group-hover:border-[#676D50]/50 transition-colors'>
                          <Plus size={24} className='text-[#9EA581] group-hover:text-[#676D50] transition-colors' />
                        </div>
                        <p className='text-xs text-[#676D50]/60 font-medium'>Add Character</p>
                      </div>
                    )}
                  </div>
                </Card.Content>
              </Card>
            ) : (
              <div
                onClick={() => {
                  setSelectedTier('secondary');
                  openNewAudienceModal();
                }}
                className='bg-[#F6F3E5] dark:bg-gray-800 rounded-2xl border-2 border-dashed border-[#9EA581]/30 flex items-center justify-center h-32 transition-all duration-300 hover:border-[#676D50]/50 cursor-pointer'
              >
                <div className='text-center'>
                  <Plus size={32} className='text-[#9EA581] mx-auto mb-2' />
                  <p className='text-[#676D50] font-medium'>Create Secondary Audience</p>
                </div>
              </div>
            )}
          </div>

          {/* Tertiary Audience Section */}
          <div className='mb-12'>
            <div className='flex items-center gap-3 mb-6'>
              <div className='w-8 h-8 bg-[#9EA581] rounded-full flex items-center justify-center'>
                <span className='text-[#F8F4DF] font-bold text-sm'>🥉</span>
              </div>
              <h2 className='font-display text-2xl font-bold text-[#676D50]'>Tertiary Audience</h2>
              <span className='text-[#676D50]/60 text-sm'>Additional target demographic</span>
            </div>

            {/* Tertiary Audience Card */}
            {audiences.filter((audience) => audience.tier === 'tertiary').length > 0 ? (
              <Card variant='default' className='mb-4'>
                <Card.Content>
                  <div className='flex items-center justify-between mb-4'>
                    <div>
                      <Card.Title level={3}>
                        {audiences.filter((audience) => audience.tier === 'tertiary')[0]?.personaName ||
                          'Tertiary Audience'}
                      </Card.Title>
                      <p className='text-[#676D50]/80 text-sm'>
                        {audiences.filter((audience) => audience.tier === 'tertiary')[0]?.bio?.slice(0, 100) ||
                          'Additional target demographic for your brand'}
                        ...
                      </p>
                    </div>
                    <div className='flex gap-2'>
                      <Link
                        to={`/audiences/${audiences.filter((audience) => audience.tier === 'tertiary')[0]?.id}`}
                        className='px-4 py-2 text-sm text-[#F8F4DF] bg-[#676D50] hover:bg-[#849068] rounded-lg transition-colors font-medium'
                      >
                        Edit Details
                      </Link>
                      <button
                        onClick={() => handleDeleteAudienceTier('tertiary')}
                        className='px-4 py-2 text-sm text-white bg-red-500 hover:bg-red-600 rounded-lg transition-colors font-medium'
                        title='Delete entire Tertiary audience and all characters'
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>

                  {/* Character Avatars */}
                  <div className='grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3'>
                    {getCharactersByTier('tertiary')
                      .slice(0, 6)
                      .map((character, index) => (
                        <div key={character.id} className='text-center group cursor-pointer relative'>
                          <Link to={`/characters/${character.id}`} className='block'>
                            <div className='w-20 h-20 rounded-full overflow-hidden mb-2 mx-auto border-2 border-[#B5B178]/20 group-hover:border-[#676D50]/50 transition-colors'>
                              {character.avatarUrl ? (
                                <img
                                  src={character.avatarUrl}
                                  alt={character.name}
                                  className='w-full h-full object-cover group-hover:scale-105 transition-transform'
                                />
                              ) : (
                                <div className='w-full h-full bg-[#F6F3E5] dark:bg-gray-700 flex items-center justify-center group-hover:bg-[#676D50]/10 transition-colors'>
                                  <User
                                    size={24}
                                    className='text-[#9EA581] group-hover:text-[#676D50] transition-colors'
                                  />
                                </div>
                              )}
                            </div>
                            <p className='text-xs text-[#676D50] font-medium group-hover:text-[#1F2419] transition-colors'>
                              {character.name || `Character ${index + 1}`}
                            </p>
                          </Link>

                          {/* Delete Button */}
                          <button
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleDeleteCharacter(character.id, character.name || `Character ${index + 1}`);
                            }}
                            className='absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity shadow-sm'
                            title='Delete character'
                          >
                            <X size={12} />
                          </button>
                        </div>
                      ))}

                    {/* Add Character Button */}
                    {getCharactersByTier('tertiary').length < 6 && (
                      <div
                        onClick={() => {
                          setSelectedTier('tertiary');
                          openNewAudienceModal();
                        }}
                        className='text-center cursor-pointer group'
                      >
                        <div className='w-20 h-20 rounded-full border-2 border-dashed border-[#9EA581]/30 flex items-center justify-center mb-2 mx-auto group-hover:border-[#676D50]/50 transition-colors'>
                          <Plus size={24} className='text-[#9EA581] group-hover:text-[#676D50] transition-colors' />
                        </div>
                        <p className='text-xs text-[#676D50]/60 font-medium'>Add Character</p>
                      </div>
                    )}
                  </div>
                </Card.Content>
              </Card>
            ) : (
              <div
                onClick={() => {
                  setSelectedTier('tertiary');
                  openNewAudienceModal();
                }}
                className='bg-[#F6F3E5] dark:bg-gray-900 rounded-2xl border-2 border-dashed border-[#9EA581]/30 flex items-center justify-center h-32 transition-all duration-300 hover:border-[#676D50]/50 cursor-pointer'
              >
                <div className='text-center'>
                  <Plus size={32} className='text-[#9EA581] mx-auto mb-2' />
                  <p className='text-[#676D50] font-medium'>Create Tertiary Audience</p>
                </div>
              </div>
            )}
          </div>

          {/* Additional Audiences (Upgrade Required) */}
          <div className='mb-12'>
            <div className='flex items-center gap-3 mb-6'>
              <div className='w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center'>
                <span className='text-white font-bold text-sm'>+</span>
              </div>
              <h2 className='font-display text-2xl font-bold text-gray-400'>Additional Audiences</h2>
              <span className='px-3 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full border border-yellow-200 font-medium'>
                Pro Plan Required
              </span>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
              {/* Upgrade Prompt Cards */}
              {[1, 2, 3, 4].map((index) => (
                <div
                  key={index}
                  onClick={() => setShowUpgradeModal(true)}
                  className='bg-gray-50 dark:bg-gray-900 rounded-2xl border-2 border-dashed border-gray-300 dark:border-gray-600 flex items-center justify-center h-64 transition-all duration-300 hover:border-[#676D50]/50 cursor-pointer group'
                >
                  <div className='text-center'>
                    <div className='w-12 h-12 bg-gray-200 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-[#676D50]/10 transition-colors'>
                      <Plus size={24} className='text-gray-400 group-hover:text-[#676D50] transition-colors' />
                    </div>
                    <p className='text-gray-500 font-medium mb-1'>Audience #{index + 3}</p>
                    <p className='text-gray-400 text-sm'>Upgrade to unlock</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* New Audience Modal */}
        {showNewAudienceModal && (
          <div className='fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50'>
            <Card variant='elevated' className='w-96 max-w-md mx-4'>
              <Card.Header>
                <Card.Title>Create New Avatar</Card.Title>
              </Card.Header>
              <Card.Content>
                <form onSubmit={handleCreateAudience}>
                  <FormGroup>
                    <Label htmlFor='tier'>Audience Tier</Label>
                    <select
                      id='tier'
                      value={selectedTier}
                      onChange={(e) => setSelectedTier(e.target.value as 'primary' | 'secondary' | 'tertiary')}
                      className='w-full px-4 py-3 border border-[#B5B178]/30 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50] focus:border-[#676D50] bg-white dark:bg-gray-800 text-[#1F2419] dark:text-white mb-4'
                    >
                      <option value='primary'>🎯 Primary Audience</option>
                      <option value='secondary'>🥈 Secondary Audience</option>
                      <option value='tertiary'>🥉 Tertiary Audience</option>
                    </select>
                  </FormGroup>

                  <FormGroup>
                    <Label htmlFor='personaName'>Avatar Name</Label>
                    <Input
                      type='text'
                      id='personaName'
                      name='personaName'
                      value={newAudienceName}
                      onChange={(e) => setNewAudienceName(e.target.value)}
                      placeholder='Enter avatar name...'
                      required
                    />
                  </FormGroup>

                  <div className='flex justify-end gap-4 mt-6'>
                    <Button type='button' variant='ghost' onClick={closeNewAudienceModal}>
                      Cancel
                    </Button>
                    <Button type='submit' variant='primary' loading={isCreatingAudience}>
                      Create Avatar
                    </Button>
                  </div>
                </form>
              </Card.Content>
            </Card>
          </div>
        )}

        {/* Upgrade Modal */}
        {showUpgradeModal && (
          <div className='fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50'>
            <div className='bg-white dark:bg-gray-900 rounded-2xl p-8 w-96 max-w-md mx-4 border border-[#B5B178]/20 dark:border-gray-600 shadow-xl'>
              <div className='text-center'>
                <div className='w-16 h-16 bg-gradient-to-r from-[#676D50] to-[#849068] rounded-full flex items-center justify-center mx-auto mb-4'>
                  <span className='text-2xl'>🚀</span>
                </div>
                <h2 className='font-display text-2xl font-bold text-[#676D50] dark:text-white mb-4'>Upgrade to Pro</h2>
                <p className='text-[#676D50]/80 dark:text-gray-300 mb-6 leading-relaxed'>
                  Unlock unlimited audiences and advanced features to supercharge your marketing campaigns.
                </p>

                <div className='bg-[#F6F3E5] dark:bg-gray-700 rounded-xl p-4 mb-6'>
                  <h3 className='font-semibold text-[#676D50] dark:text-white mb-2'>Pro Plan Includes:</h3>
                  <ul className='text-sm text-[#676D50]/80 dark:text-gray-300 space-y-1 text-left'>
                    <li>• Unlimited audience tiers</li>
                    <li>• Advanced avatar generation</li>
                    <li>• Priority support</li>
                    <li>• Team collaboration</li>
                  </ul>
                </div>

                <div className='flex gap-3'>
                  <button
                    onClick={() => setShowUpgradeModal(false)}
                    className='flex-1 px-6 py-3 text-[#676D50] hover:text-[#1F2419] dark:hover:text-white transition-colors font-medium'
                  >
                    Maybe Later
                  </button>
                  <button
                    onClick={() => {
                      setShowUpgradeModal(false);
                      // TODO: Navigate to pricing page
                      console.log('Navigate to pricing page');
                    }}
                    className='flex-1 px-6 py-3 bg-gradient-to-r from-[#676D50] to-[#849068] hover:from-[#849068] hover:to-[#676D50] text-[#F8F4DF] rounded-xl transition-all duration-300 font-medium shadow-sm'
                  >
                    Upgrade Now
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </ActiveSessionChecker>
  );
};

export default AudiencePage;
