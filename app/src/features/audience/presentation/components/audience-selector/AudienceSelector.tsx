import React from 'react';
import { getAudiences, useQuery } from 'wasp/client/operations';
import { User } from 'lucide-react';
import type { AudienceSelectorProps } from '../../../domain/types';
import { useOrganizationState } from '../../../../../organization/store';

const AudienceSelector: React.FC<AudienceSelectorProps> = ({ onSelect, selectedId, className = '' }) => {
  const { selectedOrganizationId } = useOrganizationState();
  const {
    data: audiences,
    isLoading,
    error,
  } = useQuery(getAudiences, { organizationId: selectedOrganizationId! }, { enabled: !!selectedOrganizationId }) as {
    data: any[];
    isLoading: boolean;
    error: any;
  };

  if (isLoading) {
    return <div className={`text-gray-500 ${className}`}>Loading audiences...</div>;
  }

  if (error) {
    return <div className={`text-red-500 ${className}`}>Error loading audiences: {error.message}</div>;
  }

  if (!audiences?.length) {
    return <div className={`text-gray-500 ${className}`}>No audiences found</div>;
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {audiences.map((audience) => (
        <button
          key={audience.id}
          onClick={() => onSelect(audience)}
          className={`w-full px-4 py-2 text-left rounded-lg transition-colors duration-200 flex items-center space-x-3 ${
            selectedId === audience.id ? 'bg-[#676D50] text-white' : 'bg-white hover:bg-[#676D50]/10'
          }`}
        >
          <div className='flex-shrink-0'>
            {audience.avatarUrl ? (
              <img
                src={audience.avatarUrl}
                alt={audience.personaName || ''}
                className='w-8 h-8 rounded-full object-cover'
              />
            ) : (
              <div className='w-8 h-8 rounded-full bg-[#676D50]/10 flex items-center justify-center'>
                <User className='w-4 h-4 text-[#676D50]' />
              </div>
            )}
          </div>
          <div>
            <div className='font-medium'>{audience.personaName || 'Unnamed Audience'}</div>
            {audience.occupation && <div className='text-sm opacity-75'>{audience.occupation}</div>}
          </div>
        </button>
      ))}
    </div>
  );
};

export default AudienceSelector;
