/**
 * 💬 Generation Chat Bubble - CLEAN GENERATION TRIGGER
 * @description Clean chat bubble component that triggers the generation pipeline
 * @responsibility Handles user input → triggers StartGeneration use case → shows loading card
 * @ai_context Entry point for the 3-step generation pipeline (loading → concept → final)
 */

import React, { useState, useRef, useEffect } from 'react';
import { useGeneration } from '../hooks/useGeneration';

// ============================================================================
// COMPONENT INTERFACES
// ============================================================================

export interface GenerationChatBubbleProps {
  userId: string;
  canvasId: string;
  position: { x: number; y: number };
  onClose?: () => void;
  onGenerationStarted?: (generationId: string) => void;
  className?: string;
  autoFocus?: boolean;
  placeholder?: string;
  maxLength?: number;
}

export interface GenerationTypeOption {
  id: 'product_photography' | 'ad_photography';
  label: string;
  description: string;
  icon: string;
}

// ============================================================================
// GENERATION CHAT BUBBLE COMPONENT
// ============================================================================

export const GenerationChatBubble: React.FC<GenerationChatBubbleProps> = ({
  userId,
  canvasId,
  position,
  onClose,
  onGenerationStarted,
  className = '',
  autoFocus = true,
  placeholder = 'Describe what you want to create...',
  maxLength = 2000,
}) => {
  // State
  const [userPrompt, setUserPrompt] = useState('');
  const [selectedType, setSelectedType] = useState<'product_photography' | 'ad_photography'>('product_photography');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Refs
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const bubbleRef = useRef<HTMLDivElement>(null);

  // Hooks
  const {
    startGeneration,
    isProcessing,
    connectionState,
    connect,
  } = useGeneration({
    userId,
    canvasId,
    autoConnect: true,
  });

  // ============================================================================
  // GENERATION TYPE OPTIONS
  // ============================================================================

  const generationTypes: GenerationTypeOption[] = [
    {
      id: 'product_photography',
      label: 'Product Photo',
      description: 'Professional product photography',
      icon: '📸',
    },
    {
      id: 'ad_photography',
      label: 'Ad Creative',
      description: 'Marketing and advertising visuals',
      icon: '🎯',
    },
  ];

  // ============================================================================
  // EFFECTS
  // ============================================================================

  useEffect(() => {
    // Auto-focus textarea
    if (autoFocus && textareaRef.current) {
      textareaRef.current.focus();
    }

    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [autoFocus, userPrompt]);

  useEffect(() => {
    // Ensure WebSocket connection
    if (connectionState === 'disconnected') {
      connect();
    }
  }, [connectionState, connect]);

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!userPrompt.trim() || isSubmitting || isProcessing) {
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      console.log('[GenerationChatBubble] Starting generation:', {
        prompt: userPrompt.substring(0, 100) + '...',
        type: selectedType,
        position,
        connectionState,
      });

      // Always use the real StartGeneration use case for the full pipeline
      const generationId = await startGeneration({
        userPrompt: userPrompt.trim(),
        position,
        requestText: userPrompt.trim(),
        generationType: selectedType,
        referenceImages: [], // TODO: Add reference image support
        brandContext: {
          // TODO: Add brand context from canvas/user settings
        },
      });

      if (generationId) {
        console.log('[GenerationChatBubble] Generation started successfully:', generationId);

        // Notify parent component
        onGenerationStarted?.(generationId);

        // Close the chat bubble
        onClose?.();
      } else {
        setError('Failed to start generation. Please try again.');
      }
    } catch (error) {
      console.error('[GenerationChatBubble] Submit error:', error);
      setError(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    } else if (e.key === 'Escape') {
      onClose?.();
    }
  };

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    if (value.length <= maxLength) {
      setUserPrompt(value);
      setError(null);
    }
  };

  const handleTypeSelect = (type: 'product_photography' | 'ad_photography') => {
    setSelectedType(type);
  };

  const handleClickOutside = (e: MouseEvent) => {
    if (bubbleRef.current && !bubbleRef.current.contains(e.target as Node)) {
      onClose?.();
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // ============================================================================
  // RENDER HELPERS
  // ============================================================================

  const renderGenerationTypes = () => (
    <div className="flex gap-2 mb-3">
      {generationTypes.map((type) => (
        <button
          key={type.id}
          type="button"
          onClick={() => handleTypeSelect(type.id)}
          className={`
            flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all
            ${selectedType === type.id
              ? 'bg-[#9EA581] text-white shadow-sm'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }
          `}
        >
          <span>{type.icon}</span>
          <span>{type.label}</span>
        </button>
      ))}
    </div>
  );

  const renderConnectionStatus = () => {
    if (connectionState === 'connected') return null;

    const statusConfig = {
      disconnected: { color: 'text-yellow-500', text: 'Offline Mode (Testing)' },
      connecting: { color: 'text-yellow-500', text: 'Connecting...' },
      error: { color: 'text-yellow-500', text: 'Offline Mode (Testing)' },
    };

    const config = statusConfig[connectionState] || statusConfig.disconnected;

    return (
      <div className={`text-xs ${config.color} mb-2 flex items-center gap-1`}>
        <div className="w-2 h-2 rounded-full bg-current animate-pulse"></div>
        {config.text}
      </div>
    );
  };

  const renderError = () => {
    if (!error) return null;

    return (
      <div className="text-red-500 text-sm mb-2 p-2 bg-red-50 rounded border border-red-200">
        {error}
      </div>
    );
  };

  const renderSubmitButton = () => {
    // Allow testing even when disconnected
    const isDisabled = !userPrompt.trim() || isSubmitting || isProcessing;

    return (
      <button
        type="submit"
        disabled={isDisabled}
        className={`
          px-4 py-2 rounded-lg font-medium text-sm transition-all
          ${isDisabled
            ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
            : 'bg-[#9EA581] text-white hover:bg-[#849068] shadow-sm hover:shadow-md'
          }
        `}
      >
        {isSubmitting || isProcessing ? (
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            Creating...
          </div>
        ) : connectionState === 'connected' ? (
          'Create'
        ) : (
          'Test Create'
        )}
      </button>
    );
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div
      ref={bubbleRef}
      className={`
        absolute z-50 bg-white rounded-xl shadow-lg border border-gray-200 p-4 min-w-[320px] max-w-[400px]
        ${className}
      `}
      style={{
        left: position.x,
        top: position.y,
        transform: 'translate(-50%, -100%)',
      }}
    >
      {/* Connection Status */}
      {renderConnectionStatus()}

      {/* Generation Type Selector */}
      {renderGenerationTypes()}

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-3">
        {/* Error Display */}
        {renderError()}

        {/* Textarea */}
        <div className="relative">
          <textarea
            ref={textareaRef}
            value={userPrompt}
            onChange={handleTextareaChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="
              w-full p-3 border border-gray-200 rounded-lg resize-none
              focus:outline-none focus:ring-2 focus:ring-[#9EA581] focus:border-transparent
              placeholder-gray-400 text-gray-900
            "
            rows={3}
            maxLength={maxLength}
          />
          
          {/* Character Count */}
          <div className="absolute bottom-2 right-2 text-xs text-gray-400">
            {userPrompt.length}/{maxLength}
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center">
          <button
            type="button"
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-sm font-medium"
          >
            Cancel
          </button>
          
          {renderSubmitButton()}
        </div>
      </form>

      {/* Tip */}
      <div className="mt-3 text-xs text-gray-500 border-t border-gray-100 pt-3">
        💡 <strong>Tip:</strong> Be specific about style, lighting, and composition for best results
      </div>
    </div>
  );
};

// ============================================================================
// EXPORT
// ============================================================================

export default GenerationChatBubble;
