/**
 * 🎯 Generation Hook - MAIN ORCHESTRATION HOOK
 * @description Main React hook for the generation pipeline
 * @responsibility Orchestrates the 3-step generation process with race-condition prevention
 * @ai_context Primary interface for components to interact with generation system
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  GenerationState, 
  GenerationEventType,
  GenerationEvent 
} from '../../domain/events/GenerationEvents';
import { generationEventBus } from '../../domain/events/EventBus';
import { GenerationStateMachine, GenerationStateContext } from '../../domain/state/GenerationStateMachine';
import { CloudflareWebSocketClient } from '../../infrastructure/websocket/CloudflareWebSocket';
import { StartGenerationUseCase, StartGenerationRequest } from '../../application/usecases/StartGeneration';
import { autoSaveService } from '../../domain/services/AutoSaveService';

// ============================================================================
// HOOK INTERFACES
// ============================================================================

export interface UseGenerationConfig {
  userId: string;
  canvasId: string;
  autoConnect?: boolean;
  enableAutoSave?: boolean;
}

export interface GenerationInstance {
  id: string;
  state: GenerationState;
  context: GenerationStateContext;
  stateMachine: GenerationStateMachine;
  createdAt: number;
  updatedAt: number;
}

export interface UseGenerationReturn {
  // State
  generations: Map<string, GenerationInstance>;
  currentGeneration: GenerationInstance | null;
  connectionState: 'disconnected' | 'connecting' | 'connected' | 'error';
  isProcessing: boolean;

  // Actions
  startGeneration: (request: Omit<StartGenerationRequest, 'userId' | 'canvasId'>) => Promise<string | null>;
  answerQuestion: (generationId: string, questionIndex: number, answer: string) => Promise<boolean>;
  generateFinal: (generationId: string) => Promise<boolean>;
  cancelGeneration: (generationId: string) => Promise<boolean>;
  setCurrentGeneration: (generationId: string | null) => void;

  // WebSocket
  connect: () => Promise<boolean>;
  disconnect: () => void;
  
  // Utilities
  getGenerationById: (generationId: string) => GenerationInstance | null;
  getGenerationsByState: (state: GenerationState) => GenerationInstance[];
  clearCompletedGenerations: () => void;
}

// ============================================================================
// GENERATION HOOK
// ============================================================================

export function useGeneration(config: UseGenerationConfig): UseGenerationReturn {
  // State
  const [generations, setGenerations] = useState<Map<string, GenerationInstance>>(new Map());
  const [currentGenerationId, setCurrentGenerationId] = useState<string | null>(null);
  const [connectionState, setConnectionState] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  const [isProcessing, setIsProcessing] = useState(false);

  // Refs
  const webSocketRef = useRef<CloudflareWebSocketClient | null>(null);
  const startGenerationUseCaseRef = useRef<StartGenerationUseCase>(new StartGenerationUseCase());
  const eventUnsubscribers = useRef<(() => void)[]>([]);

  // Computed values
  const currentGeneration = currentGenerationId ? generations.get(currentGenerationId) || null : null;

  // ============================================================================
  // INITIALIZATION
  // ============================================================================

  useEffect(() => {
    console.log('[useGeneration] Initializing generation hook:', config);

    // Initialize WebSocket client
    webSocketRef.current = new CloudflareWebSocketClient(config.userId, config.canvasId);

    // Setup event listeners
    setupEventListeners();

    // Auto-connect if enabled
    if (config.autoConnect !== false) {
      connect();
    }

    // Cleanup on unmount
    return () => {
      cleanup();
    };
  }, [config.userId, config.canvasId]);

  // ============================================================================
  // EVENT LISTENERS
  // ============================================================================

  const setupEventListeners = useCallback(() => {
    console.log('[useGeneration] Setting up event listeners');

    // Listen for loading card created
    const unsubscribeLoadingCard = generationEventBus.subscribe(
      GenerationEventType.LOADING_CARD_CREATED,
      handleLoadingCardCreated
    );

    // Listen for concept card created
    const unsubscribeConcept = generationEventBus.subscribe(
      GenerationEventType.CONCEPT_CARD_CREATED,
      handleConceptCardCreated
    );

    // Listen for concept card answered
    const unsubscribeAnswer = generationEventBus.subscribe(
      GenerationEventType.CONCEPT_CARD_ANSWERED,
      handleConceptCardAnswered
    );

    // Listen for final generation completed
    const unsubscribeFinal = generationEventBus.subscribe(
      GenerationEventType.FINAL_GENERATION_COMPLETED,
      handleFinalGenerationCompleted
    );

    // Listen for state transitions
    const unsubscribeState = generationEventBus.subscribe(
      GenerationEventType.STATE_TRANSITION,
      handleStateTransition
    );

    // Store unsubscribers
    eventUnsubscribers.current = [
      unsubscribeLoadingCard,
      unsubscribeConcept,
      unsubscribeAnswer,
      unsubscribeFinal,
      unsubscribeState,
    ];

    // Setup WebSocket listeners
    if (webSocketRef.current) {
      webSocketRef.current.addEventListener('generation_event', handleCollaborativeEvent);
      webSocketRef.current.addEventListener('user_joined', handleUserJoined);
      webSocketRef.current.addEventListener('user_left', handleUserLeft);
    }
  }, []);

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const handleLoadingCardCreated = useCallback((event: GenerationEvent) => {
    if (event.type !== GenerationEventType.LOADING_CARD_CREATED) return;
    if (event.canvasId !== config.canvasId) return;

    console.log('[useGeneration] Loading card created:', event.generationId);

    // Create new generation instance
    const stateMachine = new GenerationStateMachine(
      event.generationId,
      event.canvasId,
      event.userId,
      GenerationState.LOADING
    );

    const instance: GenerationInstance = {
      id: event.generationId,
      state: GenerationState.LOADING,
      context: stateMachine.getContext(),
      stateMachine,
      createdAt: event.timestamp,
      updatedAt: event.timestamp,
    };

    setGenerations(prev => new Map(prev).set(event.generationId, instance));
    
    // Set as current if no current generation
    if (!currentGenerationId) {
      setCurrentGenerationId(event.generationId);
    }
  }, [config.canvasId, currentGenerationId]);

  const handleConceptCardCreated = useCallback((event: GenerationEvent) => {
    if (event.type !== GenerationEventType.CONCEPT_CARD_CREATED) return;
    if (event.canvasId !== config.canvasId) return;

    console.log('[useGeneration] Concept card created:', event.generationId);

    setGenerations(prev => {
      const newMap = new Map(prev);
      const instance = newMap.get(event.generationId);
      
      if (instance) {
        instance.state = GenerationState.CONCEPT;
        instance.updatedAt = event.timestamp;
        instance.stateMachine.transition('CONCEPT_READY');
        instance.context = instance.stateMachine.getContext();
      }
      
      return newMap;
    });
  }, [config.canvasId]);

  const handleConceptCardAnswered = useCallback((event: GenerationEvent) => {
    if (event.type !== GenerationEventType.CONCEPT_CARD_ANSWERED) return;
    if (event.canvasId !== config.canvasId) return;

    console.log('[useGeneration] Concept card answered:', event.generationId);

    setGenerations(prev => {
      const newMap = new Map(prev);
      const instance = newMap.get(event.generationId);
      
      if (instance) {
        instance.updatedAt = event.timestamp;
        instance.context = instance.stateMachine.getContext();
      }
      
      return newMap;
    });
  }, [config.canvasId]);

  const handleFinalGenerationCompleted = useCallback((event: GenerationEvent) => {
    if (event.type !== GenerationEventType.FINAL_GENERATION_COMPLETED) return;
    if (event.canvasId !== config.canvasId) return;

    console.log('[useGeneration] Final generation completed:', event.generationId);

    setGenerations(prev => {
      const newMap = new Map(prev);
      const instance = newMap.get(event.generationId);
      
      if (instance) {
        instance.state = GenerationState.COMPLETED;
        instance.updatedAt = event.timestamp;
        instance.stateMachine.transition('GENERATION_COMPLETE');
        instance.context = instance.stateMachine.getContext();
      }
      
      return newMap;
    });

    setIsProcessing(false);
  }, [config.canvasId]);

  const handleStateTransition = useCallback((event: GenerationEvent) => {
    if (event.type !== GenerationEventType.STATE_TRANSITION) return;
    if (event.canvasId !== config.canvasId) return;

    console.log('[useGeneration] State transition:', event.generationId, event.payload);

    setGenerations(prev => {
      const newMap = new Map(prev);
      const instance = newMap.get(event.generationId);
      
      if (instance) {
        instance.state = event.payload.toState;
        instance.updatedAt = event.timestamp;
        instance.context = instance.stateMachine.getContext();
      }
      
      return newMap;
    });
  }, [config.canvasId]);

  const handleCollaborativeEvent = useCallback((event: GenerationEvent) => {
    console.log('[useGeneration] Collaborative event received:', event.type);
    // Events are already processed by the event bus
  }, []);

  const handleUserJoined = useCallback((data: any) => {
    console.log('[useGeneration] User joined:', data);
  }, []);

  const handleUserLeft = useCallback((data: any) => {
    console.log('[useGeneration] User left:', data);
  }, []);

  // ============================================================================
  // ACTIONS
  // ============================================================================

  const startGeneration = useCallback(async (
    request: Omit<StartGenerationRequest, 'userId' | 'canvasId'>
  ): Promise<string | null> => {
    if (isProcessing) {
      console.warn('[useGeneration] Generation already in progress');
      return null;
    }

    setIsProcessing(true);

    try {
      const fullRequest: StartGenerationRequest = {
        ...request,
        userId: config.userId,
        canvasId: config.canvasId,
      };

      const result = await startGenerationUseCaseRef.current.execute(fullRequest);

      if (result.success) {
        console.log('[useGeneration] Generation started successfully:', result.generationId);
        return result.generationId;
      } else {
        console.error('[useGeneration] Generation start failed:', result.error);
        setIsProcessing(false);
        return null;
      }
    } catch (error) {
      console.error('[useGeneration] Start generation error:', error);
      setIsProcessing(false);
      return null;
    }
  }, [config.userId, config.canvasId, isProcessing]);

  const answerQuestion = useCallback(async (
    generationId: string,
    questionIndex: number,
    answer: string
  ): Promise<boolean> => {
    try {
      console.log('[useGeneration] Answering question:', { generationId, questionIndex, answer });

      // TODO: Implement answer question logic
      // This would trigger concept card update and potentially new preview generation

      return true;
    } catch (error) {
      console.error('[useGeneration] Answer question error:', error);
      return false;
    }
  }, []);

  const generateFinal = useCallback(async (generationId: string): Promise<boolean> => {
    try {
      console.log('[useGeneration] Starting final generation:', generationId);

      const instance = generations.get(generationId);
      if (!instance || instance.state !== GenerationState.CONCEPT) {
        console.warn('[useGeneration] Invalid state for final generation:', instance?.state);
        return false;
      }

      setIsProcessing(true);

      // TODO: Implement final generation logic
      // This would trigger the final image generation use case

      return true;
    } catch (error) {
      console.error('[useGeneration] Generate final error:', error);
      setIsProcessing(false);
      return false;
    }
  }, [generations]);

  const cancelGeneration = useCallback(async (generationId: string): Promise<boolean> => {
    try {
      console.log('[useGeneration] Cancelling generation:', generationId);

      const instance = generations.get(generationId);
      if (!instance) {
        return false;
      }

      await instance.stateMachine.transition('ERROR', {
        error: {
          message: 'Cancelled by user',
          code: 'USER_CANCELLED',
          step: instance.state,
          timestamp: Date.now(),
          retryable: false,
        },
      });

      setIsProcessing(false);
      return true;
    } catch (error) {
      console.error('[useGeneration] Cancel generation error:', error);
      return false;
    }
  }, [generations]);

  const setCurrentGeneration = useCallback((generationId: string | null) => {
    setCurrentGenerationId(generationId);
    
    if (generationId && webSocketRef.current) {
      webSocketRef.current.setGenerationId(generationId);
    }
  }, []);

  // ============================================================================
  // WEBSOCKET ACTIONS
  // ============================================================================

  const connect = useCallback(async (): Promise<boolean> => {
    if (!webSocketRef.current) {
      return false;
    }

    setConnectionState('connecting');

    try {
      const success = await webSocketRef.current.connect();
      setConnectionState(success ? 'connected' : 'error');
      return success;
    } catch (error) {
      console.error('[useGeneration] Connection error:', error);
      setConnectionState('error');
      return false;
    }
  }, []);

  const disconnect = useCallback(() => {
    if (webSocketRef.current) {
      webSocketRef.current.disconnect();
    }
    setConnectionState('disconnected');
  }, []);

  // ============================================================================
  // UTILITIES
  // ============================================================================

  const getGenerationById = useCallback((generationId: string): GenerationInstance | null => {
    return generations.get(generationId) || null;
  }, [generations]);

  const getGenerationsByState = useCallback((state: GenerationState): GenerationInstance[] => {
    return Array.from(generations.values()).filter(gen => gen.state === state);
  }, [generations]);

  const clearCompletedGenerations = useCallback(() => {
    setGenerations(prev => {
      const newMap = new Map();
      prev.forEach((instance, id) => {
        if (instance.state !== GenerationState.COMPLETED) {
          newMap.set(id, instance);
        }
      });
      return newMap;
    });
  }, []);

  // ============================================================================
  // CLEANUP
  // ============================================================================

  const cleanup = useCallback(() => {
    console.log('[useGeneration] Cleaning up');

    // Unsubscribe from events
    eventUnsubscribers.current.forEach(unsubscribe => unsubscribe());
    eventUnsubscribers.current = [];

    // Disconnect WebSocket
    if (webSocketRef.current) {
      webSocketRef.current.disconnect();
      webSocketRef.current = null;
    }

    setConnectionState('disconnected');
  }, []);

  // ============================================================================
  // RETURN HOOK INTERFACE
  // ============================================================================

  return {
    // State
    generations,
    currentGeneration,
    connectionState,
    isProcessing,

    // Actions
    startGeneration,
    answerQuestion,
    generateFinal,
    cancelGeneration,
    setCurrentGeneration,

    // WebSocket
    connect,
    disconnect,

    // Utilities
    getGenerationById,
    getGenerationsByState,
    clearCompletedGenerations,
  };
}
