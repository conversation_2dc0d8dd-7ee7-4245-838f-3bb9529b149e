/**
 * 💾 Auto-Save Service - PERSISTENT STATE MANAGEMENT
 * @description Auto-saves all generation state changes to canvas database
 * @responsibility Ensures every state change is persisted and recoverable
 * @ai_context Core persistence layer for race-condition-free generation pipeline
 */

import { 
  GenerationEventType,
  AutoSaveTriggeredEvent,
  GenerationState 
} from '../events/GenerationEvents';
import { generationEventBus } from '../events/EventBus';
import { GenerationStateContext } from '../state/GenerationStateMachine';

// ============================================================================
// AUTO-SAVE INTERFACES
// ============================================================================

export interface AutoSaveConfig {
  enabled: boolean;
  debounceMs: number;
  maxRetries: number;
  retryDelayMs: number;
  batchSize: number;
}

export interface SaveOperation {
  id: string;
  entityType: 'loading_card' | 'concept_card' | 'final_image' | 'canvas_state' | 'generation_context';
  entityId: string;
  data: Record<string, any>;
  timestamp: number;
  retryCount: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

export interface SaveResult {
  success: boolean;
  operationId: string;
  error?: string;
  timestamp: number;
}

// ============================================================================
// AUTO-SAVE SERVICE CLASS
// ============================================================================

export class AutoSaveService {
  private static instance: AutoSaveService;
  private config: AutoSaveConfig;
  private pendingOperations: Map<string, SaveOperation> = new Map();
  private saveQueue: SaveOperation[] = [];
  private isProcessing = false;
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map();

  private constructor(config: Partial<AutoSaveConfig> = {}) {
    this.config = {
      enabled: true,
      debounceMs: 500,
      maxRetries: 3,
      retryDelayMs: 1000,
      batchSize: 10,
      ...config,
    };

    this.setupEventListeners();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(config?: Partial<AutoSaveConfig>): AutoSaveService {
    if (!AutoSaveService.instance) {
      AutoSaveService.instance = new AutoSaveService(config);
    }
    return AutoSaveService.instance;
  }

  /**
   * Setup event listeners for auto-save triggers
   */
  private setupEventListeners(): void {
    // Listen for all events that should trigger auto-save
    generationEventBus.subscribe(
      GenerationEventType.LOADING_CARD_CREATED,
      async (event) => {
        if (event.type === GenerationEventType.LOADING_CARD_CREATED) {
          await this.saveLoadingCard(event.payload.cardId, {
            id: event.payload.cardId,
            position: event.payload.position,
            requestText: event.payload.requestText,
            userPrompt: event.payload.userPrompt,
            state: 'loading',
            canvasId: event.canvasId,
            userId: event.userId,
            generationId: event.generationId,
            createdAt: event.timestamp,
          });
        }
      }
    );

    generationEventBus.subscribe(
      GenerationEventType.CONCEPT_CARD_CREATED,
      async (event) => {
        if (event.type === GenerationEventType.CONCEPT_CARD_CREATED) {
          await this.saveConceptCard(event.payload.cardId, {
            id: event.payload.cardId,
            loadingCardId: event.payload.loadingCardId,
            position: event.payload.position,
            concept: event.payload.concept,
            prompt: event.payload.prompt,
            questions: event.payload.questions,
            answers: {},
            previewImageUrl: event.payload.previewImageUrl,
            suggestionChips: event.payload.suggestionChips,
            state: 'concept',
            canvasId: event.canvasId,
            userId: event.userId,
            generationId: event.generationId,
            createdAt: event.timestamp,
          });
        }
      }
    );

    generationEventBus.subscribe(
      GenerationEventType.CONCEPT_CARD_ANSWERED,
      async (event) => {
        if (event.type === GenerationEventType.CONCEPT_CARD_ANSWERED) {
          await this.updateConceptCard(event.payload.cardId, {
            [`answers.${event.payload.questionIndex}`]: event.payload.answer,
            updatedPrompt: event.payload.updatedPrompt,
            previewImageUrl: event.payload.newPreviewImageUrl,
            suggestionChips: event.payload.newSuggestionChips,
            updatedAt: event.timestamp,
          });
        }
      }
    );

    generationEventBus.subscribe(
      GenerationEventType.FINAL_GENERATION_COMPLETED,
      async (event) => {
        if (event.type === GenerationEventType.FINAL_GENERATION_COMPLETED) {
          await this.saveFinalImages(event.payload.taskId, {
            taskId: event.payload.taskId,
            cardId: event.payload.cardId,
            imageUrls: event.payload.imageUrls,
            finalPrompt: event.payload.finalPrompt,
            metadata: event.payload.metadata,
            state: 'completed',
            canvasId: event.canvasId,
            userId: event.userId,
            generationId: event.generationId,
            completedAt: event.timestamp,
          });
        }
      }
    );

    generationEventBus.subscribe(
      GenerationEventType.STATE_TRANSITION,
      async (event) => {
        if (event.type === GenerationEventType.STATE_TRANSITION) {
          await this.saveGenerationContext(event.generationId, {
            generationId: event.generationId,
            canvasId: event.canvasId,
            userId: event.userId,
            currentState: event.payload.toState,
            previousState: event.payload.fromState,
            transitionReason: event.payload.reason,
            metadata: event.payload.metadata,
            updatedAt: event.timestamp,
          });
        }
      }
    );
  }

  /**
   * Save loading card data
   */
  public async saveLoadingCard(cardId: string, data: Record<string, any>): Promise<SaveResult> {
    return this.queueSaveOperation({
      id: this.generateOperationId(),
      entityType: 'loading_card',
      entityId: cardId,
      data,
      timestamp: Date.now(),
      retryCount: 0,
      priority: 'high',
    });
  }

  /**
   * Save concept card data
   */
  public async saveConceptCard(cardId: string, data: Record<string, any>): Promise<SaveResult> {
    return this.queueSaveOperation({
      id: this.generateOperationId(),
      entityType: 'concept_card',
      entityId: cardId,
      data,
      timestamp: Date.now(),
      retryCount: 0,
      priority: 'high',
    });
  }

  /**
   * Update concept card data
   */
  public async updateConceptCard(cardId: string, updates: Record<string, any>): Promise<SaveResult> {
    return this.queueSaveOperation({
      id: this.generateOperationId(),
      entityType: 'concept_card',
      entityId: cardId,
      data: { ...updates, isUpdate: true },
      timestamp: Date.now(),
      retryCount: 0,
      priority: 'medium',
    });
  }

  /**
   * Save final images data
   */
  public async saveFinalImages(taskId: string, data: Record<string, any>): Promise<SaveResult> {
    return this.queueSaveOperation({
      id: this.generateOperationId(),
      entityType: 'final_image',
      entityId: taskId,
      data,
      timestamp: Date.now(),
      retryCount: 0,
      priority: 'critical',
    });
  }

  /**
   * Save generation context
   */
  public async saveGenerationContext(generationId: string, data: Record<string, any>): Promise<SaveResult> {
    return this.queueSaveOperation({
      id: this.generateOperationId(),
      entityType: 'generation_context',
      entityId: generationId,
      data,
      timestamp: Date.now(),
      retryCount: 0,
      priority: 'medium',
    });
  }

  /**
   * Queue save operation with debouncing
   */
  private async queueSaveOperation(operation: SaveOperation): Promise<SaveResult> {
    if (!this.config.enabled) {
      return {
        success: false,
        operationId: operation.id,
        error: 'Auto-save disabled',
        timestamp: Date.now(),
      };
    }

    // Publish auto-save triggered event
    await generationEventBus.publish({
      type: GenerationEventType.AUTO_SAVE_TRIGGERED,
      timestamp: operation.timestamp,
      userId: operation.data.userId || 'system',
      canvasId: operation.data.canvasId || 'unknown',
      generationId: operation.data.generationId || 'unknown',
      payload: {
        entityType: operation.entityType,
        entityId: operation.entityId,
        data: operation.data,
      },
    });

    // Debounce similar operations
    const debounceKey = `${operation.entityType}_${operation.entityId}`;
    
    if (this.debounceTimers.has(debounceKey)) {
      clearTimeout(this.debounceTimers.get(debounceKey)!);
    }

    return new Promise((resolve) => {
      const timer = setTimeout(async () => {
        this.debounceTimers.delete(debounceKey);
        
        // Add to queue
        this.saveQueue.push(operation);
        this.pendingOperations.set(operation.id, operation);

        // Process queue
        const result = await this.processQueue();
        resolve(result);
      }, this.config.debounceMs);

      this.debounceTimers.set(debounceKey, timer);
    });
  }

  /**
   * Process save queue
   */
  private async processQueue(): Promise<SaveResult> {
    if (this.isProcessing || this.saveQueue.length === 0) {
      return {
        success: false,
        operationId: 'queue_busy',
        error: 'Queue is busy or empty',
        timestamp: Date.now(),
      };
    }

    this.isProcessing = true;

    try {
      // Process operations in batches
      const batch = this.saveQueue.splice(0, this.config.batchSize);
      const results: SaveResult[] = [];

      for (const operation of batch) {
        const result = await this.executeSaveOperation(operation);
        results.push(result);

        if (!result.success && operation.retryCount < this.config.maxRetries) {
          // Retry failed operations
          operation.retryCount++;
          setTimeout(() => {
            this.saveQueue.unshift(operation);
            this.processQueue();
          }, this.config.retryDelayMs * operation.retryCount);
        } else {
          // Remove from pending operations
          this.pendingOperations.delete(operation.id);
        }
      }

      // Return the first result (for simplicity)
      return results[0] || {
        success: true,
        operationId: 'batch_processed',
        timestamp: Date.now(),
      };
    } finally {
      this.isProcessing = false;

      // Continue processing if queue has more items
      if (this.saveQueue.length > 0) {
        setTimeout(() => this.processQueue(), 100);
      }
    }
  }

  /**
   * Execute individual save operation
   */
  private async executeSaveOperation(operation: SaveOperation): Promise<SaveResult> {
    try {
      console.log(`[AutoSaveService] Saving ${operation.entityType}:${operation.entityId}`);

      // TODO: Implement actual database save operations
      // This would integrate with your canvas repository or database layer
      
      // For now, simulate save operation
      await new Promise(resolve => setTimeout(resolve, 50));

      // Publish auto-save completed event
      await generationEventBus.publish({
        type: GenerationEventType.AUTO_SAVE_COMPLETED,
        timestamp: Date.now(),
        userId: operation.data.userId || 'system',
        canvasId: operation.data.canvasId || 'unknown',
        generationId: operation.data.generationId || 'unknown',
        payload: {
          entityType: operation.entityType,
          entityId: operation.entityId,
          data: operation.data,
        },
      });

      return {
        success: true,
        operationId: operation.id,
        timestamp: Date.now(),
      };
    } catch (error) {
      console.error(`[AutoSaveService] Save operation failed:`, error);

      // Publish auto-save error event
      await generationEventBus.publish({
        type: GenerationEventType.AUTO_SAVE_ERROR,
        timestamp: Date.now(),
        userId: operation.data.userId || 'system',
        canvasId: operation.data.canvasId || 'unknown',
        generationId: operation.data.generationId || 'unknown',
        payload: {
          entityType: operation.entityType,
          entityId: operation.entityId,
          data: operation.data,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });

      return {
        success: false,
        operationId: operation.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      };
    }
  }

  /**
   * Generate unique operation ID
   */
  private generateOperationId(): string {
    return `save_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Get pending operations count
   */
  public getPendingOperationsCount(): number {
    return this.pendingOperations.size;
  }

  /**
   * Get queue status
   */
  public getQueueStatus(): {
    pending: number;
    queued: number;
    processing: boolean;
  } {
    return {
      pending: this.pendingOperations.size,
      queued: this.saveQueue.length,
      processing: this.isProcessing,
    };
  }
}

// ============================================================================
// SINGLETON EXPORT
// ============================================================================

export const autoSaveService = AutoSaveService.getInstance();
