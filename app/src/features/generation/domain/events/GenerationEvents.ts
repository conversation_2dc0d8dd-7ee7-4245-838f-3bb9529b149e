/**
 * 🎯 Generation Events - TYPE-SAFE EVENT SYSTEM
 * @description Replaces string-based events with typed system to prevent typos
 * @responsibility Defines all generation-related events with proper typing
 * @ai_context Clean event system for race-condition-free generation pipeline
 */

// ============================================================================
// EVENT TYPES
// ============================================================================

export enum GenerationEventType {
  // Step 1: Loading Card Events
  LOADING_CARD_CREATED = 'LOADING_CARD_CREATED',
  LOADING_CARD_UPDATED = 'LOADING_CARD_UPDATED',
  LOADING_CARD_REMOVED = 'LOADING_CARD_REMOVED',

  // Step 2: Concept Card Events
  CONCEPT_CARD_CREATED = 'CONCEPT_CARD_CREATED',
  CONCEPT_CARD_UPDATED = 'CONCEPT_CARD_UPDATED',
  CONCEPT_CARD_ANSWERED = 'CONCEPT_CARD_ANSWERED',
  CONCEPT_CARD_READY = 'CONCEPT_CARD_READY',

  // Step 3: Final Generation Events
  FINAL_GENERATION_STARTED = 'FINAL_GENERATION_STARTED',
  FINAL_GENERATION_PROGRESS = 'FINAL_GENERATION_PROGRESS',
  FINAL_GENERATION_COMPLETED = 'FINAL_GENERATION_COMPLETED',
  FINAL_GENERATION_ERROR = 'FINAL_GENERATION_ERROR',

  // State Management Events
  STATE_TRANSITION = 'STATE_TRANSITION',
  AUTO_SAVE_TRIGGERED = 'AUTO_SAVE_TRIGGERED',
  AUTO_SAVE_COMPLETED = 'AUTO_SAVE_COMPLETED',
  AUTO_SAVE_ERROR = 'AUTO_SAVE_ERROR',

  // Collaboration Events
  USER_JOINED_GENERATION = 'USER_JOINED_GENERATION',
  USER_LEFT_GENERATION = 'USER_LEFT_GENERATION',
  COLLABORATIVE_UPDATE = 'COLLABORATIVE_UPDATE',
}

// ============================================================================
// EVENT PAYLOAD INTERFACES
// ============================================================================

export interface BaseGenerationEvent {
  type: GenerationEventType;
  timestamp: number;
  userId: string;
  canvasId: string;
  generationId: string;
}

export interface LoadingCardCreatedEvent extends BaseGenerationEvent {
  type: GenerationEventType.LOADING_CARD_CREATED;
  payload: {
    cardId: string;
    position: { x: number; y: number };
    requestText: string;
    userPrompt: string;
  };
}

export interface ConceptCardCreatedEvent extends BaseGenerationEvent {
  type: GenerationEventType.CONCEPT_CARD_CREATED;
  payload: {
    cardId: string;
    loadingCardId: string; // For replacement
    position: { x: number; y: number };
    concept: string;
    prompt: string;
    questions: string[];
    previewImageUrl?: string;
    suggestionChips: string[];
  };
}

export interface ConceptCardAnsweredEvent extends BaseGenerationEvent {
  type: GenerationEventType.CONCEPT_CARD_ANSWERED;
  payload: {
    cardId: string;
    questionIndex: number;
    answer: string;
    updatedPrompt: string;
    newPreviewImageUrl?: string;
    newSuggestionChips: string[];
  };
}

export interface FinalGenerationStartedEvent extends BaseGenerationEvent {
  type: GenerationEventType.FINAL_GENERATION_STARTED;
  payload: {
    cardId: string;
    taskId: string;
    finalPrompt: string;
    aspectRatio: '1024x1024' | '1536x1024' | '1024x1536';
    numImages: number;
  };
}

export interface FinalGenerationProgressEvent extends BaseGenerationEvent {
  type: GenerationEventType.FINAL_GENERATION_PROGRESS;
  payload: {
    taskId: string;
    progress: number; // 0-100
    stage: 'queued' | 'processing' | 'generating' | 'finalizing';
    message?: string;
  };
}

export interface FinalGenerationCompletedEvent extends BaseGenerationEvent {
  type: GenerationEventType.FINAL_GENERATION_COMPLETED;
  payload: {
    taskId: string;
    cardId: string;
    imageUrls: string[];
    finalPrompt: string;
    metadata?: Record<string, any>;
  };
}

export interface StateTransitionEvent extends BaseGenerationEvent {
  type: GenerationEventType.STATE_TRANSITION;
  payload: {
    fromState: GenerationState;
    toState: GenerationState;
    reason: string;
    metadata?: Record<string, any>;
  };
}

export interface AutoSaveTriggeredEvent extends BaseGenerationEvent {
  type: GenerationEventType.AUTO_SAVE_TRIGGERED;
  payload: {
    entityType: 'loading_card' | 'concept_card' | 'final_image' | 'canvas_state' | 'generation_context';
    entityId: string;
    data: Record<string, any>;
  };
}

// ============================================================================
// STATE DEFINITIONS
// ============================================================================

export enum GenerationState {
  IDLE = 'IDLE',
  LOADING = 'LOADING',           // Step 1: Loading card shown
  CONCEPT = 'CONCEPT',           // Step 2: Concept card active
  GENERATING = 'GENERATING',     // Step 3: Final generation in progress
  COMPLETED = 'COMPLETED',       // Final images generated
  ERROR = 'ERROR',               // Error state
}

// ============================================================================
// AUTO-SAVE EVENT INTERFACES
// ============================================================================

export interface AutoSaveCompletedEvent extends BaseGenerationEvent {
  type: GenerationEventType.AUTO_SAVE_COMPLETED;
  payload: {
    entityType: 'loading_card' | 'concept_card' | 'final_image' | 'canvas_state' | 'generation_context';
    entityId: string;
    data: Record<string, any>;
  };
}

export interface AutoSaveErrorEvent extends BaseGenerationEvent {
  type: GenerationEventType.AUTO_SAVE_ERROR;
  payload: {
    entityType: 'loading_card' | 'concept_card' | 'final_image' | 'canvas_state' | 'generation_context';
    entityId: string;
    data: Record<string, any>;
    error: string;
  };
}

// ============================================================================
// UNION TYPES
// ============================================================================

export type GenerationEvent =
  | LoadingCardCreatedEvent
  | ConceptCardCreatedEvent
  | ConceptCardAnsweredEvent
  | FinalGenerationStartedEvent
  | FinalGenerationProgressEvent
  | FinalGenerationCompletedEvent
  | StateTransitionEvent
  | AutoSaveTriggeredEvent
  | AutoSaveCompletedEvent
  | AutoSaveErrorEvent;

// ============================================================================
// EVENT METADATA
// ============================================================================

export interface GenerationEventMetadata {
  source: 'user' | 'system' | 'ai' | 'collaboration';
  priority: 'low' | 'medium' | 'high' | 'critical';
  shouldBroadcast: boolean;
  shouldAutoSave: boolean;
  retryable: boolean;
}

// ============================================================================
// EVENT WITH METADATA
// ============================================================================

export interface GenerationEventWithMetadata {
  event: GenerationEvent;
  metadata: GenerationEventMetadata;
}
