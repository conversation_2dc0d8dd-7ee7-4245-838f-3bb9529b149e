/**
 * 🚌 Generation Event Bus - TYPE-SAFE EVENT SYSTEM
 * @description Central event bus for generation pipeline with type safety
 * @responsibility Manages event publishing, subscription, and routing
 * @ai_context Replaces string-based events to prevent typos and ensure type safety
 */

import { 
  GenerationEvent, 
  GenerationEventType, 
  GenerationEventWithMetadata,
  GenerationEventMetadata 
} from './GenerationEvents';

// ============================================================================
// EVENT LISTENER TYPES
// ============================================================================

export type EventListener<T extends GenerationEvent = GenerationEvent> = (event: T) => void | Promise<void>;

export interface EventSubscription {
  id: string;
  eventType: GenerationEventType;
  listener: EventListener;
  once: boolean;
}

// ============================================================================
// EVENT BUS CLASS
// ============================================================================

export class GenerationEventBus {
  private static instance: GenerationEventBus;
  private subscriptions: Map<GenerationEventType, EventSubscription[]> = new Map();
  private eventHistory: GenerationEventWithMetadata[] = [];
  private maxHistorySize = 1000;

  private constructor() {}

  /**
   * Get singleton instance
   */
  public static getInstance(): GenerationEventBus {
    if (!GenerationEventBus.instance) {
      GenerationEventBus.instance = new GenerationEventBus();
    }
    return GenerationEventBus.instance;
  }

  /**
   * Subscribe to events with type safety
   */
  public subscribe<T extends GenerationEvent>(
    eventType: T['type'],
    listener: EventListener<T>,
    options: { once?: boolean } = {}
  ): () => void {
    const subscription: EventSubscription = {
      id: this.generateSubscriptionId(),
      eventType,
      listener: listener as EventListener,
      once: options.once || false,
    };

    if (!this.subscriptions.has(eventType)) {
      this.subscriptions.set(eventType, []);
    }

    this.subscriptions.get(eventType)!.push(subscription);

    // Return unsubscribe function
    return () => this.unsubscribe(subscription.id);
  }

  /**
   * Subscribe to multiple event types
   */
  public subscribeToMultiple<T extends GenerationEvent>(
    eventTypes: T['type'][],
    listener: EventListener<T>,
    options: { once?: boolean } = {}
  ): () => void {
    const unsubscribeFunctions = eventTypes.map(eventType =>
      this.subscribe(eventType, listener, options)
    );

    // Return function that unsubscribes from all
    return () => {
      unsubscribeFunctions.forEach(unsubscribe => unsubscribe());
    };
  }

  /**
   * Publish event with metadata
   */
  public async publish(
    event: GenerationEvent,
    metadata: Partial<GenerationEventMetadata> = {}
  ): Promise<void> {
    const fullMetadata: GenerationEventMetadata = {
      source: 'system',
      priority: 'medium',
      shouldBroadcast: true,
      shouldAutoSave: true,
      retryable: true,
      ...metadata,
    };

    const eventWithMetadata: GenerationEventWithMetadata = {
      event,
      metadata: fullMetadata,
    };

    // Add to history
    this.addToHistory(eventWithMetadata);

    // Log event for debugging
    console.log(`[GenerationEventBus] Publishing ${event.type}:`, {
      generationId: event.generationId,
      canvasId: event.canvasId,
      userId: event.userId,
      metadata: fullMetadata,
    });

    // Get subscribers for this event type
    const subscribers = this.subscriptions.get(event.type) || [];

    // Execute all listeners
    const promises = subscribers.map(async (subscription) => {
      try {
        await subscription.listener(event);

        // Remove one-time listeners
        if (subscription.once) {
          this.unsubscribe(subscription.id);
        }
      } catch (error) {
        console.error(`[GenerationEventBus] Error in listener for ${event.type}:`, error);
        
        // Don't let one listener failure break others
        if (fullMetadata.retryable) {
          console.log(`[GenerationEventBus] Event ${event.type} is retryable, considering retry logic`);
        }
      }
    });

    await Promise.allSettled(promises);
  }

  /**
   * Unsubscribe by subscription ID
   */
  private unsubscribe(subscriptionId: string): void {
    this.subscriptions.forEach((subscriptions, eventType) => {
      const index = subscriptions.findIndex(sub => sub.id === subscriptionId);
      if (index !== -1) {
        subscriptions.splice(index, 1);
        if (subscriptions.length === 0) {
          this.subscriptions.delete(eventType);
        }
      }
    });
  }

  /**
   * Get event history for debugging
   */
  public getEventHistory(
    filters: {
      generationId?: string;
      canvasId?: string;
      userId?: string;
      eventTypes?: GenerationEventType[];
      since?: number;
    } = {}
  ): GenerationEventWithMetadata[] {
    return this.eventHistory.filter(eventWithMetadata => {
      const { event } = eventWithMetadata;

      if (filters.generationId && event.generationId !== filters.generationId) return false;
      if (filters.canvasId && event.canvasId !== filters.canvasId) return false;
      if (filters.userId && event.userId !== filters.userId) return false;
      if (filters.eventTypes && !filters.eventTypes.includes(event.type)) return false;
      if (filters.since && event.timestamp < filters.since) return false;

      return true;
    });
  }

  /**
   * Clear event history
   */
  public clearHistory(): void {
    this.eventHistory = [];
  }

  /**
   * Get current subscriptions for debugging
   */
  public getSubscriptions(): Map<GenerationEventType, EventSubscription[]> {
    return new Map(this.subscriptions);
  }

  /**
   * Add event to history with size management
   */
  private addToHistory(eventWithMetadata: GenerationEventWithMetadata): void {
    this.eventHistory.push(eventWithMetadata);

    // Trim history if it gets too large
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory = this.eventHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Generate unique subscription ID
   */
  private generateSubscriptionId(): string {
    return `sub_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}

// ============================================================================
// SINGLETON EXPORT
// ============================================================================

export const generationEventBus = GenerationEventBus.getInstance();
