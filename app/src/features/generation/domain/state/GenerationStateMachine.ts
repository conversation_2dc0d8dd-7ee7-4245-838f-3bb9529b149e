/**
 * 🎰 Generation State Machine - RACE-CONDITION-FREE PIPELINE
 * @description Sequential state machine for 3-step generation pipeline
 * @responsibility Ensures steps 1→2→3 execute in order without race conditions
 * @ai_context Core state management for loading → concept → final generation
 */

import { 
  GenerationState, 
  GenerationEventType,
  StateTransitionEvent 
} from '../events/GenerationEvents';
import { generationEventBus } from '../events/EventBus';

// ============================================================================
// STATE MACHINE INTERFACES
// ============================================================================

export interface GenerationStateContext {
  generationId: string;
  canvasId: string;
  userId: string;
  currentState: GenerationState;
  previousState?: GenerationState;
  
  // Step 1: Loading Card Data
  loadingCard?: {
    cardId: string;
    position: { x: number; y: number };
    requestText: string;
    userPrompt: string;
    createdAt: number;
  };

  // Step 2: Concept Card Data
  conceptCard?: {
    cardId: string;
    concept: string;
    prompt: string;
    questions: string[];
    answers: Record<number, string>;
    previewImageUrl?: string;
    suggestionChips: string[];
    createdAt: number;
    updatedAt: number;
  };

  // Step 3: Final Generation Data
  finalGeneration?: {
    taskId: string;
    finalPrompt: string;
    aspectRatio: '1024x1024' | '1536x1024' | '1024x1536';
    numImages: number;
    progress: number;
    stage: 'queued' | 'processing' | 'generating' | 'finalizing';
    imageUrls?: string[];
    startedAt: number;
    completedAt?: number;
  };

  // Error handling
  error?: {
    message: string;
    code: string;
    step: GenerationState;
    timestamp: number;
    retryable: boolean;
  };

  // Metadata
  createdAt: number;
  updatedAt: number;
}

export interface StateTransition {
  from: GenerationState;
  to: GenerationState;
  trigger: string;
  guard?: (context: GenerationStateContext) => boolean;
  action?: (context: GenerationStateContext) => Promise<void> | void;
}

// ============================================================================
// STATE MACHINE CLASS
// ============================================================================

export class GenerationStateMachine {
  private context: GenerationStateContext;
  private transitions: StateTransition[] = [];
  private isTransitioning = false;

  constructor(
    generationId: string,
    canvasId: string,
    userId: string,
    initialState: GenerationState = GenerationState.IDLE
  ) {
    this.context = {
      generationId,
      canvasId,
      userId,
      currentState: initialState,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    this.setupTransitions();
  }

  /**
   * Setup valid state transitions
   */
  private setupTransitions(): void {
    this.transitions = [
      // Step 1: Start generation (IDLE → LOADING)
      {
        from: GenerationState.IDLE,
        to: GenerationState.LOADING,
        trigger: 'START_GENERATION',
        guard: (context) => !context.loadingCard,
      },

      // Step 2: Concept ready (LOADING → CONCEPT)
      {
        from: GenerationState.LOADING,
        to: GenerationState.CONCEPT,
        trigger: 'CONCEPT_READY',
        guard: (context) => !!context.loadingCard && !!context.conceptCard,
      },

      // Step 3: Start final generation (CONCEPT → GENERATING)
      {
        from: GenerationState.CONCEPT,
        to: GenerationState.GENERATING,
        trigger: 'START_FINAL_GENERATION',
        guard: (context) => !!context.conceptCard && !context.finalGeneration,
      },

      // Step 4: Generation complete (GENERATING → COMPLETED)
      {
        from: GenerationState.GENERATING,
        to: GenerationState.COMPLETED,
        trigger: 'GENERATION_COMPLETE',
        guard: (context) => !!context.finalGeneration?.imageUrls?.length,
      },

      // Error transitions (any state → ERROR)
      {
        from: GenerationState.LOADING,
        to: GenerationState.ERROR,
        trigger: 'ERROR',
      },
      {
        from: GenerationState.CONCEPT,
        to: GenerationState.ERROR,
        trigger: 'ERROR',
      },
      {
        from: GenerationState.GENERATING,
        to: GenerationState.ERROR,
        trigger: 'ERROR',
      },

      // Recovery transitions (ERROR → previous state)
      {
        from: GenerationState.ERROR,
        to: GenerationState.LOADING,
        trigger: 'RETRY_FROM_LOADING',
        guard: (context) => context.error?.retryable === true,
      },
      {
        from: GenerationState.ERROR,
        to: GenerationState.CONCEPT,
        trigger: 'RETRY_FROM_CONCEPT',
        guard: (context) => context.error?.retryable === true,
      },
      {
        from: GenerationState.ERROR,
        to: GenerationState.GENERATING,
        trigger: 'RETRY_FROM_GENERATING',
        guard: (context) => context.error?.retryable === true,
      },

      // Reset transition (any state → IDLE)
      {
        from: GenerationState.COMPLETED,
        to: GenerationState.IDLE,
        trigger: 'RESET',
      },
      {
        from: GenerationState.ERROR,
        to: GenerationState.IDLE,
        trigger: 'RESET',
      },
    ];
  }

  /**
   * Attempt state transition with race condition protection
   */
  public async transition(
    trigger: string,
    data?: Partial<GenerationStateContext>
  ): Promise<boolean> {
    // Prevent concurrent transitions
    if (this.isTransitioning) {
      console.warn(`[GenerationStateMachine] Transition already in progress for ${this.context.generationId}`);
      return false;
    }

    this.isTransitioning = true;

    try {
      // Find valid transition
      const transition = this.transitions.find(
        t => t.from === this.context.currentState && t.trigger === trigger
      );

      if (!transition) {
        console.warn(
          `[GenerationStateMachine] Invalid transition: ${this.context.currentState} → ${trigger}`
        );
        return false;
      }

      // Check guard condition
      if (transition.guard && !transition.guard(this.context)) {
        console.warn(
          `[GenerationStateMachine] Guard condition failed for transition: ${this.context.currentState} → ${transition.to}`
        );
        return false;
      }

      // Update context
      const previousState = this.context.currentState;
      this.context = {
        ...this.context,
        ...data,
        previousState,
        currentState: transition.to,
        updatedAt: Date.now(),
      };

      // Execute transition action
      if (transition.action) {
        await transition.action(this.context);
      }

      // Publish state transition event
      await generationEventBus.publish({
        type: GenerationEventType.STATE_TRANSITION,
        timestamp: Date.now(),
        userId: this.context.userId,
        canvasId: this.context.canvasId,
        generationId: this.context.generationId,
        payload: {
          fromState: previousState,
          toState: transition.to,
          reason: trigger,
          metadata: data,
        },
      });

      console.log(
        `[GenerationStateMachine] Transition successful: ${previousState} → ${transition.to} (${trigger})`
      );

      return true;
    } catch (error) {
      console.error(`[GenerationStateMachine] Transition error:`, error);
      
      // Set error state if not already in error
      if (this.context.currentState !== GenerationState.ERROR) {
        this.context = {
          ...this.context,
          currentState: GenerationState.ERROR,
          error: {
            message: error instanceof Error ? error.message : 'Unknown error',
            code: 'TRANSITION_ERROR',
            step: this.context.currentState,
            timestamp: Date.now(),
            retryable: true,
          },
          updatedAt: Date.now(),
        };
      }

      return false;
    } finally {
      this.isTransitioning = false;
    }
  }

  /**
   * Get current state
   */
  public getState(): GenerationState {
    return this.context.currentState;
  }

  /**
   * Get full context
   */
  public getContext(): GenerationStateContext {
    return { ...this.context };
  }

  /**
   * Check if transition is valid
   */
  public canTransition(trigger: string): boolean {
    const transition = this.transitions.find(
      t => t.from === this.context.currentState && t.trigger === trigger
    );

    if (!transition) return false;
    if (transition.guard && !transition.guard(this.context)) return false;

    return true;
  }

  /**
   * Get available transitions from current state
   */
  public getAvailableTransitions(): string[] {
    return this.transitions
      .filter(t => t.from === this.context.currentState)
      .filter(t => !t.guard || t.guard(this.context))
      .map(t => t.trigger);
  }

  /**
   * Update context data without state transition
   */
  public updateContext(data: Partial<GenerationStateContext>): void {
    this.context = {
      ...this.context,
      ...data,
      updatedAt: Date.now(),
    };
  }
}
