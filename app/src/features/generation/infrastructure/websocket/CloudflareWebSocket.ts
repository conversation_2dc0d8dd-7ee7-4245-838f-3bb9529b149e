/**
 * ☁️ Cloudflare WebSocket Client - 100% CLOUDFLARE WORKERS
 * @description Pure Cloudflare Workers WebSocket integration (no WASP)
 * @responsibility Real-time communication for generation pipeline
 * @ai_context Replaces legacy WASP WebSocket with clean Cloudflare Workers system
 */

import { 
  GenerationEvent, 
  GenerationEventType,
  GenerationEventWithMetadata 
} from '../../domain/events/GenerationEvents';
import { generationEventBus } from '../../domain/events/EventBus';

// ============================================================================
// WEBSOCKET INTERFACES
// ============================================================================

export interface CloudflareWebSocketConfig {
  url: string;
  reconnectAttempts: number;
  reconnectDelay: number;
  heartbeatInterval: number;
  messageTimeout: number;
}

export interface WebSocketMessage {
  type: 'generation_event' | 'heartbeat' | 'user_joined' | 'user_left' | 'user_join' | 'user_leave' | 'state_response' | 'error';
  payload: any;
  timestamp: number;
  userId: string;
  canvasId?: string;
  generationId?: string;
  roomId?: string;
  canvasState?: any;
  users?: any;
}

export interface ConnectionState {
  status: 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error';
  lastConnected?: number;
  reconnectAttempts: number;
  error?: string;
}

// ============================================================================
// CLOUDFLARE WEBSOCKET CLIENT
// ============================================================================

export class CloudflareWebSocketClient {
  private ws: WebSocket | null = null;
  private config: CloudflareWebSocketConfig;
  private connectionState: ConnectionState;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private messageQueue: WebSocketMessage[] = [];
  private eventListeners: Map<string, Set<(data: any) => void>> = new Map();

  // User context
  private userId: string;
  private canvasId: string;
  private currentGenerationId?: string;

  constructor(
    userId: string,
    canvasId: string,
    config: Partial<CloudflareWebSocketConfig> = {}
  ) {
    this.userId = userId;
    this.canvasId = canvasId;
    
    this.config = {
      url: import.meta.env.VITE_CANVAS_WORKERS_URL || 'wss://canvas-workers.j-5bb.workers.dev',
      reconnectAttempts: 5,
      reconnectDelay: 1000,
      heartbeatInterval: 30000,
      messageTimeout: 10000,
      ...config,
    };

    this.connectionState = {
      status: 'disconnected',
      reconnectAttempts: 0,
    };

    this.setupEventBusIntegration();
  }

  /**
   * Setup integration with generation event bus
   */
  private setupEventBusIntegration(): void {
    // Listen for specific generation events and broadcast them
    generationEventBus.subscribe(GenerationEventType.LOADING_CARD_CREATED, async (event) => {
      if (event.canvasId === this.canvasId) {
        await this.broadcastGenerationEvent(event);
      }
    });

    generationEventBus.subscribe(GenerationEventType.CONCEPT_CARD_CREATED, async (event) => {
      if (event.canvasId === this.canvasId) {
        await this.broadcastGenerationEvent(event);
      }
    });

    generationEventBus.subscribe(GenerationEventType.CONCEPT_CARD_ANSWERED, async (event) => {
      if (event.canvasId === this.canvasId) {
        await this.broadcastGenerationEvent(event);
      }
    });

    generationEventBus.subscribe(GenerationEventType.FINAL_GENERATION_STARTED, async (event) => {
      if (event.canvasId === this.canvasId) {
        await this.broadcastGenerationEvent(event);
      }
    });

    generationEventBus.subscribe(GenerationEventType.FINAL_GENERATION_PROGRESS, async (event) => {
      if (event.canvasId === this.canvasId) {
        await this.broadcastGenerationEvent(event);
      }
    });

    generationEventBus.subscribe(GenerationEventType.FINAL_GENERATION_COMPLETED, async (event) => {
      if (event.canvasId === this.canvasId) {
        await this.broadcastGenerationEvent(event);
      }
    });

    generationEventBus.subscribe(GenerationEventType.STATE_TRANSITION, async (event) => {
      if (event.canvasId === this.canvasId) {
        await this.broadcastGenerationEvent(event);
      }
    });
  }

  /**
   * Connect to Cloudflare Workers WebSocket
   */
  public async connect(): Promise<boolean> {
    if (this.connectionState.status === 'connected' || this.connectionState.status === 'connecting') {
      console.log('[CloudflareWebSocket] Already connected or connecting, skipping...');
      return true;
    }

    console.log('[CloudflareWebSocket] Connecting to Cloudflare Workers...');
    this.connectionState.status = 'connecting';

    try {
      const wsUrl = `${this.config.url}?userId=${this.userId}&canvasId=${this.canvasId}`;
      this.ws = new WebSocket(wsUrl);

      return new Promise((resolve, reject) => {
        if (!this.ws) {
          reject(new Error('WebSocket not initialized'));
          return;
        }

        this.ws.onopen = () => {
          console.log('[CloudflareWebSocket] Connected to Cloudflare Workers');
          this.connectionState = {
            status: 'connected',
            lastConnected: Date.now(),
            reconnectAttempts: 0,
          };

          this.startHeartbeat();
          this.processMessageQueue();
          resolve(true);
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event);
        };

        this.ws.onclose = (event) => {
          console.log('[CloudflareWebSocket] Connection closed:', event.code, event.reason);
          this.handleDisconnection();
        };

        this.ws.onerror = (error) => {
          console.error('[CloudflareWebSocket] Connection error:', error);
          this.connectionState = {
            ...this.connectionState,
            status: 'error',
            error: 'Connection failed',
          };
          reject(error);
        };

        // Connection timeout
        setTimeout(() => {
          if (this.connectionState.status === 'connecting') {
            reject(new Error('Connection timeout'));
          }
        }, this.config.messageTimeout);
      });
    } catch (error) {
      console.error('[CloudflareWebSocket] Connect error:', error);
      this.connectionState = {
        ...this.connectionState,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
      return false;
    }
  }

  /**
   * Disconnect from WebSocket
   */
  public disconnect(): void {
    console.log('[CloudflareWebSocket] Disconnecting...');
    this.stopHeartbeat();
    this.stopReconnect();

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }

    this.connectionState.status = 'disconnected';
  }

  /**
   * Send message to Cloudflare Workers
   */
  public async sendMessage(message: Omit<WebSocketMessage, 'timestamp' | 'userId' | 'canvasId'>): Promise<boolean> {
    const fullMessage: WebSocketMessage = {
      ...message,
      timestamp: Date.now(),
      userId: this.userId,
      canvasId: this.canvasId,
    };

    if (this.connectionState.status !== 'connected') {
      console.log('[CloudflareWebSocket] Queueing message (not connected)');
      this.messageQueue.push(fullMessage);
      
      // Attempt to reconnect
      if (this.connectionState.status === 'disconnected') {
        this.attemptReconnect();
      }
      
      return false;
    }

    try {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify(fullMessage));
        return true;
      } else {
        this.messageQueue.push(fullMessage);
        return false;
      }
    } catch (error) {
      console.error('[CloudflareWebSocket] Send message error:', error);
      this.messageQueue.push(fullMessage);
      return false;
    }
  }

  /**
   * Broadcast generation event to other users
   */
  public async broadcastGenerationEvent(event: GenerationEvent): Promise<boolean> {
    return this.sendMessage({
      type: 'generation_event',
      payload: event,
      generationId: event.generationId,
    });
  }

  /**
   * Set current generation ID for context
   */
  public setGenerationId(generationId: string): void {
    this.currentGenerationId = generationId;
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);

      console.log('[CloudflareWebSocket] Received message:', message.type);

      switch (message.type) {
        case 'generation_event':
          this.handleGenerationEvent(message.payload);
          break;

        case 'user_joined':
        case 'user_join':
          this.emitToListeners('user_joined', message.payload);
          break;

        case 'user_left':
        case 'user_leave':
          this.emitToListeners('user_left', message.payload);
          break;

        case 'state_response':
          console.log('[CloudflareWebSocket] Received initial state:', message);
          this.emitToListeners('state_response', message);
          break;

        case 'heartbeat':
          // Heartbeat response - connection is alive
          break;

        case 'error':
          console.error('[CloudflareWebSocket] Server error:', message.payload);
          this.emitToListeners('error', message.payload);
          break;

        default:
          console.warn('[CloudflareWebSocket] Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('[CloudflareWebSocket] Message parsing error:', error);
    }
  }

  /**
   * Handle generation events from other users
   */
  private handleGenerationEvent(event: GenerationEvent): void {
    // Don't process events from this user (avoid loops)
    if (event.userId === this.userId) {
      return;
    }

    // Emit to local event bus for other components to handle
    generationEventBus.publish(event, {
      source: 'collaboration',
      shouldBroadcast: false, // Already broadcasted
      shouldAutoSave: false,  // Don't auto-save collaborative events
    });

    // Emit to local listeners
    this.emitToListeners('generation_event', event);
  }

  /**
   * Handle disconnection and attempt reconnect
   */
  private handleDisconnection(): void {
    this.stopHeartbeat();
    this.connectionState.status = 'disconnected';

    // Attempt reconnect if not manually disconnected
    if (this.connectionState.reconnectAttempts < this.config.reconnectAttempts) {
      this.attemptReconnect();
    }
  }

  /**
   * Attempt to reconnect
   */
  private attemptReconnect(): void {
    if (this.connectionState.status === 'reconnecting') {
      return;
    }

    this.connectionState.status = 'reconnecting';
    this.connectionState.reconnectAttempts++;

    const delay = this.config.reconnectDelay * this.connectionState.reconnectAttempts;

    console.log(`[CloudflareWebSocket] Reconnecting in ${delay}ms (attempt ${this.connectionState.reconnectAttempts})`);

    this.reconnectTimer = setTimeout(async () => {
      try {
        await this.connect();
      } catch (error) {
        console.error('[CloudflareWebSocket] Reconnect failed:', error);
        
        if (this.connectionState.reconnectAttempts < this.config.reconnectAttempts) {
          this.attemptReconnect();
        } else {
          console.error('[CloudflareWebSocket] Max reconnect attempts reached');
          this.connectionState.status = 'error';
          this.connectionState.error = 'Max reconnect attempts reached';
        }
      }
    }, delay);
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      this.sendMessage({
        type: 'heartbeat',
        payload: { timestamp: Date.now() },
      });
    }, this.config.heartbeatInterval);
  }

  /**
   * Stop heartbeat timer
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * Stop reconnect timer
   */
  private stopReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * Process queued messages
   */
  private processMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      if (message) {
        this.sendMessage(message);
      }
    }
  }

  /**
   * Add event listener
   */
  public addEventListener(event: string, listener: (data: any) => void): () => void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    
    this.eventListeners.get(event)!.add(listener);

    // Return unsubscribe function
    return () => {
      this.eventListeners.get(event)?.delete(listener);
    };
  }

  /**
   * Emit to local listeners
   */
  private emitToListeners(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`[CloudflareWebSocket] Listener error for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Get connection state
   */
  public getConnectionState(): ConnectionState {
    return { ...this.connectionState };
  }

  /**
   * Get queue status
   */
  public getQueueStatus(): { queued: number; connected: boolean } {
    return {
      queued: this.messageQueue.length,
      connected: this.connectionState.status === 'connected',
    };
  }
}
