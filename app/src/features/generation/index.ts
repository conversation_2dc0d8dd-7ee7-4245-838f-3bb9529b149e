/**
 * 🎯 Generation Feature - MAIN EXPORT
 * @description Clean, modern generation system with race-condition-free pipeline
 * @responsibility Central export point for all generation functionality
 * @ai_context 100% Cloudflare Workers WebSocket system, no legacy WASP dependencies
 */

// ============================================================================
// DOMAIN EXPORTS
// ============================================================================

// Events
export {
  GenerationEventType,
  GenerationState,
  type GenerationEvent,
  type LoadingCardCreatedEvent,
  type ConceptCardCreatedEvent,
  type ConceptCardAnsweredEvent,
  type FinalGenerationStartedEvent,
  type FinalGenerationProgressEvent,
  type FinalGenerationCompletedEvent,
  type StateTransitionEvent,
  type AutoSaveTriggeredEvent,
  type GenerationEventMetadata,
  type GenerationEventWithMetadata,
} from './domain/events/GenerationEvents';

export {
  generationEventBus,
  type EventListener,
  type EventSubscription,
} from './domain/events/EventBus';

// State Management
export {
  GenerationStateMachine,
  type GenerationStateContext,
  type StateTransition,
} from './domain/state/GenerationStateMachine';

// Services
export {
  autoSaveService,
  type AutoSaveConfig,
  type SaveOperation,
  type SaveResult,
} from './domain/services/AutoSaveService';

// ============================================================================
// APPLICATION EXPORTS
// ============================================================================

// Use Cases
export {
  StartGenerationUseCase,
  createStartGenerationUseCase,
  startProductPhotography,
  startAdPhotography,
  type StartGenerationRequest,
  type StartGenerationResponse,
} from './application/usecases/StartGeneration';

export {
  CreateConceptUseCase,
  createCreateConceptUseCase,
  type CreateConceptRequest,
  type CreateConceptResponse,
  type AIConceptResult,
} from './application/usecases/CreateConcept';

export {
  GenerateImageUseCase,
  createGenerateImageUseCase,
  generateFromConcept,
  type GenerateImageRequest,
  type GenerateImageResponse,
  type AIImageResult,
} from './application/usecases/GenerateImage';

// ============================================================================
// INFRASTRUCTURE EXPORTS
// ============================================================================

// WebSocket
export {
  CloudflareWebSocketClient,
  type CloudflareWebSocketConfig,
  type WebSocketMessage,
  type ConnectionState,
} from './infrastructure/websocket/CloudflareWebSocket';

// ============================================================================
// PRESENTATION EXPORTS
// ============================================================================

// Hooks
export {
  useGeneration,
  type UseGenerationConfig,
  type GenerationInstance,
  type UseGenerationReturn,
} from './presentation/hooks/useGeneration';

// Components
export {
  GenerationChatBubble,
  type GenerationChatBubbleProps,
  type GenerationTypeOption,
} from './presentation/components';

// ============================================================================
// CONVENIENCE EXPORTS
// ============================================================================

/**
 * Quick setup function for basic generation functionality
 */
export function setupGeneration(config: {
  userId: string;
  canvasId: string;
  autoConnect?: boolean;
  enableAutoSave?: boolean;
}) {
  // Import services dynamically to avoid circular dependencies
  const { generationEventBus } = require('./domain/events/EventBus');
  const { autoSaveService } = require('./domain/services/AutoSaveService');
  const { StartGenerationUseCase } = require('./application/usecases/StartGeneration');
  const { CreateConceptUseCase } = require('./application/usecases/CreateConcept');
  const { CloudflareWebSocketClient } = require('./infrastructure/websocket/CloudflareWebSocket');

  return {
    // Event bus for listening to generation events
    eventBus: generationEventBus,

    // Auto-save service for persistence
    autoSave: autoSaveService,

    // Use cases for direct usage
    useCases: {
      startGeneration: new StartGenerationUseCase(),
      createConcept: new CreateConceptUseCase(),
    },

    // WebSocket client for real-time updates
    createWebSocketClient: () => new CloudflareWebSocketClient(config.userId, config.canvasId),
  };
}

/**
 * Type guard to check if an event is a generation event
 */
export function isGenerationEvent(event: any): boolean {
  return event &&
         typeof event.type === 'string' &&
         typeof event.timestamp === 'number' &&
         typeof event.userId === 'string' &&
         typeof event.canvasId === 'string' &&
         typeof event.generationId === 'string';
}

// ============================================================================
// VERSION INFO
// ============================================================================

export const GENERATION_SYSTEM_VERSION = '1.0.0';
export const GENERATION_SYSTEM_NAME = 'Clean Generation Pipeline';

// ============================================================================
// MIGRATION HELPERS
// ============================================================================

/**
 * Helper to migrate from legacy WASP WebSocket events to new generation events
 */
export function migrateLegacyEvent(legacyEvent: any): any | null {
  // TODO: Implement migration logic for existing events
  // This would help transition from the old system to the new one
  console.warn('[Generation] Legacy event migration not yet implemented:', legacyEvent);
  return null;
}

/**
 * Helper to check if the generation system is properly initialized
 */
export function validateGenerationSystem(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check environment variables
  if (!import.meta.env.VITE_CANVAS_WORKERS_URL) {
    errors.push('VITE_CANVAS_WORKERS_URL environment variable is required');
  }

  // Check WebSocket support
  if (typeof WebSocket === 'undefined') {
    errors.push('WebSocket support is required');
  }

  // Check event bus
  try {
    const { generationEventBus } = require('./domain/events/EventBus');
    generationEventBus.getSubscriptions();
  } catch (error) {
    errors.push('Generation event bus is not properly initialized');
  }

  // Check auto-save service
  try {
    const { autoSaveService } = require('./domain/services/AutoSaveService');
    autoSaveService.getQueueStatus();
  } catch (error) {
    warnings.push('Auto-save service may not be properly initialized');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

// ============================================================================
// DEBUG HELPERS
// ============================================================================

/**
 * Debug helper to get current system status
 */
export function getGenerationSystemStatus() {
  const { generationEventBus } = require('./domain/events/EventBus');
  const { autoSaveService } = require('./domain/services/AutoSaveService');

  return {
    version: GENERATION_SYSTEM_VERSION,
    eventBus: {
      subscriptions: generationEventBus.getSubscriptions().size,
      history: generationEventBus.getEventHistory().length,
    },
    autoSave: autoSaveService.getQueueStatus(),
    validation: validateGenerationSystem(),
  };
}

/**
 * Debug helper to clear all generation data
 */
export function clearGenerationSystem() {
  console.warn('[Generation] Clearing all generation system data');

  // Clear event history
  const { generationEventBus } = require('./domain/events/EventBus');
  generationEventBus.clearHistory();

  // Note: Auto-save queue will be cleared naturally as operations complete

  console.log('[Generation] Generation system cleared');
}
