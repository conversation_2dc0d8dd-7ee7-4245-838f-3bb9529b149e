/**
 * 💡 Create Concept Use Case - STEP 2 OF PIPELINE
 * @description Transforms loading card into concept card with AI-generated content
 * @responsibility Handles AI concept generation → concept card creation → triggers final generation readiness
 * @ai_context Second step in sequential race-condition-free generation pipeline
 */

import { 
  GenerationEventType,
  ConceptCardCreatedEvent,
  GenerationState 
} from '../../domain/events/GenerationEvents';
import { generationEventBus } from '../../domain/events/EventBus';
import { GenerationStateMachine } from '../../domain/state/GenerationStateMachine';

// ============================================================================
// USE CASE INTERFACES
// ============================================================================

export interface CreateConceptRequest {
  generationId: string;
  userId: string;
  canvasId: string;
  userPrompt: string;
  generationType: 'product_photography' | 'ad_photography';
  position: { x: number; y: number };
  loadingCardId: string;
  referenceImages: string[];
  brandContext?: {
    brandName?: string;
    brandColors?: string[];
    brandStyle?: string;
  };
}

export interface CreateConceptResponse {
  success: boolean;
  conceptCardId: string;
  concept: string;
  prompt: string;
  questions: string[];
  suggestionChips: string[];
  previewImageUrl?: string;
  error?: string;
}

export interface AIConceptResult {
  concept: string;
  refinedPrompt: string;
  questions: string[];
  suggestionChips: string[];
  previewImageUrl?: string;
}

// ============================================================================
// CREATE CONCEPT USE CASE
// ============================================================================

export class CreateConceptUseCase {
  /**
   * Execute the create concept use case
   */
  public async execute(request: CreateConceptRequest): Promise<CreateConceptResponse> {
    try {
      console.log('[CreateConcept] Starting concept generation:', {
        generationId: request.generationId,
        generationType: request.generationType,
        prompt: request.userPrompt.substring(0, 100) + '...',
      });

      // Generate unique concept card ID
      const conceptCardId = this.generateConceptCardId();

      // Validate request
      const validationError = this.validateRequest(request);
      if (validationError) {
        return {
          success: false,
          conceptCardId,
          concept: '',
          prompt: '',
          questions: [],
          suggestionChips: [],
          error: validationError,
        };
      }

      // Step 1: Generate AI concept
      const aiResult = await this.generateAIConcept(request);
      if (!aiResult) {
        return {
          success: false,
          conceptCardId,
          concept: '',
          prompt: '',
          questions: [],
          suggestionChips: [],
          error: 'Failed to generate AI concept',
        };
      }

      // Step 2: Create concept card event
      const conceptCardEvent: ConceptCardCreatedEvent = {
        type: GenerationEventType.CONCEPT_CARD_CREATED,
        timestamp: Date.now(),
        userId: request.userId,
        canvasId: request.canvasId,
        generationId: request.generationId,
        payload: {
          cardId: conceptCardId,
          loadingCardId: request.loadingCardId,
          position: request.position,
          concept: aiResult.concept,
          prompt: aiResult.refinedPrompt,
          questions: aiResult.questions,
          previewImageUrl: aiResult.previewImageUrl,
          suggestionChips: aiResult.suggestionChips,
        },
      };

      // Step 3: Publish concept card created event
      await generationEventBus.publish(conceptCardEvent, {
        source: 'ai',
        priority: 'high',
        shouldBroadcast: true,
        shouldAutoSave: true,
        retryable: true,
      });

      // Step 4: Update state machine (if available)
      await this.updateStateMachine(request.generationId, conceptCardId, aiResult);

      console.log('[CreateConcept] Concept card created successfully:', {
        generationId: request.generationId,
        conceptCardId,
        concept: aiResult.concept.substring(0, 100) + '...',
      });

      return {
        success: true,
        conceptCardId,
        concept: aiResult.concept,
        prompt: aiResult.refinedPrompt,
        questions: aiResult.questions,
        suggestionChips: aiResult.suggestionChips,
        previewImageUrl: aiResult.previewImageUrl,
      };
    } catch (error) {
      console.error('[CreateConcept] Execution error:', error);

      return {
        success: false,
        conceptCardId: 'error',
        concept: '',
        prompt: '',
        questions: [],
        suggestionChips: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Validate the create concept request
   */
  private validateRequest(request: CreateConceptRequest): string | null {
    if (!request.generationId) {
      return 'Generation ID is required';
    }

    if (!request.userId) {
      return 'User ID is required';
    }

    if (!request.canvasId) {
      return 'Canvas ID is required';
    }

    if (!request.userPrompt || request.userPrompt.trim().length === 0) {
      return 'User prompt is required';
    }

    if (!request.loadingCardId) {
      return 'Loading card ID is required';
    }

    if (!['product_photography', 'ad_photography'].includes(request.generationType)) {
      return 'Invalid generation type';
    }

    return null;
  }

  /**
   * Generate AI concept using OpenRouter/GPT-4
   */
  private async generateAIConcept(request: CreateConceptRequest): Promise<AIConceptResult | null> {
    try {
      console.log('[CreateConcept] Generating AI concept...');

      // Build AI prompt based on generation type
      const systemPrompt = this.buildSystemPrompt(request.generationType, request.brandContext);
      const userPrompt = this.buildUserPrompt(request);

      // TODO: Integrate with OpenRouter/GPT-4 API
      // This would call your existing AI generation service
      // For now, return mock data that follows the expected structure

      const mockResult: AIConceptResult = {
        concept: this.generateMockConcept(request),
        refinedPrompt: this.generateMockRefinedPrompt(request),
        questions: this.generateMockQuestions(request.generationType),
        suggestionChips: this.generateMockSuggestionChips(request.generationType),
        previewImageUrl: undefined, // Will be generated later
      };

      // Simulate AI processing delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      console.log('[CreateConcept] AI concept generated successfully');
      return mockResult;
    } catch (error) {
      console.error('[CreateConcept] AI concept generation error:', error);
      return null;
    }
  }

  /**
   * Build system prompt for AI generation
   */
  private buildSystemPrompt(generationType: string, brandContext?: any): string {
    const basePrompt = `You are an expert creative director specializing in ${generationType.replace('_', ' ')}.`;
    
    let prompt = basePrompt + '\n\n';
    
    if (generationType === 'product_photography') {
      prompt += `Create compelling product photography concepts that:
- Highlight product features and benefits
- Use professional lighting and composition
- Consider target audience and use cases
- Incorporate brand elements naturally`;
    } else {
      prompt += `Create engaging advertising photography concepts that:
- Tell a compelling brand story
- Evoke emotional connection
- Drive action and engagement
- Align with brand values and messaging`;
    }

    if (brandContext?.brandName) {
      prompt += `\n\nBrand Context: ${brandContext.brandName}`;
      if (brandContext.brandColors) {
        prompt += `\nBrand Colors: ${brandContext.brandColors.join(', ')}`;
      }
      if (brandContext.brandStyle) {
        prompt += `\nBrand Style: ${brandContext.brandStyle}`;
      }
    }

    return prompt;
  }

  /**
   * Build user prompt for AI generation
   */
  private buildUserPrompt(request: CreateConceptRequest): string {
    let prompt = `User Request: ${request.userPrompt}\n\n`;
    
    if (request.referenceImages.length > 0) {
      prompt += `Reference Images: ${request.referenceImages.length} provided\n\n`;
    }

    prompt += `Please provide:
1. A creative concept description
2. A refined photography prompt
3. 3-5 clarifying questions to improve the concept
4. 5-8 suggestion chips for variations`;

    return prompt;
  }

  /**
   * Update state machine with concept data
   */
  private async updateStateMachine(
    generationId: string,
    conceptCardId: string,
    aiResult: AIConceptResult
  ): Promise<void> {
    try {
      // TODO: Retrieve state machine instance for this generation
      // For now, create a new one (in real implementation, this would be retrieved from a store)
      
      console.log('[CreateConcept] State machine updated for generation:', generationId);
    } catch (error) {
      console.error('[CreateConcept] State machine update error:', error);
    }
  }

  /**
   * Generate unique concept card ID
   */
  private generateConceptCardId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    return `concept_${timestamp}_${random}`;
  }

  // ============================================================================
  // MOCK DATA GENERATORS (Replace with real AI integration)
  // ============================================================================

  private generateMockConcept(request: CreateConceptRequest): string {
    const concepts = {
      product_photography: [
        "A clean, minimalist product shot with dramatic lighting that emphasizes the product's premium quality and craftsmanship.",
        "An lifestyle scene showing the product in use, creating an emotional connection with the target audience.",
        "A creative flat lay composition that tells the product's story through carefully curated props and styling.",
      ],
      ad_photography: [
        "A bold, eye-catching composition that immediately communicates the brand's value proposition and personality.",
        "An emotional storytelling scene that connects the product to aspirational lifestyle moments.",
        "A dynamic action shot that demonstrates the product's benefits in a compelling, energetic way.",
      ],
    };

    const typeKey = request.generationType as keyof typeof concepts;
    const conceptList = concepts[typeKey] || concepts.product_photography;
    return conceptList[Math.floor(Math.random() * conceptList.length)];
  }

  private generateMockRefinedPrompt(request: CreateConceptRequest): string {
    return `Professional ${request.generationType.replace('_', ' ')} of ${request.userPrompt}, shot with high-end camera equipment, perfect lighting, commercial quality, 8K resolution, award-winning photography`;
  }

  private generateMockQuestions(generationType: string): string[] {
    const questions = {
      product_photography: [
        "What's the primary use case you want to highlight?",
        "Should we focus on technical features or emotional benefits?",
        "What's your target audience demographic?",
        "Do you prefer clean minimalist or lifestyle context?",
      ],
      ad_photography: [
        "What emotion should this ad evoke?",
        "Who is your target customer?",
        "What's the main call-to-action?",
        "Should we emphasize product features or lifestyle benefits?",
        "What's your brand's personality?",
      ],
    };

    const typeKey = generationType as keyof typeof questions;
    return questions[typeKey] || questions.product_photography;
  }

  private generateMockSuggestionChips(generationType: string): string[] {
    const chips = {
      product_photography: [
        "Add lifestyle context",
        "Focus on details",
        "Include size reference",
        "Show multiple angles",
        "Add brand elements",
        "Use dramatic lighting",
        "Create flat lay",
        "Show in use",
      ],
      ad_photography: [
        "Add emotional story",
        "Include people",
        "Show transformation",
        "Add call-to-action",
        "Use bold colors",
        "Create urgency",
        "Show benefits",
        "Add social proof",
      ],
    };

    const typeKey = generationType as keyof typeof chips;
    return chips[typeKey] || chips.product_photography;
  }
}

// ============================================================================
// FACTORY FUNCTION
// ============================================================================

export function createCreateConceptUseCase(): CreateConceptUseCase {
  return new CreateConceptUseCase();
}
