/**
 * 🎨 Generate Image Use Case
 * 
 * @description Handles the final image generation step of the pipeline
 * @responsibility Convert concept card to final generated images
 * @ai_context This is Step 3 of the generation pipeline: Concept → Final Images
 */

import { generationEventBus } from '../../domain/events/EventBus';
import { GenerationEventType, type FinalGenerationCompletedEvent } from '../../domain/events/GenerationEvents';
import { GenerationStateMachine } from '../../domain/state/GenerationStateMachine';

// ============================================================================
// INTERFACES
// ============================================================================

export interface GenerateImageRequest {
  generationId: string;
  conceptCardId: string;
  userId: string;
  canvasId: string;
  prompt: string;
  concept: string;
  numImages: number;
  referenceImages?: string[];
  aspectRatio?: '1024x1024' | '1536x1024' | '1024x1536';
  brandContext?: {
    brandName?: string;
    brandColors?: string[];
    brandStyle?: string;
  };
}

export interface GenerateImageResponse {
  success: boolean;
  imageUrls: string[];
  generationId: string;
  conceptCardId: string;
  error?: string;
}

export interface AIImageResult {
  imageUrls: string[];
  metadata: {
    prompt: string;
    model: string;
    processingTime: number;
    aspectRatio: string;
    numImages: number;
  };
}

// ============================================================================
// GENERATE IMAGE USE CASE
// ============================================================================

export class GenerateImageUseCase {
  /**
   * Execute the generate image use case
   */
  public async execute(request: GenerateImageRequest): Promise<GenerateImageResponse> {
    try {
      console.log('[GenerateImage] Starting image generation:', {
        generationId: request.generationId,
        conceptCardId: request.conceptCardId,
        numImages: request.numImages,
        prompt: request.prompt.substring(0, 100) + '...',
      });

      // Validate request
      const validationError = this.validateRequest(request);
      if (validationError) {
        return {
          success: false,
          imageUrls: [],
          generationId: request.generationId,
          conceptCardId: request.conceptCardId,
          error: validationError,
        };
      }

      // Step 1: Generate AI images
      const aiResult = await this.generateAIImages(request);
      if (!aiResult) {
        return {
          success: false,
          imageUrls: [],
          generationId: request.generationId,
          conceptCardId: request.conceptCardId,
          error: 'Failed to generate AI images',
        };
      }

      // Step 2: Create final generation completed event
      const imageEvent: FinalGenerationCompletedEvent = {
        type: GenerationEventType.FINAL_GENERATION_COMPLETED,
        timestamp: Date.now(),
        userId: request.userId,
        canvasId: request.canvasId,
        generationId: request.generationId,
        payload: {
          taskId: `task_${request.conceptCardId}`,
          cardId: request.conceptCardId,
          imageUrls: aiResult.imageUrls,
          finalPrompt: request.prompt,
          metadata: aiResult.metadata,
        },
      };

      // Step 3: Publish image generated event
      await generationEventBus.publish(imageEvent, {
        source: 'ai',
        priority: 'high',
        shouldBroadcast: true,
        shouldAutoSave: true,
        retryable: true,
      });

      // Step 4: Update state machine (if available)
      await this.updateStateMachine(request.generationId, request.conceptCardId, aiResult);

      console.log('[GenerateImage] Images generated successfully:', {
        generationId: request.generationId,
        conceptCardId: request.conceptCardId,
        imageCount: aiResult.imageUrls.length,
      });

      return {
        success: true,
        imageUrls: aiResult.imageUrls,
        generationId: request.generationId,
        conceptCardId: request.conceptCardId,
      };
    } catch (error) {
      console.error('[GenerateImage] Execution error:', error);

      return {
        success: false,
        imageUrls: [],
        generationId: request.generationId,
        conceptCardId: request.conceptCardId,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Validate the generate image request
   */
  private validateRequest(request: GenerateImageRequest): string | null {
    if (!request.generationId) {
      return 'Generation ID is required';
    }

    if (!request.conceptCardId) {
      return 'Concept card ID is required';
    }

    if (!request.userId) {
      return 'User ID is required';
    }

    if (!request.canvasId) {
      return 'Canvas ID is required';
    }

    if (!request.prompt || request.prompt.trim().length === 0) {
      return 'Prompt is required';
    }

    if (!request.concept || request.concept.trim().length === 0) {
      return 'Concept is required';
    }

    if (!request.numImages || request.numImages < 1 || request.numImages > 5) {
      return 'Number of images must be between 1 and 5';
    }

    return null;
  }

  /**
   * Generate AI images using the existing image generation service
   */
  private async generateAIImages(request: GenerateImageRequest): Promise<AIImageResult | null> {
    try {
      console.log('[GenerateImage] Generating AI images...');

      // TODO: Integrate with existing image generation service
      // This would call your existing generateImage function from the server
      // For now, return mock data that follows the expected structure

      const mockResult: AIImageResult = {
        imageUrls: this.generateMockImageUrls(request.numImages),
        metadata: {
          prompt: request.prompt,
          model: 'flux-1.1-pro',
          processingTime: 5000,
          aspectRatio: request.aspectRatio || '1024x1024',
          numImages: request.numImages,
        },
      };

      // Simulate AI processing delay
      await new Promise(resolve => setTimeout(resolve, 3000));

      console.log('[GenerateImage] AI images generated successfully');
      return mockResult;
    } catch (error) {
      console.error('[GenerateImage] AI image generation error:', error);
      return null;
    }
  }

  /**
   * Update state machine with image data
   */
  private async updateStateMachine(
    generationId: string,
    conceptCardId: string,
    aiResult: AIImageResult
  ): Promise<void> {
    try {
      // TODO: Retrieve state machine instance for this generation
      // For now, just log the update (in real implementation, this would update the state machine)

      console.log('[GenerateImage] State machine updated for generation:', generationId);
    } catch (error) {
      console.error('[GenerateImage] State machine update error:', error);
    }
  }

  /**
   * Generate mock image URLs for testing
   */
  private generateMockImageUrls(numImages: number): string[] {
    const urls: string[] = [];
    for (let i = 0; i < numImages; i++) {
      urls.push(`https://via.placeholder.com/1024x1024/9EA581/FFFFFF?text=Generated+Image+${i + 1}`);
    }
    return urls;
  }
}

// ============================================================================
// FACTORY FUNCTION
// ============================================================================

export function createGenerateImageUseCase(): GenerateImageUseCase {
  return new GenerateImageUseCase();
}

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

/**
 * Quick image generation for concept cards
 */
export async function generateFromConcept(
  conceptCardId: string,
  generationId: string,
  userId: string,
  canvasId: string,
  prompt: string,
  concept: string,
  options: {
    numImages?: number;
    referenceImages?: string[];
    aspectRatio?: '1024x1024' | '1536x1024' | '1024x1536';
    brandContext?: {
      brandName?: string;
      brandColors?: string[];
      brandStyle?: string;
    };
  } = {}
): Promise<GenerateImageResponse> {
  const useCase = new GenerateImageUseCase();
  
  return useCase.execute({
    generationId,
    conceptCardId,
    userId,
    canvasId,
    prompt,
    concept,
    numImages: options.numImages || 1,
    referenceImages: options.referenceImages,
    aspectRatio: options.aspectRatio || '1024x1024',
    brandContext: options.brandContext,
  });
}
