/**
 * 🚀 Start Generation Use Case - STEP 1 OF PIPELINE
 * @description Initiates the generation pipeline with loading card creation
 * @responsibility Handles user prompt → loading card → triggers concept generation
 * @ai_context First step in sequential race-condition-free generation pipeline
 */

import { 
  GenerationEventType,
  LoadingCardCreatedEvent,
  GenerationState 
} from '../../domain/events/GenerationEvents';
import { generationEventBus } from '../../domain/events/EventBus';
import { GenerationStateMachine } from '../../domain/state/GenerationStateMachine';
import { autoSaveService } from '../../domain/services/AutoSaveService';

// ============================================================================
// USE CASE INTERFACES
// ============================================================================

export interface StartGenerationRequest {
  userId: string;
  canvasId: string;
  userPrompt: string;
  position: { x: number; y: number };
  requestText: string;
  generationType: 'product_photography' | 'ad_photography';
  referenceImages?: string[];
  brandContext?: {
    brandName?: string;
    brandColors?: string[];
    brandStyle?: string;
  };
}

export interface StartGenerationResponse {
  success: boolean;
  generationId: string;
  loadingCardId: string;
  error?: string;
  stateMachine?: GenerationStateMachine;
}

// ============================================================================
// START GENERATION USE CASE
// ============================================================================

export class StartGenerationUseCase {
  /**
   * Execute the start generation use case
   */
  public async execute(request: StartGenerationRequest): Promise<StartGenerationResponse> {
    try {
      console.log('[StartGeneration] Starting generation pipeline:', {
        userId: request.userId,
        canvasId: request.canvasId,
        generationType: request.generationType,
        prompt: request.userPrompt.substring(0, 100) + '...',
      });

      // Generate unique IDs
      const generationId = this.generateGenerationId();
      const loadingCardId = this.generateLoadingCardId();

      // Create state machine for this generation
      const stateMachine = new GenerationStateMachine(
        generationId,
        request.canvasId,
        request.userId,
        GenerationState.IDLE
      );

      // Validate request
      const validationError = this.validateRequest(request);
      if (validationError) {
        return {
          success: false,
          generationId,
          loadingCardId,
          error: validationError,
        };
      }

      // Step 1: Transition to LOADING state
      const transitionSuccess = await stateMachine.transition('START_GENERATION', {
        loadingCard: {
          cardId: loadingCardId,
          position: request.position,
          requestText: request.requestText,
          userPrompt: request.userPrompt,
          createdAt: Date.now(),
        },
      });

      if (!transitionSuccess) {
        return {
          success: false,
          generationId,
          loadingCardId,
          error: 'Failed to transition to loading state',
        };
      }

      // Step 2: Create loading card event
      const loadingCardEvent: LoadingCardCreatedEvent = {
        type: GenerationEventType.LOADING_CARD_CREATED,
        timestamp: Date.now(),
        userId: request.userId,
        canvasId: request.canvasId,
        generationId,
        payload: {
          cardId: loadingCardId,
          position: request.position,
          requestText: request.requestText,
          userPrompt: request.userPrompt,
        },
      };

      // Step 3: Publish loading card created event
      await generationEventBus.publish(loadingCardEvent, {
        source: 'user',
        priority: 'high',
        shouldBroadcast: true,
        shouldAutoSave: true,
        retryable: true,
      });

      // Step 4: Trigger concept generation (async)
      this.triggerConceptGeneration(generationId, request, stateMachine);

      console.log('[StartGeneration] Loading card created successfully:', {
        generationId,
        loadingCardId,
        state: stateMachine.getState(),
      });

      return {
        success: true,
        generationId,
        loadingCardId,
        stateMachine,
      };
    } catch (error) {
      console.error('[StartGeneration] Execution error:', error);

      return {
        success: false,
        generationId: 'error',
        loadingCardId: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Validate the start generation request
   */
  private validateRequest(request: StartGenerationRequest): string | null {
    if (!request.userId) {
      return 'User ID is required';
    }

    if (!request.canvasId) {
      return 'Canvas ID is required';
    }

    if (!request.userPrompt || request.userPrompt.trim().length === 0) {
      return 'User prompt is required';
    }

    if (request.userPrompt.length > 2000) {
      return 'User prompt is too long (max 2000 characters)';
    }

    if (!request.position || typeof request.position.x !== 'number' || typeof request.position.y !== 'number') {
      return 'Valid position coordinates are required';
    }

    if (!['product_photography', 'ad_photography'].includes(request.generationType)) {
      return 'Invalid generation type';
    }

    return null;
  }

  /**
   * Trigger concept generation (Step 2 of pipeline)
   */
  private async triggerConceptGeneration(
    generationId: string,
    request: StartGenerationRequest,
    stateMachine: GenerationStateMachine
  ): Promise<void> {
    try {
      // Import CreateConcept use case dynamically to avoid circular dependencies
      const { CreateConceptUseCase } = await import('./CreateConcept');
      const createConceptUseCase = new CreateConceptUseCase();

      // Prepare concept generation request
      const conceptRequest = {
        generationId,
        userId: request.userId,
        canvasId: request.canvasId,
        userPrompt: request.userPrompt,
        generationType: request.generationType,
        position: request.position,
        referenceImages: request.referenceImages || [],
        brandContext: request.brandContext,
        loadingCardId: stateMachine.getContext().loadingCard?.cardId || '',
      };

      // Execute concept generation
      const conceptResult = await createConceptUseCase.execute(conceptRequest);

      if (!conceptResult.success) {
        console.error('[StartGeneration] Concept generation failed:', conceptResult.error);
        
        // Transition to error state
        await stateMachine.transition('ERROR', {
          error: {
            message: conceptResult.error || 'Concept generation failed',
            code: 'CONCEPT_GENERATION_ERROR',
            step: GenerationState.LOADING,
            timestamp: Date.now(),
            retryable: true,
          },
        });
      }
    } catch (error) {
      console.error('[StartGeneration] Concept generation trigger error:', error);
      
      // Transition to error state
      await stateMachine.transition('ERROR', {
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          code: 'CONCEPT_TRIGGER_ERROR',
          step: GenerationState.LOADING,
          timestamp: Date.now(),
          retryable: true,
        },
      });
    }
  }

  /**
   * Generate unique generation ID
   */
  private generateGenerationId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    return `gen_${timestamp}_${random}`;
  }

  /**
   * Generate unique loading card ID
   */
  private generateLoadingCardId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    return `loading_${timestamp}_${random}`;
  }

  /**
   * Get generation status
   */
  public async getGenerationStatus(generationId: string): Promise<{
    state: GenerationState;
    context: any;
  } | null> {
    try {
      // TODO: Implement status retrieval from state store
      // This would query the state machine or database for current status
      return null;
    } catch (error) {
      console.error('[StartGeneration] Status retrieval error:', error);
      return null;
    }
  }

  /**
   * Cancel generation
   */
  public async cancelGeneration(generationId: string, userId: string): Promise<boolean> {
    try {
      console.log('[StartGeneration] Cancelling generation:', generationId);

      // TODO: Implement cancellation logic
      // This would:
      // 1. Find the state machine for this generation
      // 2. Transition to cancelled/error state
      // 3. Clean up any pending operations
      // 4. Broadcast cancellation event

      return true;
    } catch (error) {
      console.error('[StartGeneration] Cancellation error:', error);
      return false;
    }
  }
}

// ============================================================================
// FACTORY FUNCTION
// ============================================================================

export function createStartGenerationUseCase(): StartGenerationUseCase {
  return new StartGenerationUseCase();
}

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

/**
 * Quick start generation for common use cases
 */
export async function startProductPhotography(
  userId: string,
  canvasId: string,
  userPrompt: string,
  position: { x: number; y: number },
  options: {
    referenceImages?: string[];
    brandName?: string;
    brandColors?: string[];
  } = {}
): Promise<StartGenerationResponse> {
  const useCase = new StartGenerationUseCase();
  
  return useCase.execute({
    userId,
    canvasId,
    userPrompt,
    position,
    requestText: userPrompt,
    generationType: 'product_photography',
    referenceImages: options.referenceImages,
    brandContext: {
      brandName: options.brandName,
      brandColors: options.brandColors,
      brandStyle: 'modern',
    },
  });
}

/**
 * Quick start generation for ad photography
 */
export async function startAdPhotography(
  userId: string,
  canvasId: string,
  userPrompt: string,
  position: { x: number; y: number },
  options: {
    referenceImages?: string[];
    brandName?: string;
    brandColors?: string[];
    brandStyle?: string;
  } = {}
): Promise<StartGenerationResponse> {
  const useCase = new StartGenerationUseCase();
  
  return useCase.execute({
    userId,
    canvasId,
    userPrompt,
    position,
    requestText: userPrompt,
    generationType: 'ad_photography',
    referenceImages: options.referenceImages,
    brandContext: {
      brandName: options.brandName,
      brandColors: options.brandColors,
      brandStyle: options.brandStyle || 'modern',
    },
  });
}
