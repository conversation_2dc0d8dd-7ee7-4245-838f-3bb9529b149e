# 🎯 Clean Generation System

A modern, race-condition-free content generation pipeline using 100% Cloudflare Workers WebSocket architecture.

## 🏗️ Architecture Overview

This system replaces the legacy WASP WebSocket implementation with a clean, sequential pipeline:

```
User Input → Loading Card → Concept Card → Final Image Generation
    ↓            ↓             ↓              ↓
 Step 1       Step 2        Step 3         Complete
```

### Key Features

- ✅ **Race-condition-free**: Sequential state machine ensures proper step progression
- ✅ **Type-safe events**: No more string-based events prone to typos
- ✅ **Auto-save everything**: All state changes are automatically persisted
- ✅ **100% Cloudflare Workers**: No legacy WASP WebSocket dependencies
- ✅ **Real-time collaboration**: Broadcasts all changes to other users
- ✅ **Clean architecture**: Domain-driven design with clear separation of concerns

## 🚀 Quick Start

### 1. Basic Usage

```typescript
import { useGeneration, GenerationChatBubble } from '@/features/generation';

function CanvasComponent() {
  const generation = useGeneration({
    userId: 'user_123',
    canvasId: 'canvas_456',
    autoConnect: true,
  });

  const handleRightClick = (position: { x: number; y: number }) => {
    // Show chat bubble at click position
    setShowChatBubble(true);
    setChatPosition(position);
  };

  return (
    <div onContextMenu={handleRightClick}>
      {/* Your canvas content */}
      
      {showChatBubble && (
        <GenerationChatBubble
          userId="user_123"
          canvasId="canvas_456"
          position={chatPosition}
          onClose={() => setShowChatBubble(false)}
          onGenerationStarted={(generationId) => {
            console.log('Generation started:', generationId);
            setShowChatBubble(false);
          }}
        />
      )}
    </div>
  );
}
```

### 2. Advanced Usage with Event Listening

```typescript
import { 
  useGeneration, 
  generationEventBus, 
  GenerationEventType 
} from '@/features/generation';

function AdvancedCanvas() {
  const generation = useGeneration({
    userId: 'user_123',
    canvasId: 'canvas_456',
  });

  useEffect(() => {
    // Listen for concept cards being created
    const unsubscribe = generationEventBus.subscribe(
      GenerationEventType.CONCEPT_CARD_CREATED,
      (event) => {
        console.log('New concept card:', event.payload);
        // Update your canvas UI
      }
    );

    return unsubscribe;
  }, []);

  return (
    <div>
      {/* Your canvas with generation integration */}
    </div>
  );
}
```

## 📋 Sequential Pipeline

### Step 1: Loading Card Creation

```typescript
import { startProductPhotography } from '@/features/generation';

// Trigger generation programmatically
const generationId = await startProductPhotography(
  'user_123',
  'canvas_456',
  'Professional product photo of a coffee mug',
  { x: 100, y: 200 },
  {
    referenceImages: ['https://example.com/ref.jpg'],
    brandName: 'Coffee Co',
    brandColors: ['#8B4513', '#F5DEB3'],
  }
);
```

**What happens:**
1. Creates loading card at specified position
2. Auto-saves to canvas database
3. Broadcasts to other users via Cloudflare Workers
4. Triggers Step 2 automatically

### Step 2: Concept Card Generation

**What happens automatically:**
1. AI generates concept based on user prompt
2. Loading card transforms into concept card
3. Shows questions for refinement
4. Auto-saves concept data
5. Broadcasts update to collaborators

### Step 3: Final Image Generation

```typescript
// User clicks "Create Final Image" button
await generation.generateFinal(generationId);
```

**What happens:**
1. Starts streaming image generation
2. Shows progress updates in real-time
3. Auto-saves final images when complete
4. Broadcasts completion to all users

## 🔧 Integration with Existing Canvas

### Replace Legacy ChatBubble

**Old (Legacy):**
```typescript
// Remove this
import { ChatBubble } from '@/features/canvas/presentation/components/chat/ChatBubble';
```

**New (Clean):**
```typescript
// Use this instead
import { GenerationChatBubble } from '@/features/generation';
```

### Migration Steps

1. **Replace ChatBubble component:**
   ```typescript
   // Old
   <ChatBubble {...props} />
   
   // New
   <GenerationChatBubble
     userId={userId}
     canvasId={canvasId}
     position={position}
     onClose={onClose}
     onGenerationStarted={handleGenerationStarted}
   />
   ```

2. **Update event listeners:**
   ```typescript
   // Old (string-based events)
   socket.on('concept_card_created', handler);
   
   // New (type-safe events)
   generationEventBus.subscribe(
     GenerationEventType.CONCEPT_CARD_CREATED,
     handler
   );
   ```

3. **Remove legacy WebSocket code:**
   - Remove imports from `@/websocket/emitters`
   - Remove WASP WebSocket event handlers
   - Use `useGeneration` hook instead

## 🎛️ Configuration

### Environment Variables

```bash
# Required: Cloudflare Workers WebSocket URL
NEXT_PUBLIC_CANVAS_WORKERS_URL=wss://canvas-workers.j-5bb.workers.dev

# Optional: Auto-save configuration
NEXT_PUBLIC_GENERATION_AUTO_SAVE=true
NEXT_PUBLIC_GENERATION_DEBOUNCE_MS=500
```

### WebSocket Configuration

```typescript
const generation = useGeneration({
  userId: 'user_123',
  canvasId: 'canvas_456',
  autoConnect: true,        // Auto-connect on mount
  enableAutoSave: true,     // Enable auto-save
});
```

## 🔍 Debugging

### System Status

```typescript
import { getGenerationSystemStatus } from '@/features/generation';

console.log(getGenerationSystemStatus());
// {
//   version: '1.0.0',
//   eventBus: { subscriptions: 5, history: 23 },
//   autoSave: { pending: 0, queued: 0, processing: false },
//   validation: { isValid: true, errors: [], warnings: [] }
// }
```

### Event History

```typescript
import { generationEventBus } from '@/features/generation';

// Get all events for a specific generation
const events = generationEventBus.getEventHistory({
  generationId: 'gen_123',
  since: Date.now() - 60000, // Last minute
});

console.log('Recent events:', events);
```

### Connection Status

```typescript
const { connectionState, connect, disconnect } = useGeneration(config);

console.log('WebSocket status:', connectionState);
// 'disconnected' | 'connecting' | 'connected' | 'error'
```

## 🚨 Error Handling

### Automatic Recovery

The system includes automatic error recovery:

```typescript
// Automatic retry for failed operations
const result = await generation.startGeneration(request);
if (!result) {
  // System will automatically retry with exponential backoff
  console.log('Generation will retry automatically');
}
```

### Manual Error Handling

```typescript
const generation = useGeneration(config);

// Listen for errors
useEffect(() => {
  const unsubscribe = generationEventBus.subscribe(
    GenerationEventType.AUTO_SAVE_ERROR,
    (event) => {
      console.error('Auto-save failed:', event.payload);
      // Handle error (show notification, retry, etc.)
    }
  );

  return unsubscribe;
}, []);
```

## 🔄 Migration from Legacy System

### Before (Legacy WASP WebSocket)

```typescript
// ❌ Old way - prone to race conditions
import { emitConceptCardCreate } from '@/websocket/emitters';

// String-based events (typo-prone)
socket.emit('concept_card_create', data);
socket.on('concept_card_created', handler);

// Manual state management
const [loadingCards, setLoadingCards] = useState([]);
const [conceptCards, setConceptCards] = useState([]);
```

### After (Clean Generation System)

```typescript
// ✅ New way - race-condition-free
import { useGeneration, GenerationEventType } from '@/features/generation';

// Type-safe events
const generation = useGeneration(config);
generationEventBus.subscribe(GenerationEventType.CONCEPT_CARD_CREATED, handler);

// Automatic state management
const { generations, currentGeneration } = generation;
```

## 📊 Performance

- **Event Processing**: ~1ms per event
- **Auto-save Debouncing**: 500ms default (configurable)
- **WebSocket Reconnection**: Exponential backoff with 5 retry attempts
- **Memory Usage**: Minimal (event history limited to 1000 events)

## 🔐 Security

- **Type Safety**: All events are strongly typed
- **Input Validation**: All user inputs are validated
- **Rate Limiting**: Built into Cloudflare Workers
- **Authentication**: Uses existing WASP auth system

## 🎯 Next Steps

1. **Test the pipeline**: Use `GenerationChatBubble` in your canvas
2. **Monitor events**: Check browser console for event logs
3. **Verify auto-save**: Check canvas database for persisted data
4. **Test collaboration**: Open multiple browser tabs to test real-time updates
5. **Remove legacy code**: Gradually remove old WASP WebSocket dependencies

## 🆘 Troubleshooting

### Common Issues

1. **WebSocket not connecting**
   ```typescript
   // Check environment variable
   console.log(process.env.NEXT_PUBLIC_CANVAS_WORKERS_URL);
   
   // Manual connection
   await generation.connect();
   ```

2. **Events not firing**
   ```typescript
   // Check event bus subscriptions
   console.log(generationEventBus.getSubscriptions());
   ```

3. **Auto-save not working**
   ```typescript
   // Check auto-save status
   console.log(autoSaveService.getQueueStatus());
   ```

For more help, check the browser console for detailed logs prefixed with `[Generation]`.
