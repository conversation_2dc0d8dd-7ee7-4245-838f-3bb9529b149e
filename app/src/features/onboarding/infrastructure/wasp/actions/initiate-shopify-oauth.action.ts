import { type InitiateShopifyOAuth } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import crypto from 'crypto';

// Shopify App Configuration
const SHOPIFY_APP_CONFIG = {
  apiKey: process.env.SHOPIFY_API_KEY!,
  apiSecret: process.env.SHOPIFY_API_SECRET!,
  scopes: ['read_products', 'read_product_listings', 'read_inventory'].join(','),
  redirectUri: `${process.env.APP_URL}/api/shopify/oauth/callback`,
};

type Input = {
  shop: string;
  organizationId: string;
};

type Output = {
  authUrl: string;
  state: string;
  shop: string;
};

// Store OAuth state temporarily (in production, use Redis or database)
const oauthStates = new Map<
  string,
  {
    userId: number;
    organizationId: string;
    shop: string;
    timestamp: number;
  }
>();

export const initiateShopifyOAuth: InitiateShopifyOAuth<Input, Output> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User must be authenticated');
  }

  const { shop, organizationId } = args;

  try {
    // Validate shop domain
    const shopDomain = validateShopDomain(shop);

    // Generate state parameter for security
    const state = crypto.randomBytes(32).toString('hex');

    // Store state and user info for callback validation
    oauthStates.set(state, {
      userId: typeof context.user.id === 'number' ? context.user.id : parseInt(context.user.id, 10),
      organizationId,
      shop: shopDomain,
      timestamp: Date.now(),
    });

    // Clean up old states (older than 10 minutes)
    cleanupOldStates();

    // Build Shopify OAuth URL
    const authUrl = buildShopifyAuthUrl(shopDomain, state);

    console.log(`[ShopifyOAuth] Initiated OAuth for shop: ${shopDomain}`);

    return {
      authUrl,
      state,
      shop: shopDomain,
    };
  } catch (error) {
    console.error('[ShopifyOAuth] OAuth initiation failed:', error);
    throw new HttpError(500, `Shopify OAuth initiation failed: ${error.message}`);
  }
};

function validateShopDomain(shop: string): string {
  // Remove protocol if present
  let cleanShop = shop.replace(/^https?:\/\//, '');

  // Add .myshopify.com if not present
  if (!cleanShop.includes('.myshopify.com')) {
    cleanShop = `${cleanShop}.myshopify.com`;
  }

  // Validate format
  const shopRegex = /^[a-zA-Z0-9][a-zA-Z0-9\-]*\.myshopify\.com$/;
  if (!shopRegex.test(cleanShop)) {
    throw new Error('Invalid shop domain format');
  }

  return cleanShop;
}

function buildShopifyAuthUrl(shop: string, state: string): string {
  const params = new URLSearchParams({
    client_id: SHOPIFY_APP_CONFIG.apiKey,
    scope: SHOPIFY_APP_CONFIG.scopes,
    redirect_uri: SHOPIFY_APP_CONFIG.redirectUri,
    state: state,
    'grant_options[]': 'per-user',
  });

  return `https://${shop}/admin/oauth/authorize?${params.toString()}`;
}

function cleanupOldStates(): void {
  const tenMinutesAgo = Date.now() - 10 * 60 * 1000;

  for (const [state, data] of oauthStates.entries()) {
    if (data.timestamp < tenMinutesAgo) {
      oauthStates.delete(state);
    }
  }
}

// Export for use in callback
export { oauthStates, SHOPIFY_APP_CONFIG };
