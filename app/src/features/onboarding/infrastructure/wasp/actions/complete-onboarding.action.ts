import { HttpError } from 'wasp/server';
import type { OnboardingFormData } from '../../../domain/types';

type Args = OnboardingFormData & {
  organizationId: string;
};

export const completeOnboarding = async (args: Args, context: any) => {
  if (!context.user) {
    throw new HttpError(401, 'Not authenticated');
  }

  try {
    // Update user's onboarding status
    await context.entities.User.update({
      where: { id: context.user.id },
      data: {
        hasCompletedOnboarding: true,
      },
    });

    // You can add additional logic here to:
    // - Save onboarding data to organization
    // - Trigger initial data generation
    // - Set up default settings
    // - Send welcome emails, etc.

    return { success: true };
  } catch (error) {
    console.error('Error completing onboarding:', error);
    throw new HttpError(500, 'Failed to complete onboarding');
  }
};
