import { type ImportShopifyProducts } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { getProductBrief } from '../../../../products/infrastructure/wasp/actions/getProductBrief';
import { generateProductDescription } from '../../../../products/infrastructure/wasp/actions/generateProductDescription';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import sharp from 'sharp';
import axios from 'axios';

// Cloudflare R2 Configuration
const r2Client = new S3Client({
  region: 'auto',
  endpoint: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.R2_BUCKET_ACCESS_KEY!,
    secretAccessKey: process.env.R2_BUCKET_SECRET_KEY!,
  },
});

const getR2ImageUrl = (folderPath: string, fileName: string) =>
  `${process.env.R2_PUBLIC_URL}/${folderPath}/${fileName}`;

type Input = {
  organizationId: string;
  selectedProductIds: string[];
};

type Output = {
  importedCount: number;
  skippedCount: number;
  errors: string[];
};

export const importShopifyProducts: ImportShopifyProducts<Input, Output> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User must be authenticated');
  }

  const { organizationId, selectedProductIds } = args;

  try {
    // Get Shopify integration
    const integration = await context.entities.ShopifyIntegration.findFirst({
      where: {
        userId: context.user.id,
        organizationId: organizationId,
        status: 'connected',
      },
    });

    if (!integration) {
      throw new HttpError(404, 'No connected Shopify store found');
    }

    let importedCount = 0;
    let skippedCount = 0;
    const errors: string[] = [];
    const createdProducts: any[] = [];

    // Import each selected product
    for (const productId of selectedProductIds) {
      try {
        // Fetch detailed product data from Shopify
        const productData = await fetchShopifyProduct(integration.shop, integration.accessToken, productId);

        if (!productData) {
          errors.push(`Product ${productId} not found`);
          skippedCount++;
          continue;
        }

        // Check if product already exists
        const existingProduct = await context.entities.Product.findFirst({
          where: {
            organizationId: organizationId,
            sku: `shopify-${productId}`,
          },
        });

        if (existingProduct) {
          console.log(`[ShopifyImport] Product ${productId} already exists, skipping`);
          skippedCount++;
          continue;
        }

        // Create product in our database with basic Shopify data
        const newProduct = await context.entities.Product.create({
          data: {
            name: productData.title,
            productType: productData.product_type || 'General',
            brandName: productData.vendor || 'Unknown',
            description: stripHtml(productData.body_html || ''),
            price: parseFloat(productData.variants[0]?.price || '0'),
            currency: 'USD', // Default currency
            originalUrl: `https://${integration.shop}/products/${productData.handle}`,
            metadata: {
              shopifyId: productData.id,
              shopifyHandle: productData.handle,
              shopifyStatus: productData.status,
              importedAt: new Date().toISOString(),
              shop: integration.shop,
              source: 'shopify',
            },
            availability: productData.status === 'active' ? 'In Stock' : 'Out of Stock',
            features: [], // Will be generated by AI analysis
            dimensions: '', // Will be analyzed
            format: '', // Will be analyzed
            targetAudience: '', // Will be generated by AI
            usp: '', // Will be generated by AI
            keywords: [productData.product_type, productData.vendor].filter(Boolean),
            certifications: [],
            legalDisclaimers: [],
            images: productData.images.map((img: any) => img.src), // Initial Shopify images
            videos: [],
            shippingOptions: [],
            returnPolicy: '',
            reviews: JSON.stringify([]),
            ratings: JSON.stringify([]),
            customerSupport: '',
            manufacturer: productData.vendor || '',
            releaseDate: productData.created_at || new Date().toISOString(),
            sku: `shopify-${productData.id}`,
            userId: context.user.id,
            organizationId: organizationId,
            // Analysis tracking fields
            analysisStatus: 'PENDING',
            analysisStartedAt: null,
            analysisCompletedAt: null,
          },
        });

        createdProducts.push(newProduct);
        importedCount++;
        console.log(`[ShopifyImport] Successfully imported product: ${productData.title}`);
      } catch (error) {
        console.error(`[ShopifyImport] Failed to import product ${productId}:`, error);
        errors.push(`Failed to import product ${productId}: ${error.message}`);
        skippedCount++;
      }
    }

    // Update last sync time
    await context.entities.ShopifyIntegration.update({
      where: { id: integration.id },
      data: { lastSyncAt: new Date() },
    });

    // Start background AI analysis for imported products
    if (createdProducts.length > 0) {
      console.log(`[ShopifyImport] Starting background AI analysis for ${createdProducts.length} products`);

      // Process each product in the background (don't await)
      processShopifyProductsAnalysis(createdProducts, context).catch((error) => {
        console.error('[ShopifyImport] Background analysis failed:', error);
      });
    }

    console.log(
      `[ShopifyImport] Import completed: ${importedCount} imported, ${skippedCount} skipped, ${errors.length} errors`
    );

    return {
      importedCount,
      skippedCount,
      errors,
    };
  } catch (error) {
    console.error('[ShopifyImport] Import failed:', error);
    throw new HttpError(500, `Failed to import Shopify products: ${error.message}`);
  }
};

async function fetchShopifyProduct(shop: string, accessToken: string, productId: string): Promise<any> {
  const url = `https://${shop}/admin/api/2023-10/products/${productId}.json`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'X-Shopify-Access-Token': accessToken,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    if (response.status === 404) {
      return null;
    }
    const errorText = await response.text();
    throw new Error(`Shopify API error: ${response.status} ${errorText}`);
  }

  const data = await response.json();
  return data.product;
}

function stripHtml(html: string): string {
  // Simple HTML stripping - in production, use a proper HTML parser
  return html.replace(/<[^>]*>/g, '').trim();
}

// Background analysis processing for Shopify products
async function processShopifyProductsAnalysis(products: any[], context: any): Promise<void> {
  console.log(`[ShopifyAnalysis] Starting analysis for ${products.length} Shopify products`);

  for (const product of products) {
    try {
      console.log(`[ShopifyAnalysis] Analyzing product: ${product.name}`);

      // Update analysis status
      await context.entities.Product.update({
        where: { id: product.id },
        data: {
          analysisStatus: 'IN_PROGRESS',
          analysisStartedAt: new Date(),
        },
      });

      // Step 1: Process Shopify images and upload to R2 (no scraping needed - we have the data!)
      const processedImages = await processShopifyImages(product.images, product.id, product.userId);

      // Step 2: Generate comprehensive product brief (marketing copy, target audience, USP, etc.)
      const brief = await getProductBrief(
        {
          productId: product.id,
          productName: product.name,
          description: product.description, // Use existing Shopify description
          type: product.productType,
          features: product.features || [],
          cleanedHTML: '', // Shopify products don't have raw HTML
        },
        context
      );

      // Step 3: Update product with processed images and AI-generated data
      const updatedProduct = await context.entities.Product.update({
        where: { id: product.id },
        data: {
          // Update with processed R2 images
          images: processedImages,

          // Update with AI-generated brief data
          targetAudience: brief.targetAudience?.primary?.demographics || '',
          usp: brief.coreMessaging?.keyAttributes?.uniqueSellingProposition || '',
          keywords: [...product.keywords, ...(brief.coreMessaging?.marketingPillars || [])].filter(Boolean),

          // Analysis completion
          analysisStatus: 'COMPLETED',
          analysisCompletedAt: new Date(),
        },
      });

      // Step 4: Generate detailed visual description (AI analysis of product images)
      try {
        if (updatedProduct.images && updatedProduct.images.length >= 2) {
          await generateProductDescription({ productId: updatedProduct.id }, context);
          console.log(`[ShopifyAnalysis] Generated detailed description for: ${product.name}`);
        }
      } catch (error) {
        console.error(`[ShopifyAnalysis] Failed to generate description for ${product.name}:`, error);
      }

      console.log(`[ShopifyAnalysis] Completed analysis for: ${product.name}`);
    } catch (error) {
      console.error(`[ShopifyAnalysis] Failed to analyze product ${product.name}:`, error);

      // Update analysis status to failed
      await context.entities.Product.update({
        where: { id: product.id },
        data: {
          analysisStatus: 'FAILED',
          analysisCompletedAt: new Date(),
        },
      }).catch(console.error);
    }
  }

  console.log(`[ShopifyAnalysis] Completed analysis for all ${products.length} products`);
}

// Process Shopify images: download from Shopify CDN and upload to R2
async function processShopifyImages(shopifyImageUrls: string[], productId: number, userId: number): Promise<string[]> {
  const processedImages: string[] = [];

  console.log(`[ShopifyImages] Processing ${shopifyImageUrls.length} images for product ${productId}`);

  for (const imageUrl of shopifyImageUrls) {
    try {
      // Download image from Shopify CDN
      const response = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
        maxRedirects: 5,
        timeout: 10000, // 10 second timeout
      });
      const buffer = Buffer.from(response.data);

      // Convert to JPG and optimize
      const jpgBuffer = await sharp(buffer).jpeg({ quality: 90 }).toBuffer();

      // Generate unique filename
      const uniqueFileName = `shopify-${Date.now()}-${Math.random().toString(36).substring(7)}.jpg`;
      const folderPath = `Products/${userId}/${productId}`;

      // Upload to R2
      const params = {
        Bucket: process.env.R2_BUCKET_NAME!,
        Key: `${folderPath}/${uniqueFileName}`,
        Body: jpgBuffer,
        ContentType: 'image/jpeg',
      };

      const command = new PutObjectCommand(params);
      await r2Client.send(command);

      // Get R2 URL
      const r2Url = getR2ImageUrl(folderPath, uniqueFileName);
      processedImages.push(r2Url);

      console.log(`[ShopifyImages] Processed image: ${imageUrl} → ${r2Url}`);
    } catch (error) {
      console.error(`[ShopifyImages] Failed to process image ${imageUrl}:`, error);
      // Continue with other images if one fails
    }
  }

  console.log(`[ShopifyImages] Successfully processed ${processedImages.length}/${shopifyImageUrls.length} images`);
  return processedImages;
}
