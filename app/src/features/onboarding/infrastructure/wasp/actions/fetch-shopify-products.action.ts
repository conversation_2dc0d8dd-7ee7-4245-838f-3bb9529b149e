import { type FetchShopifyProducts } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';

interface ShopifyProduct {
  id: number;
  title: string;
  body_html: string;
  vendor: string;
  product_type: string;
  handle: string;
  status: string;
  images: Array<{
    id: number;
    src: string;
    alt?: string;
  }>;
  variants: Array<{
    id: number;
    title: string;
    price: string;
    sku?: string;
    inventory_quantity?: number;
  }>;
}

interface ShopifyProductsResponse {
  products: ShopifyProduct[];
}

type Input = {
  organizationId: string;
  limit?: number;
};

type Output = {
  products: Array<{
    id: string;
    name: string;
    description: string;
    price: number;
    currency: string;
    imageUrl?: string;
    productUrl: string;
    vendor: string;
    productType: string;
    variants: Array<{
      id: string;
      title: string;
      price: number;
      sku?: string;
      inventory?: number;
    }>;
    selected: boolean;
  }>;
  shop: string;
  totalProducts: number;
};

export const fetchShopifyProducts: FetchShopifyProducts<Input, Output> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User must be authenticated');
  }

  const { organizationId, limit = 50 } = args;

  try {
    // Get Shopify integration for this user/organization
    const integration = await context.entities.ShopifyIntegration.findFirst({
      where: {
        userId: context.user.id,
        organizationId: organizationId,
        status: 'connected',
      },
    });

    if (!integration) {
      throw new HttpError(404, 'No connected Shopify store found');
    }

    // Fetch products from Shopify API
    const products = await fetchProductsFromShopify(integration.shop, integration.accessToken, limit);

    // Transform products to our format
    const transformedProducts = products.map((product) => ({
      id: product.id.toString(),
      name: product.title,
      description: stripHtml(product.body_html || ''),
      price: parseFloat(product.variants[0]?.price || '0'),
      currency: 'USD', // Default, could be fetched from shop info
      imageUrl: product.images[0]?.src,
      productUrl: `https://${integration.shop}/products/${product.handle}`,
      vendor: product.vendor,
      productType: product.product_type,
      variants: product.variants.map((variant) => ({
        id: variant.id.toString(),
        title: variant.title,
        price: parseFloat(variant.price),
        sku: variant.sku,
        inventory: variant.inventory_quantity,
      })),
      selected: false,
    }));

    console.log(`[ShopifyClient] Fetched ${transformedProducts.length} products from ${integration.shop}`);

    return {
      products: transformedProducts,
      shop: integration.shop,
      totalProducts: transformedProducts.length,
    };
  } catch (error) {
    console.error('[ShopifyClient] Failed to fetch products:', error);
    throw new HttpError(500, `Failed to fetch Shopify products: ${error.message}`);
  }
};

async function fetchProductsFromShopify(shop: string, accessToken: string, limit: number): Promise<ShopifyProduct[]> {
  const url = `https://${shop}/admin/api/2023-10/products.json?limit=${limit}&status=active`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'X-Shopify-Access-Token': accessToken,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Shopify API error: ${response.status} ${errorText}`);
  }

  const data: ShopifyProductsResponse = await response.json();
  return data.products || [];
}

function stripHtml(html: string): string {
  // Simple HTML stripping - in production, use a proper HTML parser
  return html.replace(/<[^>]*>/g, '').trim();
}
