/**
 * 🤖 AI Synthesis Action - MODULAR VERSION
 *
 * @description Orchestrates AI-powered onboarding data generation
 * @responsibility Coordinates website analysis, brand kit generation, and audience creation
 * @dependencies OpenRouter provider, progress tracker, existing analysis actions
 * @ai_context This is the modular version of AI synthesis specifically for onboarding
 *
 * @example
 * ```typescript
 * const result = await aiSynthesis({
 *   organizationId: 'org_123',
 *   websiteUrl: 'https://example.com',
 *   companyName: 'Example Corp',
 *   businessType: 'ecommerce'
 * }, context);
 * ```
 */

import { HttpError } from 'wasp/server';
import type { OnboardingFormData } from '../../../domain/types';
import { createOpenRouterProvider, OpenRouterProvider } from '../../../../../core/ai/providers';
import { createProgressTracker, SYNTHESIS_STAGES } from '../../../../../core/websocket/progress-tracker';
import { startSynthesisSession } from '../../../../../server/actions/onboarding/synthesisWebSocket';

// Import existing analysis actions
import { analyzeWebsite } from '../../../../../server/actions/onboarding/websiteAnalysis';

// Import modular actions
import { importAudience } from '../../../../audience/infrastructure/wasp/actions/import-audience.action';
import { importBrandKitFromOnboarding } from '../../../../brand-kits/infrastructure/wasp/actions/import-brand-kit-from-onboarding';

// Import PDF processing utilities
import { convertPdfToImages, cleanupGcsImages } from '../../../../../server/utils/pdfToImages';
import { analyzeBrandKit } from '../../../../brand-kits/infrastructure/wasp/utils/analyze-brand-kit';

/**
 * 📝 AI Synthesis Input - Compatible with legacy SynthesisInput
 */
type AISynthesisInput = {
  organizationId: string;
  websiteUrl: string;
  brandName?: string;
  brandGuideFile?: string;
  shopifyIntegration?: {
    connected: boolean;
    domain?: string;
    data?: any;
  };
  assetFolderIntegration?: {
    connected: boolean;
    providers?: string[];
    data?: any;
  };
  // Additional onboarding fields
  businessType?: string;
  companyName?: string;
  industry?: string;
  targetAudience?: string;
  brandValues?: string;
  primaryGoals?: string;
  shopifyStore?: string;
  brandGuideUrl?: string;
  [key: string]: any; // Index signature for SuperJSON compatibility
};

/**
 * 📊 AI Synthesis Result
 */
interface AISynthesisResult {
  success: boolean;
  sessionId: string;
  brandKitId?: string;
  audienceIds?: number[];
  websiteAnalysis?: any;
  errors?: string[];
  totalProcessingTime: number;
  [key: string]: any; // Index signature for SuperJSON compatibility
}

/**
 * 🤖 AI Synthesis Action
 */
export const aiSynthesis = async (args: AISynthesisInput, context: any): Promise<AISynthesisResult> => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const startTime = Date.now();
  const sessionId = `synthesis_${Date.now()}_${context.user.id}`;

  console.log(`[AISynthesis] Starting synthesis session: ${sessionId}`);

  // Create progress tracker with appropriate stages
  const stages: Array<{ id: string; name: string; weight: number }> = [];

  // Always include website analysis if URL provided
  if (args.websiteUrl) {
    stages.push({ ...SYNTHESIS_STAGES[0] }); // website_analysis
  }

  // Include brand guide processing if provided
  if (args.brandGuideUrl || args.brandGuideFile) {
    stages.push({ ...SYNTHESIS_STAGES[1] }); // brand_guide_processing
  }

  // Include Shopify analysis if provided
  if (args.shopifyStore) {
    stages.push({ ...SYNTHESIS_STAGES[2] }); // shopify_analysis
  }

  // Always include brand kit and audience generation
  stages.push({ ...SYNTHESIS_STAGES[3] }); // brand_kit_generation
  stages.push({ ...SYNTHESIS_STAGES[4] }); // audience_generation

  const progressTracker = createProgressTracker(sessionId, context.user.id, stages);

  try {
    await progressTracker.start('Starting Creative Synthesis...');

    // Start persistent session tracking
    await startSynthesisSession(sessionId, context.user.id, args.organizationId);

    // Create synthesis coordinator
    const coordinator = new SynthesisCoordinator({
      sessionId,
      userId: context.user.id,
      organizationId: args.organizationId,
      formData: args,
      context,
      progressTracker,
    });

    // Execute synthesis pipeline
    const result = await coordinator.execute();

    await progressTracker.complete('Creative Synthesis completed successfully!');

    // Mark session as completed in database
    try {
      await context.entities.OnboardingSession.updateMany({
        where: {
          sessionId,
          userId: context.user.id,
          status: 'running',
        },
        data: {
          status: 'completed',
          updatedAt: new Date(),
        },
      });
      console.log(`[AISynthesis] Marked session ${sessionId} as completed`);
    } catch (error) {
      console.warn('[AISynthesis] Failed to update session status (non-critical):', error);
    }

    return {
      success: true,
      sessionId,
      brandKitId: result.brandKitId,
      audienceIds: result.audienceIds,
      websiteAnalysis: result.websiteAnalysis,
      totalProcessingTime: Date.now() - startTime,
    };
  } catch (error) {
    console.error('[AISynthesis] Synthesis failed:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    await progressTracker.fail(errorMessage);

    return {
      success: false,
      sessionId,
      errors: [errorMessage],
      totalProcessingTime: Date.now() - startTime,
    };
  }
};

/**
 * 🎯 Synthesis Coordinator
 */
class SynthesisCoordinator {
  private readonly sessionId: string;
  private readonly userId: number;
  private readonly organizationId: string;
  private readonly formData: AISynthesisInput;
  private readonly context: any;
  private readonly progressTracker: any;
  private readonly openRouter: any;

  private results: Record<string, any> = {};

  constructor(config: {
    sessionId: string;
    userId: number;
    organizationId: string;
    formData: AISynthesisInput;
    context: any;
    progressTracker: any;
  }) {
    this.sessionId = config.sessionId;
    this.userId = config.userId;
    this.organizationId = config.organizationId;
    this.formData = config.formData;
    this.context = config.context;
    this.progressTracker = config.progressTracker;

    // Initialize OpenRouter provider with onboarding-specific model
    const onboardingModel = process.env.ONBOARDING_AI_MODEL || process.env.OPENROUTER_DEFAULT_MODEL;

    if (!onboardingModel) {
      throw new Error(
        'AI model not configured for onboarding. Set ONBOARDING_AI_MODEL or OPENROUTER_DEFAULT_MODEL environment variable.'
      );
    }

    this.openRouter = new OpenRouterProvider({
      apiKey: process.env.OPENROUTER_API_KEY!,
      defaultModel: onboardingModel,
      siteUrl: process.env.SITE_URL || 'https://olivia.ai',
      siteName: process.env.SITE_NAME || 'Olivia AI Design Assistant',
    });
  }

  /**
   * 🚀 Execute synthesis pipeline
   */
  async execute(): Promise<{
    brandKitId?: string;
    audienceIds?: number[];
    websiteAnalysis?: any;
  }> {
    console.log('[SynthesisCoordinator] Form data received:', {
      websiteUrl: this.formData.websiteUrl,
      brandGuideUrl: this.formData.brandGuideUrl,
      brandGuideFile: this.formData.brandGuideFile,
      brandGuideFileName: this.formData.brandGuideFileName,
      organizationId: this.organizationId,
    });

    // Stage 1: Website Content Extraction (clean HTML to get pure text content)
    if (this.formData.websiteUrl) {
      await this.executeWebsiteContentExtraction();
    }

    // Stage 2: Brand Guide Processing (if provided)
    console.log('[SynthesisCoordinator] Checking brand guide conditions:');
    console.log('  - brandGuideUrl:', this.formData.brandGuideUrl);
    console.log('  - brandGuideFile:', this.formData.brandGuideFile);
    console.log('  - Should process brand guide:', !!(this.formData.brandGuideUrl || this.formData.brandGuideFile));

    if (this.formData.brandGuideUrl || this.formData.brandGuideFile) {
      console.log('[SynthesisCoordinator] ✅ Brand guide processing condition met, starting processing...');
      await this.executeBrandGuideProcessing();
    } else {
      console.log('[SynthesisCoordinator] ❌ No brand guide data found, skipping brand guide processing');
    }

    // Stage 3: Shopify Analysis (if provided)
    if (this.formData.shopifyStore || this.formData.shopifyIntegration?.connected) {
      await this.executeShopifyAnalysis();
    }

    // Stage 4 & 5: Parallel Generation (ASYNC OPTIMIZATION!)
    console.log('[SynthesisCoordinator] 🚀 Starting parallel brand kit and audience generation...');

    // Start both processes in parallel
    const [brandKitId, audienceIds] = await Promise.all([
      this.executeBrandKitGeneration(),
      this.executeAudienceGenerationAsync(), // New async version that doesn't need brandKitId
    ]);

    console.log('[SynthesisCoordinator] ✅ Parallel generation completed!');
    console.log(`  - Brand Kit ID: ${brandKitId}`);
    console.log(`  - Audience IDs: ${audienceIds?.length || 0} audiences created`);

    return {
      brandKitId,
      audienceIds,
      websiteAnalysis: this.results.websiteAnalysis,
    };
  }

  /**
   * 🧹 Execute website content extraction (clean HTML to get pure text)
   */
  private async executeWebsiteContentExtraction(): Promise<void> {
    await this.progressTracker.updateStage('website_analysis', 0, 'Extracting website content...');

    try {
      console.log('[SynthesisCoordinator] Starting website content extraction...');
      console.log('[SynthesisCoordinator] Website URL:', this.formData.websiteUrl);

      // Fetch the website HTML
      const response = await fetch(this.formData.websiteUrl!);
      if (!response.ok) {
        throw new Error(`Failed to fetch website: ${response.status} ${response.statusText}`);
      }

      const html = await response.text();
      console.log(`[SynthesisCoordinator] Fetched HTML: ${html.length} characters`);

      await this.progressTracker.updateStage('website_analysis', 50, 'Cleaning HTML content...');

      // Clean HTML to extract text content while preserving logo URLs
      const cleanedContent = this.cleanHtmlContent(html);
      console.log(`[SynthesisCoordinator] Cleaned content: ${cleanedContent.length} characters`);

      // Store cleaned content for brand kit generation
      this.results.websiteContent = {
        url: this.formData.websiteUrl,
        cleanedText: cleanedContent,
        originalLength: html.length,
        cleanedLength: cleanedContent.length,
      };

      await this.progressTracker.updateStage('website_analysis', 100, 'Website content extracted', 'completed');
    } catch (error) {
      console.error('[SynthesisCoordinator] Website content extraction failed:', error);
      await this.progressTracker.addError(
        'website_analysis',
        error instanceof Error ? error.message : 'Website content extraction failed'
      );

      // Continue with fallback data
      this.results.websiteContent = {
        url: this.formData.websiteUrl,
        cleanedText: `Website content from ${this.formData.websiteUrl}`,
        error: 'Content extraction failed',
        fallback: true,
      };
    }
  }

  /**
   * 🧹 Clean HTML content to extract just text and copy
   */
  private cleanHtmlContent(html: string): string {
    try {
      // Remove script and style elements completely
      let cleaned = html.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
      cleaned = cleaned.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');

      // Remove comments
      cleaned = cleaned.replace(/<!--[\s\S]*?-->/g, '');

      // Extract text from common content elements
      const contentSelectors = [
        /<title[^>]*>(.*?)<\/title>/gi,
        /<meta[^>]*name=["']description["'][^>]*content=["'](.*?)["']/gi,
        /<h[1-6][^>]*>(.*?)<\/h[1-6]>/gi,
        /<p[^>]*>(.*?)<\/p>/gi,
        /<span[^>]*>(.*?)<\/span>/gi,
        /<div[^>]*>(.*?)<\/div>/gi,
        /<a[^>]*>(.*?)<\/a>/gi,
        /<button[^>]*>(.*?)<\/button>/gi,
        /<li[^>]*>(.*?)<\/li>/gi,
        /<td[^>]*>(.*?)<\/td>/gi,
        /<th[^>]*>(.*?)<\/th>/gi,
      ];

      let extractedText = '';

      // Extract text from each selector
      contentSelectors.forEach((selector) => {
        let match;
        while ((match = selector.exec(cleaned)) !== null) {
          if (match[1]) {
            // Remove any remaining HTML tags from the extracted text
            const cleanText = match[1].replace(/<[^>]*>/g, ' ').trim();
            if (cleanText && cleanText.length > 2) {
              extractedText += cleanText + '\n';
            }
          }
        }
      });

      // Clean up the extracted text
      extractedText = extractedText
        .replace(/\s+/g, ' ') // Replace multiple spaces with single space
        .replace(/\n\s*\n/g, '\n') // Replace multiple newlines with single newline
        .trim();

      console.log(`[SynthesisCoordinator] HTML cleaning: ${html.length} → ${extractedText.length} characters`);

      return extractedText || 'No content extracted from website';
    } catch (error) {
      console.error('[SynthesisCoordinator] HTML cleaning failed:', error);
      return 'Failed to extract website content';
    }
  }

  /**
   * 📄 Execute brand guide processing using unified PDF processing approach
   */
  private async executeBrandGuideProcessing(): Promise<void> {
    await this.progressTracker.updateStage('brand_guide_processing', 0, 'Processing brand guide...');

    try {
      console.log('[SynthesisCoordinator] Starting unified brand guide processing...');
      console.log('[SynthesisCoordinator] Organization ID:', this.organizationId);

      // 1. Get brand guide URL from either uploaded file or direct URL input
      let pdfUrl: string;
      let fileName: string;

      if (this.formData.brandGuideFile) {
        // Use uploaded file (R2 URL)
        pdfUrl = this.formData.brandGuideFile;
        fileName = this.formData.brandGuideFileName || 'brand-guide.pdf';
        console.log('[SynthesisCoordinator] Using uploaded brand guide file');
      } else if (this.formData.brandGuideUrl) {
        // Use direct URL input
        pdfUrl = this.formData.brandGuideUrl;
        fileName = 'brand-guide-from-url.pdf';
        console.log('[SynthesisCoordinator] Using brand guide URL input');
      } else {
        console.log('[SynthesisCoordinator] No brand guide file or URL found, skipping processing');
        await this.progressTracker.updateStage('brand_guide_processing', 100, 'No brand guide provided', 'completed');
        return;
      }

      console.log('[SynthesisCoordinator] Processing brand guide:', {
        fileName: fileName,
        url: pdfUrl,
        source: this.formData.brandGuideFile ? 'uploaded_file' : 'url_input',
      });

      await this.progressTracker.updateStage('brand_guide_processing', 20, 'Downloading brand guide PDF...');

      // 2. Download PDF from URL (either R2 or external URL)
      const pdfBuffer = await this.downloadPdfFromUrl(pdfUrl);

      await this.progressTracker.updateStage('brand_guide_processing', 40, 'Converting PDF to images...');

      // 3. Convert PDF to images using @hyzyla/pdfium with GCS upload
      console.log('[SynthesisCoordinator] Converting PDF to images with GCS upload...');
      const pdfResult = await convertPdfToImages(pdfBuffer, {
        scale: 2,
        saveToFile: false, // Don't save to disk in production
        imageFormat: 'png',
        useGcsUris: true, // Use Google Cloud Storage URIs for Gemini
      });

      console.log(`[SynthesisCoordinator] PDF conversion completed:`);
      console.log(`  - Pages: ${pdfResult.pageCount}`);
      console.log(`  - GCS Images: ${pdfResult.imageGcsUris.length}`);
      console.log(`  - GCS URIs:`, pdfResult.imageGcsUris);
      console.log(`  - Text content length: ${pdfResult.textContent.length}`);

      await this.progressTracker.updateStage(
        'brand_guide_processing',
        70,
        'Analyzing brand guide with Creative Synthesis...'
      );

      // 4. Store raw data for unified brand kit generation (no separate analysis)
      console.log('[SynthesisCoordinator] Storing brand guide data for unified analysis...');
      console.log(`  - Text content: ${pdfResult.textContent.length} characters`);
      console.log(`  - Images: ${pdfResult.imageGcsUris.length} GCS URIs`);

      // Store raw data instead of pre-analyzing - let the final brand kit creation see all images
      this.results.brandGuideData = {
        textContent: pdfResult.textContent,
        imageGcsUris: pdfResult.imageGcsUris,
        metadata: {
          fileName: fileName,
          pageCount: pdfResult.pageCount,
          textLength: pdfResult.textContent.length,
          imageCount: pdfResult.imageGcsUris.length,
        },
      };

      console.log('[SynthesisCoordinator] Brand guide data prepared for unified analysis');

      await this.progressTracker.updateStage(
        'brand_guide_processing',
        100,
        'Brand guide processed successfully',
        'completed'
      );

      // Note: GCS cleanup will happen after brand kit creation to avoid race conditions
    } catch (error) {
      console.error('[SynthesisCoordinator] Brand guide processing failed:', error);
      await this.progressTracker.addError(
        'brand_guide_processing',
        error instanceof Error ? error.message : 'Brand guide processing failed'
      );

      // Don't fail the entire synthesis - continue without brand guide data
      this.results.brandGuideData = null;
    }
  }

  /**
   * 📁 Get brand guide file data from database
   */
  private async getBrandGuideFile(): Promise<any> {
    try {
      const brandGuideData = await this.context.entities.BrandGuideData.findUnique({
        where: { organizationId: this.organizationId },
      });

      if (!brandGuideData || !brandGuideData.r2Url) {
        return null;
      }

      return brandGuideData;
    } catch (error) {
      console.error('[SynthesisCoordinator] Failed to get brand guide file:', error);
      return null;
    }
  }

  /**
   * ⬇️ Download PDF from URL (R2 storage or external URL)
   */
  private async downloadPdfFromUrl(pdfUrl: string): Promise<Buffer> {
    try {
      console.log(`[SynthesisCoordinator] Downloading PDF from URL: ${pdfUrl}`);

      const response = await fetch(pdfUrl);
      if (!response.ok) {
        throw new Error(`Failed to download PDF: ${response.status} ${response.statusText}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      console.log(`[SynthesisCoordinator] Downloaded PDF: ${buffer.length} bytes`);
      return buffer;
    } catch (error) {
      console.error('[SynthesisCoordinator] Failed to download PDF from URL:', error);
      throw new Error(
        `Failed to download brand guide PDF: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * �🛒 Execute Shopify analysis
   */
  private async executeShopifyAnalysis(): Promise<void> {
    await this.progressTracker.updateStage('shopify_analysis', 0, 'Analyzing Shopify store...');

    try {
      // TODO: Implement actual Shopify analysis
      // For now, create placeholder data
      this.results.shopifyData = {
        domain: this.formData.shopifyStore || this.formData.shopifyIntegration?.domain,
        products: [],
        analyzed: false,
        placeholder: true,
      };

      await this.progressTracker.updateStage('shopify_analysis', 100, 'Shopify analysis completed', 'completed');
    } catch (error) {
      console.error('[SynthesisCoordinator] Shopify analysis failed:', error);
      await this.progressTracker.addError(
        'shopify_analysis',
        error instanceof Error ? error.message : 'Shopify analysis failed'
      );
    }
  }

  /**
   * 🎨 Execute brand kit generation using Gemini AI
   */
  private async executeBrandKitGeneration(): Promise<string | undefined> {
    await this.progressTracker.updateStage('brand_kit_generation', 0, 'Generating brand kit...');

    try {
      await this.progressTracker.updateStage('brand_kit_generation', 30, 'Analyzing brand data with Brand Studio...');

      // Use our modular brand kit import action with cleaned website content and brand guide data
      const result = await importBrandKitFromOnboarding(
        {
          name: this.formData.companyName || this.formData.brandName || 'My Brand',
          organizationId: this.organizationId,
          websiteContent: this.results.websiteContent, // Pass cleaned website text content
          brandGuideData: this.results.brandGuideData, // Include brand guide images + text
          formData: this.formData,
        },
        this.context
      );

      await this.progressTracker.updateStage(
        'brand_kit_generation',
        100,
        'Brand kit generated successfully',
        'completed'
      );

      // Clean up temporary GCS images after successful brand kit creation
      if (this.results.brandGuideData?.imageGcsUris?.length > 0) {
        console.log(
          `[SynthesisCoordinator] Cleaning up ${this.results.brandGuideData.imageGcsUris.length} temporary images after successful analysis...`
        );
        cleanupGcsImages(this.results.brandGuideData.imageGcsUris).catch((error) => {
          console.warn('[SynthesisCoordinator] GCS cleanup failed (non-critical):', error);
        });
      }

      return result.id;
    } catch (error) {
      console.error('[SynthesisCoordinator] Brand kit generation failed:', error);
      await this.progressTracker.addError(
        'brand_kit_generation',
        error instanceof Error ? error.message : 'Brand kit generation failed'
      );
      throw error; // Re-throw to fail fast
    }
  }

  /**
   * 👥 Execute audience generation
   */
  private async executeAudienceGeneration(brandKitId?: string): Promise<number[]> {
    await this.progressTracker.updateStage('audience_generation', 0, 'Generating target audiences...');

    try {
      if (!brandKitId) {
        throw new Error('Brand kit required for audience generation');
      }

      // Create a product for audience generation without waiting for background analysis
      // This allows onboarding to complete quickly while product analysis happens separately
      let productToUse;

      if (this.formData.importedProducts && this.formData.importedProducts.length > 0) {
        // Use imported product data directly for audience generation (don't wait for database analysis)
        await this.progressTracker.updateStage(
          'audience_generation',
          20,
          'Creating product for audience generation...'
        );

        const importedProduct = this.formData.importedProducts[0];
        console.log(
          '[SynthesisCoordinator] Creating product from imported data for audience generation:',
          importedProduct.name
        );

        // Always create a new product for audience generation to avoid waiting for analysis
        // The actual imported products are being analyzed in the background separately
        productToUse = await this.context.entities.Product.create({
          data: {
            name: importedProduct.name,
            productType: 'product',
            description: `Imported product: ${importedProduct.name}`,
            targetAudience: this.formData.targetAudience || 'General consumers',
            usp: `High-quality ${importedProduct.name}`,
            price: 99.99,
            currency: 'USD',
            features: ['Quality', 'Reliability', 'Value'],
            availability: 'available',
            keywords: ['imported', 'onboarding'],
            certifications: [],
            legalDisclaimers: [],
            images: importedProduct.imageUrl ? [importedProduct.imageUrl] : [],
            videos: [],
            shippingOptions: [],
            reviews: '[]',
            ratings: '[]',
            customerSupport: 'Email support available',
            manufacturer: this.formData.companyName || 'Unknown',
            releaseDate: new Date().toISOString(),
            sku: `ONBOARDING-${Date.now()}`,
            originalUrl: importedProduct.productUrl,
            user: { connect: { id: this.userId } },
            organization: { connect: { id: this.organizationId } },
          },
        });
        console.log('[SynthesisCoordinator] Created onboarding product for audience generation:', productToUse.id);
      } else {
        // Fallback to sample product if no products were imported
        await this.progressTracker.updateStage('audience_generation', 20, 'Creating sample product...');

        console.log('[SynthesisCoordinator] No imported products found, creating sample product');

        productToUse = await this.context.entities.Product.create({
          data: {
            name: this.formData.companyName || this.formData.brandName || 'Sample Product',
            productType: 'service',
            description: `Sample product for ${this.formData.companyName || this.formData.brandName || 'the company'} to generate initial audiences`,
            targetAudience: this.formData.targetAudience || 'General consumers',
            usp: 'High-quality solution for customer needs',
            price: 99.99,
            currency: 'USD',
            features: ['Quality', 'Reliability', 'Value'],
            availability: 'available',
            keywords: ['sample', 'onboarding'],
            certifications: [],
            legalDisclaimers: [],
            images: [],
            videos: [],
            shippingOptions: [],
            reviews: '[]',
            ratings: '[]',
            customerSupport: 'Email support available',
            manufacturer: 'Sample Company',
            releaseDate: new Date().toISOString(),
            sku: `ONBOARDING-SAMPLE-${Date.now()}`,
            user: { connect: { id: this.userId } },
            organization: { connect: { id: this.organizationId } },
          },
        });
        console.log('[SynthesisCoordinator] Created sample product for onboarding:', productToUse.id);
      }

      await this.progressTracker.updateStage('audience_generation', 40, 'Generating audience personas...');

      // Use the modular audience import action with the selected product
      const result = await importAudience(
        {
          taskId: this.sessionId,
          brandKitId,
          productId: productToUse.id,
          organizationId: this.organizationId,
        },
        this.context
      );

      await this.progressTracker.updateStage(
        'audience_generation',
        100,
        'Audiences generated successfully',
        'completed'
      );

      return result.map((audience: any) => audience.id);
    } catch (error) {
      console.error('[SynthesisCoordinator] Audience generation failed:', error);
      await this.progressTracker.addError(
        'audience_generation',
        error instanceof Error ? error.message : 'Audience generation failed'
      );
      return [];
    }
  }

  /**
   * 👥 Execute audience generation ASYNC (doesn't wait for brand kit)
   */
  private async executeAudienceGenerationAsync(): Promise<number[]> {
    await this.progressTracker.updateStage('audience_generation', 0, 'Generating target audiences...');

    try {
      // Create a product for audience generation without waiting for brand kit
      let productToUse;

      if (this.formData.importedProducts && this.formData.importedProducts.length > 0) {
        await this.progressTracker.updateStage(
          'audience_generation',
          20,
          'Creating product for audience generation...'
        );

        const importedProduct = this.formData.importedProducts[0];
        console.log(
          '[SynthesisCoordinator] Creating product from imported data for async audience generation:',
          importedProduct.name
        );

        productToUse = await this.context.entities.Product.create({
          data: {
            name: importedProduct.name,
            productType: 'product',
            description: `Imported product: ${importedProduct.name}`,
            targetAudience: this.formData.targetAudience || 'General consumers',
            usp: `High-quality ${importedProduct.name}`,
            price: 99.99,
            currency: 'USD',
            features: ['Quality', 'Reliability', 'Value'],
            availability: 'available',
            keywords: ['imported', 'onboarding'],
            certifications: [],
            legalDisclaimers: [],
            images: importedProduct.imageUrl ? [importedProduct.imageUrl] : [],
            videos: [],
            shippingOptions: [],
            reviews: '[]',
            ratings: '[]',
            customerSupport: 'Email support available',
            manufacturer: this.formData.companyName || 'Unknown',
            releaseDate: new Date().toISOString(),
            sku: `ONBOARDING-ASYNC-${Date.now()}`,
            originalUrl: importedProduct.productUrl,
            user: { connect: { id: this.userId } },
            organization: { connect: { id: this.organizationId } },
          },
        });
      } else {
        await this.progressTracker.updateStage('audience_generation', 20, 'Creating sample product...');

        productToUse = await this.context.entities.Product.create({
          data: {
            name: this.formData.companyName || this.formData.brandName || 'Sample Product',
            productType: 'service',
            description: `Sample product for ${this.formData.companyName || this.formData.brandName || 'the company'} to generate initial audiences`,
            targetAudience: this.formData.targetAudience || 'General consumers',
            usp: 'High-quality solution for customer needs',
            price: 99.99,
            currency: 'USD',
            features: ['Quality', 'Reliability', 'Value'],
            availability: 'available',
            keywords: ['sample', 'onboarding'],
            certifications: [],
            legalDisclaimers: [],
            images: [],
            videos: [],
            shippingOptions: [],
            reviews: '[]',
            ratings: '[]',
            customerSupport: 'Email support available',
            manufacturer: 'Sample Company',
            releaseDate: new Date().toISOString(),
            sku: `ONBOARDING-ASYNC-SAMPLE-${Date.now()}`,
            user: { connect: { id: this.userId } },
            organization: { connect: { id: this.organizationId } },
          },
        });
      }

      await this.progressTracker.updateStage('audience_generation', 40, 'Generating audience personas...');

      // Generate audiences without brand kit initially - we'll link them later
      const result = await importAudience(
        {
          taskId: this.sessionId,
          // brandKitId is optional now - will be linked later when brand kit is ready
          productId: productToUse.id,
          organizationId: this.organizationId,
        },
        this.context
      );

      await this.progressTracker.updateStage(
        'audience_generation',
        100,
        'Audiences generated successfully',
        'completed'
      );

      return result.map((audience: any) => audience.id);
    } catch (error) {
      console.error('[SynthesisCoordinator] Async audience generation failed:', error);
      await this.progressTracker.addError(
        'audience_generation',
        error instanceof Error ? error.message : 'Audience generation failed'
      );
      return [];
    }
  }

  // Old OpenRouter-based methods removed - now using Gemini analysis via importBrandKitFromOnboarding
}
