import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from 'wasp/client/auth';
import { checkActiveSessions } from 'wasp/client/operations';
import { useOnboardingStore } from '../../domain/store';

interface ActiveSession {
  sessionId: string;
  status: string;
  currentStage?: string;
  completedStages: string[];
  startTime: number;
  elapsedTime: number;
}

interface UseActiveSessionRedirectResult {
  isChecking: boolean;
  hasActiveSession: boolean;
  activeSession?: ActiveSession;
  shouldRedirect: boolean;
}

// Global session cache to avoid re-checking
let globalSessionCache: {
  userId: string | null;
  hasActiveSession: boolean;
  activeSession?: ActiveSession;
  lastChecked: number;
  isChecking: boolean;
} = {
  userId: null,
  hasActiveSession: false,
  activeSession: undefined,
  lastChecked: 0,
  isChecking: false,
};

const CACHE_DURATION = 30000; // 30 seconds cache

/**
 * Hook to check for active onboarding sessions and redirect users appropriately
 *
 * @param enableRedirect - Whether to automatically redirect (default: true)
 * @returns Object with session status and redirect information
 */
export const useActiveSessionRedirect = (enableRedirect: boolean = true): UseActiveSessionRedirectResult => {
  const { data: user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { setCurrentStep } = useOnboardingStore();

  // Check if we should redirect based on cached data
  const shouldCheckPages = ['/', '/dashboard', '/brand-kits', '/audience', '/products', '/canvas', '/assets'];

  const shouldCheckPaths = ['/brand-kits/', '/audience/', '/products/', '/canvas/', '/assets/'];

  const shouldCheck =
    shouldCheckPages.includes(location.pathname) || shouldCheckPaths.some((path) => location.pathname.startsWith(path));

  // Only check once when component mounts if we should check this page
  useEffect(() => {
    if (!user || location.pathname === '/onboarding' || !shouldCheck) {
      return;
    }

    const now = Date.now();
    const cacheValid = globalSessionCache.userId === user.id && now - globalSessionCache.lastChecked < CACHE_DURATION;

    // If we have valid cached data, use it immediately
    if (cacheValid && globalSessionCache.hasActiveSession && enableRedirect) {
      console.log('[ActiveSessionRedirect] Using cached session data for redirect');
      setCurrentStep('ai-synthesis');
      navigate('/onboarding?step=ai-synthesis&resumeSession=' + globalSessionCache.activeSession?.sessionId);
      return;
    }

    // If already checking or cache is still valid (no active session), don't check again
    if (globalSessionCache.isChecking || (cacheValid && !globalSessionCache.hasActiveSession)) {
      return;
    }

    const checkForActiveSessions = async () => {
      globalSessionCache.isChecking = true;

      try {
        console.log('[ActiveSessionRedirect] Checking for active onboarding sessions...');

        const result = await checkActiveSessions({});

        console.log('[ActiveSessionRedirect] Session check result:', result);

        if (result.hasActiveSessions && result.activeSessions.length > 0) {
          const session = result.activeSessions[0];

          if (session.status === 'running') {
            console.log('[ActiveSessionRedirect] Found active running session:', session.sessionId);

            // Update global cache
            globalSessionCache = {
              userId: user.id,
              hasActiveSession: true,
              activeSession: session,
              lastChecked: now,
              isChecking: false,
            };

            if (enableRedirect) {
              setCurrentStep('ai-synthesis');
              console.log('[ActiveSessionRedirect] Redirecting to onboarding...');
              navigate('/onboarding?step=ai-synthesis&resumeSession=' + session.sessionId);
            }
          } else {
            console.log('[ActiveSessionRedirect] Found session but not running:', session.status);
            globalSessionCache = {
              userId: user.id,
              hasActiveSession: false,
              activeSession: undefined,
              lastChecked: now,
              isChecking: false,
            };
          }
        } else {
          console.log('[ActiveSessionRedirect] No active sessions found');
          globalSessionCache = {
            userId: user.id,
            hasActiveSession: false,
            activeSession: undefined,
            lastChecked: now,
            isChecking: false,
          };
        }
      } catch (error) {
        console.error('[ActiveSessionRedirect] Error checking active sessions:', error);
        globalSessionCache.isChecking = false;
      }
    };

    checkForActiveSessions();
  }, []); // Only run once on mount

  return {
    isChecking: globalSessionCache.isChecking,
    hasActiveSession: globalSessionCache.hasActiveSession,
    activeSession: globalSessionCache.activeSession,
    shouldRedirect: false, // Never show loading state since we use cache
  };
};

/**
 * Component wrapper that checks for active sessions without showing loading states
 */
export const ActiveSessionChecker: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  // Just trigger the session check, but don't show loading states
  useActiveSessionRedirect();

  // Always render children immediately - no loading states
  return React.createElement(React.Fragment, null, children);
};

/**
 * Hook for manual session checking (without automatic redirect)
 */
export const useActiveSessionStatus = () => {
  return useActiveSessionRedirect(false);
};

/**
 * Clear the global session cache (call this when sessions are completed)
 */
export const clearSessionCache = () => {
  globalSessionCache = {
    userId: null,
    hasActiveSession: false,
    activeSession: undefined,
    lastChecked: 0,
    isChecking: false,
  };
  console.log('[ActiveSessionRedirect] Session cache cleared');
};
