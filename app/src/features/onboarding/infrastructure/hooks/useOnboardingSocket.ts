import { useEffect } from 'react';
import { useSocketListener } from 'wasp/client/webSocket';
import { useOnboardingStore } from '../../domain/store';
import toast from 'react-hot-toast';

export const useOnboardingSocket = () => {
  const { updateFormData, setCurrentStep } = useOnboardingStore();

  // Listen for import results
  useSocketListener('result', (result: any) => {
    if (result?.products?.length > 0) {
      console.log('Received products from socket:', result.products);
      toast.success('Successfully found products!', { duration: 3000 });
      setCurrentStep('complete');
    }

    if (result?.brandKit) {
      console.log('Received brand kit from socket:', result.brandKit);
      toast.success('Brand kit imported successfully!', { duration: 3000 });
    }

    if (result?.audiences) {
      console.log('Received audiences from socket:', result.audiences);
      toast.success('Audiences created successfully!', { duration: 3000 });
    }
  });

  // Listen for import errors
  useSocketListener('error', (error: any) => {
    console.error('Socket error:', error);
    toast.error(`Import failed: ${error.message || 'Please try again later'}`, { duration: 4000 });
  });

  // Listen for progress updates
  useSocketListener('progressUpdate', (progress: any) => {
    console.log('Progress update:', progress);
    // You can update progress state here if needed
  });

  // Listen for synthesis updates (if available)
  // Note: Add this listener when synthesis socket events are implemented
  // useSocketListener('synthesisUpdate', (update: any) => {
  //   console.log('Synthesis update:', update);
  //   // Update synthesis progress in store
  // });

  return {
    // Return any socket-related utilities if needed
  };
};
