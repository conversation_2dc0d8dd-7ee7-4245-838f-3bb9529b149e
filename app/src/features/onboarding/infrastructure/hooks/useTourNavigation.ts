import { useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useOnboardingStore } from '../../domain/store';

export const useTourNavigation = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { tour, resetOnboarding, nextTourStep, pageIsLoaded } = useOnboardingStore();
  const isComponentMounted = useRef(true);

  useEffect(() => {
    return () => {
      isComponentMounted.current = false;
      if (!tour.isActive) {
        resetOnboarding();
      }
    };
  }, [resetOnboarding, tour.isActive]);

  useEffect(() => {
    if (isComponentMounted.current && tour.isActive) {
      pageIsLoaded();
    }
  }, [tour.isActive, pageIsLoaded]);

  const handleTourStep = () => {
    if (isComponentMounted.current && tour.isActive) {
      nextTourStep();
    }
  };

  const shouldRedirect = (targetPath: string) => {
    if (location.pathname === targetPath || location.pathname.startsWith(targetPath + '/')) {
      return false;
    }

    const canRedirect = tour.isActive;

    if (location.pathname === '/onboarding') {
      return false;
    }

    return canRedirect;
  };

  const navigateToStep = (stepId: string) => {
    const step = tour.steps.find((s) => s.id === stepId);
    if (step && step.target) {
      // Extract route from target if it's a route
      if (step.target.startsWith('/')) {
        navigate(step.target);
      }
    }
  };

  return {
    handleTourStep,
    shouldRedirect,
    navigateToStep,
    isInTour: tour.isActive,
    currentStep: tour.steps[tour.currentStepIndex],
  };
};
