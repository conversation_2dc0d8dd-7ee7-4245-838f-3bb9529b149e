// Domain exports
export { useOnboardingStore } from './domain/store';
export type * from './domain/types';

// Presentation exports
export { default as OnboardingPage } from './presentation/pages/OnboardingPage';
export { OnboardingTour } from './presentation/components/OnboardingTour';
export { ImportProgress } from './presentation/components/ImportProgress';

// Infrastructure exports (client-side only)
export { useOnboardingSocket } from './infrastructure/hooks/useOnboardingSocket';
export { useTourNavigation } from './infrastructure/hooks/useTourNavigation';
export {
  useActiveSessionRedirect,
  ActiveSessionChecker,
  useActiveSessionStatus,
  clearSessionCache,
} from './infrastructure/hooks/useActiveSessionRedirect';
