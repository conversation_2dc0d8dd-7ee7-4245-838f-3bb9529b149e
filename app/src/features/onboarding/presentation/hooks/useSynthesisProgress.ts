/**
 * 🔄 Synthesis Progress Hook
 *
 * @description React hook for tracking AI synthesis progress in real-time
 * @responsibility Manages WebSocket connection and progress state for synthesis
 * @dependencies useAuthenticatedSocket, useAuth
 * @ai_context This hook provides real-time progress updates for the onboarding synthesis
 *
 * @example
 * ```typescript
 * const { progress, isConnected, startSynthesis } = useSynthesisProgress();
 *
 * const handleStartSynthesis = async () => {
 *   await startSynthesis(formData);
 * };
 * ```
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from 'wasp/client/auth';
import { useAuthenticatedSocket } from '../../../../core/websocket/useAuthenticatedSocket';
import type { OnboardingFormData } from '../../domain/types';

/**
 * 📊 Progress State Interface
 */
interface ProgressState {
  sessionId?: string;
  status: 'idle' | 'running' | 'completed' | 'error';
  currentStage?: string;
  overallProgress: number; // 0-100
  message: string;
  stages: Array<{
    id: string;
    name: string;
    weight: number;
    status: 'pending' | 'running' | 'completed' | 'error';
    message?: string;
    error?: string;
  }>;
  startTime?: Date;
  endTime?: Date;
  errors: string[];
  result?: {
    brandKitId?: string;
    audienceIds?: number[];
    websiteAnalysis?: any;
  };
}

/**
 * 🔄 Synthesis Progress Hook Return Type
 */
interface SynthesisProgressHookReturn {
  // State
  progress: ProgressState;
  isLoading: boolean;
  isConnected: boolean;
  isAuthenticated: boolean;

  // Actions
  startSynthesis: (formData: any) => Promise<any>;
  resetProgress: () => void;

  // Computed values
  getCurrentStage: () => any;
  getElapsedTime: () => number;
  getStageSummary: () => { completed: number; total: number; failed: number };

  // Convenience flags
  isIdle: boolean;
  isRunning: boolean;
  isCompleted: boolean;
  hasError: boolean;
  hasResult: boolean;
}

/**
 * 🔄 Synthesis Progress Hook
 */
export function useSynthesisProgress(): SynthesisProgressHookReturn {
  const { data: user } = useAuth();
  const { socket, isConnected, isAuthenticated } = useAuthenticatedSocket();

  const [progress, setProgress] = useState<ProgressState>({
    status: 'idle',
    overallProgress: 0,
    message: 'Ready to start',
    stages: [],
    errors: [],
  });

  const [isLoading, setIsLoading] = useState(false);

  /**
   * 📡 Handle progress updates from WebSocket
   */
  useEffect(() => {
    if (!socket || !isConnected) return;

    const handleProgressUpdate = (data: any) => {
      console.log('[useSynthesisProgress] Progress update received:', data);

      setProgress((prev) => ({
        ...prev,
        sessionId: data.sessionId,
        status: data.status,
        currentStage: data.currentStage,
        overallProgress: data.overallProgress,
        message: data.message,
        stages: data.stages || prev.stages,
        startTime: data.startTime ? new Date(data.startTime) : prev.startTime,
        endTime: data.endTime ? new Date(data.endTime) : prev.endTime,
        errors: data.errors || prev.errors,
      }));

      // Stop loading when completed or failed
      if (data.status === 'completed' || data.status === 'error') {
        setIsLoading(false);
      }
    };

    const handleSynthesisResult = (data: any) => {
      console.log('[useSynthesisProgress] Synthesis result received:', data);

      setProgress((prev) => ({
        ...prev,
        result: {
          brandKitId: data.brandKitId,
          audienceIds: data.audienceIds,
          websiteAnalysis: data.websiteAnalysis,
        },
      }));
    };

    const handleSynthesisError = (data: any) => {
      console.error('[useSynthesisProgress] Synthesis error:', data);

      setProgress((prev) => ({
        ...prev,
        status: 'error',
        message: data.message || 'An error occurred',
        errors: [...prev.errors, data.message || 'Unknown error'],
      }));

      setIsLoading(false);
    };

    // Register event listeners
    socket.on('synthesis_progress', handleProgressUpdate);
    socket.on('synthesis_result', handleSynthesisResult);
    socket.on('synthesis_error', handleSynthesisError);

    return () => {
      socket.off('synthesis_progress', handleProgressUpdate);
      socket.off('synthesis_result', handleSynthesisResult);
      socket.off('synthesis_error', handleSynthesisError);
    };
  }, [socket, isConnected]);

  /**
   * 🚀 Start AI synthesis
   */
  const startSynthesis = useCallback(
    async (formData: any) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      // Check socket connection (with improved reliability)
      if (!socket || !isConnected || !isAuthenticated) {
        console.warn('[useSynthesisProgress] Socket not ready - proceeding without real-time updates');
        // Don't throw error, just proceed without real-time updates
      }

      setIsLoading(true);
      setProgress({
        status: 'running',
        overallProgress: 0,
        message: 'Starting AI synthesis...',
        stages: [],
        errors: [],
      });

      try {
        // Import the action from WASP
        const { aiSynthesis } = await import('wasp/client/operations');

        console.log('[useSynthesisProgress] Starting synthesis with data:', formData);

        // Join synthesis room for real-time updates
        const sessionId = `synthesis_${Date.now()}_${user.id}`;
        if (socket && isConnected && isAuthenticated) {
          console.log('[useSynthesisProgress] Joining synthesis room:', sessionId);
          socket.emit('join_synthesis', { sessionId, userId: typeof user.id === 'number' ? user.id : parseInt(user.id, 10) });
        } else {
          console.warn('[useSynthesisProgress] Cannot join synthesis room - socket not ready');
        }

        // Call the synthesis action
        const result = await aiSynthesis(formData);

        console.log('[useSynthesisProgress] Synthesis completed:', result);

        if (result.success) {
          setProgress((prev) => ({
            ...prev,
            status: 'completed',
            overallProgress: 100,
            message: 'Creative Synthesis completed successfully!',
            result: {
              brandKitId: result.brandKitId,
              audienceIds: result.audienceIds,
              websiteAnalysis: result.websiteAnalysis,
            },
          }));
        } else {
          throw new Error(result.errors?.[0] || 'Synthesis failed');
        }

        return result;
      } catch (error) {
        console.error('[useSynthesisProgress] Synthesis failed:', error);

        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

        setProgress((prev) => ({
          ...prev,
          status: 'error',
          message: `Creative Synthesis failed: ${errorMessage}`,
          errors: [...prev.errors, errorMessage],
        }));

        setIsLoading(false);
        throw error;
      }
    },
    [user?.id, socket, isConnected, isAuthenticated]
  );

  /**
   * 🔄 Reset progress state
   */
  const resetProgress = useCallback(() => {
    setProgress({
      status: 'idle',
      overallProgress: 0,
      message: 'Ready to start',
      stages: [],
      errors: [],
    });
    setIsLoading(false);
  }, []);

  /**
   * 📊 Get current stage info
   */
  const getCurrentStage = useCallback(() => {
    if (!progress.currentStage) return null;

    return progress.stages.find((stage) => stage.id === progress.currentStage);
  }, [progress.currentStage, progress.stages]);

  /**
   * 🕐 Get elapsed time
   */
  const getElapsedTime = useCallback(() => {
    if (!progress.startTime) return 0;

    const endTime = progress.endTime || new Date();
    return endTime.getTime() - progress.startTime.getTime();
  }, [progress.startTime, progress.endTime]);

  /**
   * 📈 Get stage summary
   */
  const getStageSummary = useCallback(() => {
    const completed = progress.stages.filter((s) => s.status === 'completed').length;
    const failed = progress.stages.filter((s) => s.status === 'error').length;
    const total = progress.stages.length;

    return { completed, total, failed };
  }, [progress.stages]);

  return {
    // State
    progress,
    isLoading,
    isConnected,
    isAuthenticated,

    // Actions
    startSynthesis,
    resetProgress,

    // Computed values
    getCurrentStage,
    getElapsedTime,
    getStageSummary,

    // Convenience flags
    isIdle: progress.status === 'idle',
    isRunning: progress.status === 'running',
    isCompleted: progress.status === 'completed',
    hasError: progress.status === 'error',
    hasResult: !!progress.result,
  };
}
