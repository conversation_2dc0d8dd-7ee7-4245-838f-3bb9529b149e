import React, { useState, useEffect, useRef, ReactNode } from 'react';
import { useAuth } from 'wasp/client/auth';
import { config } from 'wasp/client';
import { api } from 'wasp/client/api';
import { GoogleAuthMessage, DropboxAuthMessage, OneDriveAuthMessage } from '../../../../utils/windowCommunication';

interface OnboardingCloudConnectButtonProps {
  /** The cloud provider type */
  provider: 'google' | 'onedrive' | 'dropbox';
  /** The text to display on the button */
  buttonText?: string;
  /** Custom button class names */
  buttonClassName?: string;
  /** Custom button children (overrides buttonText) */
  buttonChildren?: ReactNode;
  /** Extra OAuth parameters */
  extraParams?: string;
  /** Callback function when authentication is successful */
  onSuccess?: (message: string) => void;
  /** Callback function when authentication fails */
  onError?: (error: string) => void;
  /** Callback function when authentication starts */
  onStart?: () => void;
}

/**
 * A cloud connection button specifically for onboarding that doesn't reload the page
 */
export const OnboardingCloudConnectButton: React.FC<OnboardingCloudConnectButtonProps> = ({
  provider,
  buttonText = `Connect ${provider}`,
  buttonClassName = 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500',
  buttonChildren,
  extraParams = 'prompt=consent&access_type=offline',
  onSuccess,
  onError,
  onStart,
}) => {
  // We store error text only for callback usage; no local rendering yet
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, setError] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const { data: user } = useAuth();

  // Keep reference to listener so we can remove later
  const messageListenerRef = useRef<((event: MessageEvent) => void) | null>(null);

  // Cleanup listener on unmount
  useEffect(() => {
    return () => {
      if (messageListenerRef.current) {
        window.removeEventListener('message', messageListenerRef.current);
        messageListenerRef.current = null;
      }
    };
  }, []);

  const getProviderConfig = () => {
    switch (provider) {
      case 'google':
        return {
          endpoint: '/api/google/oauth',
          tokenEndpoint: '/api/google/store-token',
          messageType: 'GOOGLE_AUTH_SUCCESS',
          errorType: 'GOOGLE_AUTH_ERROR',
        };
      case 'onedrive':
        return {
          endpoint: '/api/onedrive/oauth',
          tokenEndpoint: '/api/onedrive/store-token',
          messageType: 'ONEDRIVE_AUTH_SUCCESS',
          errorType: 'ONEDRIVE_AUTH_ERROR',
        };
      case 'dropbox':
        return {
          endpoint: '/api/dropbox/oauth',
          tokenEndpoint: '/api/dropbox/store-token',
          messageType: 'DROPBOX_AUTH_SUCCESS',
          errorType: 'DROPBOX_AUTH_ERROR',
        };
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  };

  const handleAuthMessage = async (event: MessageEvent) => {
    const data = event.data as GoogleAuthMessage | DropboxAuthMessage | OneDriveAuthMessage;
    if (!data) return;

    const providerConfig = getProviderConfig();

    if (data.type !== providerConfig.messageType && data.type !== providerConfig.errorType) {
      return; // ignore unrelated messages
    }

    // Remove listener once handled
    if (messageListenerRef.current) {
      window.removeEventListener('message', messageListenerRef.current);
      messageListenerRef.current = null;
    }

    setIsConnecting(false);

    if (data.type === providerConfig.messageType) {
      // Success
      if (onSuccess) onSuccess(data.message);

      if (data.refreshToken && user) {
        try {
          await api.post(providerConfig.tokenEndpoint, {
            userId: user.id,
            refreshToken: data.refreshToken,
          });
        } catch (e) {
          console.error(`[${provider}] Error storing refresh token:`, e);
        }
      }
    } else {
      // Error
      setError(data.message);
      if (onError) onError(data.message);
    }
  };

  const handleConnection = async () => {
    if (!user) {
      const errorMessage = `You must be logged in to connect ${provider}`;
      setError(errorMessage);
      if (onError) {
        onError(errorMessage);
      }
      return;
    }

    if (onStart) {
      onStart();
    }

    setIsConnecting(true);
    setError(null);

    try {
      const providerConfig = getProviderConfig();
      const redirectUrl = '/oauth/callback'; // use generic callback route if exists (adjust if needed)

      const apiUrl = config.apiUrl || window.location.origin;

      const authUrl = `${apiUrl}${providerConfig.endpoint}?redirect=${encodeURIComponent(
        redirectUrl
      )}&extraParams=${encodeURIComponent(extraParams)}&userId=${user.id}`;

      // Centered popup dimensions
      const width = 600;
      const height = 700;
      const left = window.screenX + (window.outerWidth - width) / 2;
      const top = window.screenY + (window.outerHeight - height) / 2;

      const popup = window.open(authUrl, `${provider}Auth`, `width=${width},height=${height},left=${left},top=${top}`);

      if (!popup) {
        const errorMessage = 'Popup blocked! Please enable pop-ups and try again.';
        setError(errorMessage);
        setIsConnecting(false);
        if (onError) onError(errorMessage);
        return;
      }

      // Listen for messages
      messageListenerRef.current = handleAuthMessage;
      window.addEventListener('message', messageListenerRef.current);

      // Poll for popup closed
      const poll = setInterval(() => {
        if (popup.closed) {
          clearInterval(poll);
          if (messageListenerRef.current) {
            window.removeEventListener('message', messageListenerRef.current);
            messageListenerRef.current = null;
          }
          setIsConnecting(false);
        }
      }, 500);
    } catch (error: unknown) {
      console.error(`Error initiating ${provider} OAuth:`, error);
      const errorMessage = `Failed to initiate ${provider} authentication. Please try again.`;
      setError(errorMessage);
      setIsConnecting(false);
      if (onError) {
        onError(errorMessage);
      }
    }
  };

  return (
    <button
      onClick={handleConnection}
      className={buttonClassName}
      disabled={!user || isConnecting}
      title={!user ? `You must be logged in to connect ${provider}` : undefined}
    >
      {buttonChildren || buttonText}
    </button>
  );
};
