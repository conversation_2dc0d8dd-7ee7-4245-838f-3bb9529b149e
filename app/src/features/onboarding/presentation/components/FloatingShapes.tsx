import React from 'react';

export const FloatingShapes: React.FC = () => {
  return (
    <div className='absolute inset-0 overflow-hidden pointer-events-none'>
      {/* Large background shapes */}
      <div className='absolute -top-40 -right-40 w-80 h-80 bg-[#9EA581]/10 rounded-full blur-3xl animate-pulse' />
      <div
        className='absolute -bottom-40 -left-40 w-96 h-96 bg-[#676D50]/10 rounded-full blur-3xl animate-pulse'
        style={{ animationDelay: '2s' }}
      />
      <div
        className='absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-[#B5B178]/10 rounded-full blur-3xl animate-pulse'
        style={{ animationDelay: '4s' }}
      />

      {/* Smaller floating elements */}
      <div
        className='absolute top-20 left-20 w-4 h-4 bg-[#9EA581]/30 rounded-full animate-bounce'
        style={{ animationDelay: '1s' }}
      />
      <div
        className='absolute top-40 right-32 w-6 h-6 bg-[#676D50]/30 rounded-full animate-bounce'
        style={{ animationDelay: '3s' }}
      />
      <div
        className='absolute bottom-32 left-1/4 w-3 h-3 bg-[#B5B178]/30 rounded-full animate-bounce'
        style={{ animationDelay: '5s' }}
      />
      <div
        className='absolute bottom-20 right-20 w-5 h-5 bg-[#9EA581]/30 rounded-full animate-bounce'
        style={{ animationDelay: '2s' }}
      />
    </div>
  );
};
