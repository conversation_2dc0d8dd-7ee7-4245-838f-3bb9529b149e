import React from 'react';
import { useOnboardingStore } from '../../domain/store';
import { FloatingShapes } from './FloatingShapes';
import { OnboardingHeader } from './OnboardingHeader';
import { ProgressBar } from './ProgressBar';

interface OnboardingLayoutProps {
  children: React.ReactNode;
}

export const OnboardingLayout: React.FC<OnboardingLayoutProps> = ({ children }) => {
  const { currentStep, completedSteps } = useOnboardingStore();

  const steps = [
    'intro',
    'business-type',
    'website-url',
    'product-import',
    'brand-guide',
    'ai-synthesis',
    'asset-folder',
    'complete',
  ];

  const currentStepIndex = steps.indexOf(currentStep);
  const progress = ((currentStepIndex + 1) / steps.length) * 100;

  return (
    <div className='min-h-screen bg-[#F0EFE9] dark:bg-black relative overflow-hidden'>
      {/* Floating background shapes */}
      <FloatingShapes />

      {/* Header */}
      <OnboardingHeader />

      {/* Progress bar */}
      <ProgressBar progress={progress} />

      {/* Main content */}
      <div className='relative z-10 flex items-center justify-center min-h-screen pt-20 pb-10'>
        <div className='w-full max-w-4xl mx-auto px-6'>{children}</div>
      </div>
    </div>
  );
};
