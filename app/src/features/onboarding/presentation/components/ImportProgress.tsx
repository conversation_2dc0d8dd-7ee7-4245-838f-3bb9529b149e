import React, { useEffect, useState } from 'react';
import { Check<PERSON><PERSON>cle, AlertCircle, ArrowRight, Loader2 } from 'lucide-react';

interface ImportProgressProps {
  progress: number;
  message: string;
  isComplete: boolean;
  error?: string;
  onRetry?: () => void;
  onComplete?: () => void;
}

export const ImportProgress: React.FC<ImportProgressProps> = ({
  progress,
  message,
  isComplete,
  error,
  onRetry,
  onComplete,
}) => {
  const [displayProgress, setDisplayProgress] = useState(0);

  // Animate progress changes
  useEffect(() => {
    const timer = setTimeout(() => {
      setDisplayProgress(progress);
    }, 100);
    return () => clearTimeout(timer);
  }, [progress]);

  // Auto-complete after a delay
  useEffect(() => {
    if (isComplete && onComplete) {
      const timer = setTimeout(onComplete, 2000);
      return () => clearTimeout(timer);
    }
  }, [isComplete, onComplete]);

  if (error) {
    return (
      <div className='flex flex-col items-center justify-center p-8 space-y-6'>
        <div className='w-16 h-16 bg-red-100 rounded-full flex items-center justify-center'>
          <AlertCircle className='w-8 h-8 text-red-500' />
        </div>

        <div className='text-center space-y-2'>
          <h3 className='text-xl font-semibold text-[#676D50]'>Import Error</h3>
          <p className='text-red-500 max-w-md'>{error}</p>
        </div>

        {onRetry && (
          <button
            onClick={onRetry}
            className='inline-flex items-center gap-2 bg-[#676D50] hover:bg-[#849068] text-[#F8F4DF] px-6 py-3 rounded-xl font-semibold transition-colors'
          >
            Try Again
            <ArrowRight className='w-4 h-4' />
          </button>
        )}
      </div>
    );
  }

  if (isComplete) {
    return (
      <div className='flex flex-col items-center justify-center p-8 space-y-6'>
        <div className='w-20 h-20 bg-[#676D50] rounded-full flex items-center justify-center animate-pulse'>
          <CheckCircle className='w-10 h-10 text-[#F8F4DF]' />
        </div>

        <div className='text-center space-y-2'>
          <h3 className='text-2xl font-semibold text-[#676D50]'>Import Complete!</h3>
          <p className='text-[#676D50]/80'>Your workspace is ready to use</p>
        </div>
      </div>
    );
  }

  return (
    <div className='flex flex-col items-center justify-center p-8 space-y-8'>
      {/* Animated loader */}
      <div className='relative w-32 h-32'>
        {/* Outer ring */}
        <div className='absolute inset-0 rounded-full border-4 border-[#B5B178]/20'>
          <div
            className='absolute inset-0 rounded-full border-4 border-transparent border-t-[#676D50] border-r-[#676D50] animate-spin'
            style={{ animationDuration: '2s' }}
          />
        </div>

        {/* Inner ring */}
        <div className='absolute inset-4 rounded-full border-4 border-[#9EA581]/30'>
          <div
            className='absolute inset-0 rounded-full border-4 border-transparent border-t-[#9EA581] border-l-[#9EA581] animate-spin'
            style={{ animationDuration: '1.5s', animationDirection: 'reverse' }}
          />
        </div>

        {/* Center pulse */}
        <div className='absolute inset-8 rounded-full bg-[#676D50]/20 animate-pulse' />

        {/* Progress percentage */}
        <div className='absolute inset-0 flex items-center justify-center'>
          <span className='text-2xl font-bold text-[#676D50]'>{Math.round(displayProgress)}%</span>
        </div>
      </div>

      {/* Progress bar */}
      <div className='w-full max-w-md space-y-4'>
        <div className='w-full bg-[#F6F3E5] rounded-full h-3 shadow-inner'>
          <div
            className='bg-gradient-to-r from-[#676D50] to-[#849068] h-3 rounded-full transition-all duration-500 ease-out'
            style={{ width: `${displayProgress}%` }}
          />
        </div>

        <div className='text-center space-y-2'>
          <h3 className='text-xl font-semibold text-[#676D50]'>Setting up your workspace</h3>
          <p className='text-[#676D50]/80 flex items-center justify-center gap-2'>
            <Loader2 className='w-4 h-4 animate-spin' />
            {message}
          </p>
        </div>
      </div>

      {/* Background info */}
      <div className='bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-[#B5B178]/20 max-w-md'>
        <p className='text-sm text-[#676D50]/70 text-center'>
          We're analyzing your brand and creating personalized content. This usually takes 2-3 minutes.
        </p>
      </div>
    </div>
  );
};
