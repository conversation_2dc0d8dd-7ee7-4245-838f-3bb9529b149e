/**
 * 🔄 Synthesis Progress Indicator
 *
 * @description Real-time progress indicator for AI synthesis
 * @responsibility Displays synthesis progress with stages and messages
 * @dependencies useSynthesisProgress hook
 * @ai_context This component shows real-time progress during onboarding synthesis
 *
 * @example
 * ```tsx
 * <SynthesisProgressIndicator
 *   onComplete={(result) => console.log('Synthesis done!', result)}
 *   onError={(error) => console.error('Synthesis failed:', error)}
 * />
 * ```
 */

import React from 'react';
import { useSynthesisProgress } from '../hooks/useSynthesisProgress';

interface SynthesisProgressIndicatorProps {
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
  className?: string;
}

export const SynthesisProgressIndicator: React.FC<SynthesisProgressIndicatorProps> = ({
  onComplete,
  onError,
  className = '',
}) => {
  const {
    progress,
    isLoading,
    isConnected,
    isAuthenticated,
    isCompleted,
    hasError,
    hasResult,
    getCurrentStage,
    getStageSummary,
  } = useSynthesisProgress();

  // Handle completion
  React.useEffect(() => {
    if (isCompleted && hasResult && onComplete) {
      onComplete(progress.result);
    }
  }, [isCompleted, hasResult, onComplete, progress.result]);

  // Handle errors
  React.useEffect(() => {
    if (hasError && onError) {
      onError(progress.errors[progress.errors.length - 1] || 'Unknown error');
    }
  }, [hasError, onError, progress.errors]);

  const currentStage = getCurrentStage();
  const stageSummary = getStageSummary();

  if (!isLoading && !isCompleted && !hasError) {
    return null; // Don't show anything when idle
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      {/* Header */}
      <div className='flex items-center justify-between mb-4'>
        <h3 className='text-lg font-semibold text-gray-900'>AI Synthesis Progress</h3>
        <div className='flex items-center space-x-2'>
          {/* Connection Status */}
          <div
            className={`w-2 h-2 rounded-full ${
              isConnected && isAuthenticated ? 'bg-[#676D50]' : isConnected ? 'bg-yellow-500' : 'bg-red-500'
            }`}
          />
          <span className='text-sm text-gray-500'>
            {isConnected && isAuthenticated ? 'Connected' : isConnected ? 'Authenticating...' : 'Disconnected'}
          </span>
        </div>
      </div>

      {/* Overall Progress Bar */}
      <div className='mb-6'>
        <div className='flex justify-between items-center mb-2'>
          <span className='text-sm font-medium text-gray-700'>Overall Progress</span>
          <span className='text-sm text-gray-500'>{Math.round(progress.overallProgress)}%</span>
        </div>
        <div className='w-full bg-gray-200 rounded-full h-2'>
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              hasError ? 'bg-red-500' : isCompleted ? 'bg-[#676D50]' : 'bg-[#849068]'
            }`}
            style={{ width: `${progress.overallProgress}%` }}
          />
        </div>
      </div>

      {/* Current Status Message */}
      <div className='mb-4'>
        <p className={`text-sm ${hasError ? 'text-red-600' : 'text-gray-600'}`}>{progress.message}</p>
      </div>

      {/* Current Stage Info */}
      {currentStage && (
        <div className='mb-4 p-3 bg-blue-50 rounded-lg'>
          <div className='flex items-center justify-between'>
            <span className='text-sm font-medium text-blue-900'>{currentStage.name}</span>
            <span className='text-xs text-blue-600'>
              {currentStage.status === 'running'
                ? 'In Progress'
                : currentStage.status === 'completed'
                  ? 'Completed'
                  : currentStage.status === 'error'
                    ? 'Failed'
                    : 'Pending'}
            </span>
          </div>
          {currentStage.message && <p className='text-xs text-blue-700 mt-1'>{currentStage.message}</p>}
        </div>
      )}

      {/* Stages List */}
      {progress.stages.length > 0 && (
        <div className='space-y-2'>
          <h4 className='text-sm font-medium text-gray-700 mb-2'>Stages</h4>
          {progress.stages.map((stage, index) => (
            <div
              key={stage.id}
              className={`flex items-center space-x-3 p-2 rounded ${
                stage.status === 'completed'
                  ? 'bg-[#676D50]/10'
                  : stage.status === 'running'
                    ? 'bg-[#849068]/10'
                    : stage.status === 'error'
                      ? 'bg-red-50'
                      : 'bg-gray-50'
              }`}
            >
              {/* Stage Status Icon */}
              <div
                className={`w-4 h-4 rounded-full flex items-center justify-center ${
                  stage.status === 'completed'
                    ? 'bg-[#676D50]'
                    : stage.status === 'running'
                      ? 'bg-[#849068]'
                      : stage.status === 'error'
                        ? 'bg-red-500'
                        : 'bg-gray-300'
                }`}
              >
                {stage.status === 'completed' && (
                  <svg className='w-2 h-2 text-white' fill='currentColor' viewBox='0 0 20 20'>
                    <path
                      fillRule='evenodd'
                      d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                      clipRule='evenodd'
                    />
                  </svg>
                )}
                {stage.status === 'running' && <div className='w-2 h-2 bg-white rounded-full animate-pulse' />}
                {stage.status === 'error' && (
                  <svg className='w-2 h-2 text-white' fill='currentColor' viewBox='0 0 20 20'>
                    <path
                      fillRule='evenodd'
                      d='M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z'
                      clipRule='evenodd'
                    />
                  </svg>
                )}
              </div>

              {/* Stage Info */}
              <div className='flex-1'>
                <div className='flex items-center justify-between'>
                  <span
                    className={`text-sm font-medium ${
                      stage.status === 'completed'
                        ? 'text-[#676D50]'
                        : stage.status === 'running'
                          ? 'text-[#849068]'
                          : stage.status === 'error'
                            ? 'text-red-800'
                            : 'text-gray-600'
                    }`}
                  >
                    {stage.name}
                  </span>
                  <span className='text-xs text-gray-500'>{stage.weight}%</span>
                </div>
                {stage.message && (
                  <p className={`text-xs mt-1 ${stage.status === 'error' ? 'text-red-600' : 'text-gray-500'}`}>
                    {stage.error || stage.message}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Stage Summary */}
      {stageSummary.total > 0 && (
        <div className='mt-4 pt-4 border-t border-gray-200'>
          <div className='flex justify-between text-xs text-gray-500'>
            <span>
              Completed: {stageSummary.completed}/{stageSummary.total}
            </span>
            {stageSummary.failed > 0 && <span className='text-red-500'>Failed: {stageSummary.failed}</span>}
          </div>
        </div>
      )}

      {/* Error List */}
      {progress.errors.length > 0 && (
        <div className='mt-4 p-3 bg-red-50 rounded-lg'>
          <h5 className='text-sm font-medium text-red-800 mb-2'>Errors</h5>
          <ul className='space-y-1'>
            {progress.errors.map((error, index) => (
              <li key={index} className='text-xs text-red-600'>
                • {error}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Completion Message */}
      {isCompleted && (
        <div className='mt-4 p-3 bg-[#676D50]/10 rounded-lg'>
          <div className='flex items-center'>
            <svg className='w-5 h-5 text-[#676D50] mr-2' fill='currentColor' viewBox='0 0 20 20'>
              <path
                fillRule='evenodd'
                d='M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z'
                clipRule='evenodd'
              />
            </svg>
            <span className='text-sm font-medium text-[#676D50]'>Creative Synthesis completed successfully!</span>
          </div>
          {hasResult && (
            <div className='mt-2 text-xs text-[#849068]'>
              {progress.result?.brandKitId && <div>✓ Brand kit created</div>}
              {progress.result?.audienceIds?.length && (
                <div>✓ {progress.result.audienceIds.length} audiences generated</div>
              )}
              {progress.result?.websiteAnalysis && <div>✓ Website analyzed</div>}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
