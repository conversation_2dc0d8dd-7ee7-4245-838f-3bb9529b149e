import React from 'react';

interface ProgressBarProps {
  progress: number;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({ progress }) => {
  return (
    <div className='fixed top-16 left-0 right-0 z-10'>
      <div className='max-w-7xl mx-auto px-6'>
        <div className='w-full bg-[#F6F3E5] rounded-full h-2 shadow-inner'>
          <div
            className='bg-gradient-to-r from-[#676D50] to-[#849068] h-2 rounded-full transition-all duration-500 ease-out'
            style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
          />
        </div>
        <div className='text-center mt-2'>
          <span className='text-xs text-[#676D50]/70 font-medium'>{Math.round(progress)}% Complete</span>
        </div>
      </div>
    </div>
  );
};
