import React, { useEffect } from 'react';
import Joyride, { ACTIONS, EVENTS, STATUS } from 'react-joyride';
import { useLocation, useNavigate } from 'react-router-dom';
import { useOnboardingStore } from '../../domain/store';
import { useTourNavigation } from '../../infrastructure/hooks/useTourNavigation';

export const OnboardingTour: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { tour, nextTourStep, previousTourStep, completeTour } = useOnboardingStore();
  const { shouldRedirect } = useTourNavigation();

  const handleJoyrideCallback = (data: any) => {
    const { action, index, status, type } = data;

    if ([STATUS.FINISHED, STATUS.SKIPPED].includes(status)) {
      completeTour();
    } else if ([EVENTS.STEP_AFTER, EVENTS.TARGET_NOT_FOUND].includes(type)) {
      if (action === ACTIONS.PREV) {
        previousTourStep();
      } else {
        nextTourStep();
      }
    }
  };

  // Handle navigation between tour pages
  useEffect(() => {
    if (!tour.isActive) return;

    const currentStep = tour.steps[tour.currentStepIndex];
    if (!currentStep) return;

    // Navigate to the appropriate page based on step target
    if (currentStep.target && currentStep.target.startsWith('/')) {
      if (location.pathname !== currentStep.target) {
        navigate(currentStep.target);
      }
    }
  }, [tour.currentStepIndex, tour.isActive, location.pathname, navigate, tour.steps]);

  if (!tour.isActive) {
    return null;
  }

  return (
    <Joyride
      callback={handleJoyrideCallback}
      continuous
      locale={{
        back: 'Back',
        close: 'Close',
        last: 'Finish',
        next: 'Next',
        nextLabelWithProgress: 'Next ({step} of {steps})',
        open: 'Open the dialog',
        skip: 'Skip tour',
      }}
      run={tour.isActive}
      scrollToFirstStep
      showProgress
      showSkipButton
      stepIndex={tour.currentStepIndex}
      steps={tour.steps.map((step) => ({
        target: step.target || 'body',
        content: step.description,
        title: step.title,
        placement: step.placement || 'bottom',
        disableBeacon: true,
        disableOverlayClose: true,
        hideCloseButton: false,
        spotlightClicks: true,
        styles: {
          options: {
            zIndex: 10000,
            primaryColor: '#676D50',
          },
          tooltip: {
            backgroundColor: '#F8F4DF',
            color: '#676D50',
            fontSize: '16px',
            borderRadius: '12px',
            padding: '20px',
          },
          tooltipTitle: {
            color: '#676D50',
            fontSize: '18px',
            fontWeight: 'bold',
            marginBottom: '8px',
          },
          buttonNext: {
            backgroundColor: '#676D50',
            color: '#F8F4DF',
            borderRadius: '8px',
            padding: '8px 16px',
            fontSize: '14px',
            fontWeight: '600',
          },
          buttonBack: {
            color: '#676D50',
            marginRight: '8px',
          },
          buttonSkip: {
            color: '#676D50',
          },
          spotlight: {
            borderRadius: '8px',
          },
          overlay: {
            backgroundColor: 'rgba(0, 0, 0, 0.4)',
          },
        },
      }))}
    />
  );
};
