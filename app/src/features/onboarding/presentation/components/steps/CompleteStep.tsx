import React from 'react';
import { useOnboardingStore } from '../../../domain/store';
import { Check<PERSON>ir<PERSON>, ArrowR<PERSON>, Spark<PERSON>, Palette, Users, Package, PenTool } from 'lucide-react';
import { useAction, completeSynthesisSession } from 'wasp/client/operations';
import { clearSessionCache } from '../../../infrastructure/hooks/useActiveSessionRedirect';

export const CompleteStep: React.FC = () => {
  const { startTour } = useOnboardingStore();
  const completeSynthesisSessionAction = useAction(completeSynthesisSession);

  const handleStartTour = async () => {
    try {
      // Complete any active synthesis sessions
      await completeSynthesisSessionAction({});
      console.log('[CompleteStep] Synthesis sessions completed');

      // Clear the session cache to prevent redirects
      clearSessionCache();

      startTour();
      // Navigate to main dashboard
      window.location.href = '/dashboard';
    } catch (error) {
      console.error('[CompleteStep] Error completing synthesis sessions:', error);
      // Still clear cache and navigate even if session cleanup fails
      clearSessionCache();
      startTour();
      window.location.href = '/dashboard';
    }
  };

  const handleSkipTour = async () => {
    try {
      // Complete any active synthesis sessions
      await completeSynthesisSessionAction({});
      console.log('[CompleteStep] Synthesis sessions completed');

      // Clear the session cache to prevent redirects
      clearSessionCache();

      // Navigate directly to dashboard
      window.location.href = '/dashboard';
    } catch (error) {
      console.error('[CompleteStep] Error completing synthesis sessions:', error);
      // Still clear cache and navigate even if session cleanup fails
      clearSessionCache();
      window.location.href = '/dashboard';
    }
  };

  const features = [
    {
      icon: Palette,
      title: 'Brand Kit',
      description: 'Your colors, fonts, and brand assets are ready',
      link: '/brand-kit',
    },
    {
      icon: Users,
      title: 'Audiences',
      description: 'Primary, secondary, and tertiary personas created',
      link: '/audiences',
    },
    {
      icon: Package,
      title: 'Products',
      description: 'Product catalog imported and organized',
      link: '/products',
    },
    {
      icon: PenTool,
      title: 'Design Canvas',
      description: 'Start creating marketing assets instantly',
      link: '/canvas',
    },
  ];

  return (
    <div className='max-w-4xl mx-auto space-y-8'>
      {/* Header */}
      <div className='text-center space-y-6'>
        <div className='inline-flex items-center justify-center w-20 h-20 bg-[#676D50] rounded-2xl mb-6'>
          <CheckCircle className='w-10 h-10 text-[#F8F4DF]' />
        </div>

        <h1 className='font-display text-4xl md:text-5xl font-bold text-[#676D50] leading-tight'>Welcome to Olivia!</h1>
        <p className='text-xl text-[#676D50]/80 max-w-2xl mx-auto leading-relaxed'>
          Your Creative Studio is ready. Everything has been set up and personalized for your brand.
        </p>
      </div>

      {/* Features Grid */}
      <div className='grid md:grid-cols-2 gap-6 my-12'>
        {features.map((feature, index) => (
          <div
            key={index}
            className='bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-[#B5B178]/20 shadow-lg hover:shadow-xl transition-shadow'
          >
            <div className='flex items-start gap-4'>
              <div className='w-12 h-12 bg-[#9EA581] rounded-xl flex items-center justify-center flex-shrink-0'>
                <feature.icon className='w-6 h-6 text-white' />
              </div>
              <div className='flex-1'>
                <h3 className='font-display font-semibold text-[#676D50] mb-2'>{feature.title}</h3>
                <p className='text-[#676D50]/70 text-sm mb-3'>{feature.description}</p>
                <a
                  href={feature.link}
                  className='text-[#676D50] hover:text-[#849068] transition-colors text-sm font-medium inline-flex items-center gap-1'
                >
                  Explore
                  <ArrowRight className='w-3 h-3' />
                </a>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Stats */}
      <div className='bg-gradient-to-r from-[#676D50] to-[#849068] rounded-2xl p-8 text-white text-center'>
        <h3 className='font-display text-2xl font-bold mb-6'>Your Workspace Summary</h3>
        <div className='grid grid-cols-2 md:grid-cols-4 gap-6'>
          <div>
            <div className='text-3xl font-bold mb-1'>1</div>
            <div className='text-sm opacity-90'>Brand Kit</div>
          </div>
          <div>
            <div className='text-3xl font-bold mb-1'>3</div>
            <div className='text-sm opacity-90'>Audiences</div>
          </div>
          <div>
            <div className='text-3xl font-bold mb-1'>5+</div>
            <div className='text-sm opacity-90'>Products</div>
          </div>
          <div>
            <div className='text-3xl font-bold mb-1'>∞</div>
            <div className='text-sm opacity-90'>Possibilities</div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className='text-center space-y-6'>
        <h3 className='font-display text-2xl font-semibold text-[#676D50]'>Ready to start creating?</h3>

        <div className='flex flex-col sm:flex-row gap-4 justify-center items-center'>
          <button
            onClick={handleStartTour}
            className='inline-flex items-center gap-3 bg-[#676D50] hover:bg-[#849068] text-[#F8F4DF] px-8 py-4 rounded-xl font-semibold text-lg transition-colors shadow-lg hover:shadow-xl'
          >
            <Sparkles className='w-5 h-5' />
            Take the Tour
          </button>

          <button
            onClick={handleSkipTour}
            className='inline-flex items-center gap-3 text-[#676D50] hover:text-[#849068] px-8 py-4 rounded-xl font-semibold text-lg transition-colors'
          >
            Skip to Dashboard
            <ArrowRight className='w-5 h-5' />
          </button>
        </div>

        <p className='text-sm text-[#676D50]/60'>You can always access the tour later from the help menu</p>
      </div>
    </div>
  );
};
