import React, { useState } from 'react';
import { useOnboardingStore } from '../../../domain/store';
import { ArrowRight, ArrowLeft, Globe, AlertCircle } from 'lucide-react';

export const WebsiteUrlStep: React.FC = () => {
  const { formData, updateFormData, setCurrentStep, completeStep } = useOnboardingStore();
  const [isValidUrl, setIsValidUrl] = useState(true);

  const validateUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const handleUrlChange = (url: string) => {
    updateFormData({ websiteUrl: url });
    setIsValidUrl(url === '' || validateUrl(url));

    // Auto-extract company name if not manually set and URL is valid
    if (url && validateUrl(url) && !formData.companyName) {
      try {
        const domain = new URL(url).hostname;
        const extractedName = domain.replace('www.', '').split('.')[0];
        const formattedName = extractedName.charAt(0).toUpperCase() + extractedName.slice(1);
        updateFormData({ companyName: formattedName });
      } catch (e) {
        // Ignore extraction errors
      }
    }
  };

  const handleCompanyNameChange = (name: string) => {
    updateFormData({ companyName: name });
  };

  const handleNext = () => {
    if (formData.websiteUrl && formData.companyName && isValidUrl) {
      completeStep('website-url');
      // Always go to product import step next
      setCurrentStep('product-import');
    }
  };

  const handleSkip = () => {
    updateFormData({ websiteUrl: '' });
    completeStep('website-url');
    setCurrentStep('product-import');
  };

  const handleBack = () => {
    setCurrentStep('business-type');
  };

  return (
    <div className='max-w-2xl mx-auto space-y-8'>
      {/* Header */}
      <div className='text-center space-y-4 pt-8'>
        <h1 className='font-display text-3xl md:text-4xl font-bold text-[#676D50]'>What's your website URL?</h1>
        <p className='text-lg text-[#676D50]/80'>
          We'll analyze your website to extract your brand colors, fonts, and style
        </p>
      </div>

      {/* URL Input */}
      <div className='space-y-4'>
        <div className='space-y-2'>
          <label className='block text-sm font-medium text-[#676D50]'>Website URL</label>
          <div className='relative'>
            <input
              type='url'
              value={formData.websiteUrl}
              onChange={(e) => handleUrlChange(e.target.value)}
              placeholder='https://yourwebsite.com'
              className={`w-full px-4 py-4 text-lg border-2 rounded-xl focus:outline-none focus:ring-0 transition-colors ${
                !isValidUrl ? 'border-red-300 focus:border-red-500' : 'border-[#B5B178]/30 focus:border-[#676D50]'
              }`}
            />
            {!isValidUrl && (
              <div className='absolute right-3 top-1/2 transform -translate-y-1/2'>
                <AlertCircle className='w-5 h-5 text-red-500' />
              </div>
            )}
          </div>
          {!isValidUrl && (
            <p className='text-sm text-red-600 flex items-center gap-1'>
              <AlertCircle className='w-4 h-4' />
              Please enter a valid URL
            </p>
          )}
        </div>

        {/* Company Name Input */}
        <div className='space-y-2'>
          <label className='block text-sm font-medium text-[#676D50]'>
            Company Name <span className='text-red-500'>*</span>
          </label>
          <input
            type='text'
            value={formData.companyName || ''}
            onChange={(e) => handleCompanyNameChange(e.target.value)}
            placeholder='Enter your company name'
            className='w-full px-4 py-4 text-lg border-2 border-[#B5B178]/30 focus:border-[#676D50] rounded-xl focus:outline-none focus:ring-0 transition-colors'
          />
          <p className='text-xs text-[#676D50]/60'>Auto-filled from your website URL, but you can customize it</p>
        </div>

        {/* Benefits */}
        <div className='bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-[#B5B178]/20'>
          <h3 className='font-display font-semibold text-[#676D50] mb-3'>What we'll extract from your website:</h3>
          <ul className='space-y-2 text-sm text-[#676D50]/80'>
            <li className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-[#9EA581] rounded-full' />
              Brand colors and color palette
            </li>
            <li className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-[#9EA581] rounded-full' />
              Typography and font styles
            </li>
            <li className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-[#9EA581] rounded-full' />
              Logo and brand assets
            </li>
            <li className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-[#9EA581] rounded-full' />
              Brand voice and messaging
            </li>
          </ul>
        </div>
      </div>

      {/* Navigation */}
      <div className='flex items-center justify-between pt-8'>
        <button
          onClick={handleBack}
          className='inline-flex items-center gap-2 text-[#676D50]/70 hover:text-[#676D50] transition-colors'
        >
          <ArrowLeft className='w-4 h-4' />
          Back
        </button>

        <div className='flex items-center gap-3'>
          <button onClick={handleSkip} className='text-[#676D50]/70 hover:text-[#676D50] transition-colors px-4 py-2'>
            Skip for now
          </button>

          <button
            onClick={handleNext}
            disabled={!formData.websiteUrl || !formData.companyName || !isValidUrl}
            className='inline-flex items-center gap-3 bg-[#676D50] hover:bg-[#849068] text-[#F8F4DF] px-6 py-3 rounded-xl font-semibold transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
          >
            Continue
            <ArrowRight className='w-4 h-4' />
          </button>
        </div>
      </div>
    </div>
  );
};
