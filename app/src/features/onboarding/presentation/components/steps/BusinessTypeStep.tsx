import React from 'react';
import { useOnboardingStore } from '../../../domain/store';
import { ArrowRight, ArrowLeft, ShoppingCart, Monitor, Users, MessageCircle, Building } from 'lucide-react';
import type { BusinessType } from '../../../domain/types';

export const BusinessTypeStep: React.FC = () => {
  const { formData, updateFormData, setCurrentStep, completeStep } = useOnboardingStore();

  const businessTypes: Array<{
    type: BusinessType;
    label: string;
    description: string;
    icon: React.ReactNode;
  }> = [
    {
      type: 'ecommerce',
      label: 'E-commerce',
      description: 'Online store selling physical or digital products',
      icon: <ShoppingCart className='w-6 h-6' />,
    },
    {
      type: 'saas',
      label: 'SaaS',
      description: 'Software as a Service platform or application',
      icon: <Monitor className='w-6 h-6' />,
    },
    {
      type: 'agency',
      label: 'Agency',
      description: 'Marketing, design, or consulting agency',
      icon: <Users className='w-6 h-6' />,
    },
    {
      type: 'consulting',
      label: 'Consulting',
      description: 'Professional services and consulting business',
      icon: <MessageCircle className='w-6 h-6' />,
    },
    {
      type: 'other',
      label: 'Other',
      description: 'Different type of business or organization',
      icon: <Building className='w-6 h-6' />,
    },
  ];

  const handleSelectBusinessType = (type: BusinessType) => {
    updateFormData({ businessType: type });
  };

  const handleNext = () => {
    completeStep('business-type');
    setCurrentStep('website-url');
  };

  const handleBack = () => {
    setCurrentStep('intro');
  };

  return (
    <div className='max-w-3xl mx-auto space-y-8'>
      {/* Header */}
      <div className='text-center space-y-4'>
        <h1 className='font-display text-3xl md:text-4xl font-bold text-[#676D50]'>
          What type of business do you have?
        </h1>
        <p className='text-lg text-[#676D50]/80'>This helps us customize Oliva for your specific needs</p>
      </div>

      {/* Business type cards */}
      <div className='grid md:grid-cols-2 lg:grid-cols-3 gap-4'>
        {businessTypes.map((business) => (
          <button
            key={business.type}
            onClick={() => handleSelectBusinessType(business.type)}
            className={`p-6 rounded-2xl border-2 transition-all text-left hover:shadow-lg ${
              formData.businessType === business.type
                ? 'border-[#676D50] bg-[#676D50]/5 shadow-lg'
                : 'border-[#B5B178]/30 bg-white/60 hover:border-[#9EA581]'
            }`}
          >
            <div
              className={`w-12 h-12 rounded-xl flex items-center justify-center mb-4 ${
                formData.businessType === business.type ? 'bg-[#676D50] text-white' : 'bg-[#9EA581] text-white'
              }`}
            >
              {business.icon}
            </div>
            <h3 className='font-display font-semibold text-[#676D50] mb-2'>{business.label}</h3>
            <p className='text-sm text-[#676D50]/70'>{business.description}</p>
          </button>
        ))}
      </div>

      {/* Navigation */}
      <div className='flex items-center justify-between pt-8'>
        <button
          onClick={handleBack}
          className='inline-flex items-center gap-2 text-[#676D50]/70 hover:text-[#676D50] transition-colors'
        >
          <ArrowLeft className='w-4 h-4' />
          Back
        </button>

        <button
          onClick={handleNext}
          disabled={!formData.businessType}
          className='inline-flex items-center gap-3 bg-[#676D50] hover:bg-[#849068] text-[#F8F4DF] px-6 py-3 rounded-xl font-semibold transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
        >
          Continue
          <ArrowRight className='w-4 h-4' />
        </button>
      </div>
    </div>
  );
};
