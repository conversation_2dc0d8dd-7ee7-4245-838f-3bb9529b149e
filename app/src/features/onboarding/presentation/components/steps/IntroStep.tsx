import React from 'react';
import { useOnboardingStore } from '../../../domain/store';
import { ArrowR<PERSON>, Sparkles, Palette, Users, Zap } from 'lucide-react';

export const IntroStep: React.FC = () => {
  const { setCurrentStep, completeStep } = useOnboardingStore();

  const handleGetStarted = () => {
    completeStep('intro');
    setCurrentStep('business-type');
  };

  return (
    <div className='text-center space-y-8'>
      {/* Hero section */}
      <div className='space-y-6'>
        <div className='inline-flex items-center justify-center w-20 h-20 bg-[#676D50] rounded-2xl mb-6'>
          <Sparkles className='w-10 h-10 text-[#F8F4DF]' />
        </div>

        <h1 className='font-display text-4xl md:text-5xl font-bold text-[#676D50] leading-tight'>Welcome to Olivia</h1>

        <p className='text-xl text-[#676D50]/80 max-w-2xl mx-auto leading-relaxed'>
          Your Creative Companion that creates stunning marketing assets tailored to your brand and audience.
        </p>
      </div>

      {/* Features grid */}
      <div className='grid md:grid-cols-3 gap-6 max-w-4xl mx-auto my-12'>
        <div className='bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-[#B5B178]/20 shadow-lg'>
          <div className='w-12 h-12 bg-[#9EA581] rounded-xl flex items-center justify-center mb-4 mx-auto'>
            <Palette className='w-6 h-6 text-white' />
          </div>
          <h3 className='font-display font-semibold text-[#676D50] mb-2'>Smart Brand Kit</h3>
          <p className='text-[#676D50]/70 text-sm'>
            Automatically extract your brand colors, fonts, and style from your website.
          </p>
        </div>

        <div className='bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-[#B5B178]/20 shadow-lg'>
          <div className='w-12 h-12 bg-[#9EA581] rounded-xl flex items-center justify-center mb-4 mx-auto'>
            <Users className='w-6 h-6 text-white' />
          </div>
          <h3 className='font-display font-semibold text-[#676D50] mb-2'>Smart Audiences</h3>
          <p className='text-[#676D50]/70 text-sm'>
            Generate detailed customer personas with visual character avatars.
          </p>
        </div>

        <div className='bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-[#B5B178]/20 shadow-lg'>
          <div className='w-12 h-12 bg-[#9EA581] rounded-xl flex items-center justify-center mb-4 mx-auto'>
            <Zap className='w-6 h-6 text-white' />
          </div>
          <h3 className='font-display font-semibold text-[#676D50] mb-2'>Instant Assets</h3>
          <p className='text-[#676D50]/70 text-sm'>
            Create marketing materials that perfectly match your brand in seconds.
          </p>
        </div>
      </div>

      {/* CTA */}
      <div className='space-y-4'>
        <button
          onClick={handleGetStarted}
          className='inline-flex items-center gap-3 bg-[#676D50] hover:bg-[#849068] text-[#F8F4DF] px-8 py-4 rounded-xl font-semibold text-lg transition-colors shadow-lg hover:shadow-xl'
        >
          Get Started
          <ArrowRight className='w-5 h-5' />
        </button>

        <p className='text-sm text-[#676D50]/60'>Setup takes less than 5 minutes</p>
      </div>
    </div>
  );
};
