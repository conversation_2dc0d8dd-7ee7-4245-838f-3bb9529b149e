import React, { useState, useEffect } from 'react';
import { useOnboardingStore } from '../../../domain/store';
import { ArrowRight, ArrowLeft, ShoppingBag, Globe, SkipForward, ExternalLink, Loader2, Check } from 'lucide-react';
import { useSocketListener } from 'wasp/client/webSocket';
import { useAuthenticatedSocket } from '../../../../../core/websocket/useAuthenticatedSocket';
import {
  selectProductsForImport,
  startProductImport,
  initiateShopifyOAuth,
  fetchShopifyProducts,
  importShopifyProducts,
} from 'wasp/client/operations';
import { useOrganizationState } from '../../../../../organization/store';
import toast from 'react-hot-toast';
import type { ProductImportMethod, ImportedProduct } from '../../../domain/types';

interface DetectedProduct {
  id: number;
  name: string;
  imageUrl?: string;
  productUrl: string;
  confidence: number;
  selected: boolean;
  brandName: string;
}

interface ShopifyProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  imageUrl?: string;
  productUrl: string;
  vendor: string;
  productType: string;
  variants: Array<{
    id: string;
    title: string;
    price: number;
    sku?: string;
    inventory?: number;
  }>;
  selected: boolean;
}

export const ProductImportStep: React.FC = () => {
  const { formData, updateFormData, setCurrentStep, completeStep } = useOnboardingStore();
  const { selectedOrganizationId } = useOrganizationState();
  const { socket, isConnected } = useAuthenticatedSocket();

  const [selectedMethod, setSelectedMethod] = useState<ProductImportMethod | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [discoveredProducts, setDiscoveredProducts] = useState<DetectedProduct[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Shopify-specific state
  const [shopifyProducts, setShopifyProducts] = useState<ShopifyProduct[]>([]);
  const [shopifyShop, setShopifyShop] = useState<string>('');
  const [shopifyConnected, setShopifyConnected] = useState(false);
  const [shopifyLoading, setShopifyLoading] = useState(false);
  const [shopDomain, setShopDomain] = useState('');

  // Check for Shopify OAuth callback on component mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const shopifyStatus = urlParams.get('shopify');
    const shop = urlParams.get('shop');
    const errorMessage = urlParams.get('message');

    if (shopifyStatus === 'connected' && shop) {
      setShopifyConnected(true);
      setShopifyShop(shop);
      setSelectedMethod('shopify');
      toast.success(`Successfully connected to ${shop}!`);

      // Clear URL parameters
      window.history.replaceState({}, document.title, window.location.pathname);

      // Fetch products from connected store
      handleFetchShopifyProducts();
    } else if (shopifyStatus === 'error') {
      toast.error(`Shopify connection failed: ${errorMessage || 'Unknown error'}`);
      setSelectedMethod(null);

      // Clear URL parameters
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []);

  // WebSocket listeners for product import
  useSocketListener('progressUpdate', (data) => {
    setImportProgress(data.progress);
  });

  useSocketListener('result', (data) => {
    if (data.products) {
      setDiscoveredProducts(data.products);
      setIsImporting(false);
      setImportProgress(100);
    }
  });

  useSocketListener('error', (data) => {
    let errorMessage = data.message;

    // Check for Cloudflare protection
    if (
      errorMessage.includes('Cloudflare protection') ||
      errorMessage.includes('DDoS protection') ||
      errorMessage.includes('Verifying your connection')
    ) {
      errorMessage =
        'This website has DDoS protection that blocks automated access. Try connecting your Shopify store instead, or add products manually later.';
    }

    setError(errorMessage);
    setIsImporting(false);
    setImportProgress(0);
  });

  const handleMethodSelect = (method: ProductImportMethod) => {
    setSelectedMethod(method);
    setError(null);

    if (method === 'website') {
      startWebsiteImport();
    } else if (method === 'skip') {
      handleSkip();
    }
  };

  const startWebsiteImport = async () => {
    console.log('[ProductImportStep] FormData:', formData);
    console.log('[ProductImportStep] WebsiteUrl:', formData.websiteUrl);
    console.log('[ProductImportStep] IsConnected:', isConnected);
    console.log('[ProductImportStep] Socket:', socket);

    if (!formData.websiteUrl) {
      console.error('[ProductImportStep] Missing websiteUrl');
      toast.error(`Website URL is required for import. Current URL: "${formData.websiteUrl || 'empty'}"`);
      return;
    }

    if (!isConnected) {
      console.warn('[ProductImportStep] Socket not connected, attempting to wait for connection...');
      toast.loading('Connecting to server...', { id: 'connection' });

      // Wait up to 5 seconds for connection
      let attempts = 0;
      const maxAttempts = 50; // 5 seconds with 100ms intervals

      while (!isConnected && attempts < maxAttempts) {
        await new Promise((resolve) => setTimeout(resolve, 100));
        attempts++;
      }

      toast.dismiss('connection');

      if (!isConnected) {
        console.error('[ProductImportStep] Failed to establish socket connection');
        toast.error('Unable to connect to server. Please refresh the page and try again.');
        return;
      }

      console.log('[ProductImportStep] Socket connection established');
    }

    setIsImporting(true);
    setImportProgress(0);
    setDiscoveredProducts([]);

    // Extract company name from website URL for brand name
    const brandName = formData.companyName || new URL(formData.websiteUrl).hostname.replace('www.', '').split('.')[0];

    socket.emit('importProducts', {
      url: formData.websiteUrl,
      brandName,
    });
  };

  const handleProductSelection = (productId: number, selected: boolean) => {
    setDiscoveredProducts((prev) => prev.map((p) => (p.id === productId ? { ...p, selected } : p)));
  };

  const handleImportSelected = async () => {
    const selectedProducts = discoveredProducts.filter((p) => p.selected);

    if (selectedProducts.length === 0) {
      toast.error('Please select at least one product to import');
      return;
    }

    try {
      setIsImporting(true);

      // Create import job with selected products
      const importJobId = `import_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Step 1: Create import job with selected products
      await selectProductsForImport({
        id: importJobId,
        selectedProducts: selectedProducts.map((p) => ({
          id: p.id,
          name: p.name,
          imageUrl: p.imageUrl,
          productUrl: p.productUrl,
          confidence: p.confidence,
          selected: p.selected,
          brandName: p.brandName,
          metadata: {
            productDetailsUrl: p.productUrl,
            discoveredAt: new Date().toISOString(),
          },
        })),
        organizationId: selectedOrganizationId!,
        manualUrls: [],
      });

      // Step 2: Start the background import process
      console.log(`[ProductImportStep] Starting background import for job: ${importJobId}`);
      await startProductImport({
        id: importJobId,
        organizationId: selectedOrganizationId!,
      });

      // Store imported products in onboarding state
      const importedProducts: ImportedProduct[] = selectedProducts.map((p) => ({
        id: p.id.toString(),
        name: p.name,
        imageUrl: p.imageUrl,
        productUrl: p.productUrl,
        selected: p.selected,
        confidence: p.confidence,
      }));

      updateFormData({
        productImportMethod: 'website',
        importedProducts,
      });

      toast.success(
        `Started importing ${selectedProducts.length} products. They'll appear in your Products page as they're processed.`
      );
      handleNext();
    } catch (error) {
      console.error('Error importing products:', error);
      toast.error('Failed to import products');
    } finally {
      setIsImporting(false);
    }
  };

  const handleShopifyConnect = async () => {
    if (!shopDomain.trim()) {
      toast.error('Please enter your Shopify store domain');
      return;
    }

    try {
      setShopifyLoading(true);

      const result = await initiateShopifyOAuth({
        shop: shopDomain.trim(),
        organizationId: selectedOrganizationId!,
      });

      // Redirect to Shopify OAuth
      window.location.href = result.authUrl;
    } catch (error) {
      console.error('Shopify OAuth initiation failed:', error);
      toast.error('Failed to connect to Shopify. Please check your store domain and try again.');
      setShopifyLoading(false);
    }
  };

  const handleFetchShopifyProducts = async () => {
    try {
      setShopifyLoading(true);

      const result = await fetchShopifyProducts({
        organizationId: selectedOrganizationId!,
        limit: 50,
      });

      setShopifyProducts(result.products);
      setShopifyShop(result.shop);
      toast.success(`Found ${result.products.length} products from ${result.shop}`);
    } catch (error) {
      console.error('Failed to fetch Shopify products:', error);
      toast.error('Failed to fetch products from Shopify store');
    } finally {
      setShopifyLoading(false);
    }
  };

  const handleShopifyProductSelection = (productId: string, selected: boolean) => {
    setShopifyProducts((prev) => prev.map((p) => (p.id === productId ? { ...p, selected } : p)));
  };

  const handleImportShopifyProducts = async () => {
    const selectedProducts = shopifyProducts.filter((p) => p.selected);

    if (selectedProducts.length === 0) {
      toast.error('Please select at least one product to import');
      return;
    }

    try {
      setIsImporting(true);

      const result = await importShopifyProducts({
        organizationId: selectedOrganizationId!,
        selectedProductIds: selectedProducts.map((p) => p.id),
      });

      // Store imported products in onboarding state
      const importedProducts: ImportedProduct[] = selectedProducts.map((p) => ({
        id: p.id,
        name: p.name,
        imageUrl: p.imageUrl,
        productUrl: p.productUrl,
        selected: p.selected,
        confidence: 1.0, // Shopify products have 100% confidence
      }));

      updateFormData({
        productImportMethod: 'shopify',
        importedProducts,
      });

      toast.success(
        `Successfully imported ${result.importedCount} products from Shopify! AI analysis is running in the background.`
      );

      if (result.errors.length > 0) {
        console.warn('Some products had import errors:', result.errors);
        toast.error(`${result.errors.length} products had import errors. Check console for details.`);
      }

      handleNext();
    } catch (error) {
      console.error('Error importing Shopify products:', error);
      toast.error('Failed to import products from Shopify');
    } finally {
      setIsImporting(false);
    }
  };

  const handleSkip = () => {
    updateFormData({
      productImportMethod: 'skip',
      importedProducts: [],
    });
    handleNext();
  };

  const handleNext = () => {
    completeStep('product-import');
    setCurrentStep('brand-guide');
  };

  const handleBack = () => {
    setCurrentStep('website-url');
  };

  // Show method selection if no method chosen
  if (!selectedMethod) {
    return (
      <div className='max-w-2xl mx-auto space-y-8'>
        {/* Header */}
        <div className='text-center space-y-4 pt-8'>
          <h1 className='font-display text-3xl md:text-4xl font-bold text-[#676D50]'>Import your products</h1>
          <p className='text-lg text-[#676D50]/80'>
            Choose how you'd like to import your products for better AI-generated marketing assets
          </p>
        </div>

        {/* Import Method Options */}
        <div className='space-y-4'>
          {/* Website Import Option */}
          <button
            onClick={() => handleMethodSelect('website')}
            className='w-full p-6 bg-white/60 backdrop-blur-sm rounded-xl border-2 border-[#B5B178]/20 hover:border-[#676D50]/50 transition-all duration-200 text-left group'
          >
            <div className='flex items-start gap-4'>
              <div className='w-12 h-12 bg-[#676D50]/10 rounded-xl flex items-center justify-center group-hover:bg-[#676D50]/20 transition-colors'>
                <Globe className='w-6 h-6 text-[#676D50]' />
              </div>
              <div className='flex-1'>
                <h3 className='font-display font-semibold text-[#676D50] mb-2'>Import from Website</h3>
                <p className='text-sm text-[#676D50]/80 mb-3'>
                  Automatically discover and import products from your website's sitemap
                </p>
                <div className='text-xs text-[#676D50]/60'>
                  ✓ Works with most e-commerce platforms
                  <br />
                  ✓ Automatic product discovery
                  <br />
                  ✓ Choose which products to import
                  <br />
                  <span className='text-amber-600'>⚠ May not work with DDoS-protected sites</span>
                </div>
              </div>
            </div>
          </button>

          {/* Shopify Import Option */}
          <button
            onClick={() => handleMethodSelect('shopify')}
            className='w-full p-6 bg-white/60 backdrop-blur-sm rounded-xl border-2 border-[#B5B178]/20 hover:border-[#676D50]/50 transition-all duration-200 text-left group'
          >
            <div className='flex items-start gap-4'>
              <div className='w-12 h-12 bg-[#676D50]/10 rounded-xl flex items-center justify-center group-hover:bg-[#676D50]/20 transition-colors'>
                <ShoppingBag className='w-6 h-6 text-[#676D50]' />
              </div>
              <div className='flex-1'>
                <h3 className='font-display font-semibold text-[#676D50] mb-2'>Connect Shopify Store</h3>
                <p className='text-sm text-[#676D50]/80 mb-3'>
                  Import products directly from your Shopify store with full details
                </p>
                <div className='text-xs text-[#676D50]/60'>
                  ✓ Complete product information
                  <br />
                  ✓ Inventory and pricing data
                  <br />✓ Product categories and collections
                </div>
              </div>
            </div>
          </button>

          {/* Skip Option */}
          <button
            onClick={() => handleMethodSelect('skip')}
            className='w-full p-6 bg-white/40 backdrop-blur-sm rounded-xl border-2 border-dashed border-[#B5B178]/30 hover:border-[#676D50]/50 transition-all duration-200 text-left group'
          >
            <div className='flex items-start gap-4'>
              <div className='w-12 h-12 bg-[#676D50]/10 rounded-xl flex items-center justify-center group-hover:bg-[#676D50]/20 transition-colors'>
                <SkipForward className='w-6 h-6 text-[#676D50]' />
              </div>
              <div className='flex-1'>
                <h3 className='font-display font-semibold text-[#676D50] mb-2'>Skip for now</h3>
                <p className='text-sm text-[#676D50]/80'>
                  Continue without importing products. You can add them manually later.
                </p>
              </div>
            </div>
          </button>
        </div>

        {/* Navigation */}
        <div className='flex justify-between pt-6'>
          <button
            onClick={handleBack}
            className='flex items-center gap-2 px-6 py-3 text-[#676D50] hover:text-[#849068] transition-colors'
          >
            <ArrowLeft className='w-4 h-4' />
            Back
          </button>
        </div>
      </div>
    );
  }

  // Show website import flow
  if (selectedMethod === 'website') {
    return (
      <div className='max-w-4xl mx-auto space-y-8'>
        {/* Header */}
        <div className='text-center space-y-4'>
          <div className='inline-flex items-center justify-center w-16 h-16 bg-[#676D50] rounded-2xl mb-4'>
            <Globe className='w-8 h-8 text-[#F8F4DF]' />
          </div>

          <h1 className='font-display text-3xl md:text-4xl font-bold text-[#676D50]'>
            Importing from {formData.websiteUrl}
          </h1>
          <p className='text-lg text-[#676D50]/80'>Discovering products from your website...</p>
        </div>

        {/* Progress or Error */}
        {error ? (
          <div className='bg-red-50 border border-red-200 text-red-600 p-6 rounded-xl'>
            <div className='flex items-start gap-3'>
              <div className='w-6 h-6 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5'>
                <span className='text-red-600 text-sm font-bold'>!</span>
              </div>
              <div className='flex-1'>
                <p className='font-medium mb-2'>Website Import Failed</p>
                <p className='text-sm mb-4'>{error}</p>

                {error.includes('DDoS protection') && (
                  <div className='bg-blue-50 border border-blue-200 text-blue-700 p-4 rounded-lg mb-4'>
                    <p className='font-medium text-sm mb-2'>💡 Alternative Options:</p>
                    <ul className='text-sm space-y-1'>
                      <li>• Connect your Shopify store for direct access</li>
                      <li>• Skip for now and add products manually later</li>
                      <li>• Try a different website if you have multiple stores</li>
                    </ul>
                  </div>
                )}

                <div className='flex gap-3'>
                  <button
                    onClick={() => {
                      setSelectedMethod(null);
                      setError(null);
                    }}
                    className='text-sm text-red-600 hover:text-red-700 underline'
                  >
                    Try different method
                  </button>

                  {error.includes('DDoS protection') && (
                    <button
                      onClick={() => {
                        setSelectedMethod('shopify');
                        setError(null);
                      }}
                      className='text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 transition-colors'
                    >
                      Try Shopify instead
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        ) : isImporting ? (
          <div className='bg-white/60 backdrop-blur-sm rounded-xl p-8 text-center'>
            <Loader2 className='w-8 h-8 animate-spin text-[#676D50] mx-auto mb-4' />
            <p className='text-[#676D50] font-medium mb-2'>Discovering products...</p>
            <div className='w-full bg-[#B5B178]/20 rounded-full h-2 mb-2'>
              <div
                className='bg-[#676D50] h-2 rounded-full transition-all duration-300'
                style={{ width: `${importProgress}%` }}
              />
            </div>
            <p className='text-sm text-[#676D50]/60'>{importProgress}% complete</p>
          </div>
        ) : discoveredProducts.length > 0 ? (
          <div className='space-y-6'>
            <div className='bg-white/60 backdrop-blur-sm rounded-xl p-6'>
              <h3 className='font-display font-semibold text-[#676D50] mb-4'>
                Found {discoveredProducts.length} products - Select which ones to import:
              </h3>

              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto'>
                {discoveredProducts.map((product) => (
                  <div
                    key={product.id}
                    className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                      product.selected
                        ? 'border-[#676D50] bg-[#676D50]/5'
                        : 'border-[#B5B178]/20 hover:border-[#676D50]/30'
                    }`}
                    onClick={() => handleProductSelection(product.id, !product.selected)}
                  >
                    {product.imageUrl && (
                      <img
                        src={product.imageUrl}
                        alt={product.name}
                        className='w-full h-32 object-cover rounded-lg mb-3'
                      />
                    )}
                    <h4 className='font-medium text-[#676D50] text-sm mb-1 line-clamp-2'>{product.name}</h4>
                    <p className='text-xs text-[#676D50]/60'>Confidence: {Math.round(product.confidence * 100)}%</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Import Button */}
            <div className='flex justify-between items-center'>
              <button
                onClick={() => setSelectedMethod(null)}
                className='flex items-center gap-2 px-6 py-3 text-[#676D50] hover:text-[#849068] transition-colors'
              >
                <ArrowLeft className='w-4 h-4' />
                Choose different method
              </button>

              <button
                onClick={handleImportSelected}
                disabled={discoveredProducts.filter((p) => p.selected).length === 0 || isImporting}
                className='flex items-center gap-2 px-8 py-4 bg-[#676D50] text-[#F8F4DF] rounded-xl hover:bg-[#849068] transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
              >
                {isImporting ? (
                  <>
                    <Loader2 className='w-4 h-4 animate-spin' />
                    Importing...
                  </>
                ) : (
                  <>
                    Import {discoveredProducts.filter((p) => p.selected).length} products
                    <ArrowRight className='w-4 h-4' />
                  </>
                )}
              </button>
            </div>
          </div>
        ) : null}
      </div>
    );
  }

  // Show Shopify import flow
  if (selectedMethod === 'shopify') {
    if (!shopifyConnected) {
      // Show Shopify connection form
      return (
        <div className='max-w-2xl mx-auto space-y-8'>
          {/* Header */}
          <div className='text-center space-y-4'>
            <div className='inline-flex items-center justify-center w-16 h-16 bg-[#676D50] rounded-2xl mb-4'>
              <ShoppingBag className='w-8 h-8 text-[#F8F4DF]' />
            </div>

            <h1 className='font-display text-3xl md:text-4xl font-bold text-[#676D50]'>Connect your Shopify store</h1>
            <p className='text-lg text-[#676D50]/80'>Enter your Shopify store domain to import your products</p>
          </div>

          {/* Shopify Connection Form */}
          <div className='bg-white/60 backdrop-blur-sm rounded-xl p-8 space-y-6'>
            <div>
              <label className='block text-sm font-medium text-[#676D50] mb-2'>Shopify Store Domain</label>
              <div className='relative'>
                <input
                  type='text'
                  value={shopDomain}
                  onChange={(e) => setShopDomain(e.target.value)}
                  placeholder='your-store-name'
                  className='w-full px-4 py-3 bg-white/80 border border-[#B5B178]/30 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#676D50]/50 focus:border-transparent'
                />
                <div className='absolute right-3 top-1/2 transform -translate-y-1/2 text-[#676D50]/60 text-sm'>
                  .myshopify.com
                </div>
              </div>
              <p className='text-xs text-[#676D50]/60 mt-2'>
                Enter just the store name (e.g., "my-store" for my-store.myshopify.com)
              </p>
            </div>

            <button
              onClick={handleShopifyConnect}
              disabled={!shopDomain.trim() || shopifyLoading}
              className='w-full flex items-center justify-center gap-2 px-8 py-4 bg-[#676D50] text-[#F8F4DF] rounded-xl hover:bg-[#849068] transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
            >
              {shopifyLoading ? (
                <>
                  <Loader2 className='w-4 h-4 animate-spin' />
                  Connecting...
                </>
              ) : (
                <>
                  Connect to Shopify
                  <ExternalLink className='w-4 h-4' />
                </>
              )}
            </button>

            <div className='text-center'>
              <p className='text-xs text-[#676D50]/60'>You'll be redirected to Shopify to authorize the connection</p>
            </div>
          </div>

          {/* Navigation */}
          <div className='flex justify-between pt-6'>
            <button
              onClick={() => setSelectedMethod(null)}
              className='flex items-center gap-2 px-6 py-3 text-[#676D50] hover:text-[#849068] transition-colors'
            >
              <ArrowLeft className='w-4 h-4' />
              Choose different method
            </button>
          </div>
        </div>
      );
    } else {
      // Show Shopify products selection
      return (
        <div className='max-w-4xl mx-auto space-y-8'>
          {/* Header */}
          <div className='text-center space-y-4'>
            <div className='inline-flex items-center justify-center w-16 h-16 bg-[#676D50] rounded-2xl mb-4'>
              <Check className='w-8 h-8 text-[#F8F4DF]' />
            </div>

            <h1 className='font-display text-3xl md:text-4xl font-bold text-[#676D50]'>Connected to {shopifyShop}</h1>
            <p className='text-lg text-[#676D50]/80'>Select products to import from your Shopify store</p>
          </div>

          {/* Loading or Products */}
          {shopifyLoading ? (
            <div className='bg-white/60 backdrop-blur-sm rounded-xl p-8 text-center'>
              <Loader2 className='w-8 h-8 animate-spin text-[#676D50] mx-auto mb-4' />
              <p className='text-[#676D50] font-medium'>Loading products from Shopify...</p>
            </div>
          ) : shopifyProducts.length > 0 ? (
            <div className='space-y-6'>
              <div className='bg-white/60 backdrop-blur-sm rounded-xl p-6'>
                <h3 className='font-display font-semibold text-[#676D50] mb-4'>
                  Found {shopifyProducts.length} products - Select which ones to import:
                </h3>

                <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto'>
                  {shopifyProducts.map((product) => (
                    <div
                      key={product.id}
                      className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                        product.selected
                          ? 'border-[#676D50] bg-[#676D50]/5'
                          : 'border-[#B5B178]/20 hover:border-[#676D50]/30'
                      }`}
                      onClick={() => handleShopifyProductSelection(product.id, !product.selected)}
                    >
                      {product.imageUrl && (
                        <img
                          src={product.imageUrl}
                          alt={product.name}
                          className='w-full h-32 object-cover rounded-lg mb-3'
                        />
                      )}
                      <h4 className='font-medium text-[#676D50] text-sm mb-1 line-clamp-2'>{product.name}</h4>
                      <p className='text-xs text-[#676D50]/60 mb-1'>
                        {product.vendor} • {product.productType}
                      </p>
                      <p className='text-xs font-medium text-[#676D50]'>
                        ${product.price} {product.currency}
                      </p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Import Button */}
              <div className='flex justify-between items-center'>
                <button
                  onClick={() => {
                    setShopifyConnected(false);
                    setShopifyProducts([]);
                    setShopifyShop('');
                  }}
                  className='flex items-center gap-2 px-6 py-3 text-[#676D50] hover:text-[#849068] transition-colors'
                >
                  <ArrowLeft className='w-4 h-4' />
                  Disconnect and try different method
                </button>

                <button
                  onClick={handleImportShopifyProducts}
                  disabled={shopifyProducts.filter((p) => p.selected).length === 0 || isImporting}
                  className='flex items-center gap-2 px-8 py-4 bg-[#676D50] text-[#F8F4DF] rounded-xl hover:bg-[#849068] transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
                >
                  {isImporting ? (
                    <>
                      <Loader2 className='w-4 h-4 animate-spin' />
                      Importing...
                    </>
                  ) : (
                    <>
                      Import {shopifyProducts.filter((p) => p.selected).length} products
                      <ArrowRight className='w-4 h-4' />
                    </>
                  )}
                </button>
              </div>
            </div>
          ) : (
            <div className='bg-white/60 backdrop-blur-sm rounded-xl p-8 text-center'>
              <p className='text-[#676D50] font-medium mb-4'>No products found in your Shopify store</p>
              <button onClick={() => setSelectedMethod(null)} className='text-[#676D50] hover:text-[#849068] underline'>
                Try a different import method
              </button>
            </div>
          )}
        </div>
      );
    }
  }

  return null;
};
