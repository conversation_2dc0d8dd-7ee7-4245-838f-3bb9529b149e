import React, { useState, useRef } from 'react';
import { useOnboardingStore } from '../../../domain/store';
import { useOrganizationState } from '../../../../../organization/store';
import { api } from 'wasp/client/api';
import { ArrowRight, ArrowLeft, FileText, Upload, Link } from 'lucide-react';

export const BrandGuideStep: React.FC = () => {
  const { formData, updateFormData, setCurrentStep, completeStep } = useOnboardingStore();
  const { selectedOrganizationId } = useOrganizationState();
  const [uploadMethod, setUploadMethod] = useState<'url' | 'upload'>('url');
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success' | 'error'>('idle');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleBrandGuideUrlChange = (url: string) => {
    updateFormData({ brandGuideUrl: url });
  };

  const handleFileUpload = async (file: File) => {
    if (!file) return;

    setUploadStatus('uploading');
    setUploadedFile(file);

    try {
      console.log('Uploading brand guide file:', file.name);

      // Create FormData for file upload
      const uploadFormData = new FormData();
      uploadFormData.append('brandGuide', file);

      // Add required fields - get from onboarding store and organization context
      uploadFormData.append('brandName', formData.companyName || formData.brandName || 'My Brand');
      uploadFormData.append('organizationId', selectedOrganizationId || '');

      if (!selectedOrganizationId) {
        throw new Error('No organization selected. Please select an organization first.');
      }

      // Upload to the brand guide upload API using WASP api client
      const response = await api.post('/api/onboarding/brand-guide/upload', uploadFormData, {
        headers: {
          // Don't set Content-Type - let the browser set it for FormData
        },
      });

      // WASP api client automatically handles response parsing
      const result = response.data;
      console.log('Brand guide uploaded successfully:', result);

      // Update form data with the uploaded file info
      console.log('[BrandGuideStep] Upload result structure:', result);
      console.log('[BrandGuideStep] Updating form data with:', {
        brandGuideFile: result.data.r2Url, // ✅ Fixed: result.data.r2Url instead of result.r2Url
        brandGuideFileName: file.name,
      });

      updateFormData({
        brandGuideFile: result.data.r2Url, // ✅ Fixed: result.data.r2Url instead of result.r2Url
        brandGuideFileName: file.name,
      });

      setUploadStatus('success');

      // Verify the form data was updated
      setTimeout(() => {
        console.log('[BrandGuideStep] Form data after update:', {
          brandGuideFile: formData.brandGuideFile,
          brandGuideFileName: formData.brandGuideFileName,
        });
      }, 100);
    } catch (error) {
      console.error('Brand guide upload failed:', error);

      // Extract error message from WASP API response
      const errorMessage = error?.response?.data?.error || error?.message || 'Upload failed';

      console.error('Upload error details:', errorMessage);
      setUploadStatus('error');
      setUploadedFile(null);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleDropZoneClick = () => {
    fileInputRef.current?.click();
  };

  const handleNext = () => {
    console.log('[BrandGuideStep] Moving to next step. Current form data:', {
      brandGuideFile: formData.brandGuideFile,
      brandGuideFileName: formData.brandGuideFileName,
      brandGuideUrl: formData.brandGuideUrl,
    });

    completeStep('brand-guide');
    setCurrentStep('asset-folder');
  };

  const handleSkip = () => {
    updateFormData({ brandGuideUrl: '', brandGuideFile: '', brandGuideFileName: '' });
    completeStep('brand-guide');
    setCurrentStep('asset-folder');
  };

  const handleBack = () => {
    // Go back to product import step
    setCurrentStep('product-import');
  };

  return (
    <div className='max-w-2xl mx-auto space-y-8'>
      {/* Header */}
      <div className='text-center space-y-4 pt-8'>
        <h1 className='font-display text-3xl md:text-4xl font-bold text-[#676D50]'>Upload your brand guide</h1>
        <p className='text-lg text-[#676D50]/80'>
          Help us understand your brand better with your existing brand guidelines
        </p>
      </div>

      {/* Upload Method Selection */}
      <div className='space-y-6'>
        <div className='flex gap-4'>
          <button
            onClick={() => setUploadMethod('url')}
            className={`flex-1 p-4 rounded-xl border-2 transition-all ${
              uploadMethod === 'url' ? 'border-[#676D50] bg-[#676D50]/5' : 'border-[#B5B178]/30 hover:border-[#9EA581]'
            }`}
          >
            <Link className='w-6 h-6 mx-auto mb-2 text-[#676D50]' />
            <div className='text-sm font-medium text-[#676D50]'>Share URL</div>
          </button>

          <button
            onClick={() => setUploadMethod('upload')}
            className={`flex-1 p-4 rounded-xl border-2 transition-all ${
              uploadMethod === 'upload'
                ? 'border-[#676D50] bg-[#676D50]/5'
                : 'border-[#B5B178]/30 hover:border-[#9EA581]'
            }`}
          >
            <Upload className='w-6 h-6 mx-auto mb-2 text-[#676D50]' />
            <div className='text-sm font-medium text-[#676D50]'>Upload File</div>
          </button>
        </div>

        {/* URL Input */}
        {uploadMethod === 'url' && (
          <div className='space-y-2'>
            <label className='block text-sm font-medium text-[#676D50]'>Brand Guide URL</label>
            <input
              type='url'
              value={formData.brandGuideUrl || ''}
              onChange={(e) => handleBrandGuideUrlChange(e.target.value)}
              placeholder='https://drive.google.com/file/d/...'
              className='w-full px-4 py-4 text-lg border-2 border-[#B5B178]/30 rounded-xl focus:outline-none focus:border-[#676D50] transition-colors'
            />
            <p className='text-xs text-[#676D50]/60'>Share a link to your brand guide (Google Drive, Dropbox, etc.)</p>
          </div>
        )}

        {/* File Upload */}
        {uploadMethod === 'upload' && (
          <div className='space-y-2'>
            <label className='block text-sm font-medium text-[#676D50]'>Upload Brand Guide</label>
            <div
              onClick={handleDropZoneClick}
              className={`border-2 border-dashed rounded-xl p-8 text-center transition-colors cursor-pointer ${
                uploadStatus === 'uploading'
                  ? 'border-[#9EA581] bg-[#9EA581]/5'
                  : uploadStatus === 'success'
                    ? 'border-green-500 bg-green-50'
                    : uploadStatus === 'error'
                      ? 'border-red-500 bg-red-50'
                      : 'border-[#B5B178]/30 hover:border-[#9EA581]'
              }`}
            >
              {uploadStatus === 'uploading' ? (
                <>
                  <div className='w-12 h-12 mx-auto mb-4 animate-spin'>
                    <div className='w-12 h-12 border-4 border-[#9EA581]/20 border-t-[#9EA581] rounded-full'></div>
                  </div>
                  <p className='text-[#676D50] font-medium mb-2'>Uploading {uploadedFile?.name}...</p>
                  <p className='text-sm text-[#676D50]/60'>Please wait while we process your file</p>
                </>
              ) : uploadStatus === 'success' ? (
                <>
                  <FileText className='w-12 h-12 text-green-600 mx-auto mb-4' />
                  <p className='text-green-700 font-medium mb-2'>✅ {uploadedFile?.name} uploaded successfully!</p>
                  <p className='text-sm text-green-600/80'>Your brand guide is ready for analysis</p>
                </>
              ) : uploadStatus === 'error' ? (
                <>
                  <Upload className='w-12 h-12 text-red-500 mx-auto mb-4' />
                  <p className='text-red-700 font-medium mb-2'>❌ Upload failed</p>
                  <p className='text-sm text-red-600/80'>Click to try again</p>
                </>
              ) : (
                <>
                  <Upload className='w-12 h-12 text-[#676D50]/50 mx-auto mb-4' />
                  <p className='text-[#676D50] font-medium mb-2'>Drop your brand guide here or click to browse</p>
                  <p className='text-sm text-[#676D50]/60'>Supports PDF, DOC, DOCX files up to 10MB</p>
                </>
              )}
              <input
                ref={fileInputRef}
                type='file'
                accept='.pdf,.doc,.docx'
                className='hidden'
                onChange={handleFileInputChange}
              />
            </div>
          </div>
        )}

        {/* What we'll extract */}
        <div className='bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-[#B5B178]/20'>
          <h3 className='font-display font-semibold text-[#676D50] mb-3'>What we'll extract from your brand guide:</h3>
          <ul className='space-y-2 text-sm text-[#676D50]/80'>
            <li className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-[#9EA581] rounded-full' />
              Brand colors and color codes
            </li>
            <li className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-[#9EA581] rounded-full' />
              Typography and font specifications
            </li>
            <li className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-[#9EA581] rounded-full' />
              Logo usage guidelines
            </li>
            <li className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-[#9EA581] rounded-full' />
              Brand voice and tone
            </li>
            <li className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-[#9EA581] rounded-full' />
              Visual style preferences
            </li>
          </ul>
        </div>
      </div>

      {/* Navigation */}
      <div className='flex items-center justify-between pt-8'>
        <button
          onClick={handleBack}
          className='inline-flex items-center gap-2 text-[#676D50]/70 hover:text-[#676D50] transition-colors'
        >
          <ArrowLeft className='w-4 h-4' />
          Back
        </button>

        <div className='flex items-center gap-3'>
          <button onClick={handleSkip} className='text-[#676D50]/70 hover:text-[#676D50] transition-colors px-4 py-2'>
            Skip for now
          </button>

          <button
            onClick={handleNext}
            className='inline-flex items-center gap-3 bg-[#676D50] hover:bg-[#849068] text-[#F8F4DF] px-6 py-3 rounded-xl font-semibold transition-colors'
          >
            Continue
            <ArrowRight className='w-4 h-4' />
          </button>
        </div>
      </div>
    </div>
  );
};
