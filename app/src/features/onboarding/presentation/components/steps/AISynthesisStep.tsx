import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useOnboardingStore } from '../../../domain/store';
import { useSynthesisProgress } from '../../hooks/useSynthesisProgress';
import { useAuth } from 'wasp/client/auth';
import { useOrganizationState } from '../../../../../organization/store';
import {
  ArrowRight,
  ArrowLeft,
  Sparkles,
  Loader2,
  CheckCircle,
  Palette,
  Users,
  Package,
  Globe,
  FileText,
} from 'lucide-react';

export const AISynthesisStep: React.FC = () => {
  const { formData, setCurrentStep, completeStep } = useOnboardingStore();
  const { data: user } = useAuth();
  const { selectedOrganizationId } = useOrganizationState();
  const location = useLocation();
  const { progress, isLoading, isConnected, startSynthesis, isCompleted, hasError, getCurrentStage, getStageSummary } =
    useSynthesisProgress();

  const [hasStarted, setHasStarted] = useState(false);
  const [isResuming, setIsResuming] = useState(false);

  // Check if we're resuming a session from URL params
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const resumeSessionId = urlParams.get('resumeSession');

    if (resumeSessionId) {
      console.log('[AISynthesisStep] Resuming session:', resumeSessionId);
      setIsResuming(true);
      setHasStarted(true);
      // The useSynthesisProgress hook should automatically connect to the existing session
    }
  }, [location.search]);

  const tasks = [
    { id: 'website_analysis', label: 'Extracting website content and brand data', icon: Globe },
    { id: 'brand_guide_processing', label: 'Converting PDF to images and analyzing', icon: FileText },
    { id: 'brand_kit_generation', label: 'Creating comprehensive brand guidelines', icon: Palette },
    { id: 'audience_generation', label: 'Generating detailed audience personas', icon: Users },
  ];

  // Map progress stages to our UI tasks
  const getTaskProgress = (taskId: string) => {
    const stage = progress.stages.find((s) => s.id === taskId);
    if (!stage) return { progress: 0, status: 'pending' };

    if (stage.status === 'completed') return { progress: 100, status: 'completed' };
    if (stage.status === 'running') return { progress: 50, status: 'running' };
    if (stage.status === 'error') return { progress: 0, status: 'error' };
    return { progress: 0, status: 'pending' };
  };

  const handleStartGeneration = async () => {
    if (!selectedOrganizationId) {
      console.error('No organization selected');
      return;
    }

    setHasStarted(true);

    try {
      console.log('[AISynthesisStep] Starting synthesis with form data:', formData);
      console.log('[AISynthesisStep] Brand guide file info:', {
        brandGuideFile: formData.brandGuideFile,
        brandGuideFileName: formData.brandGuideFileName,
        brandGuideUrl: formData.brandGuideUrl,
      });

      // Check if we have ANY brand guide data at all
      const hasBrandGuideData = !!(formData.brandGuideFile || formData.brandGuideUrl);
      console.log('[AISynthesisStep] Has brand guide data:', hasBrandGuideData);

      if (!hasBrandGuideData) {
        console.error(
          '[AISynthesisStep] ❌ NO BRAND GUIDE DATA FOUND! This means the upload did not save to form data properly.'
        );
        console.log('[AISynthesisStep] Full form data object:', JSON.stringify(formData, null, 2));
      }

      await startSynthesis({
        organizationId: selectedOrganizationId,
        websiteUrl: formData.websiteUrl,
        companyName: formData.companyName,
        businessType: formData.businessType,
        industry: formData.industry,
        targetAudience: formData.targetAudience,
        brandValues: formData.brandValues,
        primaryGoals: formData.primaryGoals,
        shopifyStore: formData.shopifyStore,
        brandGuideUrl: formData.brandGuideUrl,
        brandGuideFile: formData.brandGuideFile, // ✅ Include uploaded brand guide file
        brandGuideFileName: formData.brandGuideFileName, // ✅ Include filename for reference
      });
    } catch (error) {
      console.error('[AISynthesisStep] Synthesis failed:', error);
    }
  };

  const handleNext = () => {
    completeStep('ai-synthesis');
    setCurrentStep('complete');
  };

  const handleBack = () => {
    setCurrentStep('asset-folder');
  };

  // Show progress view if synthesis has started
  if (hasStarted || isLoading || isCompleted) {
    return (
      <div className='max-w-2xl mx-auto space-y-8'>
        {/* Header */}
        <div className='text-center space-y-4'>
          <div className='inline-flex items-center justify-center w-16 h-16 bg-[#676D50] rounded-2xl mb-4'>
            {isCompleted ? (
              <CheckCircle className='w-8 h-8 text-[#F8F4DF]' />
            ) : hasError ? (
              <div className='w-8 h-8 text-red-500'>❌</div>
            ) : (
              <Sparkles className='w-8 h-8 text-[#F8F4DF]' />
            )}
          </div>

          <h1 className='font-display text-3xl md:text-4xl font-bold text-[#676D50]'>
            {isCompleted
              ? 'Setup Complete!'
              : hasError
                ? 'Setup Failed'
                : isResuming
                  ? 'Resuming your setup...'
                  : 'Setting up your workspace...'}
          </h1>
          <p className='text-lg text-[#676D50]/80'>
            {isCompleted
              ? 'Your Creative Studio is ready to use'
              : hasError
                ? 'There was an issue setting up your workspace'
                : isResuming
                  ? 'We found your previous session and are continuing where you left off'
                  : 'Our Creative Synthesis is analyzing your brand and creating your personalized workspace'}
          </p>

          {isResuming && (
            <div className='text-sm text-blue-600 bg-blue-50 px-4 py-2 rounded-lg inline-block'>
              🔄 Resumed from previous session
            </div>
          )}

          {/* Connection Status */}
          {!isConnected && (
            <div className='text-sm text-orange-600 bg-orange-50 px-3 py-2 rounded-lg'>⚠️ Connecting to server...</div>
          )}
        </div>

        {/* Progress */}
        <div className='space-y-6'>
          <div className='space-y-2'>
            <div className='flex justify-between text-sm'>
              <span className='text-[#676D50]/70'>{progress.message}</span>
              <span className='text-[#676D50]/70'>{Math.round(progress.overallProgress)}%</span>
            </div>
            <div className='w-full bg-[#F6F3E5] rounded-full h-3'>
              <div
                className={`h-3 rounded-full transition-all duration-300 ${
                  hasError
                    ? 'bg-red-500'
                    : isCompleted
                      ? 'bg-green-500'
                      : 'bg-gradient-to-r from-[#676D50] to-[#849068]'
                }`}
                style={{ width: `${progress.overallProgress}%` }}
              />
            </div>
          </div>

          {/* Task List */}
          <div className='space-y-3'>
            {tasks.map((task) => {
              const taskProgress = getTaskProgress(task.id);
              const isTaskComplete = taskProgress.status === 'completed';
              const isTaskActive = taskProgress.status === 'running';
              const isTaskError = taskProgress.status === 'error';

              return (
                <div
                  key={task.id}
                  className='flex items-center gap-4 p-4 bg-white/60 rounded-xl border border-[#B5B178]/20'
                >
                  <div
                    className={`w-10 h-10 rounded-xl flex items-center justify-center relative ${
                      isTaskComplete
                        ? 'bg-[#676D50] text-white'
                        : isTaskActive
                          ? 'bg-[#9EA581] text-white'
                          : isTaskError
                            ? 'bg-red-500 text-white'
                            : 'bg-[#F6F3E5] text-[#676D50]/50'
                    }`}
                  >
                    {isTaskComplete ? (
                      <CheckCircle className='w-5 h-5' />
                    ) : isTaskActive ? (
                      <Loader2 className='w-5 h-5 animate-spin absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2' />
                    ) : isTaskError ? (
                      <div className='text-xs'>❌</div>
                    ) : (
                      <task.icon className='w-5 h-5' />
                    )}
                  </div>
                  <div className='flex-1'>
                    <div className='font-medium text-[#676D50]'>{task.label}</div>
                    {isTaskActive && (
                      <div className='w-full bg-[#F6F3E5] rounded-full h-1 mt-2'>
                        <div
                          className='bg-[#9EA581] h-1 rounded-full transition-all duration-300'
                          style={{ width: `${taskProgress.progress}%` }}
                        />
                      </div>
                    )}
                    {isTaskError && <div className='text-xs text-red-600 mt-1'>Failed</div>}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Navigation */}
        {isCompleted && (
          <div className='flex justify-center pt-8'>
            <button
              onClick={handleNext}
              className='inline-flex items-center gap-3 bg-[#676D50] hover:bg-[#849068] text-[#F8F4DF] px-8 py-4 rounded-xl font-semibold text-lg transition-colors'
            >
              Continue to Dashboard
              <ArrowRight className='w-5 h-5' />
            </button>
          </div>
        )}

        {/* Error State */}
        {hasError && (
          <div className='flex justify-center gap-4 pt-8'>
            <button
              onClick={handleBack}
              className='inline-flex items-center gap-2 text-[#676D50]/70 hover:text-[#676D50] transition-colors'
            >
              <ArrowLeft className='w-4 h-4' />
              Back
            </button>
            <button
              onClick={handleStartGeneration}
              className='inline-flex items-center gap-3 bg-[#676D50] hover:bg-[#849068] text-[#F8F4DF] px-8 py-4 rounded-xl font-semibold text-lg transition-colors'
            >
              Try Again
            </button>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className='max-w-2xl mx-auto space-y-8'>
      {/* Header */}
      <div className='text-center space-y-4 pt-8'>
        <h1 className='font-display text-3xl md:text-4xl font-bold text-[#676D50]'>Ready to create your workspace?</h1>
        <p className='text-lg text-[#676D50]/80'>
          Our Creative Synthesis will analyze your information and set up everything you need
        </p>
      </div>

      {/* What will be created */}
      <div className='space-y-4'>
        <h3 className='font-display font-semibold text-[#676D50] text-center mb-6'>
          Here's what we'll create for you:
        </h3>

        <div className='grid gap-4'>
          {tasks.map((task) => (
            <div
              key={task.id}
              className='flex items-center gap-4 p-4 bg-white/60 rounded-xl border border-[#B5B178]/20'
            >
              <div className='w-10 h-10 bg-[#9EA581] rounded-xl flex items-center justify-center'>
                <task.icon className='w-5 h-5 text-white' />
              </div>
              <div className='font-medium text-[#676D50]'>{task.label}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Navigation */}
      <div className='flex items-center justify-between pt-8'>
        <button
          onClick={handleBack}
          className='inline-flex items-center gap-2 text-[#676D50]/70 hover:text-[#676D50] transition-colors'
        >
          <ArrowLeft className='w-4 h-4' />
          Back
        </button>

        <button
          onClick={handleStartGeneration}
          className='inline-flex items-center gap-3 bg-[#676D50] hover:bg-[#849068] text-[#F8F4DF] px-8 py-4 rounded-xl font-semibold text-lg transition-colors'
        >
          <Sparkles className='w-5 h-5' />
          Start Setup
        </button>
      </div>
    </div>
  );
};
