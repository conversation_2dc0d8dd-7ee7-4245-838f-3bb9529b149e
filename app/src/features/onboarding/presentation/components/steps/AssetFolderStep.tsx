import React, { useState, useEffect } from 'react';
import { useOnboardingStore } from '../../../domain/store';
import { ArrowRight, ArrowLeft, Cloud, Check, ExternalLink, Loader2 } from 'lucide-react';
import { OnboardingCloudConnectButton } from '../OnboardingCloudConnectButton';
import { useQuery, getCloudConnections } from 'wasp/client/operations';
import { useOrganizationState } from '../../../../../organization/store';
import { CloudAssetConnection } from '../../../../assets/domain/types';

export const AssetFolderStep: React.FC = () => {
  const { selectedOrganizationId } = useOrganizationState();
  const { setCurrentStep, completeStep } = useOnboardingStore();
  const [connections, setConnections] = useState({
    googleDrive: false,
    oneDrive: false,
    dropbox: false,
  });
  const [connecting, setConnecting] = useState({
    googleDrive: false,
    oneDrive: false,
    dropbox: false,
  });
  const [connectionMessages, setConnectionMessages] = useState({
    googleDrive: '',
    oneDrive: '',
    dropbox: '',
  });

  const { isLoading } = useQuery(
    getCloudConnections,
    {
      organizationId: selectedOrganizationId!,
    },
    {
      enabled: !!selectedOrganizationId,
      onSuccess: ({ connections }: { connections: unknown }) => {
        const typedConnections = connections as CloudAssetConnection[];
        setConnections({
          googleDrive: typedConnections.some((c) => c.provider === 'google_drive' && c.isConnected),
          oneDrive: typedConnections.some((c) => c.provider === 'onedrive' && c.isConnected),
          dropbox: typedConnections.some((c) => c.provider === 'dropbox' && c.isConnected),
        });
      },
    }
  );

  // Check for OAuth callback parameters on component mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);

    // Check for Google Drive connection success
    if (urlParams.get('google_connected') === 'true') {
      const successMessage = urlParams.get('success') || 'Successfully connected to Google Drive!';
      handleConnectionSuccess('googleDrive', successMessage);

      // Clean up URL parameters
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('google_connected');
      newUrl.searchParams.delete('success');
      window.history.replaceState({}, '', newUrl.toString());
    }

    // Check for Google Drive connection error
    if (urlParams.get('google_error') === 'true') {
      const errorMessage = urlParams.get('error') || 'Failed to connect to Google Drive';
      handleConnectionError('googleDrive', errorMessage);

      // Clean up URL parameters
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('google_error');
      newUrl.searchParams.delete('error');
      window.history.replaceState({}, '', newUrl.toString());
    }

    // TODO: Add similar checks for OneDrive and Dropbox when their callbacks are updated
  }, []);

  const handleNext = () => {
    completeStep('asset-folder');
    setCurrentStep('ai-synthesis');
  };

  const handleBack = () => {
    setCurrentStep('brand-guide');
  };

  const handleSkip = () => {
    completeStep('asset-folder');
    setCurrentStep('ai-synthesis');
  };

  const handleConnectionStart = (provider: 'googleDrive' | 'oneDrive' | 'dropbox') => {
    setConnecting((prev) => ({ ...prev, [provider]: true }));
    setConnectionMessages((prev) => ({ ...prev, [provider]: 'Connecting...' }));
  };

  const handleConnectionSuccess = (provider: 'googleDrive' | 'oneDrive' | 'dropbox', message?: string) => {
    setConnections((prev) => ({ ...prev, [provider]: true }));
    setConnecting((prev) => ({ ...prev, [provider]: false }));
    setConnectionMessages((prev) => ({
      ...prev,
      [provider]: message || 'Successfully connected!',
    }));

    // Clear success message after 3 seconds
    setTimeout(() => {
      setConnectionMessages((prev) => ({ ...prev, [provider]: '' }));
    }, 3000);
  };

  const handleConnectionError = (provider: 'googleDrive' | 'oneDrive' | 'dropbox', error: string) => {
    setConnecting((prev) => ({ ...prev, [provider]: false }));
    setConnectionMessages((prev) => ({ ...prev, [provider]: error }));

    // Clear error message after 5 seconds
    setTimeout(() => {
      setConnectionMessages((prev) => ({ ...prev, [provider]: '' }));
    }, 5000);
  };

  const cloudProviders = [
    {
      id: 'googleDrive' as const,
      provider: 'google' as const,
      name: 'Google Drive',
      description: 'Access your Google Drive files and folders',
      color: 'bg-blue-500',
      icon: '📁',
      connected: connections.googleDrive,
      connecting: connecting.googleDrive,
      message: connectionMessages.googleDrive,
    },
    {
      id: 'oneDrive' as const,
      provider: 'onedrive' as const,
      name: 'Microsoft OneDrive',
      description: 'Connect to your OneDrive business or personal account',
      color: 'bg-blue-600',
      icon: '☁️',
      connected: connections.oneDrive,
      connecting: connecting.oneDrive,
      message: connectionMessages.oneDrive,
    },
    {
      id: 'dropbox' as const,
      provider: 'dropbox' as const,
      name: 'Dropbox',
      description: 'Sync your Dropbox files and shared folders',
      color: 'bg-blue-700',
      icon: '📦',
      connected: connections.dropbox,
      connecting: connecting.dropbox,
      message: connectionMessages.dropbox,
    },
  ];

  return (
    <div className='max-w-4xl mx-auto space-y-8'>
      {/* Header */}
      <div className='text-center space-y-4'>
        <div className='inline-flex items-center justify-center w-16 h-16 bg-[#676D50] rounded-2xl mb-4'>
          <Cloud className='w-8 h-8 text-[#F8F4DF]' />
        </div>

        <h1 className='font-display text-3xl md:text-4xl font-bold text-[#676D50]'>Connect your cloud storage</h1>
        <p className='text-lg text-[#676D50]/80'>
          Access your existing files from Google Drive, OneDrive, and Dropbox directly in your asset library
        </p>

        {!Object.values(connections).some(Boolean) && (
          <div className='inline-flex items-center gap-2 bg-blue-50 text-blue-700 px-4 py-2 rounded-full text-sm'>
            💡 Tip: You can always connect these later in your asset library settings
          </div>
        )}
      </div>

      {/* Cloud Storage Providers */}
      {isLoading ? (
        <div className='flex items-center justify-center'>
          <Loader2 className='w-4 h-4 animate-spin' />
        </div>
      ) : (
        <>
          <div className='space-y-4'>
            {cloudProviders.map((provider) => {
              return (
                <div
                  key={provider.id}
                  className='bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-[#B5B178]/20 shadow-lg'
                >
                  <div className='flex items-center justify-between'>
                    <div className='flex items-center gap-4'>
                      <div
                        className={`w-12 h-12 ${provider.color} rounded-xl flex items-center justify-center text-white text-xl`}
                      >
                        {provider.icon}
                      </div>
                      <div>
                        <h3 className='font-display font-semibold text-[#676D50] mb-1'>{provider.name}</h3>
                        <p className='text-sm text-[#676D50]/70'>{provider.description}</p>
                      </div>
                    </div>

                    <div className='flex flex-col items-end gap-2'>
                      <div className='flex items-center gap-3'>
                        {provider.connected ? (
                          <div className='flex items-center gap-2 text-green-600'>
                            <Check className='w-5 h-5' />
                            <span className='font-medium'>Connected</span>
                          </div>
                        ) : provider.connecting ? (
                          <div className='flex items-center gap-2 text-[#676D50]'>
                            <Loader2 className='w-5 h-5 animate-spin' />
                            <span className='font-medium'>Connecting...</span>
                          </div>
                        ) : (
                          <OnboardingCloudConnectButton
                            provider={provider.provider}
                            buttonText='Connect'
                            buttonClassName='inline-flex items-center gap-2 bg-[#676D50] hover:bg-[#849068] text-[#F8F4DF] px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
                            onStart={() => handleConnectionStart(provider.id)}
                            onSuccess={(message) => handleConnectionSuccess(provider.id, message)}
                            onError={(error) => handleConnectionError(provider.id, error)}
                          />
                        )}
                      </div>

                      {/* Connection message */}
                      {provider.message && (
                        <div
                          className={`text-sm px-3 py-1 rounded-full ${
                            provider.connected
                              ? 'bg-green-100 text-green-700'
                              : provider.connecting
                                ? 'bg-blue-100 text-blue-700'
                                : 'bg-red-100 text-red-700'
                          }`}
                        >
                          {provider.message}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Connection Summary */}
          {(connections.googleDrive || connections.oneDrive || connections.dropbox) && (
            <div className='bg-green-50 border border-green-200 rounded-2xl p-6 mb-6'>
              <div className='flex items-center gap-3 mb-4'>
                <div className='w-10 h-10 bg-green-500 rounded-full flex items-center justify-center'>
                  <Check className='w-6 h-6 text-white' />
                </div>
                <div>
                  <h3 className='font-display font-semibold text-green-800'>
                    Great! You&apos;ve connected {Object.values(connections).filter(Boolean).length} cloud storage
                    provider
                    {Object.values(connections).filter(Boolean).length !== 1 ? 's' : ''}
                  </h3>
                  <p className='text-green-700'>Your cloud files will be available in your asset library</p>
                </div>
              </div>

              <div className='flex flex-wrap gap-2'>
                {connections.googleDrive && (
                  <span className='inline-flex items-center gap-1 bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm'>
                    📁 Google Drive
                  </span>
                )}
                {connections.oneDrive && (
                  <span className='inline-flex items-center gap-1 bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm'>
                    ☁️ OneDrive
                  </span>
                )}
                {connections.dropbox && (
                  <span className='inline-flex items-center gap-1 bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm'>
                    📦 Dropbox
                  </span>
                )}
              </div>
            </div>
          )}
        </>
      )}

      {/* Benefits */}
      <div className='bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-[#B5B178]/20'>
        <h3 className='font-display font-semibold text-[#676D50] mb-6 text-center'>Why connect your cloud storage?</h3>

        <div className='grid md:grid-cols-2 gap-6'>
          <div className='space-y-4'>
            <div className='flex items-start gap-3'>
              <div className='w-2 h-2 bg-[#9EA581] rounded-full mt-2' />
              <div>
                <div className='font-medium text-[#676D50]'>Unified Access</div>
                <div className='text-sm text-[#676D50]/70'>
                  View all your assets in one place without duplicating files
                </div>
              </div>
            </div>
            <div className='flex items-start gap-3'>
              <div className='w-2 h-2 bg-[#9EA581] rounded-full mt-2' />
              <div>
                <div className='font-medium text-[#676D50]'>Smart Organization</div>
                <div className='text-sm text-[#676D50]/70'>Tag and organize cloud files alongside uploaded assets</div>
              </div>
            </div>
            <div className='flex items-start gap-3'>
              <div className='w-2 h-2 bg-[#9EA581] rounded-full mt-2' />
              <div>
                <div className='font-medium text-[#676D50]'>Real-time Sync</div>
                <div className='text-sm text-[#676D50]/70'>Always see the latest version of your cloud files</div>
              </div>
            </div>
          </div>

          <div className='space-y-4'>
            <div className='flex items-start gap-3'>
              <div className='w-2 h-2 bg-[#9EA581] rounded-full mt-2' />
              <div>
                <div className='font-medium text-[#676D50]'>No Storage Limits</div>
                <div className='text-sm text-[#676D50]/70'>Access unlimited files without using our storage</div>
              </div>
            </div>
            <div className='flex items-start gap-3'>
              <div className='w-2 h-2 bg-[#9EA581] rounded-full mt-2' />
              <div>
                <div className='font-medium text-[#676D50]'>Easy Collaboration</div>
                <div className='text-sm text-[#676D50]/70'>Share cloud assets with team members seamlessly</div>
              </div>
            </div>
            <div className='flex items-start gap-3'>
              <div className='w-2 h-2 bg-[#9EA581] rounded-full mt-2' />
              <div>
                <div className='font-medium text-[#676D50]'>Optional Setup</div>
                <div className='text-sm text-[#676D50]/70'>
                  You can always connect these later in your asset library
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className='flex items-center justify-between pt-8'>
        <button
          onClick={handleBack}
          className='inline-flex items-center gap-2 text-[#676D50]/70 hover:text-[#676D50] transition-colors'
        >
          <ArrowLeft className='w-4 h-4' />
          Back
        </button>

        <div className='flex items-center gap-4'>
          {!Object.values(connections).some(Boolean) && (
            <button
              onClick={handleSkip}
              className='inline-flex items-center gap-2 text-[#676D50]/70 hover:text-[#676D50] transition-colors'
            >
              Skip for now
              <ExternalLink className='w-4 h-4' />
            </button>
          )}

          <button
            onClick={handleNext}
            className='inline-flex items-center gap-3 bg-[#676D50] hover:bg-[#849068] text-[#F8F4DF] px-8 py-4 rounded-xl font-semibold text-lg transition-colors'
          >
            {Object.values(connections).some(Boolean)
              ? 'Continue with connected storage'
              : 'Continue without cloud storage'}
            <ArrowRight className='w-5 h-5' />
          </button>
        </div>
      </div>
    </div>
  );
};
