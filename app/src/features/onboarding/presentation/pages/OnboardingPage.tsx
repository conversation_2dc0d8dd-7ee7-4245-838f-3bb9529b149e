import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useOnboardingStore } from '../../domain/store';
import { OnboardingLayout } from '../components/OnboardingLayout';
import { IntroStep } from '../components/steps/IntroStep';
import { BusinessTypeStep } from '../components/steps/BusinessTypeStep';
import { WebsiteUrlStep } from '../components/steps/WebsiteUrlStep';
import { ProductImportStep } from '../components/steps/ProductImportStep';
import { BrandGuideStep } from '../components/steps/BrandGuideStep';
import { AISynthesisStep } from '../components/steps/AISynthesisStep';
import { AssetFolderStep } from '../components/steps/AssetFolderStep';
import { CompleteStep } from '../components/steps/CompleteStep';

const OnboardingPage: React.FC = () => {
  const location = useLocation();
  const { currentStep, setCurrentStep } = useOnboardingStore();

  // Handle URL parameters to navigate to specific steps
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const stepParam = urlParams.get('step');

    // If step parameter is provided and it's a valid step, navigate to it
    if (stepParam && isValidStep(stepParam)) {
      setCurrentStep(stepParam as any);
    }
  }, [location.search, setCurrentStep]);

  const isValidStep = (step: string): boolean => {
    const validSteps = [
      'intro',
      'business-type',
      'website-url',
      'product-import',
      'brand-guide',
      'ai-synthesis',
      'asset-folder',
      'complete',
    ];
    return validSteps.includes(step);
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'intro':
        return <IntroStep />;
      case 'business-type':
        return <BusinessTypeStep />;
      case 'website-url':
        return <WebsiteUrlStep />;
      case 'product-import':
        return <ProductImportStep />;
      case 'brand-guide':
        return <BrandGuideStep />;
      case 'ai-synthesis':
        return <AISynthesisStep />;
      case 'asset-folder':
        return <AssetFolderStep />;
      case 'complete':
        return <CompleteStep />;
      default:
        return <IntroStep />;
    }
  };

  return <OnboardingLayout>{renderCurrentStep()}</OnboardingLayout>;
};

export default OnboardingPage;
