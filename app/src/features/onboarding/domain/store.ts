import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type {
  OnboardingState,
  OnboardingStep,
  OnboardingFormData,
  OnboardingActions,
  TourState,
  TourStep,
  BusinessType,
  ProductImportMethod,
  ImportedProduct,
} from './types';

interface OnboardingStore extends OnboardingState, OnboardingActions {
  formData: OnboardingFormData;
  tour: TourState;
  importProgress: {
    isImporting: boolean;
    progress: number;
    message: string;
    error?: string;
  };
  setImportProgress: (progress: { isImporting: boolean; progress: number; message: string; error?: string }) => void;
}

const defaultTourSteps: TourStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to Olivia!',
    description:
      "Let's take a quick tour of your new Creative Studio. We'll show you the key features to get you started.",
    target: 'body',
    placement: 'center',
  },
  {
    id: 'brand-kit',
    title: 'Brand Kit',
    description:
      'Your brand colors, fonts, and assets are automatically organized here. This is where your brand identity lives.',
    target: '/brand-kit',
    placement: 'bottom',
  },
  {
    id: 'audiences',
    title: 'Audiences',
    description:
      "Your target audiences with detailed personas and character avatars. We've created primary, secondary, and tertiary audiences for you.",
    target: '/audiences',
    placement: 'bottom',
  },
  {
    id: 'products',
    title: 'Products',
    description: 'Manage your product catalog and generate marketing assets. Import from Shopify or add manually.',
    target: '/products',
    placement: 'bottom',
  },
  {
    id: 'canvas',
    title: 'Design Canvas',
    description: 'Create and edit marketing assets with AI assistance. This is where the magic happens!',
    target: '/canvas',
    placement: 'bottom',
  },
  {
    id: 'complete',
    title: "You're all set!",
    description: 'You now know the basics of Oliva. Start creating amazing marketing assets for your brand!',
    target: 'body',
    placement: 'center',
  },
];

export const useOnboardingStore = create<OnboardingStore>()(
  persist(
    (set, get) => ({
      // Initial state
      currentStep: 'intro',
      completedSteps: [],
      isComplete: false,
      formData: {
        businessType: 'ecommerce',
        websiteUrl: '',
        shopifyStore: '',
        brandGuideUrl: '',
        brandGuideFile: '',
        brandGuideFileName: '',
        companyName: '',
        brandName: '',
        industry: '',
        targetAudience: '',
        brandValues: '',
        primaryGoals: '',
        productImportMethod: undefined,
        importedProducts: [],
      },
      tour: {
        isActive: false,
        currentStepIndex: 0,
        steps: defaultTourSteps,
        isComplete: false,
      },
      importProgress: {
        isImporting: false,
        progress: 0,
        message: '',
        error: undefined,
      },

      // Actions
      setCurrentStep: (step: OnboardingStep) => {
        set({ currentStep: step });
      },

      completeStep: (step: OnboardingStep) => {
        const { completedSteps } = get();
        if (!completedSteps.includes(step)) {
          set({
            completedSteps: [...completedSteps, step],
            isComplete: step === 'complete',
          });
        }
      },

      updateFormData: (data: Partial<OnboardingFormData>) => {
        set((state) => ({
          formData: { ...state.formData, ...data },
        }));
      },

      resetOnboarding: () => {
        set({
          currentStep: 'intro',
          completedSteps: [],
          isComplete: false,
          formData: {
            businessType: 'ecommerce',
            websiteUrl: '',
            shopifyStore: '',
            brandGuideUrl: '',
            companyName: '',
            industry: '',
            targetAudience: '',
            brandValues: '',
            primaryGoals: '',
            productImportMethod: undefined,
            importedProducts: [],
          },
          tour: {
            isActive: false,
            currentStepIndex: 0,
            steps: defaultTourSteps,
            isComplete: false,
          },
        });
      },

      startTour: () => {
        set((state) => ({
          tour: {
            ...state.tour,
            isActive: true,
            currentStepIndex: 0,
            isComplete: false,
          },
        }));
      },

      nextTourStep: () => {
        const { tour } = get();
        const nextIndex = tour.currentStepIndex + 1;

        if (nextIndex >= tour.steps.length) {
          // Tour complete
          set((state) => ({
            tour: {
              ...state.tour,
              isActive: false,
              isComplete: true,
            },
          }));
        } else {
          set((state) => ({
            tour: {
              ...state.tour,
              currentStepIndex: nextIndex,
            },
          }));
        }
      },

      previousTourStep: () => {
        const { tour } = get();
        const prevIndex = Math.max(0, tour.currentStepIndex - 1);

        set((state) => ({
          tour: {
            ...state.tour,
            currentStepIndex: prevIndex,
          },
        }));
      },

      completeTour: () => {
        set((state) => ({
          tour: {
            ...state.tour,
            isActive: false,
            isComplete: true,
          },
        }));
      },

      pageIsLoaded: () => {
        // This can be used to trigger tour steps when pages load
        const { tour } = get();
        if (tour.isActive) {
          // Handle page-specific tour logic here
          console.log('Page loaded during tour, step:', tour.currentStepIndex);
        }
      },

      setImportProgress: (progress) => {
        set({ importProgress: progress });
      },
    }),
    {
      name: 'onboarding-store',
      partialize: (state) => ({
        currentStep: state.currentStep,
        completedSteps: state.completedSteps,
        isComplete: state.isComplete,
        formData: state.formData,
        tour: state.tour,
      }),
    }
  )
);
