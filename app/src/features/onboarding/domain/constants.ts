// Synthesis stage constants for AI-driven onboarding
export const SYNTHESIS_STAGES = {
  WEBSITE_CRAWLING: 'website_crawling',
  BRAND_GUIDE_ANALYSIS: 'brand_guide_analysis',
  SHOPIFY_DATA_PULL: 'shopify_data_pull',
  ASSET_INDEXING: 'asset_indexing',
  BRAND_KIT_GENERATION: 'brand_kit_generation',
  AUDIENCE_GENERATION: 'audience_generation',
  PRODUCT_PROCESSING: 'product_processing',
} as const;

export const SYNTHESIS_STAGE_MESSAGES = {
  [SYNTHESIS_STAGES.WEBSITE_CRAWLING]: {
    start: 'Analyzing your website...',
    progress: 'Extracting brand elements and content...',
    complete: 'Website analysis completed',
  },
  [SYNTHESIS_STAGES.BRAND_GUIDE_ANALYSIS]: {
    start: 'Processing brand guide...',
    progress: 'Extracting brand guidelines and assets...',
    complete: 'Brand guide analysis completed',
  },
  [SYNTHESIS_STAGES.SHOPIFY_DATA_PULL]: {
    start: 'Importing Shopify data...',
    progress: 'Fetching products and store information...',
    complete: 'Shopify data imported',
  },
  [SYNTHESIS_STAGES.ASSET_INDEXING]: {
    start: 'Indexing assets...',
    progress: 'Organizing and categorizing assets...',
    complete: 'Asset indexing completed',
  },
  [SYNTHESIS_STAGES.BRAND_KIT_GENERATION]: {
    start: 'Generating brand kit...',
    progress: 'Creating comprehensive brand guidelines...',
    complete: 'Brand kit generated successfully',
  },
  [SYNTHESIS_STAGES.AUDIENCE_GENERATION]: {
    start: 'Creating audience personas...',
    progress: 'Analyzing target demographics and behaviors...',
    complete: 'Audience personas created',
  },
  [SYNTHESIS_STAGES.PRODUCT_PROCESSING]: {
    start: 'Processing products...',
    progress: 'Organizing and categorizing products...',
    complete: 'Product processing completed',
  },
} as const;

// Onboarding step definitions
export const ONBOARDING_STEPS = [
  {
    id: 'intro',
    name: 'Welcome',
    description: 'Get started with Oliva',
  },
  {
    id: 'business-type',
    name: 'Business Type',
    description: 'Tell us about your business',
  },
  {
    id: 'website-url',
    name: 'Website Analysis',
    description: 'Analyze your website',
  },
  {
    id: 'product-import',
    name: 'Product Import',
    description: 'Import your products',
  },
  {
    id: 'brand-guide',
    name: 'Brand Guide',
    description: 'Upload your brand guidelines',
  },
  {
    id: 'asset-folder',
    name: 'Cloud Storage',
    description: 'Connect your cloud storage',
  },
  {
    id: 'ai-synthesis',
    name: 'AI Processing',
    description: 'Generate your brand assets',
  },
  {
    id: 'complete',
    name: 'Complete',
    description: 'Setup complete!',
  },
] as const;

export type OnboardingStepId = (typeof ONBOARDING_STEPS)[number]['id'];
export type SynthesisStageId = (typeof SYNTHESIS_STAGES)[keyof typeof SYNTHESIS_STAGES];
