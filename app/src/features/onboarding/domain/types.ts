// Onboarding domain types
export type OnboardingStep =
  | 'intro'
  | 'business-type'
  | 'website-url'
  | 'product-import'
  | 'brand-guide'
  | 'ai-synthesis'
  | 'asset-folder'
  | 'complete';

export type BusinessType = 'ecommerce' | 'saas' | 'agency' | 'consulting' | 'other';

export type ProductImportMethod = 'shopify' | 'website' | 'skip';

export interface ImportedProduct {
  id: string;
  name: string;
  imageUrl?: string;
  productUrl: string;
  selected: boolean;
  confidence: number;
}

export interface OnboardingState {
  currentStep: OnboardingStep;
  completedSteps: OnboardingStep[];
  businessType?: BusinessType;
  websiteUrl?: string;
  shopifyStore?: string;
  brandGuideUrl?: string;
  productImportMethod?: ProductImportMethod;
  importedProducts?: ImportedProduct[];
  isComplete: boolean;
}

export interface OnboardingFormData {
  businessType: BusinessType;
  websiteUrl: string;
  shopifyStore?: string;
  brandGuideUrl?: string;
  brandGuideFile?: string; // R2 URL of uploaded brand guide file
  brandGuideFileName?: string; // Original filename of uploaded brand guide
  companyName?: string;
  brandName?: string; // Alternative brand name field
  industry?: string;
  targetAudience?: string;
  brandValues?: string;
  primaryGoals?: string;
  productImportMethod?: ProductImportMethod;
  importedProducts?: ImportedProduct[];
}

export interface OnboardingProgress {
  step: number;
  totalSteps: number;
  message: string;
  isComplete: boolean;
}

export interface ImportProgress {
  taskId: string;
  progress: number;
  message: string;
  isComplete: boolean;
  error?: string;
}

// Tour navigation types
export interface TourStep {
  id: string;
  title: string;
  description: string;
  target?: string;
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'center';
  action?: () => void;
}

export interface TourState {
  isActive: boolean;
  currentStepIndex: number;
  steps: TourStep[];
  isComplete: boolean;
}

// Onboarding actions
export interface OnboardingActions {
  setCurrentStep: (step: OnboardingStep) => void;
  completeStep: (step: OnboardingStep) => void;
  updateFormData: (data: Partial<OnboardingFormData>) => void;
  resetOnboarding: () => void;
  startTour: () => void;
  nextTourStep: () => void;
  previousTourStep: () => void;
  completeTour: () => void;
  pageIsLoaded: () => void;
}

// Website analysis types
export interface WebsiteAnalysis {
  url: string;
  title?: string;
  description?: string;
  colors?: string[];
  fonts?: string[];
  images?: string[];
  content?: string;
  brandElements?: {
    logo?: string;
    primaryColor?: string;
    secondaryColors?: string[];
    typography?: string[];
  };
}

// AI synthesis types
export interface AISynthesisResult {
  brandGuide?: {
    colors: string[];
    fonts: string[];
    logoUrl?: string;
    brandVoice?: string;
    brandValues?: string[];
  };
  audiences?: {
    primary?: any;
    secondary?: any;
    tertiary?: any;
  };
  products?: any[];
  recommendations?: string[];
}
