import { z } from 'zod';

// Asset collection interface
export interface AssetCollection {
  id: string;
  name: string;
  description: string | null;
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
  userId: number;
  organizationId: string;
  projectId: string | null;

  // Related entities (optional for domain entity)
  assets?: any[]; // Asset references
  user?: any;
  organization?: any;
  project?: any;
}

// Validation schemas
export const CreateCollectionSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  isPublic: z.boolean().default(false),
  organizationId: z.string(),
  projectId: z.string().optional(),
});

export const UpdateCollectionSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  isPublic: z.boolean().optional(),
});

// Asset collection domain class with business logic
export class AssetCollectionEntity {
  constructor(private collection: AssetCollection) {}

  // Getters
  get id(): string {
    return this.collection.id;
  }

  get name(): string {
    return this.collection.name;
  }

  get description(): string | null {
    return this.collection.description;
  }

  get isPublic(): boolean {
    return this.collection.isPublic;
  }

  get userId(): number {
    return this.collection.userId;
  }

  get organizationId(): string {
    return this.collection.organizationId;
  }

  get projectId(): string | null {
    return this.collection.projectId;
  }

  get createdAt(): Date {
    return this.collection.createdAt;
  }

  get updatedAt(): Date {
    return this.collection.updatedAt;
  }

  // Business logic methods
  canBeEdited(userId: number): boolean {
    return this.collection.userId === userId;
  }

  canBeDeleted(userId: number): boolean {
    return this.collection.userId === userId;
  }

  canBeViewed(userId: number, organizationId: string): boolean {
    // Public collections can be viewed by anyone in the organization
    if (this.collection.isPublic && this.collection.organizationId === organizationId) {
      return true;
    }

    // Private collections can only be viewed by the owner
    return this.collection.userId === userId;
  }

  canAddAssets(userId: number): boolean {
    return this.collection.userId === userId;
  }

  canRemoveAssets(userId: number): boolean {
    return this.collection.userId === userId;
  }

  isProjectCollection(): boolean {
    return this.collection.projectId !== null;
  }

  isPersonalCollection(): boolean {
    return this.collection.projectId === null && !this.collection.isPublic;
  }

  isOrganizationCollection(): boolean {
    return this.collection.isPublic;
  }

  // Update methods
  updateName(newName: string): void {
    if (newName.trim().length === 0) {
      throw new Error('Collection name cannot be empty');
    }
    if (newName.length > 100) {
      throw new Error('Collection name cannot exceed 100 characters');
    }
    this.collection.name = newName.trim();
    this.collection.updatedAt = new Date();
  }

  updateDescription(newDescription: string | null): void {
    if (newDescription && newDescription.length > 500) {
      throw new Error('Collection description cannot exceed 500 characters');
    }
    this.collection.description = newDescription;
    this.collection.updatedAt = new Date();
  }

  updateVisibility(isPublic: boolean): void {
    this.collection.isPublic = isPublic;
    this.collection.updatedAt = new Date();
  }

  // Static factory methods
  static fromPrismaCollection(prismaCollection: any): AssetCollectionEntity {
    return new AssetCollectionEntity(prismaCollection as AssetCollection);
  }

  static createNew(data: z.infer<typeof CreateCollectionSchema> & { userId: number }): Partial<AssetCollection> {
    return {
      name: data.name.trim(),
      description: data.description || null,
      isPublic: data.isPublic,
      userId: data.userId,
      organizationId: data.organizationId,
      projectId: data.projectId || null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  // Validation methods
  static validateName(name: string): boolean {
    return name.trim().length > 0 && name.length <= 100;
  }

  static validateDescription(description: string | null): boolean {
    return description === null || description.length <= 500;
  }

  // Convert to plain object
  toPlainObject(): AssetCollection {
    return this.collection;
  }

  // Get summary information
  getSummary(): {
    id: string;
    name: string;
    description: string | null;
    isPublic: boolean;
    isProjectCollection: boolean;
    createdAt: Date;
    updatedAt: Date;
  } {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      isPublic: this.isPublic,
      isProjectCollection: this.isProjectCollection(),
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
