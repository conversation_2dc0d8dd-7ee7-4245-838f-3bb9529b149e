import { z } from 'zod';

// Asset status enumeration
export enum AssetStatus {
  ACTIVE = 'active',
  DELETED = 'deleted',
  PROCESSING = 'processing',
  FAILED = 'failed',
}

// Asset type enumeration
export enum AssetType {
  IMAGE = 'image',
  VIDEO = 'video',
  DOCUMENT = 'document',
  AUDIO = 'audio',
  OTHER = 'other',
}

// Asset source enumeration
export enum AssetSource {
  UPLOADED = 'uploaded',
  GOOGLE_DRIVE = 'google_drive',
  ONEDRIVE = 'onedrive',
  DROPBOX = 'dropbox',
}

// Asset category enumeration
export enum AssetCategory {
  BRAND = 'brand',
  PRODUCT = 'product',
  MARKETING = 'marketing',
  SOCIAL = 'social',
  GENERATED = 'generated',
  REFERENCE = 'reference',
}

// Note: PhotographyModel, AssetAnalytics, AssetTag, and AssetCollection
// are now imported from 'wasp/entities' as they are generated by WASP from schema.prisma

// All Asset, AssetTag, AssetCollection types are now generated by WASP
// Import them from 'wasp/entities' instead

// Validation schemas
export const AssetTagSchema = z.object({
  name: z.string().min(1).max(50),
  color: z.string().optional(),
  category: z.string().optional(),
});

export const AssetCollectionSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  isPublic: z.boolean().default(false),
});

export const AssetUploadSchema = z.object({
  fileName: z.string().min(1),
  fileType: z.string().min(1),
  fileUrl: z.string().url(),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  collectionIds: z.array(z.string()).optional(),
  organizationId: z.string(),
  width: z.number().optional(),
  height: z.number().optional(),
  altText: z.string().optional(),
  description: z.string().optional(),
  source: z.nativeEnum(AssetSource).default(AssetSource.UPLOADED),
  cloudFileId: z.string().optional(), // For cloud storage files
  cloudThumbnailUrl: z.string().optional(), // Cloud provider thumbnail
});

export const CloudAssetSchema = z.object({
  fileName: z.string().min(1),
  fileType: z.string().min(1),
  cloudFileId: z.string().min(1), // Required for cloud assets
  source: z
    .nativeEnum(AssetSource)
    .refine((val) => val !== AssetSource.UPLOADED, { message: 'Cloud assets must specify a cloud source' }),
  cloudThumbnailUrl: z.string().url().optional(),
  cloudWebViewUrl: z.string().url().optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  collectionIds: z.array(z.string()).optional(),
  organizationId: z.string(),
  width: z.number().optional(),
  height: z.number().optional(),
  altText: z.string().optional(),
  description: z.string().optional(),
  lastModified: z.date().optional(), // Cloud file last modified date
  cloudPath: z.string().optional(), // Full path in cloud storage
});

export const AssetUpdateSchema = z.object({
  fileName: z.string().optional(),
  category: z.string().optional(),
  altText: z.string().optional(),
  description: z.string().optional(),
  tags: z.array(z.string()).optional(),
  collectionIds: z.array(z.string()).optional(),
});

// Asset business logic can be implemented as utility functions
// that work with the WASP-generated Asset type
