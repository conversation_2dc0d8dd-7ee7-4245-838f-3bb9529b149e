import { z } from 'zod';
import type { Board, BoardAsset } from 'wasp/entities';

// Extended Board type with relationships for domain logic
interface BoardWithRelations extends Board {
  assets?: BoardAsset[];
  childBoards?: Board[];
  parentBoard?: Board | null;
}

// Note: Using WASP-generated Board and BoardAsset types as base
// Extended with relationships for domain logic

// Board view types
export enum BoardViewType {
  GALLERY = 'gallery',
  LIST = 'list',
  GRID = 'grid',
}

// Board asset status types
export enum BoardAssetStatus {
  NONE = 'None',
  APPROVED = 'Approved',
  REJECTED = 'Rejected',
  PENDING = 'Pending',
  IN_REVIEW = 'In Review',
}

// Validation schemas
export const CreateBoardSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).default(''),
  organizationId: z.string(),
  parentBoardId: z.string().optional(),
  listView: z.nativeEnum(BoardViewType).default(BoardViewType.GALLERY),
});

export const UpdateBoardSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  parentBoardId: z.string().optional(),
  listView: z.nativeEnum(BoardViewType).optional(),
});

export const AddAssetToBoardSchema = z.object({
  assetId: z.number(),
  boardId: z.string(),
  status: z.nativeEnum(BoardAssetStatus).default(BoardAssetStatus.NONE),
});

export const UpdateBoardAssetStatusSchema = z.object({
  boardAssetId: z.string(),
  status: z.nativeEnum(BoardAssetStatus),
});

// Board domain class with business logic
export class BoardEntity {
  constructor(private board: BoardWithRelations) {}

  // Getters
  get id(): string {
    return this.board.id;
  }

  get name(): string {
    return this.board.name;
  }

  get description(): string {
    return this.board.description;
  }

  get organizationId(): string {
    return this.board.organizationId;
  }

  get parentBoardId(): string | null {
    return this.board.parentBoardId;
  }

  get listView(): string | null {
    return this.board.listView;
  }

  get deletedAt(): Date | null {
    return this.board.deletedAt;
  }

  get createdAt(): Date {
    return this.board.createdAt;
  }

  get updatedAt(): Date {
    return this.board.updatedAt;
  }

  get assets(): BoardAsset[] {
    return this.board.assets || [];
  }

  get childBoards(): Board[] {
    return this.board.childBoards || [];
  }

  get parentBoard(): Board | null {
    return this.board.parentBoard || null;
  }

  // Business logic methods
  isRootBoard(): boolean {
    return this.board.parentBoardId === null;
  }

  isChildBoard(): boolean {
    return this.board.parentBoardId !== null;
  }

  hasChildren(): boolean {
    return this.childBoards.length > 0;
  }

  isDeleted(): boolean {
    return this.board.deletedAt !== null;
  }

  canBeDeleted(): boolean {
    // Can only delete if no child boards and no assets
    return this.childBoards.length === 0 && this.assets.length === 0;
  }

  canAddAssets(): boolean {
    return !this.isDeleted();
  }

  canCreateChildBoard(): boolean {
    return !this.isDeleted();
  }

  getAssetCount(): number {
    return this.assets.length;
  }

  getChildBoardCount(): number {
    return this.childBoards.length;
  }

  getViewType(): BoardViewType {
    return (this.board.listView as BoardViewType) || BoardViewType.GALLERY;
  }

  // Update methods
  updateName(newName: string): void {
    if (newName.trim().length === 0) {
      throw new Error('Board name cannot be empty');
    }
    if (newName.length > 100) {
      throw new Error('Board name cannot exceed 100 characters');
    }
    this.board.name = newName.trim();
    this.board.updatedAt = new Date();
  }

  updateDescription(newDescription: string): void {
    if (newDescription.length > 500) {
      throw new Error('Board description cannot exceed 500 characters');
    }
    this.board.description = newDescription;
    this.board.updatedAt = new Date();
  }

  updateListView(viewType: BoardViewType): void {
    this.board.listView = viewType;
    this.board.updatedAt = new Date();
  }

  moveToParent(newParentId: string | null): void {
    // Prevent circular references
    if (newParentId === this.board.id) {
      throw new Error('Board cannot be its own parent');
    }
    this.board.parentBoardId = newParentId;
    this.board.updatedAt = new Date();
  }

  softDelete(): void {
    if (!this.canBeDeleted()) {
      throw new Error('Cannot delete board with child boards or assets');
    }
    this.board.deletedAt = new Date();
    this.board.updatedAt = new Date();
  }

  restore(): void {
    this.board.deletedAt = null;
    this.board.updatedAt = new Date();
  }

  // Static factory methods
  static fromPrismaBoard(prismaBoard: any): BoardEntity {
    return new BoardEntity(prismaBoard as BoardWithRelations);
  }

  static createNew(data: z.infer<typeof CreateBoardSchema>): Partial<Board> {
    return {
      name: data.name.trim(),
      description: data.description,
      organizationId: data.organizationId,
      parentBoardId: data.parentBoardId || null,
      listView: data.listView,
      deletedAt: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  // Validation methods
  static validateName(name: string): boolean {
    return name.trim().length > 0 && name.length <= 100;
  }

  static validateDescription(description: string): boolean {
    return description.length <= 500;
  }

  // Convert to plain object
  toPlainObject(): BoardWithRelations {
    return this.board;
  }

  // Get summary information
  getSummary(): {
    id: string;
    name: string;
    description: string;
    isRootBoard: boolean;
    hasChildren: boolean;
    assetCount: number;
    childBoardCount: number;
    viewType: BoardViewType;
    createdAt: Date;
    updatedAt: Date;
  } {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      isRootBoard: this.isRootBoard(),
      hasChildren: this.hasChildren(),
      assetCount: this.getAssetCount(),
      childBoardCount: this.getChildBoardCount(),
      viewType: this.getViewType(),
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }

  // Get hierarchy path (simplified version - only returns current board name)
  // Note: Full hierarchy traversal would require repository access to load parent boards
  getHierarchyPath(): string[] {
    return [this.name];
  }

  // Alternative method that works with just parentBoardId
  // This would need to be implemented with repository calls to get parent names
  getHierarchyPathIds(): string[] {
    const path: string[] = [this.id];
    // Note: This would need repository access to traverse up the hierarchy
    // For now, just return the current board ID
    return path;
  }
}
