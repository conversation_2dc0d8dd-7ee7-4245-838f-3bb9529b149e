import { z } from 'zod';

// Tag category enumeration
export enum TagCategory {
  STYLE = 'style',
  CONTENT = 'content',
  PROJECT = 'project',
  BRAND = 'brand',
  PRODUCT = 'product',
  CAMPAIGN = 'campaign',
  CUSTOM = 'custom',
}

// Predefined color options for tags
export const TAG_COLORS = {
  RED: '#ef4444',
  ORANGE: '#f97316',
  YELLOW: '#eab308',
  GREEN: '#22c55e',
  BLUE: '#3b82f6',
  INDIGO: '#6366f1',
  PURPLE: '#a855f7',
  PINK: '#ec4899',
  GRAY: '#6b7280',
  SLATE: '#64748b',
} as const;

// Asset tag interface
export interface AssetTag {
  id: string;
  name: string;
  color: string | null;
  category: string | null;
  createdAt: Date;
  updatedAt: Date;
  userId: number;
  organizationId: string;

  // Related entities (optional for domain entity)
  assets?: any[]; // Asset references
  user?: any;
  organization?: any;
}

// Validation schemas
export const CreateTagSchema = z.object({
  name: z.string().min(1).max(50),
  color: z.string().optional(),
  category: z.nativeEnum(TagCategory).optional(),
  organizationId: z.string(),
});

export const UpdateTagSchema = z.object({
  name: z.string().min(1).max(50).optional(),
  color: z.string().optional(),
  category: z.nativeEnum(TagCategory).optional(),
});

// Asset tag domain class with business logic
export class AssetTagEntity {
  constructor(private tag: AssetTag) {}

  // Getters
  get id(): string {
    return this.tag.id;
  }

  get name(): string {
    return this.tag.name;
  }

  get color(): string | null {
    return this.tag.color;
  }

  get category(): string | null {
    return this.tag.category;
  }

  get userId(): number {
    return this.tag.userId;
  }

  get organizationId(): string {
    return this.tag.organizationId;
  }

  get createdAt(): Date {
    return this.tag.createdAt;
  }

  get updatedAt(): Date {
    return this.tag.updatedAt;
  }

  // Business logic methods
  canBeEdited(userId: number): boolean {
    return this.tag.userId === userId;
  }

  canBeDeleted(userId: number): boolean {
    return this.tag.userId === userId;
  }

  canBeUsed(userId: number, organizationId: string): boolean {
    // Tags can be used by anyone in the same organization
    return this.tag.organizationId === organizationId;
  }

  getDisplayColor(): string {
    return this.tag.color || TAG_COLORS.GRAY;
  }

  getCategory(): TagCategory | null {
    return this.tag.category as TagCategory | null;
  }

  isSystemTag(): boolean {
    // System tags might have specific categories or naming conventions
    return (
      this.tag.category === TagCategory.BRAND ||
      this.tag.category === TagCategory.PRODUCT ||
      this.tag.name.startsWith('system:')
    );
  }

  isCustomTag(): boolean {
    return this.tag.category === TagCategory.CUSTOM || this.tag.category === null;
  }

  isProjectTag(): boolean {
    return this.tag.category === TagCategory.PROJECT;
  }

  // Update methods
  updateName(newName: string): void {
    const trimmedName = newName.trim();
    if (trimmedName.length === 0) {
      throw new Error('Tag name cannot be empty');
    }
    if (trimmedName.length > 50) {
      throw new Error('Tag name cannot exceed 50 characters');
    }
    this.tag.name = trimmedName;
    this.tag.updatedAt = new Date();
  }

  updateColor(newColor: string | null): void {
    if (newColor && !this.isValidColor(newColor)) {
      throw new Error('Invalid color format');
    }
    this.tag.color = newColor;
    this.tag.updatedAt = new Date();
  }

  updateCategory(newCategory: TagCategory | null): void {
    this.tag.category = newCategory;
    this.tag.updatedAt = new Date();
  }

  // Validation methods
  private isValidColor(color: string): boolean {
    // Check if it's a valid hex color
    const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    return hexColorRegex.test(color) || Object.values(TAG_COLORS).includes(color as any);
  }

  static validateName(name: string): boolean {
    const trimmedName = name.trim();
    return trimmedName.length > 0 && trimmedName.length <= 50;
  }

  static validateColor(color: string | null): boolean {
    if (color === null) return true;
    const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    return hexColorRegex.test(color) || Object.values(TAG_COLORS).includes(color as any);
  }

  // Static factory methods
  static fromPrismaTag(prismaTag: any): AssetTagEntity {
    return new AssetTagEntity(prismaTag as AssetTag);
  }

  static createNew(data: z.infer<typeof CreateTagSchema> & { userId: number }): Partial<AssetTag> {
    return {
      name: data.name.trim(),
      color: data.color || null,
      category: data.category || null,
      userId: data.userId,
      organizationId: data.organizationId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  // Utility methods
  static getDefaultColor(): string {
    return TAG_COLORS.GRAY;
  }

  static getAvailableColors(): typeof TAG_COLORS {
    return TAG_COLORS;
  }

  static getAvailableCategories(): TagCategory[] {
    return Object.values(TagCategory);
  }

  // Convert to plain object
  toPlainObject(): AssetTag {
    return this.tag;
  }

  // Get summary information
  getSummary(): {
    id: string;
    name: string;
    color: string;
    category: string | null;
    isSystemTag: boolean;
    createdAt: Date;
  } {
    return {
      id: this.id,
      name: this.name,
      color: this.getDisplayColor(),
      category: this.category,
      isSystemTag: this.isSystemTag(),
      createdAt: this.createdAt,
    };
  }
}
