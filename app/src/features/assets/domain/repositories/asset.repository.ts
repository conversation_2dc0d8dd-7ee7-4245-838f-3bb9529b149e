import {
  Asset,
  AssetCollection,
  AssetTag,
  SharedAsset,
  AssetBoard,
  BoardAsset,
  AssetComment,
  AssetCommentReply,
  AssetSearchFilters,
  AssetSearchOptions,
  AssetSearchResult,
  AssetUploadResult,
  AssetUsageAnalytics,
  OrganizationAssetAnalytics,
} from '../types';

// Asset repository interface defining all data access operations
export interface AssetRepository {
  // Asset CRUD operations
  findById(id: number): Promise<Asset | null>;
  findByIds(ids: number[]): Promise<Asset[]>;
  findByOrganization(
    organizationId: string,
    filters?: AssetSearchFilters,
    options?: AssetSearchOptions
  ): Promise<AssetSearchResult>;
  create(asset: Partial<Asset>): Promise<Asset>;
  update(id: number, updates: Partial<Asset>): Promise<Asset>;
  delete(id: number): Promise<void>;
  softDelete(id: number): Promise<void>;
  restore(id: number): Promise<Asset>;

  // Asset search and filtering
  search(filters: AssetSearchFilters, options?: AssetSearchOptions): Promise<AssetSearchResult>;
  findByTags(tagIds: string[], organizationId: string): Promise<Asset[]>;
  findByCollection(collectionId: string): Promise<Asset[]>;
  findSimilar(assetId: number, limit?: number): Promise<Asset[]>;
  findDuplicates(organizationId: string): Promise<Asset[][]>;

  // Asset variations and versions
  findVariations(originalAssetId: number): Promise<Asset[]>;
  findVersions(parentAssetId: number): Promise<Asset[]>;
  createVariation(originalAssetId: number, variationData: Partial<Asset>): Promise<Asset>;
  createVersion(parentAssetId: number, versionData: Partial<Asset>): Promise<Asset>;
  setCoverVersion(assetId: number): Promise<Asset>;

  // Asset analytics
  getAssetAnalytics(assetId: number): Promise<AssetUsageAnalytics>;
  getOrganizationAnalytics(organizationId: string): Promise<OrganizationAssetAnalytics>;
  recordView(assetId: number, userId?: number): Promise<void>;
  recordDownload(assetId: number, userId?: number): Promise<void>;
  recordShare(assetId: number, userId?: number): Promise<void>;

  // Bulk operations
  bulkUpdate(assetIds: number[], updates: Partial<Asset>): Promise<Asset[]>;
  bulkDelete(assetIds: number[]): Promise<void>;
  bulkAddTags(assetIds: number[], tagIds: string[]): Promise<void>;
  bulkRemoveTags(assetIds: number[], tagIds: string[]): Promise<void>;
  bulkAddToCollection(assetIds: number[], collectionId: string): Promise<void>;
  bulkRemoveFromCollection(assetIds: number[], collectionId: string): Promise<void>;
}

// Asset collection repository interface
export interface AssetCollectionRepository {
  findById(id: string): Promise<AssetCollection | null>;
  findByOrganization(organizationId: string, userId?: number): Promise<AssetCollection[]>;
  findByUser(userId: number): Promise<AssetCollection[]>;
  findByProject(projectId: string): Promise<AssetCollection[]>;
  create(collection: Partial<AssetCollection>): Promise<AssetCollection>;
  update(id: string, updates: Partial<AssetCollection>): Promise<AssetCollection>;
  delete(id: string): Promise<void>;

  // Collection-asset relationships
  addAssets(collectionId: string, assetIds: number[]): Promise<void>;
  removeAssets(collectionId: string, assetIds: number[]): Promise<void>;
  getAssets(collectionId: string): Promise<Asset[]>;
  getAssetCount(collectionId: string): Promise<number>;
}

// Asset tag repository interface
export interface AssetTagRepository {
  findById(id: string): Promise<AssetTag | null>;
  findByOrganization(organizationId: string): Promise<AssetTag[]>;
  findByUser(userId: number): Promise<AssetTag[]>;
  findByName(name: string, organizationId: string): Promise<AssetTag | null>;
  findByCategory(category: string, organizationId: string): Promise<AssetTag[]>;
  create(tag: Partial<AssetTag>): Promise<AssetTag>;
  update(id: string, updates: Partial<AssetTag>): Promise<AssetTag>;
  delete(id: string): Promise<void>;

  // Tag-asset relationships
  addToAssets(tagId: string, assetIds: number[]): Promise<void>;
  removeFromAssets(tagId: string, assetIds: number[]): Promise<void>;
  getAssets(tagId: string): Promise<Asset[]>;
  getAssetCount(tagId: string): Promise<number>;

  // Tag analytics
  getPopularTags(organizationId: string, limit?: number): Promise<{ tag: AssetTag; count: number }[]>;
  getUnusedTags(organizationId: string): Promise<AssetTag[]>;
}

// Shared asset repository interface
export interface SharedAssetRepository {
  findById(id: string): Promise<SharedAsset | null>;
  findByToken(token: string): Promise<SharedAsset | null>;
  findByAsset(assetId: number): Promise<SharedAsset[]>;
  findByUser(userId: number): Promise<SharedAsset[]>;
  findByEmail(email: string): Promise<SharedAsset[]>;
  create(sharedAsset: Partial<SharedAsset>): Promise<SharedAsset>;
  update(id: string, updates: Partial<SharedAsset>): Promise<SharedAsset>;
  delete(id: string): Promise<void>;

  // Sharing analytics
  recordAccess(shareId: string, accessType: 'view' | 'download'): Promise<void>;
  getShareAnalytics(shareId: string): Promise<any>;

  // Cleanup operations
  deleteExpired(): Promise<number>;
  deleteInactive(daysInactive: number): Promise<number>;
}

// Asset board repository interface
export interface AssetBoardRepository {
  findById(id: string): Promise<AssetBoard | null>;
  findByOrganization(organizationId: string): Promise<AssetBoard[]>;
  findByUser(userId: number): Promise<AssetBoard[]>;
  findByParent(parentBoardId: string): Promise<AssetBoard[]>;
  create(board: Partial<AssetBoard>): Promise<AssetBoard>;
  update(id: string, updates: Partial<AssetBoard>): Promise<AssetBoard>;
  delete(id: string): Promise<void>;

  // Board-asset relationships
  addAsset(boardId: string, assetId: number, status?: string): Promise<BoardAsset>;
  removeAsset(boardId: string, assetId: number): Promise<void>;
  updateAssetStatus(boardId: string, assetId: number, status: string): Promise<BoardAsset>;
  getAssets(boardId: string): Promise<BoardAsset[]>;
  getAssetCount(boardId: string): Promise<number>;
}

// Asset comment repository interface
export interface AssetCommentRepository {
  findById(id: string): Promise<AssetComment | null>;
  findByAsset(assetId: number): Promise<AssetComment[]>;
  findByUser(userId: number): Promise<AssetComment[]>;
  create(comment: Partial<AssetComment>): Promise<AssetComment>;
  update(id: string, updates: Partial<AssetComment>): Promise<AssetComment>;
  delete(id: string): Promise<void>;

  // Comment replies
  addReply(commentId: string, reply: Partial<AssetCommentReply>): Promise<AssetCommentReply>;
  updateReply(replyId: string, updates: Partial<AssetCommentReply>): Promise<AssetCommentReply>;
  deleteReply(replyId: string): Promise<void>;
  getReplies(commentId: string): Promise<AssetCommentReply[]>;
}

// Combined repository interface for dependency injection
export interface AssetRepositories {
  assets: AssetRepository;
  collections: AssetCollectionRepository;
  tags: AssetTagRepository;
  shares: SharedAssetRepository;
  boards: AssetBoardRepository;
  comments: AssetCommentRepository;
}
