// TODO: Update this service to work with WASP operations
// For now, commenting out to avoid compilation errors
/*
import {
  SharedAsset,
  SharedAssetEntity,
  SharingPermission,
  SharingSettings,
  AssetError,
  AssetNotFoundError,
  AssetPermissionError,
  CreateSharedAssetSchema,
  UpdateSharedAssetSchema,
} from '../types';
import { AssetRepositories } from '../repositories/asset.repository';

export interface ShareAssetRequest {
  assetId: number;
  sharedWithEmail?: string; // undefined for public links
  permissions?: SharingPermission[];
  settings?: Partial<SharingSettings>;
  expiresAt?: Date;
}

export interface ShareAssetResponse {
  shareId: string;
  token: string;
  shareUrl: string;
  isPublicLink: boolean;
  expiresAt: Date | null;
}

export class AssetSharingService {
  constructor(
    private repositories: AssetRepositories,
    private baseUrl: string = 'https://app.example.com'
  ) {}

  // Create a new share
  async shareAsset(
    request: ShareAssetRequest,
    userId: number
  ): Promise<ShareAssetResponse> {
    // Verify asset exists and user can share it
    const asset = await this.repositories.assets.findById(request.assetId);
    if (!asset) {
      throw new AssetNotFoundError(request.assetId);
    }

    // Check permissions (simplified - in real app would check organization membership, etc.)
    if (asset.userId !== userId) {
      throw new AssetPermissionError('share asset');
    }

    // Check if already shared with this email
    if (request.sharedWithEmail) {
      const existingShares = await this.repositories.shares.findByAsset(request.assetId);
      const existingShare = existingShares.find(
        share => share.sharedWithEmail === request.sharedWithEmail && share.isActive
      );
      if (existingShare) {
        throw new AssetError('Asset already shared with this email', 'ALREADY_SHARED', 400);
      }
    }

    // Generate unique token
    const token = SharedAssetEntity.generateToken();

    // Create shared asset data
    const sharedAssetData = SharedAssetEntity.createNew({
      assetId: request.assetId,
      sharedWithEmail: request.sharedWithEmail,
      permissions: request.permissions || SharedAssetEntity.getDefaultPermissions(),
      settings: { ...SharedAssetEntity.getDefaultSettings(), ...request.settings },
      expiresAt: request.expiresAt,
      sharedByUserId: userId,
      token,
    });

    // Create in repository
    const createdShare = await this.repositories.shares.create(sharedAssetData);
    const shareEntity = SharedAssetEntity.fromPrismaSharedAsset(createdShare);

    // Generate share URL
    const shareUrl = this.generateShareUrl(token);

    // Send notification email if sharing with specific email
    if (request.sharedWithEmail) {
      await this.sendShareNotification(request.sharedWithEmail, shareUrl, asset, userId);
    }

    return {
      shareId: shareEntity.id,
      token: shareEntity.token,
      shareUrl,
      isPublicLink: shareEntity.isPublicLink(),
      expiresAt: shareEntity.expiresAt,
    };
  }

  // Get shared asset by token
  async getSharedAsset(token: string): Promise<SharedAssetEntity> {
    const sharedAsset = await this.repositories.shares.findByToken(token);
    if (!sharedAsset) {
      throw new AssetError('Invalid or expired share link', 'INVALID_SHARE', 404);
    }

    const shareEntity = SharedAssetEntity.fromPrismaSharedAsset(sharedAsset);
    
    if (!shareEntity.isValid()) {
      throw new AssetError('Share link has expired or been deactivated', 'EXPIRED_SHARE', 410);
    }

    return shareEntity;
  }

  // Access shared asset (records view)
  async accessSharedAsset(
    token: string,
    password?: string
  ): Promise<{ asset: any; sharedAsset: SharedAssetEntity }> {
    const shareEntity = await this.getSharedAsset(token);

    // Check password if required
    if (shareEntity.requiresPassword()) {
      if (!password || shareEntity.settings.password !== password) {
        throw new AssetError('Invalid password', 'INVALID_PASSWORD', 401);
      }
    }

    // Get the actual asset
    const asset = await this.repositories.assets.findById(shareEntity.assetId);
    if (!asset) {
      throw new AssetNotFoundError(shareEntity.assetId);
    }

    // Record the view
    shareEntity.recordView();
    await this.repositories.shares.update(shareEntity.id, shareEntity.toPlainObject());

    return {
      asset,
      sharedAsset: shareEntity,
    };
  }

  // Download shared asset
  async downloadSharedAsset(token: string, password?: string): Promise<{ downloadUrl: string }> {
    const { asset, sharedAsset } = await this.accessSharedAsset(token, password);

    if (!sharedAsset.canDownload()) {
      throw new AssetPermissionError('download asset');
    }

    // Record the download
    sharedAsset.recordDownload();
    await this.repositories.shares.update(sharedAsset.id, sharedAsset.toPlainObject());

    // Return download URL (could be signed URL for security)
    return {
      downloadUrl: asset.fileUrl,
    };
  }

  // Update share settings
  async updateShare(
    shareId: string,
    updates: any,
    userId: number
  ): Promise<SharedAssetEntity> {
    const sharedAsset = await this.repositories.shares.findById(shareId);
    if (!sharedAsset) {
      throw new AssetError('Share not found', 'SHARE_NOT_FOUND', 404);
    }

    // Check permissions
    if (sharedAsset.sharedByUserId !== userId) {
      throw new AssetPermissionError('update share');
    }

    // Validate updates
    const validatedUpdates = UpdateSharedAssetSchema.parse(updates);

    // Update share
    const updatedShare = await this.repositories.shares.update(shareId, validatedUpdates);
    return SharedAssetEntity.fromPrismaSharedAsset(updatedShare);
  }

  // Revoke share
  async revokeShare(shareId: string, userId: number): Promise<void> {
    const sharedAsset = await this.repositories.shares.findById(shareId);
    if (!sharedAsset) {
      throw new AssetError('Share not found', 'SHARE_NOT_FOUND', 404);
    }

    // Check permissions
    if (sharedAsset.sharedByUserId !== userId) {
      throw new AssetPermissionError('revoke share');
    }

    // Deactivate the share
    const shareEntity = SharedAssetEntity.fromPrismaSharedAsset(sharedAsset);
    shareEntity.deactivate();
    
    await this.repositories.shares.update(shareId, shareEntity.toPlainObject());
  }

  // Get all shares for an asset
  async getAssetShares(assetId: number, userId: number): Promise<SharedAssetEntity[]> {
    // Verify user can view shares for this asset
    const asset = await this.repositories.assets.findById(assetId);
    if (!asset) {
      throw new AssetNotFoundError(assetId);
    }

    if (asset.userId !== userId) {
      throw new AssetPermissionError('view asset shares');
    }

    const shares = await this.repositories.shares.findByAsset(assetId);
    return shares.map(share => SharedAssetEntity.fromPrismaSharedAsset(share));
  }

  // Get all shares created by a user
  async getUserShares(userId: number): Promise<SharedAssetEntity[]> {
    const shares = await this.repositories.shares.findByUser(userId);
    return shares.map(share => SharedAssetEntity.fromPrismaSharedAsset(share));
  }

  // Cleanup expired shares
  async cleanupExpiredShares(): Promise<number> {
    return await this.repositories.shares.deleteExpired();
  }

  // Cleanup inactive shares
  async cleanupInactiveShares(daysInactive: number = 90): Promise<number> {
    return await this.repositories.shares.deleteInactive(daysInactive);
  }

  // Get sharing analytics
  async getShareAnalytics(shareId: string, userId: number): Promise<any> {
    const sharedAsset = await this.repositories.shares.findById(shareId);
    if (!sharedAsset) {
      throw new AssetError('Share not found', 'SHARE_NOT_FOUND', 404);
    }

    // Check permissions
    if (sharedAsset.sharedByUserId !== userId) {
      throw new AssetPermissionError('view share analytics');
    }

    return await this.repositories.shares.getShareAnalytics(shareId);
  }

  // Helper methods
  private generateShareUrl(token: string): string {
    return `${this.baseUrl}/shared/${token}`;
  }

  private async sendShareNotification(
    email: string,
    shareUrl: string,
    asset: any,
    sharedByUserId: number
  ): Promise<void> {
    // This would integrate with your email service
    // For now, we'll just log it
    console.log(`Sending share notification to ${email} for asset ${asset.fileName}`);
    console.log(`Share URL: ${shareUrl}`);
  }

  // Utility methods for creating different types of shares
  async createPublicLink(
    assetId: number,
    userId: number,
    options?: {
      allowDownload?: boolean;
      expiresAt?: Date;
      requirePassword?: boolean;
      password?: string;
    }
  ): Promise<ShareAssetResponse> {
    return await this.shareAsset({
      assetId,
      permissions: [SharingPermission.VIEW, ...(options?.allowDownload ? [SharingPermission.DOWNLOAD] : [])],
      settings: {
        allowDownload: options?.allowDownload ?? true,
        requirePassword: options?.requirePassword ?? false,
        password: options?.password,
        trackViews: true,
        allowComments: false,
        watermark: false,
      },
      expiresAt: options?.expiresAt,
    }, userId);
  }

  async shareWithEmail(
    assetId: number,
    email: string,
    userId: number,
    options?: {
      permissions?: SharingPermission[];
      allowComments?: boolean;
      expiresAt?: Date;
    }
  ): Promise<ShareAssetResponse> {
    return await this.shareAsset({
      assetId,
      sharedWithEmail: email,
      permissions: options?.permissions ?? [SharingPermission.VIEW, SharingPermission.COMMENT],
      settings: {
        allowDownload: true,
        allowComments: options?.allowComments ?? true,
        trackViews: true,
        requirePassword: false,
        watermark: false,
      },
      expiresAt: options?.expiresAt,
    }, userId);
  }
}
*/
