// TODO: Update this service to work with WASP-generated types
// For now, commenting out to avoid compilation errors
/*
import {
  Asset,
  AssetEntity,
  AssetSearchFilters,
  AssetSearchOptions,
  AssetSearchResult,
  AssetUploadResult,
  AssetError,
  AssetNotFoundError,
  AssetPermissionError,
  AssetValidationError,
  AssetUploadSchema,
  AssetUpdateSchema,
} from '../types';
*/
// import { AssetRepositories } from '../repositories/asset.repository';

// TODO: Reimplement this service to work with WASP operations
/*
export class AssetService {
  constructor(private repositories: AssetRepositories) {}

  // Asset retrieval methods
  async getAssetById(id: number, userId: number): Promise<AssetEntity> {
    const asset = await this.repositories.assets.findById(id);
    if (!asset) {
      throw new AssetNotFoundError(id);
    }

    const assetEntity = AssetEntity.fromPrismaAsset(asset);
    
    // Check if user can view this asset
    if (!this.canUserAccessAsset(assetEntity, userId)) {
      throw new AssetPermissionError('view asset');
    }

    return assetEntity;
  }

  async getAssetsByOrganization(
    organizationId: string,
    userId: number,
    filters?: AssetSearchFilters,
    options?: AssetSearchOptions
  ): Promise<AssetSearchResult> {
    const searchFilters: AssetSearchFilters = {
      ...filters,
      organizationId,
    };

    return await this.repositories.assets.findByOrganization(
      organizationId,
      searchFilters,
      options
    );
  }

  async searchAssets(
    filters: AssetSearchFilters,
    options?: AssetSearchOptions,
    userId?: number
  ): Promise<AssetSearchResult> {
    // Add user-specific filters if needed
    const searchFilters = { ...filters };
    
    return await this.repositories.assets.search(searchFilters, options);
  }

  // Asset creation and upload
  async createAsset(
    uploadData: any,
    userId: number
  ): Promise<AssetEntity> {
    // Validate upload data
    const validatedData = AssetUploadSchema.parse(uploadData);
    
    // Create asset entity data
    const assetData = AssetEntity.createNew({
      ...validatedData,
      userId,
    });

    // Create asset in repository
    const createdAsset = await this.repositories.assets.create(assetData);
    
    // Handle tags if provided
    if (validatedData.tags && validatedData.tags.length > 0) {
      await this.addTagsToAsset(createdAsset.id, validatedData.tags, userId);
    }

    // Handle collections if provided
    if (validatedData.collectionIds && validatedData.collectionIds.length > 0) {
      await this.addAssetToCollections(createdAsset.id, validatedData.collectionIds, userId);
    }

    // Record analytics
    await this.repositories.assets.recordView(createdAsset.id, userId);

    return AssetEntity.fromPrismaAsset(createdAsset);
  }

  // Asset updates
  async updateAsset(
    id: number,
    updateData: any,
    userId: number
  ): Promise<AssetEntity> {
    const asset = await this.getAssetById(id, userId);
    
    if (!asset.canBeEdited(userId)) {
      throw new AssetPermissionError('edit asset');
    }

    // Validate update data
    const validatedData = AssetUpdateSchema.parse(updateData);

    // Extract tags and collections for separate handling
    const { tags, collectionIds, ...assetUpdateData } = validatedData;

    // Update asset (without tags and collections)
    const updatedAsset = await this.repositories.assets.update(id, assetUpdateData);
    
    // Handle tag updates
    if (tags) {
      await this.updateAssetTags(id, tags, userId);
    }

    // Handle collection updates
    if (collectionIds) {
      await this.updateAssetCollections(id, collectionIds, userId);
    }

    return AssetEntity.fromPrismaAsset(updatedAsset);
  }

  // Asset deletion
  async deleteAsset(id: number, userId: number): Promise<void> {
    const asset = await this.getAssetById(id, userId);
    
    if (!asset.canBeDeleted(userId)) {
      throw new AssetPermissionError('delete asset');
    }

    await this.repositories.assets.softDelete(id);
  }

  async permanentlyDeleteAsset(id: number, userId: number): Promise<void> {
    const asset = await this.getAssetById(id, userId);
    
    if (!asset.canBeDeleted(userId)) {
      throw new AssetPermissionError('permanently delete asset');
    }

    await this.repositories.assets.delete(id);
  }

  async restoreAsset(id: number, userId: number): Promise<AssetEntity> {
    const asset = await this.repositories.assets.findById(id);
    if (!asset) {
      throw new AssetNotFoundError(id);
    }

    const assetEntity = AssetEntity.fromPrismaAsset(asset);
    if (!assetEntity.canBeEdited(userId)) {
      throw new AssetPermissionError('restore asset');
    }

    const restoredAsset = await this.repositories.assets.restore(id);
    return AssetEntity.fromPrismaAsset(restoredAsset);
  }

  // Asset variations and versions
  async createVariation(
    originalAssetId: number,
    variationData: any,
    userId: number
  ): Promise<AssetEntity> {
    const originalAsset = await this.getAssetById(originalAssetId, userId);
    
    if (!originalAsset.canBeEdited(userId)) {
      throw new AssetPermissionError('create variation');
    }

    const variation = await this.repositories.assets.createVariation(
      originalAssetId,
      { ...variationData, userId }
    );

    return AssetEntity.fromPrismaAsset(variation);
  }

  async createVersion(
    parentAssetId: number,
    versionData: any,
    userId: number
  ): Promise<AssetEntity> {
    const parentAsset = await this.getAssetById(parentAssetId, userId);
    
    if (!parentAsset.canBeEdited(userId)) {
      throw new AssetPermissionError('create version');
    }

    const version = await this.repositories.assets.createVersion(
      parentAssetId,
      { ...versionData, userId }
    );

    return AssetEntity.fromPrismaAsset(version);
  }

  async setCoverVersion(assetId: number, userId: number): Promise<AssetEntity> {
    const asset = await this.getAssetById(assetId, userId);
    
    if (!asset.canBeEdited(userId)) {
      throw new AssetPermissionError('set cover version');
    }

    const updatedAsset = await this.repositories.assets.setCoverVersion(assetId);
    return AssetEntity.fromPrismaAsset(updatedAsset);
  }

  // Tag management
  async addTagsToAsset(
    assetId: number,
    tagNames: string[],
    userId: number
  ): Promise<void> {
    const asset = await this.getAssetById(assetId, userId);
    
    if (!asset.canBeEdited(userId)) {
      throw new AssetPermissionError('add tags to asset');
    }

    // Create tags if they don't exist
    const tagIds: string[] = [];
    for (const tagName of tagNames) {
      let tag = await this.repositories.tags.findByName(tagName, asset.toPlainObject().organizationId);
      if (!tag) {
        tag = await this.repositories.tags.create({
          name: tagName,
          userId,
          organizationId: asset.toPlainObject().organizationId,
        });
      }
      tagIds.push(tag.id);
    }

    await this.repositories.tags.addToAssets(tagIds[0], [assetId]);
  }

  async removeTagsFromAsset(
    assetId: number,
    tagIds: string[],
    userId: number
  ): Promise<void> {
    const asset = await this.getAssetById(assetId, userId);
    
    if (!asset.canBeEdited(userId)) {
      throw new AssetPermissionError('remove tags from asset');
    }

    for (const tagId of tagIds) {
      await this.repositories.tags.removeFromAssets(tagId, [assetId]);
    }
  }

  private async updateAssetTags(
    assetId: number,
    tagNames: string[],
    userId: number
  ): Promise<void> {
    // This would involve comparing current tags with new tags
    // and adding/removing as needed
    // Implementation would depend on specific requirements
  }

  // Collection management
  async addAssetToCollections(
    assetId: number,
    collectionIds: string[],
    userId: number
  ): Promise<void> {
    const asset = await this.getAssetById(assetId, userId);
    
    if (!asset.canBeEdited(userId)) {
      throw new AssetPermissionError('add asset to collections');
    }

    for (const collectionId of collectionIds) {
      await this.repositories.collections.addAssets(collectionId, [assetId]);
    }
  }

  async removeAssetFromCollections(
    assetId: number,
    collectionIds: string[],
    userId: number
  ): Promise<void> {
    const asset = await this.getAssetById(assetId, userId);
    
    if (!asset.canBeEdited(userId)) {
      throw new AssetPermissionError('remove asset from collections');
    }

    for (const collectionId of collectionIds) {
      await this.repositories.collections.removeAssets(collectionId, [assetId]);
    }
  }

  private async updateAssetCollections(
    assetId: number,
    collectionIds: string[],
    userId: number
  ): Promise<void> {
    // This would involve comparing current collections with new collections
    // and adding/removing as needed
    // Implementation would depend on specific requirements
  }

  // Analytics
  async recordAssetView(assetId: number, userId?: number): Promise<void> {
    await this.repositories.assets.recordView(assetId, userId);
  }

  async recordAssetDownload(assetId: number, userId?: number): Promise<void> {
    await this.repositories.assets.recordDownload(assetId, userId);
  }

  async recordAssetShare(assetId: number, userId?: number): Promise<void> {
    await this.repositories.assets.recordShare(assetId, userId);
  }

  // Helper methods
  private canUserAccessAsset(asset: AssetEntity, userId: number): boolean {
    // For now, users can access assets in their organization
    // This could be expanded with more complex permission logic
    return true;
  }

  // Bulk operations
  async bulkDeleteAssets(assetIds: number[], userId: number): Promise<void> {
    // Verify user can delete all assets
    for (const assetId of assetIds) {
      const asset = await this.getAssetById(assetId, userId);
      if (!asset.canBeDeleted(userId)) {
        throw new AssetPermissionError(`delete asset ${assetId}`);
      }
    }

    await this.repositories.assets.bulkDelete(assetIds);
  }

  async bulkAddTags(
    assetIds: number[],
    tagNames: string[],
    userId: number,
    organizationId: string
  ): Promise<void> {
    // Verify user can edit all assets
    for (const assetId of assetIds) {
      const asset = await this.getAssetById(assetId, userId);
      if (!asset.canBeEdited(userId)) {
        throw new AssetPermissionError(`edit asset ${assetId}`);
      }
    }

    // Create tags if they don't exist and get tag IDs
    const tagIds: string[] = [];
    for (const tagName of tagNames) {
      let tag = await this.repositories.tags.findByName(tagName, organizationId);
      if (!tag) {
        tag = await this.repositories.tags.create({
          name: tagName,
          userId,
          organizationId,
        });
      }
      tagIds.push(tag.id);
    }

    await this.repositories.assets.bulkAddTags(assetIds, tagIds);
  }

  async bulkRemoveTags(
    assetIds: number[],
    tagIds: string[],
    userId: number
  ): Promise<void> {
    // Verify user can edit all assets
    for (const assetId of assetIds) {
      const asset = await this.getAssetById(assetId, userId);
      if (!asset.canBeEdited(userId)) {
        throw new AssetPermissionError(`edit asset ${assetId}`);
      }
    }

    await this.repositories.assets.bulkRemoveTags(assetIds, tagIds);
  }
}
*/
