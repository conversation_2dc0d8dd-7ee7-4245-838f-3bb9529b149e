import { z } from 'zod';
import type { Board, BoardAsset } from 'wasp/entities';
import {
  BoardEntity,
  CreateBoardSchema,
  UpdateBoardSchema,
  AddAssetToBoardSchema,
  UpdateBoardAssetStatusSchema,
  BoardViewType,
  BoardAssetStatus,
} from '../entities/board.entity';

// Board repository interface (to be implemented by infrastructure layer)
export interface BoardRepository {
  findById(id: string): Promise<Board | null>;
  findByOrganization(organizationId: string): Promise<Board[]>;
  findByParent(parentId: string | null): Promise<Board[]>;
  findRootBoards(organizationId: string): Promise<Board[]>;
  create(boardData: Partial<Board>): Promise<Board>;
  update(id: string, boardData: Partial<Board>): Promise<Board>;
  delete(id: string): Promise<void>;
  addAsset(boardAssetData: Partial<BoardAsset>): Promise<BoardAsset>;
  removeAsset(boardAssetId: string): Promise<void>;
  updateAssetStatus(boardAssetId: string, status: BoardAssetStatus): Promise<BoardAsset>;
  findBoardAssets(boardId: string): Promise<BoardAsset[]>;
}

// Board service for business logic
export class BoardService {
  constructor(private boardRepository: BoardRepository) {}

  // Board CRUD operations
  async createBoard(data: z.infer<typeof CreateBoardSchema>): Promise<BoardEntity> {
    // Validate input
    const validatedData = CreateBoardSchema.parse(data);

    // Check if parent board exists (if specified)
    if (validatedData.parentBoardId) {
      const parentBoard = await this.boardRepository.findById(validatedData.parentBoardId);
      if (!parentBoard) {
        throw new Error('Parent board not found');
      }
      if (parentBoard.deletedAt) {
        throw new Error('Cannot create child board under deleted parent');
      }
    }

    // Create board data
    const boardData = BoardEntity.createNew(validatedData);

    // Save to repository
    const createdBoard = await this.boardRepository.create(boardData);

    return BoardEntity.fromPrismaBoard(createdBoard);
  }

  async updateBoard(id: string, data: z.infer<typeof UpdateBoardSchema>): Promise<BoardEntity> {
    // Validate input
    const validatedData = UpdateBoardSchema.parse(data);

    // Check if board exists
    const existingBoard = await this.boardRepository.findById(id);
    if (!existingBoard) {
      throw new Error('Board not found');
    }

    const boardEntity = BoardEntity.fromPrismaBoard(existingBoard);
    if (boardEntity.isDeleted()) {
      throw new Error('Cannot update deleted board');
    }

    // Validate parent board change (if specified)
    if (validatedData.parentBoardId !== undefined) {
      if (validatedData.parentBoardId === id) {
        throw new Error('Board cannot be its own parent');
      }

      if (validatedData.parentBoardId) {
        const parentBoard = await this.boardRepository.findById(validatedData.parentBoardId);
        if (!parentBoard) {
          throw new Error('Parent board not found');
        }
        if (parentBoard.deletedAt) {
          throw new Error('Cannot move board under deleted parent');
        }
      }
    }

    // Update board
    const updatedBoard = await this.boardRepository.update(id, {
      ...validatedData,
      updatedAt: new Date(),
    });

    return BoardEntity.fromPrismaBoard(updatedBoard);
  }

  async deleteBoard(id: string): Promise<void> {
    // Check if board exists
    const existingBoard = await this.boardRepository.findById(id);
    if (!existingBoard) {
      throw new Error('Board not found');
    }

    const boardEntity = BoardEntity.fromPrismaBoard(existingBoard);

    // Check if board can be deleted
    if (!boardEntity.canBeDeleted()) {
      throw new Error('Cannot delete board with child boards or assets');
    }

    // Soft delete the board
    await this.boardRepository.update(id, {
      deletedAt: new Date(),
      updatedAt: new Date(),
    });
  }

  async restoreBoard(id: string): Promise<BoardEntity> {
    // Check if board exists
    const existingBoard = await this.boardRepository.findById(id);
    if (!existingBoard) {
      throw new Error('Board not found');
    }

    const boardEntity = BoardEntity.fromPrismaBoard(existingBoard);
    if (!boardEntity.isDeleted()) {
      throw new Error('Board is not deleted');
    }

    // Restore the board
    const restoredBoard = await this.boardRepository.update(id, {
      deletedAt: null,
      updatedAt: new Date(),
    });

    return BoardEntity.fromPrismaBoard(restoredBoard);
  }

  // Board query operations
  async getBoardById(id: string): Promise<BoardEntity | null> {
    const board = await this.boardRepository.findById(id);
    return board ? BoardEntity.fromPrismaBoard(board) : null;
  }

  async getBoardsByOrganization(organizationId: string): Promise<BoardEntity[]> {
    const boards = await this.boardRepository.findByOrganization(organizationId);
    return boards.map((board) => BoardEntity.fromPrismaBoard(board));
  }

  async getRootBoards(organizationId: string): Promise<BoardEntity[]> {
    const boards = await this.boardRepository.findRootBoards(organizationId);
    return boards.map((board) => BoardEntity.fromPrismaBoard(board));
  }

  async getChildBoards(parentId: string): Promise<BoardEntity[]> {
    const boards = await this.boardRepository.findByParent(parentId);
    return boards.map((board) => BoardEntity.fromPrismaBoard(board));
  }

  // Asset-board operations
  async addAssetToBoard(data: z.infer<typeof AddAssetToBoardSchema>): Promise<void> {
    // Validate input
    const validatedData = AddAssetToBoardSchema.parse(data);

    // Check if board exists and is not deleted
    const board = await this.boardRepository.findById(validatedData.boardId);
    if (!board) {
      throw new Error('Board not found');
    }

    const boardEntity = BoardEntity.fromPrismaBoard(board);
    if (boardEntity.isDeleted()) {
      throw new Error('Cannot add assets to deleted board');
    }

    // Add asset to board
    await this.boardRepository.addAsset({
      assetId: validatedData.assetId,
      boardId: validatedData.boardId,
      status: validatedData.status,
      createdAt: new Date(),
    });
  }

  async removeAssetFromBoard(boardAssetId: string): Promise<void> {
    await this.boardRepository.removeAsset(boardAssetId);
  }

  async updateBoardAssetStatus(data: z.infer<typeof UpdateBoardAssetStatusSchema>): Promise<void> {
    // Validate input
    const validatedData = UpdateBoardAssetStatusSchema.parse(data);

    // Update asset status
    await this.boardRepository.updateAssetStatus(validatedData.boardAssetId, validatedData.status);
  }

  async getBoardAssets(boardId: string): Promise<BoardAsset[]> {
    return await this.boardRepository.findBoardAssets(boardId);
  }

  // Utility methods
  async getBoardHierarchy(organizationId: string): Promise<BoardEntity[]> {
    // Get all boards for the organization
    const allBoards = await this.getBoardsByOrganization(organizationId);

    // Build hierarchy tree
    const boardMap = new Map<string, BoardEntity>();
    allBoards.forEach((board) => boardMap.set(board.id, board));

    // Return root boards with their children populated
    return allBoards.filter((board) => board.isRootBoard());
  }

  async moveBoardToParent(boardId: string, newParentId: string | null): Promise<BoardEntity> {
    // Validate that the move doesn't create circular references
    if (newParentId) {
      const wouldCreateCycle = await this.wouldCreateCycle(boardId, newParentId);
      if (wouldCreateCycle) {
        throw new Error('Moving board would create circular reference');
      }
    }

    return await this.updateBoard(boardId, { parentBoardId: newParentId ?? undefined });
  }

  private async wouldCreateCycle(boardId: string, potentialParentId: string): Promise<boolean> {
    let currentParentId: string | null | undefined = potentialParentId;

    while (currentParentId) {
      if (currentParentId === boardId) {
        return true; // Cycle detected
      }

      const parentBoard = await this.boardRepository.findById(currentParentId);
      currentParentId = parentBoard?.parentBoardId ?? null;
    }

    return false;
  }
}
