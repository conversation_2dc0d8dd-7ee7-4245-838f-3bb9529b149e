// Re-export WASP-generated types
export type { Asset, AssetCollection, AssetTag, Board, BoardAsset } from 'wasp/entities';

// Re-export our domain enums and schemas
export {
  AssetStatus,
  AssetType,
  AssetCategory,
  AssetSource,
  AssetUploadSchema,
  AssetUpdateSchema,
  CloudAssetSchema,
} from '../entities/asset.entity';

// Re-export our domain enums and schemas (non-conflicting)
export { CreateCollectionSchema, UpdateCollectionSchema } from '../entities/asset-collection.entity';

export { TagCategory, TAG_COLORS, CreateTagSchema, UpdateTagSchema } from '../entities/asset-tag.entity';

// Re-export board domain types and schemas
export {
  BoardViewType,
  BoardAssetStatus,
  CreateBoardSchema,
  UpdateBoardSchema,
  AddAssetToBoardSchema,
  UpdateBoardAssetStatusSchema,
} from '../entities/board.entity';

// Note: Custom AssetCollection and AssetTag interfaces are not exported
// because they conflict with the WASP-generated types from schema.prisma

// Re-export WASP-generated SharedAsset type
export type { SharedAsset } from 'wasp/entities';

// Re-export only the enums and schemas that don't conflict with WASP types
export { SharingPermission } from '../entities/shared-asset.entity';

// Note: SharingSettings, CreateSharedAssetSchema, UpdateSharedAssetSchema
// are not exported because they conflict with the WASP SharedAsset schema
// The WASP SharedAsset uses Json for settings instead of typed objects

// Import WASP types for use in this file
import type { Asset, AssetTag, SharedAsset, Board, BoardAsset } from 'wasp/entities';

// Additional domain-specific types

// Cloud asset types
export interface CloudAssetFile {
  id: string;
  name: string;
  mimeType: string;
  size: number;
  lastModified: Date;
  thumbnailUrl?: string;
  webViewUrl?: string;
  downloadUrl?: string;
  path?: string;
  source: string; // Use string instead of AssetSource enum
  isFolder?: boolean; // Added to support folder navigation
  folderPath?: string; // For tracking the current folder path
}

export interface CloudAssetConnection {
  provider: string; // Use string instead of AssetSource enum
  isConnected: boolean;
  lastSync?: Date;
  totalFiles?: number;
  error?: string;
}

export interface CloudAssetSyncResult {
  provider: string; // Use string instead of AssetSource enum
  syncedFiles: number;
  errors: string[];
  lastSync: Date;
}

// Asset search and filtering types
export interface AssetSearchFilters {
  query?: string;
  tags?: string[];
  collections?: string[];
  categories?: string[];
  fileTypes?: string[];
  sources?: string[]; // Filter by asset source
  dateRange?: {
    from: Date;
    to: Date;
  };
  sizeRange?: {
    min: number;
    max: number;
  };
  isGenerated?: boolean;
  isVariation?: boolean;
  userId?: number;
  organizationId: string;
}

export interface AssetSearchOptions {
  page?: number;
  limit?: number;
  sortBy?: 'uploadedAt' | 'fileName' | 'fileSize' | 'views' | 'downloads';
  sortOrder?: 'asc' | 'desc';
  includeDeleted?: boolean;
}

export interface AssetSearchResult {
  assets: Asset[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Asset upload types
export interface AssetUploadProgress {
  assetId?: number;
  fileName: string;
  progress: number; // 0-100
  status: 'uploading' | 'processing' | 'completed' | 'failed';
  error?: string;
}

export interface AssetUploadResult {
  success: boolean;
  asset?: Asset;
  error?: string;
}

// Asset board types
export interface AssetBoard {
  id: string;
  name: string;
  description: string | null;
  organizationId: string;
  userId: number;
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
  parentBoardId: string | null;

  // Related entities
  assets?: BoardAsset[];
  childBoards?: AssetBoard[];
  parentBoard?: AssetBoard | null;
}

// Note: Using WASP-generated BoardAsset type instead of custom interface
// to avoid conflicts with the generated types

// Asset comment types
export interface AssetComment {
  id: string;
  assetId: number;
  authorId: number;
  content: string;
  createdAt: Date;
  updatedAt: Date;

  // Related entities
  asset?: Asset;
  author?: any; // User type
  replies?: AssetCommentReply[];
}

export interface AssetCommentReply {
  id: string;
  commentId: string;
  authorId: number;
  content: string;
  createdAt: Date;
  updatedAt: Date;

  // Related entities
  comment?: AssetComment;
  author?: any; // User type
}

// Asset analytics types
export interface AssetUsageAnalytics {
  totalViews: number;
  totalDownloads: number;
  totalShares: number;
  uniqueViewers: number;
  averageViewDuration: number;
  topReferrers: string[];
  viewsByDate: { date: string; views: number }[];
  downloadsByDate: { date: string; downloads: number }[];
}

export interface OrganizationAssetAnalytics {
  totalAssets: number;
  totalStorage: number; // in bytes
  assetsByType: { type: string; count: number }[];
  assetsByCategory: { category: string; count: number }[];
  uploadsByDate: { date: string; uploads: number }[];
  topTags: { tag: string; count: number }[];
  topCollections: { collection: string; count: number }[];
  storageByUser: { userId: number; storage: number }[];
}

// Asset transformation types
export interface AssetTransformation {
  id: string;
  assetId: number;
  type: 'resize' | 'crop' | 'rotate' | 'filter' | 'compress';
  parameters: Record<string, any>;
  resultUrl: string;
  createdAt: Date;
}

// Asset version types
export interface AssetVersion {
  id: number;
  originalAssetId: number;
  versionNumber: number;
  fileName: string;
  fileUrl: string;
  changes: string; // Description of changes
  createdAt: Date;
  createdBy: number;
  isCurrent: boolean;
}

// Asset import types
export interface AssetImportJob {
  id: string;
  organizationId: string;
  userId: number;
  source: 'url' | 'bulk_upload' | 'external_service';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  totalAssets: number;
  processedAssets: number;
  failedAssets: number;
  errors: string[];
  createdAt: Date;
  updatedAt: Date;
  completedAt: Date | null;
}

// Asset sharing analytics
export interface SharingAnalytics {
  shareId: string;
  totalViews: number;
  uniqueViewers: number;
  totalDownloads: number;
  viewsByDate: { date: string; views: number }[];
  downloadsByDate: { date: string; downloads: number }[];
  referrers: { referrer: string; count: number }[];
  locations: { country: string; count: number }[];
}

// Error types
export class AssetError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 400
  ) {
    super(message);
    this.name = 'AssetError';
  }
}

export class AssetNotFoundError extends AssetError {
  constructor(assetId: number | string) {
    super(`Asset with ID ${assetId} not found`, 'ASSET_NOT_FOUND', 404);
  }
}

export class AssetPermissionError extends AssetError {
  constructor(action: string) {
    super(`Permission denied for action: ${action}`, 'PERMISSION_DENIED', 403);
  }
}

export class AssetUploadError extends AssetError {
  constructor(message: string) {
    super(`Upload failed: ${message}`, 'UPLOAD_FAILED', 400);
  }
}

export class AssetValidationError extends AssetError {
  constructor(field: string, message: string) {
    super(`Validation failed for ${field}: ${message}`, 'VALIDATION_FAILED', 400);
  }
}

// Event types for domain events
export interface AssetDomainEvent {
  type: string;
  assetId: number;
  userId: number;
  organizationId: string;
  timestamp: Date;
  data: Record<string, any>;
}

export interface AssetCreatedEvent extends AssetDomainEvent {
  type: 'asset.created';
  data: {
    asset: Asset;
  };
}

export interface AssetUpdatedEvent extends AssetDomainEvent {
  type: 'asset.updated';
  data: {
    asset: Asset;
    changes: Record<string, any>;
  };
}

export interface AssetDeletedEvent extends AssetDomainEvent {
  type: 'asset.deleted';
  data: {
    assetId: number;
  };
}

export interface AssetSharedEvent extends AssetDomainEvent {
  type: 'asset.shared';
  data: {
    sharedAsset: SharedAsset;
  };
}

export interface AssetTaggedEvent extends AssetDomainEvent {
  type: 'asset.tagged';
  data: {
    assetId: number;
    tags: AssetTag[];
  };
}

export interface AssetAddedToCollectionEvent extends AssetDomainEvent {
  type: 'asset.added_to_collection';
  data: {
    assetId: number;
    collectionId: string;
  };
}

// Union type for all domain events
export type AssetDomainEvents =
  | AssetCreatedEvent
  | AssetUpdatedEvent
  | AssetDeletedEvent
  | AssetSharedEvent
  | AssetTaggedEvent
  | AssetAddedToCollectionEvent;
