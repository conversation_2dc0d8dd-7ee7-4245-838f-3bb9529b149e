# 📁 Assets Feature

## 🎯 Overview

The Assets feature manages digital asset storage, organization, and collaboration for organizations. It provides comprehensive asset management including uploads, tagging, collections, sharing, and collaborative workflows with boards and comments.

## 📁 Directory Structure

```
features/assets/
├── 🧠 domain/                          # Business logic layer
│   ├── entities/                       # Domain entities
│   │   ├── asset.entity.ts             # Core asset domain model
│   │   ├── asset-collection.entity.ts  # Collection management
│   │   ├── asset-tag.entity.ts         # Tagging system
│   │   ├── shared-asset.entity.ts      # Asset sharing
│   │   └── asset-board.entity.ts       # Board collaboration
│   ├── services/                       # Business services
│   │   ├── asset.service.ts            # Core asset operations
│   │   ├── asset-upload.service.ts     # Upload processing
│   │   ├── asset-sharing.service.ts    # Sharing workflows
│   │   ├── asset-board.service.ts      # Board management
│   │   └── asset-search.service.ts     # Search and filtering
│   ├── repositories/                   # Data access interfaces
│   │   └── asset.repository.ts         # Asset data operations
│   └── types/                          # Domain-specific types
│       └── index.ts                    # Type definitions
├── 🔧 infrastructure/                  # External integrations
│   ├── wasp/                           # WASP operations
│   │   ├── actions/                    # WASP actions
│   │   │   ├── upload-asset.action.ts
│   │   │   ├── delete-asset.action.ts
│   │   │   ├── share-asset.action.ts
│   │   │   ├── add-asset-tags.action.ts
│   │   │   ├── create-collection.action.ts
│   │   │   └── board-operations.action.ts
│   │   └── queries/                    # WASP queries
│   │       ├── get-assets.query.ts
│   │       ├── get-asset.query.ts
│   │       ├── get-boards.query.ts
│   │       └── get-shared-asset.query.ts
│   └── database/                       # Database implementations
│       └── asset.repository.impl.ts
├── 🎨 presentation/                    # UI layer
│   ├── pages/                          # Page components
│   │   ├── asset-library.page.tsx
│   │   ├── asset-editor.page.tsx
│   │   ├── asset-preview.page.tsx
│   │   ├── asset-board.page.tsx
│   │   └── shared-asset.page.tsx
│   ├── components/                     # React components
│   │   ├── asset-card/                 # Asset card component
│   │   ├── asset-grid/                 # Grid layout
│   │   ├── asset-sidebar/              # Navigation sidebar
│   │   ├── asset-viewer/               # Asset viewer modal
│   │   ├── tag-editor/                 # Tag management
│   │   ├── collection-picker/          # Collection management
│   │   ├── board-components/           # Board collaboration
│   │   └── upload-components/          # File upload
│   ├── hooks/                          # React hooks
│   │   ├── use-asset-library.hook.ts   # Asset operations
│   │   ├── use-asset-upload.hook.ts    # Upload workflow
│   │   └── use-asset-sharing.hook.ts   # Sharing workflow
│   └── utils/                          # UI utilities
│       └── asset-transforms.ts         # Data transformations
├── 📚 docs/                            # Feature documentation
│   ├── ARCHITECTURE.md                 # Technical architecture
│   ├── API.md                          # API documentation
│   └── MIGRATION.md                    # Migration guide
└── index.ts                            # Feature exports
```

## 🎯 Core Capabilities

### 1. **Asset Management**
- Upload, organize, and manage digital assets
- Support for images, documents, and generated content
- Version control and asset variations
- Metadata extraction and analysis

### 2. **Organization & Discovery**
- Tag-based categorization system
- Collection-based organization
- Advanced search and filtering
- Smart recommendations

### 3. **Collaboration & Sharing**
- Asset boards for project collaboration
- Commenting and feedback system
- Secure sharing with external users
- Real-time collaboration features

### 4. **Integration Features**
- Generated asset management
- Brand kit integration
- Canvas integration for design workflows
- Export and download capabilities

## 🔄 Business Workflows

### Asset Upload Workflow
1. User uploads files via drag-and-drop or file picker
2. System processes and optimizes assets
3. Metadata is extracted and analyzed
4. Assets are categorized and made searchable
5. Notifications sent to relevant team members

### Collaboration Workflow
1. Assets are organized into boards
2. Team members can comment and provide feedback
3. Status tracking through board columns
4. Real-time updates and notifications
5. Final approval and publishing

### Sharing Workflow
1. User selects assets to share
2. Sharing permissions and expiration set
3. Secure links generated for external access
4. Recipients receive notifications
5. Access tracking and analytics

## 🔗 Dependencies

### Internal
- `@organization`: Organization context and state
- `@client/components`: Shared UI components
- `@client/hooks`: Common React hooks

### External
- Prisma: Database operations
- WASP: Framework operations
- React Hook Form: Form management
- Zustand: State management

## 📊 Performance Considerations

### Optimization Strategies
- Lazy loading of asset grids
- Image optimization and compression
- Caching of frequently accessed assets
- Pagination for large asset collections

### Monitoring
- Upload success rates
- Asset access patterns
- Collaboration engagement
- Storage usage metrics

## 🧪 Testing Strategy

### Unit Tests
- Domain entity validation
- Service logic testing
- Repository operations
- Component rendering

### Integration Tests
- WASP action/query testing
- Database operations
- File upload workflows
- Sharing functionality

### E2E Tests
- Complete asset workflows
- Collaboration features
- Cross-feature integration

## 🚀 Migration Status

This feature was migrated from the legacy asset structure to follow the modular architecture pattern. All functionality has been preserved while improving maintainability and testability.

## 📦 Key Exports

```typescript
// Domain
export { Asset, AssetStatus, AssetType } from './domain/entities/asset.entity';
export { AssetService } from './domain/services/asset.service';

// Infrastructure
export { uploadAsset, deleteAsset, shareAsset } from './infrastructure/wasp/actions';
export { getAssets, getAsset, getBoards } from './infrastructure/wasp/queries';

// Presentation
export { AssetLibraryPage, AssetEditorPage } from './presentation/pages';
export { AssetCard, AssetGrid, AssetViewer } from './presentation/components';
```
