// Domain exports (types only for client-side use)
export * from './domain/types';

// Presentation exports (client-side only)
export * from './presentation/pages';
export * from './presentation/components';
export * from './presentation/hooks';
// export * from './presentation/utils'; // Will be added when utils are created

// Note: Domain services and infrastructure are server-side only
// They should not be imported on the client side
// Use WASP operations (actions/queries) instead

// Feature configuration
export const ASSETS_FEATURE_CONFIG = {
  name: 'assets',
  version: '1.0.0',
  description: 'Asset management and collaboration feature',
  dependencies: ['organization', 'authentication'],
  capabilities: [
    'asset-upload',
    'asset-organization',
    'asset-sharing',
    'asset-collaboration',
    'asset-search',
    'asset-analytics',
  ],
} as const;
