import type {
  CreateBoard,
  UpdateBoard,
  DeleteBoard,
  AddAssetToBoard,
  UpdateBoardAssetStatus,
  GetBoards,
  GetBoard,
  GetBoardAssets,
} from 'wasp/server/operations';
import type { Board, BoardAsset, OrganizationMember } from 'wasp/entities';
import { HttpError } from 'wasp/server';

// Create Board Action
export const createBoard: CreateBoard<
  { name: string; description?: string; organizationId: string; parentBoardId?: string },
  Board
> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const { name, description = '', organizationId, parentBoardId } = args;

  // Verify user is member of organization
  const orgMember = await context.entities.OrganizationMember.findFirst({
    where: {
      userId: context.user.id,
      organizationId: organizationId,
    },
  });

  if (!orgMember) {
    throw new HttpError(403, 'User is not a member of this organization');
  }

  // Validate parent board if specified
  if (parentBoardId) {
    const parentBoard = await context.entities.Board.findUnique({
      where: { id: parentBoardId },
    });

    if (!parentBoard) {
      throw new HttpError(404, 'Parent board not found');
    }

    if (parentBoard.organizationId !== organizationId) {
      throw new HttpError(403, 'Parent board belongs to different organization');
    }

    if (parentBoard.deletedAt) {
      throw new HttpError(400, 'Cannot create child board under deleted parent');
    }
  }

  // Create the board
  const board = await context.entities.Board.create({
    data: {
      name: name.trim(),
      description,
      organizationId,
      parentBoardId: parentBoardId || null,
      listView: 'gallery',
    },
  });

  return board;
};

// Update Board Action
export const updateBoard: UpdateBoard<
  { id: string; name?: string; description?: string; parentBoardId?: string; listView?: string },
  Board
> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const { id, ...updateData } = args;

  // Find the board
  const board = await context.entities.Board.findUnique({
    where: { id },
    include: { organization: true },
  });

  if (!board) {
    throw new HttpError(404, 'Board not found');
  }

  if (board.deletedAt) {
    throw new HttpError(400, 'Cannot update deleted board');
  }

  // Verify user is member of organization
  const orgMember = await context.entities.OrganizationMember.findFirst({
    where: {
      userId: context.user.id,
      organizationId: board.organizationId,
    },
  });

  if (!orgMember) {
    throw new HttpError(403, 'User is not a member of this organization');
  }

  // Validate parent board change if specified
  if (updateData.parentBoardId !== undefined) {
    if (updateData.parentBoardId === id) {
      throw new HttpError(400, 'Board cannot be its own parent');
    }

    if (updateData.parentBoardId) {
      const parentBoard = await context.entities.Board.findUnique({
        where: { id: updateData.parentBoardId },
      });

      if (!parentBoard) {
        throw new HttpError(404, 'Parent board not found');
      }

      if (parentBoard.organizationId !== board.organizationId) {
        throw new HttpError(403, 'Parent board belongs to different organization');
      }

      if (parentBoard.deletedAt) {
        throw new HttpError(400, 'Cannot move board under deleted parent');
      }
    }
  }

  // Update the board
  const updatedBoard = await context.entities.Board.update({
    where: { id },
    data: {
      ...updateData,
      updatedAt: new Date(),
    },
  });

  return updatedBoard;
};

// Delete Board Action (Soft Delete)
export const deleteBoard: DeleteBoard<{ id: string }, void> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const { id } = args;

  // Find the board
  const board = await context.entities.Board.findUnique({
    where: { id },
    include: {
      childBoards: true,
      assets: true,
    },
  });

  if (!board) {
    throw new HttpError(404, 'Board not found');
  }

  // Verify user is member of organization
  const orgMember = await context.entities.OrganizationMember.findFirst({
    where: {
      userId: context.user.id,
      organizationId: board.organizationId,
    },
  });

  if (!orgMember) {
    throw new HttpError(403, 'User is not a member of this organization');
  }

  // Check if board can be deleted (no child boards or assets)
  if (board.childBoards.length > 0) {
    throw new HttpError(400, 'Cannot delete board with child boards');
  }

  if (board.assets.length > 0) {
    throw new HttpError(400, 'Cannot delete board with assets');
  }

  // Soft delete the board
  await context.entities.Board.update({
    where: { id },
    data: {
      deletedAt: new Date(),
      updatedAt: new Date(),
    },
  });
};

// Add Asset to Board Action
export const addAssetToBoard: AddAssetToBoard<
  { assetId: number; boardId: string; status?: string },
  BoardAsset
> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const { assetId, boardId, status = 'None' } = args;

  // Find the board
  const board = await context.entities.Board.findUnique({
    where: { id: boardId },
  });

  if (!board) {
    throw new HttpError(404, 'Board not found');
  }

  if (board.deletedAt) {
    throw new HttpError(400, 'Cannot add assets to deleted board');
  }

  // Find the asset
  const asset = await context.entities.Asset.findUnique({
    where: { id: assetId },
  });

  if (!asset) {
    throw new HttpError(404, 'Asset not found');
  }

  // Verify user is member of organization
  const orgMember = await context.entities.OrganizationMember.findFirst({
    where: {
      userId: context.user.id,
      organizationId: board.organizationId,
    },
  });

  if (!orgMember) {
    throw new HttpError(403, 'User is not a member of this organization');
  }

  // Check if asset is already in board
  const existingBoardAsset = await context.entities.BoardAsset.findFirst({
    where: {
      assetId,
      boardId,
    },
  });

  if (existingBoardAsset) {
    throw new HttpError(400, 'Asset is already in this board');
  }

  // Add asset to board
  const boardAsset = await context.entities.BoardAsset.create({
    data: {
      assetId,
      boardId,
      status,
    },
  });

  return boardAsset;
};

// Update Board Asset Status Action
export const updateBoardAssetStatus: UpdateBoardAssetStatus<
  { boardAssetId: string; status: string },
  BoardAsset
> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const { boardAssetId, status } = args;

  // Find the board asset
  const boardAsset = await context.entities.BoardAsset.findUnique({
    where: { id: boardAssetId },
    include: { board: true },
  });

  if (!boardAsset) {
    throw new HttpError(404, 'Board asset not found');
  }

  // Verify user is member of organization
  const orgMember = await context.entities.OrganizationMember.findFirst({
    where: {
      userId: context.user.id,
      organizationId: boardAsset.board.organizationId,
    },
  });

  if (!orgMember) {
    throw new HttpError(403, 'User is not a member of this organization');
  }

  // Update the status
  const updatedBoardAsset = await context.entities.BoardAsset.update({
    where: { id: boardAssetId },
    data: { status },
  });

  return updatedBoardAsset;
};

// Get Boards Query
export const getBoards: GetBoards<{ organizationId: string }, Board[]> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const { organizationId } = args;

  // Verify user is member of organization
  const orgMember = await context.entities.OrganizationMember.findFirst({
    where: {
      userId: context.user.id,
      organizationId,
    },
  });

  if (!orgMember) {
    throw new HttpError(403, 'User is not a member of this organization');
  }

  // Get all non-deleted boards for the organization
  const boards = await context.entities.Board.findMany({
    where: {
      organizationId,
      deletedAt: null,
    },
    include: {
      childBoards: {
        where: { deletedAt: null },
      },
      assets: true,
      parentBoard: true,
    },
    orderBy: [{ parentBoardId: 'asc' }, { name: 'asc' }],
  });

  return boards;
};

// Get Single Board Query
export const getBoard: GetBoard<{ id: string }, Board | null> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const { id } = args;

  if (!id) {
    return null;
  }

  // Find the board
  const board = await context.entities.Board.findUnique({
    where: { id },
    include: {
      childBoards: {
        where: { deletedAt: null },
      },
      assets: {
        include: { asset: true },
      },
      parentBoard: true,
    },
  });


  if (!board) {
    return null;
  }

  // Verify user is member of organization
  const orgMember = await context.entities.OrganizationMember.findFirst({
    where: {
      userId: context.user.id,
      organizationId: board.organizationId,
    },
  });

  if (!orgMember) {
    throw new HttpError(403, 'User is not a member of this organization');
  }

  return board;
};

// Get Board Assets Query
export const getBoardAssets: GetBoardAssets<{ boardId: string }, BoardAsset[]> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const { boardId } = args;

  // Find the board
  const board = await context.entities.Board.findUnique({
    where: { id: boardId },
  });

  if (!board) {
    throw new HttpError(404, 'Board not found');
  }

  // Verify user is member of organization
  const orgMember = await context.entities.OrganizationMember.findFirst({
    where: {
      userId: context.user.id,
      organizationId: board.organizationId,
    },
  });

  if (!orgMember) {
    throw new HttpError(403, 'User is not a member of this organization');
  }

  // Get board assets
  const boardAssets = await context.entities.BoardAsset.findMany({
    where: { boardId },
    include: { asset: true },
    orderBy: { createdAt: 'desc' },
  });

  return boardAssets;
};
