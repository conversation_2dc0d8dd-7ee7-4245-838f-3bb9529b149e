import sharp from 'sharp';

const TARGET_FILE_SIZE = 6 * 1024 * 1024; // 6MB

export async function compressImage(buffer: Buffer, mimeType: string): Promise<Buffer> {
  let quality = 85; // Start with slightly lower quality since target is 6MB
  let compressedBuffer = buffer;
  let attempt = 0;
  const maxAttempts = 5;

  while (compressedBuffer.length > TARGET_FILE_SIZE && attempt < maxAttempts) {
    try {
      let sharpImage = sharp(buffer);

      // Convert PNG to JPEG if it's too large
      if (mimeType === 'image/png' && buffer.length > TARGET_FILE_SIZE) {
        sharpImage = sharpImage.jpeg({ quality });
      } else if (mimeType === 'image/jpeg') {
        sharpImage = sharpImage.jpeg({ quality });
      } else if (mimeType === 'image/webp') {
        sharpImage = sharpImage.webp({ quality });
      }

      // More aggressive resizing strategy for 6MB target
      const metadata = await sharp(compressedBuffer).metadata();
      const width = metadata.width;
      if (width) {
        // Resize based on attempt number
        if (width > 1800 || attempt > 0) {
          const targetWidth = attempt === 0 ? 1800 : Math.min(1800, Math.floor(width * 0.8));
          sharpImage = sharpImage.resize(targetWidth, null, { withoutEnlargement: true });
        }
      }

      compressedBuffer = await sharpImage.toBuffer();

      // Reduce quality more aggressively
      quality = Math.max(quality - 12, 55); // Lower minimum quality to 55
      attempt++;

      console.log(
        `Compression attempt ${attempt}: size=${(compressedBuffer.length / 1024 / 1024).toFixed(2)}MB, quality=${quality}`
      );
    } catch (error) {
      console.error('Error compressing image:', error);
      throw error;
    }
  }

  return compressedBuffer;
}
