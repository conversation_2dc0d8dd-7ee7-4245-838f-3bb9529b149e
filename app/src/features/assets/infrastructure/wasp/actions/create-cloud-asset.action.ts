import { HttpError } from 'wasp/server';
import type { CreateCloudAsset } from 'wasp/server/operations';
import { CloudAssetSchema, AssetSource } from '../../../domain/types';

/**
 * 🔗 Create a virtual asset reference for a cloud storage file
 */
export const createCloudAsset: CreateCloudAsset<
  {
    cloudFileId: string;
    fileName: string;
    fileType: string;
    source: string;
    organizationId: string;
    cloudThumbnailUrl?: string;
    cloudWebViewUrl?: string;
    cloudPath?: string;
    width?: number;
    height?: number;
    category?: string;
    tags?: string[];
    collectionIds?: string[];
    altText?: string;
    description?: string;
    lastModified?: Date;
  },
  { id: number; success: boolean }
> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'You must be logged in to create cloud assets');
  }

  const user = context.user; // Type assertion after auth check

  try {
    // Validate input data
    const validatedData = CloudAssetSchema.parse(args);

    // Check if cloud asset already exists for this file
    const existingAsset = await context.entities.Asset.findFirst({
      where: {
        cloudFileId: validatedData.cloudFileId,
        source: validatedData.source,
        organizationId: validatedData.organizationId,
        deletedAt: null,
      },
    });

    if (existingAsset) {
      return {
        id: existingAsset.id,
        success: true,
      };
    }

    // Create virtual asset reference
    const asset = await context.entities.Asset.create({
      data: {
        fileName: validatedData.fileName,
        fileType: validatedData.fileType,
        fileUrl: validatedData.cloudWebViewUrl || '', // Use web view URL as fallback
        source: validatedData.source,
        cloudFileId: validatedData.cloudFileId,
        cloudThumbnailUrl: validatedData.cloudThumbnailUrl,
        cloudWebViewUrl: validatedData.cloudWebViewUrl,
        cloudPath: validatedData.cloudPath,
        lastModified: validatedData.lastModified,
        width: validatedData.width,
        height: validatedData.height,
        category: validatedData.category,
        userId: user.id,
        organizationId: validatedData.organizationId,
        isGenerated: false,
        isCover: true,
        version: 1,
      },
    });

    // Add tags if provided
    if (validatedData.tags && validatedData.tags.length > 0) {
      const tagConnections = validatedData.tags.map((tagName) => ({
        name: tagName,
        userId: user.id,
        organizationId: validatedData.organizationId,
      }));

      // Create or connect tags
      for (const tagData of tagConnections) {
        const tag = await context.entities.AssetTag.upsert({
          where: {
            name_userId: {
              name: tagData.name,
              userId: user.id,
            },
          },
          create: tagData,
          update: {},
        });

        // Connect tag to asset
        await context.entities.Asset.update({
          where: { id: asset.id },
          data: {
            tags: {
              connect: { id: tag.id },
            },
          },
        });
      }
    }

    // Add to collections if provided
    if (validatedData.collectionIds && validatedData.collectionIds.length > 0) {
      await context.entities.Asset.update({
        where: { id: asset.id },
        data: {
          collections: {
            connect: validatedData.collectionIds.map((id) => ({ id })),
          },
        },
      });
    }

    console.log(`[createCloudAsset] Created virtual asset for ${validatedData.source} file: ${validatedData.fileName}`);

    return {
      id: asset.id,
      success: true,
    };
  } catch (error) {
    console.error('[createCloudAsset] Error creating cloud asset:', error);

    if (error instanceof Error && error.message.includes('validation')) {
      throw new HttpError(400, `Invalid cloud asset data: ${error.message}`);
    }

    throw new HttpError(
      500,
      `Failed to create cloud asset: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};
