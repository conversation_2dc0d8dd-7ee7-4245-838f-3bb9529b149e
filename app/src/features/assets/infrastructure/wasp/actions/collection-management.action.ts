import { z } from 'zod';
import { authenticateUser } from '../../../../../server/helpers';
import {
  type CreateCollection,
  type UpdateCollection,
  type AddToCollection,
  type RemoveFromCollection,
} from 'wasp/server/operations';
import { HttpError } from 'wasp/server';

// Collection Management Actions

// Collection schema
const CollectionSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  isPublic: z.boolean().default(false),
  organizationId: z.string(),
  projectId: z.string().optional(),
});

type CreateCollectionArgs = z.infer<typeof CollectionSchema>;

export const createCollection: CreateCollection<CreateCollectionArgs, { id: string }> = async (args, context) => {
  const currentUser = authenticateUser(context);

  try {
    CollectionSchema.parse(args);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new HttpError(400, error.errors.map((e) => e.message).join(', '));
    }
    throw error;
  }

  const { name, description, isPublic, organizationId, projectId } = args;

  // Check if collection with same name already exists for this user
  const existingCollection = await context.entities.AssetCollection.findFirst({
    where: {
      name,
      userId: currentUser.id,
      organizationId,
    },
  });

  if (existingCollection) {
    throw new HttpError(400, 'Collection with this name already exists');
  }

  const collection = await context.entities.AssetCollection.create({
    data: {
      name,
      description,
      isPublic,
      user: { connect: { id: currentUser.id } },
      organization: { connect: { id: organizationId } },
      ...(projectId && { project: { connect: { id: projectId } } }),
    },
  });

  return { id: collection.id };
};

// Update collection
type UpdateCollectionArgs = {
  id: string;
  data: Partial<z.infer<typeof CollectionSchema>>;
};

export const updateCollection: UpdateCollection<UpdateCollectionArgs, void> = async (args, context) => {
  const currentUser = authenticateUser(context);
  const { id, data } = args;

  // Validate the update data
  const updateSchema = CollectionSchema.partial();
  try {
    updateSchema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new HttpError(400, error.errors.map((e) => e.message).join(', '));
    }
    throw error;
  }

  // Verify user owns the collection
  const collection = await context.entities.AssetCollection.findFirst({
    where: {
      id,
      userId: currentUser.id,
    },
  });

  if (!collection) {
    throw new HttpError(404, 'Collection not found or you do not have permission to update it');
  }

  // If updating name, check for duplicates
  if (data.name && data.name !== collection.name) {
    const existingCollection = await context.entities.AssetCollection.findFirst({
      where: {
        name: data.name,
        userId: currentUser.id,
        organizationId: collection.organizationId,
        id: { not: id },
      },
    });

    if (existingCollection) {
      throw new HttpError(400, 'Collection with this name already exists');
    }
  }

  await context.entities.AssetCollection.update({
    where: { id },
    data: {
      ...data,
      // Don't allow changing organizationId or userId through this endpoint
      organizationId: undefined,
      userId: undefined,
    },
  });
};

// Delete collection
export const deleteCollection = async (args: { id: string }, context: any) => {
  const currentUser = authenticateUser(context);
  const { id } = args;

  // Verify user owns the collection
  const collection = await context.entities.AssetCollection.findFirst({
    where: {
      id,
      userId: currentUser.id,
    },
  });

  if (!collection) {
    throw new HttpError(404, 'Collection not found or you do not have permission to delete it');
  }

  // Remove all assets from the collection first
  await context.entities.Asset.updateMany({
    where: {
      collections: {
        some: { id },
      },
    },
    data: {
      collections: {
        disconnect: { id },
      },
    },
  });

  // Delete the collection
  await context.entities.AssetCollection.delete({
    where: { id },
  });
};

// Add assets to collection
type AddToCollectionArgs = {
  collectionId: string;
  assetIds: number[];
};

export const addToCollection: AddToCollection<AddToCollectionArgs, void> = async (args, context) => {
  const currentUser = authenticateUser(context);
  const { collectionId, assetIds } = args;

  // Verify user owns the collection
  const collection = await context.entities.AssetCollection.findFirst({
    where: {
      id: collectionId,
      userId: currentUser.id,
    },
  });

  if (!collection) {
    throw new HttpError(404, 'Collection not found or you do not have permission to modify it');
  }

  // Verify user owns all assets
  const assets = await context.entities.Asset.findMany({
    where: {
      id: { in: assetIds },
      userId: currentUser.id,
    },
    select: { id: true },
  });

  if (assets.length !== assetIds.length) {
    throw new HttpError(403, 'You can only add your own assets to collections');
  }

  // Add assets to collection
  await context.entities.AssetCollection.update({
    where: { id: collectionId },
    data: {
      assets: {
        connect: assetIds.map((id) => ({ id })),
      },
    },
  });
};

// Remove assets from collection
type RemoveFromCollectionArgs = {
  collectionId: string;
  assetIds: number[];
};

export const removeFromCollection: RemoveFromCollection<RemoveFromCollectionArgs, void> = async (args, context) => {
  const currentUser = authenticateUser(context);
  const { collectionId, assetIds } = args;

  // Verify user owns the collection
  const collection = await context.entities.AssetCollection.findFirst({
    where: {
      id: collectionId,
      userId: currentUser.id,
    },
  });

  if (!collection) {
    throw new HttpError(404, 'Collection not found or you do not have permission to modify it');
  }

  // Remove assets from collection
  await context.entities.AssetCollection.update({
    where: { id: collectionId },
    data: {
      assets: {
        disconnect: assetIds.map((id) => ({ id })),
      },
    },
  });
};

// Bulk collection operations
export const bulkAddToCollections = async (
  args: {
    assetIds: number[];
    collectionIds: string[];
  },
  context: any
) => {
  const currentUser = authenticateUser(context);
  const { assetIds, collectionIds } = args;

  // Verify user owns all collections
  const collections = await context.entities.AssetCollection.findMany({
    where: {
      id: { in: collectionIds },
      userId: currentUser.id,
    },
    select: { id: true },
  });

  if (collections.length !== collectionIds.length) {
    throw new HttpError(403, 'You can only add assets to your own collections');
  }

  // Verify user owns all assets
  const assets = await context.entities.Asset.findMany({
    where: {
      id: { in: assetIds },
      userId: currentUser.id,
    },
    select: { id: true },
  });

  if (assets.length !== assetIds.length) {
    throw new HttpError(403, 'You can only add your own assets to collections');
  }

  // Add assets to all collections
  await Promise.all(
    collectionIds.map((collectionId) =>
      context.entities.AssetCollection.update({
        where: { id: collectionId },
        data: {
          assets: {
            connect: assetIds.map((id) => ({ id })),
          },
        },
      })
    )
  );
};

export const bulkRemoveFromCollections = async (
  args: {
    assetIds: number[];
    collectionIds: string[];
  },
  context: any
) => {
  const currentUser = authenticateUser(context);
  const { assetIds, collectionIds } = args;

  // Verify user owns all collections
  const collections = await context.entities.AssetCollection.findMany({
    where: {
      id: { in: collectionIds },
      userId: currentUser.id,
    },
    select: { id: true },
  });

  if (collections.length !== collectionIds.length) {
    throw new HttpError(403, 'You can only remove assets from your own collections');
  }

  // Remove assets from all collections
  await Promise.all(
    collectionIds.map((collectionId) =>
      context.entities.AssetCollection.update({
        where: { id: collectionId },
        data: {
          assets: {
            disconnect: assetIds.map((id) => ({ id })),
          },
        },
      })
    )
  );
};

// Get collection with assets
export const getCollectionWithAssets = async (args: { id: string; includeAssets?: boolean }, context: any) => {
  const currentUser = authenticateUser(context);
  const { id, includeAssets = true } = args;

  const collection = await context.entities.AssetCollection.findFirst({
    where: {
      id,
      OR: [{ userId: currentUser.id }, { isPublic: true }],
    },
    include: {
      ...(includeAssets && {
        assets: {
          include: {
            tags: true,
            analytics: true,
          },
          orderBy: {
            uploadedAt: 'desc',
          },
        },
      }),
      user: {
        select: {
          id: true,
          email: true,
        },
      },
    },
  });

  if (!collection) {
    throw new HttpError(404, 'Collection not found or you do not have permission to view it');
  }

  return collection;
};
