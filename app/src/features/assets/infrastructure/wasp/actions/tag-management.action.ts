import { z } from 'zod';
import { authenticateUser } from '../../../../../server/helpers';
import { type AddAssetTags, type RemoveAssetTags } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';

// Tag Management Actions

// Add tags to assets
type AddAssetTagsArgs = {
  assetIds: number[];
  tags: string[];
  organizationId: string;
};

export const addAssetTags: AddAssetTags<AddAssetTagsArgs, void> = async (args, context) => {
  const currentUser = authenticateUser(context);

  const { assetIds, tags, organizationId } = args;

  // Validate that user owns all assets
  const assets = await context.entities.Asset.findMany({
    where: {
      id: { in: assetIds },
      userId: currentUser.id,
    },
    select: { id: true },
  });

  if (assets.length !== assetIds.length) {
    throw new HttpError(403, 'You can only add tags to your own assets');
  }

  await Promise.all(
    assetIds.map((assetId) =>
      context.entities.Asset.update({
        where: {
          id: assetId,
          userId: currentUser.id,
        },
        data: {
          tags: {
            connectOrCreate: tags.map((tag) => ({
              where: {
                name_userId: {
                  name: tag,
                  userId: currentUser.id,
                },
              },
              create: {
                name: tag,
                userId: currentUser.id,
                organizationId,
              },
            })),
          },
        },
      })
    )
  );
};

// Remove tags from assets
type RemoveAssetTagsArgs = {
  assetIds: number[];
  tagIds: string[];
};

export const removeAssetTags: RemoveAssetTags<RemoveAssetTagsArgs, void> = async (args, context) => {
  const currentUser = authenticateUser(context);
  const { assetIds, tagIds } = args;

  // Validate that user owns all assets
  const assets = await context.entities.Asset.findMany({
    where: {
      id: { in: assetIds },
      userId: currentUser.id,
    },
    select: { id: true },
  });

  if (assets.length !== assetIds.length) {
    throw new HttpError(403, 'You can only remove tags from your own assets');
  }

  await Promise.all(
    assetIds.map((assetId) =>
      context.entities.Asset.update({
        where: {
          id: assetId,
          userId: currentUser.id,
        },
        data: {
          tags: {
            disconnect: tagIds.map((id) => ({ id })),
          },
        },
      })
    )
  );
};

// Create a new tag
const CreateTagSchema = z.object({
  name: z.string().min(1).max(50),
  color: z.string().optional(),
  category: z.string().optional(),
  organizationId: z.string(),
});

type CreateTagArgs = z.infer<typeof CreateTagSchema>;

export const createTag = async (args: CreateTagArgs, context: any) => {
  const currentUser = authenticateUser(context);

  try {
    CreateTagSchema.parse(args);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new HttpError(400, error.errors.map((e) => e.message).join(', '));
    }
    throw error;
  }

  const { name, color, category, organizationId } = args;

  // Check if tag already exists for this user
  const existingTag = await context.entities.AssetTag.findFirst({
    where: {
      name,
      userId: currentUser.id,
    },
  });

  if (existingTag) {
    throw new HttpError(400, 'Tag with this name already exists');
  }

  const tag = await context.entities.AssetTag.create({
    data: {
      name,
      color,
      category,
      user: { connect: { id: currentUser.id } },
      organization: { connect: { id: organizationId } },
    },
  });

  return tag;
};

// Update a tag
const UpdateTagSchema = z.object({
  name: z.string().min(1).max(50).optional(),
  color: z.string().optional(),
  category: z.string().optional(),
});

type UpdateTagArgs = {
  id: string;
  data: z.infer<typeof UpdateTagSchema>;
};

export const updateTag = async (args: UpdateTagArgs, context: any) => {
  const currentUser = authenticateUser(context);
  const { id, data } = args;

  try {
    UpdateTagSchema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new HttpError(400, error.errors.map((e) => e.message).join(', '));
    }
    throw error;
  }

  // Verify user owns the tag
  const tag = await context.entities.AssetTag.findFirst({
    where: {
      id,
      userId: currentUser.id,
    },
  });

  if (!tag) {
    throw new HttpError(404, 'Tag not found or you do not have permission to update it');
  }

  // If updating name, check for duplicates
  if (data.name && data.name !== tag.name) {
    const existingTag = await context.entities.AssetTag.findFirst({
      where: {
        name: data.name,
        userId: currentUser.id,
        id: { not: id },
      },
    });

    if (existingTag) {
      throw new HttpError(400, 'Tag with this name already exists');
    }
  }

  const updatedTag = await context.entities.AssetTag.update({
    where: { id },
    data,
  });

  return updatedTag;
};

// Delete a tag
export const deleteTag = async (args: { id: string }, context: any) => {
  const currentUser = authenticateUser(context);
  const { id } = args;

  // Verify user owns the tag
  const tag = await context.entities.AssetTag.findFirst({
    where: {
      id,
      userId: currentUser.id,
    },
  });

  if (!tag) {
    throw new HttpError(404, 'Tag not found or you do not have permission to delete it');
  }

  // Remove tag from all assets first
  await context.entities.Asset.updateMany({
    where: {
      tags: {
        some: { id },
      },
    },
    data: {
      tags: {
        disconnect: { id },
      },
    },
  });

  // Delete the tag
  await context.entities.AssetTag.delete({
    where: { id },
  });
};

// Bulk tag operations
export const bulkAddTags = async (
  args: {
    assetIds: number[];
    tagNames: string[];
    organizationId: string;
  },
  context: any
) => {
  const currentUser = authenticateUser(context);
  const { assetIds, tagNames, organizationId } = args;

  // Validate that user owns all assets
  const assets = await context.entities.Asset.findMany({
    where: {
      id: { in: assetIds },
      userId: currentUser.id,
    },
    select: { id: true },
  });

  if (assets.length !== assetIds.length) {
    throw new HttpError(403, 'You can only add tags to your own assets');
  }

  // Create or find all tags
  const tagOperations = tagNames.map((tagName) => ({
    where: {
      name_userId: {
        name: tagName,
        userId: currentUser.id,
      },
    },
    create: {
      name: tagName,
      userId: currentUser.id,
      organizationId,
    },
  }));

  // Add tags to all assets
  await Promise.all(
    assetIds.map((assetId) =>
      context.entities.Asset.update({
        where: { id: assetId },
        data: {
          tags: {
            connectOrCreate: tagOperations,
          },
        },
      })
    )
  );
};

export const bulkRemoveTags = async (
  args: {
    assetIds: number[];
    tagIds: string[];
  },
  context: any
) => {
  const currentUser = authenticateUser(context);
  const { assetIds, tagIds } = args;

  // Validate that user owns all assets
  const assets = await context.entities.Asset.findMany({
    where: {
      id: { in: assetIds },
      userId: currentUser.id,
    },
    select: { id: true },
  });

  if (assets.length !== assetIds.length) {
    throw new HttpError(403, 'You can only remove tags from your own assets');
  }

  // Remove tags from all assets
  await Promise.all(
    assetIds.map((assetId) =>
      context.entities.Asset.update({
        where: { id: assetId },
        data: {
          tags: {
            disconnect: tagIds.map((id) => ({ id })),
          },
        },
      })
    )
  );
};
