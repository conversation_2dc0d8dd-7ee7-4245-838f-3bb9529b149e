import type { DeleteAsset } from 'wasp/server/operations';
import type { Asset } from 'wasp/entities';
import { HttpError } from 'wasp/server';

export const deleteAsset: DeleteAsset<{ id: number }, void> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const { id } = args;

  // Find the asset
  const asset = await context.entities.Asset.findUnique({
    where: { id },
  });

  if (!asset) {
    throw new HttpError(404, 'Asset not found');
  }

  // Check if user owns the asset
  if (asset.userId !== context.user.id) {
    throw new HttpError(403, 'You can only delete your own assets');
  }

  // Delete the asset (this will cascade to related records)
  await context.entities.Asset.delete({
    where: { id },
  });
};
