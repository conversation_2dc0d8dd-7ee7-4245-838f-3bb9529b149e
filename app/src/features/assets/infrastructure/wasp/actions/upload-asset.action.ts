import { z } from 'zod';
import { Asset } from 'wasp/entities';
import { authenticateUser } from '../../../../../server/helpers';
import { type UploadAsset } from 'wasp/server/operations';
import { HttpError } from 'wasp/server';
import { uploadToR2 } from '../../../../../server/libs';
import { v4 as uuidv4 } from 'uuid';
import { fileTypeFromBuffer } from 'file-type';
import { compressImage } from '../utils/image-processing';

const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
const TARGET_FILE_SIZE = 6 * 1024 * 1024; // 6MB

const fileSignatures = {
  '/9j/': 'image/jpeg',
  iVBORw0KGgo: 'image/png',
  R0lGOD: 'image/gif',
  UklGR: 'image/webp',
  JVBERi0: 'application/pdf',
};

export function getFileTypeFromBase64(base64String: string): string | null {
  for (const [signature, mimeType] of Object.entries(fileSignatures)) {
    if (base64String.startsWith(signature)) {
      return mimeType;
    }
  }
  return null;
}

type UploadAssetResponse<T extends { type?: 'DUPLICATE' }> = T extends { type: 'DUPLICATE' }
  ? {
      type: 'DUPLICATE';
      existingAsset: Asset;
      message: string;
    }
  : Asset;

type UploadResult = UploadAssetResponse<{ type?: 'DUPLICATE' }>;

// Update the schema and types
const UploadAssetSchema = z.object({
  base64Content: z.string().min(1),
  claimedMimeType: z.string(),
  category: z.string().default('').optional(),
  isGenerated: z.boolean().default(false).optional(),
  key: z.string().default('brand-kits').optional(),
  photographyModelId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  collectionIds: z.array(z.string()).optional(),
  organizationId: z.string().min(1),
  previousAssetParentId: z.number().nullable().optional(),
  fileName: z.string().nullable().optional(),
  boardIds: z.union([z.string(), z.array(z.string())]).optional(),
});

type UploadAssetArgs = z.infer<typeof UploadAssetSchema>;

export const uploadAsset: UploadAsset<UploadAssetArgs, any> = async (args, context) => {
  const currentUser = authenticateUser(context);

  try {
    UploadAssetSchema.parse(args);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new HttpError(400, error.errors.map((e) => e.message).join(', '));
    }
    throw error;
  }

  const {
    base64Content,
    claimedMimeType,
    category,
    isGenerated,
    key,
    photographyModelId,
    tags = [],
    collectionIds = [],
    organizationId,
    boardIds,
  } = args;

  let { previousAssetParentId, fileName } = args;

  if (fileName) {
    const existingAsset = await context.entities.Asset.findFirst({
      where: {
        fileName,
        userId: currentUser.id,
      },
      select: { id: true, fileName: true, fileUrl: true, fileType: true },
    });

    if (existingAsset) {
      return {
        type: 'DUPLICATE',
        existingAsset,
        message: 'File name already exists',
      };
    }
  }

  // Improved base64 handling
  const base64Data = base64Content.split(';base64,').pop() || '';
  if (!base64Data) {
    throw new HttpError(400, 'Invalid base64 content');
  }

  let fileBuffer: Buffer;
  try {
    fileBuffer = Buffer.from(base64Data, 'base64');
  } catch (error) {
    throw new HttpError(400, 'Invalid base64 encoding');
  }

  const fileTypeResult = await fileTypeFromBuffer(fileBuffer);
  if (!fileTypeResult) {
    throw new HttpError(400, 'Unsupported file type');
  }
  const detectedMimeType = fileTypeResult.mime;

  if (detectedMimeType !== claimedMimeType) {
    throw new HttpError(400, 'File type does not match the claimed type');
  }

  if (fileBuffer.length > MAX_FILE_SIZE) {
    throw new HttpError(400, `File size exceeds the maximum limit of ${MAX_FILE_SIZE / (1024 * 1024)}MB`);
  }

  // Get image dimensions before compression
  let width: number | undefined;
  let height: number | undefined;

  if (detectedMimeType.startsWith('image/')) {
    const sharp = (await import('sharp')).default;
    const metadata = await sharp(fileBuffer).metadata();
    width = metadata.width;
    height = metadata.height;
  }

  // Compress image if it's over TARGET_FILE_SIZE (6MB)
  if (fileBuffer.length > TARGET_FILE_SIZE && detectedMimeType.startsWith('image/')) {
    console.log(`Compressing image of size ${(fileBuffer.length / 1024 / 1024).toFixed(2)}MB`);
    fileBuffer = await compressImage(fileBuffer, detectedMimeType);
    console.log(`Compressed to ${(fileBuffer.length / 1024 / 1024).toFixed(2)}MB`);
  }

  fileName = fileName ?? `${uuidv4()}-${Date.now()}`.replace(/[^a-zA-Z0-9\\_\\-]+/g, '_');
  const uploadedUrl = await uploadToR2({ fileBuffer, fileName, key });
  let version = 0;

  if (previousAssetParentId) {
    const parentAsset = await context.entities.Asset.findFirst({
      where: { id: previousAssetParentId },
      select: { parentAssetId: true },
    });

    if (parentAsset?.parentAssetId) {
      previousAssetParentId = parentAsset.parentAssetId;
    }

    version = await context.entities.Asset.count({
      where: {
        OR: [{ id: previousAssetParentId }, { parentAssetId: previousAssetParentId }],
      },
    });
  }

  // Define a type for the asset data to ensure type safety
  type AssetCreateData = {
    fileName: string;
    fileType: string;
    fileUrl: string;
    category?: string;
    isGenerated?: boolean;
    width?: number;
    height?: number;
    user: { connect: { id: string } };
    organization: { connect: { id: string } };
    isCover: boolean;
    version: number;
    tags?: {
      connectOrCreate: {
        where: { name_userId: { name: string; userId: string } };
        create: { name: string; userId: string; organizationId: string };
      }[];
    };
    collections?: {
      connect: { id: string }[];
    };
    photographyModel?: {
      connect: { id: string };
    };
  };

  // Create the asset data with proper typing
  const assetData: AssetCreateData = {
    fileName,
    fileType: detectedMimeType,
    fileUrl: uploadedUrl,
    category,
    isGenerated,
    width,
    height,
    user: { connect: { id: currentUser.id } },
    organization: { connect: { id: organizationId } },
    isCover: true,
    version: version + 1,
  };

  // Add optional relations conditionally
  if (tags?.length > 0) {
    assetData.tags = {
      connectOrCreate: tags.map((tag) => ({
        where: { name_userId: { name: tag, userId: currentUser.id } },
        create: { name: tag, userId: currentUser.id, organizationId },
      })),
    };
  }

  if (collectionIds?.length > 0) {
    assetData.collections = {
      connect: collectionIds.map((id) => ({ id })),
    };
  }

  if (photographyModelId) {
    assetData.photographyModel = {
      connect: { id: photographyModelId },
    };
  }

  // Create the asset with optimized include
  const newUpload = await context.entities.Asset.create({
    data: assetData,
    include: {
      tags: true,
      collections: true,
      photographyModel: true,
    },
  });

  // Optimize board asset creation for multiple boards
  if (boardIds && newUpload?.id) {
    if (Array.isArray(boardIds) && boardIds.length > 0) {
      // Use createMany for better performance with multiple boards
      await context.entities.BoardAsset.createMany({
        data: boardIds.map((boardId) => ({
          boardId,
          assetId: newUpload.id,
        })),
        skipDuplicates: true, // Skip any duplicates to prevent errors
      });
    } else if (!Array.isArray(boardIds)) {
      await context.entities.BoardAsset.create({
        data: {
          boardId: boardIds,
          assetId: newUpload.id,
        },
      });
    }
  }

  if (previousAssetParentId && newUpload?.id) {
    await context.entities.Asset.updateMany({
      where: {
        OR: [{ id: previousAssetParentId }, { parentAssetId: previousAssetParentId }],
      },
      data: {
        isCover: false,
        parentAssetId: newUpload.id,
      },
    });
  }

  return {
    ...newUpload,
    type: undefined,
  };
};
