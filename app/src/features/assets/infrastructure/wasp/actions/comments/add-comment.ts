import { z } from 'zod';
import { HttpError, config } from 'wasp/server';
import { authenticateUser } from '../../../../../../server/helpers';
import { createNotificationFromTemplate } from '../../../../../../notification/helper';
import { NOTIFICATION_TYPES, NOTIFICATION_SUBTYPES } from '../../../../../../notification/constants';
import { type AddComment } from 'wasp/server/operations';

// Validation schema for the addComment action
const addCommentSchema = z.object({
  assetId: z.number().min(1),
  text: z.string().min(1),
  x: z.number(),
  y: z.number(),
  organizationId: z.string().min(1),
});

export const addComment: AddComment<
  {
    assetId: number;
    text: string;
    x: number;
    y: number;
    organizationId: string;
  },
  any
> = async (args, context) => {
  const currentUser = authenticateUser(context);

  try {
    const validatedData = addCommentSchema.parse(args);
    const { assetId, text, x, y, organizationId } = validatedData;

    // Verify user is a member of the organization
    await context.entities.OrganizationMember.findFirstOrThrow({
      where: {
        organizationId,
        userId: currentUser.id,
      },
    });

    // Create the comment
    const comment = await context.entities.Comment.create({
      data: {
        text,
        x,
        y,
        asset: {
          connect: { id: assetId },
        },
        author: {
          connect: { id: currentUser.id },
        },
        organization: {
          connect: { id: organizationId },
        },
      },
      include: {
        author: true,
        replies: {
          include: {
            author: true,
          },
        },
      },
    });

    // Get all organization members except the current user
    const organizationMembers = await context.entities.OrganizationMember.findMany({
      where: {
        organizationId,
        userId: {
          not: currentUser.id,
        },
      },
      include: {
        user: true,
      },
    });

    // Send notifications to all organization members
    for (const member of organizationMembers) {
      if (!member.userId) continue; // Skip if userId is null

      try {
        await createNotificationFromTemplate({
          notificationEntity: context.entities.Notification,
          data: {
            userId: member.userId,
            type: NOTIFICATION_TYPES.ACTIVITY,
            subtype: NOTIFICATION_SUBTYPES.COMMENT,
            data: {
              actorName: currentUser.username || 'A user',
              commentText: text.substring(0, 50) + (text.length > 50 ? '...' : ''),
            },
            linkType: 'asset',
            linkId: String(assetId),
            linkData: {
              assetId,
              commentId: comment.id,
            },
            imageUrl: `${config.frontendUrl}/icons/comment.svg`,
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // Expires in 24 hours
          },
        });
      } catch (notificationError) {
        // Log but don't fail the entire operation if notification sending fails
        console.error('Failed to send notification:', notificationError);
      }
    }

    return comment;
  } catch (err) {
    if (err instanceof z.ZodError) {
      throw new HttpError(400, err.errors[0].message);
    }
    throw new HttpError(500, 'Failed to add comment');
  }
};
