import { z } from 'zod';
import { HttpError, config } from 'wasp/server';
import { authenticateUser } from '../../../../../../server/helpers';
import { createNotificationFromTemplate } from '../../../../../../notification/helper';
import { NOTIFICATION_TYPES, NOTIFICATION_SUBTYPES } from '../../../../../../notification/constants';
import { type AddCommentReply } from 'wasp/server/operations';

// Validation schema for the addCommentReply action
const addCommentReplySchema = z.object({
  commentId: z.string().min(1),
  text: z.string().min(1),
  organizationId: z.string().min(1),
});

export const addCommentReply: AddCommentReply<
  {
    commentId: string;
    text: string;
    organizationId: string;
  },
  any
> = async (args, context) => {
  const currentUser = authenticateUser(context);

  try {
    const validatedData = addCommentReplySchema.parse(args);
    const { commentId, text, organizationId } = validatedData;

    // Verify user is a member of the organization
    await context.entities.OrganizationMember.findFirstOrThrow({
      where: {
        organizationId,
        userId: currentUser.id,
      },
    });

    // Get the original comment to get the asset ID
    const originalComment = await context.entities.Comment.findFirstOrThrow({
      where: { id: commentId },
      include: {
        asset: true,
      },
    });

    // Create the reply
    const reply = await context.entities.Reply.create({
      data: {
        text,
        comment: {
          connect: { id: commentId },
        },
        author: {
          connect: { id: currentUser.id },
        },
        organization: {
          connect: { id: organizationId },
        },
      },
      include: {
        author: true,
      },
    });

    // Get all organization members except the current user
    const organizationMembers = await context.entities.OrganizationMember.findMany({
      where: {
        organizationId,
        userId: {
          not: currentUser.id,
        },
      },
      include: {
        user: true,
      },
    });

    // Send notifications to all organization members
    for (const member of organizationMembers) {
      if (!member.userId) continue; // Skip if userId is null

      try {
        await createNotificationFromTemplate({
          notificationEntity: context.entities.Notification,
          data: {
            userId: member.userId,
            type: NOTIFICATION_TYPES.ACTIVITY,
            subtype: NOTIFICATION_SUBTYPES.COMMENT,
            data: {
              actorName: currentUser.username || 'A user',
              commentText: text.substring(0, 50) + (text.length > 50 ? '...' : ''),
            },
            linkType: 'asset',
            linkId: String(originalComment.assetId),
            linkData: {
              assetId: originalComment.assetId,
              commentId: commentId,
              replyId: reply.id,
            },
            imageUrl: `${config.frontendUrl}/icons/comment.svg`,
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // Expires in 24 hours
          },
        });
      } catch (notificationError) {
        // Log but don't fail the entire operation if notification sending fails
        console.error('Failed to send notification:', notificationError);
      }
    }

    // Fetch the updated comment with replies
    const updatedComment = await context.entities.Comment.findFirst({
      where: { id: commentId },
      include: {
        author: true,
        replies: {
          include: {
            author: true,
          },
          orderBy: {
            createdAt: 'asc',
          },
        },
      },
    });

    return { reply, comment: updatedComment };
  } catch (err) {
    if (err instanceof z.ZodError) {
      throw new HttpError(400, err.errors[0].message);
    }
    throw new HttpError(500, 'Failed to add reply');
  }
};