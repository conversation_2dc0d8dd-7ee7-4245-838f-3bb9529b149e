import { HttpError } from 'wasp/server';
import type { GetCloudAssets } from 'wasp/server/operations';
import { getGoogleDriveFiles } from '../../../../../server/queries/googleDrive/getFiles';
import { getOneDriveFiles } from '../../../../../server/queries/oneDriveFiles/getOneDriveFiles';
import { getDropboxFiles } from '../../../../../server/queries/dropboxFiles/getDropboxFiles';
import { AssetSource, CloudAssetFile } from '../../../domain/types';
import { cacheFetch } from '../../../../../server/libs/cache';

/**
 * 🔍 Get cloud storage files as virtual assets
 */
export const getCloudAssets: GetCloudAssets<
  {
    organizationId: string;
    source?: string;
    search?: string;
    pageToken?: string;
    pageSize?: number;
    folderPath?: string; // Added for folder navigation
  },
  {
    files: any[];
    hasMore: boolean;
    nextPageToken?: string;
    source: string;
    currentPath?: string; // Added to track current folder
  }
> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'You must be logged in to access cloud assets');
  }

  const { source, search, pageToken, pageSize = 30 } = args;

  try {
    switch (source) {
      case AssetSource.GOOGLE_DRIVE:
        return await getGoogleDriveCloudAssets(args, context);

      case AssetSource.ONEDRIVE:
        return await getOneDriveCloudAssets(args, context);

      case AssetSource.DROPBOX:
        return await getDropboxCloudAssets(args, context);

      default:
        throw new HttpError(400, 'Invalid cloud storage source');
    }
  } catch (error) {
    console.error(`[getCloudAssets] Error fetching ${source} files:`, error);
    throw new HttpError(
      500,
      `Failed to fetch ${source} files: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};

/**
 * 📁 Get Google Drive files as cloud assets
 */
async function getGoogleDriveCloudAssets(args: any, context: any) {
  const cacheKey = [
    'googleDrive',
    context.user?.id ?? 'anon',
    args.search || 'all',
    args.pageToken || 'first',
    args.pageSize || 30,
  ].join(':');

  return cacheFetch(cacheKey, 60 * 60 * 24, async () => {
    const result = await getGoogleDriveFiles(
      {
        searchQuery: args.search,
        pageToken: args.pageToken,
        pageSize: args.pageSize,
      },
      context
    );

    const cloudFiles = result.files.map((file) => ({
      id: file.id,
      name: file.name,
      mimeType: file.mimeType,
      size: Number(file.size) || 0,
      lastModified: new Date(file.modifiedTime || Date.now()),
      thumbnailUrl: file.thumbnailLink,
      webViewUrl: file.webViewLink,
      path: file.name, // Google Drive doesn't have traditional paths
      source: 'google_drive',
    }));

    return {
      files: cloudFiles,
      hasMore: !!result.nextPageToken,
      nextPageToken: result.nextPageToken || undefined,
      source: 'google_drive',
    };
  });
}

/**
 * 📁 Get OneDrive files as cloud assets
 */
async function getOneDriveCloudAssets(args: any, context: any) {
  const cacheKey = ['oneDrive', context.user?.id ?? 'anon', args.folderPath || 'root', args.pageToken || 'first'].join(
    ':'
  );

  return cacheFetch(cacheKey, 60 * 60 * 24, async () => {
    const result = await getOneDriveFiles(
      {
        path: args.folderPath || args.path,
        nextLink: args.pageToken,
      },
      context
    );

    const cloudFiles = result.files
      .filter((file) => file.isFolder || file.type?.startsWith('image/')) // Include folders and images
      .map((file) => ({
        id: file.id,
        name: file.name,
        mimeType: file.type || (file.isFolder ? 'folder' : 'application/octet-stream'),
        size: file.size || 0,
        lastModified: new Date(file.lastModified || Date.now()),
        thumbnailUrl: file.thumbnailUrl || undefined,
        path: file.path,
        source: 'onedrive',
        isFolder: file.isFolder || false,
        folderPath: args.folderPath || '',
      }));

    return {
      files: cloudFiles,
      hasMore: result.hasMore,
      nextPageToken: result.nextLink || undefined,
      source: 'onedrive',
      currentPath: args.folderPath || '',
    };
  });
}

/**
 * 📁 Get Dropbox files as cloud assets
 */
async function getDropboxCloudAssets(args: any, context: any) {
  const cacheKey = ['dropbox', context.user?.id ?? 'anon', args.folderPath || 'root'].join(':');

  return cacheFetch(cacheKey, 60 * 60 * 24, async () => {
    try {
      const result = await getDropboxFiles(
        {
          path: args.folderPath || args.path || '',
        },
        context
      );

      const cloudFiles = result.files.map((file: any) => ({
        id: file.id,
        name: file.name,
        mimeType: file.type || (file.isFolder ? 'folder' : 'application/octet-stream'),
        size: file.size || 0,
        lastModified: new Date(file.lastModified || Date.now()),
        thumbnailUrl: file.thumbnailUrl,
        path: file.path,
        source: 'dropbox',
        isFolder: file.isFolder || false,
        folderPath: args.folderPath || '',
      }));

      return {
        files: cloudFiles,
        hasMore: result.hasMore || false,
        nextPageToken: result.cursor || undefined,
        source: 'dropbox',
        currentPath: args.folderPath || '',
      };
    } catch (error) {
      console.warn('[getCloudAssets] Dropbox integration not implemented yet');
      return {
        files: [],
        hasMore: false,
        nextPageToken: undefined,
        source: 'dropbox',
        currentPath: '',
      };
    }
  });
}
