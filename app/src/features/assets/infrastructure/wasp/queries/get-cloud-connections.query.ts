import { HttpError } from 'wasp/server';
import type { GetCloudConnections } from 'wasp/server/operations';
import { CloudAssetConnection } from '../../../domain/types';
import { getGoogleRefreshToken } from '../../../../../server/services/googleOAuthService';
import { getOneDriveRefreshToken } from '../../../../../server/services/oneDriveOAuthService';
import { getDropboxRefreshToken } from '../../../../../server/services/dropboxOAuthService';

/**
 * 🔗 Get cloud storage connection status for user
 */
export const getCloudConnections: GetCloudConnections<{ organizationId: string }, { connections: any[] }> = async (
  args,
  context
) => {
  if (!context.user) {
    throw new HttpError(401, 'You must be logged in to check cloud connections');
  }

  try {
    const connections: CloudAssetConnection[] = [];

    // Check Google Drive connection
    try {
      const googleToken = await getGoogleRefreshToken(context.user.id);
      const googleAssetCount = await context.entities.Asset.count({
        where: {
          organizationId: args.organizationId,
          source: 'google_drive',
          deletedAt: null,
        },
      });

      connections.push({
        provider: 'google_drive',
        isConnected: !!googleToken,
        totalFiles: googleAssetCount,
        lastSync: googleToken ? new Date() : undefined,
      });
    } catch (error) {
      connections.push({
        provider: 'google_drive',
        isConnected: false,
        error: 'Failed to check Google Drive connection',
      });
    }

    // Check OneDrive connection
    try {
      const oneDriveToken = await getOneDriveRefreshToken(context.user.id);
      const oneDriveAssetCount = await context.entities.Asset.count({
        where: {
          organizationId: args.organizationId,
          source: 'onedrive',
          deletedAt: null,
        },
      });

      connections.push({
        provider: 'onedrive',
        isConnected: !!oneDriveToken,
        totalFiles: oneDriveAssetCount,
        lastSync: oneDriveToken ? new Date() : undefined,
      });
    } catch (error) {
      connections.push({
        provider: 'onedrive',
        isConnected: false,
        error: 'Failed to check OneDrive connection',
      });
    }

    // Check Dropbox connection
    try {
      const dropboxToken = await getDropboxRefreshToken(context.user.id);
      const dropboxAssetCount = await context.entities.Asset.count({
        where: {
          organizationId: args.organizationId,
          source: 'dropbox',
          deletedAt: null,
        },
      });

      connections.push({
        provider: 'dropbox',
        isConnected: !!dropboxToken,
        totalFiles: dropboxAssetCount,
        lastSync: dropboxToken ? new Date() : undefined,
      });
    } catch (error) {
      connections.push({
        provider: 'dropbox',
        isConnected: false,
        error: 'Failed to check Dropbox connection',
      });
    }

    return { connections: connections as any };
  } catch (error) {
    console.error('[getCloudConnections] Error checking cloud connections:', error);
    throw new HttpError(
      500,
      `Failed to check cloud connections: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};
