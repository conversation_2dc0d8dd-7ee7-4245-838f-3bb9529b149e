import { Asset } from 'wasp/entities';
import { type GetAsset } from 'wasp/server/operations';
import { authenticateUser } from '../../../../../server/helpers';
import { HttpError, prisma } from 'wasp/server';
import { Prisma } from '@prisma/client';

// Inline URL cleaner function
const cleanUrl = (url: string): string => {
  // Remove any duplicate protocol prefixes
  const cleanedUrl = url.replace(/^(https?:\/\/)+(https?:\/\/)/, '$1');

  // Ensure the URL starts with https://
  if (!cleanedUrl.startsWith('https://')) {
    return 'https://' + cleanedUrl.replace(/^http:\/\//, '');
  }

  return cleanedUrl;
};

export const getAsset: GetAsset<{ assetId: string; organizationId: string }, Asset> = async (args, context) => {
  const currentUser = authenticateUser(context);

  if (args.assetId.startsWith('task_')) {
    const [_, taskId, index] = args.assetId.split('_');
    const task = await context.entities.GenerationTask.findUnique({
      where: { taskId: taskId },
    });

    if (!task || task.userId !== currentUser.id || !task.result || !task.prompt) {
      throw new HttpError(404, 'Generated image not found or you do not have permission to access it');
    }

    const resultData = JSON.parse(task.result);
    if (!resultData.images || resultData.images.length <= parseInt(index)) {
      throw new HttpError(404, 'No image found for this task and index');
    }

    return context.entities.Asset.create({
      data: {
        id: parseInt(`${task.id}${index}`, 10),
        isGenerated: true,
        isVariation: false,
        fileUrl: cleanUrl(resultData.images[parseInt(index)]),
        fileName: `Generated Image ${parseInt(index) + 1}`,
        fileType: 'image/png',
        category: 'Generated',
        userId: task.userId,
        uploadedAt: task.createdAt,
        photographyModelId: task.modelId,
        prompt: task.prompt,
        deletedAt: null,
        generationTaskId: task.id,
        originalAssetId: null,
        variationSettings: Prisma.JsonNull,
        organizationId: args.organizationId,
      },
      include: {
        photographyModel: true,
        variations: {
          include: {
            photographyModel: true,
          },
        },
        originalAsset: {
          include: {
            photographyModel: true,
            variations: {
              include: {
                photographyModel: true,
              },
            },
          },
        },
        tags: true,
        collections: true,
        analytics: true,
        user: true,
        parentAsset: true,
        versions: true,
        comments: {
          include: {
            author: true,
            replies: {
              include: {
                author: true,
              },
              orderBy: {
                createdAt: 'asc',
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        BoardAsset: {
          include: {
            board: true,
          },
        },
      },
    });
  } else {
    const organizations = await prisma.organizationMember.findMany({
      where: { userId: currentUser.id },
      select: { organizationId: true },
    });

    const organizationIds = organizations.map((org) => org.organizationId);

    const asset = await context.entities.Asset.findFirst({
      where: {
        id: Number(args.assetId),
        organizationId: {
          in: organizationIds,
        },
      },
      include: {
        photographyModel: true,
        variations: {
          include: {
            photographyModel: true,
          },
        },
        originalAsset: {
          include: {
            photographyModel: true,
            variations: {
              include: {
                photographyModel: true,
              },
            },
          },
        },
        tags: true,
        collections: true,
        analytics: true,
        user: true,
        parentAsset: {
          include: {
            versions: true,
          },
        },
        versions: true,
        comments: {
          include: {
            author: true,
            replies: {
              include: {
                author: true,
              },
              orderBy: {
                createdAt: 'asc',
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        BoardAsset: {
          include: {
            board: {
              include: {
                assets: {
                  take: 1,
                  include: {
                    asset: {
                      select: {
                        id: true,
                        fileUrl: true,
                        fileType: true,
                      },
                    },
                  },
                  orderBy: {
                    createdAt: 'desc',
                  },
                },
                _count: {
                  select: {
                    assets: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!asset) {
      throw new HttpError(404, 'Asset not found or you do not have permission to access it');
    }

    return {
      ...asset,
      fileUrl: cleanUrl(asset.fileUrl),
    };
  }
};

// Get asset with minimal data for performance
export const getAssetMinimal = async (args: { assetId: number; organizationId: string }, context: any) => {
  const currentUser = authenticateUser(context);

  const organizations = await prisma.organizationMember.findMany({
    where: { userId: currentUser.id },
    select: { organizationId: true },
  });

  const organizationIds = organizations.map((org) => org.organizationId);

  const asset = await context.entities.Asset.findFirst({
    where: {
      id: args.assetId,
      organizationId: {
        in: organizationIds,
      },
    },
    select: {
      id: true,
      fileName: true,
      fileType: true,
      fileUrl: true,
      width: true,
      height: true,
      category: true,
      isGenerated: true,
      userId: true,
      uploadedAt: true,
      organizationId: true,
    },
  });

  if (!asset) {
    throw new HttpError(404, 'Asset not found or you do not have permission to access it');
  }

  return {
    ...asset,
    fileUrl: cleanUrl(asset.fileUrl),
  };
};

// Get asset variations
export const getAssetVariations = async (args: { assetId: number; organizationId: string }, context: any) => {
  const currentUser = authenticateUser(context);

  // First verify the user can access the original asset
  const originalAsset = await getAssetMinimal(args, context);

  const variations = await context.entities.Asset.findMany({
    where: {
      originalAssetId: args.assetId,
      deletedAt: null,
    },
    include: {
      tags: true,
      analytics: true,
    },
    orderBy: {
      uploadedAt: 'desc',
    },
  });

  return {
    originalAsset,
    variations,
  };
};

// Get asset versions
export const getAssetVersions = async (args: { assetId: number; organizationId: string }, context: any) => {
  const currentUser = authenticateUser(context);

  // First verify the user can access the asset
  const asset = await getAssetMinimal(args, context);

  // Get all versions (including the current asset if it's a parent)
  const versions = await context.entities.Asset.findMany({
    where: {
      OR: [{ parentAssetId: args.assetId }, { id: args.assetId }],
      deletedAt: null,
    },
    include: {
      user: {
        select: {
          id: true,
          email: true,
        },
      },
    },
    orderBy: {
      version: 'desc',
    },
  });

  return {
    asset,
    versions,
  };
};

// Get asset analytics
export const getAssetAnalytics = async (args: { assetId: number; organizationId: string }, context: any) => {
  const currentUser = authenticateUser(context);

  // Verify user can access the asset
  const asset = await getAssetMinimal(args, context);

  // Check if user owns the asset
  if (asset.userId !== currentUser.id) {
    throw new HttpError(403, 'You can only view analytics for your own assets');
  }

  const analytics = await context.entities.AssetAnalytics.findUnique({
    where: { assetId: args.assetId },
  });

  return {
    asset,
    analytics: analytics || {
      views: 0,
      downloads: 0,
      shares: 0,
      lastViewed: null,
      lastDownload: null,
      lastShared: null,
    },
  };
};
