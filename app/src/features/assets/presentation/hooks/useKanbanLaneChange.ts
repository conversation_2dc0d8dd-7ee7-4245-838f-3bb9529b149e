import { useCallback } from 'react';
import { updateBoardAssetStatus } from 'wasp/client/operations';
import { toast } from 'react-hot-toast';

interface BoardAsset {
  id: string;
  assetId: number;
  status: string;
}

export function useKanbanLaneChange(boardAssets: BoardAsset[], refetch?: () => void) {
  const handleLaneChange = useCallback(
    async (assetId: string, _from: string, to: string) => {
      const boardAsset = boardAssets.find((ba) => ba.assetId === Number(assetId));
      if (!boardAsset) {
        toast.error('Asset not found');
        return;
      }
      await updateBoardAssetStatus({ boardAssetId: boardAsset.id, status: to });
      if (refetch) refetch();
    },
    [boardAssets, refetch]
  );

  return handleLaneChange;
}
