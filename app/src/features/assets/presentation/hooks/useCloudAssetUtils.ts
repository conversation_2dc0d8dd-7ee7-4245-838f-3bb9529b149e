import { AssetLibrarySections } from '../components';
import { AssetSource } from '../../domain/types';

export function useCloudAssetUtils() {
  // Helper functions for cloud asset filtering
  const isCloudSection = (section: AssetLibrarySections) => {
    return [AssetLibrarySections.GOOGLE_DRIVE, AssetLibrarySections.ONEDRIVE, AssetLibrarySections.DROPBOX].includes(
      section
    );
  };

  const getCloudSource = (section: AssetLibrarySections) => {
    switch (section) {
      case AssetLibrarySections.GOOGLE_DRIVE:
        return AssetSource.GOOGLE_DRIVE;
      case AssetLibrarySections.ONEDRIVE:
        return AssetSource.ONEDRIVE;
      case AssetLibrarySections.DROPBOX:
        return AssetSource.DROPBOX;
      default:
        return undefined;
    }
  };

  const getAssetFilter = (section: AssetLibrarySections) => {
    switch (section) {
      case AssetLibrarySections.GOOGLE_DRIVE:
      case AssetLibrarySections.ONEDRIVE:
      case AssetLibrarySections.DROPBOX:
        return 'all'; // Cloud assets use 'all' filter with source filtering
      default:
        return section;
    }
  };

  const getAssetSources = (section: AssetLibrarySections) => {
    switch (section) {
      case AssetLibrarySections.GOOGLE_DRIVE:
        return ['google_drive'];
      case AssetLibrarySections.ONEDRIVE:
        return ['onedrive'];
      case AssetLibrarySections.DROPBOX:
        return ['dropbox'];
      default:
        return undefined; // No source filtering for regular sections
    }
  };

  // Get section title
  const getSectionTitle = (section: AssetLibrarySections) => {
    switch (section) {
      case AssetLibrarySections.ALL_ASSETS:
        return 'All Assets';
      case AssetLibrarySections.RECENTLY_ADDED:
        return 'Recently Added';
      case AssetLibrarySections.MY_UPLOADS:
        return 'My Uploads';
      case AssetLibrarySections.RECENTLY_DELETED:
        return 'Recently Deleted';
      case AssetLibrarySections.GOOGLE_DRIVE:
        return 'Google Drive';
      case AssetLibrarySections.ONEDRIVE:
        return 'OneDrive';
      case AssetLibrarySections.DROPBOX:
        return 'Dropbox';
      default:
        return 'Assets';
    }
  };

  // Get empty state message
  const getEmptyMessage = (section: AssetLibrarySections, searchQuery?: string) => {
    if (searchQuery) {
      return `No assets found for "${searchQuery}"`;
    }

    switch (section) {
      case AssetLibrarySections.RECENTLY_ADDED:
        return 'No recently added assets';
      case AssetLibrarySections.MY_UPLOADS:
        return "You haven't uploaded any assets yet";
      case AssetLibrarySections.RECENTLY_DELETED:
        return 'No recently deleted assets';
      case AssetLibrarySections.GOOGLE_DRIVE:
        return "No Google Drive files found. Make sure you're connected and have image files in your drive.";
      case AssetLibrarySections.ONEDRIVE:
        return "No OneDrive files found. Make sure you're connected and have image files in your drive.";
      case AssetLibrarySections.DROPBOX:
        return "No Dropbox files found. Make sure you're connected and have image files in your dropbox.";
      default:
        return 'No assets found';
    }
  };

  return {
    isCloudSection,
    getCloudSource,
    getAssetFilter,
    getAssetSources,
    getSectionTitle,
    getEmptyMessage,
  } as const;
}
