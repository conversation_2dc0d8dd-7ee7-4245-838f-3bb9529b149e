import { useState } from 'react';
import { useQuery, createBoard, updateBoard, deleteBoard, getBoards } from 'wasp/client/operations';
import { useOrganizationState } from '../../../../organization/store';
import { Board } from '../../domain/types';

export function useBoardManagement() {
  const { selectedOrganizationId } = useOrganizationState();
  const [selectedBoardId, setSelectedBoardId] = useState<string>();

  // Fetch boards
  const {
    data: boards,
    isLoading,
    refetch,
  } = useQuery(getBoards, { organizationId: selectedOrganizationId }, { enabled: !!selectedOrganizationId });

  const selectedBoard = boards?.find((board: Board) => board.id === selectedBoardId);

  // Board CRUD operations
  const handleCreateBoard = async (data: { name: string; description?: string; parentBoardId?: string }) => {
    try {
      await createBoard({
        name: data.name,
        description: data.description || '',
        organizationId: selectedOrganizationId,
        parentBoardId: data.parentBoardId,
      });

      refetch();
      return { success: true };
    } catch (error) {
      console.error('Error creating board:', error);
      return { success: false, error };
    }
  };

  const handleUpdateBoard = async (data: {
    id: string;
    name: string;
    description?: string;
    parentBoardId?: string;
  }) => {
    try {
      await updateBoard({
        id: data.id,
        name: data.name,
        description: data.description,
        parentBoardId: data.parentBoardId,
      });

      refetch();
      return { success: true };
    } catch (error) {
      console.error('Error updating board:', error);
      return { success: false, error };
    }
  };

  const handleDeleteBoard = async (boardId: string, boardName: string) => {
    if (!confirm(`Are you sure you want to delete "${boardName}"?`)) {
      return { success: false, cancelled: true };
    }

    try {
      await deleteBoard({ id: boardId });

      if (selectedBoardId === boardId) {
        setSelectedBoardId(undefined);
      }

      refetch();
      return { success: true };
    } catch (error) {
      console.error('Error deleting board:', error);
      return { success: false, error };
    }
  };

  return {
    // Data
    boards,
    selectedBoard,
    selectedBoardId,

    // Loading state
    isLoading,

    // Actions
    setSelectedBoardId,
    handleCreateBoard,
    handleUpdateBoard,
    handleDeleteBoard,
    refetch,
  } as const;
}
