import { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useSocket } from 'wasp/client/webSocket';
import { useBoardData } from './useBoardData';
import { useBoardForm } from './useBoardForm';
import { useBoardAssetsTransform } from './useBoardAssetsTransform';
import { useKanbanLaneChange } from './useKanbanLaneChange';

export type BoardViewType = 'grid' | 'list' | 'kanban';

export function useBoardView() {
  const { boardId } = useParams<{ boardId: string }>();
  const { socket, isConnected } = useSocket();
  const [viewType, setViewType] = useState<BoardViewType>('grid');

  // Board data
  const { boardData, boardAssets, childBoards, refetch } = useBoardData(boardId);

  // Board form handling
  const { form, handleFieldChange } = useBoardForm(boardData, boardId);

  // Asset transformations
  const { assetsData, displayAssets } = useBoardAssetsTransform(boardAssets, childBoards);

  // Kanban functionality
  const handleLaneChange = useKanbanLaneChange(boardAssets, refetch);

  return {
    // Board info
    boardId,
    boardData,
    boardAssets,
    childBoards,

    // View state
    viewType,
    setViewType,

    // Asset data
    assetsData,
    displayAssets,

    // Form handling
    form,
    handleFieldChange,

    // Actions
    handleLaneChange,
    refetch,

    // WebSocket
    socket,
    isConnected,
  } as const;
}
