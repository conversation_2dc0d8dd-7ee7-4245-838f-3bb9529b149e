import { useQuery, getAssets, getBoards, getCloudAssets, getCloudConnections } from 'wasp/client/operations';
import { useOrganizationState } from '../../../../organization/store';
import { AssetLibrarySections } from '../components';

interface UseAssetQueriesParams {
  currentSection: AssetLibrarySections;
  isCurrentSectionCloud: boolean;
  cloudSource?: string;
  debouncedSearchQuery: string;
  selectedTags: string[];
  selectedCollections: string[];
  page: number;
  limit: number;
  currentPageToken?: string;
  currentFolderPath: string;
  getAssetFilter: (section: AssetLibrarySections) => string;
  getAssetSources: (section: AssetLibrarySections) => string[] | undefined;
}

export function useAssetQueries(params: UseAssetQueriesParams) {
  const { selectedOrganizationId } = useOrganizationState();

  const {
    currentSection,
    isCurrentSectionCloud,
    cloudSource,
    debouncedSearchQuery,
    selectedTags,
    selectedCollections,
    page,
    limit,
    currentPageToken,
    currentFolderPath,
    getAssetFilter,
    getAssetSources,
  } = params;

  // Local assets query (for regular sections)
  const {
    data: localAssetsData,
    isLoading: isLoadingLocalAssets,
    refetch: refetchLocalAssets,
  } = useQuery(
    getAssets,
    {
      organizationId: selectedOrganizationId!,
      filter: getAssetFilter(currentSection) as any,
      sources: getAssetSources(currentSection),
      search: debouncedSearchQuery,
      tags: selectedTags,
      collections: selectedCollections,
      page,
      limit,
    },
    {
      enabled: !!selectedOrganizationId && !isCurrentSectionCloud,
    }
  );

  // Cloud assets query (for cloud sections)
  const {
    data: cloudAssetsData,
    isLoading: isLoadingCloudAssets,
    refetch: refetchCloudAssets,
  } = useQuery(
    getCloudAssets,
    {
      organizationId: selectedOrganizationId!,
      source: cloudSource!,
      search: debouncedSearchQuery,
      pageToken: currentPageToken === '' ? undefined : currentPageToken,
      pageSize: limit,
      folderPath: currentFolderPath || undefined,
    },
    {
      enabled: !!selectedOrganizationId && isCurrentSectionCloud && !!cloudSource,
    }
  );

  // Cloud connections data
  const { data: cloudConnectionsData, isLoading: isLoadingConnections } = useQuery(
    getCloudConnections,
    {
      organizationId: selectedOrganizationId!,
    },
    {
      enabled: !!selectedOrganizationId,
    }
  );

  // Boards data
  const { data: boards } = useQuery(getBoards, {
    organizationId: selectedOrganizationId,
  });

  // Combine data based on current section
  const assetsData = isCurrentSectionCloud ? cloudAssetsData : localAssetsData;
  const isLoading = isCurrentSectionCloud ? isLoadingCloudAssets : isLoadingLocalAssets;
  const refetchAssets = isCurrentSectionCloud ? refetchCloudAssets : refetchLocalAssets;

  const cloudConnections = cloudConnectionsData?.connections || [];

  return {
    // Raw data
    localAssetsData,
    cloudAssetsData,
    cloudConnectionsData,
    boards,

    // Combined/computed data
    assetsData,
    cloudConnections,

    // Loading states
    isLoading,
    isLoadingLocalAssets,
    isLoadingCloudAssets,
    isLoadingConnections,

    // Refetch functions
    refetchAssets,
    refetchLocalAssets,
    refetchCloudAssets,
  } as const;
}
