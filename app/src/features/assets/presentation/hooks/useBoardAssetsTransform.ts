import { AssetWithRelations, ensureAssetRelations } from '../types/asset-with-relations';
import type { AssetDisplayItem } from '../../domain/types/board.types';
import type { Board } from '../../domain/types';

interface BoardAsset {
  id: string;
  assetId: number;
  status: string;
  asset: any;
}

export function useBoardAssetsTransform(boardAssets: BoardAsset[], childBoards: Board[] = []) {
  const assetMap = new Map<number, AssetWithRelations>();

  boardAssets?.forEach((ba) => {
    if (ba?.asset) {
      const assetWithStatus = ensureAssetRelations({
        ...ba.asset,
        status: ba.status || 'None',
      });
      assetMap.set(assetWithStatus.id, assetWithStatus);
    }
  });

  const assetsData: AssetWithRelations[] = Array.from(assetMap.values());

  // Transform assets to display items
  const assetDisplayItems: AssetDisplayItem[] = assetsData.map((asset) => ({
    id: asset.id.toString(),
    imageUrl: asset.fileUrl,
    height: asset.height ?? 0,
    width: asset.width ?? 0,
    uploadedAt: asset.uploadedAt,
    status: (asset as any).status ?? 'None',
    details: {
      id: asset.id.toString(),
      name: asset.fileName ?? 'Untitled',
      owner: '',
      tags: asset.tags?.map((t: any) => t.name ?? '') ?? [],
      version: asset.version ?? 1,
      type: asset.fileType ?? 'unknown',
    },
  }));

  // Transform child boards to display items (treated as folders)
  const boardDisplayItems: AssetDisplayItem[] = childBoards.map((board) => ({
    id: board.id,
    imageUrl: '', // could be a placeholder image or leave empty
    height: 0,
    width: 0,
    uploadedAt: new Date(board.createdAt || Date.now()),
    status: 'Board',
    details: {
      id: board.id,
      name: board.name,
      owner: '',
      tags: [],
      version: 1,
      type: 'board',
    },
  }));

  const displayAssets: AssetDisplayItem[] = [...boardDisplayItems, ...assetDisplayItems];

  return { assetsData, displayAssets } as const;
}
