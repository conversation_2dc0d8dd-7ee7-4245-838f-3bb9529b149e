import { useState, useEffect } from 'react';
import { useOrganizationState } from '../../../../organization/store';
import { useAssetSidebar } from './useAssetSidebar';
import { useAssetLibraryState } from './useAssetLibraryState';
import { useCloudAssetUtils } from './useCloudAssetUtils';
import { useCloudPagination } from './useCloudPagination';
import { useAssetQueries } from './useAssetQueries';
import { useAssetTransforms } from './useAssetTransforms';
import { useAssetActions } from './useAssetActions';

export function useAssetLibrary({ limit = 20 }: { limit: number }) {
  const { selectedOrganizationId } = useOrganizationState();
  const [page, setPage] = useState(1);

  // Get sidebar functionality
  const sidebarHook = useAssetSidebar();

  // Additional hooks needed for main content
  const libraryState = useAssetLibraryState();
  const cloudUtils = useCloudAssetUtils();
  const cloudPagination = useCloudPagination();

  // Computed values
  const isCurrentSectionCloud = cloudUtils.isCloudSection(libraryState.currentSection);
  const cloudSource = cloudUtils.getCloudSource(libraryState.currentSection);

  // Full asset queries for main content (with pagination)
  const queries = useAssetQueries({
    currentSection: libraryState.currentSection,
    isCurrentSectionCloud,
    cloudSource,
    debouncedSearchQuery: libraryState.debouncedSearchQuery,
    selectedTags: libraryState.selectedTags,
    selectedCollections: libraryState.selectedCollections,
    page,
    limit,
    currentPageToken: cloudPagination.getCurrentPageToken(),
    currentFolderPath: libraryState.currentFolderPath,
    getAssetFilter: cloudUtils.getAssetFilter,
    getAssetSources: cloudUtils.getAssetSources,
  });

  // Asset transformations for main content
  const transforms = useAssetTransforms({
    assetsData: queries.assetsData,
    isCurrentSectionCloud,
    selectedOrganizationId,
  });

  // Asset actions for main content
  const actions = useAssetActions({
    setCurrentSection: libraryState.setCurrentSection,
    setSearchInput: libraryState.setSearchInput,
    setSelectedTags: libraryState.setSelectedTags,
    setSelectedCollections: libraryState.setSelectedCollections,
    setViewMode: libraryState.setViewMode,
    setCurrentFolderPath: libraryState.setCurrentFolderPath,
    setShowUpload: libraryState.setShowUpload,
    setPage,
    isCurrentSectionCloud,
    refetchAssets: queries.refetchAssets,
    cloudPagination: {
      resetPagination: cloudPagination.resetPagination,
      handleFolderNavigation: cloudPagination.handleFolderNavigation,
    },
  });

  // Handle cloud pagination - update page tokens when new data arrives
  useEffect(() => {
    if (isCurrentSectionCloud && queries.cloudAssetsData?.nextPageToken) {
      cloudPagination.updatePageTokens(queries.cloudAssetsData.nextPageToken);
    }
  }, [queries.cloudAssetsData?.nextPageToken, isCurrentSectionCloud, cloudPagination.updatePageTokens]);

  // Computed pagination values
  const canGoToNextCloudPage = cloudPagination.canGoToNextPage(transforms.hasMoreCloudAssets);
  const canGoToPrevCloudPage = cloudPagination.canGoToPrevPage();

  // Cloud pagination handlers
  const handleCloudNextPage = () => {
    if (canGoToNextCloudPage) {
      cloudPagination.handleNextPage();
    }
  };

  const handleCloudPrevPage = () => {
    if (canGoToPrevCloudPage) {
      cloudPagination.handlePrevPage();
    }
  };

  return {
    // Inherit all sidebar functionality
    ...sidebarHook,

    // Additional state for main content
    searchInput: libraryState.searchInput,
    currentFolderPath: libraryState.currentFolderPath,
    breadcrumbs: libraryState.breadcrumbs,
    debouncedSearchQuery: libraryState.debouncedSearchQuery,
    page,
    limit,

    // Computed values for main content
    getSectionTitle: () => cloudUtils.getSectionTitle(libraryState.currentSection),
    getEmptyMessage: () => cloudUtils.getEmptyMessage(libraryState.currentSection, libraryState.debouncedSearchQuery),
    isCurrentSectionCloud,

    // Main content data
    assets: transforms.assets,
    totalAssets: transforms.totalAssets,
    hasMoreCloudAssets: transforms.hasMoreCloudAssets,

    // Loading states
    isLoading: queries.isLoading,
    isNavigatingFolder: cloudPagination.isNavigatingFolder,

    // Pagination
    canGoToPrevCloudPage,
    canGoToNextCloudPage,
    currentCloudPageIndex: cloudPagination.currentCloudPageIndex,

    // Additional actions for main content
    handleAssetClick: actions.handleAssetClick,
    handleAssetDelete: actions.handleAssetDelete,
    handleUpdate: actions.handleUpdate,
    handleCloudPrevPage,
    handleCloudNextPage,
    handleNavigateToFolder: actions.handleNavigateToFolder,
    handleNavigateUp: (currentPath: string) => actions.handleNavigateUp(currentPath),
    refetchAssets: queries.refetchAssets,
    setPage,
  } as const;
}
