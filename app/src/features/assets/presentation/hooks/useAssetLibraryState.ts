import { useQueryState, parseAsStringEnum, parseAsString, parseAsArrayOf } from 'nuqs';
import { useDebounce } from 'use-debounce';
import { AssetLibrarySections } from '../components';

export function useAssetLibraryState() {
  // URL state management using nuqs
  const [currentSection, setCurrentSection] = useQueryState(
    'section',
    parseAsStringEnum(Object.values(AssetLibrarySections)).withDefault(AssetLibrarySections.ALL_ASSETS)
  );

  const [searchInput, setSearchInput] = useQueryState('search', parseAsString.withDefault(''));

  const [selectedTags, setSelectedTags] = useQueryState('tags', parseAsArrayOf(parseAsString).withDefault([]));

  const [selectedCollections, setSelectedCollections] = useQueryState(
    'collections',
    parseAsArrayOf(parseAsString).withDefault([])
  );

  const [viewMode, setViewMode] = useQueryState('view', parseAsStringEnum(['grid', 'list']).withDefault('grid'));

  const [currentFolderPath, setCurrentFolderPath] = useQueryState('folder', parseAsString.withDefault(''));

  const [, setShowUpload] = useQueryState('upload-modal', {
    defaultValue: '',
    shallow: true,
  });

  // Debounce search input to avoid excessive API calls
  const [debouncedSearchQuery] = useDebounce(searchInput, 500);

  // Build breadcrumb navigation
  const breadcrumbs = currentFolderPath ? currentFolderPath.split('/').filter(Boolean) : [];

  return {
    // State
    currentSection,
    searchInput,
    selectedTags,
    selectedCollections,
    viewMode,
    currentFolderPath,
    debouncedSearchQuery,
    breadcrumbs,

    // Setters
    setCurrentSection,
    setSearchInput,
    setSelectedTags,
    setSelectedCollections,
    setViewMode,
    setCurrentFolderPath,
    setShowUpload,
  } as const;
}
