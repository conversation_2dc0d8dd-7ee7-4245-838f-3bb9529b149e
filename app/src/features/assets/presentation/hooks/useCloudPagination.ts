import { useState, useEffect, useCallback } from 'react';

export function useCloudPagination() {
  const [cloudPageTokens, setCloudPageTokens] = useState<string[]>(['']);
  const [currentCloudPageIndex, setCurrentCloudPageIndex] = useState(0);
  const [isNavigatingFolder, setIsNavigatingFolder] = useState(false);

  // Handle cloud pagination - update page tokens when new data arrives
  const updatePageTokens = useCallback((nextPageToken?: string) => {
    if (nextPageToken) {
      setCloudPageTokens((prev) => {
        const newTokens = [...prev];
        // Only add the next token if it's not already in the array
        if (!newTokens.includes(nextPageToken)) {
          newTokens.push(nextPageToken);
        }
        return newTokens;
      });
    }
  }, []);

  const resetPagination = useCallback(() => {
    setCloudPageTokens(['']);
    setCurrentCloudPageIndex(0);
  }, []);

  const getCurrentPageToken = useCallback(() => {
    return cloudPageTokens[currentCloudPageIndex] || undefined;
  }, [cloudPageTokens, currentCloudPageIndex]);

  const canGoToNextPage = useCallback(
    (hasMore: boolean) => {
      return hasMore && currentCloudPageIndex + 1 < cloudPageTokens.length;
    },
    [currentCloudPageIndex, cloudPageTokens.length]
  );

  const canGoToPrevPage = useCallback(() => {
    return currentCloudPageIndex > 0;
  }, [currentCloudPageIndex]);

  const handleNextPage = useCallback(() => {
    setCurrentCloudPageIndex((prev) => prev + 1);
  }, []);

  const handlePrevPage = useCallback(() => {
    setCurrentCloudPageIndex((prev) => prev - 1);
  }, []);

  const handleFolderNavigation = useCallback(
    async (callback: () => Promise<void> | void) => {
      setIsNavigatingFolder(true);
      try {
        await callback();
        resetPagination();
      } finally {
        setTimeout(() => setIsNavigatingFolder(false), 300);
      }
    },
    [resetPagination]
  );

  return {
    // State
    cloudPageTokens,
    currentCloudPageIndex,
    isNavigatingFolder,

    // Computed
    getCurrentPageToken,
    canGoToNextPage,
    canGoToPrevPage,

    // Actions
    updatePageTokens,
    resetPagination,
    handleNextPage,
    handlePrevPage,
    handleFolderNavigation,
    setIsNavigatingFolder,
  } as const;
}
