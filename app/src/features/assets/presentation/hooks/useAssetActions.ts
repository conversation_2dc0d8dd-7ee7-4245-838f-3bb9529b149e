import { useCallback } from 'react';
import { Asset } from '../../domain/types';
import { AssetLibrarySections } from '../components';
import { useAssetInteractions } from './useAssetInteractions';

interface UseAssetActionsParams {
  setCurrentSection: (section: AssetLibrarySections) => void;
  setSearchInput: (search: string) => void;
  setSelectedTags: (tags: string[]) => void;
  setSelectedCollections: (collections: string[]) => void;
  setViewMode: (mode: 'grid' | 'list') => void;
  setCurrentFolderPath: (path: string) => void;
  setShowUpload: (value: string) => void;
  setPage: (page: number | ((prev: number) => number)) => void;
  isCurrentSectionCloud: boolean;
  refetchAssets: () => void;
  cloudPagination: {
    resetPagination: () => void;
    handleFolderNavigation: (callback: () => Promise<void> | void) => Promise<void>;
  };
}

export function useAssetActions(params: UseAssetActionsParams) {
  const {
    setCurrentSection,
    setSearchInput,
    setSelectedTags,
    setSelectedCollections,
    setViewMode,
    setCurrentFolderPath,
    setShowUpload,
    setPage,
    isCurrentSectionCloud,
    refetchAssets,
    cloudPagination,
  } = params;

  // Use common asset interactions
  const assetInteractions = useAssetInteractions();

  // Event handlers for library state management
  const handleSectionChange = useCallback(
    (section: AssetLibrarySections) => {
      setCurrentSection(section);
    },
    [setCurrentSection]
  );

  const handleSearchChange = useCallback(
    (search: string) => {
      setSearchInput(search);
      setPage(1);
      cloudPagination.resetPagination();
    },
    [setSearchInput, setPage, cloudPagination]
  );

  const handleTagFilter = useCallback(
    (tags: string[]) => {
      setSelectedTags(tags);
      setPage(1);
    },
    [setSelectedTags, setPage]
  );

  const handleCollectionFilter = useCallback(
    (collections: string[]) => {
      setSelectedCollections(collections);
      setPage(1);
    },
    [setSelectedCollections, setPage]
  );

  const handleViewModeChange = useCallback(
    (mode: 'grid' | 'list') => {
      setViewMode(mode);
    },
    [setViewMode]
  );

  const handleUploadClick = useCallback(() => {
    setShowUpload('open');
  }, [setShowUpload]);

  // Custom asset click handler that includes folder navigation logic
  const handleAssetClick = useCallback(
    async (asset: Asset, event?: React.MouseEvent) => {
      // Handle folder navigation for cloud assets
      if (asset.fileType === 'folder' && isCurrentSectionCloud) {
        await cloudPagination.handleFolderNavigation(async () => {
          const folderAsset = asset as any;
          const newPath = folderAsset?.id || folderAsset.name;
          setCurrentFolderPath(newPath);
        });
        return;
      }

      // Use common asset click handler for regular assets
      assetInteractions.handleAssetClick(asset.id, event);
    },
    [isCurrentSectionCloud, setCurrentFolderPath, cloudPagination, assetInteractions]
  );

  // Custom asset delete handler that refetches after deletion
  const handleAssetDelete = useCallback(
    async (asset: Asset) => {
      const result = await assetInteractions.handleAssetDelete(asset.id, asset.fileName);
      if (result.success) {
        await refetchAssets();
      }
      return result;
    },
    [assetInteractions, refetchAssets]
  );

  const handleUpdate = useCallback(async () => {
    await refetchAssets();
  }, [refetchAssets]);

  const handleNavigateToFolder = useCallback(
    async (folderPath: string) => {
      await cloudPagination.handleFolderNavigation(() => {
        setCurrentFolderPath(folderPath);
      });
    },
    [setCurrentFolderPath, cloudPagination]
  );

  const handleNavigateUp = useCallback(
    async (currentFolderPath: string) => {
      if (!currentFolderPath) return;

      await cloudPagination.handleFolderNavigation(() => {
        const pathParts = currentFolderPath.split('/').filter(Boolean);
        pathParts.pop(); // Remove last folder
        const parentPath = pathParts.join('/');
        setCurrentFolderPath(parentPath);
      });
    },
    [setCurrentFolderPath, cloudPagination]
  );

  return {
    // Library state actions
    handleSectionChange,
    handleSearchChange,
    handleTagFilter,
    handleCollectionFilter,
    handleViewModeChange,
    handleUploadClick,

    // Asset-specific actions (with library-specific logic)
    handleAssetClick,
    handleAssetDelete,
    handleUpdate,

    // Folder navigation actions
    handleNavigateToFolder,
    handleNavigateUp,

    // Common asset interactions (not overridden)
    handleAssetEdit: assetInteractions.handleAssetEdit,
    handleAssetShare: assetInteractions.handleAssetShare,
    handleAssetDownload: assetInteractions.handleAssetDownload,
    handleBulkAssetDelete: assetInteractions.handleBulkAssetDelete,
  } as const;
}
