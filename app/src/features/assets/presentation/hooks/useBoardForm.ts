import { useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useDebouncedCallback } from 'use-debounce';
import { toast } from 'react-hot-toast';
import { updateBoard } from 'wasp/client/operations';
import { useOrganizationState } from '../../../../organization/store';

const formSchema = z.object({
  name: z.string().min(1, 'Board name is required'),
  description: z.string().optional(),
});
export type FormValues = z.infer<typeof formSchema>;

export function useBoardForm(boardData: any, boardId?: string) {
  const { selectedOrganizationId } = useOrganizationState();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: { name: '', description: '' },
  });

  // populate when board data changes
  useEffect(() => {
    if (boardData) {
      form.reset({
        name: boardData.name || '',
        description: boardData.description || '',
      });
    }
  }, [boardData, form]);

  // autosave
  const debouncedSave = useDebouncedCallback(async (values: FormValues) => {
    if (!boardId || !selectedOrganizationId) return;
    try {
      await updateBoard({ id: boardId, ...values });
      toast.success('Your changes have been automatically saved', { duration: 2000 });
    } catch (err) {
      console.error(err);
      toast.error('Please try again later');
    }
  }, 1000);

  const handleFieldChange = useCallback(
    (field: any, value: string | null) => {
      const newValue = value || '';
      field.onChange(newValue);
      debouncedSave({ ...form.getValues(), [field.name]: newValue });
    },
    [form, debouncedSave]
  );

  return { form, handleFieldChange } as const;
}
