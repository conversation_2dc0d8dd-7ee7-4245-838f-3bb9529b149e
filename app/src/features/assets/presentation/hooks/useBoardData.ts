import { useQuery } from 'wasp/client/operations';
import { getBoard } from 'wasp/client/operations';

export function useBoardData(boardId?: string) {

  const {
    data: boardData,
    isLoading,
    error,
    refetch,
  } = useQuery(
    getBoard,
    {
      id: boardId!,
    },
    {
      enabled: !!boardId,
    }
  );

  const boardAssets = (boardData as any)?.assets || [];
  const childBoards = (boardData as any)?.childBoards || [];

  return { boardData, boardAssets, childBoards, isLoading, error, refetch } as const;
}
