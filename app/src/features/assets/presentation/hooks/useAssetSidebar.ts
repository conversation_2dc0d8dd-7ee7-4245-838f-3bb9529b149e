import { useAssetLibraryState } from './useAssetLibraryState';
import { useCloudAssetUtils } from './useCloudAssetUtils';
import { useAssetQueries } from './useAssetQueries';
import { useAssetTransforms } from './useAssetTransforms';
import { useOrganizationState } from '../../../../organization/store';

export function useAssetSidebar() {
  const { selectedOrganizationId } = useOrganizationState();

  // Core sidebar state
  const libraryState = useAssetLibraryState();
  const cloudUtils = useCloudAssetUtils();

  // Computed values for sidebar
  const isCurrentSectionCloud = cloudUtils.isCloudSection(libraryState.currentSection);
  const cloudSource = cloudUtils.getCloudSource(libraryState.currentSection);

  // Only query data needed for sidebar (tags, collections, boards, connections)
  const queries = useAssetQueries({
    currentSection: libraryState.currentSection,
    isCurrentSectionCloud,
    cloudSource,
    debouncedSearchQuery: libraryState.debouncedSearchQuery,
    selectedTags: libraryState.selectedTags,
    selectedCollections: libraryState.selectedCollections,
    page: 1, // Sidebar doesn't need pagination
    limit: 1, // Minimal data for sidebar
    currentPageToken: undefined,
    currentFolderPath: libraryState.currentFolderPath,
    getAssetFilter: cloudUtils.getAssetFilter,
    getAssetSources: cloudUtils.getAssetSources,
  });

  // Transform only sidebar-relevant data
  const transforms = useAssetTransforms({
    assetsData: queries.assetsData,
    isCurrentSectionCloud,
    selectedOrganizationId,
  });

  // Sidebar-specific actions
  const handleUploadClick = () => {
    libraryState.setShowUpload('open');
  };

  const handleSectionChange = (section: string) => {
    libraryState.setCurrentSection(section as any);
    libraryState.setCurrentFolderPath('');
    libraryState.setSearchInput('');
    libraryState.setSelectedTags([]);
    libraryState.setSelectedCollections([]);
  };

  const handleSearchChange = (value: string) => {
    libraryState.setSearchInput(value);
  };

  const handleTagFilter = (tags: string[]) => {
    libraryState.setSelectedTags(tags);
  };

  const handleCollectionFilter = (collections: string[]) => {
    libraryState.setSelectedCollections(collections);
  };

  const handleViewModeChange = (mode: 'grid' | 'list') => {
    libraryState.setViewMode(mode);
  };

  return {
    // Sidebar state
    currentSection: libraryState.currentSection,
    selectedTags: libraryState.selectedTags,
    selectedCollections: libraryState.selectedCollections,
    viewMode: libraryState.viewMode,

    // Sidebar data
    collections: transforms.collections,
    tags: transforms.tags,
    boards: queries.boards,
    cloudConnections: queries.cloudConnections,

    // Sidebar actions
    handleUploadClick,
    handleSectionChange,
    handleSearchChange,
    handleTagFilter,
    handleCollectionFilter,
    handleViewModeChange,
  } as const;
}
