import { useMemo } from 'react';
import { useState } from 'react';
import { AssetCollection, AssetTag } from '../../domain/types';
import { AssetWithRelations, ensureAssetRelations } from '../types/asset-with-relations';

interface UseAssetTransformsParams {
  assetsData: any;
  isCurrentSectionCloud: boolean;
  selectedOrganizationId?: string;
}

export function useAssetTransforms(params: UseAssetTransformsParams) {
  const { assetsData, isCurrentSectionCloud, selectedOrganizationId } = params;

  // Mock data for collections and tags - in real app these would come from separate queries
  const [collections] = useState<AssetCollection[]>([]);
  const [tags] = useState<AssetTag[]>([]);

  // Transform the assets to ensure all relationships are present
  const assets = useMemo(() => {
    const rawAssets = isCurrentSectionCloud
      ? (assetsData as any)?.files || [] // Cloud assets use 'files' property
      : (assetsData as any)?.assets || []; // Local assets use 'assets' property

    return rawAssets.map((asset: any) => {
      if (isCurrentSectionCloud) {
        // Transform cloud asset to match local asset structure
        const fileUrl = asset.thumbnailUrl || asset.webViewUrl;

        return {
          id: asset.id,
          fileName: asset.name,
          fileUrl: fileUrl,
          fileType: asset.mimeType,
          fileSize: asset.size,
          uploadedAt: asset.lastModified,
          source: asset.source,
          cloudFileId: asset.id,
          cloudThumbnailUrl: asset.thumbnailUrl,
          cloudWebViewUrl: asset.webViewUrl,
          cloudPath: asset.path,
          lastModified: asset.lastModified,
          // Default values for cloud assets to match AssetWithRelations
          category: 'Cloud',
          tags: [],
          collections: [],
          analytics: null,
          isGenerated: false,
          isVariation: false,
          isCover: true,
          deletedAt: null,
          organizationId: selectedOrganizationId,
          userId: null,
          width: null,
          height: null,
          photographyModelId: null,
          generationTaskId: null,
          originalAssetId: null,
          parentAssetId: null,
          variationSettings: null,
          variations: [],
          originalAsset: null,
        } as unknown as AssetWithRelations;
      } else {
        // Local assets - ensure relations are present
        return ensureAssetRelations(asset) as AssetWithRelations;
      }
    });
  }, [assetsData, isCurrentSectionCloud, selectedOrganizationId]);

  const totalAssets = useMemo(() => {
    return isCurrentSectionCloud
      ? (assetsData as any)?.files?.length || 0 // Current page count for cloud assets
      : (assetsData as any)?.total || 0; // Total count for local assets
  }, [assetsData, isCurrentSectionCloud]);

  const hasMoreCloudAssets = useMemo(() => {
    return isCurrentSectionCloud && (assetsData as any)?.hasMore;
  }, [assetsData, isCurrentSectionCloud]);

  return {
    assets,
    collections,
    tags,
    totalAssets,
    hasMoreCloudAssets,
  } as const;
}
