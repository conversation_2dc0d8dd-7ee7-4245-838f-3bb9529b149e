import { useNavigate } from 'react-router-dom';
import { deleteAsset } from 'wasp/client/operations';

export function useAssetInteractions() {
  const navigate = useNavigate();

  // Common asset actions
  const handleAssetClick = (assetId: number, event?: React.MouseEvent) => {
    if (event?.ctrlKey || event?.metaKey) {
      // Open in new tab/window
      window.open(`/asset/${assetId}`, '_blank');
    } else {
      navigate(`/asset/${assetId}`);
    }
  };

  const handleAssetDelete = async (assetId: number, assetName?: string) => {
    const confirmMessage = assetName
      ? `Are you sure you want to delete "${assetName}"?`
      : 'Are you sure you want to delete this asset?';

    if (!confirm(confirmMessage)) {
      return { success: false, cancelled: true };
    }

    try {
      await deleteAsset({ id: assetId });
      return { success: true };
    } catch (error) {
      console.error('Error deleting asset:', error);
      return { success: false, error };
    }
  };

  const handleAssetEdit = (assetId: number) => {
    navigate(`/asset/${assetId}/edit`);
  };

  const handleAssetShare = (assetId: number) => {
    navigate(`/asset/${assetId}/share`);
  };

  const handleAssetDownload = (assetUrl: string, fileName?: string) => {
    const link = document.createElement('a');
    link.href = assetUrl;
    link.download = fileName || 'asset';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleBulkAssetDelete = async (assetIds: number[]) => {
    if (!confirm(`Are you sure you want to delete ${assetIds.length} assets?`)) {
      return { success: false, cancelled: true };
    }

    try {
      // Delete assets in parallel
      await Promise.all(assetIds.map((id) => deleteAsset({ id })));
      return { success: true };
    } catch (error) {
      console.error('Error deleting assets:', error);
      return { success: false, error };
    }
  };

  return {
    // Single asset actions
    handleAssetClick,
    handleAssetDelete,
    handleAssetEdit,
    handleAssetShare,
    handleAssetDownload,

    // Bulk actions
    handleBulkAssetDelete,
  } as const;
}
