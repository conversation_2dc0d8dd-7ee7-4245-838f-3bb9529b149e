import type { Asset, AssetTag, AssetCollection, PhotographyModel } from 'wasp/entities';

// Extended Asset type that includes all the relationships
// This is what we get from queries that include related data
export interface AssetWithRelations extends Asset {
  tags?: AssetTag[];
  collections?: AssetCollection[];
  variations?: Asset[];
  photographyModel?: PhotographyModel | null;
  analytics?: {
    id: string;
    assetId: number;
    views: number;
    downloads: number;
    shares: number;
    lastViewed: Date | null;
    lastDownload: Date | null;
    lastShared: Date | null;
    createdAt: Date;
    updatedAt: Date;
  } | null;
}

// Type for asset queries that include relationships
export interface AssetQueryResult {
  assets: AssetWithRelations[];
  total: number;
}

// Helper function to ensure an asset has all required relationship fields
export function ensureAssetRelations(asset: any): AssetWithRelations {
  return {
    ...asset,
    tags: asset.tags || [],
    collections: asset.collections || [],
    variations: asset.variations || [],
    photographyModel: asset.photographyModel || null,
    analytics: asset.analytics || null,
  };
}
