import React, { memo } from 'react';
import { Trash2, Layers, Tag, Folder, Calendar, FolderOpen, ChevronRight } from 'lucide-react';
import { Asset, AssetCollection } from '../../../domain/types';
import { AssetWithRelations } from '../../types/asset-with-relations';
import { TagEditor } from '../tag-editor/TagEditor';
import { CollectionPicker } from '../collection-picker/CollectionPicker';
import { GoogleDriveImage } from '../GoogleDriveImage';

// Type guard function
const isGeneratedAsset = (asset: AssetWithRelations): boolean => {
  return asset.isGenerated === true;
};

type Props = {
  asset: AssetWithRelations;
  onClick?: (asset: AssetWithRelations) => void;
  onDelete?: (asset: AssetWithRelations) => void;
  onUpdate?: () => void;
  allTags: string[];
  allCollections: AssetCollection[];
  className?: string;
  showDetails?: boolean;
};

export const AssetListItem = ({
  asset,
  onClick,
  onDelete,
  onUpdate,
  allTags,
  allCollections,
  className = '',
  showDetails = true,
}: Props) => {
  const handleClick = () => {
    if (onClick) {
      onClick(asset);
    }
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDelete) {
      onDelete(asset);
    }
  };

  const isGenerated = isGeneratedAsset(asset);
  const fileName = isGenerated ? (asset as any).prompt : asset.fileName || 'Untitled';
  const category = isGenerated ? 'Generated' : asset.category;
  const isFolder = asset.fileType === 'folder';
  const cloudProvider = asset.source;

  const isVariation = asset.isVariation;
  const hasVariations = asset.variations && asset.variations.length > 0;
  const variationCount = asset.variations?.length ?? 0;

  // Get dynamic folder styling based on cloud provider
  const getFolderDisplayInfo = () => {
    if (!isFolder) return null;

    switch (cloudProvider) {
      case 'onedrive':
        return {
          icon: FolderOpen,
          color: 'text-blue-500 dark:text-blue-400',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          label: 'OneDrive Folder',
        };
      case 'dropbox':
        return {
          icon: FolderOpen,
          color: 'text-blue-600 dark:text-blue-400',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          label: 'Dropbox Folder',
        };
      default:
        return {
          icon: FolderOpen,
          color: 'text-[#676D50] dark:text-[#A6884C]',
          bgColor: 'bg-[#676D50]/10 dark:bg-[#A6884C]/10',
          label: 'Folder',
        };
    }
  };

  const folderInfo = getFolderDisplayInfo();

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(new Date(date));
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  return (
    <div
      className={`relative group bg-white dark:bg-gray-800 border border-[#B5B178]/20 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer ${className}`}
      onClick={handleClick}
    >
      <div className='flex items-center gap-4'>
        {/* Thumbnail */}
        <div
          className={`w-16 h-16 ${folderInfo ? folderInfo.bgColor : 'bg-gray-100 dark:bg-gray-700'} rounded overflow-hidden flex-shrink-0 flex items-center justify-center`}
        >
          {isFolder && folderInfo ? (
            <div className='flex items-center gap-1'>
              <folderInfo.icon size={28} className={folderInfo.color} />
              <ChevronRight size={16} className={`${folderInfo.color} opacity-60`} />
            </div>
          ) : asset.fileUrl?.startsWith('google-drive:') ? (
            <GoogleDriveImage
              fileId={asset.fileUrl.replace('google-drive:', '')}
              alt={fileName || 'Asset'}
              className='w-full h-full object-cover'
              loading='lazy'
              size='small'
            />
          ) : (
            <img src={asset.fileUrl} alt={fileName || 'Asset'} className='w-full h-full object-cover' loading='lazy' />
          )}
        </div>

        {/* Content */}
        <div className='flex-grow min-w-0'>
          <div className='flex items-center justify-between'>
            <h3 className='font-medium text-[#1F2419] dark:text-white truncate text-sm'>{fileName}</h3>
            <div className='flex items-center gap-3 text-xs'>
              {/* Status badges */}
              <div className='flex gap-2'>
                {isGenerated && (
                  <span className='bg-[#676D50]/10 dark:bg-[#A6884C]/10 text-[#676D50] dark:text-[#A6884C] px-2 py-0.5 rounded-full'>
                    AI Generated
                  </span>
                )}
                {isVariation && (
                  <span className='bg-[#676D50]/10 dark:bg-[#A6884C]/10 text-[#676D50] dark:text-[#A6884C] px-2 py-0.5 rounded-full'>
                    Variation
                  </span>
                )}
                {hasVariations && (
                  <span className='bg-[#676D50]/10 dark:bg-[#A6884C]/10 text-[#676D50] dark:text-[#A6884C] px-2 py-0.5 rounded-full flex items-center gap-1'>
                    <Layers size={12} />
                    {variationCount}
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className='flex items-center gap-4 mt-1'>
            <span className={`text-xs ${folderInfo ? folderInfo.color : 'text-[#676D50] dark:text-[#A6884C]'}`}>
              {isFolder && folderInfo ? folderInfo.label : category}
            </span>
            {asset.photographyModel && (
              <span className='text-xs text-gray-500 dark:text-gray-400'>{asset.photographyModel.name}</span>
            )}
            {showDetails && (
              <>
                {asset.width && asset.height && (
                  <span className='text-xs text-gray-500 dark:text-gray-400'>
                    {asset.width} × {asset.height}
                  </span>
                )}
                <span className='text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1'>
                  <Calendar size={10} />
                  {formatDate(asset.uploadedAt)}
                </span>
              </>
            )}
          </div>

          {/* Tags and Collections */}
          <div className='mt-2 flex items-center gap-4 text-xs text-[#676D50] dark:text-[#A6884C]'>
            {asset.tags && asset.tags.length > 0 && (
              <div className='flex items-center gap-1'>
                <Tag size={12} className='text-[#676D50] dark:text-[#A6884C]' />
                <span>
                  {asset.tags.length} tag{asset.tags.length !== 1 ? 's' : ''}
                </span>
              </div>
            )}
            {asset.collections && asset.collections.length > 0 && (
              <div className='flex items-center gap-1'>
                <Folder size={12} className='text-[#676D50] dark:text-[#A6884C]' />
                <span>
                  {asset.collections.length} collection{asset.collections.length !== 1 ? 's' : ''}
                </span>
              </div>
            )}
          </div>

          {/* Tags preview */}
          {asset.tags && asset.tags.length > 0 && showDetails && (
            <div className='mt-2 flex flex-wrap gap-1'>
              {asset.tags.slice(0, 3).map((tag) => (
                <span
                  key={tag.id}
                  className='inline-flex items-center px-2 py-0.5 rounded text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                  style={{ backgroundColor: tag.color || undefined }}
                >
                  {tag.name}
                </span>
              ))}
              {asset.tags.length > 3 && (
                <span className='text-xs text-gray-500 dark:text-gray-400'>+{asset.tags.length - 3} more</span>
              )}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className='flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity'>
          <div onClick={(e) => e.stopPropagation()} className='flex gap-1'>
            <TagEditor assetId={asset.id} tags={asset.tags || []} allTags={allTags} onUpdate={onUpdate} />
            <CollectionPicker
              assetId={asset.id}
              collections={asset.collections || []}
              allCollections={allCollections}
              onUpdate={onUpdate}
            />
          </div>
          {onDelete && (
            <button
              onClick={handleDelete}
              className='text-red-500 dark:text-red-400 hover:text-red-600 dark:hover:text-red-500 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors'
              title='Delete asset'
            >
              <Trash2 size={16} />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default memo(AssetListItem);
