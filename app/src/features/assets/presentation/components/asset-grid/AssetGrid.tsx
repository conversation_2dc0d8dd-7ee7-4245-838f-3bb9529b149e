import React, { memo } from "react";
import { AssetCollection } from "../../../domain/types";
import { AssetWithRelations } from "../../types/asset-with-relations";
import { AssetDisplayItem } from "../../../domain/types/board.types";
import { AssetCard } from "../asset-card";
import { AssetListItem } from "../asset-list-item";

// Union type to support both asset types
type GridAsset = AssetWithRelations | AssetDisplayItem;

// Type guard functions
const isAssetDisplayItem = (asset: GridAsset): asset is AssetDisplayItem => {
	return "details" in asset && typeof asset.details === "object";
};

interface AssetGridProps {
	assets: GridAsset[];
	mode?: "grid" | "list";
	onAssetClick?: (asset: GridAsset) => void;
	onAssetDelete?: (asset: GridAsset) => void;
	onUpdate?: () => void;
	allTags?: string[];
	allCollections?: AssetCollection[];
	className?: string;
	gridCols?: {
		default?: number;
		sm?: number;
		md?: number;
		lg?: number;
		xl?: number;
	};
	loading?: boolean;
	emptyMessage?: string;
}

export const AssetGrid = memo(function AssetGrid({
	assets,
	mode = "grid",
	onAssetClick,
	onAssetDelete,
	onUpdate,
	allTags = [],
	allCollections = [],
	className = "",
	gridCols = {
		default: 2,
		sm: 3,
		md: 4,
		lg: 5,
		xl: 6,
	},
	loading = false,
	emptyMessage = "No assets found",
}: AssetGridProps) {
	const getAssetKey = (asset: GridAsset): string => {
		if (isAssetDisplayItem(asset)) {
			return asset.id;
		}
		return asset.id.toString();
	};

	const getGridClasses = () => {
		const classes = ["grid", "gap-4"];

		if (gridCols.default) classes.push(`grid-cols-${gridCols.default}`);
		if (gridCols.sm) classes.push(`sm:grid-cols-${gridCols.sm}`);
		if (gridCols.md) classes.push(`md:grid-cols-${gridCols.md}`);
		if (gridCols.lg) classes.push(`lg:grid-cols-${gridCols.lg}`);
		if (gridCols.xl) classes.push(`xl:grid-cols-${gridCols.xl}`);

		return classes.join(" ");
	};

	if (loading) {
		return (
			<div className={`${className}`}>
				{mode === "grid" ? (
					<div className={getGridClasses()}>
						{Array.from({ length: 12 }).map((_, index) => (
							<div
								key={`loading-${index}`}
								className="aspect-square bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"
							/>
						))}
					</div>
				) : (
					<div className="space-y-2">
						{Array.from({ length: 8 }).map((_, index) => (
							<div
								key={`loading-list-${index}`}
								className="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"
							/>
						))}
					</div>
				)}
			</div>
		);
	}

	if (assets.length === 0) {
		return (
			<div
				className={`flex flex-col items-center justify-center py-12 text-center ${className}`}
			>
				<div className="text-gray-400 dark:text-gray-500 mb-2">
					<svg
						className="w-16 h-16 mx-auto"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={1}
							d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
						/>
					</svg>
				</div>
				<h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">
					{emptyMessage}
				</h3>
				<p className="text-gray-500 dark:text-gray-400">
					Upload your first asset to get started
				</p>
			</div>
		);
	}

	return (
		<div className={className}>
			{mode === "grid" ? (
				<div className={getGridClasses()}>
					{assets.map((asset) => {
						const key = getAssetKey(asset);

						if (isAssetDisplayItem(asset)) {
							// Handle AssetDisplayItem
							if (asset.details.type === "board") {
								// Render board item with folder icon
								return (
									<div
										key={key}
										className="group relative cursor-pointer"
										onClick={() => onAssetClick?.(asset)}
										onKeyDown={(e) => {
											if (e.key === "Enter" || e.key === " ") {
												e.preventDefault();
												onAssetClick?.(asset);
											}
										}}
										role="button"
										tabIndex={0}
										aria-label={`Open board ${asset.details.name}`}
									>
										<div className="aspect-square bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow">
											<div className="w-full h-[120px] bg-blue-50 flex items-center justify-center">
												<svg
													className="w-12 h-12 text-blue-500"
													fill="none"
													stroke="currentColor"
													viewBox="0 0 24 24"
													aria-hidden="true"
												>
													<path
														strokeLinecap="round"
														strokeLinejoin="round"
														strokeWidth={2}
														d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0a2 2 0 002-2h10a2 2 0 012 2v0"
													/>
												</svg>
											</div>
										</div>

										{/* Board info overlay */}
										<div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-3 rounded-b-lg">
											<h3 className="text-white text-sm font-medium truncate">
												{asset.details.name}
											</h3>
											<div className="flex items-center justify-between mt-1">
												<span className="text-xs px-2 py-0.5 rounded bg-blue-500/80 text-white">
													Board
												</span>
											</div>
										</div>
									</div>
								);
							} else {
								// For AssetDisplayItem assets, create a simple card instead of converting
								return (
									<div
										key={key}
										className="group relative cursor-pointer"
										onClick={() => onAssetClick?.(asset)}
										onKeyDown={(e) => {
											if (e.key === "Enter" || e.key === " ") {
												e.preventDefault();
												onAssetClick?.(asset);
											}
										}}
										role="button"
										tabIndex={0}
										aria-label={`Open asset ${asset.details.name}`}
									>
										<div className="aspect-square bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow">
											{asset.imageUrl ? (
												<img
													src={asset.imageUrl}
													alt={asset.details.name}
													className="w-full h-full object-cover"
												/>
											) : (
												<div className="w-full h-full bg-gray-100 flex items-center justify-center">
													<svg
														className="w-12 h-12 text-gray-400"
														fill="none"
														stroke="currentColor"
														viewBox="0 0 24 24"
														aria-hidden="true"
													>
														<path
															strokeLinecap="round"
															strokeLinejoin="round"
															strokeWidth={1}
															d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
														/>
													</svg>
												</div>
											)}
										</div>

										{/* Asset info overlay */}
										<div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-3 rounded-b-lg">
											<h3 className="text-white text-sm font-medium truncate">
												{asset.details.name}
											</h3>
											<div className="flex items-center justify-between mt-1">
												<span className="text-xs px-2 py-0.5 rounded bg-white/20 text-white">
													{asset.details.type}
												</span>
												{asset.width && asset.height && (
													<span className="text-xs text-white/80">
														{asset.width}×{asset.height}
													</span>
												)}
											</div>
										</div>
									</div>
								);
							}
						} else {
							// Handle AssetWithRelations (traditional asset)
							return (
								<AssetCard
									key={key}
									asset={asset}
									onClick={() => onAssetClick?.(asset)}
									onDelete={
										onAssetDelete ? () => onAssetDelete(asset) : undefined
									}
									onUpdate={onUpdate}
									allTags={allTags}
									allCollections={allCollections}
								/>
							);
						}
					})}
				</div>
			) : (
				<div className="space-y-2">
					{assets.map((asset) => {
						const key = getAssetKey(asset);

						if (isAssetDisplayItem(asset)) {
							// Handle AssetDisplayItem in list mode
							if (asset.details.type === "board") {
								// Render board item in list mode
								return (
									<div
										key={key}
										className="flex items-center gap-4 p-4 bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
										onClick={() => onAssetClick?.(asset)}
										onKeyDown={(e) => {
											if (e.key === "Enter" || e.key === " ") {
												e.preventDefault();
												onAssetClick?.(asset);
											}
										}}
										role="button"
										tabIndex={0}
										aria-label={`Open board ${asset.details.name}`}
									>
										<div className="w-12 h-12 bg-blue-50 rounded-md flex items-center justify-center flex-shrink-0">
											<svg
												className="w-6 h-6 text-blue-500"
												fill="none"
												stroke="currentColor"
												viewBox="0 0 24 24"
												aria-hidden="true"
											>
												<path
													strokeLinecap="round"
													strokeLinejoin="round"
													strokeWidth={2}
													d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0a2 2 0 002-2h10a2 2 0 012 2v0"
												/>
											</svg>
										</div>
										<div className="flex-1 min-w-0">
											<h3 className="text-sm font-medium text-gray-900 truncate">
												{asset.details.name}
											</h3>
											<p className="text-xs text-gray-500 mt-1">
												Board •{" "}
												{new Date(asset.uploadedAt).toLocaleDateString()}
											</p>
										</div>
									</div>
								);
							} else {
								// Simple list item for AssetDisplayItem assets
								return (
									<div
										key={key}
										className="flex items-center gap-4 p-4 bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
										onClick={() => onAssetClick?.(asset)}
										onKeyDown={(e) => {
											if (e.key === "Enter" || e.key === " ") {
												e.preventDefault();
												onAssetClick?.(asset);
											}
										}}
										role="button"
										tabIndex={0}
										aria-label={`Open asset ${asset.details.name}`}
									>
										<div className="w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center flex-shrink-0">
											{asset.imageUrl ? (
												<img
													src={asset.imageUrl}
													alt={asset.details.name}
													className="w-full h-full object-cover rounded-md"
												/>
											) : (
												<svg
													className="w-6 h-6 text-gray-400"
													fill="none"
													stroke="currentColor"
													viewBox="0 0 24 24"
													aria-hidden="true"
												>
													<path
														strokeLinecap="round"
														strokeLinejoin="round"
														strokeWidth={1}
														d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
													/>
												</svg>
											)}
										</div>
										<div className="flex-1 min-w-0">
											<h3 className="text-sm font-medium text-gray-900 truncate">
												{asset.details.name}
											</h3>
											<p className="text-xs text-gray-500 mt-1">
												{asset.details.type} •{" "}
												{new Date(asset.uploadedAt).toLocaleDateString()}
											</p>
										</div>
									</div>
								);
							}
						} else {
							// Handle AssetWithRelations in list mode
							return (
								<AssetListItem
									key={key}
									asset={asset}
									onClick={() => onAssetClick?.(asset)}
									onDelete={
										onAssetDelete ? () => onAssetDelete(asset) : undefined
									}
									onUpdate={onUpdate}
									allTags={allTags}
									allCollections={allCollections}
								/>
							);
						}
					})}
				</div>
			)}
		</div>
	);
});

export default AssetGrid;
