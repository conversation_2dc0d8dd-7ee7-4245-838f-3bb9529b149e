import React, { useState } from "react";
import {
	Plus,
	Search,
	Star,
	Clock,
	Upload,
	Trash2,
	ChevronRight,
	Folder,
	Tag,
	Grid,
	List,
	Filter,
	Cloud,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import {
	AssetCollection,
	AssetTag,
	CloudAssetConnection,
} from "../../../domain/types";
import BoardSelector from "../board-selector/BoardSelector";

// Asset library sections enum
export enum AssetLibrarySections {
	ALL_ASSETS = "all",
	RECENTLY_ADDED = "recent",
	MY_UPLOADS = "owned",
	RECENTLY_DELETED = "deleted",
	GOOGLE_DRIVE = "google_drive",
	ONEDRIVE = "onedrive",
	DROPBOX = "dropbox",
}

interface AssetSidebarProps {
	onUploadClick?: () => void;
	onSectionChange?: (section: AssetLibrarySections) => void;
	onSearchChange?: (search: string) => void;
	onTagFilter?: (tags: string[]) => void;
	onCollectionFilter?: (collections: string[]) => void;
	onViewModeChange?: (mode: "grid" | "list") => void;
	collections?: AssetCollection[];
	tags?: AssetTag[];
	boards?: any[];
	cloudConnections?: CloudAssetConnection[];
	className?: string;
	currentSection?: AssetLibrarySections;
	currentViewMode?: "grid" | "list";
	selectedTags?: string[];
	selectedCollections?: string[];
}

export const AssetSidebar: React.FC<AssetSidebarProps> = ({
	onUploadClick,
	onSectionChange,
	onSearchChange,
	onTagFilter,
	onCollectionFilter,
	onViewModeChange,
	collections = [],
	tags = [],
	boards = [],
	cloudConnections = [],
	className = "",
	currentSection = AssetLibrarySections.ALL_ASSETS,
	currentViewMode = "grid",
	selectedTags = [],
	selectedCollections = [],
}) => {
	const navigate = useNavigate();
	const [searchInput, setSearchInput] = useState("");
	const [showFilters, setShowFilters] = useState(false);
	const [expandedSections, setExpandedSections] = useState<
		Record<string, boolean>
	>({
		collections: true,
		tags: true,
		boards: true,
		cloudStorage: true,
	});

	// ✅ Good: Handle search change in event handler instead of useEffect
	const handleSearchInputChange = (value: string) => {
		setSearchInput(value);
		// Pass the search value immediately to parent - let parent handle debouncing
		onSearchChange?.(value);
	};

	const handleSectionClick = (section: AssetLibrarySections) => {
		// Navigate to assets route with the section as a query parameter
		const searchParams = new URLSearchParams();
		searchParams.set("section", section);
		navigate(`/assets?${searchParams.toString()}`);
		onSectionChange?.(section);
	};

	const handleTagToggle = (tagName: string) => {
		const newTags = selectedTags.includes(tagName)
			? selectedTags.filter((t) => t !== tagName)
			: [...selectedTags, tagName];
		onTagFilter?.(newTags);
	};

	const handleCollectionToggle = (collectionId: string) => {
		const newCollections = selectedCollections.includes(collectionId)
			? selectedCollections.filter((c) => c !== collectionId)
			: [...selectedCollections, collectionId];
		onCollectionFilter?.(newCollections);
	};

	const toggleSection = (section: string) => {
		setExpandedSections((prev) => ({
			...prev,
			[section]: !prev[section],
		}));
	};

	const sidebarSections = [
		{
			key: AssetLibrarySections.ALL_ASSETS,
			label: "All assets",
			icon: Star,
		},
		{
			key: AssetLibrarySections.RECENTLY_ADDED,
			label: "Recently added",
			icon: Clock,
		},
		{
			key: AssetLibrarySections.MY_UPLOADS,
			label: "My uploads",
			icon: Upload,
		},
		{
			key: AssetLibrarySections.RECENTLY_DELETED,
			label: "Recently deleted",
			icon: Trash2,
		},
	];

	return (
		<div
			className={`w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col`}
		>
			{/* Header */}
			<div className="p-4 border-b border-gray-200 dark:border-gray-700">
				<button
					type="button"
					onClick={onUploadClick}
					className="w-full bg-[#676D50] dark:bg-[#A6884C] text-white px-4 py-2 rounded-lg hover:bg-[#5A5F46] dark:hover:bg-[#8F7340] transition-colors flex items-center justify-center gap-2"
				>
					<Plus size={16} />
					Upload
				</button>
			</div>

			{/* Search */}
			<div className="p-4 border-b border-gray-200 dark:border-gray-700">
				<div className="relative">
					<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-4 w-4" />
					<input
						type="text"
						placeholder="Search assets..."
						className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-[#676D50] dark:focus:ring-[#A6884C] focus:border-transparent"
						value={searchInput}
						onChange={(e) => handleSearchInputChange(e.target.value)}
					/>
				</div>
			</div>

			{/* View Mode Toggle */}
			<div className="p-4 border-b border-gray-200 dark:border-gray-700">
				<div className="flex items-center justify-between mb-2">
					<span className="text-sm font-medium text-gray-700 dark:text-gray-300">
						View
					</span>
					<button
						type="button"
						onClick={() => setShowFilters(!showFilters)}
						className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
					>
						<Filter size={16} />
					</button>
				</div>
				<div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
					<button
						type="button"
						onClick={() => onViewModeChange?.("grid")}
						className={`flex-1 flex items-center justify-center gap-1 px-3 py-1 rounded text-sm transition-colors ${
							currentViewMode === "grid"
								? "bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm"
								: "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
						}`}
					>
						<Grid size={14} />
						Grid
					</button>
					<button
						type="button"
						onClick={() => onViewModeChange?.("list")}
						className={`flex-1 flex items-center justify-center gap-1 px-3 py-1 rounded text-sm transition-colors ${
							currentViewMode === "list"
								? "bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm"
								: "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
						}`}
					>
						<List size={14} />
						List
					</button>
				</div>
			</div>

			{/* Navigation */}
			<div className="flex-1 overflow-y-auto">
				{/* Main Sections */}
				<div className="p-4 border-b border-gray-200 dark:border-gray-700">
					<div className="space-y-1">
						{sidebarSections.map((section) => {
							const Icon = section.icon;
							const isActive = currentSection === section.key;

							return (
								<button
									key={section.key}
									onClick={() => handleSectionClick(section.key)}
									className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors ${
										isActive
											? "bg-[#676D50]/10 dark:bg-[#A6884C]/10 text-[#676D50] dark:text-[#A6884C]"
											: "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
									}`}
								>
									<Icon size={16} />
									{section.label}
								</button>
							);
						})}
					</div>
				</div>

				{/* Cloud Storage */}
				<div className="p-4 border-b border-gray-200 dark:border-gray-700">
					<button
						onClick={() => toggleSection("cloudStorage")}
						className="w-full flex items-center justify-between text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
					>
						<div className="flex items-center gap-2">
							<Cloud size={16} />
							Cloud Storage
						</div>
						<ChevronRight
							size={16}
							className={`transition-transform ${expandedSections.cloudStorage ? "rotate-90" : ""}`}
						/>
					</button>
					{expandedSections.cloudStorage && (
						<div className="space-y-1">
							{/* Google Drive */}
							{(() => {
								const googleConnection = cloudConnections.find(
									(c) => c.provider === "google_drive",
								);
								const isActive =
									currentSection === AssetLibrarySections.GOOGLE_DRIVE;
								return (
									<button
										onClick={() =>
											handleSectionClick(AssetLibrarySections.GOOGLE_DRIVE)
										}
										disabled={!googleConnection?.isConnected}
										className={`w-full flex items-center justify-between px-2 py-1 rounded text-sm transition-colors ${
											isActive
												? "bg-[#676D50]/10 dark:bg-[#A6884C]/10 text-[#676D50] dark:text-[#A6884C]"
												: googleConnection?.isConnected
													? "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
													: "text-gray-400 dark:text-gray-500 cursor-not-allowed"
										}`}
									>
										<div className="flex items-center gap-2">
											<div className="w-3 h-3 bg-blue-500 rounded-sm flex items-center justify-center text-white text-xs">
												📁
											</div>
											<span className="truncate">Google Drive</span>
										</div>
										<div className="flex items-center gap-1">
											{googleConnection?.isConnected ? (
												<>
													<div className="w-2 h-2 bg-green-500 rounded-full"></div>
													{googleConnection.totalFiles && (
														<span className="text-xs text-gray-500">
															{googleConnection.totalFiles}
														</span>
													)}
												</>
											) : (
												<div className="w-2 h-2 bg-gray-400 rounded-full"></div>
											)}
										</div>
									</button>
								);
							})()}

							{/* OneDrive */}
							{(() => {
								const oneDriveConnection = cloudConnections.find(
									(c) => c.provider === "onedrive",
								);
								const isActive =
									currentSection === AssetLibrarySections.ONEDRIVE;
								return (
									<button
										onClick={() =>
											handleSectionClick(AssetLibrarySections.ONEDRIVE)
										}
										disabled={!oneDriveConnection?.isConnected}
										className={`w-full flex items-center justify-between px-2 py-1 rounded text-sm transition-colors ${
											isActive
												? "bg-[#676D50]/10 dark:bg-[#A6884C]/10 text-[#676D50] dark:text-[#A6884C]"
												: oneDriveConnection?.isConnected
													? "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
													: "text-gray-400 dark:text-gray-500 cursor-not-allowed"
										}`}
									>
										<div className="flex items-center gap-2">
											<div className="w-3 h-3 bg-blue-600 rounded-sm flex items-center justify-center text-white text-xs">
												☁️
											</div>
											<span className="truncate">OneDrive</span>
										</div>
										<div className="flex items-center gap-1">
											{oneDriveConnection?.isConnected ? (
												<>
													<div className="w-2 h-2 bg-green-500 rounded-full"></div>
													{oneDriveConnection.totalFiles && (
														<span className="text-xs text-gray-500">
															{oneDriveConnection.totalFiles}
														</span>
													)}
												</>
											) : (
												<div className="w-2 h-2 bg-gray-400 rounded-full"></div>
											)}
										</div>
									</button>
								);
							})()}

							{/* Dropbox */}
							{(() => {
								const dropboxConnection = cloudConnections.find(
									(c) => c.provider === "dropbox",
								);
								const isActive =
									currentSection === AssetLibrarySections.DROPBOX;
								return (
									<button
										onClick={() =>
											handleSectionClick(AssetLibrarySections.DROPBOX)
										}
										disabled={!dropboxConnection?.isConnected}
										className={`w-full flex items-center justify-between px-2 py-1 rounded text-sm transition-colors ${
											isActive
												? "bg-[#676D50]/10 dark:bg-[#A6884C]/10 text-[#676D50] dark:text-[#A6884C]"
												: dropboxConnection?.isConnected
													? "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
													: "text-gray-400 dark:text-gray-500 cursor-not-allowed"
										}`}
									>
										<div className="flex items-center gap-2">
											<div className="w-3 h-3 bg-blue-700 rounded-sm flex items-center justify-center text-white text-xs">
												📦
											</div>
											<span className="truncate">Dropbox</span>
										</div>
										<div className="flex items-center gap-1">
											{dropboxConnection?.isConnected ? (
												<>
													<div className="w-2 h-2 bg-green-500 rounded-full"></div>
													{dropboxConnection.totalFiles && (
														<span className="text-xs text-gray-500">
															{dropboxConnection.totalFiles}
														</span>
													)}
												</>
											) : (
												<div className="w-2 h-2 bg-gray-400 rounded-full"></div>
											)}
										</div>
									</button>
								);
							})()}
						</div>
					)}
				</div>

				{/* Collections */}
				{collections.length > 0 && (
					<div className="p-4 border-b border-gray-200 dark:border-gray-700">
						<button
							onClick={() => toggleSection("collections")}
							className="w-full flex items-center justify-between text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
						>
							Collections
							<ChevronRight
								size={16}
								className={`transition-transform ${expandedSections.collections ? "rotate-90" : ""}`}
							/>
						</button>
						{expandedSections.collections && (
							<div className="space-y-1">
								{collections.slice(0, 10).map((collection) => (
									<button
										key={collection.id}
										onClick={() => handleCollectionToggle(collection.id)}
										className={`w-full flex items-center gap-2 px-2 py-1 rounded text-sm transition-colors ${
											selectedCollections.includes(collection.id)
												? "bg-[#676D50]/10 dark:bg-[#A6884C]/10 text-[#676D50] dark:text-[#A6884C]"
												: "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
										}`}
									>
										<Folder size={14} />
										<span className="truncate">{collection.name}</span>
										{collection.isPublic && (
											<span className="text-xs text-[#676D50] dark:text-[#A6884C]">
												•
											</span>
										)}
									</button>
								))}
								{collections.length > 10 && (
									<div className="text-xs text-gray-500 dark:text-gray-400 px-2">
										+{collections.length - 10} more
									</div>
								)}
							</div>
						)}
					</div>
				)}

				{/* Tags */}
				{tags.length > 0 && (
					<div className="p-4 border-b border-gray-200 dark:border-gray-700">
						<button
							onClick={() => toggleSection("tags")}
							className="w-full flex items-center justify-between text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
						>
							Tags
							<ChevronRight
								size={16}
								className={`transition-transform ${expandedSections.tags ? "rotate-90" : ""}`}
							/>
						</button>
						{expandedSections.tags && (
							<div className="space-y-1">
								{tags.slice(0, 15).map((tag) => (
									<button
										key={tag.id}
										onClick={() => handleTagToggle(tag.name)}
										className={`w-full flex items-center gap-2 px-2 py-1 rounded text-sm transition-colors ${
											selectedTags.includes(tag.name)
												? "bg-[#676D50]/10 dark:bg-[#A6884C]/10 text-[#676D50] dark:text-[#A6884C]"
												: "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
										}`}
									>
										<Tag size={12} />
										<span className="truncate">{tag.name}</span>
									</button>
								))}
								{tags.length > 15 && (
									<div className="text-xs text-gray-500 dark:text-gray-400 px-2">
										+{tags.length - 15} more
									</div>
								)}
							</div>
						)}
					</div>
				)}

				{/* Boards */}
				{boards && Array.isArray(boards) && boards.length > 0 && (
					<div className="p-4">
						<div className="mb-2">
							<button
								type="button"
								onClick={() => toggleSection("boards")}
								className="w-full flex items-center justify-between text-sm font-medium text-gray-700 dark:text-gray-300"
							>
								<div className="flex items-center gap-2">
									<Folder size={16} />
									Boards
								</div>
								<ChevronRight
									size={16}
									className={`transition-transform ${expandedSections.boards ? "rotate-90" : ""}`}
								/>
							</button>
						</div>
						{expandedSections.boards && (
							<BoardSelector
								boards={boards}
								selectedBoardId={undefined}
								onBoardSelect={(value) => {
									// Navigate to board assets page with section change
									const searchParams = new URLSearchParams();
									navigate(`/assets/board/${value}`);
								}}
								allowCreation={false}
								showHeader={false}
							/>
						)}
					</div>
				)}
			</div>
		</div>
	);
};

export default AssetSidebar;
