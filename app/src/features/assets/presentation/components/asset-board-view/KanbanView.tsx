import React, { useState } from "react";
import {
	DndContext,
	DragOverlay,
	closestCorners,
	KeyboardSensor,
	PointerSensor,
	useSensor,
	useSensors,
	DragStartEvent,
	DragOverEvent,
	DragEndEvent,
	UniqueIdentifier,
	useDroppable,
} from "@dnd-kit/core";
import {
	arrayMove,
	SortableContext,
	sortableKeyboardCoordinates,
	useSortable,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import type { AssetDisplayItem } from "../../../domain/types/board.types";

interface Containers {
	[key: string]: UniqueIdentifier[];
}

const defaultAnnouncements = {
	onDragStart(id: UniqueIdentifier) {
		console.log(`Picked up draggable item ${id}.`);
	},
	onDragOver(id: UniqueIdentifier, overId: UniqueIdentifier | null) {
		if (overId) {
			console.log(
				`Draggable item ${id} was moved over droppable area ${overId}.`,
			);
			return;
		}
		console.log(`Draggable item ${id} is no longer over a droppable area.`);
	},
	onDragEnd(id: UniqueIdentifier, overId: UniqueIdentifier | null) {
		if (overId) {
			console.log(
				`Draggable item ${id} was dropped over droppable area ${overId}`,
			);
			return;
		}
		console.log(`Draggable item ${id} was dropped.`);
	},
	onDragCancel(id: UniqueIdentifier) {
		console.log(`Dragging was cancelled. Draggable item ${id} was dropped.`);
	},
};

interface KanbanViewProps {
	assets: AssetDisplayItem[];
	onAssetClick: (asset: AssetDisplayItem) => void;
	onLaneChange?: (
		assetId: string,
		fromLane: string,
		toLane: string,
	) => Promise<void>;
}

export function KanbanView({
	assets = [],
	onAssetClick,
	onLaneChange,
}: KanbanViewProps): JSX.Element {
	// Separate boards from regular assets
	const boards = assets.filter((asset) => asset.details.type === "board");
	const regularAssets = assets.filter(
		(asset) => asset.details.type !== "board",
	);

	const [items, setItems] = useState<Containers>({
		None:
			regularAssets
				?.filter((asset) => !asset.status || asset.status === "None")
				.map((asset) => asset.id) || [],
		"Need Edit":
			regularAssets
				?.filter((asset) => asset.status === "Need Edit")
				.map((asset) => asset.id) || [],
		"In Progress":
			regularAssets
				?.filter((asset) => asset.status === "In Progress")
				.map((asset) => asset.id) || [],
		"Need Review":
			regularAssets
				?.filter((asset) => asset.status === "Need Review")
				.map((asset) => asset.id) || [],
		Done:
			regularAssets
				?.filter((asset) => asset.status === "Done")
				.map((asset) => asset.id) || [],
	});
	const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);

	const sensors = useSensors(
		useSensor(PointerSensor),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		}),
	);

	const findContainer = (id: UniqueIdentifier): string | undefined => {
		if (id in items) {
			return id as string;
		}
		return Object.keys(items).find((key) => items[key].includes(id));
	};

	const handleDragStart = ({ active }: DragStartEvent) => {
		setActiveId(active.id);
	};

	const handleDragOver = ({ active, over }: DragOverEvent) => {
		if (!over) return;

		const id = active.id;
		const overId = over.id;

		const activeContainer = findContainer(id);
		const overContainer = findContainer(overId);

		if (
			!activeContainer ||
			!overContainer ||
			activeContainer === overContainer
		) {
			return;
		}

		setItems((prev) => {
			const activeItems = prev[activeContainer];
			const overItems = prev[overContainer];
			const activeIndex = activeItems.indexOf(id);

			let newIndex: number;
			if (overId in prev) {
				newIndex = overItems.length;
			} else {
				const overIndex = overItems.indexOf(overId);
				newIndex = overIndex >= 0 ? overIndex : overItems.length;
			}

			return {
				...prev,
				[activeContainer]: [
					...prev[activeContainer].filter((item) => item !== id),
				],
				[overContainer]: [
					...overItems.slice(0, newIndex),
					activeItems[activeIndex],
					...overItems.slice(newIndex),
				],
			};
		});
	};

	const handleDragEnd = async ({ active, over }: DragEndEvent) => {
		if (!over) {
			setActiveId(null);
			return;
		}

		const activeContainer = findContainer(active.id);
		const overContainer = findContainer(over.id);

		if (!activeContainer || !overContainer) {
			setActiveId(null);
			return;
		}

		if (onLaneChange) {
			await onLaneChange(active.id.toString(), activeContainer, overContainer);
		}

		// If items are reordered within the same container
		if (activeContainer === overContainer) {
			const activeIndex = items[activeContainer].indexOf(active.id);
			const overIndex = items[overContainer].indexOf(over.id);

			if (activeIndex !== overIndex) {
				setItems((items) => ({
					...items,
					[overContainer]: arrayMove(
						items[overContainer],
						activeIndex,
						overIndex,
					),
				}));
			}
		}

		setActiveId(null);
	};

	// Remove wrapperStyle constant and update the wrapper div
	return (
		<div className="space-y-4">
			{/* Boards Section - Display boards at the top, outside of kanban lanes */}
			{boards.length > 0 && (
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
						<svg
							className="w-5 h-5 text-blue-500"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0a2 2 0 002-2h10a2 2 0 012 2v0"
							/>
						</svg>
						Boards ({boards.length})
					</h3>
					<div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
						{boards.map((board) => (
							<div
								key={board.id}
								className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200 hover:bg-blue-100 transition-colors cursor-pointer"
								onClick={() => onAssetClick(board)}
							>
								<div className="w-10 h-10 bg-blue-100 rounded-md flex items-center justify-center flex-shrink-0">
									<svg
										className="w-5 h-5 text-blue-600"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0a2 2 0 002-2h10a2 2 0 012 2v0"
										/>
									</svg>
								</div>
								<div className="flex-1 min-w-0">
									<h4 className="font-medium text-blue-900 truncate text-sm">
										{board.details.name}
									</h4>
									<p className="text-xs text-blue-600 mt-0.5">Board</p>
								</div>
							</div>
						))}
					</div>
				</div>
			)}

			{/* Kanban Lanes for Assets Only */}
			<div className="flex flex-row overflow-x-auto">
				<DndContext
					// @ts-expect-error
					screenReaderInstructions={defaultAnnouncements}
					sensors={sensors}
					collisionDetection={closestCorners}
					onDragStart={handleDragStart}
					onDragOver={handleDragOver}
					onDragEnd={handleDragEnd}
				>
					<Container id="None" items={items["None"]} />
					<Container id="Need Edit" items={items["Need Edit"]} />
					<Container id="In Progress" items={items["In Progress"]} />
					<Container id="Need Review" items={items["Need Review"]} />
					<Container id="Done" items={items["Done"]} />
					<DragOverlay>{activeId ? <Item id={activeId} /> : null}</DragOverlay>
				</DndContext>
			</div>
		</div>
	);

	function Container({ id, items }: ContainerProps): JSX.Element {
		const { setNodeRef } = useDroppable({ id });

		return (
			<SortableContext
				id={id}
				items={items}
				strategy={verticalListSortingStrategy}
			>
				<div
					ref={setNodeRef}
					className="flex-1 bg-slate-200 p-4 m-4 rounded-lg min-w-[300px]"
				>
					<div className="flex justify-between">
						<h2 className="font-semibold text-gray-900 mb-4 text-lg">{id}</h2>
						<div className="text-gray-600 text-sm">
							{items.length} {items.length === 1 ? "item" : "items"}
						</div>
					</div>
					<div className="max-h-[700px] overflow-scroll">
						{items.map((id) => (
							<SortableItem key={id} id={id} />
						))}
						{items.length === 0 && (
							<div className="text-gray-400 text-center p-4 border-2 border-dashed border-gray-300 rounded-lg">
								Drop items here
							</div>
						)}
					</div>
				</div>
			</SortableContext>
		);
	}

	function Item({ id }: ItemProps): JSX.Element {
		const asset = regularAssets?.find((a) => a.id === id);

		if (!asset) {
			return <div className="text-gray-400">Asset not found</div>;
		}

		// Since we've filtered out boards, this will only render regular assets
		// Keep the existing card design for assets
		return (
			<div
				className="w-full bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow p-3 cursor-pointer"
				onClick={() => onAssetClick(asset)}
			>
				<div className="flex gap-3">
					{/* Image thumbnail */}
					<div className="w-16 h-16 flex-shrink-0">
						{asset.imageUrl ? (
							<img
								src={asset.imageUrl}
								alt={asset.details.name}
								className="w-full h-full object-cover rounded-md"
								title={
									asset.width && asset.height
										? `${asset.width}x${asset.height}`
										: undefined
								}
							/>
						) : (
							<div className="w-full h-full bg-gray-100 rounded-md flex items-center justify-center">
								<svg
									className="w-8 h-8 text-gray-400"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
									/>
								</svg>
							</div>
						)}
					</div>

					{/* Asset information */}
					<div className="flex-1 min-w-0">
						<h3 className="font-medium text-gray-900 truncate mb-1">
							{asset.details.name}
						</h3>
						<div className="flex flex-col gap-1">
							<div className="flex items-center gap-2 text-xs text-gray-500">
								<span className="bg-gray-100 px-2 py-1 rounded">
									{asset.details.type}
								</span>
								<span>v{asset.details.version}</span>
								{asset.details.owner && <span>by {asset.details.owner}</span>}
							</div>
							{asset.details.tags.length > 0 && (
								<div className="flex flex-wrap gap-1 mt-1">
									{asset.details.tags.map((tag) => (
										<span
											key={tag}
											className="text-xs bg-blue-50 text-blue-600 px-2 py-0.5 rounded"
										>
											{tag}
										</span>
									))}
								</div>
							)}
							<div className="flex items-center gap-2 text-xs text-gray-400 mt-1">
								{asset.width && asset.height && (
									<>
										<span>
											{asset.width}×{asset.height}px
										</span>
										<span>•</span>
									</>
								)}
								<span>
									{new Date(asset.uploadedAt).toLocaleDateString(undefined, {
										year: "numeric",
										month: "short",
										day: "numeric",
									})}
								</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}

	function SortableItem({ id }: SortableItemProps): JSX.Element {
		const {
			attributes,
			listeners,
			setNodeRef,
			transform,
			transition,
			isDragging,
		} = useSortable({ id });

		const style = {
			transform: CSS.Transform.toString(transform),
			transition,
		};

		return (
			<div
				ref={setNodeRef}
				style={style}
				{...attributes}
				{...listeners}
				className={`touch-manipulation my-2 ${isDragging ? "opacity-50 rotate-3" : ""}`}
			>
				<Item id={id} />
			</div>
		);
	}
}

interface ContainerProps {
	id: string;
	items: UniqueIdentifier[];
}

interface ItemProps {
	id: UniqueIdentifier;
}

interface SortableItemProps {
	id: UniqueIdentifier;
}
