import React, { useEffect, useState } from "react";
import type { AssetDisplayItem } from "../../../domain/types/board.types";

// Asset List View Component
export function AssetListView({
	assets,
	onAssetClick,
}: {
	assets: AssetDisplayItem[];
	onAssetClick: (asset: AssetDisplayItem) => void;
}) {
	const [items, setItems] = useState<AssetDisplayItem[]>([]);
	const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

	// Sort assets by date when they change or sort direction changes
	useEffect(() => {
		const sortedItems = [...assets].sort((a, b) => {
			const dateA = new Date(a.uploadedAt).getTime();
			const dateB = new Date(b.uploadedAt).getTime();
			return sortDirection === "asc" ? dateA - dateB : dateB - dateA;
		});
		setItems(sortedItems);
	}, [assets, sortDirection]);

	// Toggle sort direction
	const toggleSortDirection = () => {
		setSortDirection((prev) => (prev === "asc" ? "desc" : "asc"));
	};

	return (
		<div className="border rounded-md overflow-hidden">
			<table className="w-full">
				<thead className="bg-muted">
					<tr>
						<th className="text-left p-3 font-medium">Preview</th>
						<th className="text-left p-3 font-medium">Name</th>
						<th className="text-left p-3 font-medium">Type</th>
						<th
							className="text-left p-3 font-medium cursor-pointer hover:bg-muted/80 flex items-center gap-1"
							onClick={toggleSortDirection}
						>
							Uploaded
							<span className="ml-1">
								{sortDirection === "asc" ? "↑" : "↓"}
							</span>
						</th>
					</tr>
				</thead>
				<tbody>
					{items.map((asset) => (
						<AssetRow
							key={asset.id}
							asset={asset}
							onClick={() => onAssetClick(asset)}
						/>
					))}
				</tbody>
			</table>
		</div>
	);
}

function AssetRow({
	asset,
	onClick,
}: {
	asset: AssetDisplayItem;
	onClick: () => void;
}) {
	const renderPreview = () => {
		if (asset.details.type === "board") {
			return (
				<div className="w-12 h-12 flex items-center justify-center bg-gray-100 rounded">
					<svg
						className="w-6 h-6 text-gray-400"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M3 7a1 1 0 011-1h5l2 2h8a1 1 0 011 1v9a1 1 0 01-1 1H4a1 1 0 01-1-1V7z"
						/>
					</svg>
				</div>
			);
		}

		if (asset.imageUrl) {
			return (
				<img
					src={asset.imageUrl}
					alt={asset.details.name}
					className="w-full h-full object-cover"
				/>
			);
		}

		return (
			<div className="w-12 h-12 flex items-center justify-center bg-gray-100 rounded">
				<svg
					className="w-6 h-6 text-gray-400"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
				>
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
					/>
				</svg>
			</div>
		);
	};

	return (
		<tr className="border-t hover:bg-muted/50 cursor-pointer" onClick={onClick}>
			<td className="p-3">
				<div className="w-12 h-12 rounded overflow-hidden">
					{renderPreview()}
				</div>
			</td>
			<td className="p-3">{asset.details.name}</td>
			<td className="p-3 capitalize">{asset.details.type}</td>
			<td className="p-3">{new Date(asset.uploadedAt).toLocaleDateString()}</td>
		</tr>
	);
}
