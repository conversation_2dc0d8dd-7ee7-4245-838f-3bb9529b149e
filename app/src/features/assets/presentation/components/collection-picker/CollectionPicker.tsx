import React, { useState } from 'react';
import { FolderPlus, X, Folder, Loader2 } from 'lucide-react';
import { AssetCollection } from '../../../domain/types';
import { addToCollection, removeFromCollection, createCollection } from 'wasp/client/operations';
import { usePopup } from '../../hooks/use-popup';
import toast from 'react-hot-toast';
import { useOrganizationState } from '../../../../../organization/store';

interface CollectionPickerProps {
  assetId: number;
  collections: AssetCollection[];
  allCollections: AssetCollection[];
  onUpdate?: () => void;
  className?: string;
}

export const CollectionPicker: React.FC<CollectionPickerProps> = ({
  assetId,
  collections,
  allCollections,
  onUpdate,
  className = '',
}) => {
  const { selectedOrganizationId } = useOrganizationState();
  const [newCollectionName, setNewCollectionName] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingCollectionId, setLoadingCollectionId] = useState<string | null>(null);
  const { isOpen, toggle, close, popupRef, triggerRef } = usePopup({
    onClose: () => {
      setNewCollectionName('');
      setIsCreating(false);
    },
  });

  const handleCreateCollection = async () => {
    if (!newCollectionName.trim()) return;

    try {
      setIsLoading(true);
      const result = await createCollection({
        name: newCollectionName.trim(),
        isPublic: false,
        organizationId: selectedOrganizationId as any,
      });

      if (result.id) {
        await addToCollection({
          assetIds: [assetId],
          collectionId: result.id,
        });
      }

      setNewCollectionName('');
      setIsCreating(false);
      await onUpdate?.();
      toast.success('Collection created and asset added');
      close();
    } catch (error) {
      console.error('Failed to create collection:', error);
      toast.error('Failed to create collection');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddToCollection = async (collectionId: string) => {
    try {
      setLoadingCollectionId(collectionId);
      await addToCollection({
        assetIds: [assetId],
        collectionId,
      });
      await onUpdate?.();
      toast.success('Added to collection');
      close();
    } catch (error) {
      console.error('Failed to add to collection:', error);
      toast.error('Failed to add to collection');
    } finally {
      setLoadingCollectionId(null);
    }
  };

  const handleRemoveFromCollection = async (collectionId: string) => {
    try {
      setLoadingCollectionId(collectionId);
      await removeFromCollection({
        assetIds: [assetId],
        collectionId,
      });
      await onUpdate?.();
      toast.success('Removed from collection');
    } catch (error) {
      console.error('Failed to remove from collection:', error);
      toast.error('Failed to remove from collection');
    } finally {
      setLoadingCollectionId(null);
    }
  };

  const currentCollectionIds = collections.map((c) => c.id);
  const availableCollections = allCollections.filter((c) => !currentCollectionIds.includes(c.id));

  return (
    <div className={`relative ${className}`}>
      {/* Collection Button */}
      <button
        ref={triggerRef}
        className='p-1.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors'
        onClick={toggle}
        title='Manage collections'
      >
        <Folder size={16} className='text-gray-500 dark:text-gray-400' />
        {collections.length > 0 && (
          <span className='absolute -top-1 -right-1 bg-[#676D50] dark:bg-[#A6884C] text-white text-xs rounded-full w-4 h-4 flex items-center justify-center'>
            {collections.length}
          </span>
        )}
      </button>

      {/* Collection Picker Popup */}
      {isOpen && (
        <div
          ref={popupRef}
          className='absolute top-full right-0 mt-1 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3 z-10'
        >
          {/* Quick Create */}
          <div className='mb-3'>
            {isCreating ? (
              <div className='flex gap-2'>
                <input
                  type='text'
                  value={newCollectionName}
                  onChange={(e) => setNewCollectionName(e.target.value)}
                  className='flex-1 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400'
                  placeholder='Collection name'
                  autoFocus
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && newCollectionName.trim() && !isLoading) {
                      handleCreateCollection();
                    }
                    if (e.key === 'Escape') {
                      setIsCreating(false);
                      setNewCollectionName('');
                    }
                  }}
                  disabled={isLoading}
                />
                <button
                  onClick={() => newCollectionName.trim() && handleCreateCollection()}
                  className='bg-[#676D50] dark:bg-[#A6884C] text-white px-2 py-1 rounded text-sm hover:bg-[#5A5F46] dark:hover:bg-[#8F7340] disabled:opacity-50 disabled:cursor-not-allowed min-w-[60px] flex items-center justify-center'
                  disabled={!newCollectionName.trim() || isLoading}
                >
                  {isLoading ? <Loader2 className='w-4 h-4 animate-spin text-white' /> : 'Create'}
                </button>
                <button
                  onClick={() => {
                    setIsCreating(false);
                    setNewCollectionName('');
                  }}
                  className='text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 px-1'
                  disabled={isLoading}
                >
                  <X size={16} />
                </button>
              </div>
            ) : (
              <button
                onClick={() => setIsCreating(true)}
                className='flex items-center gap-1 text-[#676D50] dark:text-[#A6884C] hover:text-[#5A5F46] dark:hover:text-[#8F7340] text-sm'
              >
                <FolderPlus size={14} />
                New Collection
              </button>
            )}
          </div>

          {/* Current Collections */}
          {collections.length > 0 && (
            <div className='mb-3'>
              <div className='text-xs font-medium text-gray-500 dark:text-gray-400 mb-2'>Current Collections</div>
              <div className='flex flex-col gap-1'>
                {collections.map((collection) => (
                  <div
                    key={collection.id}
                    className='group flex items-center justify-between bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-2 py-1 rounded text-sm transition-colors'
                  >
                    <span className='flex items-center gap-1.5'>
                      <Folder size={14} className='text-gray-400 dark:text-gray-500' />
                      <span className='truncate'>{collection.name}</span>
                      {collection.isPublic && (
                        <span className='text-xs text-[#676D50] dark:text-[#A6884C]'>Public</span>
                      )}
                    </span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveFromCollection(collection.id);
                      }}
                      className={`${
                        loadingCollectionId === collection.id ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
                      } hover:text-red-500 dark:hover:text-red-400 transition-opacity disabled:cursor-not-allowed`}
                      disabled={loadingCollectionId === collection.id}
                    >
                      {loadingCollectionId === collection.id ? (
                        <Loader2 className='w-3 h-3 animate-spin text-gray-500 dark:text-gray-400' />
                      ) : (
                        <X size={14} />
                      )}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Available Collections */}
          {availableCollections.length > 0 && (
            <div>
              <div className='text-xs font-medium text-gray-500 dark:text-gray-400 mb-2'>Add to Collection</div>
              <div className='flex flex-col gap-1 max-h-32 overflow-y-auto'>
                {availableCollections.map((collection) => (
                  <button
                    key={collection.id}
                    onClick={() => handleAddToCollection(collection.id)}
                    className='flex items-center justify-between bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 px-2 py-1 rounded text-sm text-left transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
                    disabled={loadingCollectionId === collection.id}
                  >
                    <span className='flex items-center gap-1.5 flex-1 min-w-0'>
                      <Folder size={14} className='text-gray-400 dark:text-gray-500 flex-shrink-0' />
                      <span className='truncate'>{collection.name}</span>
                      {collection.isPublic && (
                        <span className='text-xs text-[#676D50] dark:text-[#A6884C] flex-shrink-0'>Public</span>
                      )}
                    </span>
                    {loadingCollectionId === collection.id && (
                      <Loader2 className='w-3 h-3 animate-spin text-gray-500 dark:text-gray-400 flex-shrink-0' />
                    )}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* No collections message */}
          {collections.length === 0 && availableCollections.length === 0 && !isCreating && (
            <div className='text-center text-gray-500 dark:text-gray-400 text-sm py-2'>
              No collections available. Create your first collection above.
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CollectionPicker;
