import React, { useState } from 'react';
import { Tag, Plus, X, Loader2 } from 'lucide-react';
import { AssetTag } from '../../../domain/types';
import { addAssetTags, removeAssetTags } from 'wasp/client/operations';
import { usePopup } from '../../hooks/use-popup';
import toast from 'react-hot-toast';
import { useOrganizationState } from '../../../../../organization/store';

interface TagEditorProps {
  assetId: number;
  tags: AssetTag[];
  allTags: string[];
  onUpdate?: () => void;
  className?: string;
}

export const TagEditor: React.FC<TagEditorProps> = ({ assetId, tags, allTags, onUpdate, className = '' }) => {
  const { selectedOrganizationId } = useOrganizationState();
  const [newTag, setNewTag] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingTagId, setLoadingTagId] = useState<string | null>(null);
  const { isOpen, toggle, popupRef, triggerRef } = usePopup({
    onClose: () => setNewTag(''),
  });

  const handleAddTag = async (tagName: string) => {
    if (!tagName.trim()) return;

    try {
      setIsLoading(true);
      await addAssetTags({
        assetIds: [assetId],
        tags: [tagName.trim()],
        organizationId: selectedOrganizationId!,
      });
      setNewTag('');
      await onUpdate?.();
      toast.success('Tag added successfully');
    } catch (error) {
      console.error('Failed to add tag:', error);
      toast.error('Failed to add tag');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveTag = async (tagId: string) => {
    try {
      setLoadingTagId(tagId);
      await removeAssetTags({
        assetIds: [assetId],
        tagIds: [tagId],
      });
      await onUpdate?.();
      toast.success('Tag removed successfully');
    } catch (error) {
      console.error('Failed to remove tag:', error);
      toast.error('Failed to remove tag');
    } finally {
      setLoadingTagId(null);
    }
  };

  const existingTagNames = tags.map((t) => t.name);
  const suggestedTags = allTags.filter((t) => !existingTagNames.includes(t));

  return (
    <div className={`relative ${className}`}>
      {/* Tag Button */}
      <button
        ref={triggerRef}
        className='p-1.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors'
        onClick={toggle}
        title='Manage tags'
      >
        <Tag size={16} className='text-gray-500 dark:text-gray-400' />
        {tags.length > 0 && (
          <span className='absolute -top-1 -right-1 bg-[#676D50] dark:bg-[#A6884C] text-white text-xs rounded-full w-4 h-4 flex items-center justify-center'>
            {tags.length}
          </span>
        )}
      </button>

      {/* Tag Editor Popup */}
      {isOpen && (
        <div
          ref={popupRef}
          className='absolute top-full right-0 mt-1 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3 z-10'
        >
          {/* Quick Add */}
          <div className='mb-3'>
            <div className='flex gap-2'>
              <input
                type='text'
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                className='flex-1 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400'
                placeholder='Add tag...'
                autoFocus
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && newTag.trim() && !isLoading) {
                    handleAddTag(newTag);
                  }
                }}
                disabled={isLoading}
              />
              <button
                onClick={() => newTag.trim() && handleAddTag(newTag)}
                className='bg-[#676D50] dark:bg-[#A6884C] text-white p-1 rounded hover:bg-[#5A5F46] dark:hover:bg-[#8F7340] disabled:opacity-50 disabled:cursor-not-allowed min-w-[28px] flex items-center justify-center'
                disabled={!newTag.trim() || isLoading}
              >
                {isLoading ? <Loader2 className='w-4 h-4 animate-spin text-white' /> : <Plus size={16} />}
              </button>
            </div>
          </div>

          {/* Current Tags */}
          {tags.length > 0 && (
            <div className='mb-3'>
              <div className='text-xs font-medium text-gray-500 dark:text-gray-400 mb-2'>Current Tags</div>
              <div className='flex flex-wrap gap-1'>
                {tags.map((tag) => (
                  <div
                    key={tag.id}
                    className='group flex items-center gap-1 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-2 py-0.5 rounded text-sm transition-colors'
                    style={{ backgroundColor: tag.color || undefined }}
                  >
                    {tag.name}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveTag(tag.id);
                      }}
                      className={`${
                        loadingTagId === tag.id ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
                      } hover:text-red-500 dark:hover:text-red-400 transition-opacity disabled:cursor-not-allowed`}
                      disabled={loadingTagId === tag.id}
                    >
                      {loadingTagId === tag.id ? (
                        <Loader2 className='w-3 h-3 animate-spin text-gray-500 dark:text-gray-400' />
                      ) : (
                        <X size={14} />
                      )}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Suggested Tags */}
          {suggestedTags.length > 0 && (
            <div>
              <div className='text-xs font-medium text-gray-500 dark:text-gray-400 mb-2'>Suggested Tags</div>
              <div className='flex flex-wrap gap-1'>
                {suggestedTags.slice(0, 10).map((tag) => (
                  <button
                    key={tag}
                    onClick={() => handleAddTag(tag)}
                    className='bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 px-2 py-0.5 rounded text-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
                    disabled={isLoading}
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* No tags message */}
          {tags.length === 0 && suggestedTags.length === 0 && (
            <div className='text-center text-gray-500 dark:text-gray-400 text-sm py-2'>
              No tags available. Create your first tag above.
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TagEditor;
