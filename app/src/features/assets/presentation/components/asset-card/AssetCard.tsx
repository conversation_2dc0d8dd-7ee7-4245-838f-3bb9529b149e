import React, { memo, useState, useEffect } from 'react';
import { Trash2, Layers, Tag, Folder, ExternalLink, FolderOpen, ChevronRight } from 'lucide-react';
import { AssetCollection } from '../../../domain/types';
import { AssetWithRelations } from '../../types/asset-with-relations';
import { TagEditor } from '../tag-editor/TagEditor';
import { CollectionPicker } from '../collection-picker/CollectionPicker';
import { imageCache } from '../../../../../features/canvas/presentation/utils';

// Type guard function
const isGeneratedAsset = (asset: AssetWithRelations): boolean => {
  return asset.isGenerated === true;
};

type Props = {
  asset: AssetWithRelations;
  onClick?: (asset: AssetWithRelations) => void;
  onDelete?: (asset: AssetWithRelations) => void;
  onUpdate?: () => void;
  allTags: string[];
  allCollections: AssetCollection[];
  className?: string;
};

export const AssetCard = ({ asset, onClick, onDelete, onUpdate, allTags, allCollections, className = '' }: Props) => {
  const handleClick = (e: React.MouseEvent) => {
    if (onClick) {
      e.preventDefault();
      onClick(asset);
    } else if (isCloudAsset && asset.cloudWebViewUrl) {
      // For cloud assets without custom onClick, open in cloud provider
      e.preventDefault();
      window.open(asset.cloudWebViewUrl, '_blank');
    }
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onDelete) {
      onDelete(asset);
    }
  };

  const isGenerated = isGeneratedAsset(asset);
  const fileName = isGenerated ? (asset as any).prompt : asset.fileName || 'Untitled';

  const category = isGenerated ? 'Generated' : asset.category;

  const isVariation = asset.isVariation;
  const hasVariations = asset.variations && asset.variations.length > 0;
  const variationCount = asset.variations?.length ?? 0;

  // Cloud asset detection
  const isCloudAsset = asset.source && asset.source !== 'uploaded';
  const isFolder = asset.fileType === 'folder';
  const cloudProvider = asset.source;

  // Get dynamic folder styling based on cloud provider
  const getFolderDisplayInfo = () => {
    if (!isFolder) return null;

    switch (cloudProvider) {
      case 'onedrive':
        return {
          icon: FolderOpen,
          color: 'text-blue-500 dark:text-blue-400',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-800',
          label: 'OneDrive Folder',
        };
      case 'dropbox':
        return {
          icon: FolderOpen,
          color: 'text-blue-600 dark:text-blue-400',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-800',
          label: 'Dropbox Folder',
        };
      default:
        return {
          icon: FolderOpen,
          color: 'text-[#676D50] dark:text-[#A6884C]',
          bgColor: 'bg-[#676D50]/10 dark:bg-[#A6884C]/10',
          borderColor: 'border-[#676D50]/20 dark:border-[#A6884C]/20',
          label: 'Folder',
        };
    }
  };

  const folderInfo = getFolderDisplayInfo();

  // Get cloud provider info
  const getCloudProviderInfo = (source: string) => {
    switch (source) {
      case 'google_drive':
        return { name: 'Google Drive', icon: '📁', color: 'bg-blue-500' };
      case 'onedrive':
        return { name: 'OneDrive', icon: '☁️', color: 'bg-blue-600' };
      case 'dropbox':
        return { name: 'Dropbox', icon: '📦', color: 'bg-blue-700' };
      default:
        return { name: 'Cloud', icon: '☁️', color: 'bg-gray-500' };
    }
  };

  const selectedUrl = isCloudAsset && asset.cloudThumbnailUrl ? asset.cloudThumbnailUrl : asset.fileUrl;

  const [image, setImage] = useState<HTMLImageElement | null>(() => {
    if (!selectedUrl) return null;
    return imageCache.getImage(selectedUrl);
  });

  useEffect(() => {
    if (!selectedUrl) return;

    const cached = imageCache.getImage(selectedUrl);
    if (cached) {
      setImage(cached);
      return;
    }

    let isMounted = true;
    imageCache
      .loadImage(selectedUrl)
      .then((img) => {
        if (isMounted) setImage(img);
      })
      .catch(() => {});

    return () => {
      isMounted = false;
    };
  }, [selectedUrl]);

  const cloudInfo = isCloudAsset ? getCloudProviderInfo(cloudProvider!) : null;

  console.log({ data: asset.fileUrl?.startsWith('google-drive:'), asset });

  return (
    <div className={`relative group ${className}`}>
      <div className='block cursor-pointer' onClick={handleClick}>
        <div
          className={`bg-white dark:bg-gray-800 border ${folderInfo ? folderInfo.borderColor : 'border-[#B5B178]/20 dark:border-gray-700'} rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 ${isFolder ? 'hover:scale-105' : ''}`}
        >
          <div
            className={`h-32 ${folderInfo ? folderInfo.bgColor : 'bg-gray-100 dark:bg-gray-700'} flex items-center justify-center relative`}
          >
            {isFolder && folderInfo ? (
              <div className='flex flex-col items-center gap-2'>
                <folderInfo.icon size={48} className={folderInfo.color} />
                <ChevronRight size={20} className={`${folderInfo.color} animate-pulse`} />
              </div>
            ) : image ? (
              <img src={image.src} alt={fileName || 'Asset'} className='w-full h-full object-cover' loading='lazy' />
            ) : (
              <div className='w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-700 text-xs text-gray-400'>
                Loading...
              </div>
            )}

            {/* Cloud asset overlay */}
            {isCloudAsset && (
              <div className='absolute inset-0 bg-black/10 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity'>
                <div className='bg-white/90 dark:bg-gray-800/90 rounded-full p-2'>
                  <ExternalLink size={16} className='text-gray-700 dark:text-gray-300' />
                </div>
              </div>
            )}

            {/* Status badges */}
            <div className='absolute top-2 left-2 flex flex-col gap-1'>
              {isCloudAsset && cloudInfo && (
                <div className={`${cloudInfo.color} text-white text-xs px-2 py-1 rounded-full flex items-center gap-1`}>
                  <span>{cloudInfo.icon}</span>
                  <span>{cloudInfo.name}</span>
                </div>
              )}
              {isGenerated && (
                <div className='bg-[#676D50] dark:bg-[#A6884C] text-white text-xs px-2 py-1 rounded-full'>
                  AI Generated
                </div>
              )}
              {isVariation && (
                <div className='bg-[#676D50] dark:bg-[#A6884C] text-white text-xs px-2 py-1 rounded-full'>
                  Variation
                </div>
              )}
              {hasVariations && (
                <div className='bg-[#676D50] dark:bg-[#A6884C] text-white text-xs px-2 py-1 rounded-full flex items-center gap-1'>
                  <Layers size={12} />
                  {variationCount}
                </div>
              )}
            </div>
          </div>

          <div className='p-3'>
            <h3 className='font-medium text-[#1F2419] dark:text-white truncate text-sm'>{fileName}</h3>
            <div className='flex justify-between items-center mt-1'>
              <p className={`text-xs ${folderInfo ? folderInfo.color : 'text-[#676D50] dark:text-[#A6884C]'}`}>
                {isFolder && folderInfo ? folderInfo.label : isCloudAsset ? cloudInfo?.name : category}
              </p>
              {asset.photographyModel && (
                <p className='text-xs text-gray-500 dark:text-gray-400'>{asset.photographyModel.name}</p>
              )}
              {isCloudAsset && asset.cloudPath && (
                <p className='text-xs text-gray-500 dark:text-gray-400 truncate max-w-20' title={asset.cloudPath}>
                  {asset.cloudPath}
                </p>
              )}
            </div>

            {/* Tags and Collections */}
            <div className='mt-2 flex items-center gap-2 text-xs text-[#676D50] dark:text-[#A6884C]'>
              {asset.tags && asset.tags.length > 0 && (
                <div className='flex items-center gap-1'>
                  <Tag size={12} className='text-[#676D50] dark:text-[#A6884C]' />
                  <span>{asset.tags.length}</span>
                </div>
              )}
              {asset.collections && asset.collections.length > 0 && (
                <div className='flex items-center gap-1'>
                  <Folder size={12} className='text-[#676D50] dark:text-[#A6884C]' />
                  <span>{asset.collections.length}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className='absolute top-2 right-2 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity'>
        {onDelete && (
          <button
            onClick={handleDelete}
            className='bg-red-500 dark:bg-red-600 text-white p-1 rounded-full hover:bg-red-600 dark:hover:bg-red-700 transition-colors'
            title='Delete asset'
          >
            <Trash2 size={16} />
          </button>
        )}
        <div onClick={(e) => e.preventDefault()} className='flex gap-1'>
          <TagEditor assetId={asset.id} tags={asset.tags || []} allTags={allTags} onUpdate={onUpdate} />
          <CollectionPicker
            assetId={asset.id}
            collections={asset.collections || []}
            allCollections={allCollections}
            onUpdate={onUpdate}
          />
        </div>
      </div>
    </div>
  );
};

export default memo(AssetCard);
