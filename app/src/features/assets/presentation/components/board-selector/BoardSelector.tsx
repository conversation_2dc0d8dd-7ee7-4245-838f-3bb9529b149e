import React, { useState } from "react";
import { ChevronDown, ChevronRight, Folder, Plus, X } from "lucide-react";
import { Board, BoardAsset } from "../../../domain/types";

// Extended Board type with relationships for UI components
interface BoardWithRelations extends Board {
	assets?: BoardAsset[];
	childBoards?: Board[];
	parentBoard?: Board | null;
}

interface BoardSelectorProps {
	boards: BoardWithRelations[];
	selectedBoardId?: string;
	onBoardSelect: (boardId: string) => void;
	onCreateBoard?: (name: string, parentId?: string) => void;
	allowCreation?: boolean;
	className?: string;
	showHeader?: boolean;
}

interface BoardTreeItemProps {
	board: BoardWithRelations;
	level: number;
	selectedBoardId?: string;
	onSelect: (boardId: string) => void;
	onCreateChild?: (name: string, parentId: string) => void;
	allowCreation?: boolean;
	children?: BoardWithRelations[];
}

const BoardTreeItem: React.FC<BoardTreeItemProps> = ({
	board,
	level,
	selectedBoardId,
	onSelect,
	onCreateChild,
	allowCreation = false,
	children = [],
}) => {
	const [isExpanded, setIsExpanded] = useState(true);
	const [isCreatingChild, setIsCreatingChild] = useState(false);
	const [newBoardName, setNewBoardName] = useState("");

	const hasChildren = children.length > 0;
	const paddingLeft = level * 16;
	const isSelected = board.id === selectedBoardId;

	const handleCreateChild = () => {
		if (newBoardName.trim() && onCreateChild) {
			onCreateChild(newBoardName.trim(), board.id);
			setNewBoardName("");
			setIsCreatingChild(false);
		}
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter") {
			handleCreateChild();
		} else if (e.key === "Escape") {
			setIsCreatingChild(false);
			setNewBoardName("");
		}
	};

	return (
		<div>
			{/* Board Item */}
			<div
				className={`flex items-center gap-2 py-2 px-3 rounded-lg cursor-pointer transition-colors ${
					isSelected
						? "bg-[#676D50] text-white"
						: "hover:bg-gray-100 dark:hover:bg-gray-700"
				}`}
				style={{ paddingLeft: `${paddingLeft + 12}px` }}
				onClick={() => onSelect(board.id)}
			>
				{/* Expand/Collapse Button */}
				{hasChildren && (
					<button
						onClick={(e) => {
							e.stopPropagation();
							setIsExpanded(!isExpanded);
						}}
						className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
					>
						{isExpanded ? (
							<ChevronDown size={16} />
						) : (
							<ChevronRight size={16} />
						)}
					</button>
				)}

				{/* Folder Icon */}
				<Folder size={16} className="text-[#676D50] dark:text-[#A6884C]" />

				{/* Board Name */}
				<span className="flex-1 text-sm font-medium truncate">
					{board.name}
				</span>

				{/* Asset Count */}
				{board.assets && (
					<span className="text-xs text-gray-500 dark:text-gray-400">
						{board.assets.length}
					</span>
				)}

				{/* Create Child Button */}
				{allowCreation && (
					<button
						onClick={(e) => {
							e.stopPropagation();
							setIsCreatingChild(true);
						}}
						className="p-1 opacity-0 group-hover:opacity-100 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-opacity"
						title="Create child board"
					>
						<Plus size={14} />
					</button>
				)}
			</div>

			{/* Create Child Form */}
			{isCreatingChild && (
				<div
					className="flex items-center gap-2 py-2 px-3 ml-4"
					style={{ paddingLeft: `${paddingLeft + 28}px` }}
				>
					<Folder size={16} className="text-gray-400" />
					<input
						type="text"
						value={newBoardName}
						onChange={(e) => setNewBoardName(e.target.value)}
						onKeyDown={handleKeyDown}
						placeholder="Board name"
						className="flex-1 text-sm bg-transparent border-b border-gray-300 dark:border-gray-600 focus:outline-none focus:border-[#676D50]"
						autoFocus
					/>
					<button
						onClick={handleCreateChild}
						className="p-1 text-green-600 hover:bg-green-100 dark:hover:bg-green-900 rounded"
						disabled={!newBoardName.trim()}
					>
						<Plus size={14} />
					</button>
					<button
						onClick={() => {
							setIsCreatingChild(false);
							setNewBoardName("");
						}}
						className="p-1 text-red-600 hover:bg-red-100 dark:hover:bg-red-900 rounded"
					>
						<X size={14} />
					</button>
				</div>
			)}

			{/* Children */}
			{hasChildren && isExpanded && (
				<div>
					{children.map((childBoard) => (
						<BoardTreeItem
							key={childBoard.id}
							board={childBoard}
							level={level + 1}
							selectedBoardId={selectedBoardId}
							onSelect={onSelect}
							onCreateChild={onCreateChild}
							allowCreation={allowCreation}
							children={childBoard.childBoards || []}
						/>
					))}
				</div>
			)}
		</div>
	);
};

export const BoardSelector: React.FC<BoardSelectorProps> = ({
	boards,
	selectedBoardId,
	onBoardSelect,
	onCreateBoard,
	allowCreation = false,
	className = "",
	showHeader = true,
}) => {
	const [isCreatingRoot, setIsCreatingRoot] = useState(false);
	const [newBoardName, setNewBoardName] = useState("");

	// Build board hierarchy
	const boardMap = new Map<string, BoardWithRelations>();
	boards.forEach((board) => boardMap.set(board.id, board));

	const rootBoards = boards.filter((board) => !board.parentBoardId);

	const getBoardChildren = (boardId: string): BoardWithRelations[] => {
		return boards.filter((board) => board.parentBoardId === boardId);
	};

	const handleCreateRoot = () => {
		if (newBoardName.trim() && onCreateBoard) {
			onCreateBoard(newBoardName.trim());
			setNewBoardName("");
			setIsCreatingRoot(false);
		}
	};

	const handleCreateChild = (name: string, parentId: string) => {
		if (onCreateBoard) {
			onCreateBoard(name, parentId);
		}
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter") {
			handleCreateRoot();
		} else if (e.key === "Escape") {
			setIsCreatingRoot(false);
			setNewBoardName("");
		}
	};

	return (
		<div className={`space-y-1 ${className}`}>
			{/* Header */}
			<div className="flex items-center justify-between">
				{showHeader && (
					<h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
						Boards
					</h3>
				)}
				{allowCreation && (
					<button
						onClick={() => setIsCreatingRoot(true)}
						className="p-1 text-[#676D50] dark:text-[#A6884C] hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
						title="Create new board"
					>
						<Plus size={16} />
					</button>
				)}
			</div>

			{/* Create Root Board Form */}
			{isCreatingRoot && (
				<div className="flex items-center gap-2 py-2 px-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
					<Folder size={16} className="text-gray-400" />
					<input
						type="text"
						value={newBoardName}
						onChange={(e) => setNewBoardName(e.target.value)}
						onKeyDown={handleKeyDown}
						placeholder="Board name"
						className="flex-1 text-sm bg-transparent border-b border-gray-300 dark:border-gray-600 focus:outline-none focus:border-[#676D50]"
						autoFocus
					/>
					<button
						onClick={handleCreateRoot}
						className="p-1 text-green-600 hover:bg-green-100 dark:hover:bg-green-900 rounded"
						disabled={!newBoardName.trim()}
					>
						<Plus size={14} />
					</button>
					<button
						onClick={() => {
							setIsCreatingRoot(false);
							setNewBoardName("");
						}}
						className="p-1 text-red-600 hover:bg-red-100 dark:hover:bg-red-900 rounded"
					>
						<X size={14} />
					</button>
				</div>
			)}

			{/* Board Tree */}
			<div className="space-y-1 group">
				{rootBoards.length === 0 && !isCreatingRoot ? (
					<div className="text-sm text-gray-500 dark:text-gray-400 text-center py-4">
						No boards found
						{allowCreation && (
							<button
								onClick={() => setIsCreatingRoot(true)}
								className="block mx-auto mt-2 text-[#676D50] dark:text-[#A6884C] hover:underline"
							>
								Create your first board
							</button>
						)}
					</div>
				) : (
					rootBoards.map((board) => (
						<BoardTreeItem
							key={board.id}
							board={board}
							level={0}
							selectedBoardId={selectedBoardId}
							onSelect={onBoardSelect}
							onCreateChild={allowCreation ? handleCreateChild : undefined}
							allowCreation={allowCreation}
							children={getBoardChildren(board.id)}
						/>
					))
				)}
			</div>
		</div>
	);
};

export default BoardSelector;
