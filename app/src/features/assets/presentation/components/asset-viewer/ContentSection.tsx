import React, { useState } from "react";
import { Tab, Comment } from "./types";
// import { InfoSection } from './InfoSection';
// import { VersionSection } from './VersionSection';
// import { BoardSection } from './BoardSection';
// import { CommentSection } from './CommentSection';
// import { ViewSection } from './ViewSection';
// import { SidebarNavigation } from './SidebarNavigation';
import { HeaderSection } from "./HeaderSection";
import { Asset } from "wasp/entities";
import { ViewSection } from "./ViewSection";
import { SidebarNavigation } from "./SidebarNavigation";
import { InfoSection } from "./InfoSection";
import { VersionSection } from "./VersionSection";
import { BoardSection } from "./BoardSection";
import { CommentSection } from "./CommentSection";

interface ContentSectionProps {
	asset: Asset & {
		fileUrl?: string;
		fileName?: string;
		fileType?: string;
		fileSize?: number;
		metadata?: Record<string, unknown>;
		organizationId?: string;
		versions?: Asset[];
		parentAsset?: (Asset & { versions: Asset[] }) | null;
		comments?: Comment[];
		tags?: any[];
		user?: any;
		BoardAsset?: {
			board: {
				id: string;
				name: string;
				_count: {
					assets: number;
				};
				assets: {
					asset: Asset;
				}[];
			};
		}[];
		settings?: any;
	};
	activeTab: Tab;
	setActiveTab: (tab: Tab) => void;
	isAdmin: boolean;
	comments: Comment[];
	setComments: React.Dispatch<React.SetStateAction<Comment[]>>;
	activeComment: string | null;
	setActiveComment: React.Dispatch<React.SetStateAction<string | null>>;
	newCommentMode: boolean;
	setNewCommentMode: React.Dispatch<React.SetStateAction<boolean>>;
	newCommentPos: { x: number; y: number };
	setNewCommentPos: React.Dispatch<
		React.SetStateAction<{ x: number; y: number }>
	>;
	imageRef: React.RefObject<HTMLDivElement>;
	handleImageClick: (e: React.MouseEvent<HTMLDivElement>) => void;
	sidebars: {
		label: string;
		icon: React.ElementType;
		value: Tab;
		show: boolean;
	}[];
	allowDownload?: boolean;
	openShareModal?: () => void;
	handleCommentAction?: () => boolean;
}

export const ContentSection: React.FC<ContentSectionProps> = ({
	asset,
	activeTab,
	setActiveTab,
	isAdmin,
	comments,
	setComments,
	activeComment,
	setActiveComment,
	newCommentMode,
	setNewCommentMode,
	newCommentPos,
	setNewCommentPos,
	imageRef,
	handleImageClick,
	sidebars,
	allowDownload,
	openShareModal,
	handleCommentAction,
}) => {
	// State for tracking hovered comment between image and sidebar
	const [hoveredCommentId, setHoveredCommentId] = useState<string | null>(null);

	return (
		<div className="flex flex-col h-[90vh]">
			<HeaderSection
				asset={asset}
				isAdmin={isAdmin}
				allowDownload={allowDownload}
				openShareModal={openShareModal}
			/>
			<div className="flex">
				<ViewSection
					asset={asset}
					imageRef={imageRef}
					comments={comments}
					setComments={setComments}
					activeComment={activeComment}
					setActiveComment={setActiveComment}
					newCommentMode={newCommentMode}
					setNewCommentMode={setNewCommentMode}
					newCommentPos={newCommentPos}
					setNewCommentPos={setNewCommentPos}
					handleImageClick={handleImageClick}
					hoveredCommentId={hoveredCommentId}
					setHoveredCommentId={setHoveredCommentId}
					handleCommentAction={handleCommentAction}
					settings={asset.settings}
				/>
				<div className="w-80 border-l border-primary-800 p-4 overflow-y-auto">
					{activeTab === Tab.Info && (
						<InfoSection asset={asset} isAdmin={isAdmin} />
					)}
					{activeTab === Tab.Versions && (
						<VersionSection
							versions={[
								...(asset?.parentAsset
									? [
											asset.parentAsset,
											...(asset.parentAsset?.versions ?? []),
										].filter((version) => version?.id !== asset.id)
									: []),
								...(asset?.versions || []),
							].sort(
								(a, b) =>
									new Date(b.uploadedAt).getTime() -
									new Date(a.uploadedAt).getTime(),
							)}
							isAdmin={isAdmin}
						/>
					)}
					{activeTab === Tab.Boards && (
						<BoardSection asset={asset} isAdmin={isAdmin} />
					)}
					{activeTab === Tab.Comments && (
						<CommentSection
							asset={{
								...asset,
								comments: asset.comments || [],
							}}
							isAdmin={isAdmin}
							hoveredCommentId={hoveredCommentId}
							setHoveredCommentId={setHoveredCommentId}
							handleCommentAction={handleCommentAction}
						/>
					)}
				</div>

				<SidebarNavigation
					activeTab={activeTab}
					setActiveTab={setActiveTab}
					sidebars={sidebars}
				/>
			</div>
		</div>
	);
};
