import React, { useState } from "react";
import toast from "react-hot-toast";
import { useQueryState } from "nuqs";
import { addAssetToBoard, getBoards, useQuery } from "wasp/client/operations";
import { useOrganizationState } from "../../../../../organization/store";
import BoardSelector from "../board-selector/BoardSelector";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
	Form,
	FormField,
	FormItem,
	FormLabel,
} from "../../../../../client/components/ui/form";
import {
	Button,
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "../../../../../client/components/ui/index";

const addToBoardSchema = z.object({
	boardId: z.string().optional(),
});

type AddToBoardFormValues = z.infer<typeof addToBoardSchema>;

export function AddAssetToBoard({ assetId }: { assetId: number }) {
	// const [isLoading, setIsLoading] = useState(false);
	const { selectedOrganizationId } = useOrganizationState();
	const [showModal, setShowModal] = useQueryState("add-to-board-modal", {
		defaultValue: "",
	});

	// Initialize React Hook Form
	const form = useForm<AddToBoardFormValues>({
		resolver: zodResolver(addToBoardSchema),
		defaultValues: {
			boardId: "",
		},
	});

	const { data: boards = [], isLoading } = useQuery(getBoards, {
		organizationId: selectedOrganizationId,
	});

	const handleAddToBoard = async (formValues: AddToBoardFormValues) => {
		if (!formValues.boardId) {
			toast.error("Please select a board");
			return;
		}

		try {
			await addAssetToBoard({
				assetId,
				boardId: formValues.boardId,
			});

			handleClose();
			toast.success("Asset added to board successfully");
		} catch (error) {
			toast.error("Failed to add asset to board");
			console.error("Error:", error);
		}
	};

	const handleClose = () => {
		setShowModal("");
		form.reset();
	};

	if (isLoading) {
		return <p>loading...</p>;
	}

	return (
		<Dialog open={!!showModal} onOpenChange={handleClose}>
			<DialogContent className="bg-[#F0EFE9] border-2 border-[#676D50] dark:bg-black">
				<DialogHeader>
					<DialogTitle>Add to Board</DialogTitle>
				</DialogHeader>

				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(handleAddToBoard)}
						className="space-y-6"
					>
						<FormField
							control={form.control}
							name="boardId"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Select Board</FormLabel>
									<div className="flex flex-col gap-2 p-3 rounded max-h-[50vh] overflow-y-auto">
										<BoardSelector
											boards={boards as any}
											selectedBoardId={field.value}
											onBoardSelect={(boardId) => field.onChange(boardId)}
											allowCreation={false}
											showHeader={false}
											className="max-h-48 overflow-y-auto"
										/>
									</div>
								</FormItem>
							)}
						/>

						<div className="flex justify-end gap-2 mt-4">
							<Button variant="outline" onClick={handleClose} type="button">
								Cancel
							</Button>
							<Button
								type="submit"
								disabled={isLoading || !form.watch("boardId")}
							>
								{isLoading ? "Adding..." : "Add to Board"}
							</Button>
						</div>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
