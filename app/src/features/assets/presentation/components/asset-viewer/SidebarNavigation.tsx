import React from "react";
import { Button } from "../../../../../client/components/ui/index";
import { cn } from "../../../../../shared/utils";
import { Tab } from "./types";

interface SidebarNavigationProps {
	activeTab: Tab;
	setActiveTab: (tab: Tab) => void;
	sidebars: {
		label: string;
		icon: React.ElementType;
		value: Tab;
		show: boolean;
	}[];
}

export const SidebarNavigation: React.FC<SidebarNavigationProps> = ({
	activeTab,
	setActiveTab,
	sidebars,
}) => {
	return (
		<div className="w-17 border-l border-border flex flex-col items-center p-4 gap-5">
			{sidebars.map((item) => {
				return (
					item?.show && (
						<Button
							key={item.value}
							variant="ghost"
							size="icon"
							className={cn(
								"rounded-full",
								activeTab === item.value &&
									"bg-accent text-accent-foreground ring-2 ring-primary/50",
							)}
							onClick={() => setActiveTab(item.value)}
						>
							<item.icon className="h-5 w-5" />
						</Button>
					)
				);
			})}
		</div>
	);
};
