import {
	Layout,
	Info,
	MessageSquare,
	FileStack,
	SquareMenu,
} from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import { useAuth } from "wasp/client/auth";
import { useQueryState } from "nuqs";
import { ContentSection } from "./ContentSection";
import { AssetViewerProps, Comment, Tab } from "./types";

export default function AssetViewer({
	asset,
	settings,
	openShareModal,
	handleCommentAction,
}: AssetViewerProps) {
	const { data: currentUser } = useAuth();
	const [activeTab, setActiveTab] = useQueryState("tab", {
		defaultValue: Tab.Info,
		parse: (value: string) => {
			return Object.values(Tab).includes(value as Tab)
				? (value as Tab)
				: Tab.Info;
		},
		shallow: true,
	});

	// Initialize comments from asset data or empty array
	const initialComments =
		asset.comments && asset.comments.length > 0
			? asset.comments.map((comment) => ({
					id: comment.id,
					x: comment.x,
					y: comment.y,
					text: comment.text,
					createdAt: comment.createdAt,
					author: comment.author,
					replies: comment.replies || [],
				}))
			: [];

	const [comments, setComments] = useState<Comment[]>(initialComments);
	const [activeComment, setActiveComment] = useState<string | null>(null);
	const [newCommentMode, setNewCommentMode] = useState(false);
	const [newCommentPos, setNewCommentPos] = useState({ x: 0, y: 0 });
	const imageRef = useRef<HTMLDivElement>(null);

	// Update comments when asset changes
	useEffect(() => {
		if (asset.comments && asset.comments.length > 0) {
			const formattedComments = asset.comments.map((comment) => ({
				id: comment.id,
				x: comment.x,
				y: comment.y,
				text: comment.text,
				createdAt: comment.createdAt,
				author: comment.author,
				replies: comment.replies || [],
			}));

			setComments(formattedComments);
		}
	}, [asset]);

	const isAdmin = !!currentUser;

	const { allowComments, allowDownload, showComments, showVersions } =
		settings || {};

	const SIDEBARS = [
		// {
		// 	label: "View",
		// 	icon: Layout,
		// 	value: Tab.View,
		// 	show: true,
		// },
		{
			label: "Info",
			icon: Info,
			value: Tab.Info,
			show: true,
		},
		{
			label: "Versions",
			icon: FileStack,
			value: Tab.Versions,
			show: Boolean(isAdmin || showVersions),
		},
		{
			label: "Comments",
			icon: MessageSquare,
			value: Tab.Comments,
			show: Boolean(isAdmin || allowComments || showComments),
		},
		{
			label: "Boards",
			icon: SquareMenu,
			value: Tab.Boards,
			show: true,
		},
	];

	useEffect(() => {
		const handleClickOutside = (e: MouseEvent) => {
			if (imageRef.current && !imageRef.current.contains(e.target as Node)) {
				setNewCommentMode(false);
				setActiveComment(null);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, []);

	// This function is now just a placeholder for any container-level click logic
	// The actual image click handling is done internally by the ViewSection component
	const handleImageClick = () => {
		// If we're trying to add a comment, check permissions first
		if (newCommentMode && handleCommentAction) {
			return handleCommentAction();
		}
		// ViewSection now handles the exact positioning logic internally
	};

	return (
		<ContentSection
			asset={asset}
			activeTab={activeTab}
			setActiveTab={setActiveTab}
			isAdmin={isAdmin}
			comments={comments}
			setComments={setComments}
			activeComment={activeComment}
			setActiveComment={setActiveComment}
			newCommentMode={newCommentMode}
			setNewCommentMode={setNewCommentMode}
			newCommentPos={newCommentPos}
			setNewCommentPos={setNewCommentPos}
			imageRef={imageRef}
			handleImageClick={handleImageClick}
			sidebars={SIDEBARS}
			allowDownload={allowDownload || false}
			openShareModal={openShareModal}
			handleCommentAction={handleCommentAction}
		/>
	);
}
