import React from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>rigger,
	Ta<PERSON>Content,
	Input,
} from "../../../../../client/components/ui/index";
import { InfoSectionProps } from "./types";
import { Badge } from "lucide-react";

export const InfoSection: React.FC<InfoSectionProps> = ({ asset, isAdmin }) => (
	<Tabs defaultValue="details" className="w-full">
		<TabsList className="w-full">
			<TabsTrigger value="details" className="flex-1">
				Details
			</TabsTrigger>
			<TabsTrigger value="activity" className="flex-1">
				Activity
			</TabsTrigger>
		</TabsList>
		<TabsContent value="details" className="mt-4">
			<div className="space-y-4">
				<div>
					<span className="text-sm text-gray-400">Tags</span>

					<div className="flex flex-wrap gap-2 mt-2">
						{asset.tags?.map((tag) => (
							<Badge key={tag.id}>{tag.name}</Badge>
						))}

						{isAdmin && (
							<Input
								//   value={newTag}
								//   onChange={(e) => setNewTag(e.target.value)}
								//   onKeyDown={handleAddTag}
								placeholder="Add tag..."
								className="w-full"
							/>
						)}
					</div>
				</div>
				<div>
					<span className="text-sm text-gray-400">Category</span>
					<p className="mt-1">{asset.category}</p>
				</div>
				<div>
					<span className="text-sm text-gray-400">Dimension</span>
					<p className="mt-1">
						{asset.height} x {asset.width}
					</p>
				</div>
				<div>
					<span className="text-sm text-gray-400">Uploaded at</span>
					<p className="mt-1">{new Date(asset.uploadedAt).toLocaleString()}</p>
				</div>
				<div>
					<span className="text-sm text-gray-400">Version</span>
					<p className="mt-1">{asset.version}</p>
				</div>
			</div>
		</TabsContent>
		<TabsContent value="activity">
			<div className="text-sm text-gray-400">No recent activity</div>
		</TabsContent>
	</Tabs>
);
