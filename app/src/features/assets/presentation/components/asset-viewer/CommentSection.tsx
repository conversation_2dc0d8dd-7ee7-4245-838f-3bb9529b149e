import React, { useState, useEffect, useCallback, memo } from "react";
import {
	Button,
	Label,
	Input,
} from "../../../../../client/components/ui/index";
import { useAuth } from "wasp/client/auth";
import { Plus, MessageSquare, Send, Badge, MessageCircle } from "lucide-react";
import {
	Avatar,
	AvatarFallback,
} from "../../../../../client/components/ui/avatar";
import { Textarea } from "../../../../../client/components/ui/textarea";
import { CommentSectionProps } from "./types";
import { formatRelativeTime } from "./utils";
import { toast } from "react-hot-toast";
import { useLocation } from "react-router-dom";
import { addComment, addCommentReply } from "wasp/client/operations";

// Define types for comment data
type CommentData = {
	id: string;
	text: string;
	x: number;
	y: number;
	createdAt: string;
	author: {
		username?: string | null;
	};
	replies?: ReplyData[];
};

type ReplyData = {
	id: string;
	text: string;
	createdAt: string;
	author: {
		username?: string | null;
	};
};

// Helper utilities to format author display
const getDisplayName = (username?: string | null): string => {
	if (!username) return "User";
	return username.split("@")[0];
};
const getInitials = (name: string): string =>
	name.substring(0, 2).toUpperCase();

const CommentSectionComponent: React.FC<CommentSectionProps> = ({
	asset,
	isAdmin,
	setHoveredCommentId,
	hoveredCommentId,
	handleCommentAction,
}) => {
	const { data: currentUser } = useAuth();
	const location = useLocation();
	const [newComment, setNewComment] = useState("");
	const [replyText, setReplyText] = useState<Record<string, string>>({});
	const [expandedComment, setExpandedComment] = useState<string | null>(null);
	const [isAddingComment, setIsAddingComment] = useState(false);
	const [comments, setComments] = useState<CommentData[]>(
		(asset.comments as unknown as CommentData[]) || [],
	);
	const [isSubmitting, setIsSubmitting] = useState(false);

	// Fetch comments when asset changes
	useEffect(() => {
		if (asset.comments) {
			setComments(asset.comments as unknown as CommentData[]);
		}
	}, [asset]);

	// Handle comment highlighting from URL hash
	useEffect(() => {
		const hash = location.hash;
		if (hash) {
			const commentId = hash.replace("#comment-", "");
			if (commentId) {
				// Find the comment or reply
				const findCommentOrReply = (
					comments: CommentData[],
				): { commentId: string; replyId?: string } | null => {
					for (const comment of comments) {
						if (comment.id === commentId) {
							return { commentId: comment.id };
						}
						if (comment.replies) {
							const reply = comment.replies.find((r) => r.id === commentId);
							if (reply) {
								return { commentId: comment.id, replyId: reply.id };
							}
						}
					}
					return null;
				};

				const result = findCommentOrReply(comments);
				if (result) {
					setExpandedComment(result.commentId);
					if (result.replyId) {
						// Scroll to the reply
						setTimeout(() => {
							const replyElement = document.getElementById(
								`reply-${result.replyId}`,
							);
							if (replyElement) {
								replyElement.scrollIntoView({
									behavior: "smooth",
									block: "center",
								});
							}
						}, 100);
					} else {
						// Scroll to the comment
						setTimeout(() => {
							const commentElement = document.getElementById(
								`comment-${result.commentId}`,
							);
							if (commentElement) {
								commentElement.scrollIntoView({
									behavior: "smooth",
									block: "center",
								});
							}
						}, 100);
					}
				}
			}
		}
	}, [location.hash, comments]);

	// Memoize the canUserComment function
	const canUserComment = useCallback(() => {
		if (!currentUser) return false;
		return handleCommentAction ? handleCommentAction() : true;
	}, [currentUser, handleCommentAction]);

	// Function to add a new comment
	const handleAddComment = async () => {
		if (!canUserComment()) {
			toast.error(
				"You need to be a member of the organization to comment on this asset",
			);
			return;
		}

		if (!newComment.trim() || !currentUser || !asset.organizationId) {
			toast.error(
				!currentUser
					? "You need to be logged in to add a comment."
					: !newComment.trim()
						? "Comment text cannot be empty."
						: "Missing organization information.",
			);
			return;
		}

		try {
			setIsSubmitting(true);

			// Call the backend operation to add a comment (default position for sidebar comments)
			const createdComment = await addComment({
				assetId: asset.id,
				text: newComment.trim(),
				x: 0, // Default position for sidebar comments
				y: 0, // Default position for sidebar comments
				organizationId: asset.organizationId,
			});

			// Format the new comment for UI
			const formattedComment = {
				id: createdComment.id,
				text: createdComment.text,
				x: createdComment.x,
				y: createdComment.y,
				createdAt: createdComment.createdAt,
				author: createdComment.author,
				replies: createdComment.replies,
			};

			setComments([...comments, formattedComment as unknown as CommentData]);
			setNewComment("");
			setIsAddingComment(false);
			toast.success("Comment added successfully!");
		} catch (error) {
			console.error("Error adding comment:", error);
			toast.error(
				`Failed to add comment: ${error instanceof Error ? error.message : "An unknown error occurred"}`,
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	// Function to add a reply to a comment
	const handleAddReply = async (commentId: string) => {
		// First check if user has permission to comment
		if (!canUserComment()) {
			toast.error(
				"You need to be a member of the organization to comment on this asset",
			);
			return;
		}

		if (
			!replyText[commentId]?.trim() ||
			!currentUser ||
			!asset.organizationId
		) {
			toast.error(
				!currentUser
					? "You need to be logged in to add a reply."
					: !replyText[commentId]?.trim()
						? "Reply text cannot be empty."
						: "Missing organization information.",
			);
			return;
		}

		try {
			setIsSubmitting(true);

			// Call the backend operation to add a reply
			const { comment: updatedComment } = await addCommentReply({
				commentId: commentId,
				text: replyText[commentId].trim(),
				organizationId: asset.organizationId,
			});

			// Update the comments list with the updated comment
			const updatedComments = comments.map((comment) => {
				if (comment.id === commentId) {
					return updatedComment as unknown as CommentData;
				}
				return comment;
			});

			setComments(updatedComments);
			setReplyText({ ...replyText, [commentId]: "" });
			toast.success("Reply added successfully!");
		} catch (error) {
			console.error("Error adding reply:", error);
			toast.error(
				`Failed to add reply: ${error instanceof Error ? error.message : "An unknown error occurred"}`,
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	// Use the formatRelativeTime function for formatting dates
	const formatDate = (date: Date | string) => {
		if (!date) return "";
		return formatRelativeTime(date);
	};

	// Handle mouse enter/leave for comment hover highlighting
	const handleCommentHover = (commentId: string) => {
		if (setHoveredCommentId) {
			setHoveredCommentId(commentId);
		}
	};

	const handleCommentLeave = () => {
		if (setHoveredCommentId) {
			setHoveredCommentId(null);
		}
	};

	return (
		<div className="mt-4 space-y-6">
			<div className="flex items-center justify-between">
				<div className="space-y-1">
					<Label className="text-base">Comments</Label>
					<p className="text-sm text-muted-foreground">
						{comments.length} comments
					</p>
				</div>
				{canUserComment() && (
					<Button
						variant="outline"
						size="sm"
						className="gap-2"
						onClick={() => setIsAddingComment(true)}
					>
						<Plus className="h-4 w-4" />
						Add Comment
					</Button>
				)}
			</div>

			{isAddingComment && (
				<div className="space-y-2 p-3 border border-border rounded-md">
					<Label htmlFor="new-comment">New Comment</Label>
					<Textarea
						id="new-comment"
						placeholder="Write your comment..."
						value={newComment}
						onChange={(e) => setNewComment(e.target.value)}
						className="min-h-[100px]"
					/>
					<div className="flex justify-end gap-2">
						<Button
							variant="ghost"
							size="sm"
							onClick={() => {
								setIsAddingComment(false);
								setNewComment("");
							}}
						>
							Cancel
						</Button>
						<Button
							size="sm"
							onClick={() => handleAddComment()}
							disabled={!newComment.trim() || isSubmitting}
						>
							{isSubmitting ? "Posting..." : "Post Comment"}
						</Button>
					</div>
				</div>
			)}

			<div className="space-y-4 h-[calc(90vh-14rem)] overflow-y-auto pr-2">
				{comments.length === 0 ? (
					<div className="text-center py-8 text-muted-foreground">
						<MessageSquare className="h-12 w-12 mx-auto mb-3 opacity-20" />
						<p>No comments yet</p>
						<p className="text-sm">Be the first to comment on this asset</p>
					</div>
				) : (
					comments.map((comment) => {
						const isPositioned = comment.x !== 0 || comment.y !== 0;
						const isHovered = hoveredCommentId === comment.id;
						const hasReplies = comment.replies && comment.replies.length > 0;
						const replyCount = hasReplies ? comment.replies!.length : 0;

						return (
							<div
								key={comment.id}
								id={`comment-${comment.id}`}
								className={`border ${
									isPositioned
										? isHovered
											? "border-primary bg-primary/10 shadow-md"
											: "border-border hover:border-primary/80 hover:bg-muted/50"
										: "border-border"
								} rounded-md overflow-hidden transition-all duration-200`}
								onMouseEnter={() =>
									isPositioned && handleCommentHover(comment.id)
								}
								onMouseLeave={handleCommentLeave}
							>
								<div
									className={`p-3 ${isPositioned && isHovered ? "bg-primary/5" : "bg-muted/50"} cursor-pointer`}
									onClick={() =>
										setExpandedComment(
											expandedComment === comment.id ? null : comment.id,
										)
									}
								>
									<div className="flex items-start gap-3">
										<Avatar className="h-8 w-8">
											<AvatarFallback>
												{getInitials(getDisplayName(comment.author?.username))}
											</AvatarFallback>
										</Avatar>
										<div className="flex-1">
											<div className="flex justify-between items-center">
												<p className="font-medium">
													{getDisplayName(comment.author?.username)}
												</p>
												<span className="text-xs text-muted-foreground">
													{formatDate(comment.createdAt)}
												</span>
											</div>
											<p className="mt-1 text-sm">{comment.text}</p>

											<div className="mt-2 flex items-center justify-between">
												{isPositioned && (
													<div className="text-xs text-muted-foreground flex items-center gap-1">
														<Badge className="h-3.5 w-3.5" />
														<span>
															{Math.round(comment.x)}%, {Math.round(comment.y)}%
														</span>
													</div>
												)}

												{hasReplies && (
													<div
														className={`flex items-center gap-1.5 text-xs font-medium px-2 py-1 rounded-full ml-auto ${
															expandedComment === comment.id
																? "bg-primary/20 text-primary"
																: "bg-muted/80 text-muted-foreground hover:bg-muted/70"
														} transition-colors`}
													>
														<MessageCircle
															className={`h-3.5 w-3.5 ${expandedComment === comment.id ? "text-primary" : ""}`}
														/>
														<span>
															{replyCount}{" "}
															{replyCount === 1 ? "reply" : "replies"}
														</span>
													</div>
												)}
											</div>
										</div>
									</div>
								</div>

								{expandedComment === comment.id && (
									<div className="p-3 border-t border-border text-muted-foreground">
										{hasReplies && (
											<div className="space-y-3 mb-4">
												<p className="text-xs text-muted-foreground flex items-center gap-2">
													<MessageCircle className="h-3.5 w-3.5" />
													<span>
														{replyCount}{" "}
														{replyCount === 1 ? "Reply" : "Replies"}
													</span>
												</p>
												{comment.replies!.map((reply) => (
													<div
														key={reply.id}
														id={`reply-${reply.id}`}
														className="flex items-start gap-2 pl-4 border-l-2 border-border"
													>
														<Avatar className="h-6 w-6">
															<AvatarFallback>
																{getInitials(
																	getDisplayName(reply.author?.username),
																)}
															</AvatarFallback>
														</Avatar>
														<div className="flex-1">
															<div className="flex justify-between items-center">
																<p className="text-xs font-medium">
																	{getDisplayName(reply.author?.username)}
																</p>
																<span className="text-xs text-muted-foreground">
																	{formatDate(reply.createdAt)}
																</span>
															</div>
															<p className="text-sm">{reply.text}</p>
														</div>
													</div>
												))}
											</div>
										)}

										{/* Only show reply input if user has permission */}
										{canUserComment() && (
											<div className="flex gap-2">
												<Input
													placeholder="Add a reply..."
													value={replyText[comment.id] || ""}
													onChange={(e) =>
														setReplyText({
															...replyText,
															[comment.id]: e.target.value,
														})
													}
													className="text-sm"
												/>
												<Button
													size="icon"
													onClick={() => handleAddReply(comment.id)}
													disabled={
														!replyText[comment.id]?.trim() || isSubmitting
													}
												>
													<Send className="h-4 w-4" />
												</Button>
											</div>
										)}
									</div>
								)}
							</div>
						);
					})
				)}
			</div>
		</div>
	);
};

export const CommentSection = memo(CommentSectionComponent);
