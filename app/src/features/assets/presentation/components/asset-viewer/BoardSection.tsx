import React from "react";
import { Button, Label } from "../../../../../client/components/ui/index";
import { Link, routes } from "wasp/client/router";
import { useQueryState } from "nuqs";
import { Plus } from "lucide-react";
import { BoardSectionProps } from "./types";
import { AddAssetToBoard } from "./addAssetToBoard";

export const BoardSection: React.FC<BoardSectionProps> = ({
	asset,
	isAdmin,
}) => {
	const [showModal, setShowModal] = useQueryState("add-to-board-modal", {
		defaultValue: "",
	});

	const boardsMap = new Map();
	asset?.BoardAsset?.forEach((boardAsset) => {
		if (boardAsset.board) {
			boardsMap.set(boardAsset.board.id, boardAsset.board);
		}
	});

	const uniqueBoards = Array.from(boardsMap.values());

	return (
		<>
			<div className="mt-4 space-y-6">
				<div className="flex items-center justify-between">
					<div className="space-y-1">
						<Label className="text-base">Boards ({uniqueBoards.length})</Label>
					</div>
					{isAdmin && (
						<Button
							variant="outline"
							size="sm"
							className="gap-2"
							onClick={() => setShowModal("true")}
						>
							<Plus className="h-4 w-4" />
							Add to Board
						</Button>
					)}
				</div>

				<div className="space-y-4 h-[calc(90vh-14rem)] overflow-y-auto pr-2">
					<div className="space-y-3">
						{uniqueBoards.map((board) => (
							<Link
								key={board.id}
								to={(routes as any).AssetBoardRoute?.to ?? "#"}
							>
								<div className="group flex items-center gap-3 p-2 rounded-md hover:bg-gray-800 transition-all cursor-pointer">
									<div className="w-12 h-12 rounded overflow-hidden flex-shrink-0">
										<img
											src={board?.assets?.[0]?.asset?.fileUrl}
											alt={board?.name}
											className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
										/>
									</div>
									<div className="flex-1">
										<p className="font-medium group-hover:text-white">
											{board.name}
										</p>
										<p className="text-xs text-gray-400">
											{board?._count?.assets} assets
										</p>
									</div>
								</div>
							</Link>
						))}
					</div>
				</div>
			</div>
			<AddAssetToBoard assetId={asset?.id} />
		</>
	);
};
