import React from "react";
import { Star, Share2, Download } from "lucide-react";
import { Button } from "../../../../../client/components/ui/button";

interface HeaderSectionProps {
	asset: any;
	isAdmin: boolean;
	allowDownload?: boolean;
	openShareModal?: () => void;
}

export const HeaderSection: React.FC<HeaderSectionProps> = ({
	asset,
	isAdmin,
	allowDownload,
	openShareModal,
}) => {
	return (
		<div className="flex items-center justify-between p-4 border-b border-border">
			<div className="flex items-center gap-4">
				<h2 className="text-lg font-medium">{asset.fileName}</h2>
				<div className="flex items-center text-sm text-muted-foreground">
					<span>by {asset?.user?.username}</span>
				</div>
			</div>
			<div className="flex items-center gap-4">
				<>
					<Button variant="ghost" size="icon">
						<Star className="h-5 w-5" />
					</Button>

					{openShareModal && (
						<Button
							variant="ghost"
							size="icon"
							onClick={() => openShareModal()}
						>
							<Share2 className="h-5 w-5" />
						</Button>
					)}
				</>

				{(allowDownload || isAdmin) && (
					<Button className="bg-primary hover:bg-primary/90">
						<Download className="h-5 w-5" />
					</Button>
				)}
			</div>
		</div>
	);
};
