import React from "react";
import { <PERSON><PERSON>, Label } from "../../../../../client/components/ui/index";
import { Link, routes } from "wasp/client/router";
import { useQueryState } from "nuqs";
// import { makeAsVersionCover } from "wasp/client/operations";
import { Plus } from "lucide-react";
import { VersionSectionProps, Tab } from "./types";

export const VersionSection: React.FC<VersionSectionProps> = ({
	versions = [],
	isAdmin,
}) => {
	const [activeTab] = useQueryState("tab", {
		defaultValue: Tab.Info,
		shallow: true,
	});
	const [_showUpload, setShowUpload] = useQueryState("upload-modal", {
		defaultValue: "",
		shallow: true,
	});

	const getReadableFormat = (fileType: string) => {
		const formats: Record<string, string> = {
			"image/jpeg": "JPEG",
			"image/png": "PNG",
			"image/gif": "GIF",
			"image/webp": "WebP",
			"image/svg+xml": "SVG",
		};
		return (
			formats[fileType] || fileType.split("/")[1]?.toUpperCase() || "Unknown"
		);
	};

	return (
		<div className="mt-4 space-y-6">
			<div className="flex items-center justify-between">
				<div className="space-y-1">
					<Label className="text-base">Versions</Label>
					<p className="text-sm text-gray-400">{versions.length} versions</p>
				</div>
				{isAdmin && (
					<Button
						variant="outline"
						size="sm"
						className="gap-2"
						onClick={() => setShowUpload("open")}
					>
						<Plus className="h-4 w-4" />
						Add Version
					</Button>
				)}
			</div>

			<div className="space-y-4 h-[calc(90vh-14rem)] overflow-y-auto pr-2">
				{versions.map((version) => (
					<div
						key={version.id}
						className="group relative rounded-lg border border-gray-800 bg-gray-900/50 hover:bg-gray-800/50 transition-all"
					>
						{isAdmin ? (
							<Link
								to={routes.AssetPreviewRoute.to}
								params={{ assetId: version.id } as any}
								search={{ tab: activeTab }}
								className="block relative"
							>
								<div className="aspect-[4/3] w-full overflow-hidden rounded-t-lg">
									<img
										src={version.fileUrl}
										alt={`Version ${version.version}`}
										className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
									/>
								</div>
								<div className="p-3 space-y-2">
									<div className="flex items-center justify-between">
										<span className="text-sm font-medium">
											Version {version.version}
										</span>
										{!version.isCover && isAdmin && (
											<div onClick={(e) => e.preventDefault()}>
												<Button
													variant="ghost"
													size="sm"
													className="h-8 px-3 hover:bg-gray-800 text-xs"
													onClick={() => {
														// makeAsVersionCover({
														// 	id: version.id,
														// });
													}}
												>
													Make Cover
												</Button>
											</div>
										)}
									</div>
									<div className="flex items-center gap-2 text-xs text-gray-400">
										<span>{getReadableFormat(version.fileType)}</span>
										<span>•</span>
										<span>
											{new Date(version.uploadedAt).toLocaleDateString()}
										</span>
									</div>
									{version.isCover && (
										<span className="inline-flex items-center rounded-full bg-blue-500/10 px-2 py-1 text-xs font-medium text-blue-400">
											Cover Version
										</span>
									)}
								</div>
							</Link>
						) : (
							<div className="block relative">
								<div className="aspect-[4/3] w-full overflow-hidden rounded-t-lg">
									<img
										src={version.fileUrl}
										alt={`Version ${version.version}`}
										className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
									/>
								</div>
								<div className="p-3 space-y-2">
									<div className="flex items-center justify-between">
										<span className="text-sm font-medium">
											Version {version.version}
										</span>
										{!version.isCover && isAdmin && (
											<div onClick={(e) => e.preventDefault()}>
												<Button
													variant="ghost"
													size="sm"
													className="h-8 px-3 hover:bg-gray-800 text-xs"
													onClick={() => {
														// makeAsVersionCover({
														// 	id: version.id,
														// });
													}}
												>
													Make Cover
												</Button>
											</div>
										)}
									</div>
									<div className="flex items-center gap-2 text-xs text-gray-400">
										<span>{getReadableFormat(version.fileType)}</span>
										<span>•</span>
										<span>
											{new Date(version.uploadedAt).toLocaleDateString()}
										</span>
									</div>
									{version.isCover && (
										<span className="inline-flex items-center rounded-full bg-blue-500/10 px-2 py-1 text-xs font-medium text-blue-400">
											Cover Version
										</span>
									)}
								</div>
							</div>
						)}
					</div>
				))}
			</div>
		</div>
	);
};

// Temporary stub until backend operation is available
function makeAsVersionCover(_args: { id: number }): void {
	// no-op
}
