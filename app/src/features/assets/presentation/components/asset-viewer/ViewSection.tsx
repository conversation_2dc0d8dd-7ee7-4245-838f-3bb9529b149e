import React, { useState, useRef, useEffect, useCallback, memo } from "react";
import { motion } from "framer-motion";
import { MessageSquare, X } from "lucide-react";
import { Comment } from "./types";
import { Asset } from "wasp/entities";
import { useAuth } from "wasp/client/auth";
import { toast } from "react-hot-toast";
import { useNotifications } from "../../../../../notification/useNotifications";
import { <PERSON><PERSON>, Card } from "../../../../../client/components/ui/index";
import {
	Avatar,
	AvatarImage,
	AvatarFallback,
} from "../../../../../client/components/ui/avatar";
import { Textarea } from "../../../../../client/components/ui/textarea";
import { formatRelativeTime } from "./utils";
import { addComment, addCommentReply } from "wasp/client/operations";

interface ViewSectionProps {
	asset: Asset & {
		fileUrl?: string;
		fileName?: string;
		fileType?: string;
		fileSize?: number;
		metadata?: Record<string, unknown>;
		organizationId?: string;
	};
	imageRef: React.RefObject<HTMLDivElement>;
	comments: Comment[];
	setComments: React.Dispatch<React.SetStateAction<Comment[]>>;
	activeComment: string | null;
	setActiveComment: React.Dispatch<React.SetStateAction<string | null>>;
	newCommentMode: boolean;
	setNewCommentMode: React.Dispatch<React.SetStateAction<boolean>>;
	newCommentPos: { x: number; y: number };
	setNewCommentPos: React.Dispatch<
		React.SetStateAction<{ x: number; y: number }>
	>;
	handleImageClick: (e: React.MouseEvent<HTMLDivElement>) => void;
	hoveredCommentId?: string | null;
	setHoveredCommentId?: React.Dispatch<React.SetStateAction<string | null>>;
	handleCommentAction?: () => boolean;
	settings?: {
		allowComments?: boolean;
		allowDownload?: boolean;
		showComments?: boolean;
		showVersions?: boolean;
	};
}

// Define types for the backend API responses
interface CommentAuthor {
	id: number;
	username: string | null;
	email: string | null;
	color?: string;
}

// UI specific extensions of the core types
interface UIAuthor {
	id: number;
	username?: string | null;
	email?: string | null;
	color?: string;
	// UI specific properties
	name: string;
	avatar: string;
	initials: string;
}

interface UIComment extends Omit<Comment, "author" | "createdAt" | "replies"> {
	author: UIAuthor;
	timestamp: string;
	replies: UIReply[];
}

interface UIReply {
	id: string;
	text: string;
	timestamp: string;
	author: UIAuthor;
}

// Helper function to convert API Comment to UI Comment
const toUIComment = (comment: Comment): UIComment => {
	// Derive a clean display name (strip anything after "@")
	const rawAuthorName =
		comment.author.username ?? comment.author.email ?? "User";
	const displayAuthorName = rawAuthorName.split("@")[0];
	const getInitials = (name: string) => name.substring(0, 2).toUpperCase();

	return {
		id: comment.id,
		x: comment.x,
		y: comment.y,
		text: comment.text,
		timestamp: formatRelativeTime(comment.createdAt),
		author: {
			id: comment.author.id,
			username: comment.author.username,
			email: comment.author.email,
			color: comment.author.color,
			name: displayAuthorName,
			avatar: "/placeholder-user.jpg",
			initials: getInitials(displayAuthorName || "U"),
		},
		replies: comment.replies.map((reply) => {
			const rawReplyName =
				reply.author.username ?? reply.author.email ?? "User";
			const displayReplyName = rawReplyName.split("@")[0];
			return {
				id: reply.id,
				text: reply.text,
				timestamp: formatRelativeTime(reply.createdAt),
				author: {
					id: reply.author.id,
					username: reply.author.username,
					email: reply.author.email,
					color: reply.author.color,
					name: displayReplyName,
					avatar: "/placeholder-user.jpg",
					initials: getInitials(displayReplyName || "U"),
				},
			};
		}),
	};
};

const ViewSectionComponent: React.FC<ViewSectionProps> = ({
	asset,
	imageRef,
	comments,
	setComments,
	activeComment,
	setActiveComment,
	newCommentMode,
	setNewCommentMode,
	newCommentPos,
	setNewCommentPos,
	handleImageClick: externalHandleImageClick,
	hoveredCommentId,
	setHoveredCommentId,
	handleCommentAction,
	settings,
}) => {
	const { data: currentUser } = useAuth();
	const [newCommentText, setNewCommentText] = useState("");
	const [replyText, setReplyText] = useState("");
	const [imageElement, setImageElement] = useState<HTMLImageElement | null>(
		null,
	);
	const [activePopupPosition, setActivePopupPosition] = useState<{
		top: boolean;
		right: boolean;
	} | null>(null);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const commentContainerRef = useRef<HTMLDivElement>(null);
	const newCommentRef = useRef<HTMLDivElement>(null);
	const imgContainerRef = useRef<HTMLDivElement>(null);
	const { sendCommentNotification } = useNotifications();

	// Convert API Comments to UI Comments
	const uiComments = comments.map(toUIComment);

	// Filter comments to only show positioned ones (x and y not equal to 0)
	const positionedComments = uiComments.filter(
		(comment) => comment.x !== 0 || comment.y !== 0,
	);

	// Add resize observer to update image dimensions when window resizes
	useEffect(() => {
		if (!imageElement) return;

		const updateImageDimensions = () => {
			// This function now just ensures the image is properly sized
			// We don't need to store dimensions since we're using percentages
		};

		// Initial update
		updateImageDimensions();

		// Create a ResizeObserver to detect changes in the image's dimensions
		const resizeObserver = new ResizeObserver(updateImageDimensions);

		// Observe the image element
		resizeObserver.observe(imageElement);

		// Observe the window resize to handle container changes
		window.addEventListener("resize", updateImageDimensions);

		return () => {
			if (imageElement) {
				resizeObserver.unobserve(imageElement);
			}
			window.removeEventListener("resize", updateImageDimensions);
		};
	}, [imageElement]);

	// Update popup position when active comment changes
	useEffect(() => {
		if (activeComment) {
			const activeCommentData = uiComments.find(
				(comment) => comment.id === activeComment,
			);

			if (activeCommentData) {
				// Determine if popup should open to the top or bottom and left or right
				// based on comment position relative to viewport
				setActivePopupPosition({
					top: activeCommentData.y > 50, // If in bottom half, open upward
					right: activeCommentData.x > 50, // If in right half, open to the left
				});
			}
		} else {
			setActivePopupPosition(null);
		}
	}, [activeComment, uiComments]);

	// Ensure new comment popup stays within the viewport
	useEffect(() => {
		if (newCommentMode && newCommentRef.current && imgContainerRef.current) {
			const popupRect = newCommentRef.current.getBoundingClientRect();
			const containerRect = imgContainerRef.current.getBoundingClientRect();

			// Check if popup extends beyond container boundaries
			const isOutOfBoundsRight = popupRect.right > containerRect.right;
			const isOutOfBoundsBottom = popupRect.bottom > containerRect.bottom;
			const isOutOfBoundsLeft = popupRect.left < containerRect.left;
			const isOutOfBoundsTop = popupRect.top < containerRect.top;

			// Apply corrections to popup position if needed
			if (
				isOutOfBoundsRight ||
				isOutOfBoundsLeft ||
				isOutOfBoundsBottom ||
				isOutOfBoundsTop
			) {
				const popup = newCommentRef.current;

				if (isOutOfBoundsBottom) {
					popup.style.top = "auto";
					popup.style.bottom = "100%";
					popup.style.marginTop = "0";
					popup.style.marginBottom = "10px";
				}

				if (isOutOfBoundsRight) {
					popup.style.left = "auto";
					popup.style.right = "0";
					popup.style.transform = "none";
				}

				if (isOutOfBoundsLeft) {
					popup.style.left = "0";
					popup.style.right = "auto";
					popup.style.transform = "none";
				}
			}
		}
	}, [newCommentMode]);

	// Memoize the handleCommentSubmit function
	const handleCommentSubmit = useCallback(async () => {
		// First check if commenting is allowed based on permissions
		if (handleCommentAction && !handleCommentAction()) {
			// User either needs to login or doesn't have permission to comment
			return;
		}

		if (!currentUser || !newCommentText.trim() || !asset.organizationId) {
			toast.error(
				!currentUser
					? "You need to be logged in to add a comment."
					: !newCommentText.trim()
						? "Comment text cannot be empty."
						: "Missing organization information.",
			);
			return;
		}

		try {
			setIsSubmitting(true);

			// Call the backend operation to add a comment
			const createdComment = await addComment({
				assetId: asset.id,
				text: newCommentText.trim(),
				x: newCommentPos.x,
				y: newCommentPos.y,
				organizationId: asset.organizationId,
			});

			// Only send notification if the asset owner is not the current user
			if (asset.userId && asset.userId !== currentUser.id) {
				await sendCommentNotification({
					userId: asset.userId,
					actorName: currentUser.username || "A user",
					commentText:
						newCommentText.substring(0, 50) +
						(newCommentText.length > 50 ? "..." : ""),
					assetId: asset.id,
				});
			}

			// Format the new comment for UI
			const formattedComment = {
				id: createdComment.id,
				text: createdComment.text,
				x: createdComment.x,
				y: createdComment.y,
				createdAt: createdComment.createdAt,
				author: createdComment.author,
				replies: createdComment.replies || [],
			};

			setComments([...comments, formattedComment as unknown as Comment]);
			setNewCommentText("");
			setNewCommentMode(false);
			toast.success("Comment added successfully!");
		} catch (error) {
			console.error("Error adding comment:", error);
			toast.error(
				`Failed to add comment: ${error instanceof Error ? error.message : "An unknown error occurred"}`,
			);
		} finally {
			setIsSubmitting(false);
		}
	}, [
		handleCommentAction,
		currentUser,
		newCommentText,
		asset,
		newCommentPos,
		comments,
		setComments,
		setNewCommentMode,
		sendCommentNotification,
	]);

	// Memoize the handleReplySubmit function
	const handleReplySubmit = useCallback(
		async (commentId: string) => {
			// First check if commenting is allowed based on permissions
			if (handleCommentAction && !handleCommentAction()) {
				// User either needs to login or doesn't have permission to comment
				return;
			}

			if (!currentUser || !replyText.trim() || !asset.organizationId) {
				toast.error(
					!currentUser
						? "You need to be logged in to add a reply."
						: !replyText.trim()
							? "Reply text cannot be empty."
							: "Missing organization information.",
				);
				return;
			}

			try {
				setIsSubmitting(true);

				// Call the backend operation to add a reply
				const { comment: updatedComment } = await addCommentReply({
					commentId: commentId,
					text: replyText.trim(),
					organizationId: asset.organizationId,
				});

				// Update the comments list with the updated comment
				const updatedComments = comments.map((comment) => {
					if (comment.id === commentId) {
						return updatedComment as unknown as Comment;
					}
					return comment;
				});

				setComments(updatedComments);
				setReplyText("");
				toast.success("Reply added successfully!");
			} catch (error) {
				console.error("Error adding reply:", error);
				toast.error(
					`Failed to add reply: ${error instanceof Error ? error.message : "An unknown error occurred"}`,
				);
			} finally {
				setIsSubmitting(false);
			}
		},
		[handleCommentAction, currentUser, replyText, asset, comments, setComments],
	);

	// Handle the click event specifically for placing comments
	const handleInternalImageClick = useCallback(
		(e: React.MouseEvent<HTMLDivElement | HTMLImageElement>) => {
			if (!imageElement || activeComment) return;

			e.stopPropagation();

			// First check if commenting is allowed via the permission handler
			if (handleCommentAction && !handleCommentAction()) {
				// Permission check failed - still show the comment UI with appropriate message
				const imgRect = imageElement.getBoundingClientRect();
				const clickX = e.clientX;
				const clickY = e.clientY;

				if (
					clickX >= imgRect.left &&
					clickX <= imgRect.right &&
					clickY >= imgRect.top &&
					clickY <= imgRect.bottom
				) {
					const percentX = ((clickX - imgRect.left) / imgRect.width) * 100;
					const percentY = ((clickY - imgRect.top) / imgRect.height) * 100;
					setNewCommentPos({ x: percentX, y: percentY });

					setTimeout(() => {
						setNewCommentMode(true);
					}, 10);
				}
				return;
			}

			// First, clear any existing comment mode to ensure clean state
			setNewCommentMode(false);

			// Get the image's rendered dimensions and position
			const imgRect = imageElement.getBoundingClientRect();

			// Calculate the exact point of click relative to the image itself
			const clickX = e.clientX;
			const clickY = e.clientY;

			// Only add comment if click is within image boundaries
			if (
				clickX >= imgRect.left &&
				clickX <= imgRect.right &&
				clickY >= imgRect.top &&
				clickY <= imgRect.bottom
			) {
				// Calculate the percentage position within the image
				const percentX = ((clickX - imgRect.left) / imgRect.width) * 100;
				const percentY = ((clickY - imgRect.top) / imgRect.height) * 100;

				// Set the new comment position
				setNewCommentPos({ x: percentX, y: percentY });

				// Wait for position to be applied before showing comment input
				setTimeout(() => {
					setNewCommentMode(true);
				}, 10);
			}
		},
		[
			imageElement,
			activeComment,
			handleCommentAction,
			setNewCommentPos,
			setNewCommentMode,
		],
	);

	// We need to check if the click is within the actual image bounds
	const handleContainerClick = useCallback(
		(e: React.MouseEvent<HTMLDivElement>) => {
			if (!imageElement) return;

			// For any click actions that need to be handled by parent
			externalHandleImageClick(e);
		},
		[imageElement, externalHandleImageClick],
	);

	const handleImageLoad = useCallback(
		(e: React.SyntheticEvent<HTMLImageElement>) => {
			const img = e.currentTarget;
			setImageElement(img);
		},
		[],
	);

	// Handle hover interactions with comments
	const handleCommentHover = useCallback(
		(commentId: string) => {
			if (setHoveredCommentId) {
				setHoveredCommentId(commentId);
			}
		},
		[setHoveredCommentId],
	);

	const handleCommentLeave = useCallback(() => {
		if (setHoveredCommentId) {
			setHoveredCommentId(null);
		}
	}, [setHoveredCommentId]);

	return (
		<div
			ref={imageRef}
			onClick={handleContainerClick}
			className="flex-1 relative flex items-center justify-center p-4"
		>
			<div
				ref={imgContainerRef}
				className="relative"
				style={{ maxWidth: "100%", maxHeight: "100%" }}
			>
				<motion.img
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					src={asset?.fileUrl}
					alt="Full size preview"
					className="max-w-full max-h-[calc(90vh-8rem)] w-auto h-auto object-contain"
					style={{
						aspectRatio: "auto",
					}}
					onLoad={handleImageLoad}
					onClick={handleInternalImageClick}
				/>

				{/* Only show positioned comments when image is loaded */}
				{imageElement &&
					positionedComments.map((comment) => (
						<div
							key={comment.id}
							className={`absolute z-10 transform -translate-x-1/2 -translate-y-1/2 b-re`}
							style={{
								left: `${comment.x}%`,
								top: `${comment.y}%`,
							}}
							onMouseEnter={() => handleCommentHover(comment.id)}
							onMouseLeave={handleCommentLeave}
						>
							<button
								className={`flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium transition-all duration-200
                  ${
										activeComment === comment.id
											? "bg-primary text-primary-foreground ring-2 ring-primary-foreground"
											: hoveredCommentId === comment.id
												? "bg-primary scale-110 text-primary-foreground ring-2 ring-primary-foreground"
												: "bg-primary/80 text-primary-foreground hover:bg-primary"
									}`}
								onClick={(e) => {
									e.stopPropagation();
									setActiveComment(
										activeComment === comment.id ? null : comment.id,
									);
									setNewCommentMode(false);
								}}
							>
								{comment.id.length > 2 ? comment.author.initials : comment.id}
							</button>

							{activeComment === comment.id && activePopupPosition && (
								<Card
									ref={commentContainerRef}
									className={`absolute w-72 p-3 shadow-lg z-20 bg-[#F0EFE9] dark:bg-primary border border-gray-200 dark:border-gray-800`}
									style={{
										[activePopupPosition.top ? "bottom" : "top"]: "100%",
										[activePopupPosition.right ? "right" : "left"]: "0",
										marginTop: activePopupPosition.top ? "" : "10px",
										marginBottom: activePopupPosition.top ? "10px" : "",
										transform: `translate(${activePopupPosition.right ? "50%" : "-50%"}, 0)`,
									}}
								>
									<div className="flex items-start gap-2 mb-3">
										<Avatar className="w-8 h-8 bg-primary">
											<AvatarImage
												src={comment.author.avatar}
												alt={comment.author.name}
											/>
											<AvatarFallback>{comment.author.initials}</AvatarFallback>
										</Avatar>
										<div className="flex-1">
											<div className="flex justify-between">
												<p className="text-sm font-medium">
													{comment.author.name}
												</p>
												<span className="text-xs text-muted-foreground">
													{comment.timestamp}
												</span>
											</div>
											<p className="text-sm mt-1">{comment.text}</p>
										</div>
									</div>

									{comment.replies.length > 0 && (
										<div className="pl-8 border-l-2 border-muted mb-3">
											{comment.replies.map((reply) => (
												<div
													key={reply.id}
													className="flex items-start gap-2 mt-3"
												>
													<Avatar className="w-6 h-6 bg-primary">
														<AvatarImage
															src={reply.author.avatar}
															alt={reply.author.name}
														/>
														<AvatarFallback>
															{reply.author.initials}
														</AvatarFallback>
													</Avatar>
													<div className="flex-1">
														<div className="flex justify-between">
															<p className="text-xs font-medium">
																{reply.author.name}
															</p>
															<span className="text-xs text-muted-foreground">
																{reply.timestamp}
															</span>
														</div>
														<p className="text-xs mt-0.5">{reply.text}</p>
													</div>
												</div>
											))}
										</div>
									)}

									<div className="flex gap-2">
										{!currentUser ? (
											<div className="w-full bg-gray-800 rounded p-2 text-center">
												<p className="text-xs mb-2 text-gray-400">
													You need to login to reply
												</p>
												<a
													href="/login"
													className="text-xs font-medium bg-primary hover:bg-primary/90 text-white px-3 py-1 rounded inline-block"
													onClick={(e) => e.stopPropagation()}
												>
													Login
												</a>
											</div>
										) : handleCommentAction && !handleCommentAction() ? (
											<div className="w-full bg-gray-800 rounded p-3 text-center mb-2">
												<p className="text-sm text-gray-400">
													You need to be a member of the organization to comment
													on this asset
												</p>
											</div>
										) : (
											<>
												<input
													placeholder="Add a reply..."
													value={replyText}
													onChange={(e) => setReplyText(e.target.value)}
													className="text-sm flex-1 bg-transparent border border-gray-700 rounded px-2 py-1"
													onClick={(e) => e.stopPropagation()}
												/>
												<Button
													size="icon"
													onClick={(e) => {
														e.stopPropagation();
														handleReplySubmit(comment.id);
													}}
													disabled={!replyText.trim() || isSubmitting}
												>
													<span className="sr-only">Send</span>
													<svg
														xmlns="http://www.w3.org/2000/svg"
														viewBox="0 0 24 24"
														fill="none"
														stroke="currentColor"
														strokeWidth="2"
														strokeLinecap="round"
														strokeLinejoin="round"
														className="h-4 w-4"
													>
														<path d="m22 2-7 20-4-9-9-4Z" />
														<path d="M22 2 11 13" />
													</svg>
												</Button>
											</>
										)}
									</div>
								</Card>
							)}
						</div>
					))}

				{newCommentMode && (
					<div
						className="absolute z-10 transform -translate-x-1/2 -translate-y-1/2"
						style={{
							left: `${newCommentPos.x}%`,
							top: `${newCommentPos.y}%`,
							pointerEvents: "auto",
						}}
					>
						<div className="flex items-center justify-center w-6 h-6 rounded-full bg-primary text-primary-foreground">
							<MessageSquare className="h-3 w-3" />
						</div>
						<Card
							ref={newCommentRef}
							className="absolute mt-2 w-72 p-3 shadow-lg z-99999 bg-[#F0EFE9] dark:bg-primary"
							style={{
								top: "100%",
								left: "50%",
								transform: "translateX(-50%)",
							}}
							onClick={(e) => e.stopPropagation()}
						>
							<div className="flex justify-between items-center mb-2">
								<p className="text-sm font-medium">New Comment</p>
								<Button
									variant="ghost"
									size="icon"
									className="h-6 w-6"
									onClick={(e) => {
										e.stopPropagation();
										setNewCommentMode(false);
									}}
								>
									<X className="h-4 w-4" />
								</Button>
							</div>

							{!currentUser ? (
								<div className="w-full bg-gray-800 rounded p-3 text-center mb-2">
									<p className="text-sm mb-2 text-gray-400">
										You need to login to add comments
									</p>
									<a
										href="/login"
										className="text-sm font-medium bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded inline-block"
										onClick={(e) => e.stopPropagation()}
									>
										Login
									</a>
								</div>
							) : handleCommentAction && !handleCommentAction() ? (
								<div className="w-full bg-gray-800 rounded p-3 text-center mb-2">
									<p className="text-sm text-gray-400">
										You need to be a member of the organization to comment on
										this asset
									</p>
								</div>
							) : (
								<>
									<Textarea
										placeholder="Add your comment..."
										value={newCommentText}
										onChange={(e) => setNewCommentText(e.target.value)}
										className="min-h-[80px] text-sm mb-2"
										autoFocus
										onClick={(e) => e.stopPropagation()}
									/>

									<div className="flex justify-end">
										<Button
											size="sm"
											onClick={(e) => {
												e.stopPropagation();
												handleCommentSubmit();
											}}
											disabled={!newCommentText.trim() || isSubmitting}
										>
											{isSubmitting ? "Posting..." : "Post Comment"}
										</Button>
									</div>
								</>
							)}
						</Card>
					</div>
				)}
			</div>
		</div>
	);
};

export const ViewSection = memo(ViewSectionComponent);
