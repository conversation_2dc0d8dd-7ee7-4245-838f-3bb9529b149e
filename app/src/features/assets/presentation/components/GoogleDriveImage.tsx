import React, { useState } from 'react';
import { useAuth } from 'wasp/client/auth';

interface GoogleDriveImageProps {
  fileId: string;
  alt?: string;
  className?: string;
  loading?: 'lazy' | 'eager';
  size?: 'small' | 'medium' | 'large';
}

export const GoogleDriveImage: React.FC<GoogleDriveImageProps> = ({
  fileId,
  alt = 'Google Drive Image',
  className = '',
  loading = 'lazy',
  size = 'medium',
}) => {
  const { data: user } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  if (!user) {
    return (
      <div className={`bg-gray-100 dark:bg-gray-800 flex items-center justify-center ${className}`}>
        <div className='text-gray-400 dark:text-gray-500 text-sm text-center p-2'>
          <div className='mb-1'>🔒</div>
          <div>Login required</div>
        </div>
      </div>
    );
  }

  // Construct the thumbnail URL using the new fast API
  const thumbnailUrl = `/api/google-drive/thumbnail?fileId=${fileId}&userId=${user.id}&size=${size}`;

  // Debug logging
  console.log('GoogleDriveImage rendering:', { fileId, userId: user.id, size, thumbnailUrl });

  const handleLoad = () => {
    setIsLoading(false);
    setError(null);
  };

  const handleError = (e: any) => {
    setIsLoading(false);
    console.error('Failed to load Google Drive thumbnail:', thumbnailUrl, e);
    setError('Failed to load image');
  };

  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className='absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse flex items-center justify-center'>
          <div className='text-gray-400 dark:text-gray-500 text-sm'>Loading...</div>
        </div>
      )}

      {error && (
        <div className='absolute inset-0 bg-gray-100 dark:bg-gray-800 flex items-center justify-center'>
          <div className='text-gray-400 dark:text-gray-500 text-sm text-center p-2'>
            <div className='mb-1'>⚠️</div>
            <div>Failed to load</div>
          </div>
        </div>
      )}

      <img
        src={thumbnailUrl}
        alt={alt}
        className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}
        loading={loading}
        onLoad={handleLoad}
        onError={handleError}
      />
    </div>
  );
};

export default GoogleDriveImage;
