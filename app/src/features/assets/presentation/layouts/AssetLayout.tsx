import React from "react";
import {
	SidebarInset,
	SidebarProvider,
} from "../../../../client/components/ui/sidebar";
import { FileUpload } from "../../../../client/components/file-upload";
import { ActiveSessionChecker } from "../../../onboarding/infrastructure/hooks/useActiveSessionRedirect";
import { useAssetSidebar } from "../hooks/useAssetSidebar";
import { AssetSidebar } from "../components";

export function AssetLayout({ children }: { children: React.ReactNode }) {
	const {
		currentSection,
		selectedTags,
		selectedCollections,
		viewMode,
		cloudConnections,
		collections,
		handleUploadClick,
		handleSectionChange,
		handleSearchChange,
		handleTagFilter,
		handleCollectionFilter,
		handleViewModeChange,
		tags,
		boards,
	} = useAssetSidebar();

	return (
		<ActiveSessionChecker>
			<div className={`flex h-screen bg-[#F0EFE9] dark:bg-gray-900`}>
				{/* <SidebarProvider className="bg-[#F0EFE9] dark:bg-gray-900"> */}
				<div className="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
					<AssetSidebar
						onUploadClick={handleUploadClick}
						onSectionChange={handleSectionChange}
						onSearchChange={handleSearchChange}
						onTagFilter={handleTagFilter}
						onCollectionFilter={handleCollectionFilter}
						onViewModeChange={handleViewModeChange}
						collections={collections}
						tags={tags}
						boards={boards}
						cloudConnections={cloudConnections}
						currentSection={currentSection}
						currentViewMode={viewMode}
						selectedTags={selectedTags}
						selectedCollections={selectedCollections}
					/>
				</div>

				<SidebarInset>{children}</SidebarInset>
				<FileUpload />
				{/* </SidebarProvider> */}
			</div>
		</ActiveSessionChecker>
	);
}
