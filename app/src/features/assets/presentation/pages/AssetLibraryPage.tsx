import React from "react";
import { AssetLibrarySections } from "../components/asset-sidebar";
import { AssetGrid } from "../components/asset-grid";
import { useAssetLibrary } from "../hooks/use-asset-libary";
import { AssetLayout } from "../layouts/AssetLayout";
import { AssetWithRelations } from "../types/asset-with-relations";
import type { AssetDisplayItem } from "../../domain/types/board.types";

// Type guard to check if asset is AssetWithRelations
const isAssetWithRelations = (
	asset: AssetWithRelations | AssetDisplayItem,
): asset is AssetWithRelations => {
	return !("details" in asset);
};

export const AssetLibraryPage: React.FC = () => {
	const {
		currentSection,
		selectedTags,
		selectedCollections,
		viewMode,
		currentFolderPath,
		breadcrumbs,
		getSectionTitle,
		getEmptyMessage,
		assets,
		isLoading,
		collections,
		debouncedSearchQuery,
		page,
		limit,
		totalAssets,
		isCurrentSectionCloud,
		canGoToPrevCloudPage,
		canGoToNextCloudPage,
		hasMoreCloudAssets,
		tags,
		handleAssetClick,
		handleAssetDelete,
		handleUpdate,
		handleCloudPrevPage,
		handleCloudNextPage,
		isNavigatingFolder,
		currentCloudPageIndex,
		handleNavigateToFolder,
		setPage,
	} = useAssetLibrary({ limit: 20 });

	// Wrapper functions to handle GridAsset union type
	const handleGridAssetClick = (
		asset: AssetWithRelations | AssetDisplayItem,
		event?: React.MouseEvent,
	) => {
		if (isAssetWithRelations(asset)) {
			return handleAssetClick(asset, event);
		}
		// AssetDisplayItem should not occur in AssetLibraryPage, but handle gracefully
		console.warn("Unexpected AssetDisplayItem in AssetLibraryPage");
	};

	const handleGridAssetDelete = (
		asset: AssetWithRelations | AssetDisplayItem,
	) => {
		if (isAssetWithRelations(asset)) {
			return handleAssetDelete(asset);
		}
		// AssetDisplayItem should not occur in AssetLibraryPage, but handle gracefully
		console.warn("Unexpected AssetDisplayItem in AssetLibraryPage");
		return Promise.resolve({ success: false, error: "Invalid asset type" });
	};

	return (
		<AssetLayout>
			{/* Main Content */}
			<div className="flex-1 flex flex-col overflow-hidden">
				{/* Header */}
				<div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
					<div className="flex items-center justify-between">
						<div>
							<h1 className="text-2xl font-bold text-[#676D50] dark:text-[#A6884C]">
								{getSectionTitle()}
							</h1>

							{/* Breadcrumb navigation for cloud folders */}
							{isCurrentSectionCloud &&
								(breadcrumbs.length > 0 || currentFolderPath) && (
									<div className="flex items-center space-x-2 mt-2 mb-2">
										<button
											onClick={() => handleNavigateToFolder("")}
											className="text-[#676D50] hover:text-[#849068] text-sm font-medium"
										>
											Root
										</button>
										{breadcrumbs.map((folder, index) => {
											const path = breadcrumbs.slice(0, index + 1).join("/");
											const isLast = index === breadcrumbs.length - 1;
											return (
												<div
													key={index}
													className="flex items-center space-x-2"
												>
													<span className="text-gray-400">/</span>
													{isLast ? (
														<span className="text-sm text-gray-600 dark:text-gray-400 font-medium">
															{folder}
														</span>
													) : (
														<button
															onClick={() => handleNavigateToFolder(path)}
															className="text-[#676D50] hover:text-[#849068] text-sm font-medium"
														>
															{folder}
														</button>
													)}
												</div>
											);
										})}
									</div>
								)}

							<p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
								{totalAssets} asset{totalAssets !== 1 ? "s" : ""}
								{debouncedSearchQuery && ` matching "${debouncedSearchQuery}"`}
								{selectedTags.length > 0 &&
									` with ${selectedTags.length} tag${selectedTags.length !== 1 ? "s" : ""}`}
								{selectedCollections.length > 0 &&
									` in ${selectedCollections.length} collection${selectedCollections.length !== 1 ? "s" : ""}`}
							</p>
						</div>

						{/* Additional header actions could go here */}
					</div>
				</div>

				{/* Content Area */}
				<div className="flex-1 overflow-y-auto p-6 relative">
					{/* Folder navigation loading overlay */}
					{isNavigatingFolder && (
						<div className="absolute inset-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm z-50 flex items-center justify-center">
							<div className="flex flex-col items-center gap-3">
								<div className="w-8 h-8 border-2 border-[#676D50] border-t-transparent rounded-full animate-spin"></div>
								<p className="text-sm text-[#676D50] dark:text-[#A6884C] font-medium">
									Loading folder contents...
								</p>
							</div>
						</div>
					)}

					<AssetGrid
						assets={assets}
						mode={viewMode}
						onAssetClick={handleGridAssetClick}
						onAssetDelete={
							currentSection !== AssetLibrarySections.RECENTLY_DELETED
								? handleGridAssetDelete
								: undefined
						}
						onUpdate={handleUpdate}
						allTags={tags.map((t) => t.name)}
						allCollections={collections}
						loading={isLoading || isNavigatingFolder}
						emptyMessage={getEmptyMessage()}
						gridCols={{
							default: 2,
							sm: 3,
							md: 4,
							lg: 5,
							xl: 6,
						}}
					/>

					{/* Pagination */}
					{isCurrentSectionCloud
						? // Cloud assets pagination
							(canGoToPrevCloudPage || canGoToNextCloudPage) && (
								<div className="mt-8 flex justify-center">
									<div className="flex items-center gap-2">
										<button
											onClick={handleCloudPrevPage}
											disabled={!canGoToPrevCloudPage}
											className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
										>
											Previous
										</button>
										<span className="px-3 py-2 text-sm text-gray-600 dark:text-gray-400">
											Page {currentCloudPageIndex + 1}
											{hasMoreCloudAssets ? "+" : ""}
										</span>
										<button
											onClick={handleCloudNextPage}
											disabled={!canGoToNextCloudPage}
											className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
										>
											Next
										</button>
									</div>
								</div>
							)
						: // Local assets pagination
							totalAssets > limit && (
								<div className="mt-8 flex justify-center">
									<div className="flex items-center gap-2">
										<button
											onClick={() => setPage((p) => Math.max(1, p - 1))}
											disabled={page === 1}
											className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
										>
											Previous
										</button>
										<span className="px-3 py-2 text-sm text-gray-600 dark:text-gray-400">
											Page {page} of {Math.ceil(totalAssets / limit)}
										</span>
										<button
											onClick={() => setPage((p) => p + 1)}
											disabled={page >= Math.ceil(totalAssets / limit)}
											className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
										>
											Next
										</button>
									</div>
								</div>
							)}
				</div>
			</div>
		</AssetLayout>
	);
};

export default AssetLibraryPage;
