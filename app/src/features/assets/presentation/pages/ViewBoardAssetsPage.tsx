import React from "react";
import { useNavigate } from "react-router-dom";
import { AssetGrid } from "../components/asset-grid";
import {
	Tabs,
	TabsList,
	TabsTrigger,
} from "../../../../client/components/ui/tabs";
import { LayoutGrid, List, Columns } from "lucide-react";

import { KanbanView } from "../components/asset-board-view/KanbanView";
import { AssetListView } from "../components/asset-board-view/ListView";
import { AssetLayout } from "../layouts/AssetLayout";
import { useBoardView } from "../hooks/useBoardView";

export function ViewBoardAssetsPage() {
	const navigate = useNavigate();

	// Use the focused board view hook
	const {
		boardData,
		viewType,
		setViewType,
		displayAssets,
		handleLaneChange,
		refetch,
	} = useBoardView();

	// Handler for clicking on asset or board
	const handleAssetOrBoardClick = (item: any) => {
		if (item.details?.type === "board") {
			navigate(`/assets/board/${item.id}`);
		} else {
			navigate(`/asset/${item.id}`);
		}
	};

	console.log({ displayAssets });

	return (
		<AssetLayout>
			<div className="p-6">
				<div className="mb-4 ml-2">
					<h1 className="text-2xl font-bold mb-4">
						Board: {boardData?.name || "Loading..."}
					</h1>
					<p className="text-sm text-gray-500">
						{boardData?.description || "No description"}
					</p>
				</div>

				{/* View Type Selector */}
				<Tabs
					value={viewType}
					onValueChange={(value) => setViewType(value as any)}
					className="mb-6"
				>
					<TabsList className="gap-2">
						<TabsTrigger value="grid">
							<LayoutGrid className="w-4 h-4 mr-2" />
							Grid
						</TabsTrigger>
						<TabsTrigger value="list">
							<List className="w-4 h-4 mr-2" />
							List
						</TabsTrigger>
						<TabsTrigger value="kanban">
							<Columns className="w-4 h-4 mr-2" />
							Kanban
						</TabsTrigger>
					</TabsList>
				</Tabs>

				{/* Render based on view type */}
				{viewType === "kanban" && (
					<KanbanView
						assets={displayAssets}
						onLaneChange={handleLaneChange}
						onAssetClick={handleAssetOrBoardClick}
					/>
				)}

				{viewType === "list" && (
					<AssetListView
						assets={displayAssets}
						onAssetClick={handleAssetOrBoardClick}
					/>
				)}

				{viewType === "grid" && displayAssets && displayAssets.length > 0 && (
					<AssetGrid
						assets={displayAssets}
						mode="grid"
						onAssetClick={handleAssetOrBoardClick}
						onAssetDelete={(asset) => console.log("Delete:", asset)}
						onUpdate={refetch}
						allTags={[]}
						allCollections={[]}
						loading={false}
						emptyMessage="No assets in this board"
					/>
				)}

				{(!displayAssets || displayAssets.length === 0) && (
					<div className="text-center py-12">
						<p className="text-gray-500">No assets found in this board</p>
					</div>
				)}
			</div>
		</AssetLayout>
	);
}
