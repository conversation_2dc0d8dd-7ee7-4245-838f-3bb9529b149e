import React, { useState } from "react";
import {
	useQ<PERSON>y,
	createBoard,
	updateBoard,
	deleteBoard,
	getBoards,
} from "wasp/client/operations";
import { useOrganizationState } from "../../../../organization/store";
import { BoardSelector } from "../components/board-selector/BoardSelector";
import { Board } from "../../domain/types";
import { Button } from "../../../../client/components/ui/button";
import { Input } from "../../../../client/components/ui/input";
import { Textarea } from "../../../../client/components/ui/textarea";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "../../../../client/components/ui/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "../../../../client/components/ui/dialog";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "../../../../client/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Plus, Edit, Trash2, Folder } from "lucide-react";

const CreateBoardSchema = z.object({
	name: z
		.string()
		.min(1, "Board name is required")
		.max(100, "Board name too long"),
	description: z.string().max(500, "Description too long").optional(),
	parentBoardId: z.string().optional(),
});

type CreateBoardForm = z.infer<typeof CreateBoardSchema>;

export const BoardManagementPage: React.FC = () => {
	const { selectedOrganizationId } = useOrganizationState();
	const [selectedBoardId, setSelectedBoardId] = useState<string>();
	const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

	// Fetch boards
	const {
		data: boards,
		isLoading,
		refetch,
	} = useQuery(
		getBoards,
		{ organizationId: selectedOrganizationId },
		{ enabled: !!selectedOrganizationId },
	);

	// Form for creating/editing boards
	const form = useForm<CreateBoardForm>({
		resolver: zodResolver(CreateBoardSchema),
		defaultValues: {
			name: "",
			description: "",
			parentBoardId: undefined,
		},
	});

	const selectedBoard = boards?.find(
		(board: Board) => board.id === selectedBoardId,
	);

	const handleCreateBoard = async (data: CreateBoardForm) => {
		try {
			await createBoard({
				name: data.name,
				description: data.description || "",
				organizationId: selectedOrganizationId,
				parentBoardId: data.parentBoardId,
			});

			console.log("Board created successfully!");
			setIsCreateDialogOpen(false);
			form.reset();
			refetch();
		} catch (error) {
			console.error("Error creating board:", error);
			alert("Failed to create board");
		}
	};

	const handleEditBoard = async (data: CreateBoardForm) => {
		if (!selectedBoard) return;

		try {
			await updateBoard({
				id: selectedBoard.id,
				name: data.name,
				description: data.description,
				parentBoardId: data.parentBoardId,
			});

			console.log("Board updated successfully!");
			setIsEditDialogOpen(false);
			form.reset();
			refetch();
		} catch (error) {
			console.error("Error updating board:", error);
			alert("Failed to update board");
		}
	};

	const handleDeleteBoard = async () => {
		if (!selectedBoard) return;

		if (!confirm(`Are you sure you want to delete "${selectedBoard.name}"?`)) {
			return;
		}

		try {
			await deleteBoard({ id: selectedBoard.id });
			console.log("Board deleted successfully!");
			setSelectedBoardId(undefined);
			refetch();
		} catch (error) {
			console.error("Error deleting board:", error);
			alert("Failed to delete board");
		}
	};

	const openEditDialog = () => {
		if (selectedBoard) {
			form.setValue("name", selectedBoard.name);
			form.setValue("description", selectedBoard.description || "");
			form.setValue("parentBoardId", selectedBoard.parentBoardId || undefined);
			setIsEditDialogOpen(true);
		}
	};

	if (isLoading) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-lg">Loading boards...</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto p-6 max-w-6xl">
			<div className="flex items-center justify-between mb-6">
				<div>
					<h1 className="text-3xl font-bold">Board Management</h1>
					<p className="text-gray-600 dark:text-gray-400">
						Organize your assets with boards and folders
					</p>
				</div>

				<Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
					<DialogTrigger asChild>
						<Button className="bg-[#676D50] hover:bg-[#5A6145]">
							<Plus size={16} className="mr-2" />
							Create Board
						</Button>
					</DialogTrigger>
					<DialogContent>
						<DialogHeader>
							<DialogTitle>Create New Board</DialogTitle>
							<DialogDescription>
								Create a new board to organize your assets
							</DialogDescription>
						</DialogHeader>

						<Form {...form}>
							<form
								onSubmit={form.handleSubmit(handleCreateBoard)}
								className="space-y-4"
							>
								<FormField
									control={form.control}
									name="name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Board Name</FormLabel>
											<FormControl>
												<Input placeholder="Enter board name" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="description"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Description (Optional)</FormLabel>
											<FormControl>
												<Textarea
													placeholder="Enter board description"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="parentBoardId"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Parent Board (Optional)</FormLabel>
											<FormControl>
												<select
													{...field}
													className="w-full p-2 border rounded-md"
													value={field.value || ""}
												>
													<option value="">No parent (root board)</option>
													{boards?.map((board: Board) => (
														<option key={board.id} value={board.id}>
															{board.name}
														</option>
													))}
												</select>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<div className="flex justify-end gap-2">
									<Button
										type="button"
										variant="outline"
										onClick={() => setIsCreateDialogOpen(false)}
									>
										Cancel
									</Button>
									<Button type="submit">Create Board</Button>
								</div>
							</form>
						</Form>
					</DialogContent>
				</Dialog>
			</div>

			<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
				{/* Board Tree */}
				<Card className="lg:col-span-1">
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Folder size={20} />
							Boards
						</CardTitle>
						<CardDescription>Select a board to view details</CardDescription>
					</CardHeader>
					<CardContent>
						{boards && Array.isArray(boards) && boards.length > 0 ? (
							<BoardSelector
								boards={boards}
								selectedBoardId={selectedBoardId}
								onBoardSelect={setSelectedBoardId}
								allowCreation={false}
							/>
						) : (
							<div className="text-center py-8 text-gray-500">
								<Folder size={48} className="mx-auto mb-4 opacity-50" />
								<p>No boards found</p>
								<p className="text-sm">
									Create your first board to get started
								</p>
							</div>
						)}
					</CardContent>
				</Card>

				{/* Board Details */}
				<Card className="lg:col-span-2">
					<CardHeader>
						<CardTitle>
							{selectedBoard ? selectedBoard.name : "Select a Board"}
						</CardTitle>
						<CardDescription>
							{selectedBoard
								? "Board details and actions"
								: "Choose a board from the left to view details"}
						</CardDescription>
					</CardHeader>
					<CardContent>
						{selectedBoard ? (
							<div className="space-y-6">
								{/* Board Info */}
								<div className="space-y-4">
									<div>
										<h3 className="font-medium text-sm text-gray-500 uppercase tracking-wide">
											Description
										</h3>
										<p className="mt-1">
											{selectedBoard.description || "No description provided"}
										</p>
									</div>

									<div className="grid grid-cols-2 gap-4">
										<div>
											<h3 className="font-medium text-sm text-gray-500 uppercase tracking-wide">
												Created
											</h3>
											<p className="mt-1">
												{new Date(selectedBoard.createdAt).toLocaleDateString()}
											</p>
										</div>
										<div>
											<h3 className="font-medium text-sm text-gray-500 uppercase tracking-wide">
												Last Updated
											</h3>
											<p className="mt-1">
												{new Date(selectedBoard.updatedAt).toLocaleDateString()}
											</p>
										</div>
									</div>
								</div>

								{/* Actions */}
								<div className="flex gap-2 pt-4 border-t">
									<Button
										variant="outline"
										onClick={openEditDialog}
										className="flex items-center gap-2"
									>
										<Edit size={16} />
										Edit Board
									</Button>
									<Button
										variant="destructive"
										onClick={handleDeleteBoard}
										className="flex items-center gap-2"
									>
										<Trash2 size={16} />
										Delete Board
									</Button>
								</div>
							</div>
						) : (
							<div className="text-center py-12 text-gray-500">
								<Folder size={64} className="mx-auto mb-4 opacity-30" />
								<p className="text-lg">No board selected</p>
								<p>Choose a board from the sidebar to view its details</p>
							</div>
						)}
					</CardContent>
				</Card>
			</div>

			{/* Edit Dialog */}
			<Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Edit Board</DialogTitle>
						<DialogDescription>Update board information</DialogDescription>
					</DialogHeader>

					<Form {...form}>
						<form
							onSubmit={form.handleSubmit(handleEditBoard)}
							className="space-y-4"
						>
							<FormField
								control={form.control}
								name="name"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Board Name</FormLabel>
										<FormControl>
											<Input placeholder="Enter board name" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="description"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Description (Optional)</FormLabel>
										<FormControl>
											<Textarea
												placeholder="Enter board description"
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<div className="flex justify-end gap-2">
								<Button
									type="button"
									variant="outline"
									onClick={() => setIsEditDialogOpen(false)}
								>
									Cancel
								</Button>
								<Button type="submit">Update Board</Button>
							</div>
						</form>
					</Form>
				</DialogContent>
			</Dialog>
		</div>
	);
};
