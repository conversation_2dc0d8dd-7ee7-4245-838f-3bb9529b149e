import React, { useState } from "react";
import { useParams } from "react-router-dom";
import { getAsset, useQuery } from "wasp/client/operations";
//test2
import { X } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
	Button,
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	Input,
	Label,
	Switch,
	Tabs,
	TabsContent,
	TabsList,
	TabsTrigger,
} from "../../../../client/components/ui/index";
import { useOrganizationState } from "../../../../organization/store";
import { Calendar } from "../../../../client/components/ui/calendar";
// import AssetViewer from "../components/asset-viewer";
import { FileUpload } from "../../../../client/components/file-upload";
import { toast } from "react-hot-toast";
import <PERSON><PERSON><PERSON>iewer from "../components/asset-viewer";

interface ShareModalProps {
	isOpen: boolean;
	onClose: () => void;
	assetId: number;
}

export interface ShareSettings {
	expiresAt?: Date;
	sharedWithEmail: string;
}

export interface SharedAsset {
	id: string;
	assetId: number;
	sharedWithEmail: string;
	sharedWithUserId?: number;
	sharedAt: Date;
	expiresAt?: Date;
	accessCount: number;
	lastAccessedAt?: Date;
}

// Add this schema near your other interfaces
const shareFormSchema = z
	.object({
		email: z.union([
			z.string().email("Valid email is required"),
			z
				.string()
				.length(0), // Allow empty string
			z.undefined(), // Allow undefined
		]),
		expiresAt: z.date().optional(),
		assetId: z.string(), // Changed to string type since backend expects string
		settings: z
			.object({
				allowDownload: z.boolean().optional(),
				publicLink: z.boolean().optional(),
				showVersions: z.boolean().optional(),
				showComments: z.boolean().optional(),
				allowComments: z.boolean().optional(),
			})
			.optional(),
	})
	.refine(
		(data) => {
			if (data.settings?.publicLink) {
				return true;
			}
			return data.email && data.email.length > 0;
		},
		{
			message: "Email is required when public link is disabled",
			path: ["email"],
		},
	);

type ShareFormValues = z.infer<typeof shareFormSchema>;

export function ShareModal({ isOpen, onClose, assetId }: ShareModalProps) {
	const form = useForm<ShareFormValues>({
		resolver: zodResolver(shareFormSchema),
		defaultValues: {
			assetId: String(assetId), // Convert to string
			email: "",
			settings: {
				publicLink: false,
				allowDownload: false,
				showVersions: false,
				showComments: false,
				allowComments: false,
			},
		},
	});

	const [isLoading, setIsLoading] = useState(false);

	const onSubmit = async (data: ShareFormValues) => {
		setIsLoading(true);
		try {
			// Make sure assetId is a string and expiresAt is properly formatted
			const formData = {
				...data,
				assetId: String(data.assetId),
				// Convert date to ISO string if it exists
				expiresAt: data.expiresAt ? data.expiresAt.toISOString() : undefined,
				// If publicLink is true, email can be empty
				email: data.settings?.publicLink ? "" : data.email,
			};

			// const {
			// 	data: { inviteLink = "" },
			// 	success,
			// } = await shareAsset(formData);

			// if (success) {
			// 	await navigator.clipboard.writeText(inviteLink);
			// 	toast.success("Link copied to clipboard");
			// }

			form.reset();
			onClose();
		} catch (error) {
			console.error("Error sharing:", error);
			toast.error(
				error instanceof Error ? error.message : "Failed to share asset",
			);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="sm:max-w-md bg-inherit">
				<DialogHeader>
					<div className="flex items-center justify-between">
						<DialogTitle>Share a link</DialogTitle>
						<Button variant="ghost" size="icon" onClick={onClose}>
							<X className="h-4 w-4" />
						</Button>
					</div>
				</DialogHeader>
				<form onSubmit={form.handleSubmit(onSubmit)}>
					<Tabs defaultValue="link" className="w-full">
						<TabsList className="grid w-full grid-cols-2">
							<TabsTrigger value="link">Link</TabsTrigger>
							<TabsTrigger value="settings">Settings</TabsTrigger>
						</TabsList>
						<TabsContent value="link" className="mt-4">
							<div className="space-y-4">
								<div className="flex items-center justify-between">
									<div className="space-y-0.5">
										<Label>Enable public link</Label>
									</div>
									<Switch
										checked={!!form.watch("settings.publicLink")}
										onCheckedChange={(checked) => {
											form.setValue(
												"settings.publicLink",
												checked ? true : undefined,
											);
										}}
									/>
								</div>
								{!form.watch("settings.publicLink") && (
									<div className="space-y-4">
										<div className="space-y-2">
											<Label>Share with email</Label>
											<Input
												type="email"
												placeholder="Enter email address"
												{...form.register("email")}
											/>
											{form.formState.errors.email && (
												<p className="text-sm text-red-500">
													{form.formState.errors.email.message}
												</p>
											)}
										</div>
									</div>
								)}
								<div className="flex items-center justify-between">
									<div className="space-y-0.5">
										<Label>Allow download</Label>
									</div>
									<Switch
										checked={!!form.watch("settings.allowDownload")}
										onCheckedChange={(checked) => {
											form.setValue(
												"settings.allowDownload",
												checked ? true : undefined,
											);
										}}
									/>
								</div>
								<div className="flex gap-2">
									{form.watch("settings.publicLink") ? (
										<Button
											type="submit"
											className="flex-1"
											variant="secondary"
											disabled={isLoading}
										>
											Copy link
										</Button>
									) : (
										<Button
											type="submit"
											className="flex-1"
											disabled={isLoading}
										>
											{isLoading ? "Sharing..." : "Share"}
										</Button>
									)}
								</div>
							</div>
						</TabsContent>
						<TabsContent value="settings" className="mt-4">
							<div className="space-y-4">
								<div className="space-y-0.5">
									<Label className="font-bold text-md text-slate-400">
										Versions
									</Label>
								</div>
								<div className="flex items-center justify-between">
									<div className="space-y-0.5">
										<Label>Show versions</Label>
									</div>
									<Switch
										checked={!!form.watch("settings.showVersions")}
										onCheckedChange={(checked) => {
											form.setValue(
												"settings.showVersions",
												checked ? true : undefined,
											);
										}}
									/>
								</div>

								<div className="space-y-0.5">
									<Label className="font-bold text-md text-slate-400">
										Comments
									</Label>
								</div>
								<div className="flex items-center justify-between">
									<div className="space-y-0.5">
										<Label>Show comments</Label>
									</div>
									<Switch
										checked={!!form.watch("settings.showComments")}
										onCheckedChange={(checked) => {
											form.setValue(
												"settings.showComments",
												checked ? true : undefined,
											);
										}}
									/>
								</div>
								<div className="flex items-center justify-between">
									<div className="space-y-0.5">
										<Label>Allow comments</Label>
									</div>
									<Switch
										checked={!!form.watch("settings.allowComments")}
										onCheckedChange={(checked) => {
											form.setValue(
												"settings.allowComments",
												checked ? true : undefined,
											);
										}}
									/>
								</div>

								<div className="space-y-0.5">
									<Label className="font-bold text-md text-slate-400">
										Security
									</Label>
								</div>

								<div className="space-y-2">
									<div className="flex items-center justify-between">
										<div className="space-y-0.5">
											<Label>Set expiration date</Label>
										</div>
										<Switch
											checked={!!form.watch("expiresAt")}
											onCheckedChange={(checked) => {
												form.setValue(
													"expiresAt",
													checked ? new Date() : undefined,
												);
											}}
										/>
									</div>
									{form.watch("expiresAt") && (
										<div className="space-y-2">
											<Calendar
												mode="single"
												selected={form.watch("expiresAt")}
												onSelect={(date) => form.setValue("expiresAt", date)}
												disabled={(date) => date < new Date()}
											/>
										</div>
									)}
								</div>
							</div>
						</TabsContent>
					</Tabs>
				</form>
			</DialogContent>
		</Dialog>
	);
}

export function PreviewAssetPage() {
	const { selectedOrganizationId } = useOrganizationState();
	const [showShare, setShowShare] = useState(false);
	const { id } = useParams<{ id: string }>();

	// Return early if no assetId
	if (!id) {
		return (
			<div className="flex items-center justify-center min-h-screen text-red-500">
				No asset ID provided
			</div>
		);
	}

	// Pass assetId directly as string to query
	const { data: asset }: { data: any } = useQuery(
		getAsset,
		{ assetId: id, organizationId: selectedOrganizationId! },
		{ enabled: !!id },
	);

	if (!asset) {
		return (
			<div className="flex items-center justify-center min-h-screen text-red-500">
				No asset found
			</div>
		);
	}

	console.log({ asset });

	return (
		<>
			<AssetViewer asset={asset} openShareModal={() => setShowShare(true)} />
			<ShareModal
				isOpen={showShare}
				onClose={() => setShowShare(false)}
				assetId={Number(id)}
			/>
			<FileUpload limit={1} />
		</>
	);
}
