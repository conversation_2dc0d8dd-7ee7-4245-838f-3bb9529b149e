# 🏗️ Core Infrastructure

This directory contains the foundational infrastructure that powers the entire application.

## 📁 Directory Structure

```
core/
├── ai/                     # AI infrastructure & orchestration
├── database/               # Database layer & repositories  
├── events/                 # Event system & WebSocket management
├── storage/                # File storage & caching
└── middleware/             # Cross-cutting concerns
```

## 🎯 Purpose

The core infrastructure provides:

1. **AI Orchestration**: Centralized AI model management, prompt handling, and tool execution
2. **Database Layer**: Repository patterns, transaction management, and data access abstractions
3. **Event System**: Event bus, WebSocket management, and real-time communication
4. **Storage Layer**: File management, caching, and storage abstractions
5. **Middleware**: Authentication, rate limiting, error handling, and other cross-cutting concerns

## 🤖 AI-Friendly Design

- **Clear Separation**: Each infrastructure concern is isolated
- **Explicit Dependencies**: All dependencies are clearly defined
- **Comprehensive Documentation**: Every file includes purpose and usage examples
- **Type Safety**: Full TypeScript coverage with clear interfaces

## 🔗 Integration

Core infrastructure is used by all feature modules but has no dependencies on specific features, ensuring clean architecture and easy testing.
