/**
 * 🏗️ Core Infrastructure Exports
 *
 * @description Central export point for all core infrastructure
 * @responsibility Provides clean imports for core functionality across the application
 * @dependencies All core modules
 * @ai_context This is the main entry point for core infrastructure - import from here for consistency
 *
 * @example
 * ```typescript
 * import { AIOrchestrator, EventBus, BaseRepository } from '@core';
 * import { EventType, AIModelCapability } from '@core';
 * ```
 */

// AI Infrastructure
export * from './ai/providers/base-ai-provider';
export * from './ai/orchestration/ai-orchestrator';

// Database Infrastructure
export * from './database/repositories/base-repository';

// Event System
export * from './events/event-bus';

// WebSocket Infrastructure
export * from './websocket/useAuthenticatedSocket';
export * from './websocket/progress-tracker';

// Re-export commonly used items for convenience
export { AIOrchestrator } from './ai/orchestration/ai-orchestrator';
export { EventBus, EventType } from './events/event-bus';
export { BaseRepository } from './database/repositories/base-repository';
export { BaseAIProvider, AICapability } from './ai/providers/base-ai-provider';
export type { TaskResult, OrchestrationOptions } from './ai/orchestration/ai-orchestrator';
export type { GenerationOptions, ImageAnalysisOptions, AIResponse } from './ai/providers/base-ai-provider';
export type { BaseEvent, EventHandler } from './events/event-bus';
export type { RepositoryOptions, Pagination, PaginatedResult } from './database/repositories/base-repository';
