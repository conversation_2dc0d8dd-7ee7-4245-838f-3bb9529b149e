/**
 * 🚌 Event Bus
 *
 * @description Central event system for application-wide communication
 * @responsibility Manages event publishing, subscription, and delivery
 * @dependencies EventEmitter, WebSocket manager
 * @ai_context This is the nervous system of the app - all features communicate through events
 *
 * @example
 * ```typescript
 * // Publishing an event
 * EventBus.getInstance().publish('brand-kit.created', {
 *   brandKitId: 'abc123',
 *   userId: 456
 * });
 *
 * // Subscribing to events
 * EventBus.getInstance().subscribe('brand-kit.created', (data) => {
 *   console.log('Brand kit created:', data);
 * });
 * ```
 */

import { EventEmitter } from 'events';
import { z } from 'zod';

/**
 * 🎯 Event Types
 * @ai_context All possible event types in the application
 */
export enum EventType {
  // Brand Kit Events
  BRAND_KIT_CREATED = 'brand-kit.created',
  BRAND_KIT_UPDATED = 'brand-kit.updated',
  BRAND_KIT_DELETED = 'brand-kit.deleted',
  BRAND_KIT_ANALYSIS_STARTED = 'brand-kit.analysis.started',
  BRAND_KIT_ANALYSIS_COMPLETED = 'brand-kit.analysis.completed',

  // Product Events
  PRODUCT_CREATED = 'product.created',
  PRODUCT_UPDATED = 'product.updated',
  PRODUCT_DELETED = 'product.deleted',
  PRODUCT_ANALYSIS_STARTED = 'product.analysis.started',
  PRODUCT_ANALYSIS_COMPLETED = 'product.analysis.completed',

  // Canvas Events
  CANVAS_CREATED = 'canvas.created',
  CANVAS_UPDATED = 'canvas.updated',
  CANVAS_ELEMENT_ADDED = 'canvas.element.added',
  CANVAS_ELEMENT_UPDATED = 'canvas.element.updated',
  CANVAS_ELEMENT_DELETED = 'canvas.element.deleted',

  // Agent Events
  AGENT_TASK_STARTED = 'agent.task.started',
  AGENT_TASK_COMPLETED = 'agent.task.completed',
  AGENT_TASK_FAILED = 'agent.task.failed',
  AGENT_MESSAGE_RECEIVED = 'agent.message.received',
  AGENT_MESSAGE_SENT = 'agent.message.sent',

  // Image Generation Events
  IMAGE_GENERATION_STARTED = 'image.generation.started',
  IMAGE_GENERATION_COMPLETED = 'image.generation.completed',
  IMAGE_GENERATION_FAILED = 'image.generation.failed',

  // User Events
  USER_CREATED = 'user.created',
  USER_UPDATED = 'user.updated',
  USER_LOGIN = 'user.login',
  USER_LOGOUT = 'user.logout',

  // Organization Events
  ORGANIZATION_CREATED = 'organization.created',
  ORGANIZATION_UPDATED = 'organization.updated',
  ORGANIZATION_MEMBER_ADDED = 'organization.member.added',
  ORGANIZATION_MEMBER_REMOVED = 'organization.member.removed',

  // Asset Events
  ASSET_UPLOADED = 'asset.uploaded',
  ASSET_UPDATED = 'asset.updated',
  ASSET_DELETED = 'asset.deleted',
  ASSET_SHARED = 'asset.shared',

  // Notification Events
  NOTIFICATION_CREATED = 'notification.created',
  NOTIFICATION_READ = 'notification.read',

  // System Events
  SYSTEM_ERROR = 'system.error',
  SYSTEM_WARNING = 'system.warning',
  SYSTEM_INFO = 'system.info',
}

/**
 * 📦 Base Event Schema
 * @ai_context Standard structure for all events
 */
export const BaseEventSchema = z.object({
  id: z.string(),
  type: z.nativeEnum(EventType),
  timestamp: z.date(),
  userId: z.number().optional(),
  organizationId: z.string().optional(),
  data: z.record(z.any()),
  metadata: z.record(z.any()).optional(),
});

export type BaseEvent = z.infer<typeof BaseEventSchema>;

/**
 * 🎧 Event Handler Type
 * @ai_context Function signature for event handlers
 */
export type EventHandler<T = any> = (event: BaseEvent & { data: T }) => void | Promise<void>;

/**
 * 📋 Event Subscription
 * @ai_context Represents an active event subscription
 */
interface EventSubscription {
  id: string;
  eventType: EventType;
  handler: EventHandler;
  once: boolean;
  priority: number;
}

/**
 * 🚌 Event Bus Class
 * @ai_context Singleton class that manages all application events
 */
export class EventBus {
  private static instance: EventBus;
  private emitter: EventEmitter;
  private subscriptions: Map<string, EventSubscription> = new Map();
  private eventHistory: BaseEvent[] = [];
  private maxHistorySize = 1000;

  private constructor() {
    this.emitter = new EventEmitter();
    this.emitter.setMaxListeners(100); // Increase limit for high-traffic apps
  }

  /**
   * 🏗️ Get singleton instance
   * @returns EventBus instance
   */
  static getInstance(): EventBus {
    if (!EventBus.instance) {
      EventBus.instance = new EventBus();
    }
    return EventBus.instance;
  }

  /**
   * 📢 Publish an event
   * @param eventType Type of event
   * @param data Event data
   * @param options Additional options (userId, organizationId, metadata)
   * @returns Event ID
   */
  publish(
    eventType: EventType,
    data: any,
    options: {
      userId?: number;
      organizationId?: string;
      metadata?: Record<string, any>;
    } = {}
  ): string {
    const event: BaseEvent = {
      id: this.generateEventId(),
      type: eventType,
      timestamp: new Date(),
      userId: options.userId,
      organizationId: options.organizationId,
      data,
      metadata: options.metadata,
    };

    // Validate event structure
    try {
      BaseEventSchema.parse(event);
    } catch (error) {
      console.error('[EventBus] Invalid event structure:', error);
      throw new Error('Invalid event structure');
    }

    // Add to history
    this.addToHistory(event);

    // Emit the event
    this.emitter.emit(eventType, event);
    this.emitter.emit('*', event); // Wildcard for global listeners

    console.log(`[EventBus] Published event: ${eventType}`, { eventId: event.id, data });

    return event.id;
  }

  /**
   * 🎧 Subscribe to events
   * @param eventType Type of event to listen for
   * @param handler Event handler function
   * @param options Subscription options
   * @returns Subscription ID
   */
  subscribe(
    eventType: EventType | '*',
    handler: EventHandler,
    options: {
      once?: boolean;
      priority?: number;
    } = {}
  ): string {
    const subscriptionId = this.generateSubscriptionId();
    const subscription: EventSubscription = {
      id: subscriptionId,
      eventType: eventType as EventType,
      handler,
      once: options.once || false,
      priority: options.priority || 0,
    };

    this.subscriptions.set(subscriptionId, subscription);

    // Create wrapper handler for cleanup
    const wrappedHandler = async (event: BaseEvent) => {
      try {
        await handler(event);

        // Remove subscription if it's a one-time listener
        if (subscription.once) {
          this.unsubscribe(subscriptionId);
        }
      } catch (error) {
        console.error(`[EventBus] Error in event handler for ${eventType}:`, error);

        // Publish error event
        this.publish(EventType.SYSTEM_ERROR, {
          error: error instanceof Error ? error.message : 'Unknown error',
          eventType,
          subscriptionId,
        });
      }
    };

    // Register with EventEmitter
    if (subscription.once) {
      this.emitter.once(eventType, wrappedHandler);
    } else {
      this.emitter.on(eventType, wrappedHandler);
    }

    console.log(`[EventBus] Subscribed to event: ${eventType}`, { subscriptionId });

    return subscriptionId;
  }

  /**
   * 🚫 Unsubscribe from events
   * @param subscriptionId Subscription ID to remove
   * @returns True if subscription was found and removed
   */
  unsubscribe(subscriptionId: string): boolean {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) {
      return false;
    }

    this.emitter.removeListener(subscription.eventType, subscription.handler);
    this.subscriptions.delete(subscriptionId);

    console.log(`[EventBus] Unsubscribed from event: ${subscription.eventType}`, { subscriptionId });

    return true;
  }

  /**
   * 🔍 Get event history
   * @param eventType Optional filter by event type
   * @param limit Maximum number of events to return
   * @returns Array of historical events
   */
  getHistory(eventType?: EventType, limit = 100): BaseEvent[] {
    let events = this.eventHistory;

    if (eventType) {
      events = events.filter((event) => event.type === eventType);
    }

    return events.slice(-limit);
  }

  /**
   * 📊 Get subscription statistics
   * @returns Subscription statistics
   */
  getStats(): {
    totalSubscriptions: number;
    subscriptionsByType: Record<string, number>;
    eventHistorySize: number;
  } {
    const subscriptionsByType: Record<string, number> = {};

    for (const subscription of this.subscriptions.values()) {
      const type = subscription.eventType;
      subscriptionsByType[type] = (subscriptionsByType[type] || 0) + 1;
    }

    return {
      totalSubscriptions: this.subscriptions.size,
      subscriptionsByType,
      eventHistorySize: this.eventHistory.length,
    };
  }

  /**
   * 🧹 Clear event history
   */
  clearHistory(): void {
    this.eventHistory = [];
    console.log('[EventBus] Event history cleared');
  }

  /**
   * 📝 Add event to history
   * @param event Event to add
   */
  private addToHistory(event: BaseEvent): void {
    this.eventHistory.push(event);

    // Trim history if it exceeds max size
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory = this.eventHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * 🆔 Generate unique event ID
   * @returns Unique event ID
   */
  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 🆔 Generate unique subscription ID
   * @returns Unique subscription ID
   */
  private generateSubscriptionId(): string {
    return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
