/**
 * 🗄️ Base Repository
 *
 * @description Abstract base class for all data repositories
 * @responsibility Provides common database operations and patterns
 * @dependencies Prisma client, transaction manager
 * @ai_context This is the foundation for all data access - ensures consistent patterns across all entities
 *
 * @example
 * ```typescript
 * class BrandKitRepository extends BaseRepository<BrandKit> {
 *   constructor() {
 *     super('brandKit'); // Prisma model name
 *   }
 *
 *   async findByUserId(userId: number): Promise<BrandKit[]> {
 *     return this.findMany({ where: { userId } });
 *   }
 * }
 * ```
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { prisma } from 'wasp/server';
import { z } from 'zod';

/**
 * 🔧 Repository Options Schema
 * @ai_context Configuration options for repository operations
 */
export const RepositoryOptionsSchema = z.object({
  include: z.record(z.any()).optional(),
  select: z.record(z.any()).optional(),
  orderBy: z.array(z.record(z.any())).or(z.record(z.any())).optional(),
  skip: z.number().optional(),
  take: z.number().optional(),
  cursor: z.record(z.any()).optional(),
});

export type RepositoryOptions = z.infer<typeof RepositoryOptionsSchema>;

/**
 * 📊 Pagination Schema
 * @ai_context Standard pagination parameters
 */
export const PaginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export type Pagination = z.infer<typeof PaginationSchema>;

/**
 * 📋 Paginated Result Schema
 * @ai_context Standard format for paginated responses
 */
export const PaginatedResultSchema = z.object({
  data: z.array(z.any()),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    totalPages: z.number(),
    hasNext: z.boolean(),
    hasPrev: z.boolean(),
  }),
});

export type PaginatedResult<T> = {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
};

/**
 * 🗄️ Abstract Base Repository Class
 * @ai_context All repositories extend this to ensure consistent data access patterns
 */
export abstract class BaseRepository<T> {
  protected readonly modelName: string;
  protected readonly prisma: PrismaClient;

  constructor(modelName: string) {
    this.modelName = modelName;
    this.prisma = prisma;
  }

  /**
   * 🔍 Find a single record by ID
   * @param id Record ID
   * @param options Query options (include, select, etc.)
   * @returns Single record or null
   */
  async findById(id: string | number, options?: RepositoryOptions): Promise<T | null> {
    try {
      const model = this.getModel();
      return (await model.findUnique({
        where: { id },
        ...options,
      })) as T | null;
    } catch (error) {
      console.error(`[${this.modelName}Repository] Error finding by ID:`, error);
      throw error;
    }
  }

  /**
   * 🔍 Find first record matching criteria
   * @param where Where conditions
   * @param options Query options
   * @returns First matching record or null
   */
  async findFirst(where: any, options?: RepositoryOptions): Promise<T | null> {
    try {
      const model = this.getModel();
      return (await model.findFirst({
        where,
        ...options,
      })) as T | null;
    } catch (error) {
      console.error(`[${this.modelName}Repository] Error finding first:`, error);
      throw error;
    }
  }

  /**
   * 🔍 Find many records matching criteria
   * @param where Where conditions
   * @param options Query options
   * @returns Array of matching records
   */
  async findMany(where?: any, options?: RepositoryOptions): Promise<T[]> {
    try {
      const model = this.getModel();
      return (await model.findMany({
        where,
        ...options,
      })) as T[];
    } catch (error) {
      console.error(`[${this.modelName}Repository] Error finding many:`, error);
      throw error;
    }
  }

  /**
   * 📄 Find records with pagination
   * @param where Where conditions
   * @param pagination Pagination parameters
   * @param options Query options
   * @returns Paginated result
   */
  async findManyPaginated(
    where?: any,
    pagination: Pagination = { page: 1, limit: 20, sortOrder: 'desc' },
    options?: RepositoryOptions
  ): Promise<PaginatedResult<T>> {
    try {
      const model = this.getModel();
      const { page, limit, sortBy, sortOrder } = pagination;

      const skip = (page - 1) * limit;

      // Build order by clause
      const orderBy: any = {};
      if (sortBy) {
        orderBy[sortBy] = sortOrder;
      } else {
        orderBy.createdAt = sortOrder;
      }

      // Execute queries in parallel
      const [data, total] = await Promise.all([
        model.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          ...options,
        }) as Promise<T[]>,
        model.count({ where }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        data,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      console.error(`[${this.modelName}Repository] Error finding paginated:`, error);
      throw error;
    }
  }

  /**
   * ➕ Create a new record
   * @param data Record data
   * @param options Query options
   * @returns Created record
   */
  async create(data: any, options?: RepositoryOptions): Promise<T> {
    try {
      const model = this.getModel();
      return (await model.create({
        data,
        ...options,
      })) as T;
    } catch (error) {
      console.error(`[${this.modelName}Repository] Error creating:`, error);
      throw error;
    }
  }

  /**
   * ✏️ Update a record by ID
   * @param id Record ID
   * @param data Update data
   * @param options Query options
   * @returns Updated record
   */
  async update(id: string | number, data: any, options?: RepositoryOptions): Promise<T> {
    try {
      const model = this.getModel();
      return (await model.update({
        where: { id },
        data,
        ...options,
      })) as T;
    } catch (error) {
      console.error(`[${this.modelName}Repository] Error updating:`, error);
      throw error;
    }
  }

  /**
   * 🔄 Upsert a record (create or update)
   * @param where Where conditions for finding existing record
   * @param create Data for creating new record
   * @param update Data for updating existing record
   * @param options Query options
   * @returns Upserted record
   */
  async upsert(where: any, create: any, update: any, options?: RepositoryOptions): Promise<T> {
    try {
      const model = this.getModel();
      return (await model.upsert({
        where,
        create,
        update,
        ...options,
      })) as T;
    } catch (error) {
      console.error(`[${this.modelName}Repository] Error upserting:`, error);
      throw error;
    }
  }

  /**
   * 🗑️ Delete a record by ID
   * @param id Record ID
   * @returns Deleted record
   */
  async delete(id: string | number): Promise<T> {
    try {
      const model = this.getModel();
      return (await model.delete({
        where: { id },
      })) as T;
    } catch (error) {
      console.error(`[${this.modelName}Repository] Error deleting:`, error);
      throw error;
    }
  }

  /**
   * 🗑️ Delete many records matching criteria
   * @param where Where conditions
   * @returns Count of deleted records
   */
  async deleteMany(where: any): Promise<number> {
    try {
      const model = this.getModel();
      const result = await model.deleteMany({ where });
      return result.count;
    } catch (error) {
      console.error(`[${this.modelName}Repository] Error deleting many:`, error);
      throw error;
    }
  }

  /**
   * 🔢 Count records matching criteria
   * @param where Where conditions
   * @returns Count of matching records
   */
  async count(where?: any): Promise<number> {
    try {
      const model = this.getModel();
      return await model.count({ where });
    } catch (error) {
      console.error(`[${this.modelName}Repository] Error counting:`, error);
      throw error;
    }
  }

  /**
   * ✅ Check if record exists
   * @param where Where conditions
   * @returns True if record exists, false otherwise
   */
  async exists(where: any): Promise<boolean> {
    try {
      const count = await this.count(where);
      return count > 0;
    } catch (error) {
      console.error(`[${this.modelName}Repository] Error checking existence:`, error);
      throw error;
    }
  }

  /**
   * 🔧 Get Prisma model for this repository
   * @returns Prisma model delegate
   */
  protected getModel(): any {
    return (this.prisma as any)[this.modelName];
  }
}
