/**
 * 🎭 AI Orchestrator
 *
 * @description Central coordinator for all AI operations across the application
 * @responsibility Routes AI requests to appropriate providers, manages fallbacks, handles errors
 * @dependencies BaseAIProvider, AI provider implementations
 * @ai_context This is the single entry point for all AI operations - it decides which AI model to use for each task
 *
 * @example
 * ```typescript
 * const orchestrator = AIOrchestrator.getInstance();
 * const response = await orchestrator.generateText("Analyze this brand", {
 *   preferredProvider: "gemini",
 *   fallbackProvider: "claude"
 * });
 * ```
 */

import { BaseAIProvider, AICapability, GenerationOptions, ImageAnalysisOptions } from '../providers/base-ai-provider';
import { z } from 'zod';

/**
 * 🎯 AI Task Types
 * @ai_context Different types of AI tasks that can be orchestrated
 */
export enum AITaskType {
  BRAND_ANALYSIS = 'brand_analysis',
  PRODUCT_ANALYSIS = 'product_analysis',
  IMAGE_GENERATION = 'image_generation',
  CONTENT_GENERATION = 'content_generation',
  CHAT_RESPONSE = 'chat_response',
  TOOL_EXECUTION = 'tool_execution',
  DOCUMENT_ANALYSIS = 'document_analysis',
}

/**
 * 🔧 Orchestration Options Schema
 * @ai_context Configuration for AI task orchestration
 */
export const OrchestrationOptionsSchema = z.object({
  preferredProvider: z.string().optional(),
  fallbackProvider: z.string().optional(),
  taskType: z.nativeEnum(AITaskType).optional(),
  retryAttempts: z.number().min(0).max(5).optional(),
  timeout: z.number().positive().optional(),
  requiresCapability: z.array(z.nativeEnum(AICapability)).optional(),
});

export type OrchestrationOptions = z.infer<typeof OrchestrationOptionsSchema>;

/**
 * 📊 Task Result Schema
 * @ai_context Result of an orchestrated AI task
 */
export const TaskResultSchema = z.object({
  success: z.boolean(),
  result: z.any().optional(),
  error: z.string().optional(),
  provider: z.string(),
  duration: z.number(),
  tokensUsed: z.number().optional(),
  retryCount: z.number(),
});

export type TaskResult = z.infer<typeof TaskResultSchema>;

/**
 * 🎭 AI Orchestrator Class
 * @ai_context Singleton class that manages all AI operations
 */
export class AIOrchestrator {
  private static instance: AIOrchestrator;
  private providers: Map<string, BaseAIProvider> = new Map();
  private defaultProvider: string | null = null;

  private constructor() {}

  /**
   * 🏗️ Get singleton instance
   * @returns AIOrchestrator instance
   */
  static getInstance(): AIOrchestrator {
    if (!AIOrchestrator.instance) {
      AIOrchestrator.instance = new AIOrchestrator();
    }
    return AIOrchestrator.instance;
  }

  /**
   * 📝 Register an AI provider
   * @param name Provider name (e.g., "gemini", "claude", "openai")
   * @param provider Provider instance
   * @param isDefault Whether this should be the default provider
   */
  registerProvider(name: string, provider: BaseAIProvider, isDefault = false): void {
    this.providers.set(name, provider);
    if (isDefault || !this.defaultProvider) {
      this.defaultProvider = name;
    }
  }

  /**
   * 🎯 Select the best provider for a task
   * @param options Orchestration options
   * @returns Selected provider name
   */
  private selectProvider(options: OrchestrationOptions): string {
    // If preferred provider is specified and available, use it
    if (options.preferredProvider && this.providers.has(options.preferredProvider)) {
      const provider = this.providers.get(options.preferredProvider)!;

      // Check if provider has required capabilities
      if (options.requiresCapability) {
        const hasAllCapabilities = options.requiresCapability.every((cap) => provider.hasCapability(cap));
        if (hasAllCapabilities) {
          return options.preferredProvider;
        }
      } else {
        return options.preferredProvider;
      }
    }

    // If fallback provider is specified and available, use it
    if (options.fallbackProvider && this.providers.has(options.fallbackProvider)) {
      const provider = this.providers.get(options.fallbackProvider)!;

      if (options.requiresCapability) {
        const hasAllCapabilities = options.requiresCapability.every((cap) => provider.hasCapability(cap));
        if (hasAllCapabilities) {
          return options.fallbackProvider;
        }
      } else {
        return options.fallbackProvider;
      }
    }

    // Find first provider with required capabilities
    if (options.requiresCapability) {
      for (const [name, provider] of this.providers) {
        const hasAllCapabilities = options.requiresCapability.every((cap) => provider.hasCapability(cap));
        if (hasAllCapabilities) {
          return name;
        }
      }
    }

    // Fall back to default provider
    if (this.defaultProvider && this.providers.has(this.defaultProvider)) {
      return this.defaultProvider;
    }

    // If no suitable provider found, throw error
    throw new Error('No suitable AI provider found for the requested task');
  }

  /**
   * 📝 Generate text with automatic provider selection and fallback
   * @param prompt Input prompt
   * @param generationOptions AI generation options
   * @param orchestrationOptions Orchestration configuration
   * @returns Generated text response
   */
  async generateText(
    prompt: string,
    generationOptions?: GenerationOptions,
    orchestrationOptions: OrchestrationOptions = {}
  ): Promise<TaskResult> {
    const startTime = Date.now();
    let retryCount = 0;
    const maxRetries = orchestrationOptions.retryAttempts ?? 2;

    while (retryCount <= maxRetries) {
      try {
        const providerName = this.selectProvider({
          ...orchestrationOptions,
          requiresCapability: [AICapability.TEXT_GENERATION],
        });

        const provider = this.providers.get(providerName)!;
        const result = await provider.generateText(prompt, generationOptions);

        return {
          success: true,
          result,
          provider: providerName,
          duration: Date.now() - startTime,
          tokensUsed: result.usage?.totalTokens,
          retryCount,
        };
      } catch (error) {
        retryCount++;
        if (retryCount > maxRetries) {
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            provider: 'none',
            duration: Date.now() - startTime,
            retryCount,
          };
        }

        // Wait before retry (exponential backoff)
        await new Promise((resolve) => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
      }
    }

    return {
      success: false,
      error: 'Max retries exceeded',
      provider: 'none',
      duration: Date.now() - startTime,
      retryCount,
    };
  }

  /**
   * 🖼️ Analyze image with automatic provider selection
   * @param imageUrl Image URL or base64
   * @param prompt Optional analysis prompt
   * @param analysisOptions Image analysis options
   * @param orchestrationOptions Orchestration configuration
   * @returns Analysis result
   */
  async analyzeImage(
    imageUrl: string,
    prompt?: string,
    analysisOptions?: ImageAnalysisOptions,
    orchestrationOptions: OrchestrationOptions = {}
  ): Promise<TaskResult> {
    const startTime = Date.now();
    let retryCount = 0;
    const maxRetries = orchestrationOptions.retryAttempts ?? 2;

    while (retryCount <= maxRetries) {
      try {
        const providerName = this.selectProvider({
          ...orchestrationOptions,
          requiresCapability: [AICapability.IMAGE_ANALYSIS, AICapability.VISION],
        });

        const provider = this.providers.get(providerName)!;
        const result = await provider.analyzeImage(imageUrl, prompt, analysisOptions);

        return {
          success: true,
          result,
          provider: providerName,
          duration: Date.now() - startTime,
          tokensUsed: result.usage?.totalTokens,
          retryCount,
        };
      } catch (error) {
        retryCount++;
        if (retryCount > maxRetries) {
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            provider: 'none',
            duration: Date.now() - startTime,
            retryCount,
          };
        }

        await new Promise((resolve) => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
      }
    }

    return {
      success: false,
      error: 'Max retries exceeded',
      provider: 'none',
      duration: Date.now() - startTime,
      retryCount,
    };
  }

  /**
   * 📋 Get all registered providers
   * @returns Array of provider names
   */
  getProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  /**
   * 🔍 Get provider by name
   * @param name Provider name
   * @returns Provider instance or undefined
   */
  getProvider(name: string): BaseAIProvider | undefined {
    return this.providers.get(name);
  }
}
