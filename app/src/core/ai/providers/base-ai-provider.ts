/**
 * 🤖 Base AI Provider Interface
 *
 * @description Abstract base class for all AI service providers
 * @responsibility Defines the contract that all AI providers must implement
 * @dependencies None (pure interface)
 * @ai_context This is the foundation for all AI integrations - OpenAI, Anthropic, Google Vertex, etc.
 *
 * @example
 * ```typescript
 * class OpenAIProvider extends BaseAIProvider {
 *   async generateText(prompt: string, options?: GenerationOptions): Promise<string> {
 *     // OpenAI-specific implementation
 *   }
 * }
 * ```
 */

import { z } from 'zod';

/**
 * 🎯 AI Model Capabilities
 * @ai_context Defines what each AI model can do
 */
export enum AICapability {
  TEXT_GENERATION = 'text_generation',
  IMAGE_ANALYSIS = 'image_analysis',
  IMAGE_GENERATION = 'image_generation',
  JSON_MODE = 'json_mode',
  TOOL_USE = 'tool_use',
  STREAMING = 'streaming',
  VISION = 'vision',
}

/**
 * 🔧 Generation Options Schema
 * @ai_context Configuration options for AI text generation
 */
export const GenerationOptionsSchema = z.object({
  temperature: z.number().min(0).max(2).optional(),
  maxTokens: z.number().positive().optional(),
  topP: z.number().min(0).max(1).optional(),
  frequencyPenalty: z.number().min(-2).max(2).optional(),
  presencePenalty: z.number().min(-2).max(2).optional(),
  stop: z.array(z.string()).optional(),
  stream: z.boolean().optional(),
  jsonMode: z.boolean().optional(),
  tools: z.array(z.any()).optional(),
  systemPrompt: z.string().optional(),
});

export type GenerationOptions = z.infer<typeof GenerationOptionsSchema>;

/**
 * 🎨 Image Analysis Options Schema
 * @ai_context Configuration for image analysis tasks
 */
export const ImageAnalysisOptionsSchema = z.object({
  detail: z.enum(['low', 'high', 'auto']).optional(),
  maxTokens: z.number().positive().optional(),
  temperature: z.number().min(0).max(2).optional(),
});

export type ImageAnalysisOptions = z.infer<typeof ImageAnalysisOptionsSchema>;

/**
 * 🖼️ Image Generation Options Schema
 * @ai_context Configuration for image generation tasks
 */
export const ImageGenerationOptionsSchema = z.object({
  size: z.enum(['256x256', '512x512', '1024x1024', '1792x1024', '1024x1792']).optional(),
  quality: z.enum(['standard', 'hd']).optional(),
  style: z.enum(['vivid', 'natural']).optional(),
  responseFormat: z.enum(['url', 'b64_json']).optional(),
  n: z.number().min(1).max(10).optional(),
});

export type ImageGenerationOptions = z.infer<typeof ImageGenerationOptionsSchema>;

/**
 * 📊 AI Response Schema
 * @ai_context Standardized response format from all AI providers
 */
export const AIResponseSchema = z.object({
  content: z.string(),
  usage: z
    .object({
      promptTokens: z.number(),
      completionTokens: z.number(),
      totalTokens: z.number(),
    })
    .optional(),
  finishReason: z.enum(['stop', 'length', 'tool_calls', 'content_filter']).optional(),
  toolCalls: z.array(z.any()).optional(),
});

export type AIResponse = z.infer<typeof AIResponseSchema>;

/**
 * 🤖 Abstract Base AI Provider
 * @ai_context All AI providers must extend this class to ensure consistent interface
 */
export abstract class BaseAIProvider {
  protected readonly name: string;
  protected readonly capabilities: Set<AICapability>;

  constructor(name: string, capabilities: AICapability[]) {
    this.name = name;
    this.capabilities = new Set(capabilities);
  }

  /**
   * 📝 Generate text from a prompt
   * @param prompt The input prompt
   * @param options Generation configuration options
   * @returns Generated text response
   */
  abstract generateText(prompt: string, options?: GenerationOptions): Promise<AIResponse>;

  /**
   * 🖼️ Analyze an image with optional text prompt
   * @param imageUrl URL or base64 of the image to analyze
   * @param prompt Optional text prompt for analysis
   * @param options Analysis configuration options
   * @returns Analysis results
   */
  abstract analyzeImage(imageUrl: string, prompt?: string, options?: ImageAnalysisOptions): Promise<AIResponse>;

  /**
   * 🎨 Generate an image from a text prompt
   * @param prompt Text description of the desired image
   * @param options Image generation configuration
   * @returns Generated image URL or base64
   */
  abstract generateImage(prompt: string, options?: ImageGenerationOptions): Promise<string>;

  /**
   * 🌊 Stream text generation (if supported)
   * @param prompt The input prompt
   * @param options Generation configuration options
   * @returns Async generator yielding text chunks
   */
  abstract streamText(prompt: string, options?: GenerationOptions): AsyncGenerator<string, void, unknown>;

  /**
   * ✅ Check if provider supports a specific capability
   * @param capability The capability to check
   * @returns True if supported, false otherwise
   */
  hasCapability(capability: AICapability): boolean {
    return this.capabilities.has(capability);
  }

  /**
   * 📋 Get all supported capabilities
   * @returns Array of supported capabilities
   */
  getCapabilities(): AICapability[] {
    return Array.from(this.capabilities);
  }

  /**
   * 🏷️ Get provider name
   * @returns Provider name
   */
  getName(): string {
    return this.name;
  }
}
