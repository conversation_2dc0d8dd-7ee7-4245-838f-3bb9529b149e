import { fal } from '@fal-ai/client';

// Configure Fal.ai client
fal.config({
  credentials: process.env.FAL_KEY,
});

export interface FalImageGenerationOptions {
  prompt: string;
  negative_prompt?: string;
  aspect_ratio?: '1:1' | '16:9' | '9:16' | '3:4' | '4:3';
  num_images?: number;
  seed?: number;
}

export interface FalImageGenerationResult {
  success: boolean;
  images?: Array<{
    url: string;
    content_type?: string;
    file_name?: string;
    file_size?: number;
  }>;
  seed?: number;
  error?: string;
}

export class FalAiProvider {
  /**
   * Generate images using Fal.ai's Imagen 4 model
   */
  static async generateImage(options: FalImageGenerationOptions): Promise<FalImageGenerationResult> {
    try {
      const result = await fal.subscribe('fal-ai/imagen4/preview', {
        input: {
          prompt: options.prompt,
          negative_prompt: options.negative_prompt || '',
          aspect_ratio: options.aspect_ratio || '1:1',
          num_images: options.num_images || 1,
          ...(options.seed && { seed: options.seed }),
        },
        logs: true,
        onQueueUpdate: (update) => {
          if (update.status === 'IN_PROGRESS') {
            update.logs?.map((log) => log.message).forEach(console.log);
          }
        },
      });

      return {
        success: true,
        images: result.data.images,
        seed: result.data.seed,
      };
    } catch (error) {
      console.error('Fal.ai image generation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate image',
      };
    }
  }

  /**
   * Generate character avatar with specific prompt construction
   */
  static async generateCharacterAvatar(audienceData: any, characterOptions: any): Promise<FalImageGenerationResult> {
    const prompt = this.constructCharacterPrompt(audienceData, characterOptions);

    return this.generateImage({
      prompt,
      aspect_ratio: characterOptions.shot === 'headshot' ? '1:1' : '3:4',
      num_images: 1,
    });
  }

  /**
   * Construct a detailed prompt for character generation
   */
  private static constructCharacterPrompt(character: any, options: any): string {
    // If character has a visual description from AI, use it as the base
    if (character.visualDescription) {
      const parts = [
        // Base quality settings
        'professional high-quality photograph,',
        'cinematic lighting, studio setup, high resolution,',

        // Use the AI-generated visual description
        character.visualDescription,

        // Add specific options that override the description
        options.clothing ? `wearing ${options.clothing} clothing,` : '',

        // Pose and composition
        this.getPoseDescription(options.pose),

        // Shot framing
        this.getShotDescription(options.shot),

        // Style and rendering
        this.getStyleDescription(options.style),

        // Environment and background
        'clean professional background, subtle depth of field,',

        // Quality markers
        '8k resolution, professional photography,',
        'sharp details, perfect focus',
      ];

      return parts.filter(Boolean).join(' ');
    }

    // Fallback to the original method if no visual description
    const parts = [
      // Base quality settings
      'professional high-quality photograph,',
      'cinematic lighting, studio setup, high resolution,',

      // Demographics
      options.gender && options.gender !== 'any' ? `${options.gender},` : '',
      this.getAgeDescriptor(options.age || character.age),
      options.ethnicity && options.ethnicity !== 'any' ? `${options.ethnicity} ethnicity,` : '',

      // Physical features
      options.hairColor && options.hairColor !== 'any' ? `${options.hairColor} hair,` : '',
      options.eyeColor && options.eyeColor !== 'any' ? `${options.eyeColor} eyes,` : '',
      options.bodyType && options.bodyType !== 'average' ? `${options.bodyType} build,` : '',

      // Professional context
      character.occupation || character.audience?.occupation
        ? `${character.occupation || character.audience.occupation},`
        : '',

      // Clothing style
      options.clothing ? `${options.clothing} clothing,` : 'professional attire,',

      // Pose and composition
      this.getPoseDescription(options.pose),

      // Shot framing
      this.getShotDescription(options.shot),

      // Style and rendering
      this.getStyleDescription(options.style),

      // Environment and background
      'clean professional background, subtle depth of field,',

      // Quality markers
      '8k resolution, professional photography,',
      'sharp details, perfect focus',
    ];

    return parts.filter(Boolean).join(' ');
  }

  private static getAgeDescriptor(age?: string): string {
    if (!age) return 'professional adult,';

    // Handle age ranges
    if (age.includes('-')) {
      const [min, max] = age.split('-').map((n) => parseInt(n.trim()));
      if (min <= 25) return 'young professional, fresh and energetic look,';
      if (min >= 45) return 'experienced professional, confident and distinguished look,';
      return 'established professional, confident and experienced look,';
    }

    return 'professional adult,';
  }

  private static getPoseDescription(pose?: string): string {
    switch (pose) {
      case 'sitting':
        return 'sitting pose, relaxed yet professional,';
      case 'walking':
        return 'walking pose, dynamic and confident,';
      case 'portrait':
        return 'portrait pose, direct eye contact,';
      case 'action':
        return 'action pose, dynamic and engaging,';
      default:
        return 'standing confidently, professional posture,';
    }
  }

  private static getShotDescription(shot?: string): string {
    switch (shot) {
      case 'headshot':
        return 'close-up headshot, shoulders up,';
      case 'half-body':
        return 'medium shot, waist up,';
      default:
        return 'full body shot, professional composition,';
    }
  }

  private static getStyleDescription(style?: string): string {
    switch (style) {
      case 'artistic':
        return 'stylized character design, modern illustration style,';
      case 'cartoon':
        return 'cartoon style, friendly and approachable,';
      default:
        return 'photorealistic, natural skin texture, detailed features,';
    }
  }
}
