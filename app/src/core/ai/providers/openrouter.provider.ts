/**
 * 🤖 OpenRouter AI Provider
 *
 * @description OpenRouter implementation of the BaseAIProvider interface
 * @responsibility Handles all OpenRouter API interactions for text generation and analysis
 * @dependencies BaseAIProvider, axios
 * @ai_context This provider handles OpenRouter's unified API for multiple AI models
 *
 * @example
 * ```typescript
 * const provider = new OpenRouterProvider();
 * const response = await provider.generateText('Hello world', {
 *   temperature: 0.7,
 *   jsonMode: true
 * });
 * ```
 */

import axios from 'axios';
import {
  BaseAIProvider,
  AICapability,
  GenerationOptions,
  ImageAnalysisOptions,
  ImageGenerationOptions,
  AIResponse,
} from './base-ai-provider';

/**
 * 🔧 OpenRouter Configuration
 */
interface OpenRouterConfig {
  apiKey: string;
  baseURL?: string;
  defaultModel?: string;
  siteUrl?: string;
  siteName?: string;
}

/**
 * 📝 OpenRouter Message Format
 */
interface OpenRouterMessage {
  role: 'system' | 'user' | 'assistant';
  content: string | Array<{ type: 'text' | 'image_url'; text?: string; image_url?: { url: string } }>;
}

/**
 * 🤖 OpenRouter AI Provider Implementation
 */
export class OpenRouterProvider extends BaseAIProvider {
  private readonly config: OpenRouterConfig;
  private readonly baseURL: string;

  constructor(config: OpenRouterConfig) {
    super('OpenRouter', [
      AICapability.TEXT_GENERATION,
      AICapability.IMAGE_ANALYSIS,
      AICapability.JSON_MODE,
      AICapability.TOOL_USE,
      AICapability.STREAMING,
      AICapability.VISION,
    ]);

    this.config = {
      baseURL: 'https://openrouter.ai/api/v1',
      defaultModel: process.env.OPENROUTER_DEFAULT_MODEL,
      siteUrl: 'https://olivia.ai',
      siteName: 'Olivia AI Design Assistant',
      ...config,
    };

    if (!this.config.defaultModel) {
      throw new Error('OpenRouter model not configured. Set OPENROUTER_DEFAULT_MODEL environment variable.');
    }

    this.baseURL = this.config.baseURL!;

    if (!this.config.apiKey) {
      throw new Error('OpenRouter API key is required');
    }
  }

  /**
   * 📝 Generate text from a prompt
   */
  async generateText(prompt: string, options?: GenerationOptions): Promise<AIResponse> {
    const messages: OpenRouterMessage[] = [];

    // Add system prompt if provided
    if (options?.systemPrompt) {
      messages.push({ role: 'system', content: options.systemPrompt });
    }

    messages.push({ role: 'user', content: prompt });

    const payload: any = {
      model: this.config.defaultModel,
      messages,
      temperature: options?.temperature,
      max_tokens: options?.maxTokens,
      top_p: options?.topP,
      frequency_penalty: options?.frequencyPenalty,
      presence_penalty: options?.presencePenalty,
      stop: options?.stop,
    };

    // Add JSON mode if requested
    if (options?.jsonMode) {
      payload.response_format = { type: 'json_object' };
    }

    // Add tools if provided
    if (options?.tools) {
      payload.tools = options.tools;
    }

    try {
      const response = await axios.post(`${this.baseURL}/chat/completions`, payload, {
        headers: {
          Authorization: `Bearer ${this.config.apiKey}`,
          'HTTP-Referer': this.config.siteUrl,
          'X-Title': this.config.siteName,
          'Content-Type': 'application/json',
        },
      });

      const choice = response.data.choices[0];
      return {
        content: choice.message.content,
        usage: response.data.usage
          ? {
              promptTokens: response.data.usage.prompt_tokens,
              completionTokens: response.data.usage.completion_tokens,
              totalTokens: response.data.usage.total_tokens,
            }
          : undefined,
        finishReason: choice.finish_reason as any,
        toolCalls: choice.message.tool_calls,
      };
    } catch (error) {
      console.error('[OpenRouterProvider] Text generation failed:', error);
      throw new Error(`OpenRouter text generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 🖼️ Analyze an image with optional text prompt
   */
  async analyzeImage(imageUrl: string, prompt?: string, options?: ImageAnalysisOptions): Promise<AIResponse> {
    const content: Array<{ type: 'text' | 'image_url'; text?: string; image_url?: { url: string } }> = [];

    if (prompt) {
      content.push({ type: 'text', text: prompt });
    }

    content.push({
      type: 'image_url',
      image_url: { url: imageUrl },
    });

    const messages: OpenRouterMessage[] = [{ role: 'user', content }];

    const payload = {
      model: this.config.defaultModel,
      messages,
      max_tokens: options?.maxTokens || 1000,
      temperature: options?.temperature || 0.7,
    };

    try {
      const response = await axios.post(`${this.baseURL}/chat/completions`, payload, {
        headers: {
          Authorization: `Bearer ${this.config.apiKey}`,
          'HTTP-Referer': this.config.siteUrl,
          'X-Title': this.config.siteName,
          'Content-Type': 'application/json',
        },
      });

      const choice = response.data.choices[0];
      return {
        content: choice.message.content,
        usage: response.data.usage
          ? {
              promptTokens: response.data.usage.prompt_tokens,
              completionTokens: response.data.usage.completion_tokens,
              totalTokens: response.data.usage.total_tokens,
            }
          : undefined,
        finishReason: choice.finish_reason as any,
      };
    } catch (error) {
      console.error('[OpenRouterProvider] Image analysis failed:', error);
      throw new Error(`OpenRouter image analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 🎨 Generate an image from a text prompt
   * Note: OpenRouter doesn't directly support image generation, this would need to be routed to a specific provider
   */
  async generateImage(prompt: string, options?: ImageGenerationOptions): Promise<string> {
    throw new Error(
      'Image generation not supported by OpenRouter provider. Use a dedicated image generation provider.'
    );
  }

  /**
   * 🌊 Stream text generation
   */
  async *streamText(prompt: string, options?: GenerationOptions): AsyncGenerator<string, void, unknown> {
    const messages: OpenRouterMessage[] = [];

    if (options?.systemPrompt) {
      messages.push({ role: 'system', content: options.systemPrompt });
    }

    messages.push({ role: 'user', content: prompt });

    const payload = {
      model: this.config.defaultModel,
      messages,
      temperature: options?.temperature,
      max_tokens: options?.maxTokens,
      stream: true,
    };

    try {
      const response = await axios.post(`${this.baseURL}/chat/completions`, payload, {
        headers: {
          Authorization: `Bearer ${this.config.apiKey}`,
          'HTTP-Referer': this.config.siteUrl,
          'X-Title': this.config.siteName,
          'Content-Type': 'application/json',
        },
        responseType: 'stream',
      });

      // Parse SSE stream
      for await (const chunk of response.data) {
        const lines = chunk.toString().split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') return;

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices[0]?.delta?.content;
              if (content) {
                yield content;
              }
            } catch (e) {
              // Skip invalid JSON
            }
          }
        }
      }
    } catch (error) {
      console.error('[OpenRouterProvider] Streaming failed:', error);
      throw new Error(`OpenRouter streaming failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 🔧 Generate structured JSON response
   */
  async generateStructuredResponse<T>(prompt: string, schema: any, options?: GenerationOptions): Promise<T> {
    const response = await this.generateText(prompt, {
      ...options,
      jsonMode: true,
      systemPrompt: `${options?.systemPrompt || ''}\n\nRespond with valid JSON matching this schema: ${JSON.stringify(schema)}`,
    });

    try {
      return JSON.parse(response.content);
    } catch (error) {
      throw new Error(`Failed to parse JSON response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

/**
 * 🏭 Factory function to create OpenRouter provider
 */
export function createOpenRouterProvider(apiKey?: string): OpenRouterProvider {
  const key = apiKey || process.env.OPENROUTER_API_KEY;

  if (!key) {
    throw new Error('OpenRouter API key not found. Set OPENROUTER_API_KEY environment variable or pass it directly.');
  }

  return new OpenRouterProvider({
    apiKey: key,
    siteUrl: process.env.SITE_URL || 'https://olivia.ai',
    siteName: process.env.SITE_NAME || 'Olivia AI Design Assistant',
  });
}
