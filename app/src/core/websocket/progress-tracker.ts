/**
 * 🔄 Progress Tracking System
 *
 * @description Centralized progress tracking with WebSocket real-time updates
 * @responsibility Manages progress state and emits updates via WebSocket
 * @dependencies WebSocket, EventBus
 * @ai_context This handles all progress tracking across the application with proper timing
 *
 * @example
 * ```typescript
 * const tracker = new ProgressTracker('synthesis_123', userId);
 * await tracker.start('Starting AI synthesis...');
 * await tracker.updateStage('website_analysis', 25, 'Analyzing website...');
 * await tracker.complete('Synthesis completed!');
 * ```
 */

import { getIO } from '../../websocket/emitters';
import { USER_ROOM_CHANNEL } from '../../websocket/constants';

/**
 * 📊 Progress Stage Definition
 */
export interface ProgressStage {
  id: string;
  name: string;
  weight: number; // Percentage of total progress (0-100)
  status: 'pending' | 'running' | 'completed' | 'error';
  message?: string;
  startTime?: Date;
  endTime?: Date;
  error?: string;
}

/**
 * 📈 Progress State
 */
export interface ProgressState {
  sessionId: string;
  userId: number;
  status: 'idle' | 'running' | 'completed' | 'error';
  currentStage?: string;
  overallProgress: number; // 0-100
  message: string;
  stages: ProgressStage[];
  startTime: Date;
  endTime?: Date;
  errors: string[];
}

/**
 * 🔄 Progress Tracker Class
 */
export class ProgressTracker {
  private state: ProgressState;
  private readonly sessionId: string;
  private readonly userId: number;

  constructor(
    sessionId: string,
    userId: number,
    stages: Omit<ProgressStage, 'status' | 'startTime' | 'endTime'>[] = []
  ) {
    this.sessionId = sessionId;
    this.userId = userId;

    this.state = {
      sessionId,
      userId,
      status: 'idle',
      overallProgress: 0,
      message: 'Initializing...',
      stages: stages.map((stage) => ({
        ...stage,
        status: 'pending' as const,
      })),
      startTime: new Date(),
      errors: [],
    };
  }

  /**
   * 🚀 Start progress tracking
   */
  async start(message: string = 'Starting...'): Promise<void> {
    this.state.status = 'running';
    this.state.message = message;
    this.state.startTime = new Date();

    await this.emitProgress();
  }

  /**
   * 📊 Update current stage progress
   */
  async updateStage(
    stageId: string,
    progress: number,
    message?: string,
    status: 'running' | 'completed' | 'error' = 'running'
  ): Promise<void> {
    const stage = this.state.stages.find((s) => s.id === stageId);

    if (stage) {
      // Update stage
      if (stage.status === 'pending' && status === 'running') {
        stage.startTime = new Date();
      }

      stage.status = status;
      stage.message = message;

      if (status === 'completed' || status === 'error') {
        stage.endTime = new Date();
      }

      this.state.currentStage = stageId;
    }

    // Calculate overall progress based on completed stages
    this.calculateOverallProgress(stageId, progress);

    if (message) {
      this.state.message = message;
    }

    await this.emitProgress();
  }

  /**
   * 📈 Calculate overall progress
   */
  private calculateOverallProgress(currentStageId: string, currentStageProgress: number): void {
    let totalProgress = 0;

    for (const stage of this.state.stages) {
      if (stage.status === 'completed') {
        totalProgress += stage.weight;
      } else if (stage.id === currentStageId && stage.status === 'running') {
        // Add partial progress for current stage
        totalProgress += (stage.weight * currentStageProgress) / 100;
      }
    }

    this.state.overallProgress = Math.min(100, Math.max(0, totalProgress));
  }

  /**
   * ❌ Add error to current stage
   */
  async addError(stageId: string, error: string): Promise<void> {
    const stage = this.state.stages.find((s) => s.id === stageId);
    if (stage) {
      stage.status = 'error';
      stage.error = error;
      stage.endTime = new Date();
    }

    this.state.errors.push(error);
    this.state.message = `Error in ${stage?.name || stageId}: ${error}`;

    await this.emitProgress();
  }

  /**
   * ✅ Complete progress tracking
   */
  async complete(message: string = 'Completed successfully!'): Promise<void> {
    this.state.status = 'completed';
    this.state.message = message;
    this.state.overallProgress = 100;
    this.state.endTime = new Date();

    // Mark any remaining stages as completed
    for (const stage of this.state.stages) {
      if (stage.status === 'pending' || stage.status === 'running') {
        stage.status = 'completed';
        stage.endTime = new Date();
      }
    }

    await this.emitProgress();
  }

  /**
   * 💥 Fail progress tracking
   */
  async fail(error: string): Promise<void> {
    this.state.status = 'error';
    this.state.message = `Failed: ${error}`;
    this.state.endTime = new Date();
    this.state.errors.push(error);

    // Mark current stage as error
    if (this.state.currentStage) {
      const stage = this.state.stages.find((s) => s.id === this.state.currentStage);
      if (stage) {
        stage.status = 'error';
        stage.error = error;
        stage.endTime = new Date();
      }
    }

    await this.emitProgress();
  }

  /**
   * 📡 Emit progress via WebSocket
   */
  private async emitProgress(): Promise<void> {
    try {
      const io = getIO();
      if (!io) {
        console.warn('[ProgressTracker] Socket.IO instance not available');
        return;
      }

      const progressData = {
        ...this.state,
        timestamp: new Date().toISOString(),
      };

      // Emit to specific session room
      const sessionRoom = `synthesis_${this.sessionId}_${this.userId}`;
      io.to(sessionRoom).emit('synthesis_progress', progressData);

      // Also emit to general user room for fallback
      const userRoom = USER_ROOM_CHANNEL + this.userId;
      io.to(userRoom).emit('synthesis_progress', progressData);

      console.log(`[ProgressTracker] Progress emitted to ${sessionRoom}:`, {
        stage: this.state.currentStage,
        progress: this.state.overallProgress,
        message: this.state.message,
      });
    } catch (error) {
      console.error('[ProgressTracker] Failed to emit progress:', error);
    }
  }

  /**
   * 📊 Get current state
   */
  getState(): ProgressState {
    return { ...this.state };
  }

  /**
   * 🕐 Get elapsed time
   */
  getElapsedTime(): number {
    const endTime = this.state.endTime || new Date();
    return endTime.getTime() - this.state.startTime.getTime();
  }

  /**
   * 📋 Get stage summary
   */
  getStageSummary(): { completed: number; total: number; failed: number } {
    const completed = this.state.stages.filter((s) => s.status === 'completed').length;
    const failed = this.state.stages.filter((s) => s.status === 'error').length;
    const total = this.state.stages.length;

    return { completed, total, failed };
  }
}

/**
 * 🏭 Factory function to create progress tracker
 */
export function createProgressTracker(
  sessionId: string,
  userId: number,
  stages: Omit<ProgressStage, 'status' | 'startTime' | 'endTime'>[] = []
): ProgressTracker {
  return new ProgressTracker(sessionId, userId, stages);
}

/**
 * 📊 Predefined stage templates for common workflows
 */
export const SYNTHESIS_STAGES = [
  { id: 'website_analysis', name: 'Website Analysis', weight: 25 },
  { id: 'brand_guide_processing', name: 'Brand Guide Processing', weight: 15 },
  { id: 'shopify_analysis', name: 'Shopify Analysis', weight: 15 },
  { id: 'brand_kit_generation', name: 'Brand Kit Generation', weight: 30 },
  { id: 'audience_generation', name: 'Audience Generation', weight: 15 },
] as const;

export const ONBOARDING_STAGES = [
  { id: 'data_collection', name: 'Collecting Information', weight: 20 },
  { id: 'ai_analysis', name: 'AI Analysis', weight: 40 },
  { id: 'content_generation', name: 'Generating Content', weight: 30 },
  { id: 'finalization', name: 'Finalizing Setup', weight: 10 },
] as const;
