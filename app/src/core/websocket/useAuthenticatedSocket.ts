/**
 * 🔌 Authenticated Socket Hook - CORE VERSION
 * @description Shared authenticated socket hook for all features
 * @responsibility Wraps useSocket and automatically authenticates
 * @dependencies Wasp socket and auth hooks
 * @ai_context This is the core useAuthenticatedSocket hook that can be used across all features
 */

import { useEffect, useRef, useState } from 'react';
import { useSocket } from 'wasp/client/webSocket';
import { useAuth } from 'wasp/client/auth';

/**
 * 🔌 Authenticated Socket Hook
 * @description Hook that wraps useSocket and automatically authenticates the socket
 * when it connects and user data is available.
 * @returns The same interface as useSocket: { socket, isConnected }
 * @ai_context This is the shared version that all features should use
 */
export const useAuthenticatedSocket = () => {
  const { socket, isConnected } = useSocket();
  const { data: user } = useAuth();

  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const retryCountRef = useRef(0);
  const maxRetries = 3;

  // Make socket available globally for debugging
  if (typeof window !== 'undefined') (window as any).socket = socket;

  // Reset authentication state when connection drops
  useEffect(() => {
    if (!isConnected) {
      setIsAuthenticated(false);
      retryCountRef.current = 0;
    }
  }, [isConnected]);

  // Simple authentication without complex retry logic
  useEffect(() => {
    if (!isConnected || !user?.id || !socket || isAuthenticated) {
      return;
    }

    console.log(`[useAuthenticatedSocket] Authenticating socket for user ${user.id}`);

    // Set up auth success listener
    const handleAuthOk = () => {
      console.log(`[useAuthenticatedSocket] Authentication successful for user ${user.id}`);
      setIsAuthenticated(true);
      retryCountRef.current = 0;
      (socket as any).off('auth_ok', handleAuthOk);
      (socket as any).off('auth_error', handleAuthError);
    };

    // Set up auth error listener
    const handleAuthError = (error: any) => {
      console.error('[useAuthenticatedSocket] Authentication failed:', error);
      (socket as any).off('auth_ok', handleAuthOk);
      (socket as any).off('auth_error', handleAuthError);

      // Simple retry logic
      if (retryCountRef.current < maxRetries - 1) {
        retryCountRef.current++;
        console.log(`[useAuthenticatedSocket] Retrying authentication (${retryCountRef.current}/${maxRetries})`);
        // Retry after a delay
        setTimeout(() => {
          if (socket && isConnected && user?.id && !isAuthenticated) {
            (socket as any).emit('authenticate', { userId: user.id });
          }
        }, 1000 * retryCountRef.current);
      } else {
        console.error('[useAuthenticatedSocket] Max authentication retries exceeded');
      }
    };

    // Listen for auth responses
    (socket as any).on('auth_ok', handleAuthOk);
    (socket as any).on('auth_error', handleAuthError);

    // Send authentication request
    (socket as any).emit('authenticate', { userId: user.id });
    console.log(`[useAuthenticatedSocket] Authentication event emitted for user ${user.id}`);

    // Cleanup on unmount
    return () => {
      (socket as any).off('auth_ok', handleAuthOk);
      (socket as any).off('auth_error', handleAuthError);
    };
  }, [isConnected, user?.id, socket, isAuthenticated]);

  // Debug logging
  useEffect(() => {
    console.log(
      `[useAuthenticatedSocket] State - isConnected: ${isConnected}, isAuthenticated: ${isAuthenticated}, user: ${user?.id || 'none'}, socket: ${!!socket}`
    );
  }, [isConnected, isAuthenticated, user?.id, socket]);

  return {
    socket,
    isConnected: isConnected && isAuthenticated, // Only consider connected when both connected AND authenticated
    isAuthenticated,
  };
};
