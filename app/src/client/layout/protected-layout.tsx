import React, { useMemo, useEffect, FC, ReactNode } from 'react';
import { LeftSidebar } from './../components/navigation/LeftSidebar';
import { useSidebarState } from '../hooks/useSidebarState';
import { useLocation } from 'react-router-dom';
import { cn } from '../../shared/utils';
import Header from '../components/headers';
import { useAuth } from 'wasp/client/auth';
import { getUserOrganization, useQuery } from 'wasp/client/operations';
import { setOrganizations, setSelectedOrganizationId } from '../../organization/store';
import { setUserContext } from '../../utils/sentry';
import NotificationProvider from '../../notification/provider';

interface Props {
  children?: ReactNode;
  showHeader?: boolean;
}

const ProtectedLayout: FC<Props> = ({ children, showHeader }) => {
  const { isOpen, isMobile, isHovered } = useSidebarState();
  const { data: user } = useAuth();
  const { data: organizations } = useQuery(getUserOrganization, undefined, { enabled: !!user });
  const location = useLocation();
  const shouldDisplaySidebar = useMemo(() => {
    return (
      !!user && // Only show sidebar if user is logged in
      location.pathname !== '/' &&
      location.pathname !== '/login' &&
      location.pathname !== '/signup' &&
      location.pathname !== '/landing' &&
      location.pathname !== '/onboarding' &&
      location.pathname !== '/email-verification' &&
      location.pathname !== '/request-password-reset' &&
      location.pathname !== '/password-reset'
    );
  }, [location, user]); // Add user to dependencies

  useEffect(() => {
    if (user) {
      setUserContext({
        id: user.id,
        email: user.email as string,
      });
    }
  }, [user]);

  useEffect(() => {
    if (user?.id && organizations && organizations.length > 0) {
      setOrganizations(organizations);
      setSelectedOrganizationId(organizations[0].organizationId as string);
    }
  }, [organizations, user]);

  const contentMargin = useMemo(() => {
    if (!shouldDisplaySidebar) return '';
    if (isMobile) return 'ml-0';
    return isHovered ? 'ml-64' : 'ml-16';
  }, [shouldDisplaySidebar, isMobile, isHovered]);

  return (
    <NotificationProvider>
      <div className='dark:bg-boxdark-2 dark:text-bodydark w-full'>
        <div className='flex h-screen overflow-hidden'>
          {shouldDisplaySidebar && <LeftSidebar />}
          <div className='relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden'>
            {showHeader && user && <Header user={user} showControls={showHeader} />}
            <div className={cn('flex-1 transition-all duration-300 ease-in-out', contentMargin)}>
              <main className='h-full'>{children}</main>
            </div>
          </div>
        </div>
      </div>
    </NotificationProvider>
  );
};
export default ProtectedLayout;
