import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useReferenceData, ReferenceItem } from './utils';
import ReferenceFilters from './components/ReferenceFilters';
import ReferenceGrid from './components/ReferenceGrid';
import ProtectedLayout from '../layout/protected-layout';
import { Dialog, DialogContent } from '../components/ui/dialog';
import { X } from 'lucide-react';

const ReferenceLibraryPage: React.FC = () => {
  const { data, isLoading, error, categories, brands, getPaginatedItems } = useReferenceData();

  // State for filters and view mode
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState<boolean>(true);

  // State for infinite scrolling
  const [page, setPage] = useState<number>(1);
  const [items, setItems] = useState<ReferenceItem[]>([]);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const observer = useRef<IntersectionObserver | null>(null);
  const ITEMS_PER_PAGE = 30;

  // State for selected item preview
  const [selectedItem, setSelectedItem] = useState<ReferenceItem | null>(null);

  // Load initial items and when filters change
  useEffect(() => {
    if (!isLoading && data) {
      const result = getPaginatedItems(searchQuery, selectedCategory, selectedBrand, 1, ITEMS_PER_PAGE);
      setItems(result.items);
      setTotalItems(result.totalItems);
      setHasMore(result.hasMore);
      setPage(1);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoading, data, searchQuery, selectedCategory, selectedBrand]);

  // Load more items when scrolling
  const loadMoreItems = useCallback(() => {
    if (loadingMore || !hasMore) return;

    setLoadingMore(true);

    // Use setTimeout to ensure the loading state is visible
    setTimeout(() => {
      const nextPage = page + 1;
      const result = getPaginatedItems(searchQuery, selectedCategory, selectedBrand, nextPage, ITEMS_PER_PAGE);

      setItems((prevItems) => [...prevItems, ...result.items]);
      setHasMore(result.hasMore);
      setPage(nextPage);
      setLoadingMore(false);
    }, 500); // Short delay to show loading indicator
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loadingMore, hasMore, page, searchQuery, selectedCategory, selectedBrand]);

  // Setup intersection observer for infinite scrolling
  const lastItemRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (loadingMore) return;
      if (observer.current) observer.current.disconnect();

      observer.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasMore) {
            console.log('Loading more items...'); // Debug log
            loadMoreItems();
          }
        },
        {
          rootMargin: '0px 0px 300px 0px', // Load earlier, before user reaches the very bottom
          threshold: 0.1, // Trigger when at least 10% of the element is visible
        }
      );

      if (node) {
        console.log('Observing last item'); // Debug log
        observer.current.observe(node);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    },
    [loadingMore, hasMore]
  );

  // Reset all filters
  const resetFilters = () => {
    setSearchQuery('');
    setSelectedCategory('');
    setSelectedBrand('');
  };

  // Handle item click to show preview
  const handleItemClick = (item: ReferenceItem) => {
    setSelectedItem(item);
  };

  // Close the preview dialog
  const closePreview = () => {
    setSelectedItem(null);
  };

  return (
    <ProtectedLayout showHeader>
      <div className='p-6 bg-[#F0EFE9] dark:bg-gray-900 min-h-screen'>
        <div className='max-w-7xl mx-auto'>
          <div className='mb-8'>
            <h1 className='text-3xl font-bold text-[#1F2419] dark:text-white mb-2'>Reference Library</h1>
            <p className='text-[#676D50] dark:text-[#A6884C]'>
              Browse and search through our collection of reference images
            </p>
          </div>

          {isLoading ? (
            <div className='flex justify-center items-center h-64'>
              <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-[#676D50] dark:border-[#A6884C]'></div>
            </div>
          ) : error ? (
            <div className='bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 p-4 rounded-lg'>
              <p className='font-medium'>Error loading reference data</p>
              <p className='text-sm mt-1'>{error}</p>
            </div>
          ) : (
            <>
              <ReferenceFilters
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                categories={categories}
                selectedCategory={selectedCategory}
                setSelectedCategory={setSelectedCategory}
                brands={brands}
                selectedBrand={selectedBrand}
                setSelectedBrand={setSelectedBrand}
                viewMode={viewMode}
                setViewMode={setViewMode}
                showFilters={showFilters}
                setShowFilters={setShowFilters}
                resetFilters={resetFilters}
              />

              <div className='mb-4'>
                <p className='text-sm text-gray-500 dark:text-gray-400'>
                  Showing {items.length} of {totalItems} references
                </p>
              </div>

              <ReferenceGrid
                items={items}
                viewMode={viewMode}
                onItemClick={handleItemClick}
                lastItemRef={lastItemRef}
              />

              {loadingMore && (
                <div className='flex justify-center items-center py-8 mt-4'>
                  <div className='animate-spin rounded-full h-10 w-10 border-4 border-[#676D50]/20 dark:border-[#A6884C]/20 border-t-[#676D50] dark:border-t-[#A6884C]'></div>
                  <span className='ml-3 text-[#676D50] dark:text-[#A6884C]'>Loading more images...</span>
                </div>
              )}

              {!hasMore && items.length > 0 && (
                <div className='text-center py-8 text-gray-500 dark:text-gray-400'>
                  <p>You've reached the end of the results</p>
                </div>
              )}
            </>
          )}
        </div>

        {/* Image Preview Dialog */}
        <Dialog open={!!selectedItem} onOpenChange={(open) => !open && closePreview()}>
          <DialogContent className='sm:max-w-3xl p-0 overflow-hidden bg-white dark:bg-gray-800'>
            {selectedItem && (
              <div className='relative'>
                <button
                  onClick={closePreview}
                  className='absolute top-2 right-2 z-10 bg-black/50 text-white p-1 rounded-full hover:bg-black/70 transition-colors'
                >
                  <X size={20} />
                </button>

                <div className='max-h-[80vh] overflow-auto'>
                  <img
                    src={selectedItem.url}
                    alt={`${selectedItem.category} - ${selectedItem.brand}`}
                    className='w-full h-auto'
                  />
                </div>

                <div className='p-4 border-t border-gray-200 dark:border-gray-700'>
                  <div className='flex justify-between items-center mb-2'>
                    <span className='text-sm font-medium text-[#676D50] dark:text-[#A6884C] bg-[#676D50]/10 dark:bg-[#A6884C]/10 px-2 py-1 rounded-full'>
                      {selectedItem.category}
                    </span>
                    <span className='text-sm text-gray-500 dark:text-gray-400'>{selectedItem.brand}</span>
                  </div>

                  <div className='mt-4 flex justify-end'>
                    <a
                      href={selectedItem.url}
                      download
                      target='_blank'
                      rel='noopener noreferrer'
                      className='text-sm text-white bg-[#676D50] hover:bg-[#A6884C] dark:bg-[#676D50] dark:hover:bg-[#A6884C] px-4 py-2 rounded-lg transition-colors'
                    >
                      Download Image
                    </a>
                  </div>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </ProtectedLayout>
  );
};

export default ReferenceLibraryPage;
