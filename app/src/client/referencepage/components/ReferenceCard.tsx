import React from 'react';
import { ReferenceItem } from '../utils';

interface ReferenceCardProps {
  item: ReferenceItem;
  onClick?: (item: ReferenceItem) => void;
}

const ReferenceCard: React.FC<ReferenceCardProps> = ({ item, onClick }) => {
  const handleClick = () => {
    if (onClick) {
      onClick(item);
    }
  };

  return (
    <div
      className='bg-white dark:bg-gray-800 border border-[#B5B178]/20 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow cursor-pointer group'
      onClick={handleClick}
    >
      <div className='bg-gray-100 dark:bg-gray-700 relative overflow-hidden'>
        <img
          src={item.url}
          alt={`${item.category} - ${item.brand}`}
          className='w-full h-auto object-cover transition-transform duration-300 group-hover:scale-105'
          loading='lazy'
        />
      </div>

      <div className='p-3'>
        <div className='flex justify-between items-center'>
          <span className='text-xs font-medium text-[#676D50] dark:text-[#A6884C] bg-[#676D50]/10 dark:bg-[#A6884C]/10 px-2 py-1 rounded-full'>
            {item.category}
          </span>
          <span className='text-xs text-gray-500 dark:text-gray-400'>{item.brand}</span>
        </div>
      </div>
    </div>
  );
};

export default ReferenceCard;
