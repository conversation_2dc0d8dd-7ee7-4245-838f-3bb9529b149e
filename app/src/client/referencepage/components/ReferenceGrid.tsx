import React from 'react';
import { ReferenceItem } from '../utils';
import ReferenceCard from './ReferenceCard';

interface ReferenceGridProps {
  items: ReferenceItem[];
  viewMode: 'grid' | 'list';
  onItemClick?: (item: ReferenceItem) => void;
  lastItemRef?: (node: HTMLDivElement | null) => void;
}

const ReferenceGrid: React.FC<ReferenceGridProps> = ({ items, viewMode, onItemClick, lastItemRef }) => {
  if (items.length === 0) {
    return (
      <div className='flex flex-col items-center justify-center py-16 text-center'>
        <p className='text-lg text-[#676D50] dark:text-[#A6884C] mb-2'>No reference images found</p>
        <p className='text-sm text-gray-500 dark:text-gray-400'>Try adjusting your search or filters</p>
      </div>
    );
  }

  if (viewMode === 'list') {
    return (
      <>
        <div className='space-y-4'>
          {items.map((item, index) => (
            <div
              key={`${item.url}-${index}`}
              className='flex items-center bg-white dark:bg-gray-800 border border-[#B5B178]/20 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow cursor-pointer'
              onClick={() => onItemClick?.(item)}
            >
              <div className='w-24 flex-shrink-0'>
                <img
                  src={item.url}
                  alt={`${item.category} - ${item.brand}`}
                  className='w-full h-auto object-cover'
                  loading='lazy'
                />
              </div>

              <div className='p-4 flex-grow'>
                <div className='flex justify-between items-center'>
                  <span className='text-xs font-medium text-[#676D50] dark:text-[#A6884C] bg-[#676D50]/10 dark:bg-[#A6884C]/10 px-2 py-1 rounded-full'>
                    {item.category}
                  </span>
                  <span className='text-xs text-gray-500 dark:text-gray-400'>{item.brand}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Sentinel element for infinite scrolling - always at the bottom */}
        <div ref={lastItemRef} className='w-full h-20 flex items-center justify-center' />
      </>
    );
  }

  return (
    <>
      <div className='columns-2 sm:columns-3 md:columns-4 lg:columns-5 gap-4 space-y-4'>
        {items.map((item, index) => (
          <div key={`${item.url}-${index}`} className='break-inside-avoid mb-4'>
            <ReferenceCard item={item} onClick={onItemClick} />
          </div>
        ))}
      </div>

      {/* Sentinel element for infinite scrolling - always at the bottom */}
      <div ref={lastItemRef} className='w-full h-20 flex items-center justify-center' />
    </>
  );
};

export default ReferenceGrid;
