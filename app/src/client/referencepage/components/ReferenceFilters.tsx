import React from 'react';
import { Search, Filter, Grid, List, X } from 'lucide-react';
import { Button } from '../../components/ui/button';

interface ReferenceFiltersProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  categories: string[];
  selectedCategory: string;
  setSelectedCategory: (category: string) => void;
  brands: string[];
  selectedBrand: string;
  setSelectedBrand: (brand: string) => void;
  viewMode: 'grid' | 'list';
  setViewMode: (mode: 'grid' | 'list') => void;
  showFilters: boolean;
  setShowFilters: (show: boolean) => void;
  resetFilters: () => void;
}

const ReferenceFilters: React.FC<ReferenceFiltersProps> = ({
  searchQuery,
  setSearchQuery,
  categories,
  selectedCategory,
  setSelectedCategory,
  brands,
  selectedBrand,
  setSelectedBrand,
  viewMode,
  setViewMode,
  showFilters,
  setShowFilters,
  resetFilters,
}) => {
  const hasActiveFilters = selectedCategory !== '' || selectedBrand !== '' || searchQuery !== '';

  return (
    <>
      <div className='flex flex-col sm:flex-row justify-between items-stretch sm:items-center gap-4 sm:gap-0 mb-6'>
        <div className='flex flex-col sm:flex-row items-stretch sm:items-center gap-4 sm:gap-4 flex-grow'>
          <div className='relative flex-grow'>
            <Search
              size={20}
              className='absolute left-3 top-1/2 transform -translate-y-1/2 text-[#676D50] dark:text-[#A6884C]'
            />
            <input
              type='text'
              placeholder='Search references...'
              className='w-full pl-10 pr-4 py-2 border border-[#B5B178]/20 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#676D50] dark:focus:ring-[#A6884C] bg-white dark:bg-gray-800 text-[#1F2419] dark:text-white'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300'
              >
                <X size={16} />
              </button>
            )}
          </div>
          <button
            className={`w-full sm:w-auto flex items-center justify-center space-x-2 px-4 py-2 border border-[#B5B178]/20 dark:border-gray-700 rounded-lg ${
              showFilters
                ? 'bg-[#676D50] dark:bg-[#A6884C] text-white'
                : 'hover:bg-[#676D50]/10 dark:hover:bg-[#A6884C]/10 text-[#676D50] dark:text-[#A6884C]'
            }`}
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter size={20} />
            <span>Filter</span>
            {hasActiveFilters && (
              <span className='inline-flex items-center justify-center w-5 h-5 ml-1 text-xs font-semibold text-white bg-[#A6884C] dark:bg-[#676D50] rounded-full'>
                {(selectedCategory ? 1 : 0) + (selectedBrand ? 1 : 0)}
              </span>
            )}
          </button>
        </div>
        <div className='flex justify-end space-x-2'>
          <button
            className={`p-2 rounded-lg ${
              viewMode === 'grid'
                ? 'bg-[#676D50] dark:bg-[#A6884C] text-white'
                : 'hover:bg-[#676D50]/10 dark:hover:bg-[#A6884C]/10 text-[#676D50] dark:text-[#A6884C]'
            }`}
            onClick={() => setViewMode('grid')}
            aria-label='Grid view'
          >
            <Grid size={20} />
          </button>
          <button
            className={`p-2 rounded-lg ${
              viewMode === 'list'
                ? 'bg-[#676D50] dark:bg-[#A6884C] text-white'
                : 'hover:bg-[#676D50]/10 dark:hover:bg-[#A6884C]/10 text-[#676D50] dark:text-[#A6884C]'
            }`}
            onClick={() => setViewMode('list')}
            aria-label='List view'
          >
            <List size={20} />
          </button>
        </div>
      </div>

      {showFilters && (
        <div className='mb-6 space-y-4'>
          {/* Categories */}
          <div>
            <div className='flex justify-between items-center'>
              <div className='text-sm font-medium text-[#676D50] dark:text-[#A6884C] mb-2'>Categories</div>
              {hasActiveFilters && (
                <button onClick={resetFilters} className='text-xs text-[#676D50] dark:text-[#A6884C] hover:underline'>
                  Reset all filters
                </button>
              )}
            </div>
            <div className='flex flex-wrap gap-2'>
              <Button
                variant='rounded'
                className={
                  selectedCategory === ''
                    ? 'bg-[#676D50] dark:bg-[#A6884C] text-white text-sm'
                    : 'bg-[#676D50]/10 dark:bg-[#A6884C]/10 text-[#676D50] dark:text-[#A6884C] hover:bg-[#676D50]/20 dark:hover:bg-[#A6884C]/20 text-sm'
                }
                onClick={() => setSelectedCategory('')}
              >
                All
              </Button>
              {categories.map((category) => (
                <Button
                  key={category}
                  variant='rounded'
                  className={
                    selectedCategory === category
                      ? 'bg-[#676D50] dark:bg-[#A6884C] text-white text-sm'
                      : 'bg-[#676D50]/10 dark:bg-[#A6884C]/10 text-[#676D50] dark:text-[#A6884C] hover:bg-[#676D50]/20 dark:hover:bg-[#A6884C]/20 text-sm'
                  }
                  onClick={() => setSelectedCategory(category)}
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>

          {/* Brands */}
          <div>
            <div className='text-sm font-medium text-[#676D50] dark:text-[#A6884C] mb-2'>Brands</div>
            <div className='flex flex-wrap gap-2'>
              <Button
                variant='rounded'
                className={
                  selectedBrand === ''
                    ? 'bg-[#676D50] dark:bg-[#A6884C] text-white text-sm'
                    : 'bg-[#676D50]/10 dark:bg-[#A6884C]/10 text-[#676D50] dark:text-[#A6884C] hover:bg-[#676D50]/20 dark:hover:bg-[#A6884C]/20 text-sm'
                }
                onClick={() => setSelectedBrand('')}
              >
                All
              </Button>
              {brands.map((brand) => (
                <Button
                  key={brand}
                  variant='rounded'
                  className={
                    selectedBrand === brand
                      ? 'bg-[#676D50] dark:bg-[#A6884C] text-white text-sm'
                      : 'bg-[#676D50]/10 dark:bg-[#A6884C]/10 text-[#676D50] dark:text-[#A6884C] hover:bg-[#676D50]/20 dark:hover:bg-[#A6884C]/20 text-sm'
                  }
                  onClick={() => setSelectedBrand(brand)}
                >
                  {brand}
                </Button>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ReferenceFilters;
