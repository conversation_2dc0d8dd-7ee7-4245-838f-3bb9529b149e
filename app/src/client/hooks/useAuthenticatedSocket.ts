import { useEffect } from 'react';
import { useSocket } from 'wasp/client/webSocket';
import { useAuth } from 'wasp/client/auth';
import type { ClientToServerEvents, ServerToClientEvents } from '../websocket/types';

/**
 * Hook that wraps useSocket and automatically authenticates the socket
 * when it connects and user data is available.
 *
 * @returns The same interface as useSocket: { socket, isConnected }
 */
export const useAuthenticatedSocket = () => {
  const { socket, isConnected } = useSocket();
  // Make socket available globally for debugging
  if (typeof window !== 'undefined') (window as any).socket = socket;

  const { data: user } = useAuth();

  useEffect(() => {
    if (isConnected && user?.id && socket) {
      console.log(`[useAuthenticatedSocket] Authenticating socket for user ${user.id}`);
      try {
        // Cast socket to any to avoid type errors with the 'authenticate' event
        // This is a temporary solution until we update all WebSocket events
        (socket as any).emit('authenticate', { userId: user.id });
        console.log(`[useAuthenticatedSocket] Authentication event emitted for user ${user.id}`);
      } catch (error) {
        console.error('[useAuthenticatedSocket] Error authenticating socket:', error);
      }
    }
  }, [isConnected, user?.id, socket]);

  return { socket, isConnected };
};
