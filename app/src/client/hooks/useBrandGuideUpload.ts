import { useState } from 'react';
import { api } from 'wasp/client/api';

interface BrandGuideResult {
  brandElements: {
    colors: string[];
    typography: any[];
    logoUrls: string[];
    visualStyle: string;
  };
  brandGuidelines: {
    brandVoice: string;
    tonality: string;
    messaging: string[];
    dosDonts: any[];
    usageGuidelines: string[];
  };
  extractedContent: {
    text: string;
    pages: number;
    images: string[];
    metadata: any;
  };
  structuredData: {
    mission: string;
    vision: string;
    values: string[];
    positioning: string;
    targetAudience: string;
  };
}

interface UploadState {
  uploading: boolean;
  progress: number;
  result: BrandGuideResult | null;
  error: string | null;
}

export function useBrandGuideUpload() {
  const [state, setState] = useState<UploadState>({
    uploading: false,
    progress: 0,
    result: null,
    error: null,
  });

  const uploadBrandGuide = async (file: File, brandName: string, organizationId: string) => {
    setState({
      uploading: true,
      progress: 0,
      error: null,
      result: null,
    });

    try {
      const formData = new FormData();
      formData.append('brandGuide', file);
      formData.append('brandName', brandName);
      formData.append('organizationId', organizationId);

      const response = await api.post('/api/onboarding/brand-guide/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent: any) => {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / (progressEvent.total || progressEvent.loaded)
          );
          setState((prev) => ({
            ...prev,
            progress: percentCompleted,
          }));
        },
      });

      // response.data is expected to be { success: true, data: { message: string, r2Url: string, fileName: string } }
      // The hook should make this r2Url available to the component
      const resultData = response.data.data; // This should contain r2Url and fileName

      setState((prev) => ({
        ...prev,
        // result: response.data.data, // Old: result was the processed data, now it's upload info
        result: null, // Clear previous processing result if any, or redefine `result` in UploadState
        uploading: false,
        // We can add r2Url and fileName to the state if needed for direct access from the hook consumer
        // For now, just returning it from the function is often sufficient.
      }));

      return resultData; // Return { message, r2Url, fileName }
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || error.message || 'Upload failed';
      setState((prev) => ({
        ...prev,
        error: errorMessage,
        uploading: false,
      }));
      throw new Error(errorMessage);
    }
  };

  const checkStatus = async (organizationId: string) => {
    try {
      const response = await api.get('/api/onboarding/brand-guide/status', {
        params: { organizationId },
      });
      return response.data.data;
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || error.message || 'Status check failed';
      throw new Error(errorMessage);
    }
  };

  const reset = () => {
    setState({
      uploading: false,
      progress: 0,
      result: null,
      error: null,
    });
  };

  return {
    ...state,
    uploadBrandGuide,
    checkStatus,
    reset,
  };
}

// PDF Validation utilities
export class PDFValidator {
  static readonly MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
  static readonly ALLOWED_MIME_TYPES = ['application/pdf'];

  static validateFile(file: File): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      errors.push(`File size exceeds ${this.MAX_FILE_SIZE / (1024 * 1024)}MB limit`);
    }

    // Check MIME type
    if (!this.ALLOWED_MIME_TYPES.includes(file.type)) {
      errors.push('Only PDF files are allowed');
    }

    // Check file extension
    if (!file.name.toLowerCase().endsWith('.pdf')) {
      errors.push('File must have .pdf extension');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static sanitizeFileName(fileName: string): string {
    return fileName
      .replace(/[^a-zA-Z0-9.-]/g, '_')
      .replace(/_{2,}/g, '_')
      .substring(0, 255);
  }
}
