import { useState } from 'react';
import { api } from 'wasp/client/api';
import { useAction } from 'wasp/client/operations';
import { analyzeWebsite, analyzeWebsiteVisuals } from 'wasp/client/operations';

interface WebsiteAnalysisInput {
  websiteUrl: string;
  brandName: string;
  deepCrawl?: boolean;
  maxPages?: number;
}

interface WebsiteAnalysisResult {
  brandElements: {
    colors: string[];
    typography: string[];
    logoUrl?: string;
    visualStyle: string;
  };
  contentAnalysis: {
    brandVoice: string;
    tonality: string;
    keyMessages: string[];
    valuePropositions: string[];
    targetAudience: string;
  };
  structuredData: {
    companyInfo: any;
    products: any[];
    services: any[];
    contactInfo: any;
  };
  seoMetadata: {
    title: string;
    description: string;
    keywords: string[];
  };
  technicalDetails: {
    framework: string;
    performance: any;
    accessibility: any;
  };
}

export const useWebsiteAnalysis = () => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<WebsiteAnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);

  // Use the actual Wasp actions
  const analyzeWebsiteFn = useAction(analyzeWebsite);
  const analyzeVisualsFn = useAction(analyzeWebsiteVisuals);

  const analyzeWebsiteUrl = async (input: WebsiteAnalysisInput) => {
    setIsAnalyzing(true);
    setError(null);
    setProgress(0);

    try {
      setProgress(20);

      // Call the actual Wasp action
      const result = await analyzeWebsiteFn({
        websiteUrl: input.websiteUrl,
        brandName: input.brandName,
        deepCrawl: input.deepCrawl || false,
        maxPages: input.maxPages || 10,
      });

      setProgress(100);
      setAnalysisResult(result);

      return result;
    } catch (err: any) {
      console.error('Website analysis failed:', err);
      setError(err.message || 'An error occurred during website analysis');
      throw err;
    } finally {
      setIsAnalyzing(false);
    }
  };

  const analyzeVisuals = async (screenshot: string, brandName: string) => {
    try {
      // Call the actual Wasp action
      const result = await analyzeVisualsFn({
        screenshot,
        brandName,
      });
      return result;
    } catch (err: any) {
      console.error('Visual analysis failed:', err);
      setError(err.message || 'An error occurred during visual analysis');
      throw err;
    }
  };

  const checkCrawlStatus = async (url: string) => {
    try {
      const response = await api.get('/api/website/crawl-status', {
        params: { url },
      });
      return response.data;
    } catch (err: any) {
      console.error('Failed to check crawl status:', err);
      throw err;
    }
  };

  const reset = () => {
    setIsAnalyzing(false);
    setAnalysisResult(null);
    setError(null);
    setProgress(0);
  };

  return {
    isAnalyzing,
    analysisResult,
    error,
    progress,
    analyzeWebsiteUrl,
    analyzeVisuals,
    checkCrawlStatus,
    reset,
  };
};

// Helper hook for form validation
export const useWebsiteAnalysisValidation = () => {
  const validateUrl = (url: string): { isValid: boolean; error?: string } => {
    if (!url) {
      return { isValid: false, error: 'URL is required' };
    }

    try {
      const urlObj = new URL(url);

      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return { isValid: false, error: 'URL must use HTTP or HTTPS protocol' };
      }

      // Check for localhost or private networks
      const hostname = urlObj.hostname.toLowerCase();
      const blockedPatterns = ['localhost', '127.0.0.1', '0.0.0.0'];

      if (blockedPatterns.some((pattern) => hostname.includes(pattern))) {
        return { isValid: false, error: 'Cannot analyze localhost or private network URLs' };
      }

      return { isValid: true };
    } catch {
      return { isValid: false, error: 'Please enter a valid URL' };
    }
  };

  const validateBrandName = (brandName: string): { isValid: boolean; error?: string } => {
    if (!brandName) {
      return { isValid: false, error: 'Brand name is required' };
    }

    if (brandName.length < 2) {
      return { isValid: false, error: 'Brand name must be at least 2 characters long' };
    }

    if (brandName.length > 100) {
      return { isValid: false, error: 'Brand name must be less than 100 characters' };
    }

    return { isValid: true };
  };

  return {
    validateUrl,
    validateBrandName,
  };
};
