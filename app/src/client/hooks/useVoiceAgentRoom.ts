import { useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';

/**
 * Returns a stable room name (existing one from localStorage or a new UUID-based one).
 * All components that need the room should call this hook instead of reading localStorage.
 */
export function useVoiceAgentRoom(): string {
  // `useRef` ensures the value never changes for this React lifetime.
  const roomRef = useRef<string>();

  if (!roomRef.current) {
    // Only access localStorage in browser environment
    if (typeof window !== 'undefined') {
      // 1. Try to reuse any previously stored room.
      const stored = localStorage.getItem('currentVoiceAgentRoom');

      // 2. If none, mint a fresh one.
      roomRef.current = stored ?? `voice-agent-${uuidv4()}`;

      // 3. Persist it (does nothing if it was already there).
      localStorage.setItem('currentVoiceAgentRoom', roomRef.current);

      console.log(`[useVoiceAgentRoom] Using room name: ${roomRef.current}`);
    } else {
      // Fallback for SSR
      roomRef.current = `voice-agent-ssr-placeholder`;
    }
  }

  return roomRef.current;
}
