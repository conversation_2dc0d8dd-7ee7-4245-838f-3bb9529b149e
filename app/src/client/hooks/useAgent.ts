/**
 * Agent Hook
 *
 * This hook provides access to the agent functionality.
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { useAuth } from 'wasp/client/auth';
import { useSocket, useSocketListener } from 'wasp/client/webSocket';

// Define agent state
interface AgentState {
  isActive: boolean;
  threadId: string | null;
  status: 'idle' | 'active' | 'paused' | 'completed' | 'error';
  error: string | null;
  userMessage: string;
  agentResponse: string;
  waitingForHumanInput: boolean;
  humanInputQuestion: string;
  humanInputContext: string;
  humanInputOptions: any;
}

// Define agent options
interface UseAgentOptions {
  onComplete?: (message: string) => void;
  onError?: (error: string) => void;
  onProgress?: (progress: number, message: string) => void;
  onHumanInputRequest?: (question: string, context: string, options: any) => void;
}

/**
 * Hook to use the agent
 * @param options Agent options
 * @returns The agent state and methods
 */
export const useAgent = (options: UseAgentOptions = {}) => {
  const { data: user } = useAuth();
  const { socket, isConnected } = useSocket();
  const [state, setState] = useState<AgentState>({
    isActive: false,
    threadId: null,
    status: 'idle',
    error: null,
    userMessage: '',
    agentResponse: '',
    waitingForHumanInput: false,
    humanInputQuestion: '',
    humanInputContext: '',
    humanInputOptions: {},
  });

  // Create refs for callbacks to avoid dependency issues
  const onCompleteRef = useRef(options.onComplete);
  const onErrorRef = useRef(options.onError);
  const onProgressRef = useRef(options.onProgress);
  const onHumanInputRequestRef = useRef(options.onHumanInputRequest);

  // Update refs when callbacks change
  useEffect(() => {
    onCompleteRef.current = options.onComplete;
    onErrorRef.current = options.onError;
    onProgressRef.current = options.onProgress;
    onHumanInputRequestRef.current = options.onHumanInputRequest;
  }, [options.onComplete, options.onError, options.onProgress, options.onHumanInputRequest]);

  // Create a ref for the state to avoid closure issues
  const stateRef = useRef(state);
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  // Function to emit an event
  const emitEvent = useCallback(
    (event: 'start_agent' | 'resume_agent' | 'pause_agent' | 'cancel_agent', data: any) => {
      if (!socket || !isConnected) {
        console.error(`[Agent] Cannot emit ${event} - socket not connected`);
        return;
      }

      console.log(`[Agent] Emitting ${event}:`, data);
      socket.emit(event, data);
    },
    [socket, isConnected]
  );

  // Function to start the agent
  const startAgent = useCallback(
    (initialMessage: string, metadata: Record<string, any> = {}) => {
      if (!user?.id) {
        console.error('[Agent] Cannot start agent - user not logged in');
        return;
      }

      // Emit the start_agent event
      emitEvent('start_agent', {
        userId: user.id,
        initialMessage,
        metadata,
      });

      // Update state
      setState((prev) => ({
        ...prev,
        isActive: true,
        status: 'active',
        userMessage: initialMessage,
      }));
    },
    [user?.id, emitEvent]
  );

  // Function to provide human input
  const provideHumanInput = useCallback(
    (humanInput: string, metadata: Record<string, any> = {}) => {
      if (!state.threadId) {
        console.error('[Agent] Cannot provide human input - no active thread');
        return;
      }

      // Emit the resume_agent event
      emitEvent('resume_agent', {
        threadId: state.threadId,
        humanInput,
        metadata,
      });

      // Update state
      setState((prev) => ({
        ...prev,
        waitingForHumanInput: false,
        humanInputQuestion: '',
        humanInputContext: '',
        humanInputOptions: {},
      }));
    },
    [state.threadId, emitEvent]
  );

  // Function to pause the agent
  const pauseAgent = useCallback(
    (reason: string = 'Paused by user') => {
      if (!state.threadId) {
        console.error('[Agent] Cannot pause agent - no active thread');
        return;
      }

      // Emit the pause_agent event
      emitEvent('pause_agent', {
        threadId: state.threadId,
        reason,
      });
    },
    [state.threadId, emitEvent]
  );

  // Function to cancel the agent
  const cancelAgent = useCallback(
    (reason: string = 'Cancelled by user') => {
      if (!state.threadId) {
        console.error('[Agent] Cannot cancel agent - no active thread');
        return;
      }

      // Emit the cancel_agent event
      emitEvent('cancel_agent', {
        threadId: state.threadId,
        reason,
      });

      // Update state
      setState((prev) => ({
        ...prev,
        isActive: false,
        status: 'idle',
        threadId: null,
      }));
    },
    [state.threadId, emitEvent]
  );

  // Handle agent started event
  const handleAgentStarted = useCallback((data: { threadId: string; message: string }) => {
    console.log('[Agent] Agent started:', data);

    // Update state
    setState((prev) => ({
      ...prev,
      threadId: data.threadId,
    }));
  }, []);

  // Handle agent resumed event
  const handleAgentResumed = useCallback((data: { threadId: string; message: string }) => {
    console.log('[Agent] Agent resumed:', data);
  }, []);

  // Handle agent paused event
  const handleAgentPaused = useCallback((data: { threadId: string; message: string }) => {
    console.log('[Agent] Agent paused:', data);

    // Update state
    setState((prev) => ({
      ...prev,
      status: 'paused',
    }));
  }, []);

  // Handle agent cancelled event
  const handleAgentCancelled = useCallback((data: { threadId: string; message: string }) => {
    console.log('[Agent] Agent cancelled:', data);

    // Update state
    setState((prev) => ({
      ...prev,
      isActive: false,
      status: 'idle',
      threadId: null,
    }));
  }, []);

  // Handle agent error event
  const handleAgentError = useCallback((data: { message: string; error?: string }) => {
    console.error('[Agent] Agent error:', data);

    // Update state
    setState((prev) => ({
      ...prev,
      status: 'error',
      error: data.error || data.message,
    }));

    // Call the onError callback
    if (onErrorRef.current) {
      onErrorRef.current(data.error || data.message);
    }
  }, []);

  // Handle agent complete event
  const handleAgentComplete = useCallback((data: { threadId: string; message: string }) => {
    console.log('[Agent] Agent complete:', data);

    // Update state
    setState((prev) => ({
      ...prev,
      status: 'completed',
      agentResponse: data.message,
    }));

    // Call the onComplete callback
    if (onCompleteRef.current) {
      onCompleteRef.current(data.message);
    }
  }, []);

  // Handle update progress event
  const handleUpdateProgress = useCallback(
    (data: { threadId: string; message: string; progress: number; error?: boolean }) => {
      console.log('[Agent] Update progress:', data);

      // Update state
      setState((prev) => ({
        ...prev,
        agentResponse: data.message,
      }));

      // Call the onProgress callback
      if (onProgressRef.current) {
        onProgressRef.current(data.progress, data.message);
      }

      // If there's an error, update the state
      if (data.error) {
        setState((prev) => ({
          ...prev,
          status: 'error',
          error: data.message,
        }));

        // Call the onError callback
        if (onErrorRef.current) {
          onErrorRef.current(data.message);
        }
      }
    },
    []
  );

  // Handle request human input event
  const handleRequestHumanInput = useCallback(
    (data: { threadId: string; question: string; context: string; options: any }) => {
      console.log('[Agent] Request human input:', data);

      // Update state
      setState((prev) => ({
        ...prev,
        waitingForHumanInput: true,
        humanInputQuestion: data.question,
        humanInputContext: data.context,
        humanInputOptions: data.options,
      }));

      // Call the onHumanInputRequest callback
      if (onHumanInputRequestRef.current) {
        onHumanInputRequestRef.current(data.question, data.context, data.options);
      }
    },
    []
  );

  // Set up WebSocket event listeners
  useSocketListener('agent_started', handleAgentStarted);
  useSocketListener('agent_resumed', handleAgentResumed);
  useSocketListener('agent_paused', handleAgentPaused);
  useSocketListener('agent_cancelled', handleAgentCancelled);
  useSocketListener('agent_error', handleAgentError);
  useSocketListener('agent_complete', handleAgentComplete);
  useSocketListener('update_progress', handleUpdateProgress);
  useSocketListener('request_human_input', handleRequestHumanInput);

  // Return the agent state and methods
  return {
    ...state,
    startAgent,
    provideHumanInput,
    pauseAgent,
    cancelAgent,
  };
};
