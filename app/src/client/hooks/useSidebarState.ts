import React, { useContext, createContext, useState, useEffect, useCallback, useMemo } from 'react';

interface SidebarState {
  isOpen: boolean;
  isMobile: boolean;
  isHovered: boolean;
  toggleSidebar: () => void;
  closeSidebar: () => void;
  setIsHovered: (value: boolean) => void;
}

const defaultState: SidebarState = {
  isOpen: false,
  isMobile: false,
  isHovered: false,
  toggleSidebar: () => undefined,
  closeSidebar: () => undefined,
  setIsHovered: () => undefined,
};

const SidebarContext = createContext<SidebarState>(defaultState);

interface SidebarProviderProps {
  children: React.ReactNode;
}

export const SidebarProvider: React.FC<SidebarProviderProps> = ({ children }) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [isHovered, setIsHovered] = useState<boolean>(false);

  const toggleSidebar = useCallback(() => setIsOpen((prev) => !prev), []);
  const closeSidebar = useCallback(() => setIsOpen(false), []);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(max-width: 768px)');

    const handleMediaQueryChange = (e: MediaQueryListEvent) => {
      setIsMobile(e.matches);
    };

    // Initial check
    setIsMobile(mediaQuery.matches);

    // Add listener for subsequent changes
    mediaQuery.addEventListener('change', handleMediaQueryChange);

    return () => {
      mediaQuery.removeEventListener('change', handleMediaQueryChange);
    };
  }, []);

  const value = useMemo<SidebarState>(
    () => ({
      isOpen,
      isMobile,
      isHovered,
      toggleSidebar,
      closeSidebar,
      setIsHovered,
    }),
    [isOpen, isMobile, isHovered, toggleSidebar, closeSidebar, setIsHovered]
  );

  return React.createElement(SidebarContext.Provider, {
    value,
    children,
  });
};

export const useSidebarState = (): SidebarState => {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error('useSidebarState must be used within a SidebarProvider');
  }
  return context;
};
