import { useState, useCallback, useEffect } from 'react';
import { useSocket, useSocketListener } from 'wasp/client/webSocket';
import { useAction } from 'wasp/client/operations';
import { api } from 'wasp/client/api';
import { aiSynthesis } from 'wasp/client/operations';
import { SYNTHESIS_STAGES } from '../../features/onboarding/domain/constants';

interface SynthesisInput {
  organizationId: string;
  websiteUrl: string;
  brandName: string;
  brandGuideFile?: string;
  shopifyIntegration?: {
    connected: boolean;
    domain?: string;
    data?: any;
  };
  assetFolderIntegration?: {
    connected: boolean;
    providers?: string[];
    data?: any;
  };
}

interface SynthesisProgress {
  stage: string;
  progress: number;
  message: string;
  completed: boolean;
  timestamp?: string;
  sessionId?: string;
  metadata?: any;
}

interface SynthesisResult {
  success: boolean;
  brandKit?: any;
  audiences?: any[];
  products?: any[];
  assets?: any[];
  sessionId: string;
  completedStages: string[];
  totalProcessingTime: number;
  errors?: any[];
  [key: string]: any;
}

interface SynthesisSession {
  sessionId: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled' | 'not_found';
  currentStage?: string;
  completedStages: string[];
  errors: any[];
  startTime: number;
  progress: {
    completed: number;
    total: number;
    percentage: number;
  };
}

export interface StageProgressData {
  progress: number;
  completed: boolean;
  message: string;
  active: boolean;
  error?: string;
}

export function useSynthesis() {
  const socket = useSocket();
  const [latestProgressUpdate, setLatestProgressUpdate] = useState<SynthesisProgress | null>(null);
  const [stageProgressMap, setStageProgressMap] = useState<Record<string, StageProgressData>>({});
  const [result, setResult] = useState<SynthesisResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [session, setSession] = useState<SynthesisSession | null>(null);

  const startSynthesisFn = useAction(aiSynthesis);

  useSocketListener('synthesis_progress' as any, (data: SynthesisProgress) => {
    console.log('[useSynthesis] Raw Progress update from WebSocket:', data);
    setLatestProgressUpdate(data);
    setSessionId(data.sessionId || null);

    setStageProgressMap((prevMap) => {
      const newMap = { ...prevMap };
      Object.keys(newMap).forEach((key) => {
        if (key !== data.stage) {
          newMap[key] = { ...newMap[key], active: false };
        }
      });
      newMap[data.stage] = {
        progress: data.progress,
        completed: data.completed,
        message: data.message,
        active: !data.completed,
        error: data.metadata?.error as string | undefined,
      };
      return newMap;
    });
  });

  useSocketListener(
    'synthesis_complete' as any,
    (data: { sessionId: string; result: SynthesisResult; timestamp: string; status: string }) => {
      console.log('[useSynthesis] Synthesis completed:', data);
      setResult(data.result);
      setLatestProgressUpdate((prev) => ({
        stage: prev?.stage || 'final',
        progress: 100,
        completed: true,
        message: 'Synthesis completed successfully.',
        sessionId: data.sessionId,
      }));
      setStageProgressMap((prevMap) => {
        const finalMap = { ...prevMap };
        Object.keys(finalMap).forEach((key) => {
          if (data.result.completedStages?.includes(key) || finalMap[key]) {
            finalMap[key] = {
              ...(finalMap[key] || { progress: 0, message: '', active: false }),
              completed: true,
              progress: 100,
              active: false,
            };
          }
        });
        return finalMap;
      });
      setIsLoading(false);
    }
  );

  useSocketListener(
    'synthesis_error' as any,
    (data: { sessionId: string; error: string; timestamp: string; status: string }) => {
      console.log('[useSynthesis] Synthesis error:', data);
      setError(data.error);
      if (latestProgressUpdate?.stage) {
        setStageProgressMap((prevMap) => ({
          ...prevMap,
          [latestProgressUpdate.stage]: {
            ...(prevMap[latestProgressUpdate.stage] || { progress: 0, completed: false, message: '', active: false }),
            error: data.error,
            active: false,
          },
        }));
      }
      setIsLoading(false);
    }
  );

  useSocketListener('synthesis_joined' as any, (data: { roomName: string; sessionId: string }) => {
    console.log('[useSynthesis] Joined synthesis room:', data);
    setSessionId(data.sessionId);
  });

  useSocketListener('synthesis_session_status' as any, (data: any) => {
    console.log('[useSynthesis] Session status:', data);
    if (data.status !== 'not_found') {
      setSession(data);
    }
  });

  const startSynthesis = useCallback(
    async (input: SynthesisInput, initialStageIds: string[]) => {
      try {
        setLatestProgressUpdate(null);
        const initialMap: Record<string, StageProgressData> = {};
        initialStageIds.forEach((id) => {
          initialMap[id] = { progress: 0, completed: false, message: 'Pending...', active: false };
        });
        setStageProgressMap(initialMap);
        setResult(null);
        setError(null);
        setIsLoading(true);
        setSession(null);

        console.log('[useSynthesis] Starting synthesis with input:', input, 'Initial stages:', initialStageIds);
        const synthResult = (await startSynthesisFn(input)) as SynthesisResult;
        console.log('[useSynthesis] Synthesis action HTTP call completed:', synthResult);

        if (synthResult && synthResult.sessionId) {
          setSessionId(synthResult.sessionId);
        }
        if (synthResult && synthResult.success === false && synthResult.errors) {
          setError(JSON.stringify(synthResult.errors) || 'Synthesis initiation failed.');
          setIsLoading(false);
        }
      } catch (error: any) {
        console.error('[useSynthesis] Failed to start synthesis HTTP action:', error);
        setError(error.message || 'Failed to start synthesis action');
        setIsLoading(false);
      }
    },
    [startSynthesisFn]
  );

  const resetSynthesis = useCallback(() => {
    setLatestProgressUpdate(null);
    setStageProgressMap({});
    setResult(null);
    setError(null);
    setSessionId(null);
    setIsLoading(false);
    setSession(null);
  }, []);

  const isOverallCompleted =
    !!result?.success || (latestProgressUpdate?.completed && latestProgressUpdate.progress === 100);

  return {
    latestProgressUpdate,
    stageProgressMap,
    result,
    error,
    sessionId,
    isLoading,
    session,
    startSynthesis,
    resetSynthesis,
    isCompleted: isOverallCompleted,
    hasError: !!error,
    currentStageId: latestProgressUpdate?.stage || session?.currentStage,
  };
}

export type { SynthesisInput, SynthesisProgress, SynthesisResult, SynthesisSession };
