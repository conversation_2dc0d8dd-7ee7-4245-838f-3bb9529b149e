import { useAuth } from 'wasp/client/auth';
import { useAction, updateBrandKit, insertCategoryFieldValue, updateCategoryFieldValue } from 'wasp/client/operations';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { debounce } from 'lodash';
import toast from 'react-hot-toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { z, ZodType } from 'zod';

export type BrandKitFormData = {
  name?: string;
};

const BrandKitSchema: ZodType<BrandKitFormData> = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
});

export const getfieldName = (categoryId: number, fieldId: number) => `category-${categoryId}-field-${fieldId}`;

interface Field {
  id: number;
  fieldValueId: number;
  name: string;
  fieldType: string;
  value: string;
}

interface Category {
  id: number;
  name: string;
  fields: Field[];
}

type GroupedFields = {
  [key: string]: Field[];
};

function groupCategoryFields(categories: Category[]): GroupedFields {
  const groupedFields: GroupedFields = {};

  categories.forEach((category) => {
    category.fields.forEach((field) => {
      const key = `category-${category.id}-field-${field.id}`;
      if (!groupedFields[key]) {
        groupedFields[key] = [];
      }
      groupedFields[key].push(field);
    });
  });

  return groupedFields;
}

export default function useEditBrandKit(intialData: any) {
  const { data: user } = useAuth();
  const updateBrandKitAction = useAction(updateBrandKit);
  const insertCategoryFieldValueAction = useAction(insertCategoryFieldValue);
  const updateCategoryFieldValueAction = useAction(updateCategoryFieldValue);

  const form = useForm<BrandKitFormData>({
    mode: 'onChange',
    resolver: zodResolver(BrandKitSchema),
  });

  const onSubmit = async (updatedFormData: BrandKitFormData) => {
    if (form.formState.isDirty && user?.id) {
      try {
        await updateBrandKitAction({
          id: intialData?.id,
          ...updatedFormData,
        });
        toast.success('Brand kit updated successfully');
      } catch (err) {
        console.log('updateBrandKitAction error: ', err);
        toast.error('Brand kit update failed');
      }
    }
  };

  const insertFieldValue = async (brandCategoryId: number, categoryFieldId: number, type: string, value: string) => {
    try {
      if (['text', 'string'].includes(type)) {
        const name = getfieldName(brandCategoryId, categoryFieldId);
        const fieldValue = (form.getValues() as any)[name]?.[0] ?? null;

        if (fieldValue) {
          await updateCategoryFieldValueAction({
            id: fieldValue.fieldValueId,
            value,
          });
          return { id: fieldValue.fieldValueId, value };
        } else {
          const response = await insertCategoryFieldValueAction({
            brandKitId: intialData?.id,
            categoryFieldId,
            brandCategoryId,
            value,
          });
          return response;
        }
      } else {
        const response = await insertCategoryFieldValueAction({
          brandKitId: intialData?.id,
          categoryFieldId,
          brandCategoryId,
          value,
        });
        return response;
      }
    } catch (err) {
      console.log('insertCategoryFieldValueAction error: ', err);
      toast.error('Brand kit update failed');
      throw err;
    }
  };

  const debouncedOnSubmit = debounce(onSubmit, 500);

  useEffect(() => {
    const subscription = form.watch(() => form.handleSubmit(debouncedOnSubmit)());
    return () => {
      subscription.unsubscribe();
      debouncedOnSubmit.cancel();
    };
  }, [form.handleSubmit, form.watch, intialData]);

  useEffect(() => {
    if (intialData) {
      const data = {
        name: intialData.name,
        ...groupCategoryFields(intialData?.categories),
      };
      form.reset(data);
    }
  }, [intialData, form]);

  // Cleanup effect for debounce
  useEffect(() => {
    return () => {
      debouncedOnSubmit.cancel();
    };
  }, [debouncedOnSubmit]);

  return { form, insertFieldValue };
}
