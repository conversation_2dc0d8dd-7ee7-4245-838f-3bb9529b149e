/**
 * @deprecated This component is not being used at the moment.
 * TODO: Remove or refactor when model creation functionality is needed.
 * Last updated: January 2025
 */

import React, { useState } from 'react';
import { useQuery } from 'wasp/client/operations';
import { useLocation, useNavigate } from 'react-router-dom';
import { getProducts, addNewModel, uploadModelTrainingImages } from 'wasp/client/operations';
import { ProductModelForm } from './modelTraining/components/ProductModelForm';
import type { ImageFile, ModelType, ImageInput } from './modelTraining/types';
import toast from 'react-hot-toast';
import { useOrganizationState } from '../organization/store';
import useFeatureFlags from './hooks/use-feature-flags';
import { v4 as uuidv4 } from 'uuid';

interface ApiError {
  statusCode?: number;
  message: string;
}

function isApiError(error: unknown): error is ApiError {
  return typeof error === 'object' && error !== null && ('message' in error || 'statusCode' in error);
}

type TextComplexity = 'simple' | 'complex';

type AddNewModelInput = {
  name: string;
  imageUrls: string[];
  productId?: number;
  organizationId: string;
  textComplexity: TextComplexity;
  provider: 'runpod' | 'bfl';
  type: ModelType;
};

export function CreateModelPage() {
  const { selectedOrganizationId } = useOrganizationState();
  const {
    data: products,
    isLoading: productsLoading,
    error: productsError,
  } = useQuery(getProducts, { organizationId: selectedOrganizationId! }, { enabled: !!selectedOrganizationId });
  const { featureEnabled } = useFeatureFlags();
  const hasBflAccess = featureEnabled(['asset-pro_model_training']);

  const isLoading = productsLoading;
  const error = productsError;
  const location = useLocation();
  const navigate = useNavigate();
  const [isCreating, setIsCreating] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const state = location.state as {
    productName?: string;
    productId?: number;
    productImages?: string[];
    fromProduct?: boolean;
  };

  console.log('CreateModelPage - Products:', products);
  console.log('CreateModelPage - Location state:', state);

  const handleModelSubmit = async (
    modelName: string,
    images: ImageInput[],
    productId?: number,
    textComplexity: TextComplexity = 'simple',
    provider: 'runpod' | 'bfl' = 'runpod',
    modelType: ModelType = 'product'
  ) => {
    try {
      setIsCreating(true);
      console.log('Submitting model with images:', images);

      // Show processing toast
      const processingToast = toast.loading('Processing images...');

      // Generate a unique model ID using uuid to match backend format
      const modelId = `${modelName}_${uuidv4().substring(0, 5)}`.replace(/ /g, '-');

      // Upload images in batches of 2
      const BATCH_SIZE = 2;
      const allUploadedUrls: string[] = [];
      
      for (let i = 0; i < images.length; i += BATCH_SIZE) {
        const batch = images.slice(i, i + BATCH_SIZE);

        // Ensure each image has either fileBuffer or url
        const validBatch = batch.map(image => {
          if (!image.fileBuffer && !image.url) {
            throw new Error('Image must have either fileBuffer or url');
          }
          if (image.url) {
            return {
              url: image.url,
              fileName: image.fileName,
              contentType: image.contentType
            };
          }
          return {
            fileBuffer: image.fileBuffer,
            fileName: image.fileName,
            contentType: image.contentType
          };
        });

        // Upload batch to R2
        const result = await uploadModelTrainingImages({
          images: validBatch,
          modelId: modelId,
          modelType: modelType
        });

        if (!result.success || !result.urls) {
          throw new Error(result.error || 'Failed to upload images');
        }

        allUploadedUrls.push(...result.urls);
        
        // Update progress
        setUploadProgress(Math.round((i + batch.length) / images.length * 100));
      }

      // Update processing toast
      toast.loading('Creating model...', { id: processingToast });

      const modelData: AddNewModelInput = {
        name: modelName,
        imageUrls: allUploadedUrls,
        organizationId: selectedOrganizationId as string,
        textComplexity,
        provider,
        type: modelType,
        ...(productId && { productId }),
      };

      const result = await addNewModel(modelData);

      // Success toast
      toast.success('Model created successfully', { id: processingToast });

      // Navigate to photography page and force a refetch of models
      console.log('Model created successfully:', result);
      navigate('/photography', {
        state: {
          refetchModels: true,
          newModelId: result.id,
          newModelName: modelName,
        },
        replace: true, // Replace current history entry to prevent back navigation to form
      });
    } catch (err) {
      console.error('Error in model training process:', err);

      // Type guard for API error
      if (isApiError(err)) {
        // Show appropriate error message
        if (err.statusCode === 402) {
          toast.error('Insufficient credits. Please upgrade your plan.');
        } else {
          toast.error(err.message || 'Failed to create model. Please try again.');
        }
      } else {
        toast.error('An unexpected error occurred. Please try again.');
      }
    } finally {
      setIsCreating(false);
      setUploadProgress(0);
      setUploadProgress(0);
    }
  };

  if (isLoading) {
    return (
      <div className='flex justify-center items-center min-h-screen bg-[#F0EFE9] dark:bg-gray-900'>
        <div className='text-lg text-[#676D50] dark:text-[#A6884C]'>Loading...</div>
      </div>
    );
  }

  if (error) {
    const errorMessage = isApiError(error) ? error.message : 'An error occurred while loading products';

    return (
      <div className='flex justify-center items-center min-h-screen bg-[#F0EFE9] dark:bg-gray-900'>
        <div className='text-lg text-red-600 dark:text-red-400'>Error loading products: {errorMessage}</div>
      </div>
    );
  }

  // Find initial product if we have an ID from state
  const initialProduct = state?.productId && products ? products.find((p) => p.id === state.productId) : undefined;

  console.log('CreateModelPage - Initial product:', initialProduct);

  return (
    <div className='min-h-screen bg-[#F0EFE9] dark:bg-gray-900 text-[#1F2419] dark:text-white'>
      <div className='container mx-auto p-6'>
        <ProductModelForm
          products={products || []}
          onSubmit={handleModelSubmit}
          initialProduct={initialProduct}
          initialImages={state?.productImages}
          isSubmitting={isCreating}
          hasProAccess={hasBflAccess}
          uploadProgress={uploadProgress}
        />
      </div>
    </div>
  );
}
