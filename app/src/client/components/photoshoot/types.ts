import type { AssetWithModel } from '../../types/assets';
import type { PhotographyModel } from 'wasp/entities';

// Existing types
export interface Variation {
  id: number;
  fileUrl: string;
  isOriginal?: boolean;
  prompt?: string | null;
  uploadedAt: Date;
  variationSettings?: any;
}

export interface VariationData {
  original: AssetWithModel;
  variations: AssetWithModel[];
}

export interface GenerateVariationResponse {
  success: boolean;
  url?: string;
  error?: string;
}

export interface VariationQueryResult {
  data: VariationData | null;
  isLoading: boolean;
  error: Error | null;
}

// New Smart Shoot types
export type PhotoMode = 'manual' | 'smart';

export interface FaceIDParams {
  id_scale?: number;
  use_true_cfg?: boolean;
  start_id?: number;
}

export interface GenerationSettings {
  face_reference?: string;
  face_id_params?: FaceIDParams;
}

export type ShotType =
  | 'Lifestyle'
  | 'Group Shot'
  | 'Flat lay'
  | 'Flat White Photography'
  | 'Process shot'
  | 'Ecommerce product photo'
  | 'Packaging shot'
  | 'Lifestyle product photography'
  | 'Group product photos'
  | 'Scale product photography'
  | 'Scale Shot'
  | 'Detail shot'
  | 'Studio shot'
  | 'Furniture photography'
  | 'Aspirational shot'
  | 'Real environment photography';

export interface SceneSuggestion {
  id: string;
  title: string;
  description: string;
  imageUrl?: string;
  selected?: boolean;
}

export interface Shot {
  id: string;
  description: string;
  prompt: string;
  selected?: boolean;
}

export interface SmartShootState {
  step: number;
  shotType?: ShotType;
  selectedScene?: SceneSuggestion;
  scenes?: SceneSuggestion[];
  shots?: Shot[];
  generatedShots: Shot[];
  enhancedPrompt?: string;
  numImages: number;
  currentShot?: Shot;
}

export interface SmartShootWorkspaceProps {
  product: {
    id: number;
    name: string;
    description?: string;
    images?: string[];
  };
  onGenerate: (prompt: string) => Promise<void>;
}

export interface SmartShootResponse {
  success: boolean;
  scenes: SceneSuggestion[];
}

export interface ShotListResponse {
  success: boolean;
  shots: Shot[];
}

export interface ProductPhotoshootProps {
  product: {
    id: number;
    name: string;
    description?: string;
    images?: string[];
  };
  mode: PhotoMode;
  onModeChange: (mode: PhotoMode) => void;
}

// ImageEditor types
export interface ImageEditorProps {
  image: string;
  existingMask?: string;
  onClose: () => void;
  onSave: (maskImage: string) => void;
  selectedModelId: string;
  selectedModelName: string;
  onSelectModel: (id: string, name: string) => void;
  availableModels: PhotographyModel[] | undefined;
}

export type EditorMode = 'inpainting' | 'expansion' | 'background';

export interface EditorState {
  mode: EditorMode;
  brushSize: number;
  isDrawing: boolean;
  undoStack: ImageData[];
  canvasWidth: number;
  canvasHeight: number;
  imageWidth: number;
  imageHeight: number;
  imageLeft: number;
  imageTop: number;
  selectedModelId: string;
  selectedModelName: string;
  scale: number;
}

export type ResizeDirection = 'nw' | 'ne' | 'sw' | 'se' | 'w' | 'e' | 'n' | 's';

export interface ToolbarProps {
  state: EditorState;
  setState: React.Dispatch<React.SetStateAction<EditorState>>;
  onUndo: () => void;
  onClearMask?: () => void;
}

export interface HeaderProps {
  onSave: () => void;
  onClose: () => void;
  selectedModelId: string;
  selectedModelName: string;
  onSelectModel: (id: string, name: string) => void;
  availableModels: PhotographyModel[] | undefined;
}
