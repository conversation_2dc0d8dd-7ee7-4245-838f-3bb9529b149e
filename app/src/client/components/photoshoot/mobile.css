/* Mobile-specific styles for photoshoot components */
@media (max-width: 640px) {
  /* Container for the entire left side */
  .fixed.bottom-0.left-0.right-0 {
    z-index: 40;
  }

  .mobile-input-area {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    border-top: 1px solid rgb(229, 231, 235);
    z-index: 40;
    pointer-events: auto;
  }

  .mobile-input-area .input-container {
    padding: 0.75rem;
    background-color: white;
    pointer-events: auto;
    position: relative;
  }

  :root.dark .mobile-input-area .input-container {
    background-color: rgb(31, 41, 55);
  }

  .mobile-input-area textarea {
    width: 100%;
    min-height: 24px;
    max-height: 300px;
    resize: none;
    transition: all 0.2s ease;
    overflow-y: auto;
    overscroll-behavior-y: contain;
    line-height: 1.5;
    margin-bottom: 4rem; /* Space for button container */
    background-color: transparent;
    pointer-events: auto !important;
    -webkit-tap-highlight-color: transparent;
    -webkit-appearance: none;
    touch-action: pan-y;
    -webkit-overflow-scrolling: touch;
    user-select: text;
    padding: 0.75rem;
    border: 1px solid rgb(229, 231, 235);
    border-radius: 0.75rem;
    position: relative;
  }

  .mobile-input-area textarea::-webkit-scrollbar {
    width: 4px;
  }

  .mobile-input-area textarea::-webkit-scrollbar-track {
    background: transparent;
  }

  .mobile-input-area textarea::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
  }

  .mobile-input-area .button-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3.5rem; /* Fixed height for button container */
    padding: 0.75rem;
    background-color: white;
    border-top: 1px solid rgb(229, 231, 235);
    pointer-events: auto;
    display: flex;
    align-items: center;
  }

  /* Target the flex container */
  .mobile-input-area .button-container .flex.items-center.space-x-2 {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    gap: 0.75rem;
  }

  /* Target the first button (photo icon) */
  .mobile-input-area .button-container .flex.items-center.space-x-2 > *:first-child {
    margin-right: auto;
  }

  :root.dark .mobile-input-area {
    background-color: rgb(31, 41, 55);
    border-color: rgb(55, 65, 81);
  }

  :root.dark .mobile-input-area .button-container {
    background-color: rgb(31, 41, 55);
    border-color: rgb(55, 65, 81);
  }

  .mobile-dropdown {
    position: absolute;
    top: calc(100% + 0.5rem);
    right: 0;
    width: 280px;
    background-color: white;
    border: 1px solid rgb(229, 231, 235);
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 41;
  }

  .mobile-dropdown [class*="absolute"] {
    position: static !important;
    transform: none !important;
    margin: 0 !important;
    width: 100% !important;
    border: none !important;
    box-shadow: none !important;
    animation: none !important;
  }

  .gallery-content {
    padding-bottom: 5rem;
    position: relative;
    z-index: 1;
  }

  /* Target the gallery boxes directly */
  div[class*="bg-app-text-light/10"],
  div[class*="bg-app-text-dark/10"] {
    z-index: 1;
    position: relative;
  }

  /* Override framer-motion stacking context */
  [data-framer-motion] {
    z-index: auto !important;
  }

  /* Ensure header stays above everything */
  .fixed.top-0.z-40 {
    z-index: 50 !important;
  }

  /* Ensure textarea is interactive */
  .mobile-input-area textarea:focus {
    outline: none;
    pointer-events: auto !important;
  }
}

/* Dark mode support */
:root.dark .mobile-input-area {
  background-color: rgb(0, 0, 0);
  border-color: rgb(26, 26, 26);
}

:root.dark .mobile-dropdown {
  background-color: rgb(0, 0, 0);
  border-color: rgb(26, 26, 26);
}

:root.dark .mobile-input-area textarea {
  border-color: rgb(55, 65, 81);
  color: white;
}

:root.dark .mobile-input-area textarea::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}
