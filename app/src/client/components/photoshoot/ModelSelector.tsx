import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, Plus, Camera, Loader2 } from 'lucide-react';
import type { PhotographyModel } from 'wasp/entities';

interface ModelSelectorProps {
  label?: string;
  selectedModelId: string;
  selectedModelName: string;
  availableModels: PhotographyModel[] | undefined;
  onSelect: (id: string, name: string) => void;
  disabled?: boolean;
  emptyMessage?: string;
}

const ModelSelector: React.FC<ModelSelectorProps> = ({
  label,
  selectedModelId,
  selectedModelName,
  availableModels,
  onSelect,
  disabled = false,
  emptyMessage = 'No models available',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Log when component mounts or when selectedModelId/availableModels change
  useEffect(() => {
    console.log('[ModelSelector] Component mounted/updated with:', {
      selectedModelId,
      selectedModelName,
      availableModelsCount: availableModels?.length || 0,
      availableModelIds: availableModels?.map((m) => m.id) || [],
    });
  }, [selectedModelId, selectedModelName, availableModels]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Wrap onSelect to add logging
  const handleModelSelect = (id: string, name: string) => {
    console.log('[ModelSelector] Model selected:', { id, name });
    onSelect(id, name);
  };

  const selectedModel = availableModels?.find((model) => model.id === selectedModelId);

  // Log if selected model is found or not
  useEffect(() => {
    if (selectedModelId) {
      console.log('[ModelSelector] Selected model details:', {
        found: !!selectedModel,
        selectedModelId,
        modelDetails: selectedModel
          ? {
              id: selectedModel.id,
              name: selectedModel.name,
              isTraining: selectedModel.isTraining,
              type: selectedModel.type,
            }
          : 'Not found in available models',
      });
    }
  }, [selectedModel, selectedModelId]);

  return (
    <div className='relative' ref={dropdownRef}>
      {label && (
        <label className='block text-sm font-medium text-app-text-light dark:text-app-text-dark mb-2'>{label}</label>
      )}
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled}
        className={`flex items-center text-sm transition-colors text-app-text-light dark:text-app-text-dark ${
          disabled ? 'opacity-50 cursor-not-allowed' : ''
        }`}
      >
        {selectedModel?.isTraining ? (
          <Loader2 className='w-4 h-4 mr-1.5 animate-spin text-app-text-light dark:text-app-text-dark' />
        ) : (
          <Camera className='w-4 h-4 mr-1.5 text-app-text-light dark:text-app-text-dark' />
        )}
        <span className='max-w-[120px] truncate'>
          {selectedModel?.isTraining ? `${selectedModel.name} (Training...)` : selectedModelName}
        </span>
        <ChevronDown
          className={`w-3.5 h-3.5 ml-1 transition-transform duration-200 ${isOpen ? 'transform rotate-180' : ''}`}
        />
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className='absolute left-0 z-50 mt-2 w-[14rem] translate-x-[-6rem] max-w-[calc(100vw-2rem)] origin-top-right rounded-lg shadow-lg border border-app-border-light dark:border-app-border-dark'
          >
            <div className='p-2 overflow-hidden'>
              {/* None Option */}
              <button
                onClick={() => {
                  handleModelSelect('', 'None');
                  setIsOpen(false);
                }}
                className={`flex items-center space-x-2 w-full p-3 rounded-md transition-colors ${
                  !selectedModelId ? 'bg-gray-200 dark:bg-gray-800' : 'hover:bg-gray-100 dark:hover:bg-gray-900'
                }`}
              >
                <div className='flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-app-text-light/10 dark:bg-app-text-dark/10'>
                  <Camera className='w-4 h-4 text-app-text-light dark:text-app-text-dark' />
                </div>
                <div className='flex-1 text-left'>
                  <p className='text-sm font-medium text-app-text-light dark:text-app-text-dark'>None</p>
                  <p className='text-xs text-app-text-light dark:text-app-text-dark'>Generate without a model</p>
                </div>
                {!selectedModelId && <div className='w-2 h-2 rounded-full bg-app-text-light dark:bg-app-text-dark' />}
              </button>

              {availableModels && availableModels.length > 0 && (
                <div className='my-2 border-t border-app-border-light dark:border-app-border-dark' />
              )}

              {/* Model List */}
              <div className='max-h-60 overflow-y-auto'>
                {availableModels?.map((model) => {
                  const isTraining = model.isTraining;
                  const isComplete = !isTraining && model.trainingEnd;

                  return (
                    <button
                      key={model.id}
                      onClick={() => {
                        handleModelSelect(model.id, model.name);
                        setIsOpen(false);
                      }}
                      className={`flex items-center space-x-2 w-full p-3 rounded-md transition-colors ${
                        selectedModelId === model.id
                          ? 'bg-gray-200 dark:bg-gray-800'
                          : 'hover:bg-gray-100 dark:hover:bg-gray-900'
                      }`}
                    >
                      <div className='flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-app-text-light/10 dark:bg-app-text-dark/10'>
                        {isTraining ? (
                          <Loader2 className='w-4 h-4 animate-spin text-app-text-light dark:text-app-text-dark' />
                        ) : (
                          <Camera className='w-4 h-4 text-app-text-light dark:text-app-text-dark' />
                        )}
                      </div>
                      <div className='flex-1 text-left'>
                        <p className='text-sm font-medium truncate max-w-[120px] text-app-text-light dark:text-app-text-dark'>
                          {model.name}
                        </p>
                        <p className='text-xs text-app-text-light dark:text-app-text-dark'>
                          {isTraining
                            ? 'Training in progress...'
                            : isComplete
                              ? `${model.usage || 0} generations`
                              : 'Training failed'}
                        </p>
                      </div>
                      {selectedModelId === model.id && (
                        <div className='w-2 h-2 rounded-full bg-app-text-light dark:bg-app-text-dark' />
                      )}
                    </button>
                  );
                })}

                {(!availableModels || availableModels.length === 0) && (
                  <div className='p-3 text-center text-sm text-app-text-light dark:text-app-text-dark'>
                    {emptyMessage}
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ModelSelector;
