import React from 'react';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface BackButtonProps {
  className?: string;
}

export const BackButton: React.FC<BackButtonProps> = ({ className = '' }) => {
  const navigate = useNavigate();

  return (
    <button
      onClick={() => navigate(-1)}
      className={`flex items-center px-4 py-2 bg-[#676D50] hover:bg-[#A6884C] dark:bg-gray-900 dark:hover:bg-gray-800 text-white dark:text-gray-100 rounded-xl transition-all duration-200 shadow-sm dark:shadow-md hover:shadow-md dark:hover:shadow-lg ${className}`}
    >
      <ArrowLeft className='w-5 h-5 mr-2' />
      <span>Back</span>
    </button>
  );
};
