import { Avatar, AvatarFallback, AvatarImage } from './avatar';

interface User {
  id: string;
  name: string;
  avatar?: string;
}

interface AvatarStackProps {
  users: User[];
  limit?: number;
  size?: 'sm' | 'md' | 'lg';
}

export default function AvatarStack({ users, limit = 3, size = 'md' }: AvatarStackProps) {
  const visibleUsers = users.slice(0, limit);
  const remainingCount = users.length - limit;

  const sizeClasses = {
    sm: 'h-6 w-6 text-xs',
    md: 'h-8 w-8 text-sm',
    lg: 'h-10 w-10 text-base',
  };

  const borderSize = {
    sm: 'border-[1.5px]',
    md: 'border-2',
    lg: 'border-2',
  };

  return (
    <div className='flex items-center'>
      <div className='flex -space-x-2'>
        {visibleUsers.map((user, idx) => (
          <Avatar
            key={`${user.id}-${idx}`}
            className={`${sizeClasses[size]} ${borderSize[size]} border-background ring-0`}
          >
            <AvatarImage src={user.avatar || `/placeholder.svg?height=40&width=40`} alt={user.name} />
            <AvatarFallback>
              {user.name
                .split(' ')
                .map((n) => n[0])
                .join('')
                .toUpperCase()}
            </AvatarFallback>
          </Avatar>
        ))}
      </div>

      {remainingCount > 0 && (
        <Avatar
          className={`${sizeClasses[size]} ${borderSize[size]} border-background bg-muted text-muted-foreground ring-0`}
        >
          <AvatarFallback>+{remainingCount}</AvatarFallback>
        </Avatar>
      )}
    </div>
  );
}
