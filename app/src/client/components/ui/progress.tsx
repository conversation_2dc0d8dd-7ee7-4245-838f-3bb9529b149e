import React from 'react';
import * as ProgressPrimitive from '@radix-ui/react-progress';

import { cn } from '../../../shared/utils';

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> & {
    showPercentage?: boolean;
  }
>(({ className, value, showPercentage = false, ...props }, ref) => (
  <div className='relative'>
    <ProgressPrimitive.Root
      ref={ref}
      className={cn('relative h-2 w-full overflow-hidden rounded-full bg-olive-100/50', className)}
      {...props}
    >
      <ProgressPrimitive.Indicator
        className='h-full bg-gradient-to-r from-olive-400 to-olive-500 transition-all duration-500 ease-in-out'
        style={{
          width: `${value || 0}%`,
          boxShadow: '0 0 10px rgba(98, 112, 90, 0.3)',
        }}
      />
    </ProgressPrimitive.Root>
    {showPercentage && <div className='absolute -top-6 right-0 text-sm text-gray-600'>{Math.round(value || 0)}%</div>}
  </div>
));
Progress.displayName = ProgressPrimitive.Root.displayName;

export { Progress };
