import React, { useEffect, useRef, useState } from 'react';
import { useSidebarState } from '../../hooks/useSidebarState';
import { Link } from 'wasp/client/router';
import { Link as RouterLink } from 'react-router-dom';
import { useLocation } from 'react-router-dom';
import {
  X,
  Palette,
  Users,
  Package,
  Folder,
  Camera,
  UserCircle,
  MessageSquare,
  Library,
  LayoutDashboard,
} from 'lucide-react';
import { routes } from 'wasp/client/router';
import useFeatureFlags from '../../../client/hooks/use-feature-flags';
import ReferralWidget from '../../../referral/components/referral-widget';
import { CgProfile } from 'react-icons/cg';
import { UserMenuItems } from '../../../user/UserMenuItems';
import { cn } from '../../lib/utils';
import { type User } from 'wasp/entities';
import { useAuth } from 'wasp/client/auth';
import { <PERSON>, <PERSON> } from 'lucide-react';

// SidebarUserMenu component that opens to the right
const SidebarUserMenu: React.FC<{ user: Partial<User>; isExpanded: boolean }> = ({ user, isExpanded }) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const trigger = useRef<any>(null);
  const dropdown = useRef<any>(null);

  const toggleDropdown = () => setDropdownOpen((prev) => !prev);

  useEffect(() => {
    const clickHandler = ({ target }: MouseEvent) => {
      if (!dropdown.current) return;
      if (!dropdownOpen || dropdown.current.contains(target) || trigger.current.contains(target)) {
        return;
      }
      setDropdownOpen(false);
    };
    document.addEventListener('click', clickHandler);
    return () => document.removeEventListener('click', clickHandler);
  });

  useEffect(() => {
    const keyHandler = ({ keyCode }: KeyboardEvent) => {
      if (!dropdownOpen || keyCode !== 27) return;
      setDropdownOpen(false);
    };
    document.addEventListener('keydown', keyHandler);
    return () => document.removeEventListener('keydown', keyHandler);
  });

  return (
    <div className='relative'>
      <button
        ref={trigger}
        onClick={toggleDropdown}
        className={`relative flex items-center py-2 w-full text-white bg-transparent border-none hover:bg-[#8a9470] hover:text-[#C8A13C] transition-colors rounded-lg ${isExpanded ? 'px-4' : 'px-2 justify-center'}`}
        aria-label='Open user menu'
      >
        <span className={`w-5 h-5 flex items-center justify-center ${isExpanded ? 'mr-4' : ''}`}>
          <CgProfile size={20} />
        </span>
        {isExpanded && <span>Account</span>}
      </button>

      {/* Dropdown - opens to the right */}
      <div
        ref={dropdown}
        className={cn(
          'absolute left-full bottom-0 ml-2 flex w-60 flex-col rounded-md border border-[#E8E4D4] bg-[#F9F7ED] shadow-lg z-[100] overflow-hidden',
          {
            hidden: !dropdownOpen,
          }
        )}
        style={{ boxShadow: '0 2px 8px rgba(0,0,0,0.08)' }}
      >
        <div className='text-[#566B46] [&_a]:text-[#566B46] [&_a:hover]:text-[#C8A13C] [&_button]:text-[#566B46] [&_button:hover]:text-[#C8A13C] [&_svg]:fill-[#566B46] [&_a:hover_svg]:fill-[#C8A13C] [&_button:hover_svg]:fill-[#C8A13C] [&_.border-stroke]:border-[#E8E4D4] [&_.border-strokedark]:border-[#E8E4D4]'>
          <UserMenuItems user={user} setMobileMenuOpen={toggleDropdown} />
        </div>
      </div>
    </div>
  );
};

export const LeftSidebar: React.FC = () => {
  const { featureEnabled } = useFeatureFlags();
  const { isOpen, isMobile, isHovered, setIsHovered, closeSidebar } = useSidebarState();
  const location = useLocation();
  const sidebarRef = useRef<HTMLDivElement>(null);
  const { data: user } = useAuth();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && isOpen && sidebarRef.current && !sidebarRef.current.contains(event.target as Node)) {
        closeSidebar();
      }
    };

    if (isMobile) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isMobile, isOpen, closeSidebar]);

  const handleMouseEnter = () => {
    if (!isMobile) {
      setIsHovered(true);
    }
  };

  const handleMouseLeave = () => {
    if (!isMobile) {
      setIsHovered(false);
    }
  };

  useEffect(() => {
    if (isMobile && isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobile, isOpen]);

  type MenuItem = {
    path: string;
    icon: React.FC<any>;
    label: string;
    show: boolean;
  };

  const menuItems: MenuItem[] = [
    { path: routes.DashboardRoute.to, icon: LayoutDashboard, label: 'Dashboard', show: true },
    { path: routes.BrandKitsRoute.to, icon: Palette, label: 'Brand Kits', show: true },
    { path: routes.AudienceRoute.to, icon: Users, label: 'Audience', show: true },
    { path: routes.ProductPageRoute.to, icon: Package, label: 'Products', show: true },
    { path: '/canvas/projects', icon: Camera, label: 'Projects', show: true },
    { path: routes.AssetLibraryRoute.to, icon: Folder, label: 'Assets Library', show: true },
    { path: routes.ReferenceLibraryRoute.to, icon: Library, label: 'Reference Library', show: true },
  ];

  const handleLinkClick = () => {
    if (isMobile) {
      closeSidebar();
    }
  };

  return (
    <>
      {/* Overlay */}
      {isMobile && isOpen && <div className='fixed inset-0 bg-black bg-opacity-50 z-[45]' onClick={closeSidebar} />}

      {/* Sidebar */}
      <div
        ref={sidebarRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className={`fixed top-0 left-0 h-screen bg-[#9EA581] dark:bg-black text-white dark:text-white transition-all duration-300 ease-in-out ${
          isMobile
            ? `z-[100] ${isOpen ? 'translate-x-0 w-full sm:w-80' : '-translate-x-full w-full sm:w-80'}`
            : `z-30 ${isHovered ? 'w-64' : 'w-16'}`
        } border-r border-gray-200 dark:border-gray-600`}
      >
        <div className='flex flex-col h-full'>
          <div
            className={`relative flex items-center justify-between mt-4 mb-8 ${
              (isMobile && isOpen) || (!isMobile && isHovered) ? 'px-4' : 'px-2'
            }`}
          >
            {((isMobile && isOpen) || (!isMobile && isHovered)) && (
              <img src='https://oliviatest.xyz/logo.png' alt='Logo' className='h-8' />
            )}
            {/* Mobile Close Button */}
            {isMobile && isOpen && (
              <button
                onClick={closeSidebar}
                className='absolute right-4 p-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-800'
              >
                <X size={24} />
              </button>
            )}
          </div>
          <ul className='space-y-4 flex-grow'>
            {menuItems.map((item) => {
              if (!item.show) {
                return null;
              }

              // Use regular Link for route paths, RouterLink for custom paths
              const isCustomPath = !Object.values(routes).some((route) => route.to === item.path);

              return (
                <li key={item.path}>
                  {isCustomPath ? (
                    <RouterLink
                      to={item.path}
                      onClick={handleLinkClick}
                      className={`relative flex items-center px-4 py-2 text-white dark:text-white hover:bg-[#8a9470] hover:text-[#C8A13C] dark:hover:bg-gray-900 rounded-lg transition-colors ${
                        location.pathname === item.path ? 'bg-[#8a9470] text-[#C8A13C] dark:bg-gray-900' : ''
                      }`}
                    >
                      <span className='w-5 h-5 mr-4 flex items-center justify-center'>
                        <item.icon size={20} />
                      </span>
                      {((isMobile && isOpen) || (!isMobile && isHovered)) && <span>{item.label}</span>}
                      {/* Active indicator dot */}
                      {location.pathname === item.path && (
                        <div className='absolute right-2 w-1 h-1 bg-[#C8A13C] rounded-full'></div>
                      )}
                    </RouterLink>
                  ) : (
                    <Link
                      to={item.path as any}
                      onClick={handleLinkClick}
                      className={`relative flex items-center px-4 py-2 text-white dark:text-white hover:bg-[#8a9470] hover:text-[#C8A13C] dark:hover:bg-gray-900 rounded-lg transition-colors ${
                        location.pathname === item.path ? 'bg-[#8a9470] text-[#C8A13C] dark:bg-gray-900' : ''
                      }`}
                    >
                      <span className='w-5 h-5 mr-4 flex items-center justify-center'>
                        <item.icon size={20} />
                      </span>
                      {((isMobile && isOpen) || (!isMobile && isHovered)) && <span>{item.label}</span>}
                      {/* Active indicator dot */}
                      {location.pathname === item.path && (
                        <div className='absolute right-2 w-1 h-1 bg-[#C8A13C] rounded-full'></div>
                      )}
                    </Link>
                  )}
                </li>
              );
            })}
          </ul>
          {/* Bottom controls */}
          <div className='mt-auto mb-4 px-2 space-y-2'>
            {/* Dark Mode Toggle Switch */}
            <div
              className={`flex items-center py-2 ${
                (isMobile && isOpen) || (!isMobile && isHovered) ? 'px-4 justify-between' : 'px-2 justify-center'
              }`}
            >
              {((isMobile && isOpen) || (!isMobile && isHovered)) && (
                <span className='text-white text-sm'>Dark Mode</span>
              )}
              <button
                onClick={() => document.documentElement.classList.toggle('dark')}
                className='relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 dark:bg-gray-800 transition-colors focus:outline-none focus:ring-2 focus:ring-[#C8A13C] focus:ring-offset-2'
                aria-label='Toggle dark mode'
              >
                <span className='inline-block h-4 w-4 transform rounded-full bg-white transition-transform dark:translate-x-6 translate-x-1 shadow-lg'>
                  <Sun className='w-3 h-3 absolute top-0.5 left-0.5 text-yellow-500 dark:opacity-0 opacity-100 transition-opacity' />
                  <Moon className='w-3 h-3 absolute top-0.5 left-0.5 text-gray-700 dark:opacity-100 opacity-0 transition-opacity' />
                </span>
              </button>
            </div>

            {/* User Menu */}
            {user && <SidebarUserMenu user={user} isExpanded={(isMobile && isOpen) || (!isMobile && isHovered)} />}
          </div>
          {featureEnabled(['referrals']) && ((isMobile && isOpen) || (!isMobile && isHovered)) && <ReferralWidget />}
        </div>
      </div>
    </>
  );
};
