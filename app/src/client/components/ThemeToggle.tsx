import React from 'react';
import { useTheme } from '../context/ThemeContext';
import { FiSun, FiMoon } from 'react-icons/fi';

const ThemeToggle: React.FC = () => {
  const { isDarkMode, toggleDarkMode } = useTheme();

  return (
    <button
      onClick={toggleDarkMode}
      className="flex items-center justify-center w-10 h-10 rounded-lg transition-all duration-200 hover:bg-gray-200/80 dark:hover:bg-gray-900/90 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-700"
      aria-label="Toggle dark mode"
    >
      <div className="relative w-9 h-5 transition-colors duration-300 rounded-full bg-gray-300 dark:bg-gray-600">
        <div
          className={`absolute top-0.5 w-4 h-4 transition-transform duration-300 transform ${
            isDarkMode ? 'translate-x-4.5' : 'translate-x-0.5'
          } bg-white rounded-full shadow-md flex items-center justify-center`}
          style={{ 
            transform: isDarkMode ? 'translateX(calc(100% - 1px))' : 'translateX(1px)',
            boxShadow: '0 1px 2px rgba(0,0,0,0.2)'
          }}
        >
          {isDarkMode ? (
            <FiMoon className="w-2.5 h-2.5 text-indigo-600" />
          ) : (
            <FiSun className="w-2.5 h-2.5 text-amber-500" />
          )}
        </div>
      </div>
    </button>
  );
};

export default ThemeToggle;
