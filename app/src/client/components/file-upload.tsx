import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, X, ImageIcon, PlusCircle, Folder, CircleX } from 'lucide-react';
import toast from 'react-hot-toast';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  Progress,
  Button,
  Input,
} from './ui/index';
import { useQueryState } from 'nuqs';
import useUpload from '../hooks/useUpload';
import { useParams } from 'react-router-dom';
import { createBoard, getBoards, useQuery } from 'wasp/client/operations';
import { useOrganizationState } from '../../organization/store';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from './ui/form';
import { BoardSelector } from '../../features/assets/presentation/components/board-selector/BoardSelector';

const uploadFormSchema = z.object({
  boardId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  newBoardName: z.string().optional(),
});

type UploadFormValues = z.infer<typeof uploadFormSchema>;

export function FileUpload({ limit = 10 }: { limit?: number }) {
  const { assetId } = useParams<{ assetId: string }>();
  const { upload, duplicateFile, handleDuplicateUpload, clearDuplicateFile } = useUpload();
  const [showUpload, setShowUpload] = useQueryState('upload-modal', {
    defaultValue: '',
  });
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{
    [key: string]: number;
  }>({});
  const [draggedFiles, setDraggedFiles] = useState<File[]>([]);
  const [previews, setPreviews] = useState<{ [key: string]: string }>({});
  const { selectedOrganizationId } = useOrganizationState();
  const [isCreatingBoard, setIsCreatingBoard] = useState(false);
  const [newBoardParentId, setNewBoardParentId] = useState<string | undefined>(undefined);

  const { data: boards } = useQuery(getBoards, {
    organizationId: selectedOrganizationId,
  });

  // Initialize React Hook Form
  const form = useForm<UploadFormValues>({
    resolver: zodResolver(uploadFormSchema),
    defaultValues: {
      boardId: '',
      tags: [],
      newBoardName: '',
    },
  });

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const imageFiles = acceptedFiles.filter((file) => file.type.startsWith('image/'));
    setDraggedFiles((prevFiles) => [...prevFiles, ...imageFiles]);

    imageFiles.forEach((file) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviews((prev) => ({
          ...prev,
          [file.name]: reader.result as string,
        }));
      };
      reader.readAsDataURL(file);
    });
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp'],
    },
    multiple: true,
  });

  const handleCreateBoard = async () => {
    const newBoardName = form.watch('newBoardName');

    if (!newBoardName) {
      toast.error('Board name is required');
      return;
    }

    try {
      const result = await createBoard({
        name: newBoardName,
        organizationId: selectedOrganizationId,
        parentBoardId: newBoardParentId,
      });

      if (result) {
        // Set the newly created board as selected
        form.setValue('boardId', result.id);
        toast.success(`Created board: ${newBoardName}`);
        setIsCreatingBoard(false);
        form.setValue('newBoardName', '');
      }
    } catch (error) {
      toast.error('Failed to create board');
      console.error('Board creation error:', error);
    }
  };

  const handleUpload = async (formValues: UploadFormValues) => {
    setIsUploading(true);

    try {
      for (const file of draggedFiles) {
        setUploadProgress((prev) => ({ ...prev, [file.name]: 0 }));

        const result = await upload(
          file,
          'uploaded',
          false,
          assetId ? Number(assetId) : undefined,
          formValues.boardId ? formValues.boardId : undefined
        );

        if (result?.type === 'DUPLICATE') {
          setIsUploading(false);
          return;
        }

        setUploadProgress((prev) => ({ ...prev, [file.name]: 1 }));
      }

      handleClose();
      toast.success(`Successfully uploaded ${draggedFiles.length} file(s)`);
    } catch (error) {
      toast.error('Failed to upload files');
      console.error('Upload error:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDuplicateDecision = async (asNewVersion: boolean) => {
    setIsUploading(true);
    try {
      const result = await handleDuplicateUpload(asNewVersion);
      if (result) {
        handleClose();
        toast.success('File uploaded successfully');
      }
    } catch (error) {
      toast.error('Failed to upload file');
      console.error('Upload error:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const removeFile = (fileToRemove: File) => {
    setDraggedFiles((files) => files.filter((file) => file !== fileToRemove));
    setPreviews((prev) => {
      const newPreviews = { ...prev };
      delete newPreviews[fileToRemove.name];
      return newPreviews;
    });
  };

  const handleClose = () => {
    setDraggedFiles([]);
    setPreviews({});
    setUploadProgress({});
    setShowUpload('');
    setIsCreatingBoard(false);
    form.reset();
  };

  return (
    <>
      <Dialog open={!!showUpload} onOpenChange={handleClose}>
        <DialogContent className='bg-[#F0EFE9] border-2 border-[#676D50] dark:bg-black'>
          <DialogHeader>
            <DialogTitle>Upload Images</DialogTitle>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleUpload)} className='space-y-6'>
              {draggedFiles.length + 1 <= limit && (
                <div
                  {...getRootProps()}
                  className={`border-2 border-dashed border-primary rounded-lg p-8 text-center transition-colors ${
                    isDragActive ? 'bg-primary/10' : 'bg-background'
                  }`}
                >
                  <Upload className='w-12 h-12 mx-auto mb-4 text-primary' />
                  <h3 className='text-lg font-medium'>Drop your images here</h3>
                  <p className='text-sm text-muted-foreground mt-2'>or click to select files</p>
                  <input {...getInputProps()} />
                </div>
              )}

              {draggedFiles.length > 0 && (
                <div className='space-y-4 mt-4'>
                  {draggedFiles.map((file) => (
                    <div key={file.name} className='flex items-center gap-4'>
                      <div className='w-12 h-12 rounded-lg border bg-muted flex items-center justify-center overflow-hidden'>
                        {previews[file.name] ? (
                          <img src={previews[file.name]} alt={file.name} className='w-full h-full object-cover' />
                        ) : (
                          <ImageIcon className='w-6 h-6 text-muted-foreground' />
                        )}
                      </div>
                      <div className='flex-1'>
                        <p className='text-sm font-medium truncate'>{file.name}</p>
                        <p className='text-xs text-muted-foreground'>{(file.size / 1024 / 1024).toFixed(2)} MB</p>
                        <Progress value={uploadProgress[file.name] || 0} className='h-1 mt-2' />
                      </div>
                      <Button
                        variant='ghost'
                        size='icon'
                        className='opacity-50 hover:opacity-100'
                        onClick={() => removeFile(file)}
                        type='button'
                      >
                        <X className='w-4 h-4' />
                      </Button>
                    </div>
                  ))}
                </div>
              )}

              <FormField
                control={form.control}
                name='boardId'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='text-black dark:text-white'>Select Board</FormLabel>
                    <div className='flex flex-col gap-2 border-white border p-3 rounded'>
                      {/* Create new board option */}
                      {isCreatingBoard ? (
                        <div className='flex gap-2 items-center p-2'>
                          <FormField
                            control={form.control}
                            name='newBoardName'
                            render={({ field }) => (
                              <FormItem className='flex-1'>
                                <FormControl>
                                  <div className='flex items-center gap-2'>
                                    <Folder className='h-4 w-4 shrink-0' />
                                    <Input
                                      {...field}
                                      placeholder='Enter board name'
                                      className='flex-1'
                                      onKeyDown={(e) => {
                                        if (e.key === 'Enter') {
                                          e.preventDefault();
                                          handleCreateBoard();
                                        }
                                      }}
                                    />
                                    <Button
                                      type='button'
                                      variant='ghost'
                                      size='icon'
                                      onClick={() => setIsCreatingBoard(false)}
                                    >
                                      <CircleX className='h-4 w-4' />
                                    </Button>
                                  </div>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <Button type='button' onClick={handleCreateBoard} size='sm'>
                            Create
                          </Button>
                        </div>
                      ) : (
                        <button
                          type='button'
                          className='flex justify-between items-center w-full p-2 rounded border border-[#676D50] hover:text-white hover:bg-[#676D50] focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-700 hover:cursor-pointer hover:dark:bg-[#676D50]'
                          onClick={() => {
                            setIsCreatingBoard(true);
                            setNewBoardParentId(undefined);
                          }}
                        >
                          <div className='flex gap-2 items-center'>
                            <Folder className='h-4 w-4 shrink-0 mr-2' />
                            Create new board
                          </div>
                          <PlusCircle className='h-4 w-4' />
                        </button>
                      )}

                      {/* Existing boards */}
                      {!isCreatingBoard &&
                        (boards && boards.length > 0 ? (
                          <BoardSelector
                            boards={boards}
                            selectedBoardId={field.value}
                            onBoardSelect={(boardId) => {
                              field.onChange(boardId);
                            }}
                            onCreateBoard={(name, parentId) => {
                              // This will be handled by the create board form above
                              setIsCreatingBoard(true);
                              setNewBoardParentId(parentId);
                            }}
                            allowCreation={false}
                            className='max-h-48 overflow-y-auto'
                          />
                        ) : (
                          <div className='text-sm text-gray-400 p-2'>No boards found. Create a new one above.</div>
                        ))}
                    </div>
                  </FormItem>
                )}
              />

              <div className='flex justify-end gap-2 mt-4'>
                <Button variant='outline' onClick={handleClose} type='button'>
                  Cancel
                </Button>
                <Button type='submit' disabled={isUploading || draggedFiles.length === 0}>
                  {isUploading ? 'Uploading...' : 'Upload'}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {duplicateFile && (
        <Dialog open={!!duplicateFile} onOpenChange={() => clearDuplicateFile()}>
          <DialogContent className='bg-background dark:bg-black'>
            <DialogHeader>
              <DialogTitle>File Already Exists</DialogTitle>
              <DialogDescription className='text-gray-400'>
                A file with the name &quot;{duplicateFile.file.name}&quot; already exists. How would you like to
                proceed?
              </DialogDescription>
            </DialogHeader>

            <div className='flex justify-end gap-4 mt-4'>
              <Button variant='outline' onClick={() => handleDuplicateDecision(false)} disabled={isUploading}>
                Upload as New Asset
              </Button>
              <Button onClick={() => handleDuplicateDecision(true)} disabled={isUploading}>
                Add as Version
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
