import React from 'react';
import DropdownUser from '../../user/DropdownUser';
import { type User } from 'wasp/entities';
import OrganizationSwitch from '../../organization/components/organization-switch';
import ThemeToggle from './ThemeToggle';
import { Menu, X } from 'lucide-react';
import { useSidebarState } from '../hooks/useSidebarState';
import NotificationDropdown from '../../notification/components/NotificationDropdown';

type Props = {
  user: Partial<User>;
  showControls?: boolean;
};

const Header = ({ user, showControls = true }: Props) => {
  const { isOpen, isMobile, toggleSidebar } = useSidebarState();

  return (
    <header className='flex w-full bg-[#f2efe8] dark:bg-black text-black dark:text-white border-b border-gray-200/70 dark:border-gray-800/80 backdrop-blur-sm backdrop-saturate-150'>
      <div className='flex flex-grow items-center justify-between sm:justify-end sm:gap-6 px-5 sm:px-8 py-3.5 shadow-sm dark:shadow-gray-800/30'>
        {showControls && (
          <>
            <div className='flex items-center gap-4 2xsm:gap-7'>
              {isMobile && (
                <button
                  onClick={toggleSidebar}
                  className="p-2 rounded-lg hover:bg-gray-200/80 dark:hover:bg-gray-900/90 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-700"
                  aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
                >
                  {isOpen ? <X size={20} strokeWidth={2} /> : <Menu size={20} strokeWidth={2} />}
                </button>
              )}
              <OrganizationSwitch />
            </div>
            <div className='flex items-center gap-4 2xsm:gap-6'>
              <NotificationDropdown />
              <div className="h-7 w-px bg-gray-300/70 dark:bg-gray-600/70 mx-1 hidden sm:block" />
              <ThemeToggle />
              {!!user && <DropdownUser user={user} />}
            </div>
          </>
        )}
      </div>
    </header>
  );
};

export default Header;
