import React from 'react';
import { Control, Controller } from 'react-hook-form';

interface TextInputProps {
  name: string;
  control: Control<any>;
  label: string;
  placeholder?: string;
  multiline?: boolean;
  suggestions?: string[];
}

export const TextInput: React.FC<TextInputProps> = ({ name, control, label, placeholder, multiline = false }) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { value = '', onChange, ...fieldProps } }) => {
        return (
          <div>
            <label className='block text-sm font-medium text-gray-900 dark:text-white mb-2'>{label}</label>
            <div>
              {multiline ? (
                <textarea
                  {...fieldProps}
                  value={value}
                  onChange={onChange}
                  placeholder={placeholder}
                  rows={4}
                  className='w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-olive-500 focus:border-transparent resize-vertical dark:bg-gray-700 dark:text-white'
                />
              ) : (
                <input
                  type='text'
                  {...fieldProps}
                  value={value}
                  onChange={onChange}
                  placeholder={placeholder}
                  className='w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-olive-500 focus:border-transparent dark:bg-gray-700 dark:text-white'
                />
              )}
            </div>
          </div>
        );
      }}
    />
  );
};
