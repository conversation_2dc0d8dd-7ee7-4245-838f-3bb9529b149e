import React from 'react';
import { Controller } from 'react-hook-form';
import { Type } from 'lucide-react';
import { FontSelector } from './FontSelector';

interface TypographyEditorProps {
  name: string;
  control: any;
  label: string;
}

interface FontSize {
  h1: string;
  h2: string;
  h3: string;
  h4: string;
  h5: string;
  h6: string;
}

interface HeadingTypography {
  fontFamily: string;
  weights: string[];
  sizes: FontSize;
}

interface BodyTypography {
  fontFamily: string;
  weights: string[];
  baseSize: string;
  lineHeight: string;
}

interface Typography {
  headings: HeadingTypography;
  body: BodyTypography;
}

const defaultTypography: Typography = {
  headings: {
    fontFamily: 'Drephonic',
    weights: ['400', '700'],
    sizes: {
      h1: '3rem', // 48px
      h2: '2rem', // 32px
      h3: '1.75rem',
      h4: '1.5rem',
      h5: '1.25rem',
      h6: '1rem',
    },
  },
  body: {
    fontFamily: 'Inter',
    weights: ['400', '500', '600'],
    baseSize: '1rem', // 16px
    lineHeight: '1.5',
  },
};

export const TypographyEditor: React.FC<TypographyEditorProps> = ({ name, control, label }) => {
  return (
    <Controller
      name={name}
      control={control}
      defaultValue={defaultTypography}
      render={({ field: { value = defaultTypography, onChange } }) => {
        // Ensure value has all required properties
        const typography: Typography = {
          headings: {
            fontFamily: value?.headings?.fontFamily || defaultTypography.headings.fontFamily,
            weights: value?.headings?.weights || defaultTypography.headings.weights,
            sizes: {
              h1: value?.headings?.sizes?.h1 || defaultTypography.headings.sizes.h1,
              h2: value?.headings?.sizes?.h2 || defaultTypography.headings.sizes.h2,
              h3: value?.headings?.sizes?.h3 || defaultTypography.headings.sizes.h3,
              h4: value?.headings?.sizes?.h4 || defaultTypography.headings.sizes.h4,
              h5: value?.headings?.sizes?.h5 || defaultTypography.headings.sizes.h5,
              h6: value?.headings?.sizes?.h6 || defaultTypography.headings.sizes.h6,
            },
          },
          body: {
            fontFamily: value?.body?.fontFamily || defaultTypography.body.fontFamily,
            weights: value?.body?.weights || defaultTypography.body.weights,
            baseSize: value?.body?.baseSize || defaultTypography.body.baseSize,
            lineHeight: value?.body?.lineHeight || defaultTypography.body.lineHeight,
          },
        };

        return (
          <div className='space-y-6'>
            <div className='flex items-center mb-4'>
              <Type className='w-5 h-5 text-olive-600 mr-2' />
              <h3 className='text-lg font-medium text-gray-900 dark:text-white'>{label}</h3>
            </div>

            {/* Headings Typography */}
            <div className='space-y-4'>
              <h4 className='text-sm font-medium text-gray-700 dark:text-gray-200'>Headings</h4>

              <div className='grid grid-cols-2 gap-4'>
                {/* Font Family */}
                <div>
                  <label className='block text-sm text-gray-600 dark:text-gray-300 mb-1'>Font Family</label>
                  <FontSelector
                    value={typography.headings.fontFamily}
                    onChange={(font: string) =>
                      onChange({
                        ...typography,
                        headings: { ...typography.headings, fontFamily: font },
                      })
                    }
                    className='w-full'
                  />
                </div>

                {/* Font Weights */}
                <div>
                  <label className='block text-sm text-gray-600 dark:text-gray-300 mb-1'>
                    Font Weights (comma-separated)
                  </label>
                  <input
                    type='text'
                    value={typography.headings.weights.join(', ')}
                    onChange={(e) => {
                      const weights = e.target.value
                        .split(',')
                        .map((w) => w.trim())
                        .filter(Boolean);
                      onChange({
                        ...typography,
                        headings: { ...typography.headings, weights },
                      });
                    }}
                    className='w-full px-3 py-2 rounded-lg border border-gray-200 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-olive-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white'
                    placeholder='e.g. 600, 700'
                  />
                </div>
              </div>

              {/* Heading Sizes */}
              <div className='grid grid-cols-3 gap-4'>
                {Object.entries(typography.headings.sizes).map(([heading, size]) => (
                  <div key={heading}>
                    <label className='block text-sm text-gray-600 dark:text-gray-300 mb-1'>
                      {heading.toUpperCase()}
                    </label>
                    <input
                      type='text'
                      value={size}
                      onChange={(e) =>
                        onChange({
                          ...typography,
                          headings: {
                            ...typography.headings,
                            sizes: { ...typography.headings.sizes, [heading]: e.target.value },
                          },
                        })
                      }
                      className='w-full px-3 py-2 rounded-lg border border-gray-200 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-olive-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white'
                      placeholder='e.g. 2.5rem'
                    />
                  </div>
                ))}
              </div>

              {/* Preview */}
              <div className='bg-gray-50 dark:bg-gray-700 p-4 rounded-lg space-y-2'>
                {Object.entries(typography.headings.sizes).map(([heading, size]) => (
                  <div
                    key={heading}
                    className='dark:text-white'
                    style={{
                      fontFamily: typography.headings.fontFamily,
                      fontSize: size,
                      fontWeight: typography.headings.weights[0],
                    }}
                  >
                    {heading.toUpperCase()} - The quick brown fox
                  </div>
                ))}
              </div>
            </div>

            {/* Body Typography */}
            <div className='space-y-4 pt-6 border-t border-gray-200 dark:border-gray-600'>
              <h4 className='text-sm font-medium text-gray-700 dark:text-gray-200'>Body Text</h4>

              <div className='grid grid-cols-2 gap-4'>
                {/* Font Family */}
                <div>
                  <label className='block text-sm text-gray-600 dark:text-gray-300 mb-1'>Font Family</label>
                  <FontSelector
                    value={typography.body.fontFamily}
                    onChange={(font: string) =>
                      onChange({
                        ...typography,
                        body: { ...typography.body, fontFamily: font },
                      })
                    }
                    className='w-full'
                  />
                </div>

                {/* Font Weights */}
                <div>
                  <label className='block text-sm text-gray-600 dark:text-gray-300 mb-1'>
                    Font Weights (comma-separated)
                  </label>
                  <input
                    type='text'
                    value={typography.body.weights.join(', ')}
                    onChange={(e) => {
                      const weights = e.target.value
                        .split(',')
                        .map((w) => w.trim())
                        .filter(Boolean);
                      onChange({
                        ...typography,
                        body: { ...typography.body, weights },
                      });
                    }}
                    className='w-full px-3 py-2 rounded-lg border border-gray-200 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-olive-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white'
                    placeholder='e.g. 400, 500'
                  />
                </div>

                {/* Base Size */}
                <div>
                  <label className='block text-sm text-gray-600 dark:text-gray-300 mb-1'>Base Size</label>
                  <input
                    type='text'
                    value={typography.body.baseSize}
                    onChange={(e) =>
                      onChange({
                        ...typography,
                        body: { ...typography.body, baseSize: e.target.value },
                      })
                    }
                    className='w-full px-3 py-2 rounded-lg border border-gray-200 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-olive-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white'
                    placeholder='e.g. 1rem'
                  />
                </div>

                {/* Line Height */}
                <div>
                  <label className='block text-sm text-gray-600 dark:text-gray-300 mb-1'>Line Height</label>
                  <input
                    type='text'
                    value={typography.body.lineHeight}
                    onChange={(e) =>
                      onChange({
                        ...typography,
                        body: { ...typography.body, lineHeight: e.target.value },
                      })
                    }
                    className='w-full px-3 py-2 rounded-lg border border-gray-200 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-olive-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white'
                    placeholder='e.g. 1.5'
                  />
                </div>
              </div>

              {/* Preview */}
              <div
                className='bg-gray-50 dark:bg-gray-700 p-4 rounded-lg dark:text-white'
                style={{
                  fontFamily: typography.body.fontFamily,
                  fontSize: typography.body.baseSize,
                  lineHeight: typography.body.lineHeight,
                  fontWeight: typography.body.weights[0],
                }}
              >
                <p>
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et
                  dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut
                  aliquip ex ea commodo consequat.
                </p>
                <p className='mt-2'>
                  <span style={{ fontWeight: typography.body.weights[1] }}>
                    This text uses the medium weight to demonstrate emphasis within body text.
                  </span>
                </p>
              </div>
            </div>
          </div>
        );
      }}
    />
  );
};
