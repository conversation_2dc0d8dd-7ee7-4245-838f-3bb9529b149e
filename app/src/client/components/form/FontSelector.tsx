import React, { useEffect, useState } from 'react';
import AsyncSelect from 'react-select/async';
import { getFonts } from 'wasp/client/operations';

interface FontOption {
  value: string;
  label: string;
  category?: string;
  variants?: string[];
}

interface FontSelectorProps {
  value: string;
  onChange: (font: string) => void;
  className?: string;
}

// Cache for loaded fonts to avoid duplicate loading
const loadedFonts = new Set<string>();

// Function to load font using Google Fonts API
const loadFont = async (fontFamily: string) => {
  if (loadedFonts.has(fontFamily)) return;

  try {
    const link = document.createElement('link');
    link.href = `https://fonts.googleapis.com/css2?family=${fontFamily.replace(/ /g, '+')}:wght@100;200;300;400;500;600;700;800;900&display=swap`;
    link.rel = 'stylesheet';
    document.head.appendChild(link);
    loadedFonts.add(fontFamily);
  } catch (error) {
    console.warn(`Failed to load font ${fontFamily} from Google Fonts:`, error);
    // Still add to loadedFonts to prevent repeated attempts
    loadedFonts.add(fontFamily);
  }
};

// Default fonts to show when API is not available
const defaultFonts: FontOption[] = [
  { value: 'Arial', label: 'Arial' },
  { value: 'Helvetica', label: 'Helvetica' },
  { value: 'Times New Roman', label: 'Times New Roman' },
  { value: 'Georgia', label: 'Georgia' },
  { value: 'Inter', label: 'Inter' },
];

export const FontSelector: React.FC<FontSelectorProps> = ({ value, onChange, className }) => {
  const [fonts, setFonts] = useState<FontOption[]>(defaultFonts);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [customFont, setCustomFont] = useState<FontOption | null>(null);
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Check for dark mode on component mount and when theme changes
  useEffect(() => {
    const checkDarkMode = () => {
      setIsDarkMode(document.documentElement.classList.contains('dark'));
    };

    // Initial check
    checkDarkMode();

    // Create observer for theme changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          checkDarkMode();
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class'],
    });

    return () => observer.disconnect();
  }, []);

  // Load Google Fonts on component mount
  useEffect(() => {
    const fetchFonts = async () => {
      try {
        const response = await getFonts();
        setFonts([...defaultFonts, ...response.fonts]);
        setError(null);
      } catch (error) {
        console.error('Error loading fonts:', error);
        setError('Using system fonts (Failed to load Google Fonts)');
        setFonts(defaultFonts);
      } finally {
        setIsLoading(false);
      }
    };

    fetchFonts();
  }, []);

  // Handle custom fonts
  useEffect(() => {
    if (value && !fonts.some(font => font.value === value)) {
      // If the font isn't in our list, create a custom font option
      setCustomFont({
        value,
        label: value,
        category: 'custom',
      });
    } else {
      setCustomFont(null);
    }
  }, [value, fonts]);

  // Load selected font
  useEffect(() => {
    if (value) {
      loadFont(value).catch(error => {
        console.error('Error loading font:', error);
      });
    }
  }, [value]);

  // Filter fonts based on search input
  const filterFonts = (inputValue: string) => {
    const filteredFonts = fonts.filter((font) =>
      font.label.toLowerCase().includes(inputValue.toLowerCase())
    );

    // If we have a custom font and it matches the search, include it
    if (customFont && customFont.label.toLowerCase().includes(inputValue.toLowerCase())) {
      return [customFont, ...filteredFonts];
    }

    return filteredFonts;
  };

  // Load fonts as user types (debounced)
  const loadOptions = (inputValue: string) =>
    new Promise<FontOption[]>((resolve) => {
      setTimeout(() => {
        resolve(filterFonts(inputValue));
      }, 300);
    });

  const customStyles = {
    option: (provided: any, state: { data: FontOption; isFocused: boolean; isSelected: boolean }) => ({
      ...provided,
      fontFamily: state.data.value,
      fontSize: '16px',
      padding: '10px',
      cursor: 'pointer',
      backgroundColor: isDarkMode
        ? state.isSelected
          ? '#374151'
          : state.isFocused
            ? '#1F2937'
            : '#111827'
        : state.isSelected
          ? '#F3F4F6'
          : state.isFocused
            ? '#F9FAFB'
            : 'white',
      color: isDarkMode ? 'white' : 'black',
      ':active': {
        backgroundColor: isDarkMode ? '#374151' : '#F3F4F6',
      },
    }),
    singleValue: (provided: any, state: { data: FontOption }) => ({
      ...provided,
      fontFamily: state.data.value,
      fontSize: '16px',
      color: isDarkMode ? 'white' : 'black',
    }),
    control: (provided: any, state: { isFocused: boolean }) => ({
      ...provided,
      minHeight: '42px',
      backgroundColor: isDarkMode ? '#000000' : 'white',
      borderColor: isDarkMode ? '#1A1A1A' : '#E5E7EB',
      boxShadow: state.isFocused ? '0 0 0 2px #84CC16' : 'none',
      '&:hover': {
        borderColor: isDarkMode ? '#2A2A2A' : '#D1D5DB',
      },
    }),
    menu: (provided: any) => ({
      ...provided,
      zIndex: 9999,
      backgroundColor: isDarkMode ? '#000000' : 'white',
      border: `1px solid ${isDarkMode ? '#1A1A1A' : '#E5E7EB'}`,
    }),
    input: (provided: any) => ({
      ...provided,
      color: isDarkMode ? 'white' : 'black',
    }),
    placeholder: (provided: any) => ({
      ...provided,
      color: isDarkMode ? '#9CA3AF' : '#6B7280',
    }),
  };

  // Get current value as option
  const currentValue = customFont || fonts.find((font) => font.value === value);

  return (
    <div className="font-selector">
      <AsyncSelect
        cacheOptions
        defaultOptions={customFont ? [customFont, ...fonts] : fonts}
        value={currentValue}
        loadOptions={loadOptions}
        isLoading={isLoading}
        onChange={(option) => {
          if (option) {
            onChange((option as FontOption).value);
            loadFont((option as FontOption).value);
          }
        }}
        className={className}
        styles={customStyles}
        placeholder={isLoading ? "Loading fonts..." : "Search fonts..."}
        noOptionsMessage={() => error || "No fonts found"}
      />
      {error && (
        <div className="text-sm text-amber-600 mt-1">
          {error}
        </div>
      )}
    </div>
  );
};
