import React, { useState, useEffect } from 'react';
import { X, Smartphone, Monitor } from 'lucide-react';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

declare global {
  interface WindowEventMap {
    beforeinstallprompt: BeforeInstallPromptEvent;
  }
}

export const PWAInstallPrompt: React.FC = () => {
  console.log('[PWA] PWAInstallPrompt component rendering...');

  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);

  useEffect(() => {
    console.log('[PWA] PWAInstallPrompt component mounted');

    // Comprehensive PWA status check
    const checkPWAStatus = async () => {
      const status = {
        isHTTPS: location.protocol === 'https:' || location.hostname === 'localhost',
        hasServiceWorker: 'serviceWorker' in navigator,
        hasManifest: !!document.querySelector('link[rel="manifest"]'),
        isStandalone: window.matchMedia('(display-mode: standalone)').matches,
        isInWebAppiOS: (window.navigator as any).standalone === true,
        userAgent: navigator.userAgent,
        supportsBeforeInstallPrompt: 'onbeforeinstallprompt' in window,
        isInstalled: false,
      };

      status.isInstalled = status.isStandalone || status.isInWebAppiOS;

      console.log('[PWA] Comprehensive PWA status:', status);

      // Check service worker registration
      if (status.hasServiceWorker) {
        try {
          const registration = await navigator.serviceWorker.getRegistration();
          console.log('[PWA] Service worker registration:', registration);
        } catch (error) {
          console.error('[PWA] Error checking service worker:', error);
        }
      }

      return status;
    };

    // Check if app is already installed
    const checkIfInstalled = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const isInWebAppiOS = (window.navigator as any).standalone === true;
      const isInstalled = isStandalone || isInWebAppiOS;

      console.log('[PWA] Install check:', { isStandalone, isInWebAppiOS, isInstalled });

      if (isInstalled) {
        setIsInstalled(true);
        return true;
      }
      return false;
    };

    // Run the comprehensive check
    checkPWAStatus();

    const installed = checkIfInstalled();

    // If not installed, show the prompt after a delay (unless dismissed recently)
    if (!installed) {
      const dismissedTime = localStorage.getItem('pwa-install-dismissed');
      const shouldShowPrompt = !dismissedTime || (Date.now() - parseInt(dismissedTime)) / (1000 * 60) >= 5; // 5 minutes for testing (was 7 days)

      console.log('[PWA] Should show prompt:', shouldShowPrompt, { dismissedTime });

      if (shouldShowPrompt) {
        // Show prompt after 2 seconds to avoid being too intrusive (reduced for testing)
        console.log('[PWA] Setting timer to show install prompt in 2 seconds');
        const timer = setTimeout(() => {
          console.log('[PWA] Timer fired - showing install prompt');
          setShowInstallPrompt(true);
        }, 2000);

        return () => clearTimeout(timer);
      }
    } else {
      console.log('[PWA] App is already installed, not showing prompt');
    }

    // TEMPORARY: Force show prompt for testing (remove this later)
    if (window.location.search.includes('test-pwa')) {
      console.log('[PWA] Force showing PWA prompt for testing');
      setTimeout(() => setShowInstallPrompt(true), 1000);
    }

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: BeforeInstallPromptEvent) => {
      console.log('[PWA] beforeinstallprompt event fired!', e);
      // Prevent the mini-infobar from appearing on mobile
      e.preventDefault();
      // Save the event so it can be triggered later
      setDeferredPrompt(e);
      // Show our custom install prompt (this will override the timer-based prompt)
      setShowInstallPrompt(true);
      console.log('[PWA] Deferred prompt saved and showing custom prompt');
    };

    // Listen for app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setShowInstallPrompt(false);
      setDeferredPrompt(null);
    };

    console.log('[PWA] Adding event listeners for beforeinstallprompt and appinstalled');
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      console.log('[PWA] Removing event listeners');
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  const handleInstallClick = async () => {
    if (deferredPrompt) {
      // Use the native install prompt if available
      deferredPrompt.prompt();

      // Wait for the user to respond to the prompt
      const { outcome } = await deferredPrompt.userChoice;

      if (outcome === 'accepted') {
        console.log('User accepted the install prompt');
      } else {
        console.log('User dismissed the install prompt');
      }

      // Clear the deferredPrompt
      setDeferredPrompt(null);
      setShowInstallPrompt(false);
    } else {
      // Fallback: Show manual installation instructions
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      const isAndroid = /Android/.test(navigator.userAgent);

      let instructions = '';
      if (isIOS) {
        instructions =
          'To install:\n1. Tap the Share button\n2. Scroll down and tap "Add to Home Screen"\n3. Tap "Add" to confirm';
      } else if (isAndroid) {
        instructions =
          'To install:\n1. Tap the menu (⋮) in your browser\n2. Tap "Add to Home screen" or "Install app"\n3. Tap "Add" to confirm';
      } else {
        instructions =
          'To install:\n1. Look for an install icon in your browser\'s address bar\n2. Or check your browser\'s menu for "Install" or "Add to Home Screen"';
      }

      alert(instructions);
      setShowInstallPrompt(false);
    }
  };

  const handleDismiss = () => {
    setShowInstallPrompt(false);
    // Store dismissal in localStorage to avoid showing again for a while
    localStorage.setItem('pwa-install-dismissed', Date.now().toString());
  };

  // Don't show if already installed or not supposed to show
  if (isInstalled || !showInstallPrompt) {
    return null;
  }

  // Check if dismissed recently (within 5 minutes for testing) - this check is now done in useEffect
  const dismissedTime = localStorage.getItem('pwa-install-dismissed');
  if (dismissedTime) {
    const minutesSinceDismissed = (Date.now() - parseInt(dismissedTime)) / (1000 * 60);
    if (minutesSinceDismissed < 5) {
      return null;
    }
  }

  return (
    <div className='fixed bottom-4 left-4 right-4 sm:left-auto sm:right-4 sm:w-96 z-[100]'>
      <div
        className='bg-[#F9F7ED] border border-[#E8E4D4] rounded-lg shadow-lg p-4'
        style={{ boxShadow: '0 2px 8px rgba(0,0,0,0.08)' }}
      >
        <div className='flex items-start justify-between mb-3'>
          <div className='flex items-center gap-2'>
            <div className='w-8 h-8 rounded-lg flex items-center justify-center overflow-hidden'>
              <img
                src='https://oliviatest.xyz/OptomizedAssets/logo/logo-olivia.gif'
                alt='Olivia'
                className='w-full h-full object-cover'
              />
            </div>
            <h3 className='font-display text-lg font-semibold text-[#566B46]'>Install Olivia</h3>
          </div>
          <button
            onClick={handleDismiss}
            className='text-[#566B46] hover:text-[#C8A13C] transition-colors'
            aria-label='Dismiss install prompt'
          >
            <X className='w-5 h-5' />
          </button>
        </div>

        <p className='text-[#566B46] text-sm mb-4'>
          Install Olivia for a better experience with faster loading and offline access.
        </p>

        <div className='flex items-center gap-3 mb-4'>
          <div className='flex items-center gap-2 text-xs text-[#566B46]/70'>
            <Monitor className='w-4 h-4' />
            <span>Desktop app</span>
          </div>
          <div className='flex items-center gap-2 text-xs text-[#566B46]/70'>
            <Smartphone className='w-4 h-4' />
            <span>Mobile app</span>
          </div>
        </div>

        <div className='flex gap-2'>
          <button
            onClick={handleDismiss}
            className='flex-1 px-3 py-2 text-[#566B46] hover:text-[#C8A13C] transition-colors text-sm'
          >
            Not now
          </button>
          <button
            onClick={handleInstallClick}
            className='flex-1 px-3 py-2 bg-[#9EA581] hover:bg-[#8a9470] text-white rounded-lg transition-colors text-sm font-medium'
          >
            Install
          </button>
        </div>
      </div>
    </div>
  );
};
