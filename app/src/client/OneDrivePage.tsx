import React, { useState, useEffect } from 'react';
import { useQuery } from 'wasp/client/operations';
import { getOneDriveFiles, proxyOneDriveImage } from 'wasp/client/operations';
import { useOneDriveAuth } from '../hooks/useOneDriveAuth';
import { OneDriveConnectButton } from '../components/OneDriveConnectButton';
import { ErrorMessage, SuccessMessage } from '../components/AuthMessages';
import { Link } from 'react-router-dom';
import { ArrowLeft, Check, FolderOpen, Image, RefreshCw, Search } from 'lucide-react';

const OneDrivePage: React.FC = () => {
  const [currentPath, setCurrentPath] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [nextLink, setNextLink] = useState<string | undefined>(undefined);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [pathHistory, setPathHistory] = useState<{ id: string; name: string }[]>([]);

  const { isAuthenticated, isLoading: isAuthLoading, error: authError, successMessage } = useOneDriveAuth();

  const {
    data: filesData,
    isLoading: isFilesLoading,
    error: filesError,
    refetch: refetchFiles,
  } = useQuery(
    getOneDriveFiles,
    {
      path: currentPath,
      nextLink: nextLink,
    },
    {
      enabled: !!isAuthenticated,
    }
  );

  // Handle folder navigation
  const navigateToFolder = (folderId: string, folderName: string) => {
    setPathHistory([...pathHistory, { id: currentPath, name: folderName }]);
    setCurrentPath(folderId);
    setNextLink(undefined);
  };

  // Handle back navigation
  const navigateBack = () => {
    if (pathHistory.length > 0) {
      const newHistory = [...pathHistory];
      const previousFolder = newHistory.pop();
      setPathHistory(newHistory);
      setCurrentPath(previousFolder?.id || '');
      setNextLink(undefined);
    }
  };

  // Handle load more
  const handleLoadMore = () => {
    if (filesData?.nextLink) {
      setIsLoadingMore(true);
      setNextLink(filesData.nextLink);
    }
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    refetchFiles();
  };

  // Handle image selection
  const handleImageSelect = async (fileId: string) => {
    setSelectedImage(fileId);
    try {
      const result = await proxyOneDriveImage({
        fileId,
        asBase64: true,
      });

      // Here you could add the image to your references or do something else with it
      console.log('Image proxied successfully:', result);

      // Example: Add to references (you would need to implement this function)
      // addToReferences(result.url, fileId);
    } catch (error) {
      console.error('Error proxying image:', error);
    }
  };

  if (isAuthLoading) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 dark:border-white'></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <h1 className='text-2xl font-bold mb-6'>OneDrive Integration</h1>
        {authError && <ErrorMessage message={`Error with OneDrive integration: ${authError}`} />}
        <SuccessMessage message={successMessage} />

        <div className='bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6'>
          <div className='flex flex-col items-center justify-center py-12'>
            <FolderOpen className='w-16 h-16 text-blue-500 mb-4' />
            <h2 className='text-xl font-semibold mb-2'>Connect to OneDrive</h2>
            <p className='text-gray-600 dark:text-gray-400 mb-6 text-center max-w-md'>
              Connect your Microsoft OneDrive account to access your files and images directly from this application.
            </p>
            <OneDriveConnectButton buttonText='Connect OneDrive' redirectUrl='/onedrive' />
          </div>
        </div>

        <div className='mt-4'>
          <Link to='/account' className='text-blue-500 hover:underline flex items-center'>
            <ArrowLeft className='w-4 h-4 mr-1' /> Back to Account
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <h1 className='text-2xl font-bold mb-6'>OneDrive Files</h1>
      {authError && <ErrorMessage message={`Error with OneDrive integration: ${authError}`} />}
      {filesError && <ErrorMessage message={`Error fetching files: ${filesError}`} />}
      <SuccessMessage message={successMessage} />

      <div className='bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6'>
        <div className='flex justify-between items-center mb-4'>
          <div className='flex items-center'>
            {pathHistory.length > 0 && (
              <button
                onClick={navigateBack}
                className='mr-2 p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700'
                title='Go back'
              >
                <ArrowLeft className='w-5 h-5' />
              </button>
            )}
            <h2 className='text-lg font-semibold'>
              {pathHistory.length > 0 ? 'Current folder: ' + pathHistory[pathHistory.length - 1]?.name : 'Root folder'}
            </h2>
          </div>

          <div className='flex items-center'>
            <form onSubmit={handleSearch} className='flex mr-2'>
              <input
                type='text'
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder='Search files...'
                className='px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white'
              />
              <button
                type='submit'
                className='px-3 py-2 bg-blue-500 text-white rounded-r-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500'
              >
                <Search className='w-5 h-5' />
              </button>
            </form>

            <button
              onClick={() => refetchFiles()}
              className='p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700'
              title='Refresh'
              disabled={isFilesLoading}
            >
              <RefreshCw className={`w-5 h-5 ${isFilesLoading ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>

        {isFilesLoading && !isLoadingMore ? (
          <div className='flex items-center justify-center py-12'>
            <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 dark:border-white'></div>
          </div>
        ) : (
          <>
            {filesData?.files && filesData.files.length > 0 ? (
              <div className='grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4'>
                {filesData.files.map((file) => (
                  <div
                    key={file.id}
                    className={`relative rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow ${
                      selectedImage === file.id ? 'ring-2 ring-blue-500' : ''
                    }`}
                  >
                    {file.isFolder ? (
                      <div
                        className='cursor-pointer p-4 flex flex-col items-center justify-center h-full'
                        onClick={() => navigateToFolder(file.id, file.name)}
                      >
                        <FolderOpen className='w-12 h-12 text-yellow-500 mb-2' />
                        <p className='text-sm text-center truncate w-full'>{file.name}</p>
                      </div>
                    ) : (
                      <div className='cursor-pointer' onClick={() => handleImageSelect(file.id)}>
                        {file.thumbnailUrl ? (
                          <div className='aspect-square'>
                            <img
                              src={file.thumbnailUrl}
                              alt={file.name}
                              className='w-full h-full object-cover'
                              loading='lazy'
                            />
                          </div>
                        ) : (
                          <div className='aspect-square flex items-center justify-center bg-gray-100 dark:bg-gray-700'>
                            <Image className='w-12 h-12 text-gray-400' />
                          </div>
                        )}
                        <div className='p-2'>
                          <p className='text-sm truncate'>{file.name}</p>
                          <p className='text-xs text-gray-500 dark:text-gray-400'>
                            {file.size ? `${Math.round(file.size / 1024)} KB` : ''}
                          </p>
                        </div>
                      </div>
                    )}
                    {selectedImage === file.id && (
                      <div className='absolute top-2 right-2 bg-blue-500 text-white rounded-full p-1'>
                        <Check className='w-4 h-4' />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className='text-center py-12'>
                <FolderOpen className='w-16 h-16 text-gray-400 mx-auto mb-4' />
                <p className='text-gray-600 dark:text-gray-400'>No files found in this folder</p>
              </div>
            )}

            {filesData?.hasMore && (
              <div className='mt-6 text-center'>
                <button
                  onClick={handleLoadMore}
                  disabled={isLoadingMore}
                  className='px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50'
                >
                  {isLoadingMore ? 'Loading...' : 'Load More'}
                </button>
              </div>
            )}
          </>
        )}
      </div>

      <div className='mt-4'>
        <Link to='/account' className='text-blue-500 hover:underline flex items-center'>
          <ArrowLeft className='w-4 h-4 mr-1' /> Back to Account
        </Link>
      </div>
    </div>
  );
};

export default OneDrivePage;
