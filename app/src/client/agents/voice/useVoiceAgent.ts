/**
 * Voice Agent Hook
 *
 * This hook provides access to the voice agent functionality.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from 'wasp/client/auth';
import { v4 as uuidv4 } from 'uuid';
import { useVoiceAgentRoom } from './../../hooks/useVoiceAgentRoom';
import { useSocket, useSocketListener } from 'wasp/client/webSocket';
import type { VoiceAgentStatusData } from '../../../websocket/types';
import { Room, RoomEvent, RemoteParticipant, RemoteTrack, Track, LocalAudioTrack } from 'livekit-client';
import { generateLivekitToken } from 'wasp/client/operations';

// Define voice agent state
interface VoiceAgentState {
  isActive: boolean;
  isConnected: boolean;
  roomName: string;
  status: VoiceAgentStatusData['status'];
  error: string | null;
  userTranscript: string;
  agentTranscript: string;
  audioLevel: number;
}

// Define voice agent options
interface UseVoiceAgentOptions {
  systemPrompt?: string;
}

/**
 * Hook to use the voice agent
 * @param options Voice agent options
 * @returns The voice agent state and methods
 */
export const useVoiceAgent = (options: UseVoiceAgentOptions = {}) => {
  const { data: user } = useAuth();
  const { socket, isConnected } = useSocket();
  const [state, setState] = useState<VoiceAgentState>({
    isActive: false,
    isConnected: false,
    roomName: '',
    status: 'disconnected',
    error: null,
    userTranscript: '',
    agentTranscript: '',
    audioLevel: 0
  });

  // Create refs for LiveKit room
  const roomRef = useRef<Room | null>(null);
  const isMountedRef = useRef(true);
  const lastConnectionAttemptRef = useRef(0);
  const connectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Get the room name from our centralized hook
  const roomName = useVoiceAgentRoom();
  const stableRoomNameRef = useRef(roomName);

  const audioLevelIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Create a stable reference to the current state
  const stateRef = useRef(state);
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  // Helper function to safely emit events
  const emitEvent = useCallback((event: string, data: any) => {
    if (socket && isConnected) {
      console.log(`[VoiceAgent] Emitting ${event} event:`, data);
      // Cast socket to any to avoid type errors
      (socket as any).emit(event, data);
      return true;
    } else {
      console.error('[VoiceAgent] Socket is not connected, cannot emit event:', event);
      console.log('[VoiceAgent] Socket exists:', !!socket);
      console.log('[VoiceAgent] isConnected:', isConnected);
      return false;
    }
  }, [socket, isConnected]);

  // Function to connect to a LiveKit room
  const connectToRoom = useCallback(async () => {
    console.log('[VoiceAgent] connectToRoom called with state:', {
      isActive: state.isActive,
      socketExists: !!socket,
      isConnected,
      userId: user?.id
    });

    if (!state.isActive) {
      console.log('[VoiceAgent] Not active, skipping connection');
      return;
    }

    if (!socket) {
      console.log('[VoiceAgent] No socket, skipping connection');
      return;
    }

    if (!isConnected) {
      console.log('[VoiceAgent] Socket not connected, skipping connection');
      return;
    }

    if (!user?.id) {
      console.log('[VoiceAgent] No user ID, skipping connection');
      return;
    }

    // If we already have a room, don't create a new one
    if (roomRef.current) {
      console.log('[VoiceAgent] Room already exists, skipping connection');
      return;
    }

    // Prevent rapid reconnection attempts
    const now = Date.now();
    if (now - lastConnectionAttemptRef.current < 5000) {
      console.log('[VoiceAgent] Throttling connection attempt - too soon since last attempt');
      return;
    }
    lastConnectionAttemptRef.current = now;

    // Clear any existing timeout
    if (connectionTimeoutRef.current) {
      clearTimeout(connectionTimeoutRef.current);
    }

    // Set a timeout to prevent hanging connections
    connectionTimeoutRef.current = setTimeout(() => {
      console.warn('[VoiceAgent] Connection timeout - forcing disconnect');
      if (roomRef.current) {
        roomRef.current.disconnect();
      }
    }, 30000); // 30 second timeout

    try {
      // Generate a token for the room
      const { token } = await generateLivekitToken({
        roomName: stableRoomNameRef.current,
        participantName: `user-${user.id}`
      });

      // Start the voice agent via WebSocket
      emitEvent('startVoiceAgent', {
        roomName: stableRoomNameRef.current,
        systemPrompt: options.systemPrompt || 'You are a helpful voice assistant. Respond concisely and clearly.',
        userId: user.id  // Pass the current user's ID to the Python voice agent
      });

      console.log('[VoiceAgent] Starting voice agent with room name:', stableRoomNameRef.current);

      // Update state
      setState(prev => ({
        ...prev,
        roomName: stableRoomNameRef.current,
        isConnected: true
      }));

      // Room name is already stored by the useVoiceAgentRoom hook
      console.log(`[VoiceAgent] Using room name from hook: ${stableRoomNameRef.current}`);

      // Create a new LiveKit room
      const newRoom = new Room();

      // Set up room event listeners
      newRoom.on(RoomEvent.ParticipantConnected, (participant: RemoteParticipant) => {
        console.log('[VoiceAgent] Participant connected:', participant.identity);
      });

      newRoom.on(RoomEvent.ParticipantDisconnected, (participant: RemoteParticipant) => {
        console.log('[VoiceAgent] Participant disconnected:', participant.identity);
      });

      newRoom.on(RoomEvent.TrackSubscribed, (track: RemoteTrack, publication, participant) => {
        console.log('[VoiceAgent] Track subscribed:', track.kind, 'from', participant.identity);

        // Attach audio track to an audio element
        if (track.kind === Track.Kind.Audio) {
          console.log('[VoiceAgent] Attaching audio track to audio element');
          const audioElement = new Audio();
          audioElement.autoplay = true;
          audioElement.muted = false;
          audioElement.volume = 1.0;

          // Add event listeners to debug audio playback
          audioElement.onplay = () => console.log('[VoiceAgent] Audio started playing');
          audioElement.onpause = () => console.log('[VoiceAgent] Audio paused');
          audioElement.onended = () => console.log('[VoiceAgent] Audio ended');
          audioElement.onerror = (e) => console.error('[VoiceAgent] Audio error:', e);

          // Attach the track to the audio element
          track.attach(audioElement);

          // Log success
          console.log('[VoiceAgent] Audio track attached successfully');
        }
      });

      // Handle audio playback status changes
      newRoom.on(RoomEvent.AudioPlaybackStatusChanged, (status: boolean) => {
        console.log('[VoiceAgent] Audio playback status changed:', status ? 'started' : 'stopped');

        // Update status to 'speaking' when audio playback starts
        if (status) {
          setState(prev => ({
            ...prev,
            status: 'speaking'
          }));
        } else {
          // Update status to 'listening' when audio playback stops
          setState(prev => ({
            ...prev,
            status: prev.status === 'speaking' ? 'listening' : prev.status
          }));
        }
      });

      // Handle local track published
      newRoom.on(RoomEvent.LocalTrackPublished, (track, publication) => {
        console.log('[VoiceAgent] Local track published:', track.kind);

        if (track.kind === Track.Kind.Audio) {
          console.log('[VoiceAgent] Local audio track published');

          // Set status to listening when microphone is published
          setState(prev => ({
            ...prev,
            status: 'listening'
          }));
        }
      });

      // Handle connection state changes
      newRoom.on(RoomEvent.ConnectionStateChanged, (state) => {
        console.log('[VoiceAgent] Connection state changed:', state);

        if (state === 'connected') {
          console.log('[VoiceAgent] Connected to LiveKit room');

          // Clear connection timeout since we're connected
          if (connectionTimeoutRef.current) {
            clearTimeout(connectionTimeoutRef.current);
            connectionTimeoutRef.current = null;
          }

          // Update state
          setState(prev => ({
            ...prev,
            isConnected: true,
            status: 'connected'
          }));
        } else if (state === 'disconnected') {
          console.log('[VoiceAgent] Disconnected from LiveKit room');

          // Update state
          setState(prev => ({
            ...prev,
            isConnected: false,
            status: 'disconnected'
          }));
        }
      });

      newRoom.on(RoomEvent.TranscriptionReceived, (segments, participant, _publication) => {
        // Process each segment
        for (const segment of segments) {
          const speaker = participant && participant.identity.startsWith('agent') ? 'agent' : 'user';

          // Update state with transcript
          if (speaker === 'user') {
            setState(prev => ({ ...prev, userTranscript: segment.text }));
          } else {
            setState(prev => ({ ...prev, agentTranscript: segment.text }));
          }
        }
      });

      // Check for microphone permissions
      try {
        console.log('[VoiceAgent] Checking microphone permissions...');
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        console.log('[VoiceAgent] Microphone permission granted');

        // Stop the stream immediately to release the microphone
        stream.getTracks().forEach(track => track.stop());
      } catch (permissionError) {
        console.error('[VoiceAgent] Microphone permission denied:', permissionError);
        alert('Please allow microphone access to use the voice agent');
      }

      // Connect to the room
      const livekitUrl = import.meta.env.VITE_LIVEKIT_URL || 'wss://olivia-2ldjoidx.livekit.cloud';
      await newRoom.connect(livekitUrl, token);

      // Enable microphone
      try {
        console.log('[VoiceAgent] Enabling microphone...');
        await newRoom.localParticipant.setMicrophoneEnabled(true);
        console.log('[VoiceAgent] Microphone enabled successfully');

        // Set up audio level monitoring using Web Audio API
        try {
          console.log('[VoiceAgent] Setting up audio level monitoring');

          // Get the microphone stream
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

          // Create audio context and analyzer
          const audioContext = new AudioContext();
          const analyser = audioContext.createAnalyser();
          const microphone = audioContext.createMediaStreamSource(stream);
          microphone.connect(analyser);

          // Configure analyzer
          analyser.fftSize = 1024;
          analyser.smoothingTimeConstant = 0.5;
          const bufferLength = analyser.frequencyBinCount;
          const dataArray = new Uint8Array(bufferLength);

          // Monitor audio levels
          const audioLevelInterval = setInterval(() => {
            if (!isMountedRef.current) {
              clearInterval(audioLevelInterval);
              stream.getTracks().forEach(track => track.stop());
              return;
            }

            // Get audio data
            analyser.getByteFrequencyData(dataArray);

            // Calculate average level
            let sum = 0;
            for (let i = 0; i < bufferLength; i++) {
              sum += dataArray[i];
            }
            const average = sum / bufferLength;

            // Scale the level for better visibility (0-100)
            const level = Math.min(100, average * 0.8);

            // Update state
            setState(prev => ({ ...prev, audioLevel: level }));

            // Log significant level changes
            if (level > 10 && Date.now() % 20 === 0) {
              console.log(`[VoiceAgent] Audio level: ${level.toFixed(1)}`);
            }
          }, 100);

          // Store the interval ID for cleanup
          audioLevelIntervalRef.current = audioLevelInterval;
        } catch (error) {
          console.error('[VoiceAgent] Error setting up audio level monitoring:', error);
        }
      } catch (micError) {
        console.error('[VoiceAgent] Error enabling microphone:', micError);
      }

      // Store the room
      roomRef.current = newRoom;

      return newRoom;
    } catch (error) {
      console.error('[VoiceAgent] Error connecting to room:', error);

      // Update state with error
      setState(prev => ({
        ...prev,
        isConnected: false,
        status: 'error',
        error: error instanceof Error ? error.message : 'Error connecting to room'
      }));

      throw error;
    }
  }, [state.isActive, socket, isConnected, user?.id, emitEvent, options.systemPrompt]);

  // Function to disconnect from the room
  const disconnectFromRoom = useCallback(async () => {
    if (!roomRef.current) {
      console.log('[VoiceAgent] No room to disconnect from');
      return;
    }

    try {
      console.log('[VoiceAgent] Disconnecting from room:', stateRef.current.roomName);

      // Clear audio level monitoring interval
      if (audioLevelIntervalRef.current) {
        console.log('[VoiceAgent] Clearing audio level monitoring interval');
        clearInterval(audioLevelIntervalRef.current);
        audioLevelIntervalRef.current = null;
      }

      // Disable microphone
      try {
        console.log('[VoiceAgent] Disabling microphone...');
        await roomRef.current.localParticipant.setMicrophoneEnabled(false);
        console.log('[VoiceAgent] Microphone disabled successfully');
      } catch (micError) {
        console.error('[VoiceAgent] Error disabling microphone:', micError);
      }

      // Detach all tracks from remote participants
      roomRef.current.remoteParticipants.forEach(participant => {
        // Get all audio tracks
        const audioTracks = participant.getTrackPublications();
        audioTracks.forEach(publication => {
          if (publication.track) {
            publication.track.detach();
          }
        });
      });

      // Disconnect from the room
      await roomRef.current.disconnect();

      // Stop the voice agent via WebSocket
      if (socket && isConnected) {
        console.log('[VoiceAgent] Sending stopVoiceAgent event for room:', stateRef.current.roomName);
        emitEvent('stopVoiceAgent', {
          roomName: stateRef.current.roomName
        });
      } else {
        console.log('[VoiceAgent] Socket not connected, cannot send stopVoiceAgent event');
      }

      // Update state
      setState(prev => ({
        ...prev,
        isConnected: false,
        status: 'disconnected'
      }));

      // Note: We're not clearing the room name from localStorage here anymore
      // This allows reference images to persist between voice agent sessions
      console.log('[VoiceAgent] Room name persists in localStorage for reference images');

      // Clear the room
      roomRef.current = null;
      console.log('[VoiceAgent] Successfully disconnected from room');
    } catch (error) {
      console.error('[VoiceAgent] Error disconnecting from room:', error);
    }
  }, [socket, isConnected, emitEvent]);

  // Toggle the voice agent
  const toggleVoiceAgent = useCallback(() => {
    setState(prev => {
      const newIsActive = !prev.isActive;
      console.log(`[VoiceAgent] Toggling voice agent: ${prev.isActive} -> ${newIsActive}`);
      return { ...prev, isActive: newIsActive };
    });
  }, []);

  // Handle voice agent status updates
  const handleVoiceAgentStatus = useCallback((data: VoiceAgentStatusData) => {
    console.log('[VoiceAgent] Received voiceAgentStatus event:', data);
    console.log('[VoiceAgent] Current room name:', stateRef.current.roomName);

    if (data.roomName !== stateRef.current.roomName) {
      console.log('[VoiceAgent] Ignoring status update for different room');
      return;
    }

    // Update state with status
    console.log('[VoiceAgent] Updating status to:', data.status);
    setState(prev => ({
      ...prev,
      status: data.status,
      error: data.status === 'error' ? data.error || null : null
    }));
  }, []);

  // Handle voice agent transcript updates
  const handleVoiceAgentTranscript = useCallback((data: {
    roomName: string;
    transcript: string;
    isFinal: boolean;
    speaker: 'user' | 'agent';
  }) => {
    if (data.roomName !== stateRef.current.roomName) return;

    // Update state with transcript
    if (data.speaker === 'user') {
      setState(prev => ({ ...prev, userTranscript: data.transcript }));
    } else {
      setState(prev => ({ ...prev, agentTranscript: data.transcript }));
    }
  }, []);

  // Handle voice agent response
  const handleVoiceAgentResponse = useCallback((data: {
    roomName: string;
    taskId: string;
    response: string;
  }) => {
    if (data.roomName !== stateRef.current.roomName) return;

    // Update state with response
    setState(prev => ({ ...prev, agentTranscript: data.response }));
  }, []);

  // Set up WebSocket event listeners using WASP's useSocketListener
  // Cast the event names to any to avoid type errors
  useSocketListener('voiceAgentStatus' as any, handleVoiceAgentStatus);
  useSocketListener('voiceAgentTranscript' as any, handleVoiceAgentTranscript);
  useSocketListener('voiceAgentResponse' as any, handleVoiceAgentResponse);

  // Log all events for debugging
  useEffect(() => {
    if (socket && isConnected) {
      console.log('[VoiceAgent] Setting up WebSocket event logging');

      // Log when we receive any event (except cursor-related events)
      const logAnyEvent = (event: string, ...args: any[]) => {
        // Skip logging cursor-related events to reduce console spam
        if (event === 'cursorUpdate' || event === 'cursorPosition') {
          return;
        }
        console.log(`[VoiceAgent] Received event ${event}:`, args);
      };

      socket.onAny(logAnyEvent);

      return () => {
        console.log('[VoiceAgent] Removing WebSocket event logging');
        socket.offAny(logAnyEvent);
      };
    }
  }, [socket, isConnected]);

  // Connect or disconnect based on isActive state
  useEffect(() => {
    console.log('[VoiceAgent] isActive changed:', state.isActive);

    if (state.isActive) {
      console.log('[VoiceAgent] Connecting to room...');
      connectToRoom().catch(error => {
        console.error('[VoiceAgent] Error in connectToRoom effect:', error);
      });
    } else {
      console.log('[VoiceAgent] Disconnecting from room...');
      disconnectFromRoom().catch(error => {
        console.error('[VoiceAgent] Error in disconnectFromRoom effect:', error);
      });
    }
  }, [state.isActive, connectToRoom, disconnectFromRoom]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      console.log('[VoiceAgent] Cleaning up on unmount');
      isMountedRef.current = false;

      // Disconnect from the room
      if (roomRef.current) {
        console.log('[VoiceAgent] Disconnecting from room on unmount');
        roomRef.current.disconnect().catch(error => {
          console.error('[VoiceAgent] Error disconnecting from room on unmount:', error);
        });
      }

      // Clear audio level monitoring interval
      if (audioLevelIntervalRef.current) {
        console.log('[VoiceAgent] Clearing audio level monitoring interval on unmount');
        clearInterval(audioLevelIntervalRef.current);
        audioLevelIntervalRef.current = null;
      }
    };
  }, []);

  // Return the voice agent state and methods
  return {
    ...state,
    toggleVoiceAgent,
    connectToRoom,
    disconnectFromRoom
  };
};
