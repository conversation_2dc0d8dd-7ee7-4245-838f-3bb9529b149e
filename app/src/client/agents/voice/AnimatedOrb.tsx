import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import './AnimatedOrb.css';
import { FiMic, FiMicOff, FiArrowLeft, FiX } from 'react-icons/fi';
import { useVoiceAgent } from './useVoiceAgent';

interface AnimatedOrbProps {
  onBack?: () => void;
}

const AnimatedOrb: React.FC<AnimatedOrbProps> = ({
  onBack
}) => {
  console.log('[AnimatedOrb] Component rendering');
  // Use framer-motion positioning (transform-based) instead of CSS top/left
  const [pos, setPos] = useState<{ x: number; y: number }>(() => ({
    x: typeof window !== 'undefined' ? 100 : 100, // Position more visible for testing
    y: typeof window !== 'undefined' ? 100 : 100 // Position more visible for testing
  }));
  const [dragging, setDragging] = useState(false);
  // Track position at drag start and the pointer-to-orb offset
  const dragStart = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
  const grabOffset = useRef<{ dx: number; dy: number }>({ dx: 0, dy: 0 });
  const [showControls, setShowControls] = useState(false);
  const [hasAppeared, setHasAppeared] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Use our new voice agent hook
  const {
    isActive,
    status: assistantStatus,
    userTranscript: transcript,
    agentTranscript: assistantResponse,
    audioLevel,
    toggleVoiceAgent
  } = useVoiceAgent({
    systemPrompt: 'You are a helpful voice assistant. Respond concisely and clearly to the user\'s questions.'
  });

  // Container reference
  const containerRef = useRef<HTMLDivElement>(null);

  // Container reference for positioning

  // Set hasAppeared to true after a delay to trigger animation
  useEffect(() => {
    console.log('[AnimatedOrb] Component mounted, setting up appearance timer');
    const timer = setTimeout(() => {
      console.log('[AnimatedOrb] Setting hasAppeared to true');
      setHasAppeared(true);
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  // Our hook handles all the voice agent state changes

  // Clean up on unmount
  useEffect(() => {
    return () => {
      // Clean up any resources if needed when component unmounts
      // Our hook handles the cleanup of voice agent resources
    };
  }, []);

  // Window resize handler
  useEffect(() => {
    const handleResize = () => {
      // Adjust orb position to stay in bounds if needed
      if (pos.x > window.innerWidth - 120) {
        setPos(prev => ({ ...prev, x: window.innerWidth - 120 }));
      }
      if (pos.y > window.innerHeight - 120) {
        setPos(prev => ({ ...prev, y: window.innerHeight - 120 }));
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [pos]);

  // Toggle controls visibility
  const handleOrbClick = () => {
    if (dragging) return;

    // Toggle active state
    toggleVoiceAgent();
    setShowControls(true);
  };

  // Audio level is used directly in the component style

  // Our hook handles all the voice agent events
  // We just need to update the error message when there's an error
  // Handle status changes
  useEffect(() => {
    if (assistantStatus === 'error') {
      setErrorMessage('Error connecting to voice agent');
    } else {
      setErrorMessage(null);
    }
  }, [assistantStatus]);

  // Handle active state changes
  useEffect(() => {
    // Don't do anything that would affect position when isActive changes
    console.log('[AnimatedOrb] Active state changed:', isActive);
  }, [isActive]);

  return (
    <div className="animated-orb-container" ref={containerRef}>
      {/* We don't need to render the VoiceAgentWebSocket component anymore */}
      {/* Our useVoiceAgent hook handles the connection */}

      {/* Only show title when controls are visible */}
      {/* Title removed as requested */}

      <motion.div
        className={`animated-orb ${isActive ? 'active' : ''} ${assistantStatus === 'processing' ? 'thinking' : ''}`}
        // Only set position:absolute in style, let framer-motion handle x/y via transform
        style={{
          position: 'absolute'
        }}
        // Use framer-motion x/y for positioning (transform-based)
        animate={{
          x: pos.x,
          y: pos.y,
          scale: hasAppeared ? 1 : 0.5,
          opacity: hasAppeared ? 1 : 0,
        }}
        initial={{
          x: pos.x,
          y: pos.y,
          scale: 0.5,
          opacity: 0
        }}
        transition={{
          scale: { type: "spring", stiffness: 300, damping: 20 },
          opacity: { duration: 0.3 }
        }}
        drag={hasAppeared}
        dragConstraints={{ left: 0, right: window.innerWidth - 120, top: 0, bottom: window.innerHeight - 120 }}
        dragMomentum={false}
        onDragStart={(event, info) => {
          console.log('[AnimatedOrb] dragStart pos', pos, 'pointer', info.point);
          setDragging(true);

          // Store starting element position
          dragStart.current = pos;
          // Store pointer-to-orb offset (difference between pointer and element position)
          grabOffset.current = {
            dx: info.point.x - pos.x,
            dy: info.point.y - pos.y
          };
        }}
        onDrag={(event, info) => {
          console.log('[AnimatedOrb] drag delta', info.delta, 'offset', info.offset);
        }}
        onDragEnd={(event, info) => {
          setDragging(false);
          console.log('[AnimatedOrb] Drag end pointer', info.point, 'grabOffset', grabOffset.current);

          // Calculate new position using pointer location, keeping the correct grab offset
          const orbSize = 120;
          const newX = Math.min(
            Math.max(info.point.x - grabOffset.current.dx, 0),
            window.innerWidth - orbSize
          );
          const newY = Math.min(
            Math.max(info.point.y - grabOffset.current.dy, 0),
            window.innerHeight - orbSize
          );

          // Update framer-motion position state (transform-based)
          setPos({ x: newX, y: newY });
        }}
        onClick={() => {
          if (hasAppeared && !dragging) {
            handleOrbClick();
          }
        }}
      >
        {/* Pulse overlay for visual feedback without interfering with drag transform */}
        {isActive && <div className="orb-pulse" />}

        {/* Audio level indicator */}
        {isActive && (
          <div className="audio-level-indicator">
            <div className="audio-level-bar-container">
              <div
                className="audio-level-bar"
                style={{ width: `${Math.min(100, audioLevel * 150)}%` }}
              />
            </div>
          </div>
        )}

        {/* Transcripts positioned relative to the orb */}
        {transcript && (
          <div className="orb-transcript">
            {transcript}
          </div>
        )}

        {assistantResponse && (
          <div className="orb-response">
            {assistantResponse}
          </div>
        )}
      </motion.div>

      {/* Status indicator - only showing errors */}
      {showControls && errorMessage && (
        <div className="orb-status">
          <div className="orb-error">{errorMessage}</div>
        </div>
      )}

      {/* All buttons removed as requested - just using the orb icon for interaction */}

      {/* All instructions and buttons removed for a cleaner interface */}

      {/* Transcripts now positioned inside the orb motion.div */}
    </div>
  );
};

export default AnimatedOrb;
