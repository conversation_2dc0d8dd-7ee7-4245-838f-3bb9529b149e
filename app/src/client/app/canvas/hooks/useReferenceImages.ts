/**
 * Hook for managing reference images
 *
 * This hook provides functionality for managing reference images on the canvas
 * and synchronizing them with the server.
 */

import { useEffect, useCallback, useRef } from 'react';
import { useSocketListener } from 'wasp/client/webSocket';
import { useAuthenticatedSocket } from './../../../hooks/useAuthenticatedSocket';
import {
  useAction,
  useQuery,
  uploadReferenceImage,
  getCanvasReferences,
  createCanvasReference,
  deleteCanvasReference,
} from 'wasp/client/operations';

/**
 * Hook for managing reference images
 * @param roomName The room name to associate reference images with (for socket events)
 * @param whiteboardId The whiteboard ID to associate reference images with (for database)
 * @returns Functions for managing reference images
 */
export const useReferenceImages = (roomName: string, whiteboardId?: string) => {
  const { socket, isConnected } = useAuthenticatedSocket();

  // Use a ref to track the last time we sent reference images
  const lastSentTimeRef = useRef<number>(0);
  const lastRoomNameRef = useRef<string>('');

  // Query canvas references from database
  const { data: canvasReferences, isLoading } = useQuery(
    getCanvasReferences,
    { whiteboardId: whiteboardId ?? '' },
    { enabled: !!whiteboardId }
  );

  console.log({ canvasReferences });

  // Actions for managing canvas references
  const createCanvasReferenceAction = useAction(createCanvasReference);
  const deleteCanvasReferenceAction = useAction(deleteCanvasReference);

  // Initialize reference images from database and sync with server
  useEffect(() => {
    // Only process for voice agent rooms to avoid duplicate processing
    if (isConnected && roomName && roomName.startsWith('voice-agent-') && canvasReferences) {
      // Throttle sending reference images to once every 5 seconds per room
      const now = Date.now();
      if (now - lastSentTimeRef.current < 5000 && lastRoomNameRef.current === roomName) {
        console.log(`[useReferenceImages] Throttling reference image update for room ${roomName}`);
        return;
      }

      // Get reference images from database
      const referenceImages = canvasReferences.map((ref: any) => ref.thumbUrl);

      console.log(`[useReferenceImages] Current room name: ${roomName}`);
      console.log(`[useReferenceImages] Found ${referenceImages.length} reference images in database`);
      if (referenceImages.length > 0) {
        console.log(`[useReferenceImages] Reference image URLs:`, referenceImages);
      }

      // Only send non-empty arrays to avoid wiping out server state
      if (referenceImages.length > 0) {
        // Send reference images to the server
        socket.emit('setReferenceImages', {
          roomName,
          images: referenceImages,
        });
        console.log(
          `[useReferenceImages] Sent ${referenceImages.length} reference images to server for room ${roomName}`
        );
      } else {
        console.log(`[useReferenceImages] Nothing to sync - skipping empty array send`);
        return;
      }

      // Update the last sent time and room name
      lastSentTimeRef.current = now;
      lastRoomNameRef.current = roomName;

      console.log(
        `[useReferenceImages] Sent ${referenceImages.length} reference images to server for room ${roomName}`
      );
    }
  }, [isConnected, roomName, socket, canvasReferences]);

  // Listen for reference images updates from the server
  useSocketListener('referenceImagesUpdated', (data: { roomName: string; images: string[] }) => {
    // Only process for voice agent rooms to avoid duplicate processing
    if (data.roomName === roomName && roomName.startsWith('voice-agent-')) {
      console.log(
        `[useReferenceImages] Received ${data.images.length} reference images from server for room ${roomName}`
      );

      // Note: Database updates will be handled by the query refetch automatically
      // No need to manually update localStorage anymore
      console.log(`[useReferenceImages] Database will be updated automatically via query refetch`);
    }
  });

  // Function to add a reference image
  const uploadReferenceImageAction = useAction(uploadReferenceImage);

  const addReferenceImage = useCallback(
    async (imageUrl: string) => {
      if (!whiteboardId) {
        console.warn('[useReferenceImages] No whiteboardId provided, cannot add reference image');
        return false;
      }

      // Get current reference images from database
      const currentReferences = canvasReferences || [];
      const referenceImages = currentReferences.map((ref: any) => ref.thumbUrl);

      // Check if the image is already in the list
      if (!referenceImages.includes(imageUrl)) {
        try {
          console.log('[useReferenceImages] Adding reference image');

          // Check if this is a data URL (base64 image)
          let finalImageUrl = imageUrl;

          if (imageUrl.startsWith('data:image/')) {
            try {
              console.log(
                '[useReferenceImages] Detected base64 image, uploading to R2 to avoid localStorage quota issues'
              );

              // Upload the image to R2 storage
              const uploadResult = await uploadReferenceImageAction({
                dataUrl: imageUrl,
                roomName: roomName || 'default-room',
              });

              if (uploadResult.success && uploadResult.url) {
                console.log('[useReferenceImages] Successfully uploaded image to R2:', uploadResult.url);
                finalImageUrl = uploadResult.url;
              } else {
                console.error('[useReferenceImages] Failed to upload image to R2:', uploadResult.error);
                // Continue with the original URL as fallback
              }
            } catch (uploadError) {
              console.error('[useReferenceImages] Error uploading image to R2:', uploadError);
              // Continue with the original URL as fallback
            }
          }

          // Add to database
          await createCanvasReferenceAction({
            whiteboardId,
            thumbUrl: finalImageUrl,
          });

          // Get updated list for socket emission
          const updatedImages = [...referenceImages, finalImageUrl];

          // Debug logs
          console.log(
            '[REF-DEBUG] About to emit, isConnected =',
            isConnected,
            ' roomName =',
            roomName,
            ' imgs =',
            updatedImages.length
          );

          // Send the updated list to the server
          if (isConnected && roomName && roomName.startsWith('voice-agent-')) {
            socket.emit('setReferenceImages', {
              roomName,
              images: updatedImages,
            });

            console.log('[REF-DEBUG] EMITTED setReferenceImages');
            console.log(
              `[useReferenceImages] Added reference image and sent ${updatedImages.length} images to server for room ${roomName}`
            );
          }

          return true;
        } catch (error) {
          console.error('[useReferenceImages] Error adding reference image:', error);
          return false;
        }
      }

      return false;
    },
    [
      isConnected,
      roomName,
      socket,
      uploadReferenceImageAction,
      whiteboardId,
      canvasReferences,
      createCanvasReferenceAction,
    ]
  );

  // Function to remove a reference image
  const removeReferenceImage = useCallback(
    async (imageUrl: string) => {
      if (!whiteboardId) {
        console.warn('[useReferenceImages] No whiteboardId provided, cannot remove reference image');
        return false;
      }

      // Get current reference images from database
      const currentReferences = canvasReferences || [];
      const referenceToRemove = currentReferences.find((ref: any) => ref.thumbUrl === imageUrl);

      if (referenceToRemove) {
        try {
          // Remove from database
          await deleteCanvasReferenceAction({
            id: referenceToRemove.id,
          });

          // Get updated list for socket emission
          const updatedImages = currentReferences
            .filter((ref: any) => ref.thumbUrl !== imageUrl)
            .map((ref: any) => ref.thumbUrl);

          // Send the updated list to the server
          if (isConnected && roomName && roomName.startsWith('voice-agent-')) {
            socket.emit('setReferenceImages', {
              roomName,
              images: updatedImages,
            });

            console.log(
              `[useReferenceImages] Removed reference image and sent ${updatedImages.length} images to server for room ${roomName}`
            );
          }

          return true;
        } catch (error) {
          console.error('[useReferenceImages] Error removing reference image:', error);
          return false;
        }
      }

      return false;
    },
    [isConnected, roomName, socket, whiteboardId, canvasReferences, deleteCanvasReferenceAction]
  );

  // Function to clear all reference images
  const clearReferenceImages = useCallback(async () => {
    if (!whiteboardId) {
      console.warn('[useReferenceImages] No whiteboardId provided, cannot clear reference images');
      return;
    }

    console.log(`[useReferenceImages] Clearing all reference images for room ${roomName}`);

    try {
      // Get current references and delete them all
      const currentReferences = canvasReferences || [];

      // Delete all references from database
      await Promise.all(currentReferences.map((ref: any) => deleteCanvasReferenceAction({ id: ref.id })));

      // Send the updated list to the server
      if (isConnected && roomName && roomName.startsWith('voice-agent-')) {
        socket.emit('setReferenceImages', {
          roomName,
          images: [],
        });

        console.log(`[useReferenceImages] Cleared all reference images for room ${roomName}`);
      } else {
        console.log(`[useReferenceImages] Socket not connected, only cleared database`);
      }
    } catch (error) {
      console.error('[useReferenceImages] Error clearing reference images:', error);
    }

    // Also clear for all other rooms we might have stored
    // Commented out to prevent accidental clearing during development
    // if (isConnected) {
    //   socket.emit('clearAllReferenceImages');
    //   console.log(`[useReferenceImages] Requested to clear all reference images on server`);
    // }
  }, [isConnected, roomName, socket, whiteboardId, canvasReferences, deleteCanvasReferenceAction]);

  // Function to get reference images
  const getReferenceImages = useCallback(() => {
    if (!canvasReferences) return [];
    return canvasReferences.map((ref: any) => ref.thumbUrl);
  }, [canvasReferences]);

  // Function to reorder reference images
  const reorderReferenceImages = useCallback(
    (newOrder: string[]) => {
      try {
        // Note: For reordering, we would need to update the database with new order
        // This is a complex operation that might require adding an order field to the schema
        // For now, we'll just emit the socket event
        console.warn('[useReferenceImages] Reordering not fully implemented for database storage');

        // Send the updated list to the server
        if (isConnected && roomName && roomName.startsWith('voice-agent-')) {
          socket.emit('setReferenceImages', {
            roomName,
            images: newOrder,
          });

          console.log(
            `[useReferenceImages] Reordered reference images and sent ${newOrder.length} images to server for room ${roomName}`
          );
        }

        return true;
      } catch (error) {
        console.error('[useReferenceImages] Error reordering reference images:', error);
        return false;
      }
    },
    [isConnected, roomName, socket]
  );

  // Function to check if an image is a reference image
  const isReferenceImage = useCallback(
    (imageUrl: string) => {
      try {
        if (!canvasReferences) return false;
        const referenceImages = canvasReferences.map((ref: any) => ref.thumbUrl);
        return referenceImages.includes(imageUrl);
      } catch (error) {
        console.error('[useReferenceImages] Error checking if image is a reference:', error);
        return false;
      }
    },
    [canvasReferences]
  );

  // Return the functions
  return {
    addReferenceImage,
    removeReferenceImage,
    clearReferenceImages,
    getReferenceImages,
    reorderReferenceImages,
    isReferenceImage,
    isLoading,
  };
};
