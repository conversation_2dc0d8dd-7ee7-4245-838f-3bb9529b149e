import React, { useEffect } from 'react';
import { createConceptCard, getModelProduct } from 'wasp/client/operations';
import { v4 as uuid } from 'uuid';
import { useReferenceImages } from '../hooks/useReferenceImages';
import { useVoiceAgentRoom } from '../../../hooks/useVoiceAgentRoom';

interface ConceptCardCreatorProps {
  userId?: number;
  modelId?: string;
}

interface CardRequestDetail {
  id: string;
  prompt: string;
  position: { x: number; y: number };
  productId?: string;
}

interface CreateConceptCardRequestEvent extends CustomEvent {
  detail: string | CardRequestDetail;
}

declare global {
  interface WindowEventMap {
    createConceptCardRequest: CreateConceptCardRequestEvent;
    showLoadingConceptCard: CustomEvent;
    removeLoadingConceptCard: CustomEvent;
  }
}

const ConceptCardCreator: React.FC<ConceptCardCreatorProps> = ({ userId = 1, modelId }) => {
  const roomName = useVoiceAgentRoom();
  const { getReferenceImages } = useReferenceImages(roomName, modelId);

  // Listen for createConceptCardRequest events
  useEffect(() => {
    const handleCreateConceptCardRequest = async (event: CreateConceptCardRequestEvent) => {
      const detail = event.detail;

      try {
        // Handle both string and object details
        let request: string;
        let position = { x: 100, y: 100 }; // Default position
        let cardId = `loading-${uuid()}`; // Default ID
        let productId: string | undefined;

        if (typeof detail === 'string') {
          // Legacy format: just a string request
          request = detail;
          console.log('[ConceptCardCreator] Creating concept card for request:', request);
        } else {
          // New format: object with id, prompt, and position
          request = detail.prompt;
          position = detail.position;
          cardId = detail.id;
          productId = detail.productId;
          console.log('[ConceptCardCreator] Creating concept card for request:', request, 'at position:', position, 'with productId:', productId);
        }

        // Generate a temporary ID for the loading card
        const tempId = cardId;

        // Show loading card immediately with a fixed message
        const loadingCardEvent = new CustomEvent('showLoadingConceptCard', {
          detail: {
            id: tempId,
            position: position,
            requestText: "Creating your concept..." // Fixed message instead of actual request
          }
        });
        window.dispatchEvent(loadingCardEvent);
        console.log('[ConceptCardCreator] Dispatched loading card event with ID:', tempId);

        // Get all reference images from the ReferenceBar
        let referenceImages = getReferenceImages();

        // If we have a productId, try to get the product reference image
        if (productId) {
          try {
            // Fetch the product to get its reference_file_id
            const product = await getModelProduct({ modelId: productId });

            if (product && product.reference_file_id) {
              console.log(`[ConceptCardCreator] Found product reference image: ${product.reference_file_id}`);

              // Check if the reference image is already in the array
              if (!referenceImages.includes(product.reference_file_id)) {
                // Add the product reference image to the beginning of the array
                referenceImages = [product.reference_file_id, ...referenceImages];
                console.log(`[ConceptCardCreator] Added product reference image to reference images`);
              }
            }
          } catch (productError) {
            console.error('[ConceptCardCreator] Error fetching product reference image:', productError);
          }
        }

        console.log(`[ConceptCardCreator] Passing ${referenceImages.length} reference images for concept card creation`);

        // Call the Wasp action to create a concept card
        await createConceptCard({
          request,
          userId,
          loadingCardId: tempId, // Pass the loading card ID to replace it later
          productId, // Include the product ID if available
          referenceImages // Pass all reference images with product reference if available
        });
      } catch (error) {
        console.error('[ConceptCardCreator] Error creating concept card:', error);

        // If there's an error, remove the loading card
        const removeLoadingCardEvent = new CustomEvent('removeLoadingConceptCard', {
          detail: {
            id: typeof detail === 'string'
              ? `loading-${detail.substring(0, 10)}`
              : detail.id
          }
        });
        window.dispatchEvent(removeLoadingCardEvent);
      }
    };

    // Add event listener
    window.addEventListener('createConceptCardRequest', handleCreateConceptCardRequest);

    // Clean up
    return () => {
      window.removeEventListener('createConceptCardRequest', handleCreateConceptCardRequest);
    };
  }, [userId, getReferenceImages]);

  return null; // This component doesn't render anything
};

export default ConceptCardCreator;
