import React, { useCallback, useEffect, useState } from 'react';
import { HandTool, SelectTool } from './tools';
import useCanvasStore, {
  addHtmlElement,
  addImage,
  addText,
  deleteSelected,
  handleClear,
  handleRedo,
  handleUndo,
  setActiveTool,
  setGenericPlaceholders,
  setHtmlElements,
  setImages,
  setSelectedIds,
  toggleDebugMode,
  useCanvasShallowSelector,
} from '../store/canvas-store';
import { CustomEventDetail, ECanvasTool } from '../types';
import { usePendingTasks, useReferenceImages } from '../hooks';
import { useVoiceAgentRoom } from './../../../hooks/useVoiceAgentRoom';
import { useSocket, useSocketListener } from 'wasp/client/webSocket';
import { useAuth } from 'wasp/client/auth';

const CanvasToolBar = () => {
  const [whiteboardId, setWhiteboardId] = useState('');
  const { canUndo, canRedo } = useCanvasStore();
  const { data: user } = useAuth();
  const isAdmin = user?.isAdmin || false;

  const { activeTool, selectedIds, images, canvasSize, position, scale, genericPlaceholders, htmlElements, debug } =
    useCanvasShallowSelector((state) => ({
      activeTool: state.activeTool,
      selectedIds: state.selectedIds,
      images: state.images,
      canvasSize: state.canvasSize,
      position: state.position,
      scale: state.scale,
      genericPlaceholders: state.genericPlaceholders,
      htmlElements: state.htmlElements,
      debug: state.debug,
    }));

  // Get the room name from our centralized hook
  const roomName = useVoiceAgentRoom();

  // Get the clearReferenceImages function from the useReferenceImages hook
  const { clearReferenceImages } = useReferenceImages(roomName, whiteboardId);

  // Disable history logging to reduce console noise
  // console.log({ history, historyStep })
  // Get socket for real-time sync
  const { socket, isConnected } = useSocket();

  // TODO: need to optimize
  // TODO: this is just here since this is the least component that need optimization but need to move or refactor
  const { pendingTasks, handleAddPendingImage } = usePendingTasks(images, setImages);

  useEffect(() => {
    setWhiteboardId(localStorage.getItem('selectedModelId') ?? '');
  }, []);

  // Handle events to listen for images from gallery
  useEffect(() => {
    const handleAttachImageToCanvas = (event: Event) => {
      // Cast the event to CustomEvent to access the detail property
      const customEvent = event as CustomEvent<CustomEventDetail>;
      const { imageUrl, taskId, isPending, x, y, width, height, status, progress } = customEvent.detail;

      console.log('[KonvaCanvas] Received attachImageToCanvas event:', customEvent.detail);

      // If this is a pending image, handle it separately by dispatching a pending image event
      // This ensures we use the proper event type that only the pending image handler will process
      if (isPending && taskId) {
        console.log('[KonvaCanvas] Redirecting to pending image handler for task:', taskId);

        // Create a new event specifically for pending images
        const pendingEvent = new CustomEvent('attachPendingImageToCanvas', {
          detail: {
            taskId,
            isPending: true,
            x: x || canvasSize.width / 4 / scale,
            y: y || canvasSize.height / 4 / scale,
            width: width || 300,
            height: height || 200,
            status: status || 'IN_QUEUE',
            progress: 0,
          },
        });

        // Dispatch the pending image event
        console.log('[KonvaCanvas] Dispatching attachPendingImageToCanvas event for task:', taskId);
        window.dispatchEvent(pendingEvent);

        // Also directly call handleAddPendingImage as a fallback
        if (handleAddPendingImage) {
          console.log('[KonvaCanvas] Also directly calling handleAddPendingImage as fallback for task:', taskId);
          handleAddPendingImage(
            taskId,
            x || canvasSize.width / 4 / scale,
            y || canvasSize.height / 4 / scale,
            width || 300,
            height || 200
          );
        }

        return;
      }

      // Otherwise, add a regular image
      if (imageUrl && typeof imageUrl === 'string') {
        // Check if we already have an image with this taskId (to prevent duplicates)
        if (taskId && images.some((img) => img.taskId === taskId)) {
          console.log('[KonvaCanvas] Image for taskId already exists, not adding duplicate:', taskId);
          return;
        }

        // Calculate center position if not provided
        const centerX = x !== undefined ? x : (canvasSize.width / 2 - position.x) / scale;
        const centerY = y !== undefined ? y : (canvasSize.height / 2 - position.y) / scale;
        const imgWidth = width || 300;
        const imgHeight = height || 200;

        // Generate a unique ID for the image
        const newImageId = addImage();

        // Create a new image object
        const newImage = {
          id: newImageId,
          src: imageUrl,
          x: centerX - imgWidth / 2,
          y: centerY - imgHeight / 2,
          width: imgWidth,
          height: imgHeight,
          taskId,
        };

        // Add to images array
        setImages({ images: [...images, newImage] });

        // Select the new image
        setSelectedIds([newImageId]);
        setActiveTool('select');
      }
    };

    // Define a handler for the attachImage event (used by voice agent)
    const handleAttachImage = (event: Event) => {
      const customEvent = event as CustomEvent<{
        id: string;
        src: string;
        x: number;
        y: number;
        width: number;
        height: number;
        taskId?: string;
        imageIndex?: number;
        totalImages?: number;
      }>;

      console.log('[KonvaCanvas] Received attachImage event:', customEvent.detail);

      // Extract details from the event
      const { id, src, x, y, width, height, taskId, imageIndex, totalImages } = customEvent.detail;

      // Check if this is a pending image (content is 'pending')
      const isPending = src === 'pending';

      // Generate a unique ID for this image
      // If this is part of a multi-image generation, include the image index in the ID
      let newImageId = id;

      // If we have imageIndex and totalImages, this is part of a multi-image generation
      if (imageIndex !== undefined && totalImages && totalImages > 1) {
        // Create a unique ID that includes the image index
        newImageId = `${id}-${imageIndex}`;
        console.log(
          `[KonvaCanvas] Using unique image ID for multi-image generation: ${newImageId} (index ${imageIndex} of ${totalImages})`
        );
      } else {
        console.log(`[KonvaCanvas] Using image ID: ${newImageId}`);
      }

      if (isPending) {
        console.log('[KonvaCanvas] Adding pending image with ID:', newImageId);
        // Add a pending image with a loading indicator

        // Log the ID we're using to help with debugging
        console.log('[KonvaCanvas] Using image ID for pending image:', newImageId);

        const updatedImages = [
          ...images,
          {
            id: newImageId,
            src: '/loading-placeholder.png',
            x: x || canvasSize.width / 4 / scale,
            y: y || canvasSize.height / 4 / scale,
            width: width || 300,
            height: height || 200,
            taskId, // Store the taskId for reference
            imageIndex, // Store the image index for reference
            totalImages, // Store the total images for reference
          },
        ];
        // Add the image to the images array
        setImages({ images: updatedImages });

        // Select the new image
        setSelectedIds([newImageId]);
        setActiveTool('select');
      } else {
        console.log('[KonvaCanvas] Adding image with ID:', newImageId);
        // Add a regular image

        // Log the ID we're using to help with debugging
        console.log('[KonvaCanvas] Using image ID for regular image:', newImageId);

        // Check if we already have an image with this ID
        const existingImageIndex = images.findIndex((img) => img.id === newImageId);

        if (existingImageIndex >= 0) {
          console.log(`[KonvaCanvas] Image with ID ${newImageId} already exists, updating instead of adding new`);

          // Update the existing image instead of adding a new one
          const updatedImages = [...images];
          updatedImages[existingImageIndex] = {
            ...updatedImages[existingImageIndex],
            src,
            x: x || updatedImages[existingImageIndex].x,
            y: y || updatedImages[existingImageIndex].y,
            width: width || updatedImages[existingImageIndex].width,
            height: height || updatedImages[existingImageIndex].height,
            taskId, // Update the taskId
            imageIndex, // Update the image index
            totalImages, // Update the total images
          };

          // Update the images array
          setImages({ images: updatedImages });

          // Select the updated image
          setSelectedIds([newImageId]);
          setActiveTool('select');
        } else {
          // Add a new image
          const updatedImages = [
            ...images,
            {
              id: newImageId,
              src,
              x: x || canvasSize.width / 4 / scale,
              y: y || canvasSize.height / 4 / scale,
              width: width || 300,
              height: height || 200,
              taskId, // Store the taskId for reference
              imageIndex, // Store the image index for reference
              totalImages, // Store the total images for reference
            },
          ];
          // Add the image to the images array
          setImages({ images: updatedImages });

          // Select the new image
          setSelectedIds([newImageId]);
          setActiveTool('select');
        }
      }
    };

    // Only listen on window to avoid duplicate event handling
    window.addEventListener('attachImageToCanvas', handleAttachImageToCanvas);
    window.addEventListener('attachImage', handleAttachImage as EventListener);

    return () => {
      window.removeEventListener('attachImageToCanvas', handleAttachImageToCanvas);
      window.removeEventListener('attachImage', handleAttachImage as EventListener);
    };
  }, [addImage, images, position, scale, setImages, setSelectedIds, canvasSize, handleAddPendingImage]);

  const handleHtmlStreamUpdate = useCallback(
    (data: any) => {
      const { taskId, elementId, html, partialHtml, fragment, content, progress, isComplete } = data;
      console.log(`[KonvaCanvas] Received HTML stream update for task ${taskId}`);

      // Use any available HTML content property
      const htmlContent = html ?? partialHtml ?? fragment ?? content ?? '';

      if (!htmlContent) {
        console.log(`[KonvaCanvas] No HTML content in update for task ${taskId}`);
        return;
      }

      // Log the HTML content length and preview
      console.log(`[KonvaCanvas] HTML content length: ${htmlContent.length}`);
      console.log(`[KonvaCanvas] HTML content preview: ${htmlContent.substring(0, 100)}...`);

      // Check if we already have an HTML element for this task
      const existingHtmlElement = htmlElements.find((el) => el.taskId === taskId);

      if (!existingHtmlElement) {
        // Find the generic placeholder with this task ID
        const genericPlaceholder = genericPlaceholders.find((p) => p.taskId === taskId);

        if (genericPlaceholder) {
          // Create a new HTML element based on the generic placeholder
          console.log(`[KonvaCanvas] Creating new HTML element for task ${taskId}`);

          try {
            // Generate a consistent ID for the HTML element - DO NOT include timestamp to avoid duplicates
            const htmlId = `html-${taskId}`;
            console.log(`[KonvaCanvas] Generated HTML element ID: ${htmlId}`);

            // Check if an element with this ID already exists
            const elementWithSameId = htmlElements.find((el) => el.id === htmlId);
            if (elementWithSameId) {
              console.log(`[KonvaCanvas] Element with ID ${htmlId} already exists, updating content`);
              // Update the existing element
              const updatedElements = htmlElements.map((el) => {
                if (el.id === htmlId) {
                  return {
                    ...el,
                    html: htmlContent,
                  };
                }
                return el;
              });
              // Find the updated element to sync
              const elementToSync = updatedElements.find((el) => el.id === htmlId);
              setHtmlElements({
                htmlElements: updatedElements,
                socket,
                whiteboardId,
                htmlElementToSync: elementToSync,
              });
              return;
            }

            // Add the HTML element to the canvas
            addHtmlElement(partialHtml, htmlId, socket, whiteboardId);
            console.log(`[KonvaCanvas] HTML element added with ID: ${htmlId}`);

            // Update the HTML element with the correct position and size **using functional update to avoid stale state**
            {
              // Map over the freshest state to prevent stale closure
              const mapped = useCanvasStore.getState().htmlElements.map((el) => {
                if (el.id === htmlId) {
                  return {
                    ...el,
                    taskId,
                    x: genericPlaceholder.x,
                    y: genericPlaceholder.y,
                    width: 600,
                    height: 800,
                    backgroundColor: 'white',
                  } as typeof el;
                }
                return el;
              });

              const elementUpdated = mapped.some((el) => el.id === htmlId);
              console.log(`[KonvaCanvas] HTML element with ID ${htmlId} was updated: ${elementUpdated}`);

              // Find the updated element to sync
              const elementToSync = mapped.find((el) => el.id === htmlId);
              setHtmlElements({
                htmlElements: mapped,
                socket,
                whiteboardId,
                htmlElementToSync: elementToSync,
              });
            }

            // Verify the HTML element was added to the store
            setTimeout(() => {
              const currentHtmlElements = htmlElements;
              console.log('[KonvaCanvas] Current HTML elements count:', currentHtmlElements.length);
              console.log(
                '[KonvaCanvas] Current HTML element IDs:',
                currentHtmlElements.map((el) => el.id)
              );
              const elementAdded = currentHtmlElements.some((el) => el.id === htmlId);
              console.log('[KonvaCanvas] HTML element with ID', htmlId, 'was added:', elementAdded);

              // If the element wasn't added, try again with the same ID
              if (!elementAdded) {
                console.log('[KonvaCanvas] HTML element was not added, trying again with the same ID');
                const retryHtmlId = addHtmlElement(partialHtml, htmlId, socket, whiteboardId);
                console.log('[KonvaCanvas] Retry: HTML element added with ID:', retryHtmlId);
              }

              // Remove the generic placeholder only after we've verified the HTML element was added
              if (elementAdded) {
                console.log(`[KonvaCanvas] Removing generic placeholder for task ${taskId}`);
                const filteredPlaceholders = genericPlaceholders.filter((p) => p.id !== genericPlaceholder.id);
                console.log(`[KonvaCanvas] Generic placeholders count after removal: ${filteredPlaceholders.length}`);
                setGenericPlaceholders(filteredPlaceholders);
              }
            }, 500);
          } catch (error) {
            console.error(`[KonvaCanvas] Error creating HTML element:`, error);
          }
        } else {
          console.log(`[KonvaCanvas] No generic placeholder found for task ${taskId}, creating HTML element directly`);

          // Create a new HTML element directly
          try {
            // Generate a consistent ID for the HTML element
            const htmlId = `html-${taskId}`;
            console.log(`[KonvaCanvas] Generated HTML element ID: ${htmlId}`);

            // Check if an element with this ID already exists
            const elementWithSameId = htmlElements.find((el) => el.id === htmlId);
            if (elementWithSameId) {
              console.log(`[KonvaCanvas] Element with ID ${htmlId} already exists, updating content`);
              // Update the existing element
              const updatedElements = htmlElements.map((el) => {
                if (el.id === htmlId) {
                  return {
                    ...el,
                    html: htmlContent,
                  };
                }
                return el;
              });
              setHtmlElements({ htmlElements: updatedElements });
              return;
            }

            // Add the HTML element to the canvas
            const newHtmlId = addHtmlElement(htmlContent, htmlId);
            console.log(`[KonvaCanvas] HTML element added with ID: ${newHtmlId}`);

            // Position the element in the center of the canvas
            const x = window.innerWidth / 4;
            const y = window.innerHeight / 4;
            const width = 600;
            const height = 800;

            // Update the HTML element with position and size
            const mapped = useCanvasStore.getState().htmlElements.map((el) => {
              if (el.id === newHtmlId) {
                return {
                  ...el,
                  taskId,
                  x,
                  y,
                  width,
                  height,
                  backgroundColor: 'white',
                } as typeof el;
              }
              return el;
            });

            setHtmlElements({ htmlElements: mapped });
            console.log(`[KonvaCanvas] HTML element positioned at x:${x}, y:${y}`);
          } catch (error) {
            console.error(`[KonvaCanvas] Error creating HTML element:`, error);
          }
        }
      } else {
        // Update the existing HTML element with the new content
        console.log(`[KonvaCanvas] Updating existing HTML element for task ${taskId}`);

        const updatedElements = htmlElements.map((el) => {
          if (el.taskId === taskId) {
            console.log(`[KonvaCanvas] Updating HTML content for element with ID: ${el.id}`);
            return {
              ...el,
              html: htmlContent,
            };
          }
          return el;
        });

        // Find the updated element to sync
        const elementToSync = updatedElements.find((el) => el.taskId === taskId);
        setHtmlElements({ htmlElements: updatedElements, socket, whiteboardId, htmlElementToSync: elementToSync });
      }

      // If the HTML is complete, update the progress
      if (isComplete) {
        console.log(`[KonvaCanvas] HTML content complete for task ${taskId}`);

        // Verify that the HTML element exists
        setTimeout(() => {
          const currentHtmlElement = htmlElements.find((el) => el.taskId === taskId);
          if (currentHtmlElement) {
            console.log(`[KonvaCanvas] HTML element for task ${taskId} exists with ID: ${currentHtmlElement.id}`);
          } else {
            console.log(`[KonvaCanvas] HTML element for task ${taskId} does not exist, creating a final element`);

            // Create a final HTML element if it doesn't exist - use consistent ID
            const finalHtmlId = `html-${taskId}`;
            try {
              // Check if an element with this ID already exists
              const elementWithSameId = htmlElements.find((el) => el.id === finalHtmlId);
              if (elementWithSameId) {
                console.log(`[KonvaCanvas] Element with ID ${finalHtmlId} already exists, updating content`);
                // Update the existing element
                const updatedElements = htmlElements.map((el) => {
                  if (el.id === finalHtmlId) {
                    return {
                      ...el,
                      html: htmlContent,
                    };
                  }
                  return el;
                });
                // Find the updated element to sync
                const elementToSync = updatedElements.find((el) => el.id === finalHtmlId);
                setHtmlElements({
                  htmlElements: updatedElements,
                  socket,
                  whiteboardId,
                  htmlElementToSync: elementToSync,
                });
              } else {
                // Add a new HTML element
                addHtmlElement(partialHtml, finalHtmlId, socket, whiteboardId);
                console.log(`[KonvaCanvas] Final HTML element added with ID: ${finalHtmlId}`);
              }
            } catch (error) {
              console.error(`[KonvaCanvas] Error creating final HTML element:`, error);
            }
          }
        }, 100);
      }
    },
    [htmlElements, genericPlaceholders, addHtmlElement, setHtmlElements, setGenericPlaceholders]
  );

  // Handler for voice agent canvas updates with HTML content
  const handleVoiceAgentCanvasUpdate = useCallback(
    (data: any) => {
      // Check if this is an HTML update
      if ((data.elementType === 'html' || data.type === 'html') && (data.html || data.content) && data.taskId) {
        console.log(`[KonvaCanvas] Received voice agent canvas update with HTML content for task ${data.taskId}`);

        // Get the HTML content from either html or content property
        const htmlContent = data.html || data.content;

        // Log the HTML content length and preview
        console.log(`[KonvaCanvas] HTML content length: ${htmlContent.length}`);
        console.log(`[KonvaCanvas] HTML content preview: ${htmlContent.substring(0, 100)}...`);

        // Check if we already have an HTML element for this task
        const existingHtmlElement = htmlElements.find((el) => el.taskId === data.taskId);

        if (!existingHtmlElement) {
          // Find the generic placeholder with this task ID
          const genericPlaceholder = genericPlaceholders.find((p) => p.taskId === data.taskId);

          if (genericPlaceholder) {
            // Create a new HTML element based on the generic placeholder
            console.log(
              `[KonvaCanvas] Creating new HTML element for task ${data.taskId} from voice agent canvas update`
            );

            try {
              // Generate a consistent ID for the HTML element - DO NOT include timestamp to avoid duplicates
              const htmlId = `html-${data.taskId}`;
              console.log(`[KonvaCanvas] Generated HTML element ID: ${htmlId}`);

              // Check if an element with this ID already exists
              const elementWithSameId = htmlElements.find((el) => el.id === htmlId);
              if (elementWithSameId) {
                console.log(`[KonvaCanvas] Element with ID ${htmlId} already exists, updating content`);
                // Update the existing element
                const updatedElements = htmlElements.map((el) => {
                  if (el.id === htmlId) {
                    return {
                      ...el,
                      html: htmlContent,
                    };
                  }
                  return el;
                });
                // Find the updated element to sync
                const elementToSync = updatedElements.find((el) => el.id === htmlId);
                setHtmlElements({
                  htmlElements: updatedElements,
                  socket,
                  whiteboardId,
                  htmlElementToSync: elementToSync,
                });
                return;
              }

              // Add the HTML element to the canvas
              addHtmlElement(htmlContent, htmlId, socket, whiteboardId);
              console.log(`[KonvaCanvas] HTML element added with ID: ${htmlId}`);

              // Update the HTML element with the correct position and size **using functional update to avoid stale state**
              {
                const mapped = useCanvasStore.getState().htmlElements.map((el) => {
                  if (el.id === htmlId) {
                    return {
                      ...el,
                      taskId: data.taskId,
                      x: genericPlaceholder.x,
                      y: genericPlaceholder.y,
                      width: 600,
                      height: 800,
                      backgroundColor: 'white',
                    } as typeof el;
                  }
                  return el;
                });

                const elementUpdated = mapped.some((el) => el.id === htmlId);
                console.log(`[KonvaCanvas] HTML element with ID ${htmlId} was updated: ${elementUpdated}`);

                // Find the updated element to sync
                const elementToSync = mapped.find((el) => el.id === htmlId);
                setHtmlElements({
                  htmlElements: mapped,
                  socket,
                  whiteboardId,
                  htmlElementToSync: elementToSync,
                });
              }

              // Verify the HTML element was added to the store
              setTimeout(() => {
                const currentHtmlElements = htmlElements;
                console.log('[KonvaCanvas] Current HTML elements count:', currentHtmlElements.length);
                console.log(
                  '[KonvaCanvas] Current HTML element IDs:',
                  currentHtmlElements.map((el) => el.id)
                );
                const elementAdded = currentHtmlElements.some((el) => el.id === htmlId);
                console.log('[KonvaCanvas] HTML element with ID', htmlId, 'was added:', elementAdded);

                // If the element wasn't added, try again with the same ID
                if (!elementAdded) {
                  console.log('[KonvaCanvas] HTML element was not added, trying again with the same ID');
                  const retryHtmlId = addHtmlElement(htmlContent, htmlId, socket, whiteboardId);
                  console.log('[KonvaCanvas] Retry: HTML element added with ID:', retryHtmlId);
                }

                // Remove the generic placeholder only after we've verified the HTML element was added
                if (elementAdded) {
                  console.log(`[KonvaCanvas] Removing generic placeholder for task ${data.taskId}`);
                  const filteredPlaceholders = genericPlaceholders.filter((p) => p.id !== genericPlaceholder.id);
                  console.log(`[KonvaCanvas] Generic placeholders count after removal: ${filteredPlaceholders.length}`);
                  setGenericPlaceholders(filteredPlaceholders);
                }
              }, 500);
            } catch (error) {
              console.error(`[KonvaCanvas] Error creating HTML element:`, error);
            }
          } else {
            console.log(
              `[KonvaCanvas] No generic placeholder found for task ${data.taskId}, creating HTML element directly`
            );

            // Create a new HTML element directly
            try {
              // Generate a consistent ID for the HTML element
              const htmlId = `html-${data.taskId}`;
              console.log(`[KonvaCanvas] Generated HTML element ID: ${htmlId}`);

              // Check if an element with this ID already exists
              const elementWithSameId = htmlElements.find((el) => el.id === htmlId);
              if (elementWithSameId) {
                console.log(`[KonvaCanvas] Element with ID ${htmlId} already exists, updating content`);
                // Update the existing element
                const updatedElements = htmlElements.map((el) => {
                  if (el.id === htmlId) {
                    return {
                      ...el,
                      html: htmlContent,
                    };
                  }
                  return el;
                });
                setHtmlElements({ htmlElements: updatedElements });
                return;
              }

              // Add the HTML element to the canvas
              const newHtmlId = addHtmlElement(htmlContent, htmlId);
              console.log(`[KonvaCanvas] HTML element added with ID: ${newHtmlId}`);

              // Position the element in the center of the canvas
              const x = window.innerWidth / 4;
              const y = window.innerHeight / 4;
              const width = 600;
              const height = 800;

              // Update the HTML element with position and size
              const mapped = useCanvasStore.getState().htmlElements.map((el) => {
                if (el.id === newHtmlId) {
                  return {
                    ...el,
                    taskId: data.taskId,
                    x,
                    y,
                    width,
                    height,
                    backgroundColor: 'white',
                  } as typeof el;
                }
                return el;
              });

              setHtmlElements({ htmlElements: mapped });
              console.log(`[KonvaCanvas] HTML element positioned at x:${x}, y:${y}`);
            } catch (error) {
              console.error(`[KonvaCanvas] Error creating HTML element:`, error);
            }
          }
        } else {
          // Update the existing HTML element with the new content
          console.log(`[KonvaCanvas] Updating existing HTML element for task ${data.taskId}`);

          const updatedElements = htmlElements.map((el) => {
            if (el.taskId === data.taskId) {
              console.log(`[KonvaCanvas] Updating HTML content for element with ID: ${el.id}`);
              return {
                ...el,
                html: htmlContent,
              };
            }
            return el;
          });

          // Find the updated element to sync
          const elementToSync = updatedElements.find((el) => el.taskId === data.taskId);
          setHtmlElements({
            htmlElements: updatedElements,
            socket,
            whiteboardId,
            htmlElementToSync: elementToSync,
          });
        }
      }
    },
    [htmlElements, genericPlaceholders, addHtmlElement, setHtmlElements, setGenericPlaceholders]
  );

  // Listen for htmlStreamUpdate events using WebSocket
  useSocketListener('htmlStreamUpdate' as any, handleHtmlStreamUpdate);

  // Listen for voiceAgentCanvasUpdate events using WebSocket
  useSocketListener('voiceAgentCanvasUpdate' as any, handleVoiceAgentCanvasUpdate);

  return (
    <div className='canvas-toolbar'>
      <div className={`toolbar-wrapper ${activeTool === ECanvasTool.SELECT ? 'active' : ''}`}>
        <SelectTool active={activeTool === 'select'} onToggle={() => setActiveTool(ECanvasTool.SELECT)} />
      </div>
      <div className={`toolbar-wrapper ${activeTool === ECanvasTool.HAND ? 'active' : ''}`}>
        <HandTool active={activeTool === 'hand'} onToggle={() => setActiveTool(ECanvasTool.HAND)} />
      </div>
      <div className='divider'></div>
      <button onClick={() => addText(socket, whiteboardId)} title='Add Text'>
        <i className='icon'>T</i>
      </button>
      <div className='divider'></div>
      <button
        onClick={() => {
          // Get the current selected IDs
          const { selectedIds } = useCanvasStore.getState();

          if (selectedIds.length === 0) {
            return;
          }

          deleteSelected(socket, whiteboardId);
        }}
        disabled={selectedIds.length === 0}
        title='Delete Selected'
      >
        <i className='icon'>🗑</i>
      </button>
      <div className='divider'></div>
      <button onClick={handleUndo} disabled={!canUndo} title='Undo'>
        <i className='icon'>↩</i>
      </button>
      <button onClick={handleRedo} disabled={!canRedo} title='Redo'>
        <i className='icon'>↪</i>
      </button>
      <div className='divider'></div>
      {/* Debug mode toggle - only visible for admin users */}
      {isAdmin && (
        <>
          <button
            onClick={toggleDebugMode}
            title={debug.enabled ? 'Disable Debug Mode' : 'Enable Debug Mode'}
            className={debug.enabled ? 'active' : ''}
          >
            <i className='icon'>🐞</i>
          </button>
          <div className='divider'></div>
        </>
      )}
      {/* TODO: this should be save in the database */}
      {/* <button onClick={handleSave} title="Save">
                <i className="icon">💾</i>
            </button> */}
      <button
        onClick={() => {
          // Clear the canvas
          handleClear();

          // Also clear reference images
          clearReferenceImages();

          console.log('[CanvasToolBar] Cleared canvas and reference images');

          // Use the enhanced handleClear with socket for real-time sync
          if (socket && isConnected) {
            handleClear(socket, whiteboardId);
          } else {
            handleClear();
          }
        }}
        title='Clear Canvas and Reference Images'
      >
        <i className='icon'>✕</i>
      </button>
    </div>
  );
};

export default CanvasToolBar;
