import React, { forwardRef, ForwardedRef, useEffect, useState, useMemo, useRef } from 'react';
import { Stage, Layer, Rect, Line, Transformer } from 'react-konva';
import '../KonvaCanvas.css';
// Import MaskBoundary component with named import
import { MaskBoundary } from './MaskBoundary';
import {
  addPointToMask,
  handleDragEnd,
  handleDragStart,
  handleSelectionClick,
  handleSelectionMouseDown,
  handleSelectionMouseMove,
  handleSelectionMouseUp,
  handleTouchEnd,
  handleTouchMove,
  setMaskIsDrawing,
  setSelectedIds,
  useCanvasShallowSelector,
  useCanvasStore,
} from '../store/canvas-store';
import Konva from 'konva';
import ChatBubble from './ChatBubble';
import KonvaConceptCards from './KonvaConceptCards';
import { ECanvasTool } from '../types';
import { cn } from '../../../lib/utils';
import { useHistoryTracker } from '../hooks/useHistoryTracker';
import { useSocket } from 'wasp/client/webSocket';
import { useTheme } from '../../../context/ThemeContext';
import DebugVisualizer from './DebugVisualizer';
import { DEFAULT_CARD_WIDTH as NEWSLETTER_DEFAULT_WIDTH, DEFAULT_CARD_HEIGHT as NEWSLETTER_DEFAULT_HEIGHT } from './KonvaHtmlDisplayCard'; // Import defaults for positioning
import SceneManager from '../konvaScenes/SceneManager';
import BaseScene from '../konvaScenes/BaseScene';
import { SceneManagerContext } from '../konvaScenes/SceneContext';
import CropScene from '../konvaScenes/CropScene';

// Define the methods/properties you want to expose
// export interface CanvasRef {
//     getStage: () => Konva.Stage | null;
//     exportImage: (config?: { pixelRatio?: number }) => string;
//     centerView: () => void;
//     fitToScreen: () => void;
//     zoomTo: (scale: number) => void;
// }
type KonvaEvent = Konva.KonvaEventObject<MouseEvent | TouchEvent>;

interface CanvasProps {
  className?: string;
  children?: React.ReactNode;
}

const Canvas = forwardRef<Konva.Stage, CanvasProps>(({ className, children }, ref: ForwardedRef<Konva.Stage>) => {
  const stageRef = ref as React.RefObject<Konva.Stage>;
  // Get socket for real-time sync
  const socket = useSocket();

  // Get the current theme mode
  const { isDarkMode } = useTheme();

  const whiteboardId = localStorage.getItem('selectedModelId') || '';

  const { activeTool, selectedIds, scale, position, selection, canvasSize, isDragging, maskDrawing } =
    useCanvasShallowSelector((state) => ({
      activeTool: state.activeTool,
      selectedIds: state.selectedIds,
      scale: state.scale,
      position: state.position,
      selection: state.selection,
      canvasSize: state.canvasSize,
      isDragging: state.isDragging,
      maskDrawing: state.maskDrawing,
    }));

  // State for chat bubble
  const [chatBubble, setChatBubble] = useState({
    isVisible: false,
    position: { x: 0, y: 0 },
    productId: undefined as string | undefined,
  });

  // ===== Transformer for resizable NewsletterApprovalCard =====
  const [transformNode, setTransformNode] = useState<Konva.Node | null>(null);
  const transformerRef = useRef<Konva.Transformer>(null);

  // Keep Transformer in sync with selected node
  useEffect(() => {
    if (transformerRef.current) {
      transformerRef.current.nodes(transformNode ? [transformNode] : []);
      transformerRef.current.getLayer()?.batchDraw();
    }
  }, [transformNode]);

  // Track history changes
  useHistoryTracker();

  // Add document click handler to dismiss chat bubble when clicking outside
  useEffect(() => {
    const handleDocumentClick = (e: MouseEvent) => {
      if (chatBubble.isVisible) {
        // Check if the click is outside the chat bubble
        const bubbleElement = document.querySelector('.chat-bubble-container');
        if (bubbleElement && !bubbleElement.contains(e.target as Node)) {
          // Don't close if the click is on the canvas (we handle that separately)
          const canvasElement = document.querySelector('.konvajs-content');
          if (!canvasElement || !canvasElement.contains(e.target as Node)) {
            handleCloseChatBubble();
          }
        }
      }
    };

    document.addEventListener('click', handleDocumentClick);

    return () => {
      document.removeEventListener('click', handleDocumentClick);
    };
  }, [chatBubble.isVisible]);

  const isDraggable = activeTool === ECanvasTool.HAND || (selectedIds.length === 0 && !selection.visible);

  // ────────────────────────────────────────────────────────────────────────────
  // SceneManager initialisation – run once after Stage instance exists
  // ────────────────────────────────────────────────────────────────────────────

  const [sceneMgr, setSceneMgr] = React.useState<SceneManager | null>(null);

  React.useLayoutEffect(() => {
    if (!stageRef.current || sceneMgr) return;

    const stage = stageRef.current as unknown as Konva.Stage;
    // Create a dedicated layer that sits above the main Layer but below UI layers
    const sceneLayer = new Konva.Layer();
    stage.add(sceneLayer);

    const mgr = new SceneManager(stage);
    sceneLayer.add(mgr);

    // Ensure the default scene is created
    console.log('[Canvas.tsx useLayoutEffect] About to call mgr.goto(BaseScene)');
    mgr.goto(BaseScene as any);
    console.log('[Canvas.tsx useLayoutEffect] Called mgr.goto(BaseScene)');

    const bsInstance = mgr.getScene(BaseScene as any);
    if (bsInstance) {
      console.log('[Canvas.tsx useLayoutEffect] BaseScene instance from mgr.getScene:', {
        name: bsInstance.name(),
        id: bsInstance.id(),
        className: bsInstance.className,
        hasParent: !!bsInstance.getParent(),
        parentIsMgr: bsInstance.getParent() === mgr
      });

      const foundOnMgr = mgr.find('#BaseScene');
      console.log('[Canvas.tsx useLayoutEffect] Result of mgr.find("#BaseScene") on SceneManager children:', foundOnMgr.length > 0 ? { name: foundOnMgr[0].name(), id: foundOnMgr[0].id(), isSameInstanceAsGetScene: foundOnMgr[0] === bsInstance } : 'Not found on manager');

      const foundOnStage = stage.find('#BaseScene');
      console.log('[Canvas.tsx useLayoutEffect] Result of stage.find("#BaseScene") recursively after goto:', foundOnStage.length > 0 ? { name: foundOnStage[0].name(), id: foundOnStage[0].id(), isSameInstanceAsGetScene: foundOnStage[0] === bsInstance } : 'Not found on stage');
    } else {
      console.error('[Canvas.tsx useLayoutEffect] mgr.getScene(BaseScene) returned undefined after goto!');
    }

    setSceneMgr(mgr);
  }, [sceneMgr]);

  // Keyboard shortcuts (Esc = exit crop mode)
  React.useEffect(() => {
    if (!sceneMgr) return;
    const keyHandler = (e: KeyboardEvent) => {
      // Removed 'c' key binding for crop mode to prevent accidental activation
      if (e.key === 'Escape') {
        sceneMgr.goto(BaseScene as any);
      }
    };
    window.addEventListener('keydown', keyHandler);
    return () => window.removeEventListener('keydown', keyHandler);
  }, [sceneMgr]);

  // ────────────────────────────────────────────────────────────────────────────
  // Existing handlers
  // ────────────────────────────────────────────────────────────────────────────

  const handleMouseDown = (e: KonvaEvent) => {
    // Ignore right-clicks (button === 2) to prevent conflict with context menu
    if ('button' in e.evt && e.evt.button === 2) {
      return;
    }

    switch (activeTool) {
      case ECanvasTool.SELECT:
        handleSelectionMouseDown(e);
        break;
      case ECanvasTool.MASK:
        // Handle mask drawing
        if (maskDrawing.targetImageUrl && maskDrawing.targetImageId) {
          // Get pointer position relative to the stage
          const stage = e.target.getStage();
          if (!stage) return;

          const point = stage.getPointerPosition();
          if (!point) return;

          // Convert to image coordinates
          const imagePoint = {
            x: (point.x - position.x) / scale,
            y: (point.y - position.y) / scale,
          };

          // Always get the latest image position from the store
          // This is critical for when the image has been moved after starting mask drawing
          const targetImage = useCanvasStore.getState().images.find((img) => img.id === maskDrawing.targetImageId);

          if (targetImage) {
            // Check if the point is within the image boundaries
            const isWithinImage =
              imagePoint.x >= targetImage.x &&
              imagePoint.x <= targetImage.x + targetImage.width &&
              imagePoint.y >= targetImage.y &&
              imagePoint.y <= targetImage.y + targetImage.height;

            if (isWithinImage) {
              console.log('[Canvas] Mask drawing: Mouse down at', imagePoint);

              // Start drawing
              setMaskIsDrawing(true);

              // Convert to coordinates relative to the image
              // This is critical for proper mask drawing
              const relativePoint = {
                x: imagePoint.x - targetImage.x,
                y: imagePoint.y - targetImage.y,
              };

              console.log('[Canvas] Mask drawing: Image position', { x: targetImage.x, y: targetImage.y });
              console.log('[Canvas] Mask drawing: Image point', imagePoint);

              console.log('[Canvas] Mask drawing: Relative point', relativePoint);

              addPointToMask(relativePoint);
            } else {
              console.log('[Canvas] Mask drawing: Point outside image boundaries');
            }
          } else {
            console.log('[Canvas] Mask drawing: Target image not found');
          }
        }
        break;
      default:
        break;
    }
  };

  const handleMouseMove = (e: KonvaEvent) => {
    switch (activeTool) {
      case ECanvasTool.SELECT:
        handleSelectionMouseMove(e);
        break;
      case ECanvasTool.MASK:
        // Handle mask drawing
        if (maskDrawing.isDrawing && maskDrawing.targetImageUrl && maskDrawing.targetImageId) {
          // Get pointer position relative to the stage
          const stage = e.target.getStage();
          if (!stage) return;

          const point = stage.getPointerPosition();
          if (!point) return;

          // Convert to image coordinates
          const imagePoint = {
            x: (point.x - position.x) / scale,
            y: (point.y - position.y) / scale,
          };

          // Always get the latest image position from the store
          // This is critical for when the image has been moved after starting mask drawing
          const targetImage = useCanvasStore.getState().images.find((img) => img.id === maskDrawing.targetImageId);

          if (targetImage) {
            // Check if the point is within the image boundaries
            const isWithinImage =
              imagePoint.x >= targetImage.x &&
              imagePoint.x <= targetImage.x + targetImage.width &&
              imagePoint.y >= targetImage.y &&
              imagePoint.y <= targetImage.y + targetImage.height;

            if (isWithinImage) {
              // Convert to coordinates relative to the image
              // This is critical for proper mask drawing
              const relativePoint = {
                x: imagePoint.x - targetImage.x,
                y: imagePoint.y - targetImage.y,
              };

              console.log('[Canvas] Mask drawing move: Image position', { x: targetImage.x, y: targetImage.y });
              console.log('[Canvas] Mask drawing move: Relative point', relativePoint);

              // Add point to the mask
              addPointToMask(relativePoint);
            }
          }
        }
        break;
      default:
        break;
    }
  };

  const handleMouseUp = (e: KonvaEvent) => {
    switch (activeTool) {
      case ECanvasTool.SELECT:
        handleSelectionMouseUp(e);
        break;
      case ECanvasTool.MASK:
        // Stop mask drawing
        if (maskDrawing.isDrawing) {
          console.log('[Canvas] Mask drawing: Mouse up, stopping drawing');
          setMaskIsDrawing(false);
        }
        break;
      default:
        break;
    }
  };

  const handleClick = (e: KonvaEvent) => {
    switch (activeTool) {
      case ECanvasTool.SELECT:
        // Pass socket and whiteboardId for real-time sync
        handleSelectionClick(e, socket, whiteboardId);
        break;
      case ECanvasTool.HAND:
        if (e.target === e.target.getStage()) {
          // Pass socket and whiteboardId for real-time sync
          setSelectedIds([], socket, whiteboardId);
        }
        break;

      default:
        break;
    }

    // --- Handle NewsletterApprovalCard selection for resizing ---
    const stage = e.target.getStage();
    if (!stage) return;

    // Traverse up from clicked node to find a group marked as resizable
    let node: Konva.Node | null = e.target as Konva.Node;
    let isResizable = false;
    while (node && node !== stage) {
      if (node.getAttr('isNewsletterCard') || node.getAttr('isHtmlDisplayCard')) {
        isResizable = true;
        break;
      }
      node = node.getParent();
    }

    if (node && isResizable) {
      // Select this card for transformation
      setTransformNode(node);
    } else {
      // Clicked elsewhere, clear selection
      setTransformNode(null);
    }
  };

  // Handle right-click (context menu) event
  const handleContextMenu = (e: Konva.KonvaEventObject<MouseEvent>) => {
    // Prevent default browser context menu
    e.evt.preventDefault();

    // If the target is a Konva.Image, assume BaseScene handled it (or will handle it)
    // and do not show the ChatBubble.
    if (e.target instanceof Konva.Image) {
      // console.log('[Canvas.tsx] Context menu on Konva.Image, letting BaseScene handle.');
      return;
    }

    // ADDED CHECK: If the target is part of an HTMLElement, let HTMLElement.tsx handle it.
    if (e.target.findAncestor('.html-element-konva-group')) {
      console.log('[Canvas.tsx] Context menu on HTMLElement Konva group, letting HTMLElement.tsx handle it.');
      return;
    }

    // Get the pointer position relative to the stage
    const stage = e.target.getStage();
    if (!stage) return;

    const pointer = stage.getPointerPosition();
    if (!pointer) return;

    // Convert stage coordinates to screen coordinates
    const stageContainer = stage.container();
    const containerRect = stageContainer.getBoundingClientRect();

    const screenX = containerRect.left + pointer.x;
    const screenY = containerRect.top + pointer.y;

    // Show chat bubble at pointer position
    setChatBubble({
      isVisible: true,
      position: { x: screenX, y: screenY },
      productId: whiteboardId, // Pass the current whiteboard/product ID
    });
  };

  // Handle left-click on canvas to dismiss chat bubble
  const handleCanvasClick = (e: Konva.KonvaEventObject<MouseEvent>) => {
    // If chat bubble is visible, close it
    if (chatBubble.isVisible) {
      // Only close if the click is on the stage background, not on a specific element
      const clickedOnStage = e.target === e.target.getStage();
      if (clickedOnStage) {
        handleCloseChatBubble();
      }
    }
  };

  // Close chat bubble
  const handleCloseChatBubble = () => {
    setChatBubble({
      ...chatBubble,
      isVisible: false,
    });
  };

  const getContainerClassName = () => {
    const classes = ['canvas-container'];

    if (activeTool === 'hand') {
      classes.push('hand-tool');
      if (isDragging) classes.push('dragging');
    } else {
      if (selection.visible) {
        classes.push('selecting');
      } else if (isDragging) {
        classes.push('dragging');
      } else if (selectedIds.length === 0) {
        classes.push('default');
      }
    }

    return classes.join(' ');
  };

  // Function to determine new card position
  const getNewCardPositionOnCanvas = () => {
    if (stageRef.current) {
      const stage = stageRef.current;
      // Attempt to center the card. Adjust NEWSLETTER_DEFAULT_WIDTH/HEIGHT if using different card type defaults
      const cardWidth = NEWSLETTER_DEFAULT_WIDTH;
      const cardHeight = NEWSLETTER_DEFAULT_HEIGHT;
      return {
        x: (stage.width() / 2 / stage.scaleX()) - (cardWidth / 2) - (stage.x() / stage.scaleX()),
        y: (stage.height() / 2 / stage.scaleY()) - (cardHeight / 2) - (stage.y() / stage.scaleY()),
      };
    }
    // Fallback position if stage is not available (should ideally not happen)
    return { x: 200, y: 150 };
  };

  return (
    <SceneManagerContext.Provider value={sceneMgr}>
    <>
      <Stage
        ref={ref}
        className={cn(getContainerClassName(), className)}
        width={canvasSize.width}
        height={canvasSize.height}
        scaleX={scale}
        scaleY={scale}
        x={position.x}
        y={position.y}
        draggable={isDraggable}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onClick={(e) => {
          handleClick(e);
          handleCanvasClick(e);
        }}
        onTouchStart={handleMouseDown}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onContextMenu={handleContextMenu} // Add right-click handler
        onMouseEnter={() => {
          if (activeTool === ECanvasTool.HAND) {
            document.body.style.cursor = 'grab';
          } else if (activeTool === ECanvasTool.MASK) {
            document.body.style.cursor = 'crosshair';
          } else if (selectedIds.length === 0) {
            document.body.style.cursor = 'crosshair';
          }
        }}
        onMouseLeave={() => {
          document.body.style.cursor = 'default';
        }}
      >
        <Layer>
          {selection.visible && (
            <Rect
              x={selection.x}
              y={selection.y}
              width={selection.width}
              height={selection.height}
              fill={isDarkMode ? 'rgba(158, 165, 129, 0.15)' : 'rgba(158, 165, 129, 0.1)'}
              stroke={isDarkMode ? 'rgba(158, 165, 129, 0.9)' : 'rgba(158, 165, 129, 0.8)'}
              strokeWidth={1 / scale} // Adjust stroke width based on scale
              dash={[4 / scale, 4 / scale]} // Adjust dash based on scale
              perfectDrawEnabled={false} // Disable perfect drawing for performance
              listening={false} // Disable events for the selection rectangle
            />
          )}
          {children}
        </Layer>

        {/* Mask Drawing Layer - only visible when in mask mode */}
        {activeTool === ECanvasTool.MASK && maskDrawing.targetImageId && (
          <Layer>
            {/* Draw lines for the mask */}
            {maskDrawing.points.length >= 2 &&
              (() => {
                const targetImage = useCanvasStore
                  .getState()
                  .images.find((img) => img.id === maskDrawing.targetImageId);

                if (!targetImage) return null;

                const lines: JSX.Element[] = [];

                for (let i = 1; i < maskDrawing.points.length; i++) {
                  const prevPoint = maskDrawing.points[i - 1];
                  const point = maskDrawing.points[i];

                  if (!prevPoint || !point) {
                    console.log('[Canvas] Mask drawing: Skipping line at null separator:', i);
                    continue;
                  }

                  const canvasPrevPoint = {
                    x: prevPoint.x + targetImage.x,
                    y: prevPoint.y + targetImage.y,
                  };

                  const canvasPoint = {
                    x: point.x + targetImage.x,
                    y: point.y + targetImage.y,
                  };

                  lines.push(
                    <Line
                      key={`mask-line-${i}`}
                      points={[canvasPrevPoint.x, canvasPrevPoint.y, canvasPoint.x, canvasPoint.y]}
                      stroke='rgba(255, 255, 255, 0.7)'
                      strokeWidth={maskDrawing.brushSize}
                      lineCap='round'
                      lineJoin='round'
                      opacity={1}
                      globalCompositeOperation={maskDrawing.tool === 'brush' ? 'source-over' : 'destination-out'}
                      perfectDrawEnabled={false}
                      listening={false}
                    />
                  );
                }

                return lines;
              })()}

            {maskDrawing.targetImageId && (
              <MaskBoundary
                targetImageId={maskDrawing.targetImageId}
                scale={scale}
                key={`mask-boundary-${maskDrawing.targetImageId}`}
              />
            )}
          </Layer>
        )}

        {/* Concept Cards Layer */}
        <Layer>
          <KonvaConceptCards
            onAnswer={(id, questionIndex, answer) => {
              console.log(`[Canvas] Answer for card ${id}, question ${questionIndex}: ${answer}`);
            }}
            onGenerate={(id) => {
              console.log(`[Canvas] Generate content for card ${id}`);
            }}
            onRemove={(id) => {
              console.log(`[Canvas] Remove card ${id}`);
            }}
            getNewCardPosition={getNewCardPositionOnCanvas}
            modelId={modelId}
          />
        </Layer>

        {/* Transformer Layer for NewsletterApprovalCards */}
        <Layer>
          <Transformer
            ref={transformerRef}
            boundBoxFunc={(oldBox, newBox) => {
              // Prevent scaling too small
              if (newBox.width < 150 || newBox.height < 100) {
                return oldBox;
              }
              return newBox;
            }}
            keepRatio={false}
            rotationSnaps={[0, 90, 180, 270]}
            borderStroke="#9EA581"
            borderStrokeWidth={2}
            anchorStroke="#9EA581"
            anchorFill="#9EA581"
            anchorStrokeWidth={1}
          />
        </Layer>

        {/* Debug Visualizer Layer */}
        <DebugVisualizer />
      </Stage>

      {/* Chat Bubble */}
      <ChatBubble
        isVisible={chatBubble.isVisible}
        position={chatBubble.position}
        onClose={handleCloseChatBubble}
        productId={chatBubble.productId}
      />
    </>
    </SceneManagerContext.Provider>
  );
});

Canvas.displayName = 'Canvas';

export default Canvas;
