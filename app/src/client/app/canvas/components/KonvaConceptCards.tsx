import React, { useState, useEffect, useCallback } from 'react';
import { Group } from 'react-konva';
import KonvaGreenConceptCard from './KonvaGreenConceptCard';
import KonvaLoadingConceptCard from './KonvaLoadingConceptCard';
import NewsletterApprovalCard from './NewsletterApprovalCard';
import KonvaHtmlDisplayCard from './KonvaHtmlDisplayCard';
import { NewsletterApprovalCardDataType, NewsletterOutline } from '../types/newsletterApprovalTypes';
import { ConceptCardData } from '../types/conceptCard';
import { HtmlDisplayCardDataType } from '../types/htmlDisplayCardTypes';
import { updateConceptCard, generateFromConceptCard, getModelProduct, approveNewsletterOutline } from 'wasp/client/operations';
import { useAuthenticatedSocket } from '../../../hooks/useAuthenticatedSocket';
import { useReferenceImages } from '../hooks/useReferenceImages';
import { useVoiceAgentRoom } from '../../../hooks/useVoiceAgentRoom';
import { useAuth } from 'wasp/client/auth';
import { api } from 'wasp/client/api';
import { UpdateNewsletterCardStatusData } from '../../../../websocket/types';

interface KonvaConceptCardsProps {
  onAnswer: (id: string, questionIndex: number, answer: string) => void;
  onGenerate: (id: string) => void;
  onRemove: (id: string) => void;
  getNewCardPosition: () => { x: number; y: number };
  modelId?: string;
}

// Interface for loading card data
interface LoadingCardData {
  id: string;
  position: { x: number; y: number };
  requestText: string;
}

// Define the structure of the data expected from the 'request_human_input' WebSocket event
// This interface should match what the server is actually emitting for this event.
interface RequestHumanInputEventData {
  taskId: string;
  threadId: string;
  question: string;
  context?: string;
  options?: any;
}

// Updated event data structure for newsletter_outline_ready
interface NewsletterOutlineReadyEventData {
  taskId: string;
  threadId: string;
  outline: NewsletterOutline; // This is the structured { title, sections } object
  clientLoadingCardId?: string; // Added to identify the loading card to replace
}

// Temporary flag to silence noisy logs – set to true when debugging this component
const DEBUG_KONVA_CARDS = false;
const dbg = (...args: any[]) => { if (DEBUG_KONVA_CARDS) console.log('[KonvaConceptCards]', ...args); };

const KonvaConceptCards: React.FC<KonvaConceptCardsProps> = ({
  onAnswer,
  onGenerate,
  onRemove,
  getNewCardPosition,
  modelId
}) => {
  const [cards, setCards] = useState<ConceptCardData[]>([]);
  const [loadingCards, setLoadingCards] = useState<LoadingCardData[]>([]);
  const [newsletterApprovalCards, setNewsletterApprovalCards] = useState<NewsletterApprovalCardDataType[]>([]);
  const [htmlDisplayCards, setHtmlDisplayCards] = useState<HtmlDisplayCardDataType[]>([]);
  const [tasksPendingHtmlGeneration, setTasksPendingHtmlGeneration] = useState<Set<string>>(new Set());

  // Track loading card positions
  const [loadingCardPositions, setLoadingCardPositions] = useState<Record<string, { x: number; y: number }>>({});
  const [selectedCardId, setSelectedCardId] = useState<string | null>(null);
  const { socket, isConnected } = useAuthenticatedSocket();
  const { data: user } = useAuth();
  const roomName = useVoiceAgentRoom();
  const { getReferenceImages } = useReferenceImages(roomName, modelId);
  const [isLoading, setIsLoading] = useState(false);

  // Memoize handlers passed to effects or children if they don't depend on frequently changing state
  const handleCreateConceptCard = useCallback((cardData: ConceptCardData) => {
    dbg('Received createConceptCard event:', cardData);

    // If this card is replacing a loading card, use the loading card's position if available
    let updatedCardData = { ...cardData };

    if (cardData.loadingCardId && typeof cardData.loadingCardId === 'string') {
      // Check if we have a tracked position for this loading card
      const loadingId = cardData.loadingCardId as string; // Cast to string to satisfy TypeScript
      const loadingCardPosition = loadingCardPositions[loadingId];

      if (loadingCardPosition) {
        dbg(`Using tracked position for loading card ${cardData.loadingCardId}:`, loadingCardPosition);
        updatedCardData.position = loadingCardPosition;
      } else {
        // Try to find the loading card in the loadingCards state
        const loadingCard = loadingCards.find(card => card.id === cardData.loadingCardId);

        if (loadingCard) {
          dbg(`Using position from loadingCards for ${cardData.loadingCardId}:`, loadingCard.position);
          updatedCardData.position = loadingCard.position;
        }
      }
    }

    // Add the new card with the updated position
    setCards(prevCards => [
      ...prevCards,
      updatedCardData
    ]);

    // Select the new card
    setSelectedCardId(updatedCardData.id);

    // Remove the loading card if this card is replacing one
    if (cardData.loadingCardId && typeof cardData.loadingCardId === 'string') {
      dbg(`Replacing loading card ${cardData.loadingCardId} with real card ${cardData.id}`);
      setLoadingCards(prev => prev.filter(card => card.id !== cardData.loadingCardId));

      // Also remove the tracked position
      setLoadingCardPositions(prev => {
        const newPositions = { ...prev };
        const loadingId = cardData.loadingCardId as string; // Cast to string to satisfy TypeScript
        if (loadingId in newPositions) {
          delete newPositions[loadingId];
        }
        return newPositions;
      });
    }

    dbg('Created new concept card:', updatedCardData.id);
  }, [cards, loadingCards, loadingCardPositions]);

  const handleUpdateConceptCard = useCallback((updateData: Partial<ConceptCardData>) => {
    dbg('Received updateConceptCard event:', updateData);

    if (!updateData.id) return;

    // Log suggestion chips if present
    if (updateData.suggestionChips && updateData.suggestionChips.length > 0) {
      dbg(`Received ${updateData.suggestionChips.length} suggestion chips:`, updateData.suggestionChips);
    }

    setCards(prevCards =>
      prevCards.map(card =>
        card.id === updateData.id
          ? { ...card, ...updateData }
          : card
      )
    );

    dbg('Updated concept card:', updateData.id);
  }, []);

  const handleUpdateNewsletterCardStatus = useCallback((data: UpdateNewsletterCardStatusData) => {
    dbg('Received updateNewsletterCardStatus:', data);
    if (data.requestType === 'generate_newsletter') {
      if (data.status === 'completed' && data.result?.gmailMockUrl) {
        // Remove the approval card
        let approvalCardPosition = getNewCardPosition(); // Default position

        // Clear loading state for this task FIRST
        setTasksPendingHtmlGeneration(prev => {
          const next = new Set(prev);
          next.delete(data.taskId);
          return next;
        });

        setNewsletterApprovalCards(prevCards => {
          const cardToRemove = prevCards.find(card => card.taskId === data.taskId);
          if (cardToRemove) {
            approvalCardPosition = cardToRemove.position; // Use its position for the new HTML card
          }
          return prevCards.filter(card => card.taskId !== data.taskId);
        });

        dbg(`Newsletter task ${data.taskId} completed. Displaying HTML content.`);

        const newHtmlCard: HtmlDisplayCardDataType = {
          id: `${data.taskId}_html_display`,
          taskId: data.taskId,
          title: data.result.outlineTitle || 'Generated Newsletter',
          iframeUrl: data.result.gmailMockUrl,
          position: approvalCardPosition, // Use the position of the removed approval card or a default
          // Provide inlined HTML URLs so export button can function
          inlinedMobileHtmlUrl: data.result.inlinedMobileHtmlUrl,
          inlinedDesktopHtmlUrl: data.result.inlinedDesktopHtmlUrl,
          // width and height will use defaults in KonvaHtmlDisplayCard
        };
        setHtmlDisplayCards(prev => {
          // Prevent duplicates if event is somehow processed multiple times
          if (prev.some(card => card.id === newHtmlCard.id)) return prev;
          return [...prev, newHtmlCard];
        });
        dbg(`Added HtmlDisplayCard for ${data.taskId}`);

      } else if (data.status === 'failed') {
        setNewsletterApprovalCards(prevCards =>
          prevCards.map(card =>
            card.taskId === data.taskId
              ? { ...card, status: 'failed' } // Optionally, add error message to card state
              : card
          )
        );
        dbg(`NewsletterApprovalCard ${data.taskId} status updated to failed.`);
      } else {
        // Handle other intermediate statuses if needed (e.g., 'processing_html', 'newsletter_assets_generated')
        // This might involve updating the existing NewsletterApprovalCard's status to show progress.
        // The tasksPendingHtmlGeneration check will ensure loading card shows if approval has happened.
        setNewsletterApprovalCards(prevCards =>
          prevCards.map(card =>
            card.taskId === data.taskId
              ? { ...card, status: data.status as any } // Cast status for now
              : card
          )
        );
        dbg(`NewsletterApprovalCard ${data.taskId} status updated to ${data.status}.`);
      }
    }
  }, [getNewCardPosition]);

  // Replace your existing handleNewsletterOutlineReady with this:
  const handleNewsletterOutlineReady = useCallback((data: NewsletterOutlineReadyEventData) => {
    dbg('Received newsletter_outline_ready event. Full data:', JSON.stringify(data, null, 2));

    // Robust validation of the incoming data, especially the nested outline structure
    if (!data) {
      dbg('newsletter_outline_ready: Event data is null or undefined.');
      return;
    }
    if (!data.taskId) {
      dbg('newsletter_outline_ready: taskId is missing in event data.');
      return;
    }
    if (!data.threadId) {
      dbg('newsletter_outline_ready: threadId is missing in event data.');
      return;
    }
    if (!data.outline) {
      dbg('newsletter_outline_ready: outline object is missing in event data.');
      return;
    }
    if (typeof data.outline.title !== 'string') {
      dbg('newsletter_outline_ready: outline.title is missing or not a string.');
      return;
    }
    if (!Array.isArray(data.outline.sections)) {
      dbg('newsletter_outline_ready: outline.sections is missing or not an array.');
      return;
    }
    if (!data.outline.sections.every(section =>
        typeof section === 'object' &&
        section !== null &&
        typeof section.heading === 'string' &&
        typeof section.description === 'string'
      )) {
      dbg('newsletter_outline_ready: One or more sections in outline.sections have invalid structure.');
      return;
    }
    dbg('newsletter_outline_ready: Event data passed validation. TaskId:', data.taskId, 'ThreadId:', data.threadId);

    let finalPosition = getNewCardPosition();

    if (data.clientLoadingCardId) {
      dbg(`newsletter_outline_ready: Received clientLoadingCardId: ${data.clientLoadingCardId}`);
      const loadingCardToReplace = loadingCards.find(lc => lc.id === data.clientLoadingCardId);
      if (loadingCardToReplace) {
        dbg(`Found loading card to replace at position:`, loadingCardToReplace.position);
        finalPosition = loadingCardToReplace.position;
        // Remove the loading card that is being replaced
        setLoadingCards(prevLoadingCards => prevLoadingCards.filter(lc => lc.id !== data.clientLoadingCardId));
        // Also remove from loadingCardPositions if it exists there
        setLoadingCardPositions(prevPositions => {
          const newPositions = { ...prevPositions };
          if (data.clientLoadingCardId && newPositions[data.clientLoadingCardId]) {
            delete newPositions[data.clientLoadingCardId];
          }
          return newPositions;
        });
      } else {
        dbg(`Loading card ${data.clientLoadingCardId} not found, using default position.`);
      }
    } else {
      dbg(`No clientLoadingCardId provided with newsletter_outline_ready event.`);
    }

    const newCardData: NewsletterApprovalCardDataType = {
      id: `newsletter-approval-${data.taskId}`,
      taskId: data.taskId,
      threadId: data.threadId, // Keep threadId here as it might be used by other parts of NewsletterApprovalCard or its props
      outline: data.outline,
      outlineText: JSON.stringify(data.outline, null, 2), // For display if needed
      position: finalPosition,
      status: 'outline_ready_for_review', // Initial status
      onApprove: async (params: { taskId: string; outline: NewsletterOutline }) => {
        dbg(`Approve clicked for task:`, params.taskId);
        try {
          setTasksPendingHtmlGeneration(prev => new Set(prev).add(params.taskId));
          await approveNewsletterOutline({ taskId: params.taskId, outline: params.outline });
          // Status will be updated via WebSocket (updateNewsletterCardStatus)
          // On success, updateNewsletterCardStatus will remove from tasksPendingHtmlGeneration
          // and transition to HtmlDisplayCard or failed status.
        } catch (error) {
          dbg(`Error approving newsletter outline for task ${params.taskId}:`, error);
          setTasksPendingHtmlGeneration(prev => {
            const next = new Set(prev);
            next.delete(params.taskId);
            return next;
          });
          // Optionally, update card to show error state if approve action itself doesn't trigger a status update
          setNewsletterApprovalCards(prev =>
            prev.map(c => c.taskId === params.taskId ? { ...c, status: 'failed', error: (error as Error).message } : c)
          );
        }
      },
      // Updated onSubmitFeedback - no longer passes threadId as it's not needed by the action
      onSubmitFeedback: async (params: { taskId: string; feedback: string }) => {
        dbg(`Submit Feedback clicked for task:`, params.taskId, `Feedback:`, params.feedback);
        // The actual call to submitNewsletterOutlineFeedback is now inside NewsletterApprovalCard.tsx
        // This prop in KonvaConceptCards is now more of a notification or could be removed if not used here.
        // For now, just logging. If KonvaConceptCards needs to react to feedback submission initiation,
        // it can be done here, e.g., optimistically updating UI or showing a global loading indicator.
      },
    };

    dbg(`Creating or updating NewsletterApprovalCard with data:`, newCardData);

    setNewsletterApprovalCards(prevCards => {
      const existingCardIndex = prevCards.findIndex(card => card.taskId === data.taskId);
      if (existingCardIndex !== -1) {
        // Update existing card (e.g., if a revised outline is received)
        const updatedCards = [...prevCards];
        updatedCards[existingCardIndex] = {
          ...prevCards[existingCardIndex],
          ...newCardData, // Spread newCardData to update outline, status, etc.
          position: prevCards[existingCardIndex].position // Preserve existing position on update
        };
        dbg(`Updated existing NewsletterApprovalCard for taskId: ${data.taskId}`);
        return updatedCards;
      } else {
        // Add new card
        dbg(`Added new NewsletterApprovalCard for taskId: ${data.taskId}`);
        return [...prevCards, newCardData];
      }
    });
  }, [getNewCardPosition, loadingCards, loadingCardPositions]);

  useEffect(() => {
    if (!socket || !isConnected) return;

    dbg('Socket connected and authenticated, setting up WebSocket listeners.');

    // Listen for createConceptCard events via WebSocket
    socket.on('createConceptCard', handleCreateConceptCard);

    // Listen for updateConceptCard events via WebSocket
    socket.on('updateConceptCard', handleUpdateConceptCard);

    // Updated handler signature and internal assertion for request_human_input event
    const handleRequestHumanInput = (eventPayload: any) => {
      const data = eventPayload as RequestHumanInputEventData; // Corrected type assertion

      dbg('Received request_human_input event. Full data:', JSON.stringify(data, null, 2));

      if (!data.taskId || !data.threadId || typeof data.question === 'undefined') {
        dbg('Invalid request_human_input event data received:', data);
        return;
      }

      const defaultPosition = { x: 200, y: 150 };

      const newApprovalCard: NewsletterApprovalCardDataType = {
        id: data.taskId,
        taskId: data.taskId,
        threadId: data.threadId,
        outlineText: data.question,
        agentContext: data.context,
        initialOptions: data.options,
        position: defaultPosition,
      };
      setNewsletterApprovalCards(prev => {
        if (prev.find(card => card.id === newApprovalCard.id)) return prev;
        return [...prev, newApprovalCard];
      });
    };
    socket.on('request_human_input', handleRequestHumanInput);

    // Listen for 'newsletter_outline_ready' event
    socket.on('newsletter_outline_ready' as any, handleNewsletterOutlineReady);

    // Listen for our new newsletter card status updates
    socket.on('updateNewsletterCardStatus' as any, handleUpdateNewsletterCardStatus);

    // Clean up
    return () => {
      dbg('Cleaning up WebSocket listeners');
      socket.off('createConceptCard', handleCreateConceptCard);
      socket.off('updateConceptCard', handleUpdateConceptCard);
      socket.off('request_human_input', handleRequestHumanInput);
      socket.off('newsletter_outline_ready' as any, handleNewsletterOutlineReady);
      socket.off('updateNewsletterCardStatus' as any, handleUpdateNewsletterCardStatus);
    };
  }, [socket, isConnected, handleCreateConceptCard, handleUpdateConceptCard, handleNewsletterOutlineReady, handleUpdateNewsletterCardStatus]);

  // Also listen for DOM events as fallback
  useEffect(() => {
    dbg('Setting up DOM event listeners');

    const handleCreateConceptCardDOMEvent = (event: CustomEvent<ConceptCardData>) => {
      handleCreateConceptCard(event.detail);
    };

    const handleUpdateConceptCardDOMEvent = (event: CustomEvent<Partial<ConceptCardData>>) => {
      handleUpdateConceptCard(event.detail);
    };

    // Handler for showing loading cards
    const handleShowLoadingCard = (event: CustomEvent<LoadingCardData>) => {
      dbg('Showing loading card:', event.detail);
      setLoadingCards(prev => [...prev, event.detail]);
    };

    // Handler for removing loading cards
    const handleRemoveLoadingCard = (event: CustomEvent<{id: string}>) => {
      dbg('Removing loading card:', event.detail.id);
      setLoadingCards(prev => prev.filter(card => card.id !== event.detail.id));
    };

    // Handler for updating loading card positions
    const handleUpdateLoadingCardPosition = (event: CustomEvent<{id: string, position: {x: number, y: number}}>) => {
      const { id, position } = event.detail;
      dbg(`Updating position for loading card ${id}:`, position);

      // Update the position in the loadingCardPositions state
      setLoadingCardPositions(prev => ({
        ...prev,
        [id]: position
      }));

      // Also update the position in the loadingCards state
      setLoadingCards(prev =>
        prev.map(card =>
          card.id === id
            ? { ...card, position }
            : card
        )
      );
    };

    // Handler for checking loading cards status
    const handleCheckLoadingCards = () => {
      dbg('Checking loading cards status');
      // Dispatch an event with the current count of loading cards
      const event = new CustomEvent('loadingCardsStatus', {
        detail: { count: loadingCards.length }
      });
      window.dispatchEvent(event);
    };

    // Handler for checking concept cards status
    const handleCheckConceptCards = () => {
      dbg('Checking concept cards status');
      // Dispatch an event with the current count of concept cards
      const event = new CustomEvent('conceptCardsStatus', {
        detail: { count: cards.length }
      });
      window.dispatchEvent(event);
    };

    // Handler for checking newsletter approval cards status
    const handleCheckNewsletterApprovalCards = () => {
      dbg('Checking newsletter approval cards status');
      const event = new CustomEvent('newsletterApprovalCardsStatus', {
        detail: { count: newsletterApprovalCards.length }
      });
      window.dispatchEvent(event);
    };

    // Handler for getting a loading card's position
    const handleGetLoadingCardPosition = (event: CustomEvent<{id: string, callback: (position: {x: number, y: number} | null) => void}>) => {
      const { id, callback } = event.detail;
      dbg(`Getting position for loading card ${id}`);

      // Find the loading card
      const loadingCard = loadingCards.find(card => card.id === id);

      // If the card exists, return its position, otherwise return null
      if (loadingCard) {
        dbg(`Found loading card ${id} at position:`, loadingCard.position);
        callback(loadingCard.position);
      } else {
        dbg(`Loading card ${id} not found`);
        callback(null);
      }
    };

    // Handler for checking HTML display cards status
    const handleCheckHtmlDisplayCards = () => {
      dbg('Checking HTML display cards status');
      const event = new CustomEvent('htmlDisplayCardsStatus', {
        detail: { count: htmlDisplayCards.length }
      });
      window.dispatchEvent(event);
    };

    // Add DOM event listeners
    window.addEventListener('createConceptCard', handleCreateConceptCardDOMEvent as EventListener);
    window.addEventListener('updateConceptCard', handleUpdateConceptCardDOMEvent as EventListener);
    window.addEventListener('showLoadingConceptCard', handleShowLoadingCard as EventListener);
    window.addEventListener('removeLoadingConceptCard', handleRemoveLoadingCard as EventListener);
    window.addEventListener('updateLoadingCardPosition', handleUpdateLoadingCardPosition as EventListener);
    window.addEventListener('getLoadingCardPosition', handleGetLoadingCardPosition as EventListener);
    window.addEventListener('checkLoadingCards', handleCheckLoadingCards as EventListener);
    window.addEventListener('checkConceptCards', handleCheckConceptCards as EventListener);
    window.addEventListener('checkNewsletterApprovalCards', handleCheckNewsletterApprovalCards as EventListener);
    window.addEventListener('checkHtmlDisplayCards', handleCheckHtmlDisplayCards as EventListener);

    // Clean up
    return () => {
      dbg('Cleaning up DOM event listeners');
      window.removeEventListener('createConceptCard', handleCreateConceptCardDOMEvent as EventListener);
      window.removeEventListener('updateConceptCard', handleUpdateConceptCardDOMEvent as EventListener);
      window.removeEventListener('showLoadingConceptCard', handleShowLoadingCard as EventListener);
      window.removeEventListener('removeLoadingConceptCard', handleRemoveLoadingCard as EventListener);
      window.removeEventListener('updateLoadingCardPosition', handleUpdateLoadingCardPosition as EventListener);
      window.removeEventListener('getLoadingCardPosition', handleGetLoadingCardPosition as EventListener);
      window.removeEventListener('checkLoadingCards', handleCheckLoadingCards as EventListener);
      window.removeEventListener('checkConceptCards', handleCheckConceptCards as EventListener);
      window.removeEventListener('checkNewsletterApprovalCards', handleCheckNewsletterApprovalCards as EventListener);
      window.removeEventListener('checkHtmlDisplayCards', handleCheckHtmlDisplayCards as EventListener);
    };
  }, [loadingCards, cards, newsletterApprovalCards, htmlDisplayCards, handleCreateConceptCard, handleUpdateConceptCard, handleNewsletterOutlineReady]);

  // Handle card selection
  const handleSelectCard = (id: string) => {
    setSelectedCardId(id);
  };

  // Handle card removal
  const handleRemoveCard = useCallback((id: string) => {
    setCards(prevCards => prevCards.filter(card => card.id !== id));
    if (selectedCardId === id) {
      setSelectedCardId(null);
    }
  }, [selectedCardId]);

  // Handle input submission for preview update
  const handleAnswer = async (id: string, questionIndex: number, input: string) => {
    try {
      dbg(`Updating preview for card ${id} with input: ${input}`);

      // Find the card to get its productId
      const card = cards.find(c => c.id === id);
      const productId = card?.productId;

      if (productId) {
        dbg(`Using product ID for card ${id}: ${productId}`);
      }

      // Get the reference images
      let referenceImages = getReferenceImages();

      // If we have a productId, try to get the reference image
      if (productId) {
        try {
          // Fetch the product to get its reference_file_id
          const product = await getModelProduct({ modelId: productId });

          if (product && product.reference_file_id) {
            dbg(`Found product reference image: ${product.reference_file_id}`);

            // Check if the reference image is already in the array
            if (!referenceImages.includes(product.reference_file_id)) {
              // Add the product reference image to the beginning of the array
              referenceImages = [product.reference_file_id, ...referenceImages];
              dbg(`Added product reference image to reference images`);
            }
          }
        } catch (productError) {
          dbg(`Error fetching product reference image:`, productError);
        }
      }

      // Call the Wasp action to update the concept card
      await updateConceptCard({
        cardId: id,
        questionIndex: 0, // Always use index 0 for the single input
        answer: input,
        userId: 1, // Default user ID
        productId, // Include the product ID if available
        referenceImages // Include reference images for preview generation
      });

      // Also call the provided callback
      onAnswer(id, questionIndex, input);
    } catch (error) {
      dbg(`Error updating concept card:`, error);
    }
  };

  // Handle generate button click
  const handleGenerate = async (cardId: string) => {
    dbg(`Generate clicked for card:`, cardId);
    setIsLoading(true);

    // Find the card data to potentially get productId/modelId
    const cardData = cards.find(card => card.id === cardId);
    if (!cardData) {
      dbg(`Could not find card data for generation`);
      setIsLoading(false);
      return;
    }

    // *** ASSUMPTION: cardData contains productId ***
    // If not, this needs to be fetched or stored differently.
    const productId = cardData.productId;
    dbg(`Using productId: ${productId} for generation task`);

    try {
      // Get the full list of reference images from the bar
      let referenceImages = getReferenceImages();

      // If we have a productId, try to get the reference image
      if (productId) {
        try {
          // Fetch the product to get its reference_file_id
          const product = await getModelProduct({ modelId: productId });

          if (product && product.reference_file_id) {
            dbg(`Found product reference image: ${product.reference_file_id}`);

            // Check if the reference image is already in the array
            if (!referenceImages.includes(product.reference_file_id)) {
              // Add the product reference image to the beginning of the array
              referenceImages = [product.reference_file_id, ...referenceImages];
              dbg(`Added product reference image to reference images`);
            }
          }
        } catch (productError) {
          dbg(`Error fetching product reference image:`, productError);
        }
      }

      dbg(`Passing ${referenceImages.length} reference images for generation task`);

      await generateFromConceptCard({
        cardId,
        userId: user?.id || 1,
        productId: productId,
        referenceImages: referenceImages
      });
      // Card status update will come via WebSocket
    } catch (error) {
      dbg(`Error generating from concept card:`, error);
    } finally {
      setIsLoading(false);
    }
  };

  // Log when cards are added or removed
  useEffect(() => {
    dbg(`Current cards:`, cards);
    window.dispatchEvent(new CustomEvent('conceptCardsStatus', { detail: { count: cards.length } }));
  }, [cards]);

  useEffect(() => {
    dbg(`Current loading cards:`, loadingCards);
    window.dispatchEvent(new CustomEvent('loadingCardsStatus', { detail: { count: loadingCards.length } }));
  }, [loadingCards]);

  useEffect(() => {
    dbg(`Current newsletter approval cards:`, newsletterApprovalCards);
    window.dispatchEvent(new CustomEvent('newsletterApprovalCardsStatus', { detail: { count: newsletterApprovalCards.length } }));
  }, [newsletterApprovalCards]);

  useEffect(() => {
    dbg(`Current HTML display cards:`, htmlDisplayCards);
    window.dispatchEvent(new CustomEvent('htmlDisplayCardsStatus', { detail: { count: htmlDisplayCards.length } }));
  }, [htmlDisplayCards]);

  return (
    <Group>
      {/* Render loading cards */}
      {loadingCards.map(card => (
        <KonvaLoadingConceptCard
          key={card.id}
          id={card.id}
          position={card.position}
          requestText={card.requestText}
        />
      ))}

      {/* Render real cards */}
      {cards.map(card => (
        <KonvaGreenConceptCard
          key={card.id}
          card={card}
          onAnswer={handleAnswer}
          onGenerate={() => handleGenerate(card.id)}
          onRemove={handleRemoveCard}
          isSelected={selectedCardId === card.id}
          onSelect={() => handleSelectCard(card.id)}
        />
      ))}

      {/* Render Newsletter Approval Cards */}
      {newsletterApprovalCards.map(cardData => {
        if (tasksPendingHtmlGeneration.has(cardData.taskId)) {
          return (
            <KonvaLoadingConceptCard
              key={`loading-html-${cardData.taskId}`}
              id={`loading-html-${cardData.taskId}`}
              position={cardData.position} // Use the approval card's current position
              requestText="Generating your newsletter..."
            />
          );
        }
        // Render the actual card if not in loading state for HTML generation
        // and if it hasn't been replaced by an HtmlDisplayCard already (though htmlDisplayCards are separate)
        return (
          <NewsletterApprovalCard
            key={cardData.id}
            cardData={cardData}
            onApprove={cardData.onApprove}
            onSubmitFeedback={cardData.onSubmitFeedback}
          />
        );
      })}

      {/* Render HTML Display Cards */}
      {htmlDisplayCards.map(cardData => (
        <KonvaHtmlDisplayCard
          key={cardData.id}
          cardData={cardData}
        />
      ))}
    </Group>
  );
};

export default KonvaConceptCards;
