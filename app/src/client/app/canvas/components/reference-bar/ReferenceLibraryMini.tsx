import React, { useState, useEffect, useRef } from 'react';
import { Search, X, Image, Check } from 'lucide-react';
import { useReferenceData, ReferenceItem } from '../../../../app/referencepage/utils';
import { useReferenceImages } from '../../hooks/useReferenceImages';
import { useVoiceAgentRoom } from '../../../../hooks/useVoiceAgentRoom';

interface ReferenceLibraryMiniProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectImage: (imageUrl: string) => void;
  whiteboardId?: string;
}

const ReferenceLibraryMini: React.FC<ReferenceLibraryMiniProps> = ({
  isOpen,
  onClose,
  onSelectImage,
  whiteboardId
}) => {
  const { data, isLoading, error, getPaginatedItems } = useReferenceData();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [items, setItems] = useState<ReferenceItem[]>([]);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const ITEMS_PER_PAGE = 15;
  const observer = useRef<IntersectionObserver | null>(null);
  const roomName = useVoiceAgentRoom();
  const { addReferenceImage } = useReferenceImages(roomName, whiteboardId);

  // Load initial items and when filters change
  useEffect(() => {
    if (!isLoading && data) {
      const result = getPaginatedItems(searchQuery, selectedCategory, selectedBrand, 1, ITEMS_PER_PAGE);
      setItems(result.items);
      setHasMore(result.hasMore);
      setPage(1);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoading, data, searchQuery, selectedCategory, selectedBrand]);

  // Load more items when scrolling
  const loadMoreItems = () => {
    if (loadingMore || !hasMore) return;

    setLoadingMore(true);

    setTimeout(() => {
      const nextPage = page + 1;
      const result = getPaginatedItems(searchQuery, selectedCategory, selectedBrand, nextPage, ITEMS_PER_PAGE);

      setItems(prevItems => [...prevItems, ...result.items]);
      setHasMore(result.hasMore);
      setPage(nextPage);
      setLoadingMore(false);
    }, 300);
  };

  // Setup intersection observer for infinite scrolling
  const lastItemRef = (node: HTMLDivElement | null) => {
    if (loadingMore) return;
    if (observer.current) observer.current.disconnect();

    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        loadMoreItems();
      }
    }, {
      rootMargin: '0px 0px 200px 0px',
      threshold: 0.1
    });

    if (node) observer.current.observe(node);
  };

  // Handle drag start for an image
  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, item: ReferenceItem) => {
    console.log('[ReferenceLibraryMini] Starting drag for image:', item.url);

    // Set the drag data with JSON containing the image URL and type
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: 'reference-image',
      url: item.url
    }));

    // Optional: Set a drag image for better visual feedback
    const img = document.createElement('img');
    img.src = item.url;
    e.dataTransfer.setDragImage(img, 20, 20);

    // Set the drag effect to copy
    e.dataTransfer.effectAllowed = 'copy';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed left-[70px] top-12 bottom-0 w-[320px] bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-lg z-30 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-sm font-semibold text-gray-900 dark:text-white flex items-center">
          <Image size={16} className="mr-2 text-[#676D50] dark:text-[#A6884C]" />
          Reference Library
        </h3>
        <button
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <X size={16} />
        </button>
      </div>

      {/* Search and Filters */}
      <div className="p-3 border-b border-gray-200 dark:border-gray-700">
        <div className="relative">
          <input
            type="text"
            placeholder="Search references..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full px-3 py-2 pl-9 text-sm bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-[#676D50] dark:focus:ring-[#A6884C]"
          />
          <Search size={16} className="absolute left-3 top-2.5 text-gray-400" />
        </div>

        {/* Category and Brand Filters (simplified) */}
        <div className="grid grid-cols-2 gap-2 mt-2">
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="w-full px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-[#676D50] dark:focus:ring-[#A6884C] truncate"
          >
            <option value="">All Categories</option>
            {data?.files && [...new Set(data.files.map(item => item.category).filter(Boolean))].map((category, index) => (
              <option key={index} value={category}>{category}</option>
            ))}
          </select>

          <select
            value={selectedBrand}
            onChange={(e) => setSelectedBrand(e.target.value)}
            className="w-full px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-[#676D50] dark:focus:ring-[#A6884C] truncate"
          >
            <option value="">All Brands</option>
            {data?.files && [...new Set(data.files.map(item => item.brand).filter(Boolean))].map((brand, index) => (
              <option key={index} value={brand}>{brand}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Image Grid */}
      <div className="flex-1 overflow-y-auto p-3">
        {isLoading ? (
          <div className="flex items-center justify-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#676D50] dark:border-[#A6884C]"></div>
          </div>
        ) : error ? (
          <div className="text-center py-4 text-red-500">
            Error loading references
          </div>
        ) : items.length === 0 ? (
          <div className="text-center py-4 text-gray-500 dark:text-gray-400">
            No references found
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-2">
            {items.map((item, index) => {
              const isLastItem = index === items.length - 1;

              return (
                <div
                  key={`${item.url}-${index}`}
                  ref={isLastItem ? lastItemRef : null}
                  className="relative aspect-square rounded overflow-hidden border border-gray-200 dark:border-gray-700 hover:border-[#676D50] dark:hover:border-[#A6884C] cursor-grab active:cursor-grabbing group"
                  draggable={true}
                  onDragStart={(e) => handleDragStart(e, item)}
                  onClick={() => onSelectImage(item.url)}
                >
                  <img
                    src={item.url}
                    alt={`${item.category} - ${item.brand || 'Unknown'}`}
                    className="w-full h-full object-cover pointer-events-none"
                    loading="lazy"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 text-white text-xs bg-black bg-opacity-70 px-2 py-1 rounded">
                      Drag to canvas
                    </div>
                  </div>
                  <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 truncate opacity-0 group-hover:opacity-100 transition-opacity">
                    {item.brand || 'Unknown'}
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {loadingMore && (
          <div className="flex justify-center items-center py-4 mt-2">
            <div className="animate-spin rounded-full h-6 w-6 border-2 border-[#676D50]/20 dark:border-[#A6884C]/20 border-t-[#676D50] dark:border-t-[#A6884C]"></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReferenceLibraryMini;
