import React, { useMemo, useState } from 'react';
import { Plus, X, Image } from 'lucide-react';
import {
  getModelProduct,
  updateProduct,
  createCanvasReference,
  deleteCanvasReference,
  getCanvasReferences,
  useQuery,
} from 'wasp/client/operations';
import MediaLibraryModal from './MediaLibraryModal';
import ReferenceLibraryMini from './ReferenceLibraryMini';
import './reference-bar.css';

interface ReferenceBarProps {
  modelId?: string;
}

const ReferenceBar: React.FC<ReferenceBarProps> = ({ modelId }) => {
  // State for references
  const [productRef, setProductRef] = useState<{ fileId: string; thumbUrl: string } | null>(null);
  const [isMediaLibraryOpen, setIsMediaLibraryOpen] = useState<boolean>(false);
  const [isReferenceLibraryOpen, setIsReferenceLibraryOpen] = useState<boolean>(false);

  const { data, isLoading } = useQuery(getCanvasReferences, { whiteboardId: modelId ?? '' }, { enabled: !!modelId });

  const references = useMemo(() => {
    return Array.from(data ?? []).map((ref: any) => ({
      fileId: ref.id,
      thumbUrl: ref.thumbUrl,
    }));
  }, [data]);

  // Fetch canvas references from database and sync with localStorage
  // useEffect(() => {
  //   const fetchCanvasReferences = async () => {
  //     if (!modelId) {
  //       setIsLoading(false);
  //       return;
  //     }

  //     try {
  //       // Fetch references from database
  //       const dbReferences = await getCanvasReferences({ whiteboardId: modelId });

  //       // Combine database references with localStorage references
  //       const combinedRefs = new Set([...dbReferences.map((ref) => ref.thumbUrl)]);

  //       // Update state with combined references (excluding product reference)
  //       setCanvasRefs(
  //         Array.from(combinedRefs).map((fileId) => ({
  //           fileId,
  //           thumbUrl: fileId,
  //         }))
  //       );

  //       setIsLoading(false);
  //     } catch (error) {
  //       console.error('[ReferenceBar] Error fetching canvas references:', error);

  //       setIsLoading(false);
  //     }
  //   };

  //   fetchCanvasReferences();
  // }, [getReferenceImages, productRef, modelId]);

  // Remove product reference
  const handleToggleProductRef = async () => {
    if (!modelId || !productRef) return;

    try {
      // Get the product again to ensure we have the latest data
      const product = await getModelProduct({ modelId });

      if (product) {
        // Update product in database (clear reference_file_id)
        await updateProduct({
          id: product.id,
          reference_file_id: null,
        });
      }

      setProductRef(null);
    } catch (error) {
      console.error('[ReferenceBar] Error removing product reference:', error);
    }
  };

  // Remove canvas reference
  const handleRemoveCanvasRef = async (fileId: string) => {
    if (!modelId) return;

    try {
      // Remove from database
      await deleteCanvasReference({
        id: fileId,
      });
    } catch (error) {
      console.error('[ReferenceBar] Error removing canvas reference:', error);
    }
  };

  // Open media library
  const handleOpenMediaLibrary = () => {
    setIsMediaLibraryOpen(true);
  };

  // Handle selecting a canvas image from the media library
  const handleSelectCanvasImage = async (imageUrl: string) => {
    if (!modelId) return;

    try {
      // Check if this reference already exists in local state
      const referenceExists = references.some((ref) => ref.thumbUrl === imageUrl);
      if (referenceExists) {
        console.log('[ReferenceBar] Reference already exists, skipping:', imageUrl);
        return;
      }

      // Add to database
      await createCanvasReference({
        whiteboardId: modelId,
        thumbUrl: imageUrl,
      });
    } catch (error) {
      console.error('[ReferenceBar] Error adding canvas reference:', error);
    }
  };

  // Handle selecting a product image from the media library
  const handleSelectProductImage = async (imageUrl: string) => {
    if (!modelId) return;

    try {
      // Get the product to ensure we have the latest data
      const product = await getModelProduct({ modelId });

      if (product) {
        // Update product in database with new reference_file_id
        await updateProduct({
          id: product.id,
          reference_file_id: imageUrl,
        });

        // Update local state
        setProductRef({
          fileId: imageUrl,
          thumbUrl: imageUrl,
        });

        // // Add to reference images if not already there
        // await addReferenceImage(imageUrl);
      }
    } catch (error) {
      console.error('[ReferenceBar] Error setting product reference:', error);
    }
  };

  if (isLoading) {
    return (
      <div className='flex flex-col items-center h-[calc(100%-48px)] w-[60px] fixed top-12 right-0 bg-gray-100 dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 py-4 px-2 z-20 overflow-y-auto'>
        <div className='writing-mode-vertical-rl rotate-180 font-semibold text-[#676D50] dark:text-[#A6884C] mb-4'>
          References
        </div>
        <div className='text-gray-500 dark:text-gray-400 text-sm italic'>...</div>
      </div>
    );
  }

  return (
    <>
      <div className='flex flex-col items-center h-screen w-[60px] fixed top-0 right-0 bg-[#F6F3E5] dark:bg-gray-800 border-l border-[#E8E4D4] dark:border-gray-700 py-4 px-1 z-20 overflow-y-auto' style={{ boxShadow: '-2px 0 8px rgba(0,0,0,0.08)' }}>
        <div className='flex flex-col items-center gap-3 w-full px-1'>
          {/* Product Reference Thumbnail */}
          {productRef && (
            <div className='relative w-10 h-10 rounded overflow-hidden border-2 border-[#676D50] dark:border-[#A6884C] group'>
              <img
                src={productRef.thumbUrl}
                alt='Product Reference'
                title='Product Reference'
                className='w-full h-full object-cover cursor-grab'
                draggable={true}
                onDragStart={(e) => {
                  e.dataTransfer.setData('text/plain', productRef.thumbUrl);
                  e.dataTransfer.setData(
                    'application/json',
                    JSON.stringify({
                      type: 'reference-image',
                      url: productRef.thumbUrl,
                    })
                  );
                }}
              />
              <button
                className='absolute -top-1.5 -right-1.5 w-4 h-4 bg-white dark:bg-gray-800 text-black dark:text-white border border-gray-300 dark:border-gray-600 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-opacity'
                onClick={() => handleToggleProductRef()}
                title='Remove product reference'
              >
                <X size={10} />
              </button>
            </div>
          )}

          {/* Divider if both product and canvas refs exist */}
          {productRef && references?.length > 0 && (
            <div className='text-gray-300 dark:text-gray-600 w-full text-center my-1'>—</div>
          )}

          {/* Canvas References */}
          {references?.map((ref, index) => (
            <div
              key={index}
              className='relative w-10 h-10 rounded overflow-hidden border-2 border-blue-500 dark:border-blue-400 group'
            >
              <img
                src={ref.thumbUrl}
                alt={`Canvas Reference ${index + 1}`}
                title={`Canvas Reference ${index + 1}`}
                className='w-full h-full object-cover cursor-grab'
                draggable={true}
                onDragStart={(e) => {
                  e.dataTransfer.setData('text/plain', ref.thumbUrl);
                  e.dataTransfer.setData(
                    'application/json',
                    JSON.stringify({
                      type: 'reference-image',
                      url: ref.thumbUrl,
                    })
                  );
                }}
              />
              <button
                className='absolute -top-1.5 -right-1.5 w-4 h-4 bg-white dark:bg-gray-800 text-black dark:text-white border border-gray-300 dark:border-gray-600 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-opacity'
                onClick={() => handleRemoveCanvasRef(ref.fileId)}
                title='Remove reference'
              >
                <X size={10} />
              </button>
            </div>
          ))}

          {/* Add Reference Button */}
          <button
            className='w-10 h-10 rounded bg-gray-200 dark:bg-gray-700 border border-dashed border-[#676D50] dark:border-[#A6884C] text-[#676D50] dark:text-[#A6884C] flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors'
            onClick={handleOpenMediaLibrary}
            title='Add reference from media library'
          >
            <Plus size={16} />
          </button>

          {/* Reference Library Button */}
          <button
            className='w-10 h-10 rounded bg-gray-200 dark:bg-gray-700 border border-dashed border-blue-500 dark:border-blue-400 text-blue-500 dark:text-blue-400 flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors mt-2'
            onClick={() => setIsReferenceLibraryOpen(true)}
            title='Browse reference library'
          >
            <Image size={16} />
          </button>
        </div>
      </div>

      {/* Media Library Modal */}
      <MediaLibraryModal
        isOpen={isMediaLibraryOpen}
        onClose={() => setIsMediaLibraryOpen(false)}
        onSelectCanvasImage={handleSelectCanvasImage}
        onSelectProductImage={handleSelectProductImage}
        modelId={modelId}
      />

      {/* Reference Library Mini */}
      <ReferenceLibraryMini
        isOpen={isReferenceLibraryOpen}
        onClose={() => setIsReferenceLibraryOpen(false)}
        onSelectImage={handleSelectCanvasImage}
        whiteboardId={modelId}
      />
    </>
  );
};

export default ReferenceBar;
