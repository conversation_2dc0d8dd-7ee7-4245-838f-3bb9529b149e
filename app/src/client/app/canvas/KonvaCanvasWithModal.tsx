import React, { useState, useCallback, useEffect } from 'react';
import { useSocketListener } from 'wasp/client/webSocket';
import { useVoiceAgentRoom } from './../../hooks/useVoiceAgentRoom';
import KonvaCanvas, { KonvaCanvasProps } from './KonvaCanvas';
import ObjectRemovalModal from './components/ObjectRemovalModal';
import ImageContextMenu from './components/ImageContextMenu';

import { setImages, useCanvasShallowSelector, useCanvasStore } from './store/canvas-store';
import { ContentOutlineApprovalCardData } from './types/contentOutlineApprovalTypes';
import { ECanvasTool, Edge, ImageObject, HTMLElementData } from './types';
import { useReferenceImages } from './hooks/useReferenceImages';
import TaskListener from './components/TaskListener';
import DirectWebSocketListener from './components/DirectWebSocketListener';
import { useAuthenticatedSocket } from '../../hooks/useAuthenticatedSocket';
import { canvasEventBus } from './utils/event-bus';
import { ReferenceBar } from './components/reference-bar';
import useCanvasAnimations from './hooks/useCanvasAnimations';
import ConceptCardCreator from './components/ConceptCardCreator';
import Konva from 'konva';
import { createCanvasReference } from 'wasp/client/operations';
import { addContentOutlineApprovalCard, addInteractiveWireframe } from './store/canvas-store';
import { InteractiveWireframeData } from './types';
import KonvaSideChat from './components/KonvaSideChat';

const DEFAULT_IMAGE_WIDTH = 300;
const DEFAULT_IMAGE_HEIGHT = 200;

const isHtmlElement = (data: any) =>
  data.elementType === 'html' ||
  data.elementType === 'generate_html' ||
  data.type === 'html' ||
  data.type === 'generate_html';

const isImageElement = (data: any) => data.elementType === 'image' || data.type === 'image';

const KonvaCanvasWithModal: React.FC<KonvaCanvasProps> = ({ children, updateCursorPosition }) => {
  // Object removal modal state
  const [showObjectRemovalModal, setShowObjectRemovalModal] = useState(false);
  const [selectedImageForRemoval, setSelectedImageForRemoval] = useState<{
    id: string;
    src: string;
    width: number;
    height: number;
  } | null>(null);

  // Context menu state - REPLACING THE EXISTING ONE to match BaseScene event
  const [contextMenuVisible, setContextMenuVisible] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
  const [contextMenuData, setContextMenuData] = useState<{
    id: string;
    src: string;
    width: number;
    height: number;
    isReference?: boolean;
    connectedEdges?: Edge[];
    // Add konvaX, konvaY if needed by context menu actions later
  } | null>(null);

  // State for KonvaSideChat
  const [showSideChat, setShowSideChat] = useState(false);
  const [sideChatImageId, setSideChatImageId] = useState<string | null>(null);

  // Get images from the canvas store
  const { images, selectedIds, htmlElements } = useCanvasShallowSelector((state) => ({
    images: state.images,
    selectedIds: state.selectedIds,
    htmlElements: state.htmlElements,
  }));

  // Handler for opening the object removal editor
  const handleOpenObjectRemovalEditor = useCallback(
    (image: { id: string; src: string; width: number; height: number }) => {
      setSelectedImageForRemoval(image);
      setShowObjectRemovalModal(true);
    },
    []
  );

  // Handler for closing the object removal modal
  const handleCloseObjectRemovalModal = useCallback(() => {
    setShowObjectRemovalModal(false);
    setSelectedImageForRemoval(null);
  }, []);

  // Initialize authenticated socket connection
  const { socket, isConnected } = useAuthenticatedSocket();

  // Initialize canvas animations
  useCanvasAnimations();

  // Log socket connection status
  useEffect(() => {
    console.log('[KonvaCanvasWithModal] Socket connection status:', isConnected ? 'Connected' : 'Disconnected');
    if (isConnected) {
      console.log('[KonvaCanvasWithModal] Socket authenticated and connected');
    }
  }, [isConnected]);

  // Get the current room name from our centralized hook
  const roomName = useVoiceAgentRoom();

  // We'll handle task updates through the socket listeners directly
  // No need to use the useTasks hook here

  // Import the useReferenceImages hook with the current room name and model ID
  const { addReferenceImage, removeReferenceImage } = useReferenceImages(roomName, modelId);

  // Handler for setting an image as a reference
  const handleSetAsReference = useCallback(
    async (imageId: string) => {
      console.log('[KonvaCanvasWithModal] Setting image as reference:', imageId);

      // Find the image in the images array
      const image = images.find((img: ImageObject) => img.id === imageId);
      if (!image) {
        console.error('[KonvaCanvasWithModal] Image not found:', imageId);
        return;
      }

      // Get the modelId from localStorage
      const modelId = localStorage.getItem('selectedModelId');

      try {
        // Toggle the isReference flag
        const isCurrentlyReference = !!image.isReference;
        const updatedImages = images.map((img: ImageObject) => {
          if (img.id === imageId) {
            return {
              ...img,
              isReference: !isCurrentlyReference,
            };
          }
          return img;
        });

        // Update the images in the store
        setImages({ images: updatedImages });

        // Log the action and data URL
        if (!isCurrentlyReference) {
          console.log('[KonvaCanvasWithModal] Image set as reference:', imageId);
          console.log('[KonvaCanvasWithModal] Image source:', image.src);

          // Add the reference image using the hook
          addReferenceImage(image.src);

          // Also add to DB if modelId exists
          if (modelId) {
            try {
              await createCanvasReference({ whiteboardId: modelId, thumbUrl: image.src });
              console.log('[KonvaCanvasWithModal] Synced reference to DB for modelId:', modelId);
            } catch (dbErr) {
              console.error('[KonvaCanvasWithModal] Error syncing reference to DB:', dbErr);
            }
          }
        } else {
          console.log('[KonvaCanvasWithModal] Image removed as reference:', imageId);

          // Remove the reference image using the hook
          removeReferenceImage(image.src);
        }
      } catch (error) {
        console.error('[KonvaCanvasWithModal] Error setting image as reference:', error);
      }
    },
    [images, addReferenceImage, removeReferenceImage]
  );

  // Handler for removing an image
  const handleRemoveImage = useCallback(
    (imageId: string) => {
      console.log('[KonvaCanvasWithModal] Removing image:', imageId);

      // Delete the image chat history from the database
      try {
        // Call the API to delete the image chat history using the api wrapper
        import('wasp/client/api')
          .then(({ api }) => {
            api
              .post('/api/imagechat/deleteImageChat', { imageId })
              .then((response) => {
                console.log('[KonvaCanvasWithModal] Successfully deleted image chat history:', response.data.message);
              })
              .catch((error) => {
                console.error('[KonvaCanvasWithModal] Failed to delete image chat history:', error);
              });
          })
          .catch((error) => {
            console.error('[KonvaCanvasWithModal] Error importing api:', error);
          });
      } catch (error) {
        console.error('[KonvaCanvasWithModal] Error calling deleteImageChat API:', error);
      }

      // Remove the image from the canvas
      const updatedImages = images.filter((img) => img.id !== imageId);
      setImages({ images: updatedImages });
    },
    [images]
  );

  // Listen for custom context menu events from BaseScene
  useEffect(() => {
    const handleShowMenu = (event: Event) => {
      const customEvent = event as CustomEvent;
      const { id, src, width, height, konvaX, konvaY, pointerPosition, connectedEdges } = customEvent.detail;

      const activeTool = useCanvasStore.getState().activeTool;
      if (activeTool === ECanvasTool.MASK) {
        setContextMenuVisible(false);
        return;
      }

      const imageFromStore = useCanvasStore.getState().images.find((img: ImageObject) => img.id === id);

      setContextMenuPosition(pointerPosition);
      setContextMenuData({
        id,
        src,
        width,
        height,
        isReference: imageFromStore?.isReference || false,
        connectedEdges: connectedEdges || [],
      });
      setContextMenuVisible(true);
      console.log('[KonvaCanvasWithModal] showImageContextMenu event processed, menu visible.', customEvent.detail);
    };

    // Handler for the old URLImage context menu event (showContextMenu)
    const handleShowMenuOld = (event: Event) => {
      const customEvent = event as CustomEvent;
      const { x, y, imageId, imageSrc, imageWidth, imageHeight, isReference } = customEvent.detail;

      const activeTool = useCanvasStore.getState().activeTool;
      if (activeTool === ECanvasTool.MASK) {
        setContextMenuVisible(false);
        return;
      }

      const imageFromStore = useCanvasStore.getState().images.find((img: ImageObject) => img.id === imageId);

      setContextMenuPosition({ x, y });
      setContextMenuData({
        id: imageId,
        src: imageSrc,
        width: imageWidth,
        height: imageHeight,
        isReference: imageFromStore?.isReference || isReference || false,
        connectedEdges: [],
      });
      setContextMenuVisible(true);
      console.log('[KonvaCanvasWithModal] showContextMenu (old) event processed, menu visible.', customEvent.detail);
    };

    const handleHideMenu = () => {
      setContextMenuVisible(false);
      console.log('[KonvaCanvasWithModal] hideImageContextMenu event processed, menu hidden.');
    };

    const handleSetGroupAsReferenceEvent = async (event: Event) => {
      const customEvent = event as CustomEvent<{ imageId: string }>;
      const { imageId } = customEvent.detail;
      if (!imageId) {
        console.error('[KonvaCanvasWithModal] Set group as reference event: imageId is missing.');
        return;
      }

      // Temporarily get the stage reference directly.
      // Ideally, KonvaCanvas would expose its stageRef or a method to access BaseScene.
      const stage = Konva.stages[0]; // NOTE: This assumes only one Konva.Stage on the page.
      if (!stage) {
        console.error('[KonvaCanvasWithModal] Could not get Konva stage for setGroupAsReference.');
        return;
      }

      const baseSceneNode = stage.findOne('#BaseScene');
      if (baseSceneNode && typeof (baseSceneNode as any).exportConnectedGroupAsImage === 'function') {
        try {
          console.log(`[KonvaCanvasWithModal] Calling exportConnectedGroupAsImage for imageId: ${imageId}`);
          const dataURL = await (baseSceneNode as any).exportConnectedGroupAsImage(imageId);
          if (dataURL) {
            console.log(
              '[KonvaCanvasWithModal] Exported Group Image for reference (first 100 chars):',
              dataURL.substring(0, 100) + '...'
            );
            addReferenceImage(dataURL); // Add to reference bar (will be uploaded to R2 if dataURL)
            console.log('[KonvaCanvasWithModal] Group image added to reference bar.');
          } else {
            console.warn('[KonvaCanvasWithModal] exportConnectedGroupAsImage returned no data URL for reference.');
          }
        } catch (error) {
          console.error('[KonvaCanvasWithModal] Error exporting group as image for reference:', error);
        }
      } else {
        console.error(
          '[KonvaCanvasWithModal] BaseScene or exportConnectedGroupAsImage method not found for setGroupAsReference.'
        );
      }
      setContextMenuVisible(false); // Close context menu after action
    };

    window.addEventListener('canvas:showImageContextMenu', handleShowMenu);
    window.addEventListener('showContextMenu', handleShowMenuOld); // Listen for old URLImage event
    window.addEventListener('canvas:hideImageContextMenu', handleHideMenu);
    window.addEventListener('canvas:setGroupAsReference', handleSetGroupAsReferenceEvent);

    // General click listener to hide the menu if clicked outside
    const handleDocumentClickToHideMenu = (event: MouseEvent) => {
      if (contextMenuVisible) {
        const menuEl = document.querySelector('.image-context-menu');
        if (menuEl && menuEl.contains(event.target as Node)) {
          return; // Click is inside context menu; ignore
        }
        setContextMenuVisible(false);
      }
    };
    document.addEventListener('click', handleDocumentClickToHideMenu);

    return () => {
      window.removeEventListener('canvas:showImageContextMenu', handleShowMenu);
      window.removeEventListener('showContextMenu', handleShowMenuOld); // Remove old URLImage event listener
      window.removeEventListener('canvas:hideImageContextMenu', handleHideMenu);
      window.removeEventListener('canvas:setGroupAsReference', handleSetGroupAsReferenceEvent);
      document.removeEventListener('click', handleDocumentClickToHideMenu);
    };
  }, [contextMenuVisible, addReferenceImage]); // Added addReferenceImage to dependency array

  // Listen for voiceAgentCanvasUpdate events
  useSocketListener(
    'voiceAgentCanvasUpdate',
    (data: {
      type: string;
      content: string;
      x: number;
      y: number;
      width: number;
      height: number;
      taskId?: string;
      id?: string;
      roomName?: string;
      title?: string;
      source?: string;
    }) => {
      console.log('[KonvaCanvasWithModal] Received voiceAgentCanvasUpdate event:', data);

      // Accept both packets that come from the voiceAgent and packets without a source
      if (data.source && data.source !== 'voiceAgent') return;

      // Handle the event the same way as canvasElementAdd
      if (data.type === 'generic') {
        // This is a generic placeholder for a voice agent request
        // We'll add a generic placeholder to the canvas
        const taskId = data.taskId || `generic-${Date.now()}`;
        const elementId = data.id || `voice-agent-placeholder-${taskId}`;

        console.log('[KonvaCanvasWithModal] Adding generic placeholder with ID:', elementId);

        // Dispatch an event to add a generic placeholder
        const pendingEvent = new CustomEvent('attachPendingGenericToCanvas', {
          detail: {
            id: elementId,
            taskId,
            isPending: true,
            x: data.x,
            y: data.y,
            width: data.width || 400,
            height: data.height || 300,
            status: 'IN_PROGRESS',
            progress: 10,
            title: data.title || 'Processing Request...',
          },
        });
        window.dispatchEvent(pendingEvent);
        console.log('[KonvaCanvasWithModal] Dispatched attachPendingGenericToCanvas event');
      } else if (isHtmlElement(data)) {
        // Check if this is a pending content (for async processing)
        const isPending = data.content === 'pending' || data.type === 'placeholder';
        const taskId = data.taskId || `html-${Date.now()}`;

        // Create a unique ID for the element
        const elementId =
          data.id || // prefer explicit
          (data as any).placeholderId || // always present (using type assertion)
          `voice-agent-html-${taskId}`; // fallback

        console.log(
          '[KonvaCanvasWithModal] Processing voiceAgentCanvasUpdate for HTML, isPending:',
          isPending,
          'elementId:',
          elementId
        );

        if (isPending) {
          // For pending content, add a placeholder
          console.log('[KonvaCanvasWithModal] Adding pending content placeholder with ID:', elementId);

          // Dispatch an event to add a pending newsletter
          const pendingEvent = new CustomEvent('attachPendingNewsletterToCanvas', {
            detail: {
              id: elementId,
              taskId,
              isPending: true,
              x: data.x,
              y: data.y,
              width: data.width || 600,
              height: data.height || 800,
              status: 'IN_PROGRESS', // Use IN_PROGRESS to show the proper status message
              progress: 30, // Show some progress to indicate it's working
              title: data.title || (taskId.includes('-html') ? 'Generating HTML Content...' : 'Processing Request...'),
            },
          });
          window.dispatchEvent(pendingEvent);
          console.log('[KonvaCanvasWithModal] Dispatched attachPendingNewsletterToCanvas event');
        } else {
          // For regular HTML, add it directly
          console.log('[KonvaCanvasWithModal] Adding HTML element with ID:', elementId);

          // Extract HTML content from various possible locations
          let rawHtml = '';
          let remote = '';

          // Handle different content formats
          if (typeof data.content === 'string') {
            // If content is a string, use it directly
            rawHtml = data.content;
          } else if (typeof data.content === 'object' && data.content !== null) {
            // If content is an object, extract HTML from various possible locations
            const content = data.content as any; // Use any to bypass TypeScript errors

            // Check for result object
            if (content.result && typeof content.result === 'object') {
              if (content.result.html) rawHtml = content.result.html;
              else if (content.result.htmlPreview) rawHtml = content.result.htmlPreview;

              if (content.result.htmlUrl) remote = content.result.htmlUrl;
            }

            // Check for direct properties
            if (!rawHtml && content.html) rawHtml = content.html;
            else if (!rawHtml && content.htmlPreview) rawHtml = content.htmlPreview;

            if (!remote && content.htmlUrl) remote = content.htmlUrl;
          }

          // do **not** dispatch an empty payload
          if (!rawHtml && !remote) {
            console.warn('[KonvaCanvasWithModal] attachHtmlToCanvas skipped – no html nor htmlUrl');
            return;
          }

          const event = new CustomEvent('attachHtmlToCanvas', {
            detail: {
              id: elementId,
              html: rawHtml,
              htmlUrl: remote,
              x: data.x,
              y: data.y,
              width: data.width || 500,
              height: data.height || 400,
              backgroundColor: 'white',
            },
          });
          window.dispatchEvent(event);
        }
      } else if (isImageElement(data)) {
        // Handle image updates
        // Similar to the canvasElementAdd handler
        const taskId = data.taskId || `img-${Date.now()}`;
        const imageId = data.id || `voice-agent-image-${taskId}`;

        if (data.content === 'pending') {
          // For pending images
          console.log('[KonvaCanvasWithModal] Adding pending image with ID:', imageId);

          const pendingEvent = new CustomEvent('attachPendingImageToCanvas', {
            detail: {
              taskId: taskId,
              isPending: true,
              x: data.x,
              y: data.y,
              width: data.width || 300,
              height: data.height || 200,
              status: 'IN_QUEUE',
              progress: 0,
              title: data.title || 'Generating Image...',
            },
          });
          window.dispatchEvent(pendingEvent);
        } else {
          // For regular images
          console.log('[KonvaCanvasWithModal] Adding image with ID:', imageId);

          const event = new CustomEvent('attachImage', {
            detail: {
              id: imageId,
              src: data.content,
              x: data.x,
              y: data.y,
              width: data.width || 300,
              height: data.height || 200,
            },
          });
          window.dispatchEvent(event);
        }
      }
    }
  );

  // Listen for attachPendingImageToCanvas events
  useSocketListener(
    'attachPendingImageToCanvas',
    (data: {
      taskId: string;
      x: number;
      y: number;
      width: number;
      height: number;
      isPending: boolean;
      status: string;
    }) => {
      console.log('[KonvaCanvasWithModal] Received attachPendingImageToCanvas event:', data);

      // Create a DOM event to trigger the usePendingTasks hook
      const pendingEvent = new CustomEvent('attachPendingImageToCanvas', {
        detail: data,
      });

      // Dispatch the event to the window
      window.dispatchEvent(pendingEvent);
    }
  );

  // Listen for canvas element add events
  useSocketListener('canvasElementAdd' as any, (data: any) => {
    console.log('[KonvaCanvasWithModal] Received canvasElementAdd event:', data);

    try {
      const elementType = data.elementType || (data.content ? data.content.requestType : 'generic');
      const elementId = data.elementId || data.id || (data.content ? data.content.placeholderId : null) || `element-${Date.now()}`;
      const content = data.content || data;
      const taskId = content.taskId || data.taskId;
      const position = data.position || { x: 100, y: 100 };
      const size = data.size || { width: 400, height: 300 };

      console.log(`[KonvaCanvasWithModal] Processing element: ID=${elementId}, Type=${elementType}, TaskID=${taskId}`);

      if (elementType === 'content_outline_approval') {
        console.log('[KonvaCanvasWithModal] Matched content_outline_approval:', content);
        if (content.contentOutline && content.title) {
          const cardData: ContentOutlineApprovalCardData = {
            id: elementId,
            taskId: taskId,
            position: position,
            size: size,
            outline: content.contentOutline,
            status: content.status || 'pending_approval',
            wireframePlaceholderId: content.placeholderId,
          };
          // Use exported helper to add card to store
          addContentOutlineApprovalCard(cardData);
          console.log('[KonvaCanvasWithModal] Content Outline Approval Card added via store action.', cardData);
        } else {
          console.error('[KonvaCanvasWithModal] Missing contentOutline or title for content_outline_approval card.', content);
        }
        return; // Explicit return after handling this specific type
      } else if (elementType === 'interactive_wireframe') {
        console.log('[KonvaCanvasWithModal] Adding Interactive Wireframe:', content);
        if (content.wireframeHtmlUrl && content.contentOutlineUrl) {
          const wireframeData: InteractiveWireframeData = {
            id: elementId,
            x: position.x,
            y: position.y,
            width: size.width,
            height: size.height,
            elementType: 'interactive_wireframe',
            wireframeHtmlUrl: content.wireframeHtmlUrl,
            wireframeCssUrl: content.wireframeCssUrl, // Optional
            contentOutlineUrl: content.contentOutlineUrl,
            title: content.title || 'Interactive Website View',
            taskId: taskId,
          };
          addInteractiveWireframe(wireframeData); // Call action from canvas-store
          console.log('[KonvaCanvasWithModal] Interactive Wireframe added via store action.', wireframeData);
        } else {
          console.error('[KonvaCanvasWithModal] Missing wireframeHtmlUrl or contentOutlineUrl for interactive_wireframe.', content);
        }
        return; // Explicit return after handling this specific type
      }

      // === EXISTING GENERIC LOGIC FOR OTHER ELEMENT TYPES CONTINUES BELOW ===
      const placeholderId = data.elementId || content.placeholderId || data.id || 'placeholder-' + (content.taskId || data.taskId || 'task-' + Date.now());

      console.log(
        `[KonvaCanvasWithModal] canvasElementAdd (default handling for ${elementType}): placeholderId=${placeholderId}, taskId=${taskId}`
      );

      // Check if we are updating an existing placeholder for a generate_html task that is now completed.
      if (isHtmlElement(data) && content.status === 'completed') {
        const currentImages = useCanvasStore.getState().images;
        const existingPlaceholder = currentImages.find(img => img.id === placeholderId);

        if (existingPlaceholder) {
          console.log(`[KonvaCanvasWithModal] HTML task ${taskId} completed. Placeholder ${placeholderId} will be replaced with HTML content.`);
          // Remove the placeholder first
          window.dispatchEvent(new CustomEvent('removeLoadingConceptCard', { detail: { id: placeholderId } }));
          const updatedImages = currentImages.filter(img => img.id !== placeholderId);
          setImages({ images: updatedImages });
          console.log(`[KonvaCanvasWithModal] Removed placeholder ${placeholderId} for completed HTML task.`);

          // Now, dispatch attachHtmlToCanvas with the final HTML content
          let htmlContentToAttach = content.htmlContent || (content.result && content.result.htmlContent) || '';
          const cssContentToAttach = content.cssContent || (content.result && content.result.cssContent) || '';
          let htmlUrlToAttach = content.htmlUrl || (content.result && content.result.htmlUrl) || ''; // This should have the finalPopulatedHtmlUrl due to pgListener fix
          let titleToAttach = content.title || (content.result && content.result.finalTitle) || (content.result && content.result.title) || data.title || 'Generated Website';

          // Further ensure htmlUrlToAttach is prioritized if htmlContentToAttach is empty
          if (!htmlContentToAttach && content.result && content.result.finalPopulatedHtmlUrl) {
            htmlUrlToAttach = content.result.finalPopulatedHtmlUrl;
          }

          if (!htmlContentToAttach && !htmlUrlToAttach) {
            console.warn(
              `[KonvaCanvasWithModal] attachHtmlToCanvas skipped for completed task ${placeholderId} – no direct htmlContent or htmlUrl found.`
            );
            return;
          }
          console.log(`[KonvaCanvasWithModal] Creating attachHtmlToCanvas for completed task ${placeholderId} with htmlUrl: ${htmlUrlToAttach}`);
          const attachEvent = new CustomEvent('attachHtmlToCanvas', {
            detail: {
              id: placeholderId, // Use the placeholderId so HTMLElement can replace it
              html: htmlContentToAttach,
              css: cssContentToAttach,
              htmlUrl: htmlUrlToAttach,
              x: existingPlaceholder.x || position.x, // Use placeholder's position
              y: existingPlaceholder.y || position.y,
              width: existingPlaceholder.width || size.width || 500,
              height: existingPlaceholder.height || size.height || 400,
              backgroundColor: 'white',
              isUpdate: false, // Treat as initial creation of the HTML element
              taskId: taskId,
              title: titleToAttach,
            },
          });
          window.dispatchEvent(attachEvent);
          console.log(`[KonvaCanvasWithModal] Dispatched attachHtmlToCanvas for completed HTML element ID: ${placeholderId}`);
          return;
        }
      }

      // Original logic for creating a *new* loading placeholder for generate_html tasks
      if (data.elementType === 'generate_html' && (!content.htmlContent && !content.result?.htmlContent)) {
        const currentImages = useCanvasStore.getState().images;
        const existingPlaceholder = currentImages.find(img => img.id === placeholderId);

        if (existingPlaceholder) {
          console.log(`[KonvaCanvasWithModal] Placeholder already exists for task: ${placeholderId}, updating status to: ${content.status || 'pending'}`);
          const updatedImages = currentImages.map(img =>
            img.id === placeholderId
              ? { ...img, status: content.status || img.status, progress: content.progress || img.progress }
              : img
          );
          setImages({ images: updatedImages });
          return;
        }

        console.log(`[KonvaCanvasWithModal] Creating loading placeholder for generate_html task: ${placeholderId}`);
        const requestText = content.request || data.request || content.title || data.title || 'Generating website...';
        const newPlaceholder = {
          id: placeholderId,
          src: '/images/placeholder-icon.svg',
          x: position.x,
          y: position.y,
          width: size.width || 272,
          height: size.height || 400,
          isPending: true,
          status: content.status || 'pending',
          progress: content.progress || 0,
          taskId: taskId,
          requestText: requestText,
          type: 'generate_html'
        };
        const updatedImages = [...currentImages, newPlaceholder];
        setImages({ images: updatedImages });
        console.log(`[KonvaCanvasWithModal] Added loading placeholder to images array: ${placeholderId}`);
        return;
      }

      if (data.elementType === 'image' && content.src) {
        console.log(`[KonvaCanvasWithModal] Adding image to canvas: ${content.src}`);
        const imageIndex = content.imageIndex !== undefined ? content.imageIndex : 0;
        const totalImages = content.totalImages || 1;
        const uniqueImageId =
          totalImages > 1 ? `${placeholderId}-img-${imageIndex}` : placeholderId;
        console.log(
          `[KonvaCanvasWithModal] Using unique image ID: ${uniqueImageId} (index ${imageIndex} of ${totalImages})`
        );
        const img = new window.Image();
        img.onload = () => {
          console.log(
            `[KonvaCanvasWithModal] Image loaded with natural dimensions: ${img.naturalWidth}x${img.naturalHeight}`
          );
          let imageWidth, imageHeight;
          if (img.naturalWidth > 0 && img.naturalHeight > 0) {
            const aspectRatio = img.naturalWidth / img.naturalHeight;
            const maxSize = 800;
            if (img.naturalWidth > img.naturalHeight && img.naturalWidth > maxSize) {
              imageWidth = maxSize;
              imageHeight = maxSize / aspectRatio;
            } else if (img.naturalHeight > maxSize) {
              imageHeight = maxSize;
              imageWidth = maxSize * aspectRatio;
            } else {
              imageWidth = img.naturalWidth;
              imageHeight = img.naturalHeight;
            }
          } else {
            imageWidth = size?.width || DEFAULT_IMAGE_WIDTH;
            imageHeight = size?.height || DEFAULT_IMAGE_HEIGHT;
          }
          console.log(`[KonvaCanvasWithModal] Using calculated dimensions: ${imageWidth}x${imageHeight}`);
          const event = new CustomEvent('updateCanvasImage', {
            detail: {
              id: uniqueImageId,
              src: content.src,
              x: position.x,
              y: position.y,
              width: imageWidth,
              height: imageHeight,
              imageIndex: imageIndex,
              totalImages: totalImages,
              taskId: taskId,
            },
          });
          window.dispatchEvent(event);
          canvasEventBus.publish('updateCanvasImage', { id: uniqueImageId, src: content.src, x: position.x, y: position.y, width: imageWidth, height: imageHeight });
        };
        img.onerror = () => {
          console.error(`[KonvaCanvasWithModal] Failed to load image: ${content.src}`);
          const fallbackSize = size || { width: DEFAULT_IMAGE_WIDTH, height: DEFAULT_IMAGE_HEIGHT };
          const event = new CustomEvent('updateCanvasImage', {
            detail: {
              id: uniqueImageId,
              src: content.src,
              x: position.x,
              y: position.y,
              width: fallbackSize.width,
              height: fallbackSize.height,
              imageIndex: imageIndex,
              totalImages: totalImages,
              taskId: taskId,
            },
          });
          window.dispatchEvent(event);
          canvasEventBus.publish('updateCanvasImage', { id: uniqueImageId, src: content.src, x: position.x, y: position.y, width: fallbackSize.width, height: fallbackSize.height });
        };
        img.src = content.src;
        console.log(`[KonvaCanvasWithModal] Started loading image: ${content.src.substring(0, 50)}...`);
        console.log(`[KonvaCanvasWithModal] Image will be positioned at:`, position);
        return;
      }

      if (isHtmlElement(data as any) || elementType === 'html' || elementType === 'newsletter') {
        console.log(`[KonvaCanvasWithModal] Adding HTML element to canvas, placeholderId: ${placeholderId}`);
        window.dispatchEvent(new CustomEvent('removeLoadingConceptCard', { detail: { id: placeholderId } }));
        console.log(`[KonvaCanvasWithModal] Dispatched removeLoadingConceptCard for ID: ${placeholderId}`);
        const currentImages = useCanvasStore.getState().images;
        const placeholderIndex = currentImages.findIndex(img => img.id === placeholderId);
        if (placeholderIndex !== -1) {
          const updatedImages = currentImages.filter(img => img.id !== placeholderId);
          setImages({ images: updatedImages });
          console.log(`[KonvaCanvasWithModal] Removed placeholder from images array: ${placeholderId}`);
        }

        let htmlContent = content.htmlContent || (content.result && content.result.htmlContent) || '';
        const cssContent = content.cssContent || (content.result && content.result.cssContent) || '';
        let htmlUrl = content.htmlUrl || (content.result && content.result.htmlUrl) || '';
        let title = content.title || (content.result && content.result.title) || data.title || 'Generated Content';

        if (typeof content === 'string') {
          htmlContent = content;
        } else if (typeof content === 'object' && content !== null) {
          if (content.result && typeof content.result === 'object') {
            if (content.result.htmlContent) htmlContent = content.result.htmlContent;
            if (content.result.htmlUrl) htmlUrl = content.result.htmlUrl;
            if (content.result.title) title = content.result.title;
          }
          if (!htmlContent && content.htmlContent) htmlContent = content.htmlContent;
          if (!htmlUrl && content.htmlUrl) htmlUrl = content.htmlUrl;
          if (content.title && !(content.result && content.result.title)) title = content.title;
        }

        if (!htmlContent && !htmlUrl) {
          console.warn(
            `[KonvaCanvasWithModal] attachHtmlToCanvas skipped for ${placeholderId} – no direct htmlContent or htmlUrl found in canvasElementAdd event.`
          );
          return;
        }

        console.log(`[KonvaCanvasWithModal] Creating attachHtmlToCanvas event for ${placeholderId}`);
        const event = new CustomEvent('attachHtmlToCanvas', {
          detail: {
            id: placeholderId,
            html: htmlContent,
            css: cssContent,
            htmlUrl: htmlUrl,
            x: position.x,
            y: position.y,
            width: size.width || 500,
            height: size.height || 400,
            backgroundColor: 'white',
            isUpdate: false,
            taskId: taskId,
            title: title,
          },
        });
        window.dispatchEvent(event);
        console.log(`[KonvaCanvasWithModal] Dispatched attachHtmlToCanvas event for element ID: ${placeholderId}`);
        return;
      }

      console.warn(`[KonvaCanvasWithModal] canvasElementAdd event for unhandled or incompletely processed type: ${elementType} with ID ${elementId}. Payload:`, data);

    } catch (error) {
      console.error('[KonvaCanvasWithModal] Error processing canvasElementAdd event:', error);
    }
  });

  // Listen for canvas element remove events
  useSocketListener('canvasElementRemove', (data: { id?: string; elementId?: string; debug?: boolean }) => {
    console.log('[KonvaCanvasWithModal] Received canvasElementRemove event:', data);

    // Create a DOM event to remove the element from the canvas
    const removeEvent = new CustomEvent('removeCanvasElement', {
      detail: {
        id: data.id ?? data.elementId,
        debug: data.debug || false,
      },
    });

    // Dispatch the event to the window
    window.dispatchEvent(removeEvent);
  });

  // Listen for canvas element update events
  useSocketListener('canvasElementUpdate' as any, (data: any) => {
    // Accept both packets that come from the voiceAgent and packets without a source
    if (data.source && data.source !== 'voiceAgent') {
      return;
    }

    console.log('[KonvaCanvasWithModal] Received canvasElementUpdate event:', data);

    // Handle HTML image updates
    if (
      data.content &&
      (data.content.type === 'image_update' || data.content.type === 'images_completed') &&
      data.content.imageResults
    ) {
      console.log(
        `[KonvaCanvasWithModal] Received HTML image update for element ${data.id || (data.content && data.content.placeholderId)}`
      );

      // Get the HTML element from the canvas store
      const htmlElements = useCanvasStore.getState().htmlElements;
      const elementId = data.id || (data.content && data.content.placeholderId);
      const htmlElement = htmlElements.find((el) => el.id === elementId);

      if (htmlElement && htmlElement.htmlUrl) {
        console.log(`[KonvaCanvasWithModal] Found HTML element with ID ${htmlElement.id}, updating images`);

        // Handle specific HTML image updates
        if (data.content.type === 'image_update') {
          const { targetImageId, updatedImageUrl } = data.content;

          if (targetImageId && updatedImageUrl) {
            console.log(
              `[KonvaCanvasWithModal] Dispatching updateHtmlImage event for element ${htmlElement.id}, image ${targetImageId}`
            );

            // Dispatch a specific event for the HTMLElement to catch
            window.dispatchEvent(
              new CustomEvent('updateHtmlImage', {
                detail: {
                  htmlElementId: htmlElement.id, // ID of the parent HTML element on canvas
                  targetImageId: targetImageId, // ID of the <img> tag inside the iframe
                  updatedImageUrl: updatedImageUrl, // The new src value
                },
              })
            );
          } else {
            console.warn('[KonvaCanvasWithModal] Incomplete image_update data received:', data);
          }
        }

        // Show a completion notification when all images are done
        if (data.content.type === 'images_completed') {
          console.log(`[KonvaCanvasWithModal] All ${data.content.imageResults.length} images have been generated`);
        }
      }
    }

    // Check if this is a placeholder transition event (no content but has status and type)
    if (data.id && data.id.startsWith('voice-agent-placeholder-') && data.status && !data.content) {
      console.log('[KonvaCanvasWithModal] Received placeholder transition event:', data);

      // Extract the task ID from the element ID
      const taskId = data.taskId || data.id.replace('voice-agent-placeholder-', '');

      // Dispatch an event to update the placeholder type
      const updateEvent = new CustomEvent('updatePendingGenericProgress', {
        detail: {
          taskId,
          status: data.status,
          progress: data.progress || 50,
          type: data.type,
          title: data.type === 'image' ? 'Generating Image...' : 'Processing...',
        },
      });

      console.log('[KonvaCanvasWithModal] Dispatching updatePendingGenericProgress event for placeholder transition:', {
        taskId,
        status: data.status,
        progress: data.progress || 50,
        type: data.type,
      });

      window.dispatchEvent(updateEvent);
    }
    // Dispatch an event to update the element on the canvas
    else if (isHtmlElement(data) && data.content) {
      // Update HTML element on canvas
      console.log('[KonvaCanvasWithModal] Updating HTML element with ID:', data.id);
      console.log('[KonvaCanvasWithModal] Data object:', {
        id: data.id,
        type: data.type,
        elementType: (data as any).elementType,
        taskId: data.taskId,
        source: data.source,
        contentLength: data.content ? (typeof data.content === 'string' ? data.content.length : 'object') : 0,
      });

      // Extract HTML content from various possible locations
      let rawHtml = '';
      let remote = '';

      // Handle different content formats
      if (typeof data.content === 'string') {
        // If content is a string, use it directly
        rawHtml = data.content;
      } else if (typeof data.content === 'object' && data.content !== null) {
        // If content is an object, extract HTML from various possible locations
        const content = data.content as any; // Use any to bypass TypeScript errors

        // Check for result object
        if (content.result && typeof content.result === 'object') {
          if (content.result.html) rawHtml = content.result.html;
          else if (content.result.htmlPreview) rawHtml = content.result.htmlPreview;

          if (content.result.htmlUrl) remote = content.result.htmlUrl;
        }

        // Check for direct properties
        if (!rawHtml && content.html) rawHtml = content.html;
        else if (!rawHtml && content.htmlPreview) rawHtml = content.htmlPreview;

        if (!remote && content.htmlUrl) remote = content.htmlUrl;
      }

      // Check if this is for a generic placeholder
      if (data.id && data.id.includes('voice-agent-placeholder')) {
        // Extract the task ID from the element ID or use the provided taskId
        const taskId = data.taskId || data.id.replace('voice-agent-placeholder-', '');
        const elementId = data.id;

        console.log('[KonvaCanvasWithModal] Preparing to handle HTML content for generic placeholder:', elementId);
        console.log('[KonvaCanvasWithModal] Task ID:', taskId);
        console.log('[KonvaCanvasWithModal] HTML content preview:', rawHtml.substring(0, 100) + '...');
        console.log('[KonvaCanvasWithModal] HTML content length:', rawHtml.length || 0);

        // do **not** dispatch an empty payload
        if (!rawHtml && !remote) {
          console.warn('[KonvaCanvasWithModal] attachHtmlToCanvas skipped – no html nor htmlUrl');
          return;
        }

        // Create a custom event to update the HTML on the canvas
        const event = new CustomEvent('attachHtmlToCanvas', {
          detail: {
            id: elementId,
            html: rawHtml,
            htmlUrl: remote,
            backgroundColor: 'white',
          },
        });

        // Dispatch the event to update the HTML on the canvas
        window.dispatchEvent(event);
        console.log('[KonvaCanvasWithModal] Dispatched attachHtmlToCanvas event for generic placeholder:', elementId);
      }
      // Check if this is a newsletter update
      else if (
        (data.id && data.id.includes('voice-agent-newsletter')) ||
        (data.taskId && data.taskId.includes('-html'))
      ) {
        // Extract the task ID from the element ID or use the provided taskId
        const taskId = data.taskId || data.id.replace('voice-agent-newsletter-', '');
        const elementId = data.id || `voice-agent-html-${taskId}`;

        // Dispatch an event to update the pending newsletter
        console.log(
          '[KonvaCanvasWithModal] Preparing to dispatch updatePendingNewsletterProgress event for task:',
          taskId
        );
        console.log('[KonvaCanvasWithModal] Element ID:', elementId);
        console.log('[KonvaCanvasWithModal] HTML content preview:', rawHtml.substring(0, 100) + '...');
        console.log('[KonvaCanvasWithModal] HTML content length:', rawHtml.length || 0);

        // First, dispatch an event to update the pending newsletter
        const pendingUpdateEvent = new CustomEvent('updatePendingNewsletterProgress', {
          detail: {
            taskId,
            progress: 100,
            status: 'COMPLETED',
            html: rawHtml,
            htmlUrl: remote,
          },
        });
        window.dispatchEvent(pendingUpdateEvent);
        console.log('[KonvaCanvasWithModal] Dispatched updatePendingNewsletterProgress event for task:', taskId);

        // do **not** dispatch an empty payload
        if (!rawHtml && !remote) {
          console.warn('[KonvaCanvasWithModal] attachHtmlToCanvas skipped – no html nor htmlUrl');
          return;
        }

        // Also dispatch attachHtmlToCanvas event to ensure the HTML is displayed
        const event = new CustomEvent('attachHtmlToCanvas', {
          detail: {
            id: elementId,
            html: rawHtml,
            htmlUrl: remote,
            backgroundColor: 'white',
          },
        });
        window.dispatchEvent(event);
        console.log('[KonvaCanvasWithModal] Dispatched attachHtmlToCanvas event for newsletter:', elementId);
      }
      // For any other HTML element
      else {
        // Create a custom event to update the HTML on the canvas
        const event = new CustomEvent('attachHtmlToCanvas', {
          detail: {
            id: data.id,
            html: rawHtml,
            htmlUrl: remote,
            backgroundColor: 'white',
          },
        });

        // Dispatch the event to update the HTML on the canvas
        window.dispatchEvent(event);
        console.log('[KonvaCanvasWithModal] Dispatched attachHtmlToCanvas event for element update:', data.id);
      }

      console.log('[KonvaCanvasWithModal] HTML content update complete');
    } else if (isImageElement(data)) {
      // First, try to directly update any pending images with this task ID
      // This is a more reliable approach than using events
      const taskId = data.taskId || data.id.replace('voice-agent-image-', '');
      const pendingUpdateEvent = new CustomEvent('updatePendingImageProgress', {
        detail: {
          taskId: taskId,
          progress: 100,
          status: 'COMPLETED',
          imageUrl: data.content,
        },
      });
      window.dispatchEvent(pendingUpdateEvent);

      // Also dispatch the regular update event as a fallback
      const event = new CustomEvent('updateCanvasImage', {
        detail: {
          id: data.id,
          src: data.content,
        },
      });
      window.dispatchEvent(event);
    } else if (data.type === 'error') {
      // Handle error updates
      console.error('[KonvaCanvasWithModal] Error update received:', data.content);

      // If there's a taskId, update the corresponding task
      if (data.taskId) {
        if (data.taskId.includes('-html')) {
          // Update HTML task
          const pendingUpdateEvent = new CustomEvent('updatePendingNewsletterProgress', {
            detail: {
              taskId: data.taskId,
              progress: 0,
              status: 'ERROR',
              error: data.content,
            },
          });
          window.dispatchEvent(pendingUpdateEvent);
        } else if (data.taskId.includes('-img')) {
          // Update image task
          const pendingUpdateEvent = new CustomEvent('updatePendingImageProgress', {
            detail: {
              taskId: data.taskId,
              progress: 0,
              status: 'ERROR',
              error: data.content,
            },
          });
          window.dispatchEvent(pendingUpdateEvent);
        }
      }
    }
  });



  // Handler for saving the edited image
  const handleSaveEditedImage = useCallback(
    (newImageUrl: string) => {
      if (selectedImageForRemoval) {
        // Update the image in the canvas
        // We'll need to dispatch an event to update the image in the canvas
        const event = new CustomEvent('updateCanvasImage', {
          detail: {
            id: selectedImageForRemoval.id,
            src: newImageUrl,
          },
        });
        window.dispatchEvent(event);

        // Close the modal
        handleCloseObjectRemovalModal();
      }
    },
    [selectedImageForRemoval, handleCloseObjectRemovalModal]
  );

  // Expose the openObjectRemovalEditor function globally
  useEffect(() => {
    // Add the function to the window object
    (window as any).openObjectRemovalEditor = handleOpenObjectRemovalEditor;

    // Clean up when component unmounts
    return () => {
      delete (window as any).openObjectRemovalEditor;
    };
  }, [handleOpenObjectRemovalEditor]);

  // Get the model ID from localStorage
  const modelId = localStorage.getItem('selectedModelId');

  return (
    <>
      {/* Task Listener component to handle socket events */}
      <TaskListener />

      {/* Direct WebSocket Listener */}
      <DirectWebSocketListener />

      {/* Concept Card Creator */}
      <ConceptCardCreator userId={1} modelId={modelId} />

      {/* Reference Bar */}
      <ReferenceBar modelId={modelId || undefined} />

      <KonvaCanvas updateCursorPosition={updateCursorPosition}>{children}</KonvaCanvas>

      {/* Object Removal Modal */}
      {showObjectRemovalModal && selectedImageForRemoval && (
        <ObjectRemovalModal
          image={selectedImageForRemoval}
          onClose={handleCloseObjectRemovalModal}
          onSave={handleSaveEditedImage}
        />
      )}

      {/* Image Context Menu - RENDER THIS BASED ON NEW STATE */}
      {contextMenuVisible && contextMenuData && (
        <ImageContextMenu
          isVisible={contextMenuVisible}
          position={contextMenuPosition}
          imageId={contextMenuData.id}
          imageSrc={contextMenuData.src}
          imageWidth={contextMenuData.width}
          imageHeight={contextMenuData.height}
          isReference={contextMenuData.isReference}
          connectedEdges={contextMenuData.connectedEdges}
          onSetAsReference={(id) => {
            handleSetAsReference(id);
            setContextMenuVisible(false);
          }}
          onRemoveImage={(id) => {
            handleRemoveImage(id);
            setContextMenuVisible(false);
          }}
          onOpenObjectRemovalEditor={(imgData) => {
            handleOpenObjectRemovalEditor(imgData);
            setContextMenuVisible(false);
          }}
          onClose={() => setContextMenuVisible(false)}
        />
      )}



      {/* Image edit menu is now handled by the URLImage component */}
    </>
  );
};

export default KonvaCanvasWithModal;
