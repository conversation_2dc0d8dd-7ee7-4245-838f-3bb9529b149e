import React from 'react';
import { getPaginatedOrganizations, useQuery } from 'wasp/client/operations';
import { useState, useEffect } from 'react';
import LoadingSpinner from './LoadingSpinner';

const OrganizationTable = () => {
  const [skip, setskip] = useState(0);
  const [page, setPage] = useState(1);
  const { data, isLoading, error } = useQuery(getPaginatedOrganizations, {
    skip,
  });

  console.log({ data });

  useEffect(() => {
    setPage(1);
  }, []);

  useEffect(() => {
    setskip((page - 1) * 10);
  }, [page]);

  return (
    <div className='flex flex-col gap-4'>
      <div className='rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark'>
        <div className='flex-col flex items-start justify-between p-6 gap-3 w-full bg-gray-100/40 dark:bg-gray-700/50'>
          <div className='flex items-center justify-between gap-3 w-full px-2'>
            <div className='relative flex items-center gap-3 '></div>
            {!isLoading && (
              <div className='max-w-60'>
                <span className='text-md mr-2 text-black dark:text-white'>page</span>
                <input
                  type='number'
                  value={page}
                  min={1}
                  max={data?.totalPages}
                  onChange={(e) => {
                    setPage(parseInt(e.currentTarget.value));
                  }}
                  className='rounded-md border-1 border-stroke bg-transparent  px-4 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary'
                />
                <span className='text-md text-black dark:text-white'> / {data?.totalPages} </span>
              </div>
            )}
          </div>
        </div>

        <div className='grid grid-cols-12 border-t-4  border-stroke py-4.5 px-4 dark:border-strokedark md:px-6 '>
          <div className='col-span-3 flex items-center'>
            <p className='font-medium'>Name</p>
          </div>
          <div className='col-span-1 flex items-center'>
            <p className='font-medium'>Members</p>
          </div>
          <div className='col-span-1 flex items-center'>
            <p className='font-medium'>Brandkits</p>
          </div>

          <div className='col-span-1 flex items-center'>
            <p className='font-medium'>Audience</p>
          </div>
          <div className='col-span-1 flex items-center'>
            <p className='font-medium'>Characters</p>
          </div>

          <div className='col-span-1 flex items-center'>
            <p className='font-medium'>Products</p>
          </div>
          <div className='col-span-2 flex items-center'>
            <p className='font-medium'>Projects</p>
          </div>
        </div>
        {isLoading && (
          <div className='-mt-40'>
            <LoadingSpinner />
          </div>
        )}
        {!!data?.organizations &&
          data?.organizations?.length > 0 &&
          data.organizations.map((org: any) => (
            <div
              key={org.id}
              className='grid grid-cols-12 gap-4 border-t border-stroke py-4.5 px-4 dark:border-strokedark  md:px-6 '
            >
              <div className='col-span-3 flex items-center'>
                <div className='flex flex-col gap-1 '>
                  <p className='text-sm text-black dark:text-white'>{org.name}</p>
                  <p className='text-sm text-black dark:text-white'>{org.memberships[0].user.email}</p>
                </div>
              </div>

              <div className='col-span-1 hidden items-center sm:flex'>
                <p className='text-sm text-black dark:text-white'>{org?._count?.memberships ?? 0}</p>
              </div>
              <div className='col-span-1 hidden items-center sm:flex'>
                <p className='text-sm text-black dark:text-white'>{org?._count?.brandKits ?? 0}</p>
              </div>
              <div className='col-span-1 hidden items-center sm:flex'>
                <p className='text-sm text-black dark:text-white'>{org?._count?.audiences ?? 0}</p>
              </div>
              <div className='col-span-1 hidden items-center sm:flex'>
                <p className='text-sm text-black dark:text-white'>{org?._count?.characters ?? 0}</p>
              </div>
              <div className='col-span-1 hidden items-center sm:flex'>
                <p className='text-sm text-black dark:text-white'>{org?._count?.assets ?? 0}</p>
              </div>
              <div className='col-span-1 hidden items-center sm:flex'>
                <p className='text-sm text-black dark:text-white'>{org?._count?.products ?? 0}</p>
              </div>
              <div className='col-span-1 hidden items-center sm:flex'>
                <p className='text-sm text-black dark:text-white'>{org?._count?.projects ?? 0}</p>
              </div>
              <div className='col-span-2 flex items-center'></div>
            </div>
          ))}
      </div>
    </div>
  );
};

export default OrganizationTable;
