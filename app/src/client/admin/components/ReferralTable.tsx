import React, { useState } from 'react';
import { getReferredUsers, giveReferralCredits, useAction, useQuery } from 'wasp/client/operations';
import LoadingSpinner from './LoadingSpinner';
import { Button } from '../../components/ui/index';
import { formatDate } from '../../lib/utils';

const ReferralTable = () => {
  const [status, setStatus] = useState<'ACTIVE' | 'CLAIMED'>('ACTIVE');
  const { data, isLoading } = useQuery(getReferredUsers, {
    status,
  });
  const giveReferralCreditsAction = useAction(giveReferralCredits);

  return (
    <div className='flex flex-col gap-4'>
      <div className='rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark'>
        <div className='flex-col flex items-start justify-between p-6 gap-3 w-full bg-gray-100/40 dark:bg-gray-700/50'>
          <div className='flex items-center justify-between gap-3 w-full px-2'>
            <div className='relative flex items-center gap-3 '></div>

            <div className='flex items-center gap-2'>
              <label htmlFor='isAdmin-filter' className='block text-sm ml-2 text-gray-700 dark:text-white'>
                Status:
              </label>
              <select
                onChange={(e) => {
                  const value = e.target.value;
                  setStatus(value as any);
                }}
                className='relative z-20 w-full appearance-none rounded border border-stroke bg-white p-2 pl-4 pr-8  outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input'
              >
                <option value='ACTIVE'>Active</option>
                <option value='CLAIMED'>Claimed</option>
              </select>
            </div>
          </div>
        </div>

        <div className='grid grid-cols-12 border-t-4  border-stroke py-4.5 px-4 dark:border-strokedark md:px-6 '>
          <div className='col-span-3 flex items-center'>
            <p className='font-medium'>Referred</p>
          </div>
          <div className='col-span-3 flex items-center'>
            <p className='font-medium'>Inviter</p>
          </div>
          <div className='col-span-2 flex items-center'>
            <p className='font-medium'>Status</p>
          </div>

          <div className='col-span-1 flex items-center'>
            <p className='font-medium'>Claimed at</p>
          </div>
          <div className='col-span-2 flex items-center'></div>
        </div>
        {isLoading && (
          <div className='-mt-40'>
            <LoadingSpinner />
          </div>
        )}
        {!!data?.referredUsers &&
          data?.referredUsers?.length > 0 &&
          data.referredUsers.map((referredUser: any) => (
            <div
              key={referredUser.id}
              className='grid grid-cols-12 gap-4 border-t border-stroke py-4.5 px-4 dark:border-strokedark  md:px-6 '
            >
              <div className='col-span-3 flex items-center'>
                <div className='flex flex-col gap-1 '>
                  <p className='text-sm text-black dark:text-white'>{referredUser.claimer.email}</p>
                </div>
              </div>

              <div className='col-span-3 hidden items-center sm:flex'>
                <p className='text-sm text-black dark:text-white'>{referredUser.inviter.email}</p>
              </div>
              <div className='col-span-2 hidden items-center sm:flex'>
                <p className='text-sm text-black dark:text-white'>{referredUser.status}</p>
              </div>
              <div className='col-span-1 hidden items-center sm:flex'>
                <p className='text-sm text-black dark:text-white'>{formatDate(new Date(referredUser.claimedAt))}</p>
              </div>
              <div className='col-span-2 flex items-center justify-end'>
                {referredUser.status === 'ACTIVE' && (
                  <Button
                    onClick={() => {
                      giveReferralCreditsAction({ claimId: referredUser.id });
                    }}
                  >
                    Give 10 credits
                  </Button>
                )}
              </div>
            </div>
          ))}
      </div>
    </div>
  );
};

export default ReferralTable;
