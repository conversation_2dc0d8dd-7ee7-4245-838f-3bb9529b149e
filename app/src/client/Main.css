@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --colors-waspYellow: #676D50 !important;
  --colors-brand: var(--colors-waspYellow) !important;
  --colors-submitButtonText: white !important;
}




@layer base {
  /* Light mode by default */
  :root {
    @apply bg-[#F0EFE9] text-black;
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  :root a {
    cursor: pointer;
  }
  :root input[type="text"],
  :root textarea {
    cursor: text;
  }
  :root button,
  :root [role="button"] {
    cursor: pointer;
  }
  :root [draggable="true"] {
    cursor: move;
  }
  :root .resize-x {
    cursor: ew-resize;
  }
  :root .resize-y {
    cursor: ns-resize;
  }
  :root .resize-nw,
  :root .resize-se {
    cursor: nw-resize;
  }
  :root .resize-ne,
  :root .resize-sw {
    cursor: ne-resize;
  }
  :root .help {
    cursor: help;
  }
  :root .busy {
    cursor: progress;
  }
  :root .not-allowed {
    cursor: not-allowed;
  }

  /* Dark mode class-based */
  :root.dark {
    @apply bg-black text-white;
  }

  /* Apply dark mode styles to all pages */
  body {
    @apply bg-[#F0EFE9] dark:bg-black text-black dark:text-white;
  }


  /* Style inputs and buttons */
  input, textarea, select {
    @apply bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600;
  }

  button {
    @apply bg-white dark:bg-black text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-900;
  }
  .dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 98%;
    --card: 0 0% 0%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 8%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 8%;
    --muted-foreground: 0 0% 65%;
    --accent: 0 0% 8%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 8%;
    --input: 0 0% 8%;
    --ring: 0 0% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --colors-waspYellow: #676D50 !important;
  }
}
