import { ImageFile } from '../types';

const TARGET_FILE_SIZE = 6 * 1024 * 1024; // 6MB
const MAX_WIDTH = 1800;

async function compressImage(file: File): Promise<File> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    img.onload = async () => {
      // Calculate new dimensions while maintaining aspect ratio
      let width = img.width;
      let height = img.height;

      if (width > MAX_WIDTH) {
        height = (height * MAX_WIDTH) / width;
        width = MAX_WIDTH;
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);

      // Start with high quality
      let quality = 0.9;
      let compressedBlob: Blob | null = null;
      let attempts = 0;
      const maxAttempts = 5;

      // Compress until file size is under target or max attempts reached
      while (attempts < maxAttempts) {
        compressedBlob = await new Promise((resolve) => {
          canvas.toBlob((blob) => resolve(blob), 'image/jpeg', quality);
        });

        if (compressedBlob && compressedBlob.size <= TARGET_FILE_SIZE) {
          break;
        }

        // Reduce quality for next attempt
        quality = Math.max(0.5, quality - 0.1);
        attempts++;
      }

      if (!compressedBlob) {
        reject(new Error('Failed to compress image'));
        return;
      }

      // Create new file from blob
      const compressedFile = new File([compressedBlob], file.name, {
        type: 'image/jpeg',
        lastModified: Date.now(),
      });

      resolve(compressedFile);
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    img.src = URL.createObjectURL(file);
  });
}

export const urlToFile = async (url: string): Promise<ImageFile | null> => {
  try {
    console.log('Converting URL to file:', url);
    const response = await fetch(url);
    if (!response.ok) {
      console.error('Failed to fetch image:', response.statusText);
      return null;
    }

    const blob = await response.blob();
    const fileName = url.split('/').pop() || 'image.jpg';
    const file = new File([blob], fileName, { type: blob.type }) as ImageFile;

    // Compress if needed
    if (file.size > TARGET_FILE_SIZE) {
      console.log(`Compressing image from ${(file.size / 1024 / 1024).toFixed(2)}MB`);
      const compressedFile = await compressImage(file);
      console.log(`Compressed to ${(compressedFile.size / 1024 / 1024).toFixed(2)}MB`);
      (compressedFile as ImageFile).preview = URL.createObjectURL(compressedFile);
      return compressedFile as ImageFile;
    }

    file.preview = URL.createObjectURL(blob);
    console.log('Successfully converted URL to file:', { fileName, preview: file.preview });
    return file;
  } catch (error) {
    console.error('Error converting URL to File:', error);
    return null;
  }
};

export const urlsToFiles = async (urls: string[]): Promise<ImageFile[]> => {
  console.log('Converting URLs to files:', urls);
  if (!urls || urls.length === 0) {
    console.log('No URLs provided');
    return [];
  }

  try {
    // Process URLs in batches of 3
    const batchSize = 3;
    const results: ImageFile[] = [];

    for (let i = 0; i < urls.length; i += batchSize) {
      const batch = urls.slice(i, i + batchSize);
      const filePromises = batch.map((url) => {
        if (!url) {
          console.warn('Invalid URL provided:', url);
          return null;
        }
        return urlToFile(url);
      });

      const batchResults = await Promise.all(filePromises);
      const validFiles = batchResults.filter((file): file is ImageFile => {
        if (!file) {
          console.warn('Failed to convert URL to file');
          return false;
        }
        return true;
      });

      results.push(...validFiles);
    }

    console.log('Successfully converted URLs to files:', results);
    return results;
  } catch (error) {
    console.error('Error converting URLs to files:', error);
    return [];
  }
};

export const createFilePreview = async (file: File): Promise<ImageFile> => {
  console.log('Creating file preview for:', file.name);

  let processedFile: File = file;

  // Compress if needed
  if (file.size > TARGET_FILE_SIZE) {
    console.log(`Compressing image from ${(file.size / 1024 / 1024).toFixed(2)}MB`);
    processedFile = await compressImage(file);
    console.log(`Compressed to ${(processedFile.size / 1024 / 1024).toFixed(2)}MB`);
  }

  const imageFile = processedFile as ImageFile;
  imageFile.preview = URL.createObjectURL(processedFile);
  console.log('Created preview URL:', imageFile.preview);
  return imageFile;
};

export const revokeFilePreviews = (files: ImageFile[]) => {
  console.log('Revoking file previews for:', files.length, 'files');
  files.forEach((file) => {
    if (file.preview) {
      URL.revokeObjectURL(file.preview);
    }
  });
};
