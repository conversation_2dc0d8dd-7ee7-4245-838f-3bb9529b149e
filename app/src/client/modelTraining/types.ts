import { Product } from 'wasp/entities';

export type TextComplexity = 'simple' | 'complex';
export type ModelProvider = 'runpod' | 'bfl';
export type ModelType = 'product' | 'style';

export interface ImageFile extends File {
  preview: string;
}

export interface ImageInput {
  fileBuffer?: string;
  fileName: string;
  contentType: string;
  url?: string;
}

export interface ProductModelFormData {
  productModelName: string;
}

export interface ImageUploaderProps {
  onImagesSelected: (files: ImageFile[]) => void;
  multiple?: boolean;
  className?: string;
}

export interface ProductSelectorProps {
  products: Product[];
  onProductSelect: (product: Product) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  showSelector: boolean;
  setShowSelector: (show: boolean) => void;
}

export interface ProductModelFormProps {
  products: Product[];
  onSubmit: (
    modelName: string,
    images: ImageInput[],
    productId?: number,
    textComplexity?: TextComplexity,
    provider?: ModelProvider,
    modelType?: ModelType
  ) => Promise<void>;
  initialProduct?: Product | null;
  initialImages?: (ImageFile | string)[];
  isSubmitting?: boolean;
  hasProAccess?: boolean;
  uploadProgress?: number;
}
