/**
 * DEPRECATED: This model training component is no longer used.
 * Model training functionality has been replaced by newer systems.
 * This file is kept for reference but should not be used in new code.
 */

import React from 'react';
import { Search, Image } from 'lucide-react';
import { ProductSelectorProps } from '../types';

export const ProductSelector: React.FC<ProductSelectorProps> = ({
  products,
  onProductSelect,
  searchQuery,
  setSearchQuery,
  showSelector,
  setShowSelector,
}) => {
  const filteredProducts =
    products?.filter(
      (product) =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.productType?.toLowerCase().includes(searchQuery.toLowerCase())
    ) || [];

  const handleClickOutside = (e: MouseEvent) => {
    const target = e.target as HTMLElement;
    if (!target.closest('.product-selector')) {
      setShowSelector(false);
    }
  };

  React.useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className='relative product-selector'>
      <div className='relative'>
        <input
          type='text'
          placeholder='Search your products...'
          value={searchQuery}
          onChange={(e) => {
            setSearchQuery(e.target.value);
            setShowSelector(true);
          }}
          onFocus={() => setShowSelector(true)}
          className='w-full px-4 py-2 pl-10 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#5a6b4b]'
        />
        <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400' size={20} />
      </div>

      {showSelector && (
        <div className='absolute z-10 mt-1 w-full max-h-[400px] overflow-auto bg-white border rounded-lg shadow-lg'>
          {filteredProducts.length > 0 ? (
            filteredProducts.map((product) => (
              <div
                key={product.id}
                className='flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b last:border-b-0'
                onClick={() => {
                  console.log('Selected product with images:', product.images);
                  onProductSelect(product);
                }}
              >
                <div className='w-16 h-16 rounded-md overflow-hidden flex-shrink-0 bg-gray-100 border'>
                  {product.images?.[0] ? (
                    <img src={product.images[0]} alt={product.name} className='w-full h-full object-contain p-1' />
                  ) : (
                    <div className='w-full h-full flex items-center justify-center'>
                      <Image className='w-6 h-6 text-gray-400' />
                    </div>
                  )}
                </div>
                <div className='ml-3 flex-grow'>
                  <div className='font-medium text-gray-900'>{product.name}</div>
                  <div className='text-sm text-gray-500 flex items-center justify-between'>
                    <span>{product.productType}</span>
                    {product.images && (
                      <span className='text-xs bg-gray-100 px-2 py-1 rounded-full'>
                        {product.images.length} {product.images.length === 1 ? 'image' : 'images'}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className='p-4 text-center text-gray-500'>No products found</div>
          )}
        </div>
      )}
    </div>
  );
};
