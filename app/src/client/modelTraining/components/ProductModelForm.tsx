/**
 * DEPRECATED: This model training component is no longer used.
 * Model training functionality has been replaced by newer systems.
 * This file is kept for reference but should not be used in new code.
 */

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Loader2, AlertCircle, Info } from 'lucide-react';
import { Product } from 'wasp/entities';
import { ProductSelector } from './ProductSelector';
import { ImageUploader } from './ImageUploader';
import { ImageGrid } from './ImageGrid';
import {
  ProductModelFormData,
  ImageFile,
  ProductModelFormProps,
  TextComplexity,
  ModelProvider,
  ModelType,
} from '../types';
import { revokeFilePreviews } from '../utils/imageHelpers';

export const ProductModelForm: React.FC<ProductModelFormProps> = ({
  products,
  onSubmit,
  initialProduct,
  initialImages = [],
  isSubmitting = false,
  hasProAccess = false,
  uploadProgress = 0,
}) => {
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(initialProduct || null);
  const [productImages, setProductImages] = useState<(ImageFile | string)[]>(initialImages);
  const [isLoadingImages, setIsLoadingImages] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSelector, setShowSelector] = useState(false);
  const [textComplexity, setTextComplexity] = useState<TextComplexity>('simple');
  const [provider, setProvider] = useState<ModelProvider>('runpod');
  const [modelType, setModelType] = useState<ModelType>('product');

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<ProductModelFormData>();

  useEffect(() => {
    if (!selectedProduct) return;

    console.log('Loading images for product:', selectedProduct);
    console.log('Product images array:', selectedProduct.images);

    setIsLoadingImages(true);
    setError(null);

    try {
      setProductImages([]);
      const images = selectedProduct.images || [];
      if (images.length === 0) {
        console.log('No images found in product');
        return;
      }
      setProductImages(images);
      setValue('productModelName', selectedProduct.name);
    } catch (error) {
      console.error('Error loading product images:', error);
      setError('Failed to load product images');
    } finally {
      setIsLoadingImages(false);
    }
  }, [selectedProduct, setValue]);

  useEffect(() => {
    return () => {
      const fileImages = productImages.filter((img): img is ImageFile => typeof img !== 'string');
      revokeFilePreviews(fileImages);
    };
  }, [productImages]);

  const handleProductSelect = (product: Product) => {
    if (modelType === 'style') return;
    console.log('Selected product:', product);
    console.log('Product images:', product.images);
    setSelectedProduct(product);
    setShowSelector(false);
    setSearchQuery('');
    setError(null);
  };

  const handleImagesSelected = (newImages: ImageFile[]) => {
    console.log('Adding new images:', newImages);
    setProductImages((prev) => [...prev, ...newImages]);
    setError(null);
  };

  const handleRemoveImage = (index: number) => {
    setProductImages((prev) => {
      const newImages = [...prev];
      const removed = newImages.splice(index, 1)[0];
      if (typeof removed !== 'string' && removed.preview) {
        URL.revokeObjectURL(removed.preview);
      }
      return newImages;
    });
  };

  const handleFormSubmit = async ({ productModelName }: ProductModelFormData) => {
    if (productImages.length === 0) {
      setError('Please add at least one image');
      return;
    }

    const minImages = modelType === 'style' ? 40 : 3;
    if (productImages.length < minImages) {
      setError(`Please add at least ${minImages} images for better model training`);
      return;
    }

    if (!productModelName) {
      setError('Model name is required');
      return;
    }

    setError(null);
    try {
      // Format images based on type (file or URL)
      const formattedImages = productImages.map((image) => {
        // If it's a URL string (from product images)
        if (typeof image === 'string') {
          return {
            url: image,
            fileName: 'image.jpg',
            contentType: 'image/jpeg',
          };
        }

        // For file uploads, we need to get the file data
        return new Promise<{ fileBuffer: string; fileName: string; contentType: string }>((resolve) => {
          const reader = new FileReader();
          reader.onloadend = () => {
            const base64data = reader.result as string;
            resolve({
              fileBuffer: base64data.split(',')[1], // Remove data URL prefix
              fileName: image.name,
              contentType: image.type,
            });
          };
          reader.readAsDataURL(image);
        });
      });

      // Wait for all file processing to complete and ensure correct types
      const processedImages = await Promise.all(
        formattedImages.map(async (img) => {
          if (img instanceof Promise) {
            return await img;
          }
          return img;
        })
      );

      await onSubmit(
        productModelName,
        processedImages,
        modelType === 'product' ? selectedProduct?.id : undefined,
        textComplexity,
        provider,
        modelType
      );
    } catch (error) {
      console.error('Error submitting form:', error);
      setError('Failed to create model');
    }
  };

  return (
    <div className='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-[#B5B178]/20 dark:border-gray-700'>
      <h2 className='text-xl font-semibold mb-4 text-[#676D50] dark:text-[#A6884C]'>
        Create {modelType === 'product' ? 'Product' : 'Style'} Model
      </h2>

      {/* Model Type Selection */}
      <div className='mb-6'>
        <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Model Type</label>
        <div className='grid grid-cols-2 gap-4'>
          <button
            type='button'
            onClick={() => setModelType('product')}
            className={`p-4 rounded-lg border-2 transition-all ${
              modelType === 'product' ? 'border-[#676D50] bg-[#676D50]/10' : 'border-gray-200 hover:border-[#676D50]/50'
            }`}
          >
            <h3 className='font-medium mb-2'>Product Model</h3>
            <p className='text-sm text-gray-600 dark:text-gray-400'>Train a model for your product photos</p>
          </button>

          <button
            type='button'
            onClick={() => {
              setModelType('style');
              setSelectedProduct(null);
            }}
            className={`p-4 rounded-lg border-2 transition-all ${
              modelType === 'style' ? 'border-[#676D50] bg-[#676D50]/10' : 'border-gray-200 hover:border-[#676D50]/50'
            }`}
          >
            <h3 className='font-medium mb-2'>Style Model</h3>
            <p className='text-sm text-gray-600 dark:text-gray-400'>Train a model for a specific visual style</p>
          </button>
        </div>
      </div>

      {/* Product Selector - Only show for product models */}
      {modelType === 'product' && (
        <div className='mb-6'>
          <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>
            Import from Product
          </label>
          <ProductSelector
            products={products}
            onProductSelect={handleProductSelect}
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            showSelector={showSelector}
            setShowSelector={setShowSelector}
          />
        </div>
      )}

      <form onSubmit={handleSubmit(handleFormSubmit)} className='space-y-6'>
        <div>
          <label htmlFor='productModelName' className='block text-sm font-medium text-[#676D50] dark:text-gray-300'>
            Model Name
          </label>
          <input
            id='productModelName'
            type='text'
            {...register('productModelName', { required: 'Model name is required' })}
            className='mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-[#676D50] focus:ring focus:ring-[#676D50] focus:ring-opacity-50 bg-white dark:bg-gray-700 dark:text-white'
            disabled={isSubmitting}
          />
          {errors.productModelName && (
            <p className='mt-1 text-sm text-red-600 dark:text-red-400'>{errors.productModelName.message}</p>
          )}
        </div>

        <div>
          <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Model Provider</label>
          <div className='grid grid-cols-2 gap-4 mb-6'>
            <button
              type='button'
              onClick={() => setProvider('runpod')}
              className={`p-4 rounded-lg border-2 transition-all ${
                provider === 'runpod' ? 'border-[#676D50] bg-[#676D50]/10' : 'border-gray-200 hover:border-[#676D50]/50'
              }`}
            >
              <h3 className='font-medium mb-2'>Base Model</h3>
              <p className='text-sm text-gray-600 dark:text-gray-400'>Standard model training using RunPod</p>
            </button>

            <button
              type='button'
              onClick={() => setProvider('bfl')}
              disabled={!hasProAccess}
              className={`p-4 rounded-lg border-2 transition-all ${
                provider === 'bfl'
                  ? 'border-[#676D50] bg-[#676D50]/10'
                  : hasProAccess
                    ? 'border-gray-200 hover:border-[#676D50]/50'
                    : 'border-gray-200 opacity-50 cursor-not-allowed'
              }`}
            >
              <h3 className='font-medium mb-2'>Pro Model</h3>
              <p className='text-sm text-gray-600 dark:text-gray-400'>
                {hasProAccess ? 'Advanced model training using BFL' : 'Contact us to enable pro model training'}
              </p>
            </button>
          </div>
        </div>

        {/* Text Complexity - Only show for product models */}
        {modelType === 'product' && (
          <div>
            <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300'>
              Product Text Complexity
            </label>

            <div className='grid grid-cols-2 gap-4'>
              <button
                type='button'
                onClick={() => setTextComplexity('simple')}
                className={`p-4 rounded-lg border-2 transition-all ${
                  textComplexity === 'simple'
                    ? 'border-[#676D50] bg-[#676D50]/10'
                    : 'border-gray-200 hover:border-[#676D50]/50'
                }`}
              >
                <div className='flex items-center justify-center mb-3'>
                  <img
                    src='/images/simple-text-example.svg'
                    alt='Simple text example'
                    className='w-32 h-32 object-contain'
                  />
                </div>
                <h3 className='font-medium mb-2'>Simple/No Text</h3>
                <p className='text-sm text-gray-600 dark:text-gray-400'>
                  Product has minimal or no text (0-2 sentences)
                </p>
              </button>

              <button
                type='button'
                onClick={() => setTextComplexity('complex')}
                className={`p-4 rounded-lg border-2 transition-all ${
                  textComplexity === 'complex'
                    ? 'border-[#676D50] bg-[#676D50]/10'
                    : 'border-gray-200 hover:border-[#676D50]/50'
                }`}
              >
                <div className='flex items-center justify-center mb-3'>
                  <img
                    src='/images/complex-text-example.svg'
                    alt='Complex text example'
                    className='w-32 h-32 object-contain'
                  />
                </div>
                <h3 className='font-medium mb-2'>Complex Text</h3>
                <p className='text-sm text-gray-600 dark:text-gray-400'>Product has significant text (3+ sentences)</p>
              </button>
            </div>
          </div>
        )}

        <div>
          <label className='block text-sm font-medium text-[#676D50] dark:text-gray-300 mb-2'>Training Images</label>

          <div className='mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md flex items-center text-blue-600 dark:text-blue-400'>
            <Info className='w-5 h-5 mr-2' />
            <span className='text-sm'>
              {modelType === 'style'
                ? 'Add at least 40 images to train your style model'
                : 'Add at least 3 images for better model training results'}
            </span>
          </div>

          {error && (
            <div className='mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md flex items-center text-red-600 dark:text-red-400'>
              <AlertCircle className='w-5 h-5 mr-2' />
              {error}
            </div>
          )}

          {isLoadingImages && (
            <div className='mb-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-md flex items-center justify-center'>
              <Loader2 className='w-5 h-5 mr-2 animate-spin text-[#676D50] dark:text-[#A6884C]' />
              <span className='text-[#676D50] dark:text-[#A6884C]'>Loading images...</span>
            </div>
          )}

          {!isLoadingImages && productImages.length > 0 && (
            <div className='mb-4'>
              <ImageGrid images={productImages} onRemoveImage={handleRemoveImage} />
            </div>
          )}

          <div className='mt-4'>
            <ImageUploader
              onImagesSelected={handleImagesSelected}
              multiple={true}
              className={isSubmitting ? 'opacity-50 pointer-events-none' : ''}
            />
          </div>
        </div>

        <div className='mt-4'>
          {uploadProgress > 0 && uploadProgress < 100 && (
            <div className='mb-4'>
              <div className='w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700'>
                <div className='bg-[#676D50] h-2.5 rounded-full' style={{ width: `${uploadProgress}%` }}></div>
              </div>
              <p className='text-sm text-gray-600 mt-1'>Uploading images: {uploadProgress}%</p>
            </div>
          )}

          <button
            type='submit'
            className={`w-full px-4 py-2 bg-[#676D50] hover:bg-[#A6884C] text-white rounded-lg transition duration-200 ${
              isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <div className='flex items-center justify-center space-x-2'>
                <Loader2 className='w-5 h-5 animate-spin' />
                <span>Creating Model...</span>
              </div>
            ) : (
              'Train Model'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};
