/**
 * DEPRECATED: This model training component is no longer used.
 * Model training functionality has been replaced by newer systems.
 * This file is kept for reference but should not be used in new code.
 */

import React, { useRef, useState } from 'react';
import { Upload, Image as ImageIcon } from 'lucide-react';
import { ImageUploaderProps } from '../types';
import { createFilePreview } from '../utils/imageHelpers';

export const ImageUploader: React.FC<ImageUploaderProps> = ({ onImagesSelected, multiple = true, className = '' }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const processFiles = async (files: File[]) => {
    setIsProcessing(true);
    try {
      const imageFiles = files.filter((file) => file.type.startsWith('image/'));
      const processedFiles = await Promise.all(imageFiles.map(createFilePreview));

      if (processedFiles.length > 0) {
        onImagesSelected(processedFiles);
      }
    } catch (error) {
      console.error('Error processing files:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    await processFiles(files);
    // Reset input so the same file can be selected again
    if (event.target) event.target.value = '';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    const files = Array.from(e.dataTransfer.files);
    await processFiles(files);
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div
      className={`
        relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer
        transition-colors duration-200
        ${isDragging ? 'border-[#5a6b4b] bg-[#5a6b4b]/5' : 'border-gray-300 hover:border-[#5a6b4b]'}
        ${isProcessing ? 'opacity-50 cursor-wait' : ''}
        ${className}
      `}
      onClick={handleClick}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <input
        ref={fileInputRef}
        type='file'
        accept='image/*'
        multiple={multiple}
        className='hidden'
        onChange={handleFileChange}
        disabled={isProcessing}
      />

      <div className='flex flex-col items-center'>
        <div className='mb-2'>
          {isDragging ? (
            <Upload className='w-8 h-8 text-[#5a6b4b]' />
          ) : isProcessing ? (
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-[#5a6b4b]' />
          ) : (
            <ImageIcon className='w-8 h-8 text-gray-400' />
          )}
        </div>
        <p className='text-sm text-gray-600 mb-1'>
          {isDragging ? 'Drop images here' : isProcessing ? 'Processing images...' : 'Click to upload or drag and drop'}
        </p>
        <p className='text-xs text-gray-500'>
          {isProcessing
            ? 'Please wait while we optimize your images'
            : multiple
              ? 'Upload multiple images'
              : 'Upload an image'}
        </p>
      </div>
    </div>
  );
};
