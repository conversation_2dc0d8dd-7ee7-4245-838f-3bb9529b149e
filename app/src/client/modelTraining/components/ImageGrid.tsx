/**
 * DEPRECATED: This model training component is no longer used.
 * Model training functionality has been replaced by newer systems.
 * This file is kept for reference but should not be used in new code.
 */

import React from 'react';
import { X } from 'lucide-react';
import { ImageFile } from '../types';

interface ImageGridProps {
  images: (ImageFile | string)[];
  onRemoveImage: (index: number) => void;
  className?: string;
}

export const ImageGrid: React.FC<ImageGridProps> = ({ images, onRemoveImage, className = '' }) => {
  return (
    <div className={`grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 ${className}`}>
      {images.map((image, index) => {
        // Get image URL based on type
        const imageUrl = typeof image === 'string' ? image : image.preview || URL.createObjectURL(image);

        return (
          <div
            key={index}
            className='relative group aspect-square bg-gray-100 rounded-lg overflow-hidden border border-gray-200'
          >
            <img
              src={imageUrl}
              alt={`Image ${index + 1}`}
              className='w-full h-full object-contain p-2'
              onLoad={() => {
                // Only revoke URL if it's a File and we created a new object URL
                if (typeof image !== 'string' && !image.preview && imageUrl !== image.preview) {
                  URL.revokeObjectURL(imageUrl);
                }
              }}
            />
            <div className='absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-200'>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onRemoveImage(index);
                }}
                className='absolute top-2 right-2 p-1 bg-red-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600'
              >
                <X className='w-4 h-4 text-white' />
              </button>
            </div>
          </div>
        );
      })}
    </div>
  );
};
