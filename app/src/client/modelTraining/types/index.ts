import { Product } from 'wasp/entities';

export type ProductModelFormData = {
  productModelName: string;
};

export type ProductModelFormProps = {
  products: Product[];
  onSubmit: (name: string, images: (ImageFile | string)[], productId?: number) => Promise<void>;
  initialProduct?: Product;
  initialImages?: string[];
  isSubmitting?: boolean;
};

export type LocationState = {
  productName?: string;
  productId?: number;
  productImages?: string[];
  fromProduct?: boolean;
};

export type ImageFile = File & {
  preview?: string;
};

// Photography Model Types
export type PhotographyModelData = {
  id: string;
  name: string;
  status: string;
  usage: number;
  performance: number;
  createdAt: Date;
  updatedAt: Date;
  trainingStart: Date | null;
  trainingEnd: Date | null;
  imageUrl: string | null;
  progress: string;
  estimatedTime: number;
  isTraining: boolean;
  userId: number;
  runpodJobId: string | null;
  productId?: number;
};

// Clothing Model Types
export type GeneratedModel = {
  id: number;
  name: string;
};

export type TryOnResult = {
  modelId: number;
  resultUrl: string;
};

// Component Props Types
export type ProductSelectorProps = {
  products: Product[];
  onProductSelect: (product: Product) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  showSelector: boolean;
  setShowSelector: (show: boolean) => void;
};

export type ImageUploaderProps = {
  onImagesSelected: (files: ImageFile[]) => void;
  multiple?: boolean;
  className?: string;
};

export type ImageGridProps = {
  images: ImageFile[];
  onRemoveImage: (index: number) => void;
  className?: string;
};

// Funny loading messages
export const funnyLoadingMessages = [
  'Processing pixels...',
  'Analyzing images...',
  'Training the model...',
  'Optimizing data...',
  'Almost there...',
  'Final touches...',
];
