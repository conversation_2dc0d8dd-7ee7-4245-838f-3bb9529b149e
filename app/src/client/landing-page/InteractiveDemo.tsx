import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiCamera, FiImage, FiCheck, FiArrowRight } from 'react-icons/fi';

const InteractiveDemoSection: React.FC = () => {
  const [step, setStep] = useState(1);
  const [selectedStyle, setSelectedStyle] = useState('');
  const [selectedAngle, setSelectedAngle] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);

  const handleGenerate = () => {
    setIsGenerating(true);
    setTimeout(() => {
      setIsGenerating(false);
      setGeneratedImage('https://via.placeholder.com/600x600.png?text=AI+Generated+Product+Photo');
    }, 3000);
  };

  const styles = [
    { id: 'cozy', name: 'Natural Light', description: 'Soft, window-lit environments' },
    { id: 'minimal', name: 'Studio Clean', description: 'Professional, controlled lighting' },
    { id: 'lifestyle', name: 'Ambient Scene', description: 'Rich, contextual settings' },
  ];

  const angles = [
    { id: 'front', name: 'Hero Shot', description: 'Striking front perspective' },
    { id: 'detail', name: 'Detail Focus', description: 'Close-up feature highlights' },
    { id: 'lifestyle', name: 'Context View', description: 'Environmental placement' },
  ];

  return (
    <section className='relative py-24 overflow-hidden'>
      {/* Gradient Overlay */}
      <div className='absolute inset-0'>
        <div
          className='absolute inset-0'
          style={{
            background:
              'radial-gradient(circle at 70% 30%, rgba(156, 131, 79, 0.08), transparent 60%), radial-gradient(circle at 30% 70%, rgba(156, 131, 79, 0.05), transparent 50%)',
          }}
        />
      </div>

      <div className='relative container mx-auto px-6'>
        <div className='max-w-5xl mx-auto'>
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className='text-center mb-16'
          >
            <h2 className='text-4xl md:text-5xl font-bold mb-6 text-[#686c54]'>Create Your Perfect Shot</h2>
            <p className='text-xl text-[#686c54]'>Experience how Olivia transforms your product vision into reality</p>
          </motion.div>

          {/* Interactive Demo Area */}
          <div className='grid grid-cols-1 lg:grid-cols-2 gap-12'>
            {/* Controls Panel */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className='space-y-8'
            >
              <AnimatePresence mode='wait'>
                {step === 1 && (
                  <motion.div
                    key='step1'
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className='space-y-6'
                  >
                    <h3 className='text-2xl font-semibold text-[#686c54] mb-6'>Choose Your Style</h3>
                    <div className='grid gap-4'>
                      {styles.map((style) => (
                        <motion.button
                          key={style.id}
                          onClick={() => {
                            setSelectedStyle(style.id);
                            setTimeout(() => setStep(2), 500);
                          }}
                          className={`group relative w-full p-6 rounded-lg border transition-all duration-300 ${
                            selectedStyle === style.id
                              ? 'border-[#9c834f] bg-white bg-opacity-50'
                              : 'border-[#686c54] hover:border-[#9c834f] bg-white bg-opacity-5'
                          }`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <div className='flex items-center justify-between'>
                            <div className='text-left'>
                              <h4 className='text-[#686c54] font-semibold mb-1'>{style.name}</h4>
                              <p className='text-[#686c54] text-sm opacity-80'>{style.description}</p>
                            </div>
                            <FiArrowRight
                              className={`w-5 h-5 transform transition-all duration-300 ${
                                selectedStyle === style.id
                                  ? 'text-[#9c834f] translate-x-0'
                                  : 'text-[#686c54] -translate-x-2 group-hover:translate-x-0'
                              }`}
                            />
                          </div>
                        </motion.button>
                      ))}
                    </div>
                  </motion.div>
                )}

                {step === 2 && (
                  <motion.div
                    key='step2'
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className='space-y-6'
                  >
                    <h3 className='text-2xl font-semibold text-[#686c54] mb-6'>Select Your Shot</h3>
                    <div className='grid gap-4'>
                      {angles.map((angle) => (
                        <motion.button
                          key={angle.id}
                          onClick={() => {
                            setSelectedAngle(angle.id);
                            handleGenerate();
                          }}
                          className={`group relative w-full p-6 rounded-lg border transition-all duration-300 ${
                            selectedAngle === angle.id
                              ? 'border-[#9c834f] bg-white bg-opacity-50'
                              : 'border-[#686c54] hover:border-[#9c834f] bg-white bg-opacity-5'
                          }`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <div className='flex items-center justify-between'>
                            <div className='text-left'>
                              <h4 className='text-[#686c54] font-semibold mb-1'>{angle.name}</h4>
                              <p className='text-[#686c54] text-sm opacity-80'>{angle.description}</p>
                            </div>
                            <FiArrowRight
                              className={`w-5 h-5 transform transition-all duration-300 ${
                                selectedAngle === angle.id
                                  ? 'text-[#9c834f] translate-x-0'
                                  : 'text-[#686c54] -translate-x-2 group-hover:translate-x-0'
                              }`}
                            />
                          </div>
                        </motion.button>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Reset Button */}
              {step > 1 && (
                <motion.button
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  onClick={() => {
                    setStep(1);
                    setSelectedStyle('');
                    setSelectedAngle('');
                    setGeneratedImage(null);
                  }}
                  className='text-[#686c54] hover:text-[#9c834f] transition-colors duration-300'
                >
                  ← Start Over
                </motion.button>
              )}
            </motion.div>

            {/* Preview Area */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className='relative aspect-square rounded-2xl overflow-hidden'
            >
              <div className='absolute inset-0 backdrop-blur-sm bg-white bg-opacity-5 border-2 border-[#9c834f] border-opacity-20 rounded-2xl overflow-hidden'>
                {/* Gold Accent */}
                <div
                  className='absolute inset-0'
                  style={{
                    background: 'radial-gradient(circle at 30% 30%, rgba(156, 131, 79, 0.1), transparent 70%)',
                  }}
                />

                <div className='absolute inset-0 flex items-center justify-center'>
                  {!isGenerating && !generatedImage && (
                    <div className='text-center p-8'>
                      <FiCamera className='w-16 h-16 mx-auto mb-4 text-[#686c54] opacity-30' />
                      <p className='text-[#686c54] opacity-60'>Select options to generate your perfect shot</p>
                    </div>
                  )}

                  {isGenerating && (
                    <div className='text-center p-8'>
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
                      >
                        <FiImage className='w-16 h-16 mx-auto mb-4 text-[#9c834f]' />
                      </motion.div>
                      <p className='text-[#686c54]'>Creating your perfect shot...</p>
                    </div>
                  )}

                  {generatedImage && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className='relative w-full h-full'
                    >
                      <img src={generatedImage} alt='Generated Product' className='w-full h-full object-cover' />
                      <div className='absolute bottom-4 right-4'>
                        <motion.button
                          className='bg-[#686c54] hover:bg-[#9c834f] text-[#f0efe9] rounded-lg px-4 py-2 flex items-center space-x-2 transition-colors duration-300'
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <FiCheck className='w-4 h-4' />
                          <span>Download</span>
                        </motion.button>
                      </div>
                    </motion.div>
                  )}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default InteractiveDemoSection;
