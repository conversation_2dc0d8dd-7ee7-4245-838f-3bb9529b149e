import React from 'react';
import { Link } from 'wasp/client/router';
import { motion } from 'framer-motion';
import './HeroSection.css';

const HeroSection: React.FC = () => {
  return (
    <section className='relative min-h-screen flex items-center justify-center overflow-hidden'>
      {/* Content Container */}
      <div className='relative z-10 w-full max-w-7xl mx-auto px-6'>
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-12 items-center'>
          {/* Left Column - Main Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 1, delay: 0.2 }}
            className='space-y-8'
          >
            <h1 className='font-display text-6xl lg:text-7xl font-bold leading-tight'>
              <span className='text-[#686c54]'>Meet Olivia</span>
              <br />
              <span className='text-transparent bg-clip-text bg-gradient-to-r from-[#9c834f] to-[#a39374]'>
                World's First AI Designer
              </span>
            </h1>
            <p className='font-ui text-xl lg:text-2xl text-[#686c54] max-w-xl leading-relaxed'>
              Your all-in-one AI creative suite for stunning web design, product photography, and brand identity.
              Transform your brand in seconds.
            </p>

            {/* Feature List */}
            <motion.div
              className='space-y-4 text-lg text-[#686c54]'
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <div className='flex items-center space-x-3'>
                <div className='w-1.5 h-1.5 rounded-full bg-[#9c834f]' />
                <p>AI Web Design & Development</p>
              </div>
              <div className='flex items-center space-x-3'>
                <div className='w-1.5 h-1.5 rounded-full bg-[#9c834f]' />
                <p>Professional Product Photography</p>
              </div>
              <div className='flex items-center space-x-3'>
                <div className='w-1.5 h-1.5 rounded-full bg-[#9c834f]' />
                <p>Complete Brand Identity Suite</p>
              </div>
            </motion.div>

            {/* CTA Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <Link to='/signup'>
                <motion.button
                  className='group relative overflow-hidden rounded-lg bg-[#686c54] px-8 py-4 text-[#f0efe9] transition-all duration-300'
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span className='relative z-10 flex items-center text-lg font-semibold'>
                    Design with Olivia
                    <svg
                      className='ml-2 h-5 w-5 transform transition-transform duration-300 group-hover:translate-x-1'
                      fill='none'
                      stroke='currentColor'
                      viewBox='0 0 24 24'
                    >
                      <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M13 7l5 5m0 0l-5 5m5-5H6' />
                    </svg>
                  </span>
                  <div className='absolute inset-0 z-0 bg-[#9c834f] opacity-0 transition-opacity duration-300 group-hover:opacity-100' />
                </motion.button>
              </Link>
              <p className='mt-4 text-sm text-[#686c54]'>No credit card required • Free trial available</p>
            </motion.div>
          </motion.div>

          {/* Right Column - Visual Element */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.4 }}
            className='relative hidden lg:block'
          >
            <div className='relative w-full aspect-square'>
              {/* Animated Logo Container */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8, rotate: -10 }}
                animate={{
                  opacity: 1,
                  scale: 1,
                  rotate: 0,
                  transition: {
                    type: 'spring',
                    stiffness: 100,
                    damping: 15,
                  },
                }}
                className='relative w-full h-full flex items-center justify-center logo-container'
              >
                {/* Logo Image */}
                <div className='w-4/5 h-4/5'>
                  <motion.img
                    src='https://oliviatest.xyz/OptomizedAssets/logo/logo-olivia.gif'
                    alt='Olivia AI Logo'
                    className='w-full h-full'
                  />
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
