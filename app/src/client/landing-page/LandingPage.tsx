import React, { useState } from 'react';
import { Link, routes } from 'wasp/client/router';
import { useAuth } from 'wasp/client/auth';
import { motion } from 'framer-motion';
import { FiCamera, FiLayers, FiTarget, FiZap, FiUsers, FiCpu } from 'react-icons/fi';
import InteractiveDemoSection from './InteractiveDemo';
import HeroSection from './HeroSection';
import { ActiveSessionChecker } from '../../features/onboarding/infrastructure/hooks/useActiveSessionRedirect';

const NavLink: React.FC<{ href: string; children: React.ReactNode }> = ({ href, children }) => (
  <a href={href} className='text-[#686c54] hover:text-[#9c834f] transition duration-300 ease-in-out'>
    {children}
  </a>
);

const GlobalTextureOverlay: React.FC<{
  delay?: number;
  duration?: number;
  texture?: string;
  opacity?: number;
  scale?: number;
  translateY?: string;
}> = ({ delay = 0, duration = 0.8, texture = 'textura-01.gif', opacity = 0.15, scale = 1, translateY = '0%' }) => (
  <motion.div
    className='fixed inset-0 pointer-events-none'
    initial={{ opacity: 0 }}
    animate={{ opacity }}
    transition={{
      delay,
      duration,
      repeat: Infinity,
      repeatType: 'reverse',
      repeatDelay: Math.random() * 3 + 1,
    }}
    style={{
      backgroundImage: `url("https://oliviatest.xyz/OptomizedAssets/textures/${texture}")`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      mixBlendMode: 'multiply',
      transform: `scale(${scale}) translateY(${translateY})`,
    }}
  />
);

const FeatureCard: React.FC<{ icon: React.ReactNode; title: string; description: string; index: number }> = ({
  icon,
  title,
  description,
  index,
}) => (
  <motion.div
    className='relative overflow-hidden rounded-2xl backdrop-blur-sm bg-white bg-opacity-5 p-8 shadow-sm transition-all duration-300 border border-[#9c834f] border-opacity-10'
    whileHover={{ scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
  >
    <div className='relative z-10 flex flex-col items-start'>
      <div className='mb-4 rounded-lg backdrop-blur-sm bg-white bg-opacity-5 p-3'>{icon}</div>
      <h3 className='mb-3 text-xl font-semibold text-[#686c54]'>{title}</h3>
      <p className='text-[#686c54] opacity-80'>{description}</p>
    </div>
    <div
      className='absolute inset-0'
      style={{
        background: 'radial-gradient(circle at 30% 30%, rgba(156, 131, 79, 0.15), transparent 70%)',
      }}
    />
  </motion.div>
);

const TestimonialCard: React.FC<{ quote: string; author: string; position: string; index: number }> = ({
  quote,
  author,
  position,
  index,
}) => (
  <motion.div
    className='relative overflow-hidden rounded-2xl backdrop-blur-sm bg-white bg-opacity-5 p-8 shadow-sm border border-[#9c834f] border-opacity-10'
    whileHover={{ scale: 1.02 }}
  >
    <div className='relative z-10'>
      <div className='mb-6 text-lg italic text-[#686c54]'>&quot;{quote}&quot;</div>
      <div>
        <p className='font-semibold text-[#686c54]'>{author}</p>
        <p className='text-sm text-[#686c54] opacity-60'>{position}</p>
      </div>
    </div>
    <div
      className='absolute inset-0'
      style={{
        background: 'radial-gradient(circle at 30% 30%, rgba(156, 131, 79, 0.15), transparent 70%)',
      }}
    />
  </motion.div>
);

const LandingPage: React.FC = () => {
  const { data: user, isLoading: isUserLoading } = useAuth();
  const [activeFAQ, setActiveFAQ] = useState<number | null>(null);

  return (
    <ActiveSessionChecker>
      <div className='relative text-[#686c54]' style={{ background: '#f0efe9' }}>
        {/* Global gradients that span multiple sections */}
        <div className='fixed inset-0 pointer-events-none'>
          <div
            className='absolute inset-0'
            style={{
              background:
                'radial-gradient(circle at 70% 30%, rgba(156, 131, 79, 0.12), transparent 70%), radial-gradient(circle at 30% 80%, rgba(156, 131, 79, 0.08), transparent 60%)',
            }}
          />
          <div
            className='absolute inset-0'
            style={{
              background:
                'radial-gradient(circle at 20% 60%, rgba(156, 131, 79, 0.1), transparent 60%), radial-gradient(circle at 80% 20%, rgba(156, 131, 79, 0.06), transparent 50%)',
            }}
          />
        </div>
        {/* Global Texture Overlays */}
        {/* Large-scale textures that span multiple sections */}
        <GlobalTextureOverlay
          delay={0.1}
          duration={3.5}
          texture='texture-header-02-HOR-light.gif'
          opacity={0.12}
          scale={2.0}
          translateY='-40%'
        />
        <GlobalTextureOverlay
          delay={2.8}
          duration={4.2}
          texture='textura-03-cut.gif'
          opacity={0.08}
          scale={2.5}
          translateY='30%'
        />
        <GlobalTextureOverlay
          delay={1.5}
          duration={3.8}
          texture='textura01-header-HOR.gif'
          opacity={0.1}
          scale={2.2}
          translateY='-20%'
        />

        {/* Medium-scale overlapping textures */}
        <GlobalTextureOverlay
          delay={4.2}
          duration={3.2}
          texture='textura-01.gif'
          opacity={0.09}
          scale={1.6}
          translateY='10%'
        />
        <GlobalTextureOverlay
          delay={0.8}
          duration={4.0}
          texture='textura-02.gif'
          opacity={0.07}
          scale={1.8}
          translateY='-15%'
        />

        {/* Smaller detail textures */}
        <GlobalTextureOverlay
          delay={3.5}
          duration={3.0}
          texture='textura-03-cut.gif'
          opacity={0.06}
          scale={1.2}
          translateY='5%'
        />

        {/* Header */}
        <header className='fixed w-full z-50 backdrop-blur-sm bg-[#f0efe9] bg-opacity-90 border-b border-[#9c834f] border-opacity-10 relative'>
          <nav className='container mx-auto px-6 py-4 relative z-10'>
            <div className='flex justify-between items-center'>
              <Link to='/'>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }} className='h-12 w-[3.5rem]'>
                  <img
                    src='https://oliviatest.xyz/OptomizedAssets/logo/logo-olivia.gif'
                    alt='Olivia'
                    className='w-full h-full'
                  />
                </motion.div>
              </Link>
              <div className='hidden md:flex space-x-8'>
                <NavLink href='#features'>Features</NavLink>
                <NavLink href='#how-it-works'>How It Works</NavLink>
                <NavLink href='#testimonials'>Testimonials</NavLink>
              </div>
              <div>
                {isUserLoading ? (
                  <div className='w-24 h-10 bg-[#686c54] bg-opacity-10 rounded animate-pulse'></div>
                ) : user ? (
                  <Link to={routes.BrandKitsRoute.to}>
                    <motion.button
                      className='bg-[#686c54] hover:bg-[#9c834f] text-[#f0efe9] font-semibold py-2 px-4 rounded-lg transition duration-300 ease-in-out'
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      Dashboard
                    </motion.button>
                  </Link>
                ) : (
                  <Link to='/login'>
                    <motion.button
                      className='bg-[#686c54] hover:bg-[#9c834f] text-[#f0efe9] font-semibold py-2 px-4 rounded-lg transition duration-300 ease-in-out'
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      Get Started
                    </motion.button>
                  </Link>
                )}
              </div>
            </div>
          </nav>
        </header>

        {/* Hero Section */}
        <HeroSection />

        {/* Features Section */}
        <section id='features' className='relative py-24 overflow-hidden'>
          <div className='relative container mx-auto px-6'>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className='text-center max-w-3xl mx-auto mb-16'
            >
              <h2 className='text-4xl md:text-5xl font-bold mb-6 text-[#686c54]'>Transform Your Product Photography</h2>
              <p className='text-xl text-[#686c54] opacity-80'>
                Harness the power of AI to create stunning product visuals that capture attention and drive sales
              </p>
            </motion.div>

            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <FeatureCard
                  icon={<FiCamera className='w-8 h-8 text-[#9c834f]' />}
                  title='Multi-Angle Generation'
                  description='Create product shots from any angle instantly, perfect for e-commerce and marketing materials.'
                  index={0}
                />
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <FeatureCard
                  icon={<FiLayers className='w-8 h-8 text-[#9c834f]' />}
                  title='Style Automation'
                  description='Maintain brand consistency with automated style application across all your product shots.'
                  index={1}
                />
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <FeatureCard
                  icon={<FiZap className='w-8 h-8 text-[#9c834f]' />}
                  title='Rapid Iteration'
                  description='Test different styles and compositions in minutes, not days or weeks.'
                  index={2}
                />
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <FeatureCard
                  icon={<FiUsers className='w-8 h-8 text-[#9c834f]' />}
                  title='Audience Optimization'
                  description='Create variations tailored to different audience segments and marketing channels.'
                  index={3}
                />
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                <FeatureCard
                  icon={<FiTarget className='w-8 h-8 text-[#9c834f]' />}
                  title='Bulk Processing'
                  description='Transform entire product catalogs efficiently with consistent quality and style.'
                  index={4}
                />
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.7 }}
              >
                <FeatureCard
                  icon={<FiCpu className='w-8 h-8 text-[#9c834f]' />}
                  title='Smart Enhancement'
                  description='AI-powered touch-ups ensure every product looks its absolute best.'
                  index={5}
                />
              </motion.div>
            </div>
          </div>
        </section>

        {/* Interactive Demo Section */}
        <InteractiveDemoSection />

        {/* Testimonials Section */}
        <section id='testimonials' className='relative py-24 overflow-hidden'>
          <div className='relative container mx-auto px-6'>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className='text-center max-w-3xl mx-auto mb-16'
            >
              <h2 className='text-4xl md:text-5xl font-bold mb-6 text-[#686c54]'>Loved by Creative Teams</h2>
              <p className='text-xl text-[#686c54] opacity-80'>
                See how Olivia is transforming product photography workflows
              </p>
            </motion.div>

            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <TestimonialCard
                  quote='Olivia has revolutionized our product photography process. What used to take days now happens in minutes, and the quality is consistently amazing.'
                  author='Sarah Chen'
                  position='Creative Director, StyleCraft'
                  index={0}
                />
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <TestimonialCard
                  quote='The multi-angle generation feature is a game-changer. We can now create complete product galleries in minutes, not days.'
                  author='Michael Rodriguez'
                  position='E-commerce Manager, TrendSetters'
                  index={1}
                />
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <TestimonialCard
                  quote='The consistency in brand style across all our product photos is incredible. Olivia maintains our visual identity perfectly.'
                  author='Emma Thompson'
                  position='Brand Director, FutureWear'
                  index={2}
                />
              </motion.div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className='relative py-24 overflow-hidden'>
          <div className='relative container mx-auto px-6'>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className='text-center max-w-3xl mx-auto mb-16'
            >
              <h2 className='text-4xl md:text-5xl font-bold mb-6 text-[#686c54]'>Common Questions</h2>
              <p className='text-xl text-[#686c54] opacity-80'>Everything you need to know about Olivia</p>
            </motion.div>

            <div className='max-w-3xl mx-auto space-y-4'>
              {[
                {
                  question: 'How does Olivia ensure brand consistency?',
                  answer:
                    'Olivia uses advanced AI algorithms to analyze your brand guidelines and automatically applies your visual identity across all generated images, maintaining perfect consistency in style, lighting, and composition.',
                },
                {
                  question: 'Can I customize the generated photos?',
                  answer:
                    'Absolutely! While Olivia creates stunning photos automatically, you have full control to adjust and refine any aspect of the generated images through our intuitive editor.',
                },
                {
                  question: 'How many products can I process at once?',
                  answer:
                    "Olivia can handle bulk processing of entire product catalogs efficiently. There's no practical limit to the number of products you can process simultaneously.",
                },
                {
                  question: 'Is my brand information secure?',
                  answer:
                    'Yes, we take security seriously. All your brand assets and generated content are encrypted and stored securely. We never share your data with third parties.',
                },
              ].map((faq, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className='relative'
                >
                  <button
                    onClick={() => setActiveFAQ(activeFAQ === index ? null : index)}
                    className='w-full text-left p-6 rounded-lg backdrop-blur-sm bg-white bg-opacity-5 border border-[#9c834f] border-opacity-10 hover:border-opacity-20 transition-all duration-300 relative overflow-hidden'
                  >
                    <div className='flex justify-between items-center relative z-10'>
                      <h3 className='text-lg font-semibold text-[#686c54]'>{faq.question}</h3>
                      <motion.span
                        animate={{ rotate: activeFAQ === index ? 180 : 0 }}
                        transition={{ duration: 0.3 }}
                        className='text-[#9c834f]'
                      >
                        ↓
                      </motion.span>
                    </div>
                    <motion.div
                      initial={false}
                      animate={{ height: activeFAQ === index ? 'auto' : 0, opacity: activeFAQ === index ? 1 : 0 }}
                      transition={{ duration: 0.3 }}
                      className='overflow-hidden'
                    >
                      <p className='mt-4 text-[#686c54] opacity-80 relative z-10'>{faq.answer}</p>
                    </motion.div>
                  </button>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className='relative text-[#686c54] py-12 border-t border-[#9c834f] border-opacity-10'>
          <div className='container mx-auto px-6 relative z-10'>
            <div className='grid grid-cols-1 md:grid-cols-4 gap-8'>
              <div>
                <div className='flex items-center gap-3 mb-4'>
                  <div className='h-8 w-[2.33rem]'>
                    <img
                      src='https://oliviatest.xyz/OptomizedAssets/logo/logo-olivia.gif'
                      alt='Olivia'
                      className='h-full w-full'
                    />
                  </div>
                  <h3 className='text-2xl font-bold text-[#686c54]'>Olivia</h3>
                </div>
                <p className='mb-4 opacity-80'>AI-powered product photography for modern brands</p>
              </div>
              <div>
                <h4 className='text-lg font-semibold text-[#686c54] mb-4'>Product</h4>
                <ul className='space-y-2'>
                  <li>
                    <a href='#features' className='hover:text-[#9c834f] transition duration-300'>
                      Features
                    </a>
                  </li>
                  <li>
                    <a href='#pricing' className='hover:text-[#9c834f] transition duration-300'>
                      Pricing
                    </a>
                  </li>
                  <li>
                    <a href='#testimonials' className='hover:text-[#9c834f] transition duration-300'>
                      Testimonials
                    </a>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className='text-lg font-semibold text-[#686c54] mb-4'>Company</h4>
                <ul className='space-y-2'>
                  <li>
                    <a href='/about' className='hover:text-[#9c834f] transition duration-300'>
                      About
                    </a>
                  </li>
                  <li>
                    <a href='/blog' className='hover:text-[#9c834f] transition duration-300'>
                      Blog
                    </a>
                  </li>
                  <li>
                    <a href='/careers' className='hover:text-[#9c834f] transition duration-300'>
                      Careers
                    </a>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className='text-lg font-semibold text-[#686c54] mb-4'>Legal</h4>
                <ul className='space-y-2'>
                  <li>
                    <a href='/privacy' className='hover:text-[#9c834f] transition duration-300'>
                      Privacy
                    </a>
                  </li>
                  <li>
                    <a href='/terms' className='hover:text-[#9c834f] transition duration-300'>
                      Terms
                    </a>
                  </li>
                </ul>
              </div>
            </div>
            <div className='border-t border-[#9c834f] border-opacity-10 mt-8 pt-8 text-center text-sm'>
              <p className='opacity-80'>&copy; 2024 Olivia. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    </ActiveSessionChecker>
  );
};

export default LandingPage;
