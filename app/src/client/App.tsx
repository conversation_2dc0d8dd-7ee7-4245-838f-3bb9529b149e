import React, { useMemo } from 'react';
import './Main.css';
import { useLocation } from 'react-router-dom';
import { Outlet } from 'react-router-dom';
import { SidebarProvider } from './hooks/useSidebarState';
import { Toaster } from 'react-hot-toast';
import * as Sentry from '@sentry/react';
import { OnboardingTour } from '../features/onboarding';
import ProtectedLayout from './layout/protected-layout';
import ErrorFallback from './components/error-fallback';
import { ThemeProvider } from './context/ThemeContext';
import { NuqsAdapter } from 'nuqs/adapters/react';
import { PWAInstallPrompt } from './components/pwa/PWAInstallPrompt';

export default function App() {
  const location = useLocation();
  const isAdminDashboard = useMemo(() => {
    return location.pathname.startsWith('/admin');
  }, [location]);

  const hideHeader = useMemo(() => {
    return ['/canvas', '/product-photoshoot', '/videography', '/onboarding', '/edit-brand-kit'].some(path =>
      location.pathname.startsWith(path)
    );
  }, [location]);

  const isPublicPage = useMemo(() => {
    return (
      location.pathname === '/' ||
      location.pathname === '/login' ||
      location.pathname === '/signup' ||
      location.pathname === '/email-verification' ||
      location.pathname === '/request-password-reset' ||
      location.pathname === '/password-reset' ||
      location.pathname === '/pricing'
    );
  }, [location]);

  return (
    <NuqsAdapter>
      <SidebarProvider>
        <ThemeProvider>
          <div className='min-h-screen bg-[#f2efe8] dark:bg-black text-black dark:text-white'>
            <Sentry.ErrorBoundary fallback={<ErrorFallback />}>
              {!isPublicPage && <OnboardingTour />}
              {isAdminDashboard || isPublicPage ? (
                <Outlet />
              ) : (
                <div className='flex min-h-screen bg-gray-100'>
                  <ProtectedLayout showHeader={!hideHeader}>
                    <Outlet />
                  </ProtectedLayout>
                </div>
              )}
              <Toaster position='top-right' />
              {/* PWA Install Prompt - disabled, waiting for native browser prompt */}
              {/* {!isPublicPage && <PWAInstallPrompt />} */}
            </Sentry.ErrorBoundary>
          </div>
        </ThemeProvider>
      </SidebarProvider>
    </NuqsAdapter>
  );
}
