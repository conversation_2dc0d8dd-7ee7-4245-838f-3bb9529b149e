/**
 * @deprecated This component is not being used at the moment.
 * TODO: Remove or refactor when model card functionality is needed.
 * Last updated: January 2025
 */

import React, { useState } from 'react';
import { Camera, Trash2, Loader2 } from 'lucide-react';
import { Link } from 'react-router-dom';
import { PhotographyModel } from 'wasp/entities';

interface ModelCardProps {
  model: PhotographyModel & { isTraining: boolean };
  onDelete: (model: PhotographyModel) => Promise<void>;
  viewMode: 'grid' | 'list';
}

const getStatusColor = (status: string, provider: string | null): string => {
  // Log status for debugging
  console.log('Getting status color for:', { status, provider });

  const normalizedStatus = status?.toUpperCase();

  // Ready states
  if (['TRAINED', 'COMPLETED', 'READY'].includes(normalizedStatus)) {
    return 'bg-[#676D50] dark:bg-[#676D50]';
  }

  // Queue/Pending states
  if (['INITIALIZING', 'DATA_PREPARATION', 'IN_QUEUE', 'PENDING'].includes(normalizedStatus)) {
    return 'bg-yellow-500 dark:bg-yellow-600';
  }

  // Active training states
  if (['TRAINING', 'IN_PROGRESS', 'RUNNING'].includes(normalizedStatus)) {
    return 'bg-blue-500 dark:bg-blue-600';
  }

  // Evaluation state (BFL specific)
  if (normalizedStatus === 'EVALUATION') {
    return 'bg-purple-500 dark:bg-purple-600';
  }

  // Failed state
  if (normalizedStatus === 'FAILED') {
    return 'bg-red-500 dark:bg-red-600';
  }

  console.log('Unknown status:', status);
  return 'bg-gray-500 dark:bg-gray-600';
};

const getDisplayStatus = (status: string, provider: string | null): string => {
  // Log status for debugging
  console.log('Model status:', { status, provider });

  const normalizedStatus = status?.toUpperCase();

  // Ready states
  if (['COMPLETED', 'READY'].includes(normalizedStatus)) {
    return 'Trained';
  }

  // Queue states
  if (normalizedStatus === 'IN_QUEUE') {
    return 'In Queue';
  }

  // Pending states
  if (normalizedStatus === 'PENDING') {
    return 'Pending';
  }

  // Training states
  if (['IN_PROGRESS', 'RUNNING', 'TRAINING'].includes(normalizedStatus)) {
    return 'Training';
  }

  // Failed state
  if (normalizedStatus === 'FAILED') {
    return 'Failed';
  }

  // Initialization states
  if (['INITIALIZING', 'DATA_PREPARATION'].includes(normalizedStatus)) {
    return 'Initializing...';
  }

  // Evaluation state (BFL specific)
  if (normalizedStatus === 'EVALUATION') {
    return 'Evaluating...';
  }

  return status?.replace('_', ' ') || 'Unknown';
};

const formatTimeRemaining = (seconds: number): string => {
  if (seconds < 60) {
    return `${Math.round(seconds)}s`;
  } else if (seconds < 3600) {
    return `${Math.round(seconds / 60)}m`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.round((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  }
};

export const ModelCard = ({ model, onDelete, viewMode }: ModelCardProps) => {
  const [isDeleting, setIsDeleting] = useState(false);

  // Handle progress display
  // For RunPod models, calculate progress from steps if available
  const progressValue = model.provider === 'runpod' && model.totalSteps && model.currentStep !== null
    ? Math.round((model.currentStep / model.totalSteps) * 100)
    : typeof model.progress === 'string'
      ? parseFloat(model.progress)
      : (model.progress ?? 0);

  const progressWidth = `${progressValue}%`;

  // Calculate time remaining if available
  const timeRemaining = model.estimatedTime
    ? formatTimeRemaining(model.estimatedTime * 60) // Convert minutes to seconds
    : null;

  const isModelReady = ['Trained', 'COMPLETED', 'READY'].includes(model.status);
  const displayStatus = getDisplayStatus(model.status, model.provider);

  const handleDelete = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (window.confirm('Are you sure you want to delete this model?')) {
      setIsDeleting(true);
      try {
        await onDelete(model);
      } catch (error) {
        console.error('Error deleting model:', error);
      } finally {
        setIsDeleting(false);
      }
    }
  };

  // Log model details for debugging
  console.log('Model details:', {
    id: model.id,
    status: model.status,
    provider: model.provider,
    progress: progressValue,
    isReady: isModelReady
  });

  return (
    <div className='bg-white dark:bg-gray-900 rounded-lg shadow-sm overflow-hidden relative group border border-[#B5B178]/20 dark:border-gray-600'>
      <div className='h-full grid grid-rows-[auto_1fr_auto_auto] gap-4 p-4'>
        {/* Header with title and delete button */}
        <div className='flex justify-between items-center min-h-[32px]'>
          <h3 className='font-semibold text-[#676D50] dark:text-[#676D50] truncate pr-10'>{model.name}</h3>
          <button
            className='absolute right-4 top-4 w-8 h-8 flex items-center justify-center rounded-full bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-400 hover:text-red-500 dark:hover:text-red-400 disabled:opacity-50 transition-colors duration-200 shadow-sm'
            onClick={handleDelete}
            disabled={isDeleting}
            title="Delete model"
          >
            {isDeleting ? (
              <Loader2 className='w-5 h-5 animate-spin' />
            ) : (
              <Trash2 className='w-5 h-5' />
            )}
          </button>
        </div>

        {/* Image container */}
        <div className='bg-gray-200 dark:bg-gray-700 w-full rounded-md relative' style={{ aspectRatio: '1/1' }}>
          {model.imageUrl && (
            <img
              src={model.imageUrl}
              alt={model.name}
              className='absolute inset-0 w-full h-full object-cover rounded-md'
            />
          )}
          {!isModelReady && (
            <div className='absolute inset-0 flex flex-col items-center justify-center bg-black/50 text-white'>
              <p className='text-lg font-semibold mb-2'>Training in Progress</p>
              <div className='w-3/4 bg-gray-200 dark:bg-gray-600 rounded-full h-2.5'>
                <div
                  className='bg-[#676D50] dark:bg-[#676D50] h-2.5 rounded-full transition-all duration-300 ease-in-out'
                  style={{ width: progressWidth }}
                />
              </div>
              {timeRemaining && (
                <p className='text-sm mt-2 text-gray-300'>~{timeRemaining} remaining</p>
              )}
              {model.provider === 'runpod' && model.currentStep !== null && model.totalSteps !== null && (
                <p className='text-xs mt-1'>
                  Step {model.currentStep} of {model.totalSteps}
                </p>
              )}
            </div>
          )}
          <div className="absolute top-2 right-2 flex gap-2">
            <span
              className={`px-2 py-1 text-xs text-white rounded-full ${getStatusColor(model.status, model.provider)}`}
            >
              {displayStatus}
            </span>
            <span
              className={`px-2 py-1 text-xs text-white rounded-full ${model.type === 'style'
                ? 'bg-purple-500 dark:bg-purple-600'
                : 'bg-blue-500 dark:bg-blue-600'
                }`}
            >
              {model.type === 'style' ? 'Style' : 'Product'}
            </span>
          </div>
        </div>

        {/* Stats */}
        {isModelReady && (
          <div className='flex justify-between text-sm'>
            <div>
              <div className='text-gray-500 dark:text-gray-400'>Usage</div>
              <div className='font-semibold text-[#676D50] dark:text-[#676D50]'>{model.usage || 0}</div>
            </div>
            <div>
              <div className='text-gray-500 dark:text-gray-400'>Performance</div>
              <div className='font-semibold text-[#676D50] dark:text-[#676D50]'>{model.performance || 0}%</div>
            </div>
          </div>
        )}

        {/* Action button */}
        <Link
          to='/product-photoshoot'
          state={{ selectedModelId: model.id, selectedModelName: model.name }}
          className={`w-full py-2 px-4 flex items-center justify-center text-white rounded-md transition-all duration-200 ${isModelReady
            ? 'bg-[#676D50] hover:bg-[#A6884C] dark:bg-[#676D50] dark:hover:bg-[#A6884C]'
            : 'bg-gray-300 dark:bg-gray-600 cursor-not-allowed'
            }`}
          onClick={(e) => {
            if (!isModelReady) {
              e.preventDefault();
              return;
            }

            // Store model ID in localStorage
            localStorage.setItem('selectedModelId', model.id);
          }}
        >
          <Camera size={20} className='mr-2' />
          {isModelReady
            ? 'Generate Photo'
            : model.status === 'FAILED'
              ? 'Training Failed'
              : ['Training', 'IN_PROGRESS', 'RUNNING'].includes(model.status)
                ? 'Training in Progress...'
                : model.status === 'IN_QUEUE'
                  ? 'In Queue...'
                  : model.status === 'PENDING'
                    ? 'Pending...'
                    : model.status === 'Initializing'
                      ? 'Initializing...'
                      : 'Model Not Ready'}
        </Link>
      </div>
    </div>
  );
};

export default ModelCard;
