import React, { useEffect } from 'react';
import { sendMessageToParent, getQueryParams } from '../../utils/windowCommunication';


const GoogleOAuthCallback: React.FC = () => {
  useEffect(() => {
    const params = getQueryParams();
    
    if (params.success) {
      sendMessageToParent('GOOGLE_AUTH_SUCCESS', decodeURIComponent(params.success));
      
      setTimeout(() => {
        window.close();
      }, 500);
    } else if (params.error) {
      sendMessageToParent('GOOGLE_AUTH_ERROR', decodeURIComponent(params.error));
      
      setTimeout(() => {
        window.close();
      }, 500);
    } else {
      sendMessageToParent('GOOGLE_AUTH_ERROR', 'No response received from Google OAuth');
      
      setTimeout(() => {
        window.close();
      }, 500);
    }
  }, []);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900 p-4">
      <div className="text-center">
        <h1 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Google Authentication Complete
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          This window will close automatically. Please return to the main application.
        </p>
      </div>
    </div>
  );
};

export default GoogleOAuthCallback;
