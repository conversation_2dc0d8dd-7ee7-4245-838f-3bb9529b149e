import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { AuthWrapper } from './authWrapper';
import RegisterForm from './components/register-form';
import { Separator } from '../components/ui/separator';
import { GoogleSignInButton } from 'wasp/client/auth';

export function Signup() {
  const [needsConfirmation, setNeedsConfirmation] = useState(false);

  async function onSuccessCallback() {
    setNeedsConfirmation(true);
  }

  return (
    <AuthWrapper>
      <div className='my-10 bg-white dark:bg-gray-800'>
        {needsConfirmation ? (
          <p>Check your email for the confirmation link. If you don&apos;t see it, check spam/junk folder.</p>
        ) : (
          <>
            <div className='space-y-1 text-center mb-10'>
              <h2 className='text-2xl font-bold'>Create your account</h2>
              <p className='text-muted-foreground'>Get started with your free account today</p>
            </div>

            <div className='grid grid-cols-1 gap-4 mb-5'>
              <GoogleSignInButton />
            </div>

            <div className='relative mb-5'>
              <div className='absolute inset-0 flex items-center'>
                <Separator className='w-full' />
              </div>
              <div className='relative flex justify-center text-xs uppercase'>
                <span className='bg-background px-2 text-muted-foreground'>Or continue with email</span>
              </div>
            </div>

            <div className='space-y-4'>
              <RegisterForm callback={onSuccessCallback} />
            </div>

            <div className='flex flex-col space-y-4 mt-6'>
              <div className='text-sm text-center text-muted-foreground'>
                Already have an account?{' '}
                <Link to='/login' className='text-primary hover:underline font-medium'>
                  Sign in
                </Link>
              </div>

              <div className='text-xs text-center text-muted-foreground'>
                By creating an account, you agree to our{' '}
                <Link to='/terms' className='hover:underline'>
                  Terms of Service
                </Link>{' '}
                and{' '}
                <Link to='/privacy' className='hover:underline'>
                  Privacy Policy
                </Link>
              </div>
            </div>
          </>
        )}
      </div>
    </AuthWrapper>
  );
}
