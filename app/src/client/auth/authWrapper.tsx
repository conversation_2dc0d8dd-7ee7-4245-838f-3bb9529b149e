import React, { ReactNode } from 'react';

export function AuthWrapper({ children }: { children: ReactNode }) {
  return (
    <div className='flex min-h-full flex-col justify-center pt-10 sm:px-6 lg:px-8 bg-[#F0EFE9] dark:bg-black'>
      <div className='sm:mx-auto sm:w-full sm:max-w-md'>
        <div className='bg-white py-8 px-4 shadow-xl border border-[#B5B178]/20 sm:rounded-lg sm:px-10 dark:bg-gray-800'>
          <div className='-mt-8 text-[#1F2419] dark:text-white'>{children}</div>
        </div>
      </div>
    </div>
  );
}
