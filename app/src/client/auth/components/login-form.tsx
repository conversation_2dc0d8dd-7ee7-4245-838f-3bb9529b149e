import React from "react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
	Label,
	Input,
	Alert,
	AlertDescription,
	<PERSON><PERSON>ooter,
	But<PERSON>,
} from "../../components/ui/index";
import { login } from "wasp/client/auth";
import { Link } from "react-router-dom";

const loginSchema = z.object({
	email: z
		.string()
		.min(1, { message: "Email is required" })
		.email({ message: "Invalid email address" }),
	password: z
		.string()
		.min(1, { message: "Password is required" })
		.min(6, { message: "Password must be at least 6 characters long" }),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export default function LoginForm({
	email,
	callback,
}: {
	email?: string;
	callback: () => void;
}) {
	const [error, setError] = useState<string | null>(null);
	const {
		register,
		handleSubmit,
		formState: { errors },
	} = useForm<LoginFormValues>({
		resolver: zod<PERSON>esolver(loginSchema),
		defaultValues: {
			email,
		},
	});

	const onSubmit = async (data: LoginFormValues) => {
		try {
			// Here you would typically send a request to your authentication API
			console.log("Login attempt", data);
			// Simulate an API call
			await login({ email: data.email, password: data.password });
			await callback();
			// If login is successful, you might redirect the user or update the app state
			console.log("Login successful");
			setError(null);
		} catch (err) {
			setError("Invalid credentials");
		}
	};

	return (
		<form onSubmit={handleSubmit(onSubmit)}>
			<div className="grid w-full items-center gap-4">
				<div className="flex flex-col space-y-1.5">
					<Label htmlFor="email">Email</Label>
					<Input
						id="email"
						disabled={!!email}
						type="email"
						placeholder="Enter your email"
						{...register("email")}
					/>
					{errors.email && (
						<span className="text-red-500 text-sm">{errors.email.message}</span>
					)}
				</div>
				<div className="flex flex-col space-y-1.5">
					<Label htmlFor="password">Password</Label>
					<Input
						id="password"
						type="password"
						placeholder="Enter your password"
						{...register("password")}
					/>
					{errors.password && (
						<span className="text-red-500 text-sm">
							{errors.password.message}
						</span>
					)}
				</div>
				<div className="text-right">
					<Link
						to="/request-password-reset"
						className="text-sm text-primary hover:underline"
					>
						Forgot password?
					</Link>
				</div>
			</div>
			{error && (
				<Alert variant="destructive" className="mt-4">
					<AlertDescription>{error}</AlertDescription>
				</Alert>
			)}
			<CardFooter className="flex justify-between mt-4 px-0">
				<Button
					type="submit"
					className="w-full dark:bg-primary bg-primary dark:hover:bg-olive-400 hover:bg-olive-400 text-white"
				>
					Sign in
				</Button>
			</CardFooter>
		</form>
	);
}
