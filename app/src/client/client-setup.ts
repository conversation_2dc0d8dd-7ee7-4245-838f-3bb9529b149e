import * as Sentry from '@sentry/react';
import { MODE, SENTRY_DSN } from '../shared/config';
import { useEffect } from 'react';
import { useLocation, useNavigationType, createRoutesFromChildren, matchRoutes } from 'react-router-dom';

// Sentry.init({
//   dsn: SENTRY_DSN,
//   integrations: [
//     Sentry.reactRouterV6BrowserTracingIntegration({
//       useEffect,
//       useLocation,
//       useNavigationType,
//       createRoutesFromChildren,
//       matchRoutes,
//     }),
//     Sentry.replayIntegration(),
//   ],
//   environment: MODE,
//   replaysSessionSampleRate: 0.1,
//   replaysOnErrorSampleRate: 1.0,
// });

// Set theme color for PWA
const setThemeColor = () => {
  const themeColor = '#9EA581'; // Your olive green color

  // Update or create theme-color meta tag
  let themeColorMeta = document.querySelector('meta[name="theme-color"]');
  if (!themeColorMeta) {
    themeColorMeta = document.createElement('meta');
    themeColorMeta.setAttribute('name', 'theme-color');
    document.head.appendChild(themeColorMeta);
  }
  themeColorMeta.setAttribute('content', themeColor);

  // Update or create msapplication-navbutton-color meta tag for IE/Edge
  let navButtonMeta = document.querySelector('meta[name="msapplication-navbutton-color"]');
  if (!navButtonMeta) {
    navButtonMeta = document.createElement('meta');
    navButtonMeta.setAttribute('name', 'msapplication-navbutton-color');
    document.head.appendChild(navButtonMeta);
  }
  navButtonMeta.setAttribute('content', themeColor);

  // Update or create apple-mobile-web-app-status-bar-style for iOS
  let appleStatusMeta = document.querySelector('meta[name="apple-mobile-web-app-status-bar-style"]');
  if (!appleStatusMeta) {
    appleStatusMeta = document.createElement('meta');
    appleStatusMeta.setAttribute('name', 'apple-mobile-web-app-status-bar-style');
    document.head.appendChild(appleStatusMeta);
  }
  appleStatusMeta.setAttribute('content', 'black-translucent');

  console.log('[PWA] Theme color set to:', themeColor);
};

// Register service worker for PWA functionality
const registerServiceWorker = async () => {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      console.log('[PWA] Service worker registered successfully:', registration);

      // Listen for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              console.log('[PWA] New service worker available');
              // You could show a notification to the user here
            }
          });
        }
      });
    } catch (error) {
      console.error('[PWA] Service worker registration failed:', error);
    }
  } else {
    console.log('[PWA] Service workers not supported');
  }
};

// Unregister existing service worker
const unregisterServiceWorker = async () => {
  if ('serviceWorker' in navigator) {
    try {
      const registrations = await navigator.serviceWorker.getRegistrations();
      for (const registration of registrations) {
        console.log('[PWA] Unregistering service worker:', registration);
        await registration.unregister();
      }
      console.log('[PWA] All service workers unregistered');
    } catch (error) {
      console.error('[PWA] Failed to unregister service workers:', error);
    }
  }
};

export const setup = async () => {
  // Set theme color for PWA
  setThemeColor();

  // Force unregister any existing service workers to disable caching
  await unregisterServiceWorker();

  // Also clear all caches
  if ('caches' in window) {
    const cacheNames = await caches.keys();
    await Promise.all(cacheNames.map(name => caches.delete(name)));
    console.log('[PWA] All caches cleared');
  }

  // Register service worker for PWA functionality - DISABLED
  // await registerServiceWorker();
};
